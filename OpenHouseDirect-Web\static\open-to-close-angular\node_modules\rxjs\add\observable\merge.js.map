{"version": 3, "file": "merge.js", "sourceRoot": "", "sources": ["../../../src/add/observable/merge.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { merge as mergeStatic } from '../../observable/merge';\n\nObservable.merge = mergeStatic;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let merge: typeof mergeStatic;\n  }\n}"]}