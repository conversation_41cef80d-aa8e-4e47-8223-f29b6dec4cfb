{"version": 3, "file": "postcss-cli-resources.js", "sourceRoot": "/users/hansl/sources/hansl/angular-cli/", "sources": ["plugins/postcss-cli-resources.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;GAMG;AACH,+CAA+C;AAC/C,mCAAmC;AACnC,2BAA2B;AAG3B,iBAAiB,GAAW;IAC1B,IAAI,UAAU,CAAC;IACf,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE/C,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;QACpB,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC;IAC1B,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,OAAO,UAAU,GAAG,CAAC;AAC9B,CAAC;AAQD,iBACE,IAAY,EACZ,IAAY,EACZ,QAAyD;;QAEzD,IAAI,CAAC;YACH,MAAM,CAAC,MAAM,QAAQ,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3C,CAAC;QAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;CAAA;AAED,kBAAe,OAAO,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC,OAAmC,EAAE,EAAE;IAC7F,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAEhD,MAAM,OAAO,GAAG,CAAO,QAAgB,EAAE,aAAkC,EAAE,EAAE;QAC7E,4CAA4C;QAC5C,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC;QAClB,CAAC;QACD,sDAAsD;QACtD,2CAA2C;QAC3C,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3E,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE,CAAC,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACvF,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACzC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACT,MAAM,CAAC,GAAG,CAAC,CAAC;oBACZ,MAAM,CAAC;gBACR,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEjE,MAAM,CAAC,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAU,EAAE,OAAe,EAAE,EAAE;gBACzD,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACR,MAAM,CAAC,GAAG,CAAC,CAAC;oBACZ,MAAM,CAAC;gBACT,CAAC;gBAED,MAAM,UAAU,GAAG,8BAAe,CAChC,EAAE,YAAY,EAAE,MAAM,EAAkC,EACxD,QAAQ,EACR,EAAE,OAAO,EAAE,CACZ,CAAC;gBAEF,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC7B,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBAEhD,IAAI,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC/C,EAAE,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC;oBACnB,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;gBAChE,CAAC;gBAED,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;oBACd,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAChD,CAAC;gBAED,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAEvC,OAAO,CAAC,SAAS,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAA,CAAC;IAEF,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;QACd,MAAM,eAAe,GAA+B,EAAE,CAAC;QACvD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACpB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC7C,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC;QACT,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,GAAG,EAAkB,CAAC;QAEhD,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAM,IAAI,EAAC,EAAE;YAClD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,MAAM,QAAQ,GAAG,6CAA6C,CAAC;YAC/D,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,IAAI,KAAK,CAAC;YACV,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,qDAAqD;YACrD,OAAO,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrD,IAAI,YAAY,CAAC;gBACjB,IAAI,CAAC;oBACH,YAAY,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAC3D,CAAC;gBAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACb,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC5E,QAAQ,CAAC;gBACX,CAAC;gBAED,EAAE,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC5B,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBACrD,CAAC;gBAED,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC;oBAClD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;oBACrC,QAAQ,GAAG,IAAI,CAAC;gBAClB,CAAC;gBAED,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC5C,CAAC;YAED,EAAE,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC7B,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YACxC,CAAC;YAED,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACb,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAA,CAAC,CAAC,CAAC;IACN,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC"}