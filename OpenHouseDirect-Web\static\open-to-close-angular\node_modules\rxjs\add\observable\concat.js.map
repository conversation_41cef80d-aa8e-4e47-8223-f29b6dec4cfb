{"version": 3, "file": "concat.js", "sourceRoot": "", "sources": ["../../../src/add/observable/concat.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,uBAAuC,yBAAyB,CAAC,CAAA;AAEjE,uBAAU,CAAC,MAAM,GAAG,eAAY,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { concat as concatStatic } from '../../observable/concat';\n\nObservable.concat = concatStatic;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let concat: typeof concatStatic;\n  }\n}"]}