{"version": 3, "file": "cdk-coercion.umd.min.js", "sources": ["../../src/cdk/coercion/boolean-property.ts", "../../src/cdk/coercion/number-property.ts", "../../src/cdk/coercion/array.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nexport function coerceBooleanProperty(value: any): boolean {\n  return value != null && `${value}` !== 'false';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a data-bound value (typically a string) to a number. */\nexport function coerceNumberProperty(value: any): number;\nexport function coerceNumberProperty<D>(value: any, fallback: D): number | D;\nexport function coerceNumberProperty(value: any, fallbackValue = 0) {\n  return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nexport function _isNumberValue(value: any): boolean {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value as any)) && !isNaN(Number(value));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Wraps the provided value in an array, unless the provided value is an array. */\nexport function coerceArray<T>(value: T | T[]): T[] {\n  return Array.isArray(value) ? value : [value];\n}\n"], "names": ["coerceBooleanProperty", "value", "coerceNumberProperty", "fallback<PERSON><PERSON><PERSON>", "_isNumberValue", "Number", "isNaN", "parseFloat", "coerce<PERSON><PERSON><PERSON>", "Array", "isArray"], "mappings": ";;;;;;;0PASA,SAAAA,GAAsCC,GACpC,MAAgB,OAATA,GAAiB,GAAGA,GAAY,QCCzC,QAAAC,GAAqCD,EAAYE,GAC/C,WADF,KAAAA,IAAiDA,EAAjD,GACSC,EAAeH,GAASI,OAAOJ,GAASE,EAOjD,QAAAC,GAA+BH,GAI7B,OAAQK,MAAMC,WAAU,MAAoBD,MAAMD,OAAOJ,ICd3D,QAAAO,GAA+BP,GAC7B,MAAOQ,OAAMC,QAAQT,GAASA,GAASA"}