"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const chalk_1 = require("chalk");
const stringUtils = require('ember-cli-string-utils');
const common_tags_1 = require("common-tags");
const config_1 = require("../models/config");
const core_1 = require("@angular-devkit/core");
const operators_1 = require("rxjs/operators");
const schematics_1 = require("../utilities/schematics");
const dynamic_path_parser_1 = require("../utilities/dynamic-path-parser");
const app_utils_1 = require("../utilities/app-utils");
const path = require("path");
const Command = require('../ember-cli/lib/models/command');
const SilentError = require('silent-error');
const { cyan, yellow } = chalk_1.default;
const separatorRegEx = /[\/\\]/g;
exports.default = Command.extend({
    name: 'generate',
    description: 'Generates and/or modifies files based on a schematic.',
    aliases: ['g'],
    availableOptions: [
        {
            name: 'dry-run',
            type: Boolean,
            default: false,
            aliases: ['d'],
            description: 'Run through without making any changes.'
        },
        {
            name: 'force',
            type: Boolean,
            default: false,
            aliases: ['f'],
            description: 'Forces overwriting of files.'
        },
        {
            name: 'app',
            type: String,
            aliases: ['a'],
            description: 'Specifies app name to use.'
        },
        {
            name: 'collection',
            type: String,
            aliases: ['c'],
            description: 'Schematics collection to use.'
        },
        {
            name: 'lint-fix',
            type: Boolean,
            aliases: ['lf'],
            description: 'Use lint to fix files after generation.'
        }
    ],
    anonymousOptions: [
        '<schematic>'
    ],
    getCollectionName(rawArgs, parsedOptions) {
        let collectionName = config_1.CliConfig.getValue('defaults.schematics.collection');
        if (!rawArgs || rawArgs.length === 0) {
            return [collectionName, null];
        }
        let schematicName = rawArgs[0];
        if (schematicName.match(/:/)) {
            [collectionName, schematicName] = schematicName.split(':', 2);
        }
        else if (parsedOptions) {
            if (parsedOptions.collection) {
                collectionName = parsedOptions.collection;
            }
        }
        else {
            const parsedArgs = this.parseArgs(rawArgs, false);
            if (parsedArgs.options.collection) {
                collectionName = parsedArgs.options.collection;
            }
        }
        return [collectionName, schematicName];
    },
    beforeRun: function (rawArgs) {
        const isHelp = ['--help', '-h'].includes(rawArgs[0]);
        if (isHelp) {
            return;
        }
        const [collectionName, schematicName] = this.getCollectionName(rawArgs);
        if (!schematicName) {
            return Promise.reject(new SilentError(common_tags_1.oneLine `
          The "ng generate" command requires a
          schematic name to be specified.
          For more details, use "ng help".
      `));
        }
        if (/^\d/.test(rawArgs[1])) {
            SilentError.debugOrThrow('@angular/cli/commands/generate', `The \`ng generate ${schematicName} ${rawArgs[1]}\` file name cannot begin with a digit.`);
        }
        const SchematicGetOptionsTask = require('../tasks/schematic-get-options').default;
        const getOptionsTask = new SchematicGetOptionsTask({
            ui: this.ui,
            project: this.project
        });
        return getOptionsTask.run({
            schematicName,
            collectionName
        })
            .then((availableOptions) => {
            let anonymousOptions = [];
            if (availableOptions) {
                const nameOption = availableOptions.filter(opt => opt.name === 'name')[0];
                if (nameOption) {
                    anonymousOptions = [...anonymousOptions, '<name>'];
                }
            }
            else {
                anonymousOptions = [...anonymousOptions, '<name>'];
            }
            if (collectionName === '@schematics/angular' && schematicName === 'interface') {
                anonymousOptions = [...anonymousOptions, '<type>'];
            }
            this.registerOptions({
                anonymousOptions: anonymousOptions,
                availableOptions: availableOptions || []
            });
        });
    },
    run: function (commandOptions, rawArgs) {
        if (rawArgs[0] === 'module' && !rawArgs[1]) {
            throw 'The `ng generate module` command requires a name to be specified.';
        }
        let entityName = rawArgs[1];
        if (entityName) {
            commandOptions.name = stringUtils.dasherize(entityName.split(separatorRegEx).pop());
        }
        else {
            entityName = '';
        }
        const appConfig = app_utils_1.getAppFromConfig(commandOptions.app);
        const dynamicPathOptions = {
            project: this.project,
            entityName: entityName,
            appConfig: appConfig,
            dryRun: commandOptions.dryRun
        };
        const parsedPath = dynamic_path_parser_1.dynamicPathParser(dynamicPathOptions);
        commandOptions.sourceDir = parsedPath.sourceDir.replace(separatorRegEx, '/');
        const root = parsedPath.sourceDir + path.sep;
        commandOptions.appRoot = parsedPath.appRoot === parsedPath.sourceDir ? '' :
            parsedPath.appRoot.startsWith(root)
                ? parsedPath.appRoot.substr(root.length)
                : parsedPath.appRoot;
        commandOptions.path = parsedPath.dir.replace(separatorRegEx, '/');
        commandOptions.path = parsedPath.dir === parsedPath.sourceDir ? '' :
            parsedPath.dir.startsWith(root)
                ? commandOptions.path.substr(root.length)
                : commandOptions.path;
        const cwd = this.project.root;
        const [collectionName, schematicName] = this.getCollectionName(rawArgs, commandOptions);
        if (['component', 'c', 'directive', 'd'].indexOf(schematicName) !== -1) {
            if (commandOptions.prefix === undefined) {
                commandOptions.prefix = appConfig.prefix;
            }
            if (schematicName === 'component' || schematicName === 'c') {
                if (commandOptions.styleext === undefined) {
                    commandOptions.styleext = config_1.CliConfig.getValue('defaults.styleExt');
                }
            }
        }
        const SchematicRunTask = require('../tasks/schematic-run').default;
        const schematicRunTask = new SchematicRunTask({
            ui: this.ui,
            project: this.project
        });
        if (collectionName === '@schematics/angular' && schematicName === 'interface' && rawArgs[2]) {
            commandOptions.type = rawArgs[2];
        }
        const logger = new core_1.logging.IndentLogger('cling');
        const loggerSubscription = logger.pipe(operators_1.filter(entry => (entry.level != 'debug')))
            .subscribe(entry => {
            let color = (x) => core_1.terminal.dim(core_1.terminal.white(x));
            let output = process.stdout;
            switch (entry.level) {
                case 'info':
                    color = core_1.terminal.white;
                    break;
                case 'warn':
                    color = core_1.terminal.yellow;
                    break;
                case 'error':
                    color = core_1.terminal.red;
                    output = process.stderr;
                    break;
                case 'fatal':
                    color = (x) => core_1.terminal.bold(core_1.terminal.red(x));
                    output = process.stderr;
                    break;
            }
            output.write(color(entry.message) + '\n');
        });
        return schematicRunTask.run({
            taskOptions: commandOptions,
            workingDir: cwd,
            collectionName,
            schematicName,
            logger: this.logger
        })
            .then(() => loggerSubscription.unsubscribe());
    },
    printDetailedHelp: function (_options, rawArgs) {
        const engineHost = schematics_1.getEngineHost();
        const [collectionName] = this.getCollectionName();
        const collection = schematics_1.getCollection(collectionName);
        const schematicName = rawArgs[1];
        if (schematicName) {
            const SchematicGetHelpOutputTask = require('../tasks/schematic-get-help-output').default;
            const getHelpOutputTask = new SchematicGetHelpOutputTask({
                ui: this.ui,
                project: this.project
            });
            return getHelpOutputTask.run({
                schematicName,
                collectionName,
                nonSchematicOptions: this.availableOptions.filter((o) => !o.hidden)
            })
                .then((output) => {
                return [
                    cyan(`ng generate ${schematicName} ${cyan('[name]')} ${cyan('<options...>')}`),
                    ...output
                ].join('\n');
            });
        }
        else {
            const schematicNames = engineHost.listSchematics(collection);
            const output = [];
            output.push(cyan('Available schematics:'));
            schematicNames.forEach(schematicName => {
                output.push(yellow(`    ${schematicName}`));
            });
            return Promise.resolve(output.join('\n'));
        }
    }
});
//# sourceMappingURL=/users/hansl/sources/hansl/angular-cli/commands/generate.js.map