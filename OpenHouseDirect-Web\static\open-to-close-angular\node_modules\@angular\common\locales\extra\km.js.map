{"version": 3, "file": "km.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/km.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM;YACxC,OAAO,EAAE,KAAK;SACf;QACD,AADE;KAEH;IACD;QACE;YACE,UAAU,EAAE,YAAY,EAAE,OAAO;YACjC,MAAM,EAAE,OAAO,EAAE,KAAK;SACvB;QACD,AADE;KAEH;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'អធ្រាត្រ', 'ថ្ងៃត្រង់', 'ព្រឹក', 'រសៀល',\n      'ល្ងាច', 'យប់'\n    ],\n    ,\n  ],\n  [\n    [\n      'អធ្រាត្រ', 'ថ្ងៃ​ត្រង់', 'ព្រឹក',\n      'រសៀល', 'ល្ងាច', 'យប់'\n    ],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['00:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '24:00']\n  ]\n];\n"]}