/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/common")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/common"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.bidi=e.ng.cdk.bidi||{}),e.ng.core,e.ng.common)}(this,function(e,t,r){"use strict";var n=new t.InjectionToken("cdk-dir-doc"),i=function(){function e(e){if(this.value="ltr",this.change=new t.EventEmitter,e){var r=e.body?e.body.dir:null,n=e.documentElement?e.documentElement.dir:null;this.value=r||n||"ltr"}}return e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:void 0,decorators:[{type:t.Optional},{type:t.Inject,args:[n]}]}]},e}(),o=function(){function e(){this._dir="ltr",this._isInitialized=!1,this.change=new t.EventEmitter}return Object.defineProperty(e.prototype,"dir",{get:function(){return this._dir},set:function(e){var t=this._dir;this._dir=e,t!==this._dir&&this._isInitialized&&this.change.emit(this._dir)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"value",{get:function(){return this.dir},enumerable:!0,configurable:!0}),e.prototype.ngAfterContentInit=function(){this._isInitialized=!0},e.prototype.ngOnDestroy=function(){this.change.complete()},e.decorators=[{type:t.Directive,args:[{selector:"[dir]",providers:[{provide:i,useExisting:e}],host:{"[dir]":"dir"},exportAs:"dir"}]}],e.ctorParameters=function(){return[]},e.propDecorators={change:[{type:t.Output,args:["dirChange"]}],dir:[{type:t.Input}]},e}(),c=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{exports:[o],declarations:[o],providers:[{provide:n,useExisting:r.DOCUMENT},i]}]}],e.ctorParameters=function(){return[]},e}();e.Directionality=i,e.DIR_DOCUMENT=n,e.Dir=o,e.BidiModule=c,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-bidi.umd.min.js.map
