{"_from": "uws@~9.14.0", "_id": "uws@9.14.0", "_inBundle": false, "_integrity": "sha512-HNMztPP5A1sKuVFmdZ6BPVpBQd5bUjNC8EFMFiICK+oho/OQsAJy5hnIx4btMHiOk8j04f/DbIlqnEZ9d72dqg==", "_location": "/uws", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "uws@~9.14.0", "name": "uws", "escapedName": "uws", "rawSpec": "~9.14.0", "saveSpec": null, "fetchSpec": "~9.14.0"}, "_requiredBy": ["/engine.io"], "_resolved": "https://registry.npmjs.org/uws/-/uws-9.14.0.tgz", "_shasum": "fac8386befc33a7a3705cbd58dc47b430ca4dd95", "_spec": "uws@~9.14.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\engine.io", "author": {"name": "<PERSON>", "email": "alex<PERSON><PERSON>@gmail.com", "url": "https://github.com/alexhultman"}, "bugs": {"url": "https://github.com/uWebSockets/uWebSockets/issues"}, "bundleDependencies": false, "deprecated": "New code is available at github.com/uNetworking/uWebSockets.js", "description": "Tiny WebSockets", "engines": {"node": ">=4"}, "homepage": "https://github.com/uWebSockets/uWebSockets", "keywords": ["tiny", "websockets"], "license": "<PERSON><PERSON><PERSON>", "main": "uws.js", "name": "uws", "repository": {"type": "git", "url": "git+https://github.com/uWebSockets/uWebSockets.git"}, "scripts": {"install": "node-gyp rebuild > build_log.txt 2>&1 || exit 0"}, "version": "9.14.0"}