{"version": 3, "file": "observers.js", "sources": ["../../../src/cdk/observers/index.ts", "../../../src/cdk/observers/public-api.ts", "../../../src/cdk/observers/observe-content.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './observe-content';\n\n/**\n * @deprecated Use CdkObserveContent\n * @deletion-target 6.0.0\n */\nexport {CdkObserveContent as ObserveContent} from './observe-content';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  NgModule,\n  Output,\n  Input,\n  EventEmitter,\n  OnDestroy,\n  AfterContentInit,\n  Injectable,\n  NgZone,\n  OnChanges,\n  SimpleChanges,\n} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {Subject} from 'rxjs/Subject';\nimport {debounceTime} from 'rxjs/operators/debounceTime';\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\n@Injectable()\nexport class MutationObserverFactory {\n  create(callback: MutationCallback): MutationObserver | null {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n}\n\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\n@Directive({\n  selector: '[cdkObserveContent]',\n  exportAs: 'cdkObserveContent',\n})\nexport class CdkObserveContent implements AfterContentInit, OnChanges, OnDestroy {\n  private _observer: MutationObserver | null;\n  private _disabled = false;\n\n  /** Event emitted for each change in the element's content. */\n  @Output('cdkObserveContent') event = new EventEmitter<MutationRecord[]>();\n\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  @Input('cdkObserveContentDisabled')\n  get disabled() { return this._disabled; }\n  set disabled(value: any) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n\n  /** Used for debouncing the emitted values to the observeContent event. */\n  private _debouncer = new Subject<MutationRecord[]>();\n\n  /** Debounce interval for emitting the changes. */\n  @Input() debounce: number;\n\n  constructor(\n    private _mutationObserverFactory: MutationObserverFactory,\n    private _elementRef: ElementRef,\n    private _ngZone: NgZone) { }\n\n  ngAfterContentInit() {\n    if (this.debounce > 0) {\n      this._ngZone.runOutsideAngular(() => {\n        this._debouncer.pipe(debounceTime(this.debounce))\n            .subscribe((mutations: MutationRecord[]) => this.event.emit(mutations));\n      });\n    } else {\n      this._debouncer.subscribe(mutations => this.event.emit(mutations));\n    }\n\n    this._observer = this._ngZone.runOutsideAngular(() => {\n      return this._mutationObserverFactory.create((mutations: MutationRecord[]) => {\n        this._debouncer.next(mutations);\n      });\n    });\n\n    if (!this.disabled) {\n      this._enable();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['disabled']) {\n      changes['disabled'].currentValue ? this._disable() : this._enable();\n    }\n  }\n\n  ngOnDestroy() {\n    this._disable();\n    this._debouncer.complete();\n  }\n\n  private _disable() {\n    if (this._observer) {\n      this._observer.disconnect();\n    }\n  }\n\n  private _enable() {\n    if (this._observer) {\n      this._observer.observe(this._elementRef.nativeElement, {\n        characterData: true,\n        childList: true,\n        subtree: true\n      });\n    }\n  }\n}\n\n\n@NgModule({\n  exports: [CdkObserveContent],\n  declarations: [CdkObserveContent],\n  providers: [MutationObserverFactory]\n})\nexport class ObserversModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AEQA,AAcA,AACA,AACA;;;;AAOA,AAAA,MAAA,uBAAA,CAAA;;;;;IACE,MAAM,CAAC,QAA0B,EAAnC;QACI,OAAO,OAAO,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KACxF;;;IAJH,EAAA,IAAA,EAAC,UAAU,EAAX;;;;;;;;AAeA,AAAA,MAAA,iBAAA,CAAA;;;;;;IAuBE,WAAF,CACY,wBADZ,EAEY,WAFZ,EAGY,OAHZ,EAAA;QACY,IAAZ,CAAA,wBAAoC,GAAxB,wBAAwB,CAApC;QACY,IAAZ,CAAA,WAAuB,GAAX,WAAW,CAAvB;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QAxBA,IAAA,CAAA,SAAA,GAAsB,KAAK,CAA3B;;;;QAGA,IAAA,CAAA,KAAA,GAAuC,IAAI,YAAY,EAAoB,CAA3E;;;;QAaA,IAAA,CAAA,UAAA,GAAuB,IAAI,OAAO,EAAoB,CAAtD;KAQgC;;;;;;IAdhC,IAAM,QAAQ,GAAd,EAAmB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAzC;;;;;IACE,IAAI,QAAQ,CAAC,KAAU,EAAzB;QACI,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC/C;;;;IAaD,kBAAkB,GAApB;QACI,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAArC;gBACQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBAC5C,SAAS,CAAC,CAAC,SAA2B,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aAC7E,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SACpE;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAApD;YACM,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,SAA2B,KAA9E;gBACQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACjC,CAAC,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;KACF;;;;;IAED,WAAW,CAAC,OAAsB,EAApC;QACI,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;YACvB,OAAO,CAAC,UAAU,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;SACrE;KACF;;;;IAED,WAAW,GAAb;QACI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B;;;;IAEO,QAAQ,GAAlB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;SAC7B;;;;;IAGK,OAAO,GAAjB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;gBACrD,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;SACJ;;;;IA7EL,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;gBACT,QAAQ,EAAE,qBAAqB;gBAC/B,QAAQ,EAAE,mBAAmB;aAC9B,EAAD,EAAA;;;;IAbA,EAAA,IAAA,EAAa,uBAAuB,GAApC;IArBA,EAAA,IAAA,EAAE,UAAU,GAAZ;IAQA,EAAA,IAAA,EAAE,MAAM,GAAR;;;IAgCA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAG,MAAM,EAAT,IAAA,EAAA,CAAU,mBAAmB,EAA7B,EAAA,EAAA;IAMA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,IAAA,EAAA,CAAS,2BAA2B,EAApC,EAAA,EAAA;IAUA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,EAAA;;AA8DA,AAAA,MAAA,eAAA,CAAA;;;IALA,EAAA,IAAA,EAAC,QAAQ,EAAT,IAAA,EAAA,CAAU;gBACR,OAAO,EAAE,CAAC,iBAAiB,CAAC;gBAC5B,YAAY,EAAE,CAAC,iBAAiB,CAAC;gBACjC,SAAS,EAAE,CAAC,uBAAuB,CAAC;aACrC,EAAD,EAAA;;;;;;;;GDvHA,AAMA,AAAsE;;;;;;;;GDVtE,AAA6B;;"}