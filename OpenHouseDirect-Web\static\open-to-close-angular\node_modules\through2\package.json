{"_from": "through2@^2.0.0", "_id": "through2@2.0.5", "_inBundle": false, "_integrity": "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==", "_location": "/through2", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "through2@^2.0.0", "name": "through2", "escapedName": "through2", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/mississippi"], "_resolved": "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz", "_shasum": "01c1e39eb31d07cb7d03a96a70823260b23132cd", "_spec": "through2@^2.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\mississippi", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rvagg"}, "bugs": {"url": "https://github.com/rvagg/through2/issues"}, "bundleDependencies": false, "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}, "deprecated": false, "description": "A tiny wrapper around Node streams2 Transform to avoid explicit subclassing noise", "devDependencies": {"bl": "~2.0.1", "faucet": "0.0.1", "nyc": "~13.1.0", "safe-buffer": "~5.1.2", "stream-spigot": "~3.0.6", "tape": "~4.9.1"}, "homepage": "https://github.com/rvagg/through2#readme", "keywords": ["stream", "streams2", "through", "transform"], "license": "MIT", "main": "through2.js", "name": "through2", "repository": {"type": "git", "url": "git+https://github.com/rvagg/through2.git"}, "scripts": {"test": "node test/test.js | faucet"}, "version": "2.0.5"}