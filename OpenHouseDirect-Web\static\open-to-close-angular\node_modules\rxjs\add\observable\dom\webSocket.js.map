{"version": 3, "file": "webSocket.js", "sourceRoot": "", "sources": ["../../../../src/add/observable/dom/webSocket.ts"], "names": [], "mappings": ";AAAA,2BAA2B,qBAAqB,CAAC,CAAA;AACjD,0BAA6C,mCAAmC,CAAC,CAAA;AAEjF,uBAAU,CAAC,SAAS,GAAG,qBAAe,CAAC", "sourcesContent": ["import { Observable } from '../../../Observable';\nimport { webSocket as staticWebSocket } from '../../../observable/dom/webSocket';\n\nObservable.webSocket = staticWebSocket;\n\ndeclare module '../../../Observable' {\n  namespace Observable {\n    export let webSocket: typeof staticWebSocket;\n  }\n}"]}