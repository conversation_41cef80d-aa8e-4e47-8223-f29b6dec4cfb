{"_from": "expand-brackets@^2.1.4", "_id": "expand-brackets@2.1.4", "_inBundle": false, "_integrity": "sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA==", "_location": "/watchpack-chokidar2/expand-brackets", "_phantomChildren": {"is-accessor-descriptor": "1.0.1", "is-data-descriptor": "1.0.1", "is-extendable": "0.1.1"}, "_requested": {"type": "range", "registry": true, "raw": "expand-brackets@^2.1.4", "name": "expand-brackets", "escapedName": "expand-brackets", "rawSpec": "^2.1.4", "saveSpec": null, "fetchSpec": "^2.1.4"}, "_requiredBy": ["/watchpack-chokidar2/extglob"], "_resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz", "_shasum": "b77735e315ce30f6b6eff0f83b04151a22449622", "_spec": "expand-brackets@^2.1.4", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\watchpack-chokidar2\\node_modules\\extglob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/expand-brackets/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "url": "https://github.com/eush77"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kolarik.sk"}], "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "deprecated": false, "description": "Expand POSIX bracket expressions (character classes) in glob patterns.", "devDependencies": {"bash-match": "^0.1.1", "gulp-format-md": "^0.1.10", "helper-changelog": "^0.3.0", "minimatch": "^3.0.3", "mocha": "^3.0.2", "multimatch": "^2.1.0", "yargs-parser": "^4.0.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/jonschlinkert/expand-brackets", "keywords": ["bracket", "brackets", "character class", "expand", "expression", "posix"], "license": "MIT", "main": "index.js", "name": "expand-brackets", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/expand-brackets.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "helpers": ["helper-changelog"], "related": {"list": ["braces", "extglob", "micromatch", "nanomatch"]}, "reflinks": ["micromatch", "verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "version": "2.1.4"}