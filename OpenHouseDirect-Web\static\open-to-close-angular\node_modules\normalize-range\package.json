{"_from": "normalize-range@^0.1.2", "_id": "normalize-range@0.1.2", "_inBundle": false, "_integrity": "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==", "_location": "/normalize-range", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize-range@^0.1.2", "name": "normalize-range", "escapedName": "normalize-range", "rawSpec": "^0.1.2", "saveSpec": null, "fetchSpec": "^0.1.2"}, "_requiredBy": ["/autoprefixer"], "_resolved": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "_shasum": "2d10c06bdfd312ea9777695a4d28439456b75942", "_spec": "normalize-range@^0.1.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\autoprefixer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "bugs": {"url": "https://github.com/jamestalmage/normalize-range/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Utility for normalizing a numeric range, with a wrapping function useful for polar coordinates", "devDependencies": {"almost-equal": "^1.0.0", "codeclimate-test-reporter": "^0.1.0", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "jscs": "^2.1.1", "jshint": "^2.8.0", "jshint-stylish": "^2.0.1", "mocha": "^2.2.5", "stringify-pi": "0.0.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jamestalmage/normalize-range#readme", "keywords": ["range", "normalize", "utility", "angle", "degrees", "polar"], "license": "MIT", "name": "normalize-range", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/normalize-range.git"}, "scripts": {"cover": "istanbul cover ./node_modules/.bin/_mocha", "debug": "mocha", "lint": "jshint --reporter=node_modules/jshint-stylish *.js test/*.js", "style": "jscs *.js ./**/*.js && jscs ./test/** --config=./test/.jscsrc", "test": "npm run cover && npm run lint && npm run style", "watch": "mocha -w"}, "version": "0.1.2"}