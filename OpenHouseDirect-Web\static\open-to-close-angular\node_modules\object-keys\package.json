{"_from": "object-keys@^1.1.1", "_id": "object-keys@1.1.1", "_inBundle": false, "_integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==", "_location": "/object-keys", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object-keys@^1.1.1", "name": "object-keys", "escapedName": "object-keys", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/deep-equal", "/define-properties", "/es-abstract", "/json-stable-stringify", "/object.assign", "/own-keys"], "_resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "_shasum": "1c47f272df277f3b1daf061677d9c82e2322c60e", "_spec": "object-keys@^1.1.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\json-stable-stringify", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}], "dependencies": {}, "deprecated": false, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "covert": "^1.1.1", "eslint": "^5.13.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "is": "^3.3.0", "tape": "^4.9.2"}, "engines": {"node": ">= 0.4"}, "homepage": "https://github.com/ljharb/object-keys#readme", "keywords": ["Object.keys", "keys", "ES5", "shim"], "license": "MIT", "main": "index.js", "name": "object-keys", "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "scripts": {"audit": "npm audit", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "eslint .", "postaudit": "rm package-lock.json", "posttest": "npm run --silent audit", "preaudit": "npm install --package-lock --package-lock-only", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "tests-only": "node test/index.js"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.1"}