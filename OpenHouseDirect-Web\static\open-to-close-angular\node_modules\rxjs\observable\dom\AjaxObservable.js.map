{"version": 3, "file": "AjaxObservable.js", "sourceRoot": "", "sources": ["../../../src/observable/dom/AjaxObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,qBAAqB,iBAAiB,CAAC,CAAA;AACvC,yBAAyB,qBAAqB,CAAC,CAAA;AAC/C,4BAA4B,wBAAwB,CAAC,CAAA;AACrD,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,2BAA2B,kBAAkB,CAAC,CAAA;AAE9C,oBAAoB,qBAAqB,CAAC,CAAA;AAmB1C;IACE,EAAE,CAAC,CAAC,WAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,IAAI,WAAI,CAAC,cAAc,EAAE,CAAC;IACnC,CAAC;IAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,WAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACjC,MAAM,CAAC,IAAI,WAAI,CAAC,cAAc,EAAE,CAAC;IACnC,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC;AAED;IACE,EAAE,CAAC,CAAC,WAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,IAAI,WAAI,CAAC,cAAc,EAAE,CAAC;IACnC,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,IAAI,MAAM,SAAQ,CAAC;QACnB,IAAI,CAAC;YACH,IAAM,OAAO,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;YAC9E,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACpB,EAAE,CAAC,CAAC,IAAI,WAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBACnC,KAAK,CAAC;oBACR,CAAC;gBACH,CAAE;gBAAA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEb,CAAC;YACH,CAAC;YACD,MAAM,CAAC,IAAI,WAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACxC,CAAE;QAAA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;AACH,CAAC;AAYD,iBAAwB,GAAW,EAAE,OAAsB;IAAtB,uBAAsB,GAAtB,cAAsB;IACzD,MAAM,CAAC,IAAI,cAAc,CAAe,EAAE,MAAM,EAAE,KAAK,EAAE,QAAG,EAAE,gBAAO,EAAE,CAAC,CAAC;AAC3E,CAAC;AAFe,eAAO,UAEtB,CAAA;AAAA,CAAC;AAEF,kBAAyB,GAAW,EAAE,IAAU,EAAE,OAAgB;IAChE,MAAM,CAAC,IAAI,cAAc,CAAe,EAAE,MAAM,EAAE,MAAM,EAAE,QAAG,EAAE,UAAI,EAAE,gBAAO,EAAE,CAAC,CAAC;AAClF,CAAC;AAFe,gBAAQ,WAEvB,CAAA;AAAA,CAAC;AAEF,oBAA2B,GAAW,EAAE,OAAgB;IACtD,MAAM,CAAC,IAAI,cAAc,CAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAG,EAAE,gBAAO,EAAE,CAAC,CAAC;AAC9E,CAAC;AAFe,kBAAU,aAEzB,CAAA;AAAA,CAAC;AAEF,iBAAwB,GAAW,EAAE,IAAU,EAAE,OAAgB;IAC/D,MAAM,CAAC,IAAI,cAAc,CAAe,EAAE,MAAM,EAAE,KAAK,EAAE,QAAG,EAAE,UAAI,EAAE,gBAAO,EAAE,CAAC,CAAC;AACjF,CAAC;AAFe,eAAO,UAEtB,CAAA;AAAA,CAAC;AAEF,mBAA0B,GAAW,EAAE,IAAU,EAAE,OAAgB;IACjE,MAAM,CAAC,IAAI,cAAc,CAAe,EAAE,MAAM,EAAE,OAAO,EAAE,QAAG,EAAE,UAAI,EAAE,gBAAO,EAAE,CAAC,CAAC;AACnF,CAAC;AAFe,iBAAS,YAExB,CAAA;AAAA,CAAC;AAEF,IAAM,WAAW,GAAG,SAAG,CAAC,UAAC,CAAe,EAAE,KAAa,IAAK,OAAA,CAAC,CAAC,QAAQ,EAAV,CAAU,CAAC,CAAC;AAExE,qBAA+B,GAAW,EAAE,OAAgB;IAC1D,MAAM,CAAC,WAAW,CAChB,IAAI,cAAc,CAAe;QAC/B,MAAM,EAAE,KAAK;QACb,QAAG;QACH,YAAY,EAAE,MAAM;QACpB,gBAAO;KACR,CAAC,CACH,CAAC;AACJ,CAAC;AATe,mBAAW,cAS1B,CAAA;AAAA,CAAC;AAEF;;;;GAIG;AACH;IAAuC,kCAAa;IA4ClD,wBAAY,YAAkC;QAC5C,iBAAO,CAAC;QAER,IAAM,OAAO,GAAgB;YAC3B,KAAK,EAAE,IAAI;YACX,SAAS,EAAE;gBACT,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,iBAAiB,EAAE,CAAC;YAC5E,CAAC;YACD,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,KAAK;YACtB,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,MAAM;YACpB,OAAO,EAAE,CAAC;SACX,CAAC;QAEF,EAAE,CAAC,CAAC,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC;YACrC,OAAO,CAAC,GAAG,GAAG,YAAY,CAAC;QAC7B,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC,CAAC,IAAM,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC;gBAChC,EAAE,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACtC,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,oCAAoC,CAAC,mCAAU,GAAV,UAAW,UAAyB;QACvE,MAAM,CAAC,IAAI,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IA1ED;;;;;;;;;;;;;;;;;;;;;;;;;MAyBE;IACK,qBAAM,GAAuB,CAAC;QACnC,IAAM,MAAM,GAAQ,UAAC,YAAkC;YACrD,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;QACrB,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC;QACvB,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;QAC3B,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;QACrB,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;QACzB,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;QAE7B,MAAM,CAAqB,MAAM,CAAC;IACpC,CAAC,CAAC,EAAE,CAAC;IAoCP,qBAAC;AAAD,CAAC,AA5ED,CAAuC,uBAAU,GA4EhD;AA5EY,sBAAc,iBA4E1B,CAAA;AAED;;;;GAIG;AACH;IAAuC,kCAAiB;IAItD,wBAAY,WAA0B,EAAS,OAAoB;QACjE,kBAAM,WAAW,CAAC,CAAC;QAD0B,YAAO,GAAP,OAAO,CAAa;QAF3D,SAAI,GAAY,KAAK,CAAC;QAK5B,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QAExD,0BAA0B;QAC1B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACzD,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB,CAAC;QACjD,CAAC;QAED,6BAA6B;QAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,WAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,YAAY,WAAI,CAAC,QAAQ,CAAC,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC;YACrI,OAAO,CAAC,cAAc,CAAC,GAAG,kDAAkD,CAAC;QAC/E,CAAC;QAED,0BAA0B;QAC1B,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;QAEjF,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAED,6BAAI,GAAJ,UAAK,CAAQ;QACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAA,SAA0C,EAAlC,YAAG,EAAE,oBAAO,EAAE,4BAAW,CAAU;QAC3C,IAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAEnD,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEO,6BAAI,GAAZ;QACE,IAAA,SAGQ,EAFN,oBAAO,EACP,eAA8D,EAAnD,cAAI,EAAE,kBAAM,EAAE,YAAG,EAAE,gBAAK,EAAE,sBAAQ,EAAE,oBAAO,EAAE,cAAI,CACrD;QACT,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAM,GAAG,GAAmB,mBAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9D,EAAE,CAAC,CAAM,GAAG,KAAK,yBAAW,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YAEf,oCAAoC;YACpC,oFAAoF;YACpF,4EAA4E;YAC5E,+CAA+C;YAC/C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC/B,WAAW;YACX,IAAI,MAAM,SAAK,CAAC;YAChB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACT,MAAM,GAAG,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC5E,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,MAAM,GAAG,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;YAED,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;gBAC3B,IAAI,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC;YACd,CAAC;YAED,4EAA4E;YAC5E,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACV,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;gBAC9B,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAmB,CAAC;YACjD,CAAC;YAED,EAAE,CAAC,CAAC,iBAAiB,IAAI,GAAG,CAAC,CAAC,CAAC;gBAC7B,GAAG,CAAC,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAClD,CAAC;YAED,cAAc;YACd,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAE9B,2BAA2B;YAC3B,MAAM,GAAG,IAAI,GAAG,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClF,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;gBAC3B,IAAI,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,MAAM,CAAC,GAAG,CAAC;IACb,CAAC;IAEO,sCAAa,GAArB,UAAsB,IAAS,EAAE,WAAoB;QACnD,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAI,CAAC,QAAQ,IAAI,IAAI,YAAY,WAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QAED,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAChB,IAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5C,EAAE,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACpB,KAAK,mCAAmC;gBACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,CAAG,kBAAkB,CAAC,GAAG,CAAC,SAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAE,EAA7D,CAA6D,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/G,KAAK,kBAAkB;gBACrB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9B;gBACE,MAAM,CAAC,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,mCAAU,GAAlB,UAAmB,GAAmB,EAAE,OAAe;QACrD,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC;YACxB,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChC,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IAEO,oCAAW,GAAnB,UAAoB,GAAmB,EAAE,OAAoB;QAC3D,IAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAEtD,oBAA0C,CAAgB;YACxD,IAAA,eAAoE,EAA7D,0BAAU,EAAE,0CAAkB,EAAE,oBAAO,CAAuB;YACrE,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;gBACvB,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,UAAU,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,sBAAsB;QAC/E,CAAC;QAAA,CAAC;QACF,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC;QACrB,UAAW,CAAC,OAAO,GAAG,OAAO,CAAC;QAC9B,UAAW,CAAC,UAAU,GAAG,IAAI,CAAC;QAC9B,UAAW,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC1D,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,iBAAiB,IAAI,GAAG,CAAC,CAAC,CAAC;YAC3C,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;gBACvB,IAAI,aAAuC,CAAC;gBAC5C,aAAW,GAAG,UAAS,CAAgB;oBAC7B,yDAAkB,CAAwB;oBAClD,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC;gBACF,EAAE,CAAC,CAAC,WAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBACxB,GAAG,CAAC,UAAU,GAAG,aAAW,CAAC;gBAC/B,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,aAAW,CAAC;gBACtC,CAAC;gBACK,aAAY,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC7D,CAAC;YACD,IAAI,UAAiC,CAAC;YACtC,UAAQ,GAAG,UAA+B,CAAa;gBACrD,IAAA,eAAmE,EAA3D,0CAAkB,EAAE,0BAAU,EAAE,oBAAO,CAAqB;gBACpE,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBACvB,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9B,CAAC;gBACD,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YAC/D,CAAC,CAAC;YACF,GAAG,CAAC,OAAO,GAAG,UAAQ,CAAC;YACjB,UAAS,CAAC,OAAO,GAAG,OAAO,CAAC;YAC5B,UAAS,CAAC,UAAU,GAAG,IAAI,CAAC;YAC5B,UAAS,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC1D,CAAC;QAED,6BAAmD,CAAgB;YACjE,IAAA,wBAA8E,EAAtE,0BAAU,EAAE,0CAAkB,EAAE,oBAAO,CAAgC;YAC/E,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,yDAAyD;gBACzD,IAAI,QAAM,GAAW,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC9D,IAAI,QAAQ,GAAQ,CAAC,IAAI,CAAC,YAAY,KAAK,MAAM,GAAI,CACnD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEvD,2DAA2D;gBAC3D,uEAAuE;gBACvE,iDAAiD;gBACjD,EAAE,CAAC,CAAC,QAAM,KAAK,CAAC,CAAC,CAAC,CAAC;oBACjB,QAAM,GAAG,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;gBAC9B,CAAC;gBAED,EAAE,CAAC,CAAC,GAAG,IAAI,QAAM,IAAI,QAAM,GAAG,GAAG,CAAC,CAAC,CAAC;oBAClC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;wBACvB,kBAAkB,CAAC,QAAQ,EAAE,CAAC;oBAChC,CAAC;oBACD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACnB,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;wBACvB,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;oBACD,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,aAAa,GAAG,QAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC;QACH,CAAC;QAAA,CAAC;QACF,GAAG,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;QACvC,mBAAoB,CAAC,UAAU,GAAG,IAAI,CAAC;QACvC,mBAAoB,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7D,mBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/C,CAAC;IAED,oCAAW,GAAX;QACE,IAAA,SAA0B,EAAlB,cAAI,EAAE,YAAG,CAAU;QAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC;YAC5E,GAAG,CAAC,KAAK,EAAE,CAAC;QACd,CAAC;QACD,gBAAK,CAAC,WAAW,WAAE,CAAC;IACtB,CAAC;IACH,qBAAC;AAAD,CAAC,AA5MD,CAAuC,uBAAU,GA4MhD;AA5MY,sBAAc,iBA4M1B,CAAA;AAED;;;;;;GAMG;AACH;IAaE,sBAAmB,aAAoB,EAAS,GAAmB,EAAS,OAAoB;QAA7E,kBAAa,GAAb,aAAa,CAAO;QAAS,QAAG,GAAH,GAAG,CAAgB;QAAS,YAAO,GAAP,OAAO,CAAa;QAC9F,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IAC3D,CAAC;IACH,mBAAC;AAAD,CAAC,AAlBD,IAkBC;AAlBY,oBAAY,eAkBxB,CAAA;AAED;;;;;;GAMG;AACH;IAA+B,6BAAK;IAgBlC,mBAAY,OAAe,EAAE,GAAmB,EAAE,OAAoB;QACpE,kBAAM,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IAC3D,CAAC;IACH,gBAAC;AAAD,CAAC,AAzBD,CAA+B,KAAK,GAyBnC;AAzBY,iBAAS,YAyBrB,CAAA;AAED,0BAA0B,YAAoB,EAAE,GAAmB;IACjE,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QACrB,KAAK,MAAM;YACP,EAAE,CAAC,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC;gBACtB,+DAA+D;gBAC/D,MAAM,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,YAAY,IAAI,MAAM,CAAC,CAAC;YAClG,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,yCAAyC;gBACzC,gFAAgF;gBAChF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAE,GAAW,CAAC,YAAY,IAAI,MAAM,CAAC,CAAC;YACzD,CAAC;QACH,KAAK,KAAK;YACR,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC;QACzB,KAAK,MAAM,CAAC;QACZ;YACI,yCAAyC;YACzC,gFAAgF;YAChF,MAAM,CAAE,CAAC,UAAU,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,QAAQ,GAAI,GAAW,CAAC,YAAY,CAAC;IAC/E,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH;IAAsC,oCAAS;IAC7C,0BAAY,GAAmB,EAAE,OAAoB;QACnD,kBAAM,cAAc,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IACH,uBAAC;AAAD,CAAC,AAJD,CAAsC,SAAS,GAI9C;AAJY,wBAAgB,mBAI5B,CAAA", "sourcesContent": ["import { root } from '../../util/root';\nimport { tryCatch } from '../../util/tryCatch';\nimport { errorObject } from '../../util/errorObject';\nimport { Observable } from '../../Observable';\nimport { Subscriber } from '../../Subscriber';\nimport { TeardownLogic } from '../../Subscription';\nimport { map } from '../../operators/map';\n\nexport interface AjaxRequest {\n  url?: string;\n  body?: any;\n  user?: string;\n  async?: boolean;\n  method?: string;\n  headers?: Object;\n  timeout?: number;\n  password?: string;\n  hasContent?: boolean;\n  crossDomain?: boolean;\n  withCredentials?: boolean;\n  createXHR?: () => XMLHttpRequest;\n  progressSubscriber?: Subscriber<any>;\n  responseType?: string;\n}\n\nfunction getCORSRequest(this: AjaxRequest): XMLHttpRequest {\n  if (root.XMLHttpRequest) {\n    return new root.XMLHttpRequest();\n  } else if (!!root.XDomainRequest) {\n    return new root.XDomainRequest();\n  } else {\n    throw new Error('CORS is not supported by your browser');\n  }\n}\n\nfunction getXMLHttpRequest(): XMLHttpRequest {\n  if (root.XMLHttpRequest) {\n    return new root.XMLHttpRequest();\n  } else {\n    let progId: string;\n    try {\n      const progIds = ['Msxml2.XMLHTTP', 'Microsoft.XMLHTTP', 'Msxml2.XMLHTTP.4.0'];\n      for (let i = 0; i < 3; i++) {\n        try {\n          progId = progIds[i];\n          if (new root.ActiveXObject(progId)) {\n            break;\n          }\n        } catch (e) {\n          //suppress exceptions\n        }\n      }\n      return new root.ActiveXObject(progId);\n    } catch (e) {\n      throw new Error('XMLHttpRequest is not supported by your browser');\n    }\n  }\n}\n\nexport interface AjaxCreationMethod {\n  (urlOrRequest: string | AjaxRequest): Observable<AjaxResponse>;\n  get(url: string, headers?: Object): Observable<AjaxResponse>;\n  post(url: string, body?: any, headers?: Object): Observable<AjaxResponse>;\n  put(url: string, body?: any, headers?: Object): Observable<AjaxResponse>;\n  patch(url: string, body?: any, headers?: Object): Observable<AjaxResponse>;\n  delete(url: string, headers?: Object): Observable<AjaxResponse>;\n  getJSON<T>(url: string, headers?: Object): Observable<T>;\n}\n\nexport function ajaxGet(url: string, headers: Object = null) {\n  return new AjaxObservable<AjaxResponse>({ method: 'GET', url, headers });\n};\n\nexport function ajaxPost(url: string, body?: any, headers?: Object): Observable<AjaxResponse> {\n  return new AjaxObservable<AjaxResponse>({ method: 'POST', url, body, headers });\n};\n\nexport function ajaxDelete(url: string, headers?: Object): Observable<AjaxResponse> {\n  return new AjaxObservable<AjaxResponse>({ method: 'DELETE', url, headers });\n};\n\nexport function ajaxPut(url: string, body?: any, headers?: Object): Observable<AjaxResponse> {\n  return new AjaxObservable<AjaxResponse>({ method: 'PUT', url, body, headers });\n};\n\nexport function ajaxPatch(url: string, body?: any, headers?: Object): Observable<AjaxResponse> {\n  return new AjaxObservable<AjaxResponse>({ method: 'PATCH', url, body, headers });\n};\n\nconst mapResponse = map((x: AjaxResponse, index: number) => x.response);\n\nexport function ajaxGetJSON<T>(url: string, headers?: Object): Observable<T> {\n  return mapResponse(\n    new AjaxObservable<AjaxResponse>({\n      method: 'GET',\n      url,\n      responseType: 'json',\n      headers\n    })\n  );\n};\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class AjaxObservable<T> extends Observable<T> {\n  /**\n   * Creates an observable for an Ajax request with either a request object with\n   * url, headers, etc or a string for a URL.\n   *\n   * @example\n   * source = Rx.Observable.ajax('/products');\n   * source = Rx.Observable.ajax({ url: 'products', method: 'GET' });\n   *\n   * @param {string|Object} request Can be one of the following:\n   *   A string of the URL to make the Ajax call.\n   *   An object with the following properties\n   *   - url: URL of the request\n   *   - body: The body of the request\n   *   - method: Method of the request, such as GET, POST, PUT, PATCH, DELETE\n   *   - async: Whether the request is async\n   *   - headers: Optional headers\n   *   - crossDomain: true if a cross domain request, else false\n   *   - createXHR: a function to override if you need to use an alternate\n   *   XMLHttpRequest implementation.\n   *   - resultSelector: a function to use to alter the output value type of\n   *   the Observable. Gets {@link AjaxResponse} as an argument.\n   * @return {Observable} An observable sequence containing the XMLHttpRequest.\n   * @static true\n   * @name ajax\n   * @owner Observable\n  */\n  static create: AjaxCreationMethod = (() => {\n    const create: any = (urlOrRequest: string | AjaxRequest) => {\n      return new AjaxObservable(urlOrRequest);\n    };\n\n    create.get = ajaxGet;\n    create.post = ajaxPost;\n    create.delete = ajaxDelete;\n    create.put = ajaxPut;\n    create.patch = ajaxPatch;\n    create.getJSON = ajaxGetJSON;\n\n    return <AjaxCreationMethod>create;\n  })();\n\n  private request: AjaxRequest;\n\n  constructor(urlOrRequest: string | AjaxRequest) {\n    super();\n\n    const request: AjaxRequest = {\n      async: true,\n      createXHR: function(this: AjaxRequest) {\n        return this.crossDomain ? getCORSRequest.call(this) : getXMLHttpRequest();\n      },\n      crossDomain: false,\n      withCredentials: false,\n      headers: {},\n      method: 'GET',\n      responseType: 'json',\n      timeout: 0\n    };\n\n    if (typeof urlOrRequest === 'string') {\n      request.url = urlOrRequest;\n    } else {\n      for (const prop in urlOrRequest) {\n        if (urlOrRequest.hasOwnProperty(prop)) {\n          request[prop] = urlOrRequest[prop];\n        }\n      }\n    }\n\n    this.request = request;\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n    return new AjaxSubscriber(subscriber, this.request);\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nexport class AjaxSubscriber<T> extends Subscriber<Event> {\n  private xhr: XMLHttpRequest;\n  private done: boolean = false;\n\n  constructor(destination: Subscriber<T>, public request: AjaxRequest) {\n    super(destination);\n\n    const headers = request.headers = request.headers || {};\n\n    // force CORS if requested\n    if (!request.crossDomain && !headers['X-Requested-With']) {\n      headers['X-Requested-With'] = 'XMLHttpRequest';\n    }\n\n    // ensure content type is set\n    if (!('Content-Type' in headers) && !(root.FormData && request.body instanceof root.FormData) && typeof request.body !== 'undefined') {\n      headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';\n    }\n\n    // properly serialize body\n    request.body = this.serializeBody(request.body, request.headers['Content-Type']);\n\n    this.send();\n  }\n\n  next(e: Event): void {\n    this.done = true;\n    const { xhr, request, destination } = this;\n    const response = new AjaxResponse(e, xhr, request);\n\n    destination.next(response);\n  }\n\n  private send(): XMLHttpRequest {\n    const {\n      request,\n      request: { user, method, url, async, password, headers, body }\n    } = this;\n    const createXHR = request.createXHR;\n    const xhr: XMLHttpRequest = tryCatch(createXHR).call(request);\n\n    if (<any>xhr === errorObject) {\n      this.error(errorObject.e);\n    } else {\n      this.xhr = xhr;\n\n      // set up the events before open XHR\n      // https://developer.mozilla.org/en/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest\n      // You need to add the event listeners before calling open() on the request.\n      // Otherwise the progress events will not fire.\n      this.setupEvents(xhr, request);\n      // open XHR\n      let result: any;\n      if (user) {\n        result = tryCatch(xhr.open).call(xhr, method, url, async, user, password);\n      } else {\n        result = tryCatch(xhr.open).call(xhr, method, url, async);\n      }\n\n      if (result === errorObject) {\n        this.error(errorObject.e);\n        return null;\n      }\n\n      // timeout, responseType and withCredentials can be set once the XHR is open\n      if (async) {\n        xhr.timeout = request.timeout;\n        xhr.responseType = request.responseType as any;\n      }\n\n      if ('withCredentials' in xhr) {\n        xhr.withCredentials = !!request.withCredentials;\n      }\n\n      // set headers\n      this.setHeaders(xhr, headers);\n\n      // finally send the request\n      result = body ? tryCatch(xhr.send).call(xhr, body) : tryCatch(xhr.send).call(xhr);\n      if (result === errorObject) {\n        this.error(errorObject.e);\n        return null;\n      }\n    }\n\n    return xhr;\n  }\n\n  private serializeBody(body: any, contentType?: string) {\n    if (!body || typeof body === 'string') {\n      return body;\n    } else if (root.FormData && body instanceof root.FormData) {\n      return body;\n    }\n\n    if (contentType) {\n      const splitIndex = contentType.indexOf(';');\n      if (splitIndex !== -1) {\n        contentType = contentType.substring(0, splitIndex);\n      }\n    }\n\n    switch (contentType) {\n      case 'application/x-www-form-urlencoded':\n        return Object.keys(body).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(body[key])}`).join('&');\n      case 'application/json':\n        return JSON.stringify(body);\n      default:\n        return body;\n    }\n  }\n\n  private setHeaders(xhr: XMLHttpRequest, headers: Object) {\n    for (let key in headers) {\n      if (headers.hasOwnProperty(key)) {\n        xhr.setRequestHeader(key, headers[key]);\n      }\n    }\n  }\n\n  private setupEvents(xhr: XMLHttpRequest, request: AjaxRequest) {\n    const progressSubscriber = request.progressSubscriber;\n\n    function xhrTimeout(this: XMLHttpRequest, e: ProgressEvent) {\n      const {subscriber, progressSubscriber, request } = (<any>xhrTimeout);\n      if (progressSubscriber) {\n        progressSubscriber.error(e);\n      }\n      subscriber.error(new AjaxTimeoutError(this, request)); //TODO: Make betterer.\n    };\n    xhr.ontimeout = xhrTimeout;\n    (<any>xhrTimeout).request = request;\n    (<any>xhrTimeout).subscriber = this;\n    (<any>xhrTimeout).progressSubscriber = progressSubscriber;\n    if (xhr.upload && 'withCredentials' in xhr) {\n      if (progressSubscriber) {\n        let xhrProgress: (e: ProgressEvent) => void;\n        xhrProgress = function(e: ProgressEvent) {\n          const { progressSubscriber } = (<any>xhrProgress);\n          progressSubscriber.next(e);\n        };\n        if (root.XDomainRequest) {\n          xhr.onprogress = xhrProgress;\n        } else {\n          xhr.upload.onprogress = xhrProgress;\n        }\n        (<any>xhrProgress).progressSubscriber = progressSubscriber;\n      }\n      let xhrError: (e: ErrorEvent) => void;\n      xhrError = function(this: XMLHttpRequest, e: ErrorEvent) {\n        const { progressSubscriber, subscriber, request } = (<any>xhrError);\n        if (progressSubscriber) {\n          progressSubscriber.error(e);\n        }\n        subscriber.error(new AjaxError('ajax error', this, request));\n      };\n      xhr.onerror = xhrError;\n      (<any>xhrError).request = request;\n      (<any>xhrError).subscriber = this;\n      (<any>xhrError).progressSubscriber = progressSubscriber;\n    }\n\n    function xhrReadyStateChange(this: XMLHttpRequest, e: ProgressEvent) {\n      const { subscriber, progressSubscriber, request } = (<any>xhrReadyStateChange);\n      if (this.readyState === 4) {\n        // normalize IE9 bug (http://bugs.jquery.com/ticket/1450)\n        let status: number = this.status === 1223 ? 204 : this.status;\n        let response: any = (this.responseType === 'text' ?  (\n          this.response || this.responseText) : this.response);\n\n        // fix status code when it is 0 (0 status is undocumented).\n        // Occurs when accessing file resources or on Android 4.1 stock browser\n        // while retrieving files from application cache.\n        if (status === 0) {\n          status = response ? 200 : 0;\n        }\n\n        if (200 <= status && status < 300) {\n          if (progressSubscriber) {\n            progressSubscriber.complete();\n          }\n          subscriber.next(e);\n          subscriber.complete();\n        } else {\n          if (progressSubscriber) {\n            progressSubscriber.error(e);\n          }\n          subscriber.error(new AjaxError('ajax error ' + status, this, request));\n        }\n      }\n    };\n    xhr.onreadystatechange = xhrReadyStateChange;\n    (<any>xhrReadyStateChange).subscriber = this;\n    (<any>xhrReadyStateChange).progressSubscriber = progressSubscriber;\n    (<any>xhrReadyStateChange).request = request;\n  }\n\n  unsubscribe() {\n    const { done, xhr } = this;\n    if (!done && xhr && xhr.readyState !== 4 && typeof xhr.abort === 'function') {\n      xhr.abort();\n    }\n    super.unsubscribe();\n  }\n}\n\n/**\n * A normalized AJAX response.\n *\n * @see {@link ajax}\n *\n * @class AjaxResponse\n */\nexport class AjaxResponse {\n  /** @type {number} The HTTP status code */\n  status: number;\n\n  /** @type {string|ArrayBuffer|Document|object|any} The response data */\n  response: any;\n\n  /** @type {string} The raw responseText */\n  responseText: string;\n\n  /** @type {string} The responseType (e.g. 'json', 'arraybuffer', or 'xml') */\n  responseType: string;\n\n  constructor(public originalEvent: Event, public xhr: XMLHttpRequest, public request: AjaxRequest) {\n    this.status = xhr.status;\n    this.responseType = xhr.responseType || request.responseType;\n    this.response = parseXhrResponse(this.responseType, xhr);\n  }\n}\n\n/**\n * A normalized AJAX error.\n *\n * @see {@link ajax}\n *\n * @class AjaxError\n */\nexport class AjaxError extends Error {\n  /** @type {XMLHttpRequest} The XHR instance associated with the error */\n  xhr: XMLHttpRequest;\n\n  /** @type {AjaxRequest} The AjaxRequest associated with the error */\n  request: AjaxRequest;\n\n  /** @type {number} The HTTP status code */\n  status: number;\n\n  /** @type {string} The responseType (e.g. 'json', 'arraybuffer', or 'xml') */\n  responseType: string;\n\n  /** @type {string|ArrayBuffer|Document|object|any} The response data */\n  response: any;\n\n  constructor(message: string, xhr: XMLHttpRequest, request: AjaxRequest) {\n    super(message);\n    this.message = message;\n    this.xhr = xhr;\n    this.request = request;\n    this.status = xhr.status;\n    this.responseType = xhr.responseType || request.responseType;\n    this.response = parseXhrResponse(this.responseType, xhr);\n  }\n}\n\nfunction parseXhrResponse(responseType: string, xhr: XMLHttpRequest) {\n  switch (responseType) {\n    case 'json':\n        if ('response' in xhr) {\n          //IE does not support json as responseType, parse it internally\n          return xhr.responseType ? xhr.response : JSON.parse(xhr.response || xhr.responseText || 'null');\n        } else {\n          // HACK(benlesh): TypeScript shennanigans\n          // tslint:disable-next-line:no-any latest TS seems to think xhr is \"never\" here.\n          return JSON.parse((xhr as any).responseText || 'null');\n        }\n      case 'xml':\n        return xhr.responseXML;\n      case 'text':\n      default:\n          // HACK(benlesh): TypeScript shennanigans\n          // tslint:disable-next-line:no-any latest TS seems to think xhr is \"never\" here.\n          return  ('response' in xhr) ? xhr.response : (xhr as any).responseText;\n  }\n}\n\n/**\n * @see {@link ajax}\n *\n * @class AjaxTimeoutError\n */\nexport class AjaxTimeoutError extends AjaxError {\n  constructor(xhr: XMLHttpRequest, request: AjaxRequest) {\n    super('ajax timeout', xhr, request);\n  }\n}\n"]}