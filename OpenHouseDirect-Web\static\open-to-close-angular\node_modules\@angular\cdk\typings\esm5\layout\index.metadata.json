{"__symbolic": "module", "version": 4, "metadata": {"LayoutModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 12, "character": 1}, "arguments": [{"providers": [{"__symbolic": "reference", "name": "BreakpointObserver"}, {"__symbolic": "reference", "name": "MediaMatcher"}], "imports": [{"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "PlatformModule", "line": 14, "character": 12}]}]}], "members": {}}, "BreakpointObserver": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 30, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "MediaMatcher"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 37, "character": 64}]}], "ngOnDestroy": [{"__symbolic": "method"}], "isMatched": [{"__symbolic": "method"}], "observe": [{"__symbolic": "method"}], "_registerQuery": [{"__symbolic": "method"}]}}, "BreakpointState": {"__symbolic": "interface"}, "Breakpoints": {"XSmall": "(max-width: 599px)", "Small": "(min-width: 600px) and (max-width: 959px)", "Medium": "(min-width: 960px) and (max-width: 1279px)", "Large": "(min-width: 1280px) and (max-width: 1919px)", "XLarge": "(min-width: 1920px)", "Handset": "(max-width: 599px) and (orientation: portrait), (max-width: 959px) and (orientation: landscape)", "Tablet": "(min-width: 600px) and (max-width: 839px) and (orientation: portrait), (min-width: 960px) and (max-width: 1279px) and (orientation: landscape)", "Web": "(min-width: 840px) and (orientation: portrait), (min-width: 1280px) and (orientation: landscape)", "HandsetPortrait": "(max-width: 599px) and (orientation: portrait)", "TabletPortrait": "(min-width: 600px) and (max-width: 839px) and (orientation: portrait)", "WebPortrait": "(min-width: 840px) and (orientation: portrait)", "HandsetLandscape": "(max-width: 959px) and (orientation: landscape)", "TabletLandscape": "(min-width: 960px) and (max-width: 1279px) and (orientation: landscape)", "WebLandscape": "(min-width: 1280px) and (orientation: landscape)"}, "MediaMatcher": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 16, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 21, "character": 32}]}], "matchMedia": [{"__symbolic": "method"}]}}}, "origins": {"LayoutModule": "./public-api", "BreakpointObserver": "./breakpoints-observer", "BreakpointState": "./breakpoints-observer", "Breakpoints": "./breakpoints", "MediaMatcher": "./media-matcher"}, "importAs": "@angular/cdk/layout"}