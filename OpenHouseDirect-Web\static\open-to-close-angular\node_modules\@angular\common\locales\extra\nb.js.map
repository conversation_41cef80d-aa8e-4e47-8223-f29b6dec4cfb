{"version": 3, "file": "nb.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/nb.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QAC1C,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;QACvD,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,SAAS,EAAE,QAAQ,CAAC;KAC7E;IACD;QACE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QAC1C,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;QACvD,CAAC,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,CAAC;KACnE;IACD;QACE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QACvF,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['mn.', 'mg.', 'fm.', 'em.', 'kv.', 'nt.'],\n    ['midn.', 'morg.', 'form.', 'etterm.', 'kveld', 'natt'],\n    ['midnatt', 'morgenen', 'formiddagen', 'ettermiddagen', 'kvelden', 'natten']\n  ],\n  [\n    ['mn.', 'mg.', 'fm.', 'em.', 'kv.', 'nt.'],\n    ['midn.', 'morg.', 'form.', 'etterm.', 'kveld', 'natt'],\n    ['midnatt', 'morgen', 'formiddag', 'ettermiddag', 'kveld', 'natt']\n  ],\n  [\n    '00:00', ['06:00', '10:00'], ['10:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'],\n    ['00:00', '06:00']\n  ]\n];\n"]}