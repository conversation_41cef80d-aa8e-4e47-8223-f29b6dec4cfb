{"version": 3, "file": "empty.js", "sourceRoot": "", "sources": ["../../../src/add/observable/empty.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { empty as staticEmpty } from '../../observable/empty';\n\nObservable.empty = staticEmpty;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let empty: typeof staticEmpty;\n  }\n}"]}