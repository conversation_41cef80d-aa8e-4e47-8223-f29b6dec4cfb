{"version": 3, "file": "sq.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/sq.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY;YACpF,SAAS;SACV;QACD,AADE;KAEH;IACD;QACE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;QAC3E,AAD4E;KAE7E;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'e mesnatës', 'e mesditës', 'e mëngjesit', 'e paradites', 'e pasdites', 'e mbrëmjes',\n      'e natës'\n    ],\n    ,\n  ],\n  [\n    ['mesnatë', 'mesditë', 'mëngjes', 'paradite', 'pasdite', 'mbrëmje', 'natë'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['04:00', '09:00'], ['09:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '24:00'], ['00:00', '04:00']\n  ]\n];\n"]}