{"version": 3, "file": "bin.js", "sourceRoot": "", "sources": ["../src/bin.ts"], "names": [], "mappings": ";;;AAEA,+CAAqC;AACrC,6BAA2B;AAC3B,iCAAmC;AAEnC,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AAClC,IAAM,OAAO,GAAqB,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;AAEnE,OAAO,CAAC,UAAU,GAAG,EAAE,OAAO;IAC5B,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACR,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACxB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACf,MAAM,CAAA;IACR,CAAC;IAED,IAAM,QAAQ,GAAa,EAAE,CAAA;IAC7B,IAAM,UAAU,GAAa,EAAE,CAAA;IAE/B,IAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;QAChC,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,eAAe;QACf,UAAU;QACV,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,qBAAqB;QACrB,wBAAwB;QACxB,mBAAmB;QACnB,qBAAqB;QACrB,aAAa;QACb,gBAAgB;QAChB,kBAAkB;KACnB,CAAC,CAAA;IAEF,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,IAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAEjC,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACpB,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACtB,CAAC;QAAC,IAAI,CAAC,CAAC;YAEN,UAAU,CAAC,IAAI,OAAf,UAAU,EAAS,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;YACjC,KAAK,CAAA;QACP,CAAC;IACH,CAAC;IAED,IAAM,IAAI,GAAG,qBAAK,CAChB,OAAO,CAAC,QAAQ,EAChB,QAAQ,CAAC,MAAM,CAAC,WAAI,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,EACvD;QAYE,QAAQ,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO;QACtC,KAAK,EAAE,SAAS;KACjB,CACF,CAAA;IAGD,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAjB,CAAiB,CAAC,EAA3C,CAA2C,CAAC,CAAA;IAGtE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,IAAY,EAAE,MAAc;QAC5C,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QACnC,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC;IACH,CAAC,CAAC,CAAA;IAGF,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,IAAI,CAAC,IAAI,EAAE,EAAX,CAAW,CAAC,CAAA;AACvC,CAAC,CAAC,CAAA", "sourcesContent": ["#!/usr/bin/env node\n\nimport { spawn } from 'child_process'\nimport { join } from 'path'\nimport v8flags = require('v8flags')\n\nconst argv = process.argv.slice(2)\nconst signals: NodeJS.Signals[] = ['SIGINT', 'SIGTERM', 'SIGWINCH']\n\nv8flags(function (err, v8flags) {\n  if (err) {\n    console.error(err.stack)\n    process.exit(1)\n    return\n  }\n\n  const nodeArgs: string[] = []\n  const scriptArgs: string[] = []\n\n  const knownFlags = v8flags.concat([\n    'debug',\n    'inspect',\n    '--debug',\n    '--debug-brk',\n    '--inspect',\n    '--inspect-brk',\n    '--nolazy',\n    '--no-deprecation',\n    '--log-timer-events',\n    '--throw-deprecation',\n    '--trace-deprecation',\n    '--allow-natives-syntax',\n    '--perf-basic-prof',\n    '--preserve-symlinks',\n    '--expose-gc',\n    '--expose-http2',\n    '--trace-warnings'\n  ])\n\n  for (let i = 0; i < argv.length; i++) {\n    const arg = argv[i]\n    const flag = arg.split('=', 1)[0]\n\n    if (knownFlags.indexOf(flag) > -1) {\n      nodeArgs.push(arg)\n    } else if (/^-/.test(flag)) {\n      scriptArgs.push(arg)\n    } else {\n      // Break when we encounter a \"script\".\n      scriptArgs.push(...argv.slice(i))\n      break\n    }\n  }\n\n  const proc = spawn(\n    process.execPath,\n    nodeArgs.concat(join(__dirname, '_bin.js'), scriptArgs),\n    {\n      // We need to run in detached mode so to avoid\n      // automatic propagation of signals to the child process.\n      // This is necessary because by default, keyboard interrupts\n      // are propagated to the process tree, but `kill` is not.\n      //\n      // See: https://nodejs.org/api/child_process.html#child_process_options_detached\n      //\n      // This fix is not being required on Windows; besides, detached mode\n      // runs differently on Windows than on other platforms, and it would break\n      // the behavior of this application. See https://github.com/TypeStrong/ts-node/issues/480\n      // for more details.\n      detached: process.platform !== 'win32',\n      stdio: 'inherit'\n    }\n  )\n\n  // Ignore signals, and instead forward them to the child process.\n  signals.forEach(signal => process.on(signal, () => proc.kill(signal)))\n\n  // On spawned close, exit this process with the same code.\n  proc.on('close', (code: number, signal: string) => {\n    if (signal) {\n      process.kill(process.pid, signal)\n    } else {\n      process.exit(code)\n    }\n  })\n\n  // If this process exits, kill the child first.\n  process.on('exit', () => proc.kill())\n})\n"]}