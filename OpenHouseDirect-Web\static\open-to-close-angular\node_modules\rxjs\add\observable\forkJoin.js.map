{"version": 3, "file": "forkJoin.js", "sourceRoot": "", "sources": ["../../../src/add/observable/forkJoin.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,yBAA2C,2BAA2B,CAAC,CAAA;AAEvE,uBAAU,CAAC,QAAQ,GAAG,mBAAc,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { forkJoin as staticForkJoin } from '../../observable/forkJoin';\n\nObservable.forkJoin = staticForkJoin;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let forkJoin: typeof staticForkJoin;\n  }\n}"]}