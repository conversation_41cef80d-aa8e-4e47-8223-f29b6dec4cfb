{"version": 3, "file": "collections.es5.js", "sources": ["../../../src/cdk/collections/index.ts", "../../../src/cdk/collections/public-api.ts", "../../../src/cdk/collections/unique-selection-dispatcher.ts", "../../../src/cdk/collections/selection.ts", "../../../src/cdk/collections/data-source.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n\nexport {UNIQUE_SELECTION_DISPATCHER_PROVIDER_FACTORY as ɵa} from './unique-selection-dispatcher';", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './collection-viewer';\nexport * from './data-source';\nexport * from './selection';\nexport {\n  UniqueSelectionDispatcher,\n  UniqueSelectionDispatcherListener,\n  UNIQUE_SELECTION_DISPATCHER_PROVIDER,\n} from './unique-selection-dispatcher';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Optional, SkipSelf, OnDestroy} from '@angular/core';\n\n\n// Users of the Dispatcher never need to see this type, but TypeScript requires it to be exported.\nexport type UniqueSelectionDispatcherListener = (id: string, name: string) => void;\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\n@Injectable()\nexport class UniqueSelectionDispatcher implements OnDestroy {\n  private _listeners: UniqueSelectionDispatcherListener[] = [];\n\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id: string, name: string) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener: UniqueSelectionDispatcherListener): () => void {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter((registered: UniqueSelectionDispatcherListener) => {\n        return listener !== registered;\n      });\n    };\n  }\n\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n}\n\n/** @docs-private */\nexport function UNIQUE_SELECTION_DISPATCHER_PROVIDER_FACTORY(\n    parentDispatcher: UniqueSelectionDispatcher) {\n  return parentDispatcher || new UniqueSelectionDispatcher();\n}\n\n/** @docs-private */\nexport const UNIQUE_SELECTION_DISPATCHER_PROVIDER = {\n  // If there is already a dispatcher available, use that. Otherwise, provide a new one.\n  provide: UniqueSelectionDispatcher,\n  deps: [[new Optional(), new SkipSelf(), UniqueSelectionDispatcher]],\n  useFactory: UNIQUE_SELECTION_DISPATCHER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Subject} from 'rxjs/Subject';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nexport class SelectionModel<T> {\n  /** Currently-selected values. */\n  private _selection: Set<T> = new Set();\n\n  /** Keeps track of the deselected options that haven't been emitted by the change event. */\n  private _deselectedToEmit: T[] = [];\n\n  /** Keeps track of the selected options that haven't been emitted by the change event. */\n  private _selectedToEmit: T[] = [];\n\n  /** Cache for the array value of the selected items. */\n  private _selected: T[] | null;\n\n  /** Selected values. */\n  get selected(): T[] {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n\n    return this._selected;\n  }\n\n  /** Event emitted when the value has changed. */\n  onChange: Subject<SelectionChange<T>> | null = this._emitChanges ? new Subject() : null;\n\n  constructor(\n    private _multiple = false,\n    initiallySelectedValues?: T[],\n    private _emitChanges = true) {\n\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n\n  /**\n   * Selects a value or an array of values.\n   */\n  select(...values: T[]): void {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    this._emitChangeEvent();\n  }\n\n  /**\n   * Deselects a value or an array of values.\n   */\n  deselect(...values: T[]): void {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    this._emitChangeEvent();\n  }\n\n  /**\n   * Toggles a value between selected and deselected.\n   */\n  toggle(value: T): void {\n    this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n\n  /**\n   * Clears all of the selected values.\n   */\n  clear(): void {\n    this._unmarkAll();\n    this._emitChangeEvent();\n  }\n\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value: T): boolean {\n    return this._selection.has(value);\n  }\n\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty(): boolean {\n    return this._selection.size === 0;\n  }\n\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue(): boolean {\n    return !this.isEmpty();\n  }\n\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate?: (a: T, b: T) => number): void {\n    if (this._multiple && this._selected) {\n      this._selected.sort(predicate);\n    }\n  }\n\n  /** Emits a change event and clears the records of selected and deselected values. */\n  private _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      if (this.onChange) {\n        this.onChange.next({\n          source: this,\n          added: this._selectedToEmit,\n          removed: this._deselectedToEmit\n        });\n      }\n\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n\n  /** Selects a value. */\n  private _markSelected(value: T) {\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n\n      this._selection.add(value);\n\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Deselects a value. */\n  private _unmarkSelected(value: T) {\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Clears out the selected values. */\n  private _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  private _verifyValueAssignment(values: T[]) {\n    if (values.length > 1 && !this._multiple) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n}\n\n/**\n * Event emitted when the value of a MatSelectionModel has changed.\n * @docs-private\n */\nexport interface SelectionChange<T> {\n  /** Model that dispatched the event. */\n  source: SelectionModel<T>;\n  /** Options that were added to the model. */\n  added: T[];\n  /** Options that were removed from the model. */\n  removed: T[];\n}\n\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n */\nexport function getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Observable} from 'rxjs/Observable';\nimport {CollectionViewer} from './collection-viewer';\n\nexport abstract class DataSource<T> {\n  /**\n   * Connects a collection viewer (such as a data-table) to this data source. Note that\n   * the stream provided will be accessed during change detection and should not directly change\n   * values that are bound in template views.\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   * @returns Observable that emits a new value when the data changes.\n   */\n  abstract connect(collectionViewer: CollectionViewer): Observable<T[]>;\n\n  /**\n   * Disconnects a collection viewer (such as a data-table) from this data source. Can be used\n   * to perform any clean-up or tear-down operations when a view is being destroyed.\n   *\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   */\n  abstract disconnect(collectionViewer: CollectionViewer): void;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AIWA,IAAA,UAAA,kBAAA,YAAA;;;IAXA,OAAA,UAAA,CAAA;CA8BA,EAAA,CAAC,CAAA;;;;;;;ADtBD;;;AAKA,IAAA,cAAA,kBAAA,YAAA;IAyBE,SAAF,cAAA,CACY,SADZ,EAEI,uBAA6B,EACrB,YAHZ,EAAA;;;QAAE,IAAF,KAAA,GAAA,IAAA,CAeG;QAdS,IAAZ,CAAA,SAAqB,GAAT,SAAS,CAArB;QAEY,IAAZ,CAAA,YAAwB,GAAZ,YAAY,CAAxB;;;;QA1BA,IAAA,CAAA,UAAA,GAA+B,IAAI,GAAG,EAAE,CAAxC;;;;QAGA,IAAA,CAAA,iBAAA,GAAmC,EAAE,CAArC;;;;QAGA,IAAA,CAAA,eAAA,GAAiC,EAAE,CAAnC;;;;QAeA,IAAA,CAAA,QAAA,GAAiD,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,EAAE,GAAG,IAAI,CAAzF;QAOI,IAAI,uBAAuB,IAAI,uBAAuB,CAAC,MAAM,EAAE;YAC7D,IAAI,SAAS,EAAE;gBACb,uBAAuB,CAAC,OAAO,CAAC,UAAA,KAAK,EAA7C,EAAiD,OAAA,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAA1E,EAA0E,CAAC,CAAC;aACrE;iBAAM;gBACL,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;aAChD;;YAGD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;SACjC;KACF;IA1BD,MAAF,CAAA,cAAA,CAAM,cAAN,CAAA,SAAA,EAAA,UAAc,EAAd;;;;;;QAAE,YAAF;YACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;aACvD;YAED,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;;;KAAH,CAAA,CAAG;;;;;;;;;IAyBD,cAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAIG;QAJM,IAAT,MAAA,GAAA,EAAA,CAAuB;QAAvB,KAAS,IAAT,EAAA,GAAA,CAAuB,EAAd,EAAT,GAAA,SAAA,CAAA,MAAuB,EAAd,EAAT,EAAuB,EAAvB;YAAS,MAAT,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAuB;;QACnB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,EAAxB,EAA4B,OAAA,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAArD,EAAqD,CAAC,CAAC;QACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB,CAAH;;;;;;;;;IAKE,cAAF,CAAA,SAAA,CAAA,QAAU;;;;;IAAR,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAIG;QAJQ,IAAX,MAAA,GAAA,EAAA,CAAyB;QAAzB,KAAW,IAAX,EAAA,GAAA,CAAyB,EAAd,EAAX,GAAA,SAAA,CAAA,MAAyB,EAAd,EAAX,EAAyB,EAAzB;YAAW,MAAX,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAyB;;QACrB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,EAAxB,EAA4B,OAAA,KAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAvD,EAAuD,CAAC,CAAC;QACrD,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB,CAAH;;;;;;;;;IAKE,cAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,KAAQ,EAAjB;QACI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACpE,CAAH;;;;;;;;IAKE,cAAF,CAAA,SAAA,CAAA,KAAO;;;;IAAL,YAAF;QACI,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB,CAAH;;;;;;;;;IAKE,cAAF,CAAA,SAAA,CAAA,UAAY;;;;;IAAV,UAAW,KAAQ,EAArB;QACI,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KACnC,CAAH;;;;;;;;IAKE,cAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC;KACnC,CAAH;;;;;;;;IAKE,cAAF,CAAA,SAAA,CAAA,QAAU;;;;IAAR,YAAF;QACI,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;KACxB,CAAH;;;;;;;;;IAKE,cAAF,CAAA,SAAA,CAAA,IAAM;;;;;IAAJ,UAAK,SAAkC,EAAzC;QACI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;YACpC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAChC;KACF,CAAH;;;;;IAGU,cAAV,CAAA,SAAA,CAAA,gBAA0B;;;;;;QAEtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAChE,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACjB,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,IAAI,CAAC,eAAe;oBAC3B,OAAO,EAAE,IAAI,CAAC,iBAAiB;iBAChC,CAAC,CAAC;aACJ;YAED,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAC5B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;SAC3B;;;;;;;IAIK,cAAV,CAAA,SAAA,CAAA,aAAuB;;;;;IAAvB,UAAwB,KAAQ,EAAhC;QACI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,UAAU,EAAE,CAAC;aACnB;YAED,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAE3B,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClC;SACF;;;;;;;IAIK,cAAV,CAAA,SAAA,CAAA,eAAyB;;;;;IAAzB,UAA0B,KAAQ,EAAlC;QACI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpC;SACF;;;;;;IAIK,cAAV,CAAA,SAAA,CAAA,UAAoB;;;;;;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,KAAK,EAAnC,EAAuC,OAAA,KAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAlE,EAAkE,CAAC,CAAC;SAC/D;;;;;;;;IAOK,cAAV,CAAA,SAAA,CAAA,sBAAgC;;;;;;IAAhC,UAAiC,MAAW,EAA5C;QACI,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACxC,MAAM,uCAAuC,EAAE,CAAC;SACjD;;IAjLL,OAAA,cAAA,CAAA;CAmLA,EAAA,CAAC,CAAA;;;;;;;;;;;;AAmBD,AAAA,SAAA,uCAAA,GAAA;IACE,OAAO,KAAK,CAAC,yEAAyE,CAAC,CAAC;CACzF;;;;;;;ADhMD;;;;;;;;;;;QAiBA,IAAA,CAAA,UAAA,GAA4D,EAAE,CAA9D;;;;;;;;;;;;;IAOE,yBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;;IAAN,UAAO,EAAU,EAAE,IAAY,EAAjC;QACI,KAAqB,IAAzB,EAAA,GAAA,CAAwC,EAAf,EAAzB,GAAyB,IAAI,CAAC,UAAU,EAAf,EAAzB,GAAA,EAAA,CAAA,MAAwC,EAAf,EAAzB,EAAwC,EAAxC;YAAS,IAAI,QAAQ,GAArB,EAAA,CAAA,EAAA,CAAqB,CAArB;YACM,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SACpB;KACF,CAAH;;;;;;;;;;IAME,yBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,QAA2C,EAApD;QAAE,IAAF,KAAA,GAAA,IAAA,CAOG;QANC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,OAAO,YAAX;YACM,KAAI,CAAC,UAAU,GAAG,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,UAA6C,EAA7F;gBACQ,OAAO,QAAQ,KAAK,UAAU,CAAC;aAChC,CAAC,CAAC;SACJ,CAAC;KACH,CAAH;;;;IAEE,yBAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB,CAAH;;QA9BA,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IAvBA,OAAA,yBAAA,CAAA;;AAwBA;;;;;AAiCA,AAAA,SAAA,4CAAA,CACI,gBAA2C,EAD/C;IAEE,OAAO,gBAAgB,IAAI,IAAI,yBAAyB,EAAE,CAAC;CAC5D;;;;AAGD,AAAO,IAAM,oCAAoC,GAAG;;IAElD,OAAO,EAAE,yBAAyB;IAClC,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,yBAAyB,CAAC,CAAC;IACnE,UAAU,EAAE,4CAA4C;CACzD,CAAC;;;;;GD3DF,AACA,AACA,AAIuC;;;;;;;;GDXvC,AAEA,AAAiG;;"}