{"version": 3, "file": "ConnectableObservable.js", "sourceRoot": "", "sources": ["../../src/observable/ConnectableObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,wBAA2C,YAAY,CAAC,CAAA;AAExD,2BAA2B,eAAe,CAAC,CAAA;AAC3C,2BAA2B,eAAe,CAAC,CAAA;AAC3C,6BAA4C,iBAAiB,CAAC,CAAA;AAC9D,yBAAgD,uBAAuB,CAAC,CAAA;AAExE;;GAEG;AACH;IAA8C,yCAAa;IAOzD,+BAAY,oCAAoC,CAAQ,MAAqB;QACjE,oCAAoC,CAAQ,cAAgC;QACtF,iBAAO,CAAC;QAF8C,WAAM,GAAN,MAAM,CAAe;QACrB,mBAAc,GAAd,cAAc,CAAkB;QALxF,oCAAoC,CAAQ,cAAS,GAAW,CAAC,CAAC;QAElE,gBAAW,GAAG,KAAK,CAAC;IAKpB,CAAC;IAED,oCAAoC,CAAC,0CAAU,GAAV,UAAW,UAAyB;QACvE,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAED,oCAAoC,CAAQ,0CAAU,GAAjB;QACnC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACxC,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,uCAAO,GAAP;QACE,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,2BAAY,EAAE,CAAC;YACnD,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM;iBACvB,SAAS,CAAC,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAClE,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,UAAU,GAAG,2BAAY,CAAC,KAAK,CAAC;YAClC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;YAChC,CAAC;QACH,CAAC;QACD,MAAM,CAAC,UAAU,CAAC;IACpB,CAAC;IAED,wCAAQ,GAAR;QACE,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAkB,CAAC;IACtD,CAAC;IACH,4BAAC;AAAD,CAAC,AA5CD,CAA8C,uBAAU,GA4CvD;AA5CY,6BAAqB,wBA4CjC,CAAA;AAED,IAAM,gBAAgB,GAAQ,qBAAqB,CAAC,SAAS,CAAC;AAEjD,uCAA+B,GAA0B;IACpE,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;IACzB,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IACvC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;IACzC,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC5C,UAAU,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,UAAU,EAAE;IAClD,WAAW,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE;IACpE,UAAU,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,UAAU,EAAE;IAClD,OAAO,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,OAAO,EAAE;IAC5C,QAAQ,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,QAAQ,EAAE;CAC/C,CAAC;AAEF;IAAuC,yCAAoB;IACzD,+BAAY,WAAuB,EACf,WAAqC;QACvD,kBAAM,WAAW,CAAC,CAAC;QADD,gBAAW,GAAX,WAAW,CAA0B;IAEzD,CAAC;IACS,sCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,gBAAK,CAAC,MAAM,YAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IACS,yCAAS,GAAnB;QACE,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,gBAAK,CAAC,SAAS,WAAE,CAAC;IACpB,CAAC;IACD,oCAAoC,CAAC,4CAAY,GAAZ;QACnC,IAAM,WAAW,GAAQ,IAAI,CAAC,WAAW,CAAC;QAC1C,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAM,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC;YAC3C,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;YAC1B,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC5B,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;YAC/B,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;gBACf,UAAU,CAAC,WAAW,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IACH,4BAAC;AAAD,CAAC,AA3BD,CAAuC,2BAAiB,GA2BvD;AAED;IACE,0BAAoB,WAAqC;QAArC,gBAAW,GAAX,WAAW,CAA0B;IACzD,CAAC;IACD,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QAEjC,kCAAW,CAAU;QACtB,WAAY,CAAC,SAAS,EAAE,CAAC;QAEhC,IAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACnE,IAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAElD,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YAChB,UAAW,CAAC,UAAU,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QACxD,CAAC;QAED,MAAM,CAAC,YAAY,CAAC;IACtB,CAAC;IACH,uBAAC;AAAD,CAAC,AAjBD,IAiBC;AAED;IAAoC,sCAAa;IAI/C,4BAAY,WAA0B,EAClB,WAAqC;QACvD,kBAAM,WAAW,CAAC,CAAC;QADD,gBAAW,GAAX,WAAW,CAA0B;IAEzD,CAAC;IAED,oCAAoC,CAAC,yCAAY,GAAZ;QAE3B,kCAAW,CAAU;QAC7B,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAM,QAAQ,GAAU,WAAY,CAAC,SAAS,CAAC;QAC/C,EAAE,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAEM,WAAY,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7C,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAED,GAAG;QACH,wEAAwE;QACxE,wEAAwE;QACxE,yEAAyE;QACzE,2EAA2E;QAC3E,0EAA0E;QAC1E,yEAAyE;QACzE,eAAe;QACf,MAAM;QACN,0BAA0B;QAC1B,eAAe;QACf,gBAAgB;QAChB,aAAa;QACb,kBAAkB;QAClB,MAAM;QACN,4EAA4E;QAC5E,oEAAoE;QACpE,gDAAgD;QAChD,4EAA4E;QAC5E,6BAA6B;QAC7B,2EAA2E;QAC3E,6CAA6C;QAC7C,GAAG;QACK,gCAAU,CAAU;QAC5B,IAAM,gBAAgB,GAAU,WAAY,CAAC,WAAW,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,EAAE,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,UAAU,IAAI,gBAAgB,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC;YACzE,gBAAgB,CAAC,WAAW,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AA7DD,CAAoC,uBAAU,GA6D7C", "sourcesContent": ["import { Subject, SubjectSubscriber } from '../Subject';\nimport { Operator } from '../Operator';\nimport { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription, TeardownLogic } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\n\n/**\n * @class ConnectableObservable<T>\n */\nexport class ConnectableObservable<T> extends Observable<T> {\n\n  /** @deprecated internal use only */ public _subject: Subject<T>;\n  /** @deprecated internal use only */ public _refCount: number = 0;\n  /** @deprecated internal use only */ public _connection: Subscription;\n  _isComplete = false;\n\n  constructor(/** @deprecated internal use only */ public source: Observable<T>,\n              /** @deprecated internal use only */ public subjectFactory: () => Subject<T>) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>) {\n    return this.getSubject().subscribe(subscriber);\n  }\n\n  /** @deprecated internal use only */ public getSubject(): Subject<T> {\n    const subject = this._subject;\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n    return this._subject;\n  }\n\n  connect(): Subscription {\n    let connection = this._connection;\n    if (!connection) {\n      this._isComplete = false;\n      connection = this._connection = new Subscription();\n      connection.add(this.source\n        .subscribe(new ConnectableSubscriber(this.getSubject(), this)));\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription.EMPTY;\n      } else {\n        this._connection = connection;\n      }\n    }\n    return connection;\n  }\n\n  refCount(): Observable<T> {\n    return higherOrderRefCount()(this) as Observable<T>;\n  }\n}\n\nconst connectableProto = <any>ConnectableObservable.prototype;\n\nexport const connectableObservableDescriptor: PropertyDescriptorMap = {\n  operator: { value: null },\n  _refCount: { value: 0, writable: true },\n  _subject: { value: null, writable: true },\n  _connection: { value: null, writable: true },\n  _subscribe: { value: connectableProto._subscribe },\n  _isComplete: { value: connectableProto._isComplete, writable: true },\n  getSubject: { value: connectableProto.getSubject },\n  connect: { value: connectableProto.connect },\n  refCount: { value: connectableProto.refCount }\n};\n\nclass ConnectableSubscriber<T> extends SubjectSubscriber<T> {\n  constructor(destination: Subject<T>,\n              private connectable: ConnectableObservable<T>) {\n    super(destination);\n  }\n  protected _error(err: any): void {\n    this._unsubscribe();\n    super._error(err);\n  }\n  protected _complete(): void {\n    this.connectable._isComplete = true;\n    this._unsubscribe();\n    super._complete();\n  }\n  /** @deprecated internal use only */ _unsubscribe() {\n    const connectable = <any>this.connectable;\n    if (connectable) {\n      this.connectable = null;\n      const connection = connectable._connection;\n      connectable._refCount = 0;\n      connectable._subject = null;\n      connectable._connection = null;\n      if (connection) {\n        connection.unsubscribe();\n      }\n    }\n  }\n}\n\nclass RefCountOperator<T> implements Operator<T, T> {\n  constructor(private connectable: ConnectableObservable<T>) {\n  }\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n\n    const { connectable } = this;\n    (<any> connectable)._refCount++;\n\n    const refCounter = new RefCountSubscriber(subscriber, connectable);\n    const subscription = source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      (<any> refCounter).connection = connectable.connect();\n    }\n\n    return subscription;\n  }\n}\n\nclass RefCountSubscriber<T> extends Subscriber<T> {\n\n  private connection: Subscription;\n\n  constructor(destination: Subscriber<T>,\n              private connectable: ConnectableObservable<T>) {\n    super(destination);\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n\n    const { connectable } = this;\n    if (!connectable) {\n      this.connection = null;\n      return;\n    }\n\n    this.connectable = null;\n    const refCount = (<any> connectable)._refCount;\n    if (refCount <= 0) {\n      this.connection = null;\n      return;\n    }\n\n    (<any> connectable)._refCount = refCount - 1;\n    if (refCount > 1) {\n      this.connection = null;\n      return;\n    }\n\n    ///\n    // Compare the local RefCountSubscriber's connection Subscription to the\n    // connection Subscription on the shared ConnectableObservable. In cases\n    // where the ConnectableObservable source synchronously emits values, and\n    // the RefCountSubscriber's downstream Observers synchronously unsubscribe,\n    // execution continues to here before the RefCountOperator has a chance to\n    // supply the RefCountSubscriber with the shared connection Subscription.\n    // For example:\n    // ```\n    // Observable.range(0, 10)\n    //   .publish()\n    //   .refCount()\n    //   .take(5)\n    //   .subscribe();\n    // ```\n    // In order to account for this case, RefCountSubscriber should only dispose\n    // the ConnectableObservable's shared connection Subscription if the\n    // connection Subscription exists, *and* either:\n    //   a. RefCountSubscriber doesn't have a reference to the shared connection\n    //      Subscription yet, or,\n    //   b. RefCountSubscriber's connection Subscription reference is identical\n    //      to the shared connection Subscription\n    ///\n    const { connection } = this;\n    const sharedConnection = (<any> connectable)._connection;\n    this.connection = null;\n\n    if (sharedConnection && (!connection || sharedConnection === connection)) {\n      sharedConnection.unsubscribe();\n    }\n  }\n}\n"]}