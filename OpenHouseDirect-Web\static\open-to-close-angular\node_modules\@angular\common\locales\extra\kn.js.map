{"version": 3, "file": "kn.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/kn.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,YAAY,EAAE,SAAS,EAAE,UAAU;YACnC,MAAM,EAAE,QAAQ;SACjB;QACD;YACE,aAAa,EAAE,SAAS,EAAE,UAAU;YACpC,MAAM,EAAE,QAAQ;SACjB;KACF;IACD;QACE;YACE,YAAY,EAAE,SAAS,EAAE,UAAU;YACnC,MAAM,EAAE,QAAQ;SACjB;QACD,AADE;KAEH;IACD,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAC1F,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'ಮಧ್ಯರಾತ್ರಿ', 'ಬೆಳಗ್ಗೆ', 'ಮಧ್ಯಾಹ್ನ',\n      'ಸಂಜೆ', 'ರಾತ್ರಿ'\n    ],\n    [\n      'ಮಧ್ಯ ರಾತ್ರಿ', 'ಬೆಳಗ್ಗೆ', 'ಮಧ್ಯಾಹ್ನ',\n      'ಸಂಜೆ', 'ರಾತ್ರಿ'\n    ],\n  ],\n  [\n    [\n      'ಮಧ್ಯರಾತ್ರಿ', 'ಬೆಳಗ್ಗೆ', 'ಮಧ್ಯಾಹ್ನ',\n      'ಸಂಜೆ', 'ರಾತ್ರಿ'\n    ],\n    ,\n  ],\n  ['00:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'], ['21:00', '06:00']]\n];\n"]}