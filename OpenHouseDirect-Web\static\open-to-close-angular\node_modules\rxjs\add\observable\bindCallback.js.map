{"version": 3, "file": "bindCallback.js", "sourceRoot": "", "sources": ["../../../src/add/observable/bindCallback.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,6BAAmD,+BAA+B,CAAC,CAAA;AAEnF,uBAAU,CAAC,YAAY,GAAG,2BAAkB,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { bindCallback as staticBindCallback } from '../../observable/bindCallback';\n\nObservable.bindCallback = staticBindCallback;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let bindCallback: typeof staticBindCallback;\n  }\n}\n"]}