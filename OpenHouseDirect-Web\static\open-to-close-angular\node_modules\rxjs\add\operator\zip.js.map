{"version": 3, "file": "zip.js", "sourceRoot": "", "sources": ["../../../src/add/operator/zip.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,oBAAyB,oBAAoB,CAAC,CAAA;AAE9C,uBAAU,CAAC,SAAS,CAAC,GAAG,GAAG,cAAQ,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { zipProto } from '../../operator/zip';\n\nObservable.prototype.zip = zipProto;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    zip: typeof zipProto;\n  }\n}"]}