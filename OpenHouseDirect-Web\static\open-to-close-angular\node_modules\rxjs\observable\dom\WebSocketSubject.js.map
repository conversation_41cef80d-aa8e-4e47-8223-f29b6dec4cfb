{"version": 3, "file": "WebSocketSubject.js", "sourceRoot": "", "sources": ["../../../src/observable/dom/WebSocketSubject.ts"], "names": [], "mappings": ";;;;;;AAAA,wBAA0C,eAAe,CAAC,CAAA;AAC1D,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,6BAA6B,oBAAoB,CAAC,CAAA;AAElD,qBAAqB,iBAAiB,CAAC,CAAA;AACvC,8BAA8B,qBAAqB,CAAC,CAAA;AAEpD,yBAAyB,qBAAqB,CAAC,CAAA;AAC/C,4BAA4B,wBAAwB,CAAC,CAAA;AACrD,uBAAuB,mBAAmB,CAAC,CAAA;AAa3C;;;;GAIG;AACH;IAAyC,oCAAmB;IA2D1D,0BAAY,iBAAkE,EAAE,WAAyB;QACvG,EAAE,CAAC,CAAC,iBAAiB,YAAY,uBAAU,CAAC,CAAC,CAAC;YAC5C,kBAAM,WAAW,EAAkB,iBAAiB,CAAC,CAAC;QACxD,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,iBAAO,CAAC;YACR,IAAI,CAAC,aAAa,GAAG,WAAI,CAAC,SAAS,CAAC;YACpC,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAO,EAAK,CAAC;YAChC,EAAE,CAAC,CAAC,OAAO,iBAAiB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBAC1C,IAAI,CAAC,GAAG,GAAG,iBAAiB,CAAC;YAC/B,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,gEAAgE;gBAChE,eAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;YAClC,CAAC;YACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,6BAAa,EAAE,CAAC;QACzC,CAAC;IACH,CAAC;IAhED,yCAAc,GAAd,UAAe,CAAe;QAC5B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACI,uBAAM,GAAb,UAAiB,iBAAkD;QACjE,MAAM,CAAC,IAAI,gBAAgB,CAAI,iBAAiB,CAAC,CAAC;IACpD,CAAC;IAsBD,+BAAI,GAAJ,UAAQ,QAAwB;QAC9B,IAAM,IAAI,GAAG,IAAI,gBAAgB,CAAI,IAAI,EAAQ,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IAEO,sCAAW,GAAnB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,WAAW,GAAG,IAAI,6BAAa,EAAE,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAO,EAAK,CAAC;IAClC,CAAC;IAED,iGAAiG;IACjG,oCAAS,GAAT,UAAU,MAAiB,EAAE,QAAmB,EAAE,aAAoC;QACpF,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,MAAM,CAAC,IAAI,uBAAU,CAAC,UAAC,QAAuB;YAC5C,IAAM,MAAM,GAAG,mBAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;gBAC3B,QAAQ,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpB,CAAC;YAED,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,UAAA,CAAC;gBACjC,IAAM,MAAM,GAAG,mBAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1C,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;oBAC3B,QAAQ,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC;gBAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBAClB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC,EACC,UAAA,GAAG,IAAI,OAAA,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAnB,CAAmB,EAC1B,cAAM,OAAA,QAAQ,CAAC,QAAQ,EAAE,EAAnB,CAAmB,CAAC,CAAC;YAE7B,MAAM,CAAC;gBACL,IAAM,MAAM,GAAG,mBAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;oBAC3B,QAAQ,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpB,CAAC;gBACD,YAAY,CAAC,WAAW,EAAE,CAAC;YAC7B,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,yCAAc,GAAtB;QAAA,iBAyFC;QAxFS,sCAAa,CAAU;QAC/B,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;QAE9B,IAAI,MAAM,GAAc,IAAI,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,GAAG,IAAI,CAAC,QAAQ;gBACpB,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC;gBAC1C,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YAC3C,CAAC;QACH,CAAE;QAAA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAClB,MAAM,CAAC;QACT,CAAC;QAED,IAAM,YAAY,GAAG,IAAI,2BAAY,CAAC;YACpC,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,GAAG,UAAC,CAAQ;YACvB,IAAM,YAAY,GAAG,KAAI,CAAC,YAAY,CAAC;YACvC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;gBACjB,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC;YAED,IAAM,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC;YAE/B,KAAI,CAAC,WAAW,GAAG,uBAAU,CAAC,MAAM,CAClC,UAAC,CAAC,IAAK,OAAA,MAAM,CAAC,UAAU,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAzC,CAAyC,EAChD,UAAC,CAAC;gBACA,IAAM,eAAe,GAAG,KAAI,CAAC,eAAe,CAAC;gBAC7C,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;oBACpB,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC;gBACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;gBACjC,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,2EAA2E;wBACtG,0DAA0D,CAAC,CAAC,CAAC;gBACjE,CAAC;gBACD,KAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC,EACD;gBACE,IAAM,eAAe,GAAG,KAAI,CAAC,eAAe,CAAC;gBAC7C,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;oBACpB,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,KAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC,CACF,CAAC;YAEF,EAAE,CAAC,CAAC,KAAK,IAAI,KAAK,YAAY,6BAAa,CAAC,CAAC,CAAC;gBAC5C,YAAY,CAAC,GAAG,CAAoB,KAAM,CAAC,SAAS,CAAC,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,OAAO,GAAG,UAAC,CAAQ;YACxB,KAAI,CAAC,WAAW,EAAE,CAAC;YACnB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;QAEF,MAAM,CAAC,OAAO,GAAG,UAAC,CAAa;YAC7B,KAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAM,aAAa,GAAG,KAAI,CAAC,aAAa,CAAC;YACzC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;gBAClB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC;YACD,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACf,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,SAAS,GAAG,UAAC,CAAe;YACjC,IAAM,MAAM,GAAG,mBAAQ,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;gBAC3B,QAAQ,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,oCAAoC,CAAC,qCAAU,GAAV,UAAW,UAAyB;QAApC,iBAoBpC;QAnBS,wBAAM,CAAU;QACxB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;QACD,IAAI,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACtC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QACrD,YAAY,CAAC,GAAG,CAAC;YACP,yBAAM,CAAU;YACxB,EAAE,CAAC,CAAC,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxC,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC;oBACtC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,CAAC;gBACD,KAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,YAAY,CAAC;IACtB,CAAC;IAED,sCAAW,GAAX;QACE,IAAA,SAA+B,EAAvB,kBAAM,EAAE,kBAAM,CAAU;QAChC,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;QACD,gBAAK,CAAC,WAAW,WAAE,CAAC;QACpB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,IAAI,CAAC,WAAW,GAAG,IAAI,6BAAa,EAAE,CAAC;QACzC,CAAC;IACH,CAAC;IACH,uBAAC;AAAD,CAAC,AA3PD,CAAyC,0BAAgB,GA2PxD;AA3PY,wBAAgB,mBA2P5B,CAAA", "sourcesContent": ["import { Subject, AnonymousSubject } from '../../Subject';\nimport { Subscriber } from '../../Subscriber';\nimport { Observable } from '../../Observable';\nimport { Subscription } from '../../Subscription';\nimport { Operator } from '../../Operator';\nimport { root } from '../../util/root';\nimport { ReplaySubject } from '../../ReplaySubject';\nimport { Observer, NextObserver } from '../../Observer';\nimport { tryCatch } from '../../util/tryCatch';\nimport { errorObject } from '../../util/errorObject';\nimport { assign } from '../../util/assign';\n\nexport interface WebSocketSubjectConfig {\n  url: string;\n  protocol?: string | Array<string>;\n  resultSelector?: <T>(e: MessageEvent) => T;\n  openObserver?: NextObserver<Event>;\n  closeObserver?: NextObserver<CloseEvent>;\n  closingObserver?: NextObserver<void>;\n  WebSocketCtor?: { new(url: string, protocol?: string|Array<string>): WebSocket };\n  binaryType?: 'blob' | 'arraybuffer';\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class WebSocketSubject<T> extends AnonymousSubject<T> {\n\n  url: string;\n  protocol: string|Array<string>;\n  socket: WebSocket;\n  openObserver: NextObserver<Event>;\n  closeObserver: NextObserver<CloseEvent>;\n  closingObserver: NextObserver<void>;\n  WebSocketCtor: { new(url: string, protocol?: string|Array<string>): WebSocket };\n  binaryType?: 'blob' | 'arraybuffer';\n\n  private _output: Subject<T>;\n\n  resultSelector(e: MessageEvent) {\n    return JSON.parse(e.data);\n  }\n\n  /**\n   * Wrapper around the w3c-compatible WebSocket object provided by the browser.\n   *\n   * @example <caption>Wraps browser WebSocket</caption>\n   *\n   * let socket$ = Observable.webSocket('ws://localhost:8081');\n   *\n   * socket$.subscribe(\n   *    (msg) => console.log('message received: ' + msg),\n   *    (err) => console.log(err),\n   *    () => console.log('complete')\n   *  );\n   *\n   * socket$.next(JSON.stringify({ op: 'hello' }));\n   *\n   * @example <caption>Wraps WebSocket from nodejs-websocket (using node.js)</caption>\n   *\n   * import { w3cwebsocket } from 'websocket';\n   *\n   * let socket$ = Observable.webSocket({\n   *   url: 'ws://localhost:8081',\n   *   WebSocketCtor: w3cwebsocket\n   * });\n   *\n   * socket$.subscribe(\n   *    (msg) => console.log('message received: ' + msg),\n   *    (err) => console.log(err),\n   *    () => console.log('complete')\n   *  );\n   *\n   * socket$.next(JSON.stringify({ op: 'hello' }));\n   *\n   * @param {string | WebSocketSubjectConfig} urlConfigOrSource the source of the websocket as an url or a structure defining the websocket object\n   * @return {WebSocketSubject}\n   * @static true\n   * @name webSocket\n   * @owner Observable\n   */\n  static create<T>(urlConfigOrSource: string | WebSocketSubjectConfig): WebSocketSubject<T> {\n    return new WebSocketSubject<T>(urlConfigOrSource);\n  }\n\n  constructor(urlConfigOrSource: string | WebSocketSubjectConfig | Observable<T>, destination?: Observer<T>) {\n    if (urlConfigOrSource instanceof Observable) {\n      super(destination, <Observable<T>> urlConfigOrSource);\n    } else {\n      super();\n      this.WebSocketCtor = root.WebSocket;\n      this._output = new Subject<T>();\n      if (typeof urlConfigOrSource === 'string') {\n        this.url = urlConfigOrSource;\n      } else {\n        // WARNING: config object could override important members here.\n        assign(this, urlConfigOrSource);\n      }\n      if (!this.WebSocketCtor) {\n        throw new Error('no WebSocket constructor can be found');\n      }\n      this.destination = new ReplaySubject();\n    }\n  }\n\n  lift<R>(operator: Operator<T, R>): WebSocketSubject<R> {\n    const sock = new WebSocketSubject<R>(this, <any> this.destination);\n    sock.operator = operator;\n    return sock;\n  }\n\n  private _resetState() {\n    this.socket = null;\n    if (!this.source) {\n      this.destination = new ReplaySubject();\n    }\n    this._output = new Subject<T>();\n  }\n\n  // TODO: factor this out to be a proper Operator/Subscriber implementation and eliminate closures\n  multiplex(subMsg: () => any, unsubMsg: () => any, messageFilter: (value: T) => boolean) {\n    const self = this;\n    return new Observable((observer: Observer<any>) => {\n      const result = tryCatch(subMsg)();\n      if (result === errorObject) {\n        observer.error(errorObject.e);\n      } else {\n        self.next(result);\n      }\n\n      let subscription = self.subscribe(x => {\n        const result = tryCatch(messageFilter)(x);\n        if (result === errorObject) {\n          observer.error(errorObject.e);\n        } else if (result) {\n          observer.next(x);\n        }\n      },\n        err => observer.error(err),\n        () => observer.complete());\n\n      return () => {\n        const result = tryCatch(unsubMsg)();\n        if (result === errorObject) {\n          observer.error(errorObject.e);\n        } else {\n          self.next(result);\n        }\n        subscription.unsubscribe();\n      };\n    });\n  }\n\n  private _connectSocket() {\n    const { WebSocketCtor } = this;\n    const observer = this._output;\n\n    let socket: WebSocket = null;\n    try {\n      socket = this.protocol ?\n        new WebSocketCtor(this.url, this.protocol) :\n        new WebSocketCtor(this.url);\n      this.socket = socket;\n      if (this.binaryType) {\n        this.socket.binaryType = this.binaryType;\n      }\n    } catch (e) {\n      observer.error(e);\n      return;\n    }\n\n    const subscription = new Subscription(() => {\n      this.socket = null;\n      if (socket && socket.readyState === 1) {\n        socket.close();\n      }\n    });\n\n    socket.onopen = (e: Event) => {\n      const openObserver = this.openObserver;\n      if (openObserver) {\n        openObserver.next(e);\n      }\n\n      const queue = this.destination;\n\n      this.destination = Subscriber.create(\n        (x) => socket.readyState === 1 && socket.send(x),\n        (e) => {\n          const closingObserver = this.closingObserver;\n          if (closingObserver) {\n            closingObserver.next(undefined);\n          }\n          if (e && e.code) {\n            socket.close(e.code, e.reason);\n          } else {\n            observer.error(new TypeError('WebSocketSubject.error must be called with an object with an error code, ' +\n              'and an optional reason: { code: number, reason: string }'));\n          }\n          this._resetState();\n        },\n        ( ) => {\n          const closingObserver = this.closingObserver;\n          if (closingObserver) {\n            closingObserver.next(undefined);\n          }\n          socket.close();\n          this._resetState();\n        }\n      );\n\n      if (queue && queue instanceof ReplaySubject) {\n        subscription.add((<ReplaySubject<T>>queue).subscribe(this.destination));\n      }\n    };\n\n    socket.onerror = (e: Event) => {\n      this._resetState();\n      observer.error(e);\n    };\n\n    socket.onclose = (e: CloseEvent) => {\n      this._resetState();\n      const closeObserver = this.closeObserver;\n      if (closeObserver) {\n        closeObserver.next(e);\n      }\n      if (e.wasClean) {\n        observer.complete();\n      } else {\n        observer.error(e);\n      }\n    };\n\n    socket.onmessage = (e: MessageEvent) => {\n      const result = tryCatch(this.resultSelector)(e);\n      if (result === errorObject) {\n        observer.error(errorObject.e);\n      } else {\n        observer.next(result);\n      }\n    };\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): Subscription {\n    const { source } = this;\n    if (source) {\n      return source.subscribe(subscriber);\n    }\n    if (!this.socket) {\n      this._connectSocket();\n    }\n    let subscription = new Subscription();\n    subscription.add(this._output.subscribe(subscriber));\n    subscription.add(() => {\n      const { socket } = this;\n      if (this._output.observers.length === 0) {\n        if (socket && socket.readyState === 1) {\n          socket.close();\n        }\n        this._resetState();\n      }\n    });\n    return subscription;\n  }\n\n  unsubscribe() {\n    const { source, socket } = this;\n    if (socket && socket.readyState === 1) {\n      socket.close();\n      this._resetState();\n    }\n    super.unsubscribe();\n    if (!source) {\n      this.destination = new ReplaySubject();\n    }\n  }\n}\n"]}