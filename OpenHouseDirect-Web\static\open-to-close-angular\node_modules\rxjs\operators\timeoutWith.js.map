{"version": 3, "file": "timeoutWith.js", "sourceRoot": "", "sources": ["../../src/operators/timeoutWith.ts"], "names": [], "mappings": ";;;;;;AAIA,sBAAsB,oBAAoB,CAAC,CAAA;AAG3C,uBAAuB,gBAAgB,CAAC,CAAA;AACxC,gCAAgC,oBAAoB,CAAC,CAAA;AACrD,kCAAkC,2BAA2B,CAAC,CAAA;AAM9D,mCAAmC;AAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8CG;AACH,qBAAkC,GAAkB,EAClB,cAAkC,EAClC,SAA6B;IAA7B,yBAA6B,GAA7B,yBAA6B;IAC7D,MAAM,CAAC,UAAC,MAAqB;QAC3B,IAAI,eAAe,GAAG,eAAM,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,OAAO,GAAG,eAAe,GAAG,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAS,GAAG,CAAC,CAAC;QACjF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;IACnG,CAAC,CAAC;AACJ,CAAC;AARe,mBAAW,cAQ1B,CAAA;AAED;IACE,6BAAoB,OAAe,EACf,eAAwB,EACxB,cAAoC,EACpC,SAAqB;QAHrB,YAAO,GAAP,OAAO,CAAQ;QACf,oBAAe,GAAf,eAAe,CAAS;QACxB,mBAAc,GAAd,cAAc,CAAsB;QACpC,cAAS,GAAT,SAAS,CAAY;IACzC,CAAC;IAED,kCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,qBAAqB,CAC/C,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CACpF,CAAC,CAAC;IACL,CAAC;IACH,0BAAC;AAAD,CAAC,AAZD,IAYC;AAED;;;;GAIG;AACH;IAA0C,yCAAqB;IAI7D,+BAAY,WAA0B,EAClB,eAAwB,EACxB,OAAe,EACf,cAAoC,EACpC,SAAqB;QACvC,kBAAM,WAAW,CAAC,CAAC;QAJD,oBAAe,GAAf,eAAe,CAAS;QACxB,YAAO,GAAP,OAAO,CAAQ;QACf,mBAAc,GAAd,cAAc,CAAsB;QACpC,cAAS,GAAT,SAAS,CAAY;QANjC,WAAM,GAAwC,IAAI,CAAC;QAQzD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEc,qCAAe,GAA9B,UAAqC,UAAuC;QAClE,8CAAc,CAAgB;QAC/B,UAAW,CAAC,sBAAsB,EAAE,CAAC;QAC5C,UAAU,CAAC,GAAG,CAAC,qCAAiB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC;IAChE,CAAC;IAEO,+CAAe,GAAvB;QACU,wBAAM,CAAU;QACxB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,wEAAwE;YACxE,0EAA0E;YAC1E,2EAA2E;YAC3E,8EAA8E;YAC9E,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAA0C,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAE,CAAC;QAC5F,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAA0C,IAAI,CAAC,SAAS,CAAC,QAAQ,CACnF,qBAAqB,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CACzD,CAAC,CAAC;QACN,CAAC;IACH,CAAC;IAES,qCAAK,GAAf,UAAgB,KAAQ;QACtB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QACD,gBAAK,CAAC,KAAK,YAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,oCAAoC,CAAC,4CAAY,GAAZ;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC7B,CAAC;IACH,4BAAC;AAAD,CAAC,AA/CD,CAA0C,iCAAe,GA+CxD", "sourcesContent": ["import { Action } from '../scheduler/Action';\nimport { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { IScheduler } from '../Scheduler';\nimport { async } from '../scheduler/async';\nimport { TeardownLogic } from '../Subscription';\nimport { Observable, ObservableInput } from '../Observable';\nimport { isDate } from '../util/isDate';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OperatorFunction, MonoTypeOperatorFunction } from '../interfaces';\n\n/* tslint:disable:max-line-length */\nexport function timeoutWith<T>(due: number | Date, withObservable: ObservableInput<T>, scheduler?: IScheduler): MonoTypeOperatorFunction<T>;\nexport function timeoutWith<T, R>(due: number | Date, withObservable: ObservableInput<R>, scheduler?: IScheduler): OperatorFunction<T, T | R>;\n/* tslint:enable:max-line-length */\n\n/**\n *\n * Errors if Observable does not emit a value in given time span, in case of which\n * subscribes to the second Observable.\n *\n * <span class=\"informal\">It's a version of `timeout` operator that let's you specify fallback Observable.</span>\n *\n * <img src=\"./img/timeoutWith.png\" width=\"100%\">\n *\n * `timeoutWith` is a variation of `timeout` operator. It behaves exactly the same,\n * still accepting as a first argument either a number or a Date, which control - respectively -\n * when values of source Observable should be emitted or when it should complete.\n *\n * The only difference is that it accepts a second, required parameter. This parameter\n * should be an Observable which will be subscribed when source Observable fails any timeout check.\n * So whenever regular `timeout` would emit an error, `timeoutWith` will instead start re-emitting\n * values from second Observable. Note that this fallback Observable is not checked for timeouts\n * itself, so it can emit values and complete at arbitrary points in time. From the moment of a second\n * subscription, Observable returned from `timeoutWith` simply mirrors fallback stream. When that\n * stream completes, it completes as well.\n *\n * Scheduler, which in case of `timeout` is provided as as second argument, can be still provided\n * here - as a third, optional parameter. It still is used to schedule timeout checks and -\n * as a consequence - when second Observable will be subscribed, since subscription happens\n * immediately after failing check.\n *\n * @example <caption>Add fallback observable</caption>\n * const seconds = Rx.Observable.interval(1000);\n * const minutes = Rx.Observable.interval(60 * 1000);\n *\n * seconds.timeoutWith(900, minutes)\n *     .subscribe(\n *         value => console.log(value), // After 900ms, will start emitting `minutes`,\n *                                      // since first value of `seconds` will not arrive fast enough.\n *         err => console.log(err) // Would be called after 900ms in case of `timeout`,\n *                                 // but here will never be called.\n *     );\n *\n * @param {number|Date} due Number specifying period within which Observable must emit values\n *                          or Date specifying before when Observable should complete\n * @param {Observable<T>} withObservable Observable which will be subscribed if source fails timeout check.\n * @param {Scheduler} [scheduler] Scheduler controlling when timeout checks occur.\n * @return {Observable<T>} Observable that mirrors behaviour of source or, when timeout check fails, of an Observable\n *                          passed as a second parameter.\n * @method timeoutWith\n * @owner Observable\n */\nexport function timeoutWith<T, R>(due: number | Date,\n                                  withObservable: ObservableInput<R>,\n                                  scheduler: IScheduler = async): OperatorFunction<T, T | R> {\n  return (source: Observable<T>) => {\n    let absoluteTimeout = isDate(due);\n    let waitFor = absoluteTimeout ? (+due - scheduler.now()) : Math.abs(<number>due);\n    return source.lift(new TimeoutWithOperator(waitFor, absoluteTimeout, withObservable, scheduler));\n  };\n}\n\nclass TimeoutWithOperator<T> implements Operator<T, T> {\n  constructor(private waitFor: number,\n              private absoluteTimeout: boolean,\n              private withObservable: ObservableInput<any>,\n              private scheduler: IScheduler) {\n  }\n\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n    return source.subscribe(new TimeoutWithSubscriber(\n      subscriber, this.absoluteTimeout, this.waitFor, this.withObservable, this.scheduler\n    ));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass TimeoutWithSubscriber<T, R> extends OuterSubscriber<T, R> {\n\n  private action: Action<TimeoutWithSubscriber<T, R>> = null;\n\n  constructor(destination: Subscriber<T>,\n              private absoluteTimeout: boolean,\n              private waitFor: number,\n              private withObservable: ObservableInput<any>,\n              private scheduler: IScheduler) {\n    super(destination);\n    this.scheduleTimeout();\n  }\n\n  private static dispatchTimeout<T, R>(subscriber: TimeoutWithSubscriber<T, R>): void {\n    const { withObservable } = subscriber;\n    (<any> subscriber)._unsubscribeAndRecycle();\n    subscriber.add(subscribeToResult(subscriber, withObservable));\n  }\n\n  private scheduleTimeout(): void {\n    const { action } = this;\n    if (action) {\n      // Recycle the action if we've already scheduled one. All the production\n      // Scheduler Actions mutate their state/delay time and return themeselves.\n      // VirtualActions are immutable, so they create and return a clone. In this\n      // case, we need to set the action reference to the most recent VirtualAction,\n      // to ensure that's the one we clone from next time.\n      this.action = (<Action<TimeoutWithSubscriber<T, R>>> action.schedule(this, this.waitFor));\n    } else {\n      this.add(this.action = (<Action<TimeoutWithSubscriber<T, R>>> this.scheduler.schedule(\n        TimeoutWithSubscriber.dispatchTimeout, this.waitFor, this\n      )));\n    }\n  }\n\n  protected _next(value: T): void {\n    if (!this.absoluteTimeout) {\n      this.scheduleTimeout();\n    }\n    super._next(value);\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    this.action = null;\n    this.scheduler = null;\n    this.withObservable = null;\n  }\n}\n"]}