{"_from": "opn@~5.1.0", "_id": "opn@5.1.0", "_inBundle": false, "_integrity": "sha512-iPNl7SyM8L30Rm1sjGdLLheyHVw5YXVfi3SKWJzBI7efxRwHojfRFjwE/OLM6qp9xJYMgab8WicTU1cPoY+Hpg==", "_location": "/opn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "opn@~5.1.0", "name": "opn", "escapedName": "opn", "rawSpec": "~5.1.0", "saveSpec": null, "fetchSpec": "~5.1.0"}, "_requiredBy": ["/@angular/cli", "/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/opn/-/opn-5.1.0.tgz", "_shasum": "72ce2306a17dbea58ff1041853352b4a8fc77519", "_spec": "opn@~5.1.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\@angular\\cli", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/opn/issues"}, "bundleDependencies": false, "dependencies": {"is-wsl": "^1.1.0"}, "deprecated": false, "description": "A better node-open. Opens stuff like websites, files, executables. Cross-platform.", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "xdg-open"], "homepage": "https://github.com/sindresorhus/opn#readme", "keywords": ["app", "open", "opn", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "license": "MIT", "name": "opn", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/opn.git"}, "scripts": {"test": "xo && ava"}, "version": "5.1.0"}