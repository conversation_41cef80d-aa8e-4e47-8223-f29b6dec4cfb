{"version": 3, "file": "ro.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ro.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC;QAC9E,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC;QAC3E,CAAC,kBAAkB,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC;KAClF;IACD;QACE,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,AAAD;QAC5E,CAAC,kBAAkB,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC;KAClF;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['miezul nopții', 'la amiază', 'dimineața', 'după-amiaza', 'seara', 'noaptea'],\n    ['miezul nopții', 'amiaz<PERSON>', 'dimineața', 'după-amiaza', 'seara', 'noaptea'],\n    ['la miezul nopții', 'la amiază', 'dimineața', 'după-amiaza', 'seara', 'noaptea']\n  ],\n  [\n    ['miezul nopții', 'amiază', 'dimineața', 'după-amiaza', 'seara', 'noaptea'], ,\n    ['la miezul nopții', 'la amiaz<PERSON>', 'dimineața', 'după-amiaza', 'seara', 'noaptea']\n  ],\n  [\n    '00:00', '12:00', ['05:00', '12:00'], ['12:00', '18:00'], ['18:00', '22:00'],\n    ['22:00', '05:00']\n  ]\n];\n"]}