{"version": 3, "file": "hi.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/hi.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK;YACtC,KAAK;SACN;QACD,AADE;KAEH;IACD;QACE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC;QAC5C,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;KAC9C;IACD,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAC1F,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'मध्यरात्रि', 'सुबह', 'अपराह्न', 'शाम',\n      'रात'\n    ],\n    ,\n  ],\n  [\n    ['आधी रात', 'सुबह', 'अपराह्न', 'शाम', 'रात'],\n    ['मध्यरात्रि', 'सुबह', 'दोपहर', 'शाम', 'रात'],\n  ],\n  ['00:00', ['04:00', '12:00'], ['12:00', '16:00'], ['16:00', '20:00'], ['20:00', '04:00']]\n];\n"]}