{"version": 3, "file": "root.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/root.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,IAAI,EAAE,GAAG,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,UAAU,CAAC;QAC/E,CAAC,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,UAAU,CAAC;KACzF;IACD;QACE,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;QAChE,AADiE;KAElE;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['mi', 'n', 'in the morning', 'in the afternoon', 'in the evening', 'at night'],\n    ['midnight', 'noon', 'in the morning', 'in the afternoon', 'in the evening', 'at night'],\n  ],\n  [\n    ['midnight', 'noon', 'morning', 'afternoon', 'evening', 'night'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '06:00']\n  ]\n];\n"]}