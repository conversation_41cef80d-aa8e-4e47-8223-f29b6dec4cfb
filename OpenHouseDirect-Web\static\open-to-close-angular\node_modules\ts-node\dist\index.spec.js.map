{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,6BAA6B;AAC7B,+CAA2C;AAC3C,6BAA2B;AAC3B,+BAAiC;AACjC,+BAAiC;AACjC,uCAAyC;AACzC,iCAA2C;AAE3C,IAAM,OAAO,GAAG,WAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;AAC3C,IAAM,SAAS,GAAG,WAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;AAChD,IAAM,QAAQ,GAAG,YAAS,SAAS,uBAAgB,OAAO,OAAG,CAAA;AAE7D,IAAM,iBAAiB,GAAG,gFAAgF,CAAA;AAE1G,QAAQ,CAAC,SAAS,EAAE;IAClB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEnB,EAAE,CAAC,mCAAmC,EAAE;QACtC,aAAM,CAAC,eAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAA;IAC9D,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,KAAK,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,EAAE,CAAC,6CAA6C,EAAE,UAAU,IAAI;YAC9D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAMf,IAAM,IAAI,GAAG,qBAAK,CAAC,MAAM,EAAE;gBACzB,SAAS;gBACT,WAAW;gBACX,OAAO;gBACP,eAAe;aAChB,EAAE;gBACD,KAAK,EAAE,WAAW;aACnB,CAAC,CAAA;YAEF,IAAI,MAAM,GAAG,EAAE,CAAA;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI,IAAK,OAAA,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAzB,CAAyB,CAAC,CAAA;YAE3D,IAAI,MAAM,GAAG,EAAE,CAAA;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI,IAAK,OAAA,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAzB,CAAyB,CAAC,CAAA;YAE3D,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,IAAI;gBAC5B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;gBAC3B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;gBACtC,aAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAExB,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAIF,UAAU,CAAC,cAAM,OAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAnB,CAAmB,EAAE,IAAI,CAAC,CAAA;QAC7C,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;YACrC,oBAAI,CAAI,QAAQ,uBAAoB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACzD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yBAAyB,EAAE,UAAU,IAAI;YAC1C,oBAAI,CAAC,oCAAoC,EAAE;gBACzC,GAAG,EAAE,OAAO;aACb,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;YACxD,oBAAI,CAAI,QAAQ,WAAK,WAAI,CAAC,OAAO,EAAE,aAAa,CAAC,OAAG,EAAE,UAAU,GAAG,EAAE,MAAM;gBACzE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sBAAsB,EAAE,UAAU,IAAI;YACvC,oBAAI,CAAI,QAAQ,sEAAiE,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,EAAE,CAAC,iBAAiB,EAAE,UAAU,IAAI;gBAClC,oBAAI,CACF;oBACE,QAAQ;oBACR,6BAA6B;oBAC7B,2DAA2D;iBAC5D,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,MAAM,CAAC,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;gBAC1D,oBAAI,CACF;oBACE,QAAQ;oBACR,6BAA6B;oBAC7B,uEAAuE;iBACxE,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,MAAM,CAAC,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,EAAE,CAAC,kBAAkB,EAAE,UAAU,IAAI;YACnC,oBAAI,CACC,QAAQ,+EAA0E,EACrF,UAAU,GAAG,EAAE,MAAM;gBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qBAAqB,EAAE,UAAU,IAAI;YACtC,oBAAI,CAAI,QAAQ,yFAAoF,EAAE,UAAU,GAAG;gBACjH,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;oBACjB,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAA;gBAChE,CAAC;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CAErC,kEAAkE;oBAClE,iEAAiE,CAClE,CAAC,CAAA;gBAEF,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,iCAAiC,EAAE,UAAU,IAAI;YAClD,oBAAI,CACC,QAAQ,+GAA0G,EACrH,UAAU,GAAG;gBACX,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;oBACjB,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAA;gBAChE,CAAC;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1B,iGAAiG,CAClG,CAAA;gBAED,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAI,QAAQ,8BAA2B,EAAE,UAAU,GAAG;gBACxD,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;oBACjB,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAA;gBAChE,CAAC;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC1B,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,OAAI;oBAC3C,kDAAkD;oBAClD,oBAAoB;oBACpB,uBAAuB;iBACxB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,IAAI,CAAC,mCAAmC,EAAE,UAAU,IAAI;YACzD,oBAAI,CAAI,QAAQ,gDAA2C,EAAE,UAAU,GAAG;gBACxE,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;oBACjB,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAA;gBAChE,CAAC;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC1B,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,OAAI;oBAC3C,kDAAkD;oBAClD,oBAAoB;iBACrB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sCAAsC,EAAE,UAAU,IAAI;YACvD,oBAAI,CAAI,QAAQ,cAAS,EAAE,UAAU,GAAG;gBACtC,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;oBACjB,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAA;gBAChE,CAAC;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAA;gBAElE,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;YAC1D,IAAM,EAAE,GAAG,oBAAI,CAAC,QAAQ,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC7C,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBAElC,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,4BAA4B,EAAE,UAAU,IAAI;YAC7C,IAAM,EAAE,GAAG,oBAAI,CAAI,QAAQ,QAAK,EAAE,UAAU,GAAG,EAAE,MAAM;gBACrD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACtB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,iCAAiC,EAAE,UAAU,IAAI;YAClD,IAAM,EAAE,GAAG,oBAAI,CAAI,QAAQ,qCAAkC,EAAE,UAAU,GAAG,EAAE,MAAM;gBAClF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;gBAEtC,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACtB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAI,QAAQ,0DAAqD,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC1F,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAA;gBAE9D,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,UAAU,IAAI;YAC3D,oBAAI,CAAI,QAAQ,iDAA4C,EAAE,UAAU,GAAG,EAAE,MAAM;gBACjF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,IAAI,CAAC,uCAAuC,EAAE,UAAU,IAAI;YAC7D,oBAAI,CAAI,QAAQ,qDAAkD,EAAE,UAAU,GAAG,EAAE,MAAM;gBACvF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAE/B,MAAM,CAAC,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,UAAU,EAAE;QACnB,gBAAQ,CAAC;YACP,OAAO,EAAE,OAAO;YAChB,eAAe,EAAE;gBACf,GAAG,EAAE,UAAU;aAChB;SACF,CAAC,CAAA;QAEF,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAM,CAAC,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAA;YAEpC,aAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAM,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;YAErC,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAM,CAAC,GAAG,UAAU,CAAC,kBAAkB,EAAE;gBACvC,WAAW,EAAE,OAAO;aACrB,CAAC,CAAA;YAEF,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;YACzC,IAAI,CAAC;gBACH,OAAO,CAAC,gBAAgB,CAAC,CAAA;YAC3B,CAAC;YAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACf,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,uBAAuB;oBACvB,qBAAmB,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,WAAQ;iBAChE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,IAAI,EAAE,CAAA;YACR,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,cAAc,EAAE;YACvB,IAAI,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YACpC,IAAI,QAAgB,CAAA;YAEpB,MAAM,CAAC;gBAAA,iBAWN;gBAVC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,UAAC,CAAM,EAAE,QAAQ;oBAC5C,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;oBAE3B,CAAC,CAAC,QAAQ,GAAG,UAAC,IAAY,EAAE,QAAgB;wBAC1C,QAAQ,GAAG,IAAI,CAAA;wBACf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;oBAC5C,CAAC,CAAA;oBAED,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;gBACzB,CAAC,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,KAAK,CAAC;gBACJ,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;YAClC,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;gBACzC,IAAI,CAAC;oBACH,OAAO,CAAC,uBAAuB,CAAC,CAAA;gBAClC,CAAC;gBAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBACf,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAA;gBACrE,CAAC;gBAED,aAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE5C,IAAI,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA", "sourcesContent": ["import { expect } from 'chai'\nimport { exec, spawn } from 'child_process'\nimport { join } from 'path'\nimport semver = require('semver')\nimport ts = require('typescript')\nimport proxyquire = require('proxyquire')\nimport { register, VERSION } from './index'\n\nconst testDir = join(__dirname, '../tests')\nconst EXEC_PATH = join(__dirname, '../dist/bin')\nconst BIN_EXEC = `node \"${EXEC_PATH}\" --project \"${testDir}\"`\n\nconst SOURCE_MAP_REGEXP = /\\/\\/# sourceMappingURL=data:application\\/json;charset=utf\\-8;base64,[\\w\\+]+=*$/\n\ndescribe('ts-node', function () {\n  this.timeout(10000)\n\n  it('should export the correct version', function () {\n    expect(VERSION).to.equal(require('../package.json').version)\n  })\n\n  describe('cli', function () {\n    this.slow(1000)\n\n    it('should forward signals to the child process', function (done) {\n      this.slow(5000)\n\n      // We use `spawn` instead because apparently TravisCI\n      // does not let subprocesses be killed when ran under `sh`\n      //\n      // See: https://github.com/travis-ci/travis-ci/issues/704#issuecomment-328278149\n      const proc = spawn('node', [\n        EXEC_PATH,\n        '--project',\n        testDir,\n        'tests/signals'\n      ], {\n        shell: '/bin/bash'\n      })\n\n      let stdout = ''\n      proc.stdout.on('data', (data) => stdout += data.toString())\n\n      let stderr = ''\n      proc.stderr.on('data', (data) => stderr += data.toString())\n\n      proc.on('exit', function (code) {\n        expect(stderr).to.equal('')\n        expect(stdout).to.equal('exited fine')\n        expect(code).to.equal(0)\n\n        return done()\n      })\n\n      // Leave enough time for node to fully start\n      // the process, then send a signal\n      setTimeout(() => proc.kill('SIGINT'), 2000)\n    })\n\n    it('should execute cli', function (done) {\n      exec(`${BIN_EXEC} tests/hello-world`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should register via cli', function (done) {\n      exec(`node -r ../register hello-world.ts`, {\n        cwd: testDir\n      }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should execute cli with absolute path', function (done) {\n      exec(`${BIN_EXEC} \"${join(testDir, 'hello-world')}\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should print scripts', function (done) {\n      exec(`${BIN_EXEC} -p \"import { example } from './tests/complex/index';example()\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('example\\n')\n\n        return done()\n      })\n    })\n\n    if (semver.gte(ts.version, '1.8.0')) {\n      it('should allow js', function (done) {\n        exec(\n          [\n            BIN_EXEC,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-p \"import { main } from \\'./tests/allow-js/run\\';main()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n\n      it('should include jsx when `allow-js` true', function (done) {\n        exec(\n          [\n            BIN_EXEC,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-p \"import { Foo2 } from \\'./tests/allow-js/with-jsx\\'; Foo2.sayHi()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n    }\n\n    it('should eval code', function (done) {\n      exec(\n        `${BIN_EXEC} -e \"import * as m from './tests/module';console.log(m.example('test'))\"`,\n        function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('TEST\\n')\n\n          return done()\n        }\n      )\n    })\n\n    it('should throw errors', function (done) {\n      exec(`${BIN_EXEC} --type-check -e \"import * as m from './tests/module';console.log(m.example(123))\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.match(new RegExp(\n          // Node 0.10 can not override the `lineOffset` option.\n          '\\\\[eval\\\\]\\\\.ts \\\\(1,59\\\\): Argument of type \\'(?:number|123)\\' ' +\n          'is not assignable to parameter of type \\'string\\'\\\\. \\\\(2345\\\\)'\n        ))\n\n        return done()\n      })\n    })\n\n    it('should be able to ignore errors', function (done) {\n      exec(\n        `${BIN_EXEC} --type-check --ignoreWarnings 2345 -e \"import * as m from './tests/module';console.log(m.example(123))\"`,\n        function (err) {\n          if (err === null) {\n            return done('Command was expected to fail, but it succeeded.')\n          }\n\n          expect(err.message).to.match(\n            /TypeError: (?:(?:undefined|foo\\.toUpperCase) is not a function|.*has no method \\'toUpperCase\\')/\n          )\n\n          return done()\n        }\n      )\n    })\n\n    it('should work with source maps', function (done) {\n      exec(`${BIN_EXEC} --type-check tests/throw`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:3`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^',\n          'Error: this is a demo'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it.skip('eval should work with source maps', function (done) {\n      exec(`${BIN_EXEC} --type-check -p \"import './tests/throw'\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:3`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it('should use transpile mode by default', function (done) {\n      exec(`${BIN_EXEC} -p \"x\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain('ReferenceError: x is not defined')\n\n        return done()\n      })\n    })\n\n    it('should pipe into `ts-node` and evaluate', function (done) {\n      const cp = exec(BIN_EXEC, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('hello\\n')\n\n        return done()\n      })\n\n      cp.stdin.end(\"console.log('hello')\")\n    })\n\n    it('should pipe into `ts-node`', function (done) {\n      const cp = exec(`${BIN_EXEC} -p`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('true\\n')\n\n        return done()\n      })\n\n      cp.stdin.end('true')\n    })\n\n    it('should pipe into an eval script', function (done) {\n      const cp = exec(`${BIN_EXEC} --fast -p 'process.stdin.isTTY'`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('undefined\\n')\n\n        return done()\n      })\n\n      cp.stdin.end('true')\n    })\n\n    it('should support require flags', function (done) {\n      exec(`${BIN_EXEC} -r ./tests/hello-world -p \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\nsuccess\\nundefined\\n')\n\n        return done()\n      })\n    })\n\n    it('should support require from node modules', function (done) {\n      exec(`${BIN_EXEC} -r typescript -e \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('success\\n')\n\n        return done()\n      })\n    })\n\n    it.skip('should use source maps with react tsx', function (done) {\n      exec(`${BIN_EXEC} -r ./tests/emit-compiled.ts tests/jsx-react.tsx`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('todo')\n\n        return done()\n      })\n    })\n  })\n\n  describe('register', function () {\n    register({\n      project: testDir,\n      compilerOptions: {\n        jsx: 'preserve'\n      }\n    })\n\n    it('should be able to require typescript', function () {\n      const m = require('../tests/module')\n\n      expect(m.example('foo')).to.equal('FOO')\n    })\n\n    it('should compile through js and ts', function () {\n      const m = require('../tests/complex')\n\n      expect(m.example()).to.equal('example')\n    })\n\n    it('should work with proxyquire', function () {\n      const m = proxyquire('../tests/complex', {\n        './example': 'hello'\n      })\n\n      expect(m.example()).to.equal('hello')\n    })\n\n    it('should use source maps', function (done) {\n      try {\n        require('../tests/throw')\n      } catch (error) {\n        expect(error.stack).to.contain([\n          'Error: this is a demo',\n          `    at Foo.bar (${join(__dirname, '../tests/throw.ts')}:3:18)`\n        ].join('\\n'))\n\n        done()\n      }\n    })\n\n    describe('JSX preserve', () => {\n      let old = require.extensions['.tsx']\n      let compiled: string\n\n      before(function () {\n        require.extensions['.tsx'] = (m: any, fileName) => {\n          const _compile = m._compile\n\n          m._compile = (code: string, fileName: string) => {\n            compiled = code\n            return _compile.call(this, code, fileName)\n          }\n\n          return old(m, fileName)\n        }\n      })\n\n      after(function () {\n        require.extensions['.tsx'] = old\n      })\n\n      it('should use source maps', function (done) {\n        try {\n          require('../tests/with-jsx.tsx')\n        } catch (error) {\n          expect(error.stack).to.contain('SyntaxError: Unexpected token <\\n')\n        }\n\n        expect(compiled).to.match(SOURCE_MAP_REGEXP)\n\n        done()\n      })\n    })\n  })\n})\n"]}