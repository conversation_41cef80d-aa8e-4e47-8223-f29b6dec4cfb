{"version": 3, "file": "windowCount.js", "sourceRoot": "", "sources": ["../../src/operators/windowCount.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAE3C,wBAAwB,YAAY,CAAC,CAAA;AAGrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+CG;AACH,qBAA+B,UAAkB,EAClB,gBAA4B;IAA5B,gCAA4B,GAA5B,oBAA4B;IACzD,MAAM,CAAC,qCAAqC,MAAqB;QAC/D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAI,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;AACJ,CAAC;AALe,mBAAW,cAK1B,CAAA;AAED;IAEE,6BAAoB,UAAkB,EAClB,gBAAwB;QADxB,eAAU,GAAV,UAAU,CAAQ;QAClB,qBAAgB,GAAhB,gBAAgB,CAAQ;IAC5C,CAAC;IAED,kCAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACzG,CAAC;IACH,0BAAC;AAAD,CAAC,AATD,IASC;AAED;;;;GAIG;AACH;IAAuC,yCAAa;IAIlD,+BAAsB,WAAsC,EACxC,UAAkB,EAClB,gBAAwB;QAC1C,kBAAM,WAAW,CAAC,CAAC;QAHC,gBAAW,GAAX,WAAW,CAA2B;QACxC,eAAU,GAAV,UAAU,CAAQ;QAClB,qBAAgB,GAAhB,gBAAgB,CAAQ;QALpC,YAAO,GAAiB,CAAE,IAAI,iBAAO,EAAK,CAAE,CAAC;QAC7C,UAAK,GAAW,CAAC,CAAC;QAMxB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAES,qCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/F,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAE3B,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QACD,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,gBAAgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACzD,OAAO,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC;QACD,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,gBAAgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1D,IAAM,QAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;YACrB,WAAW,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAES,sCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACZ,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC1C,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,yCAAS,GAAnB;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACZ,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC1C,OAAO,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED,oCAAoC,CAAC,4CAAY,GAAZ;QACnC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IACH,4BAAC;AAAD,CAAC,AAxDD,CAAuC,uBAAU,GAwDhD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { OperatorFunction } from '../interfaces';\n\n/**\n * Branch out the source Observable values as a nested Observable with each\n * nested Observable emitting at most `windowSize` values.\n *\n * <span class=\"informal\">It's like {@link bufferCount}, but emits a nested\n * Observable instead of an array.</span>\n *\n * <img src=\"./img/windowCount.png\" width=\"100%\">\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable emits windows every `startWindowEvery`\n * items, each containing no more than `windowSize` items. When the source\n * Observable completes or encounters an error, the output Observable emits\n * the current window and propagates the notification from the source\n * Observable. If `startWindowEvery` is not provided, then new windows are\n * started immediately at the start of the source and when each window completes\n * with size `windowSize`.\n *\n * @example <caption>Ignore every 3rd click event, starting from the first one</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var result = clicks.windowCount(3)\n *   .map(win => win.skip(1)) // skip first of every 3 clicks\n *   .mergeAll(); // flatten the Observable-of-Observables\n * result.subscribe(x => console.log(x));\n *\n * @example <caption>Ignore every 3rd click event, starting from the third one</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var result = clicks.windowCount(2, 3)\n *   .mergeAll(); // flatten the Observable-of-Observables\n * result.subscribe(x => console.log(x));\n *\n * @see {@link window}\n * @see {@link windowTime}\n * @see {@link windowToggle}\n * @see {@link windowWhen}\n * @see {@link bufferCount}\n *\n * @param {number} windowSize The maximum number of values emitted by each\n * window.\n * @param {number} [startWindowEvery] Interval at which to start a new window.\n * For example if `startWindowEvery` is `2`, then a new window will be started\n * on every other value from the source. A new window is started at the\n * beginning of the source by default.\n * @return {Observable<Observable<T>>} An Observable of windows, which in turn\n * are Observable of values.\n * @method windowCount\n * @owner Observable\n */\nexport function windowCount<T>(windowSize: number,\n                               startWindowEvery: number = 0): OperatorFunction<T, Observable<T>> {\n  return function windowCountOperatorFunction(source: Observable<T>) {\n    return source.lift(new WindowCountOperator<T>(windowSize, startWindowEvery));\n  };\n}\n\nclass WindowCountOperator<T> implements Operator<T, Observable<T>> {\n\n  constructor(private windowSize: number,\n              private startWindowEvery: number) {\n  }\n\n  call(subscriber: Subscriber<Observable<T>>, source: any): any {\n    return source.subscribe(new WindowCountSubscriber(subscriber, this.windowSize, this.startWindowEvery));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass WindowCountSubscriber<T> extends Subscriber<T> {\n  private windows: Subject<T>[] = [ new Subject<T>() ];\n  private count: number = 0;\n\n  constructor(protected destination: Subscriber<Observable<T>>,\n              private windowSize: number,\n              private startWindowEvery: number) {\n    super(destination);\n    destination.next(this.windows[0]);\n  }\n\n  protected _next(value: T) {\n    const startWindowEvery = (this.startWindowEvery > 0) ? this.startWindowEvery : this.windowSize;\n    const destination = this.destination;\n    const windowSize = this.windowSize;\n    const windows = this.windows;\n    const len = windows.length;\n\n    for (let i = 0; i < len && !this.closed; i++) {\n      windows[i].next(value);\n    }\n    const c = this.count - windowSize + 1;\n    if (c >= 0 && c % startWindowEvery === 0 && !this.closed) {\n      windows.shift().complete();\n    }\n    if (++this.count % startWindowEvery === 0 && !this.closed) {\n      const window = new Subject<T>();\n      windows.push(window);\n      destination.next(window);\n    }\n  }\n\n  protected _error(err: any) {\n    const windows = this.windows;\n    if (windows) {\n      while (windows.length > 0 && !this.closed) {\n        windows.shift().error(err);\n      }\n    }\n    this.destination.error(err);\n  }\n\n  protected _complete() {\n    const windows = this.windows;\n    if (windows) {\n      while (windows.length > 0 && !this.closed) {\n        windows.shift().complete();\n      }\n    }\n    this.destination.complete();\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    this.count = 0;\n    this.windows = null;\n  }\n}\n"]}