{"_from": "thunky@^1.0.2", "_id": "thunky@1.1.0", "_inBundle": false, "_integrity": "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==", "_location": "/thunky", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "thunky@^1.0.2", "name": "thunky", "escapedName": "thunky", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/multicast-dns"], "_resolved": "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz", "_shasum": "5abaf714a9405db0504732bbccd2cedd9ef9537d", "_spec": "thunky@^1.0.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\multicast-dns", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mafintosh/thunky/issues"}, "bundleDependencies": false, "deprecated": false, "description": "delay the evaluation of a paramless async function and cache the result", "devDependencies": {"standard": "^12.0.1", "tape": "^4.9.1"}, "homepage": "https://github.com/mafintosh/thunky#readme", "keywords": ["memo", "thunk", "async", "lazy", "control", "flow", "cache"], "license": "MIT", "main": "index.js", "name": "thunky", "repository": {"type": "git", "url": "git://github.com/mafintosh/thunky.git"}, "scripts": {"test": "standard && tape test.js"}, "version": "1.1.0"}