{"version": 3, "file": "onErrorResumeNext.js", "sourceRoot": "", "sources": ["../../../src/add/observable/onErrorResumeNext.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,kCAA6D,oCAAoC,CAAC,CAAA;AAElG,uBAAU,CAAC,iBAAiB,GAAG,qCAAuB,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\r\nimport { onErrorResumeNext as staticOnErrorResumeNext } from '../../observable/onErrorResumeNext';\r\n\r\nObservable.onErrorResumeNext = staticOnErrorResumeNext;\r\n\r\ndeclare module '../../Observable' {\r\n  namespace Observable {\r\n    export let onErrorResumeNext: typeof staticOnErrorResumeNext;\r\n  }\r\n}"]}