{"version": 3, "file": "ta-LK.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ta-LK.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;YACrD,WAAW,EAAE,KAAK;SACnB;QACD;YACE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM;YACxC,QAAQ,EAAE,UAAU,EAAE,MAAM;YAC5B,YAAY,EAAE,MAAM;SACrB;KACF;IACD;QACE;YACE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;YACrD,WAAW,EAAE,IAAI;SAClB;QACD;YACE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM;YACxC,QAAQ,EAAE,UAAU,EAAE,MAAM;YAC5B,YAAY,EAAE,MAAM;SACrB;KACF;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC/E;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'நள்.', 'நண்.', 'அதி.', 'கா.', 'மதி.', 'பிற்.', 'மா.',\n      'அந்தி மா.', 'இர.'\n    ],\n    [\n      'நள்ளிரவு', 'நண்பகல்', 'அதிகாலை', 'காலை',\n      'மதியம்', 'பிற்பகல்', 'மாலை',\n      'அந்தி மாலை', 'இரவு'\n    ],\n  ],\n  [\n    [\n      'நள்.', 'நண்.', 'அதி.', 'கா.', 'மதி.', 'பிற்.', 'மா.',\n      'அந்தி மா.', 'இ.'\n    ],\n    [\n      'நள்ளிரவு', 'நண்பகல்', 'அதிகாலை', 'காலை',\n      'மதியம்', 'பிற்பகல்', 'மாலை',\n      'அந்தி மாலை', 'இரவு'\n    ],\n  ],\n  [\n    '00:00', '12:00', ['03:00', '05:00'], ['05:00', '12:00'], ['12:00', '14:00'],\n    ['14:00', '16:00'], ['16:00', '18:00'], ['18:00', '21:00'], ['21:00', '03:00']\n  ]\n];\n"]}