{"version": 3, "file": "layout.js", "sources": ["../../../src/cdk/layout/index.ts", "../../../src/cdk/layout/public-api.ts", "../../../src/cdk/layout/breakpoints.ts", "../../../src/cdk/layout/breakpoints-observer.ts", "../../../src/cdk/layout/media-matcher.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {NgModule} from '@angular/core';\nimport {PlatformModule} from '@angular/cdk/platform';\nimport {BreakpointObserver} from './breakpoints-observer';\nimport {MediaMatcher} from './media-matcher';\n\n@NgModule({\n  providers: [BreakpointObserver, MediaMatcher],\n  imports: [PlatformModule],\n})\nexport class LayoutModule {}\n\nexport {BreakpointObserver, BreakpointState} from './breakpoints-observer';\nexport {Breakpoints} from './breakpoints';\nexport {MediaMatcher} from './media-matcher';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nexport const Breakpoints = {\n  XSmall: '(max-width: 599px)',\n  Small: '(min-width: 600px) and (max-width: 959px)',\n  Medium: '(min-width: 960px) and (max-width: 1279px)',\n  Large: '(min-width: 1280px) and (max-width: 1919px)',\n  XLarge: '(min-width: 1920px)',\n\n  Handset: '(max-width: 599px) and (orientation: portrait), ' +\n           '(max-width: 959px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839px) and (orientation: portrait), ' +\n          '(min-width: 960px) and (max-width: 1279px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' +\n       '(min-width: 1280px) and (orientation: landscape)',\n\n  HandsetPortrait: '(max-width: 599px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n\n  HandsetLandscape: '(max-width: 959px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Injectable, NgZone, OnDestroy} from '@angular/core';\nimport {MediaMatcher} from './media-matcher';\nimport {Observable} from 'rxjs/Observable';\nimport {Subject} from 'rxjs/Subject';\nimport {map} from 'rxjs/operators/map';\nimport {startWith} from 'rxjs/operators/startWith';\nimport {takeUntil} from 'rxjs/operators/takeUntil';\nimport {coerceArray} from '@angular/cdk/coercion';\nimport {combineLatest} from 'rxjs/observable/combineLatest';\nimport {fromEventPattern} from 'rxjs/observable/fromEventPattern';\n\n/** The current state of a layout breakpoint. */\nexport interface BreakpointState {\n  /** Whether the breakpoint is currently matching. */\n  matches: boolean;\n}\n\ninterface Query {\n  observable: Observable<BreakpointState>;\n  mql: MediaQueryList;\n}\n\n/** Utility for checking the matching state of @media queries. */\n@Injectable()\nexport class BreakpointObserver implements OnDestroy {\n  /**  A map of all media queries currently being listened for. */\n  private _queries: Map<string, Query> = new Map();\n  /** A subject for all other observables to takeUntil based on. */\n  private _destroySubject: Subject<{}> = new Subject();\n\n  constructor(private mediaMatcher: MediaMatcher, private zone: NgZone) {}\n\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value: string | string[]): boolean {\n    let queries = coerceArray(value);\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value: string | string[]): Observable<BreakpointState> {\n    let queries = coerceArray(value);\n    let observables = queries.map(query => this._registerQuery(query).observable);\n\n    return combineLatest(observables, (a: BreakpointState, b: BreakpointState) => {\n      return {\n        matches: !!((a && a.matches) || (b && b.matches)),\n      };\n    });\n  }\n\n  /** Registers a specific query to be listened for. */\n  private _registerQuery(query: string): Query {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query)!;\n    }\n\n    let mql: MediaQueryList = this.mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    let queryObservable = fromEventPattern(\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      (listener: MediaQueryListListener) => {\n        mql.addListener((e: MediaQueryList) => this.zone.run(() => listener(e)));\n      },\n      (listener: MediaQueryListListener) => {\n        mql.removeListener((e: MediaQueryList) => this.zone.run(() => listener(e)));\n      })\n      .pipe(\n        takeUntil(this._destroySubject),\n        startWith(mql),\n        map((nextMql: MediaQueryList) => ({matches: nextMql.matches}))\n      );\n\n    // Add the MediaQueryList to the set of queries.\n    let output = {observable: queryObservable, mql: mql};\n    this._queries.set(query, output);\n    return output;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Injectable} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\n\n/**\n * Global registry for all dynamically-created, injected style tags.\n */\nconst styleElementForWebkitCompatibility: Map<string, HTMLStyleElement> = new Map();\n\n/** A utility for calling matchMedia queries. */\n@Injectable()\nexport class MediaMatcher {\n  /** The internal matchMedia method to return back a MediaQueryList like object. */\n  private _matchMedia: (query: string) => MediaQueryList;\n\n  constructor(private platform: Platform) {\n    this._matchMedia = this.platform.isBrowser && window.matchMedia ?\n      // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n      // call it from a different scope.\n      window.matchMedia.bind(window) :\n      noopMatchMedia;\n  }\n\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query: string): MediaQueryList {\n    if (this.platform.WEBKIT) {\n      createEmptyStyleRule(query);\n    }\n    return this._matchMedia(query);\n  }\n}\n\n/**\n * For Webkit engines that only trigger the MediaQueryListListener when there is at least one CSS\n * selector for the respective media query.\n */\nfunction createEmptyStyleRule(query: string) {\n  if (!styleElementForWebkitCompatibility.has(query)) {\n    try {\n      const style = document.createElement('style');\n\n      style.setAttribute('type', 'text/css');\n      if (!style.sheet) {\n        const cssText = `@media ${query} {.fx-query-test{ }}`;\n        style.appendChild(document.createTextNode(cssText));\n      }\n\n      document.getElementsByTagName('head')[0].appendChild(style);\n\n      // Store in private global registry\n      styleElementForWebkitCompatibility.set(query, style);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n}\n\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query: string): MediaQueryList {\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AIOA,AACA;;;AAKA,MAAM,kCAAkC,GAAkC,IAAI,GAAG,EAAE,CAAC;;;;AAIpF,AAAA,MAAA,YAAA,CAAA;;;;IAIE,WAAF,CAAsB,QAAkB,EAAxC;QAAsB,IAAtB,CAAA,QAA8B,GAAR,QAAQ,CAAU;QACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU;;;YAG7D,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,cAAc,CAAC;KAClB;;;;;;;;;IAQD,UAAU,CAAC,KAAa,EAA1B;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACxB,oBAAoB,CAAC,KAAK,CAAC,CAAC;SAC7B;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KAChC;;;IAxBH,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IARA,EAAA,IAAA,EAAQ,QAAQ,GAAhB;;;;;;;;AAuCA,SAAA,oBAAA,CAA8B,KAAa,EAA3C;IACE,IAAI,CAAC,kCAAkC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QAClD,IAAI;YACF,uBAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAE9C,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBAChB,uBAAM,OAAO,GAAG,CAAxB,OAAA,EAAkC,KAAK,CAAvC,oBAAA,CAA6D,CAAC;gBACtD,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;aACrD;YAED,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;YAG5D,kCAAkC,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACtD;QAAC,wBAAO,CAAC,EAAE;YACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAClB;KACF;CACF;;;;;;AAGD,SAAA,cAAA,CAAwB,KAAa,EAArC;IACE,OAAO;QACL,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;QACxC,KAAK,EAAE,KAAK;QACZ,WAAW,EAAE,MAAjB,GAAyB;QACrB,cAAc,EAAE,MAApB,GAA4B;KACzB,CAAC;CACH;;;;;;ADrED,AACA,AAEA,AACA,AACA,AACA,AACA,AACA,AACA;;;;;;;;AAeA,AAAA,MAAA,kBAAA,CAAA;;;;;IAME,WAAF,CAAsB,YAA0B,EAAU,IAAY,EAAtE;QAAsB,IAAtB,CAAA,YAAkC,GAAZ,YAAY,CAAc;QAAU,IAA1D,CAAA,IAA8D,GAAJ,IAAI,CAAQ;;;;QAJtE,IAAA,CAAA,QAAA,GAAyC,IAAI,GAAG,EAAE,CAAlD;;;;QAEA,IAAA,CAAA,eAAA,GAAyC,IAAI,OAAO,EAAE,CAAtD;KAE0E;;;;;IAGxE,WAAW,GAAb;QACI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;KACjC;;;;;;IAOD,SAAS,CAAC,KAAwB,EAApC;QACI,qBAAI,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KAChF;;;;;;;IAOD,OAAO,CAAC,KAAwB,EAAlC;QACI,qBAAI,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,qBAAI,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;QAE9E,OAAO,aAAa,CAAC,WAAW,EAAE,CAAC,CAAkB,EAAE,CAAkB,KAA7E;YACM,OAAO;gBACL,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;aAClD,CAAC;SACH,CAAC,CAAC;KACJ;;;;;;IAGO,cAAc,CAAC,KAAa,EAAtC;;QAEI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC5B,0BAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAE;SAClC;QAED,qBAAI,GAAG,GAAmB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;;QAE9D,qBAAI,eAAe,GAAG,gBAAgB;;;;;;QAMpC,CAAC,QAAgC,KAAvC;YACQ,GAAG,CAAC,WAAW,CAAC,CAAC,CAAiB,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1E,EACD,CAAC,QAAgC,KADvC;YAEQ,GAAG,CAAC,cAAc,CAAC,CAAC,CAAiB,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7E,CAAC;aACD,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,EAC/B,SAAS,CAAC,GAAG,CAAC,EACd,GAAG,CAAC,CAAC,OAAuB,MAAM,EAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAC,CAAC,CAAC,CAC/D,CAAC;;QAGJ,qBAAI,MAAM,GAAG,EAAC,UAAU,EAAE,eAAe,EAAE,GAAG,EAAE,GAAG,EAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACjC,OAAO,MAAM,CAAC;;;;IAvElB,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IAtBA,EAAA,IAAA,EAAQ,YAAY,GAApB;IADA,EAAA,IAAA,EAAoB,MAAM,GAA1B;;;;;;;;ADEA,AAAO,MAAM,WAAW,GAAG;IACzB,MAAM,EAAE,oBAAoB;IAC5B,KAAK,EAAE,2CAA2C;IAClD,MAAM,EAAE,4CAA4C;IACpD,KAAK,EAAE,6CAA6C;IACpD,MAAM,EAAE,qBAAqB;IAE7B,OAAO,EAAE,kDAAkD;QAClD,iDAAiD;IAC1D,MAAM,EAAE,yEAAyE;QACzE,yEAAyE;IACjF,GAAG,EAAE,kDAAkD;QAClD,kDAAkD;IAEvD,eAAe,EAAE,gDAAgD;IACjE,cAAc,EAAE,uEAAuE;IACvF,WAAW,EAAE,gDAAgD;IAE7D,gBAAgB,EAAE,iDAAiD;IACnE,eAAe,EAAE,yEAAyE;IAC1F,YAAY,EAAE,kDAAkD;CACjE,CAAC;;;;;;ADvBF,AACA,AACA,AACA,AAMA,AAAA,MAAA,YAAA,CAAA;;;IAJA,EAAA,IAAA,EAAC,QAAQ,EAAT,IAAA,EAAA,CAAU;gBACR,SAAS,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC;gBAC7C,OAAO,EAAE,CAAC,cAAc,CAAC;aAC1B,EAAD,EAAA;;;uCAGA,AACA,AACA,AAA6C;;;;;;;;GDhB7C,AAA6B;;"}