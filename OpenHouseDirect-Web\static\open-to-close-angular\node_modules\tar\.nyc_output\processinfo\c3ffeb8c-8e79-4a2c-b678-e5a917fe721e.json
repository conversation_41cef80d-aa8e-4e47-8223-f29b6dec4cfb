{"uuid": "c3ffeb8c-8e79-4a2c-b678-e5a917fe721e", "parent": "37d58de4-deea-4808-bb77-d27685bd1501", "pid": 93243, "argv": ["/usr/local/bin/node", "/Users/<USER>/dev/js/tar/test/extract.js"], "execArgv": ["-r", "/usr/local/lib/node_modules/tap/node_modules/esm/esm.js"], "cwd": "/Users/<USER>/dev/js/tar", "time": 1557878801464, "ppid": 93238, "root": "e52f8603-1293-44df-8bfa-ed740bdd2b77", "coverageFilename": "/Users/<USER>/dev/js/tar/.nyc_output/c3ffeb8c-8e79-4a2c-b678-e5a917fe721e.json", "externalId": "test/extract.js", "files": ["/Users/<USER>/dev/js/tar/lib/pack.js", "/Users/<USER>/dev/js/tar/lib/entry-writer.js", "/Users/<USER>/dev/js/tar/lib/entry.js", "/Users/<USER>/dev/js/tar/lib/global-header-writer.js", "/Users/<USER>/dev/js/tar/lib/parse.js", "/Users/<USER>/dev/js/tar/lib/buffer-entry.js", "/Users/<USER>/dev/js/tar/lib/extended-header.js", "/Users/<USER>/dev/js/tar/lib/extract.js"]}