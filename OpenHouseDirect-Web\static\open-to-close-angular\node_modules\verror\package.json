{"_from": "verror@1.10.0", "_id": "verror@1.10.0", "_inBundle": false, "_integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "_location": "/verror", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "verror@1.10.0", "name": "verror", "escapedName": "verror", "rawSpec": "1.10.0", "saveSpec": null, "fetchSpec": "1.10.0"}, "_requiredBy": ["/jsprim"], "_resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "_shasum": "3a105ca17053af55d6e270c1f8288682e18da400", "_spec": "verror@1.10.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\jsprim", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "bundleDependencies": false, "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "deprecated": false, "description": "richer JavaScript errors", "engines": ["node >=0.6.0"], "homepage": "https://github.com/davepacheco/node-verror#readme", "license": "MIT", "main": "./lib/verror.js", "name": "verror", "repository": {"type": "git", "url": "git://github.com/davepacheco/node-verror.git"}, "scripts": {"test": "make test"}, "version": "1.10.0"}