{"version": 3, "sources": ["es6-shim/Promise.browserify-es6.js", "lib/Promise.js", "lib/Scheduler.js", "lib/decorators/unhandledRejection.js", "lib/env.js", "lib/format.js", "lib/makePromise.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/es6-shim/Promise.browserify-es6.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/Promise.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/Scheduler.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/unhandledRejection.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/env.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/format.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/makePromise.js"], "names": ["unhandledRejections", "require", "PromiseConstructor", "module", "exports", "global", "Promise", "self", "define", "makePromise", "Scheduler", "async", "asap", "scheduler", "amd", "factory", "this", "_async", "_running", "_queue", "_queueLen", "_afterQueue", "_afterQueueLen", "drain", "_drain", "prototype", "enqueue", "task", "run", "afterQueue", "i", "throwit", "e", "noop", "setTimer", "format", "report", "r", "handled", "reported", "push", "logError", "id", "formatError", "value", "unreport", "indexOf", "splice", "logInfo", "formatObject", "f", "x", "tasks", "running", "flush", "length", "shift", "localConsole", "console", "error", "log", "info", "onPotentiallyUnhandledRejection", "rejection", "onPotentiallyUnhandledRejectionHandled", "onFatalRejection", "isNode", "process", "Object", "toString", "call", "hasMutationObserver", "MutationObserver", "WebKitMutationObserver", "initMutationObserver", "scheduled", "node", "document", "createTextNode", "o", "observe", "characterData", "data", "MutationObs", "capturedSetTimeout", "setTimeout", "ms", "clearTimer", "t", "clearTimeout", "nextTick", "vertxRequire", "vertx", "cancelTimer", "runOnLoop", "runOnContext", "s", "stack", "message", "Error", "String", "JSON", "tryStringify", "defaultValue", "stringify", "environment", "resolver", "handler", "_handler", "Handler", "init", "promiseResolve", "resolve", "promiseReject", "reason", "reject", "promiseNotify", "notify", "Pending", "isPromise", "Async", "<PERSON><PERSON><PERSON><PERSON>", "Rejected", "never", "foreverP<PERSON><PERSON><PERSON><PERSON>", "defer", "begetFrom", "parent", "child", "receiver", "join", "context", "all", "promises", "traverseWith", "snd", "traverse", "tryCatch2", "tryMap", "mapAt", "resolved", "traverseAt", "settleAt", "results", "pending", "become", "Fulfilled", "Array", "maybeThenable", "h", "getHandlerMaybeThenable", "state", "fold", "visitRemaining", "start", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "_unreport", "race", "TypeError", "runRace", "getHandlerUntrusted", "untrustedThen", "then", "Thenable", "FailIfRejected", "inheritedContext", "createContext", "consumers", "thenable", "AssimilateTask", "errorId", "_report", "ReportTask", "UnreportTask", "cycle", "ContinuationTask", "continuation", "ProgressTask", "_then", "tryAssimilate", "Fold", "z", "c", "to", "failIfRejected", "runContinuation1", "next", "enterContext", "tryCatchReject", "exitContext", "runContinuation3", "tryCatchReject3", "runNotify", "tryCatchReturn", "a", "b", "thisArg", "y", "inherit", "Parent", "Child", "objectCreate", "constructor", "hasCustomEvent", "CustomEvent", "ev", "ignoredException", "hasInternetExplorerCustomEvent", "createEvent", "initCustomEvent", "initEmitRejection", "emit", "type", "detail", "key", "bubbles", "cancelable", "dispatchEvent", "emitRejection", "create", "proto", "_defer", "onFulfilled", "onRejected", "onProgress", "p", "_beget", "chain", "_traverse", "_visitRemaining", "when", "fail", "_state", "fulfilled", "rejected", "progress", "q", "cont", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_resolve", "_reject", "_notify"], "mappings": "krBOOA,GAAAA,GAAAC,EAAA,wCACAC,EAAAF,EAAAC,EAAA,kBAEAE,GAAAC,QAAA,mBAAAC,QAAAA,OAAAC,QAAAJ,EACA,mBAAAK,MAAAA,KAAAD,QAAAJ,EACAA,sFCRA,SAAAM,GAAA,YACAA,GAAA,SAAAP,GAEA,GAAAQ,GAAAR,EAAA,iBACAS,EAAAT,EAAA,eACAU,EAAAV,EAAA,SAAAW,IAEA,OAAAH,IACAI,UAAA,GAAAH,GAAAC,QAIA,kBAAAH,IAAAA,EAAAM,IAAAN,EAAA,SAAAO,GAAAZ,EAAAC,QAAAW,EAAAd,yECZA,SAAAO,GAAA,YACAA,GAAA,WAUA,QAAAE,GAAAC,GACAK,KAAAC,OAAAN,EACAK,KAAAE,UAAA,EAEAF,KAAAG,OAAAH,KACAA,KAAAI,UAAA,EACAJ,KAAAK,eACAL,KAAAM,eAAA,CAEA,IAAAf,GAAAS,IACAA,MAAAO,MAAA,WACAhB,EAAAiB,UAkDA,MA1CAd,GAAAe,UAAAC,QAAA,SAAAC,GACAX,KAAAG,OAAAH,KAAAI,aAAAO,EACAX,KAAAY,OAOAlB,EAAAe,UAAAI,WAAA,SAAAF,GACAX,KAAAK,YAAAL,KAAAM,kBAAAK,EACAX,KAAAY,OAGAlB,EAAAe,UAAAG,IAAA,WACAZ,KAAAE,WACAF,KAAAE,UAAA,EACAF,KAAAC,OAAAD,KAAAO,SAOAb,EAAAe,UAAAD,OAAA,WAEA,IADA,GAAAM,GAAA,EACAA,EAAAd,KAAAI,YAAAU,EACAd,KAAAG,OAAAW,GAAAF,MACAZ,KAAAG,OAAAW,GAAA,MAMA,KAHAd,KAAAI,UAAA,EACAJ,KAAAE,UAAA,EAEAY,EAAA,EAAAA,EAAAd,KAAAM,iBAAAQ,EACAd,KAAAK,YAAAS,GAAAF,MACAZ,KAAAK,YAAAS,GAAA,MAGAd,MAAAM,eAAA,GAGAZ,KAGA,kBAAAF,IAAAA,EAAAM,IAAAN,EAAA,SAAAO,GAAAZ,EAAAC,QAAAW,+BC3EA,SAAAP,GAAA,YACAA,GAAA,SAAAP,GAyEA,QAAA8B,GAAAC,GACA,KAAAA,GAGA,QAAAC,MA3EA,GAAAC,GAAAjC,EAAA,UAAAiC,SACAC,EAAAlC,EAAA,YAEA,OAAA,UAAAK,GAoCA,QAAA8B,GAAAC,GACAA,EAAAC,UACAC,EAAAC,KAAAH,GACAI,EAAA,oCAAAJ,EAAAK,GAAA,KAAAP,EAAAQ,YAAAN,EAAAO,SAIA,QAAAC,GAAAR,GACA,GAAAP,GAAAS,EAAAO,QAAAT,EACAP,IAAA,IACAS,EAAAQ,OAAAjB,EAAA,GACAkB,EAAA,+BAAAX,EAAAK,GAAA,KAAAP,EAAAc,aAAAZ,EAAAO,SAIA,QAAAlB,GAAAwB,EAAAC,GACAC,EAAAZ,KAAAU,EAAAC,GACA,OAAAE,IACAA,EAAAnB,EAAAoB,EAAA,IAIA,QAAAA,KAEA,IADAD,EAAA,KACAD,EAAAG,OAAA,GACAH,EAAAI,QAAAJ,EAAAI,SA3DA,GAEAC,GAFAhB,EAAAR,EACAe,EAAAf,CAGA,oBAAAyB,WAIAD,EAAAC,QACAjB,EAAA,mBAAAgB,GAAAE,MACA,SAAA3B,GAAAyB,EAAAE,MAAA3B,IACA,SAAAA,GAAAyB,EAAAG,IAAA5B,IAEAgB,EAAA,mBAAAS,GAAAI,KACA,SAAA7B,GAAAyB,EAAAI,KAAA7B,IACA,SAAAA,GAAAyB,EAAAG,IAAA5B,KAGA1B,EAAAwD,gCAAA,SAAAC,GACArC,EAAAU,EAAA2B,IAGAzD,EAAA0D,uCAAA,SAAAD,GACArC,EAAAmB,EAAAkB,IAGAzD,EAAA2D,iBAAA,SAAAF,GACArC,EAAAK,EAAAgC,EAAAnB,OAGA,IAAAQ,MACAb,KACAc,EAAA,IA+BA,OAAA/C,OAUA,kBAAAE,IAAAA,EAAAM,IAAAN,EAAA,SAAAO,GAAAZ,EAAAC,QAAAW,EAAAd,sDChFA,SAAAO,GAAA,YACAA,GAAA,SAAAP,GAqCA,QAAAiE,KACA,MAAA,mBAAAC,UACA,qBAAAC,OAAA3C,UAAA4C,SAAAC,KAAAH,SAGA,QAAAI,KACA,MAAA,mBAAAC,mBAAAA,kBACA,mBAAAC,yBAAAA,uBAGA,QAAAC,GAAAF,GAMA,QAAA5C,KACA,GAAAsB,GAAAyB,CACAA,GAAA,OACAzB,IARA,GAAAyB,GACAC,EAAAC,SAAAC,eAAA,IACAC,EAAA,GAAAP,GAAA5C,EACAmD,GAAAC,QAAAJ,GAAAK,eAAA,GAQA,IAAAnD,GAAA,CACA,OAAA,UAAAoB,GACAyB,EAAAzB,EACA0B,EAAAM,KAAApD,GAAA,GAtDA,GAAAqD,GACAC,EAAA,mBAAAC,aAAAA,WAGAnD,EAAA,SAAAgB,EAAAoC,GAAA,MAAAD,YAAAnC,EAAAoC,IACAC,EAAA,SAAAC,GAAA,MAAAC,cAAAD,IACA5E,EAAA,SAAAsC,GAAA,MAAAkC,GAAAlC,EAAA,GAGA,IAAAgB,IACAtD,EAAA,SAAAsC,GAAA,MAAAiB,SAAAuB,SAAAxC,QAEA,IAAAiC,EAAAZ,IACA3D,EAAA8D,EAAAS,OAEA,KAAAC,EAAA,CACA,GAAAO,GAAA1F,EACA2F,EAAAD,EAAA,QACAzD,GAAA,SAAAgB,EAAAoC,GAAA,MAAAM,GAAA1D,SAAAoD,EAAApC,IACAqC,EAAAK,EAAAC,YACAjF,EAAAgF,EAAAE,WAAAF,EAAAG,aAGA,OACA7D,SAAAA,EACAqD,WAAAA,EACA3E,KAAAA,MAgCA,kBAAAJ,IAAAA,EAAAM,IAAAN,EAAA,SAAAO,GAAAZ,EAAAC,QAAAW,EAAAd,8BCpEA,SAAAO,GAAA,YACAA,GAAA,WAeA,QAAAmC,GAAAX,GACA,GAAAgE,GAAA,gBAAAhE,IAAA,OAAAA,IAAAA,EAAAiE,OAAAjE,EAAAkE,SAAAlE,EAAAiE,OAAAjE,EAAAkE,QAAAjD,EAAAjB,EACA,OAAAA,aAAAmE,OAAAH,EAAAA,EAAA,6BASA,QAAA/C,GAAA8B,GACA,GAAAiB,GAAAI,OAAArB,EAIA,OAHA,oBAAAiB,GAAA,mBAAAK,QACAL,EAAAM,EAAAvB,EAAAiB,IAEAA,EAUA,QAAAM,GAAAnD,EAAAoD,GACA,IACA,MAAAF,MAAAG,UAAArD,GACA,MAAAnB,GACA,MAAAuE,IA3CA,OACA5D,YAAAA,EACAM,aAAAA,EACAqD,aAAAA,MA6CA,kBAAA9F,IAAAA,EAAAM,IAAAN,EAAA,SAAAO,GAAAZ,EAAAC,QAAAW,+BCnDA,SAAAP,GAAA,YACAA,GAAA,WAEA,MAAA,UAAAiG,GAkBA,QAAAnG,GAAAoG,EAAAC,GACA3F,KAAA4F,SAAAF,IAAAG,EAAAF,EAAAG,EAAAJ,GAQA,QAAAI,GAAAJ,GAgBA,QAAAK,GAAA5D,GACAwD,EAAAK,QAAA7D,GAOA,QAAA8D,GAAAC,GACAP,EAAAQ,OAAAD,GAQA,QAAAE,GAAAjE,GACAwD,EAAAU,OAAAlE,GAjCA,GAAAwD,GAAA,GAAAW,EAEA,KACAZ,EAAAK,EAAAE,EAAAG,GACA,MAAApF,GACAiF,EAAAjF,GAGA,MAAA2E,GA4CA,QAAAK,GAAA7D,GACA,MAAAoE,GAAApE,GAAAA,EACA,GAAA7C,GAAAuG,EAAA,GAAAW,GAAAC,EAAAtE,KAQA,QAAAgE,GAAAhE,GACA,MAAA,IAAA7C,GAAAuG,EAAA,GAAAW,GAAA,GAAAE,GAAAvE,KAOA,QAAAwE,KACA,MAAAC,IAQA,QAAAC,KACA,MAAA,IAAAvH,GAAAuG,EAAA,GAAAS,IAoDA,QAAAQ,GAAAC,EAAAzH,GACA,GAAA0H,GAAA,GAAAV,GAAAS,EAAAE,SAAAF,EAAAG,OAAAC,QACA,OAAA,IAAA7H,GAAAuG,EAAAmB,GAgBA,QAAAI,GAAAC,GACA,MAAAC,GAAAC,EAAA,KAAAF,GAUA,QAAAG,GAAAtF,EAAAmF,GACA,MAAAC,GAAAG,EAAAvF,EAAAmF,GAGA,QAAAC,GAAAI,EAAAxF,EAAAmF,GAwBA,QAAAM,GAAA7G,EAAAqB,EAAAuD,GACAA,EAAAkC,UACAC,EAAAR,EAAAS,EAAAhH,EAAA4G,EAAAxF,EAAAC,EAAArB,GAAA4E,GAIA,QAAAoC,GAAAhH,EAAAqB,EAAAuD,GACAqC,EAAAjH,GAAAqB,EACA,MAAA6F,GACAtC,EAAAuC,OAAA,GAAAC,GAAAH,IA1BA,IAAA,GAAA5F,GANAwD,EAAA,kBAAAzD,GAAAyF,EAAAG,EAEApC,EAAA,GAAAY,GACA0B,EAAAX,EAAA9E,SAAA,EACAwF,EAAA,GAAAI,OAAAH,GAEAlH,EAAA,EAAAA,EAAAuG,EAAA9E,SAAAmD,EAAAkC,WAAA9G,EACAqB,EAAAkF,EAAAvG,GAEA,SAAAqB,GAAArB,IAAAuG,GAKAQ,EAAAR,EAAA1B,EAAA7E,EAAAqB,EAAAuD,KAJAsC,CAWA,OAJA,KAAAA,GACAtC,EAAAuC,OAAA,GAAAC,GAAAH,IAGA,GAAAzI,GAAAuG,EAAAH,GAgBA,QAAAmC,GAAAR,EAAA1B,EAAA7E,EAAAqB,EAAAuD,GACA,GAAA0C,EAAAjG,GAAA,CACA,GAAAkG,GAAAC,EAAAnG,GACA6C,EAAAqD,EAAAE,OAEA,KAAAvD,EACAqD,EAAAG,KAAA7C,EAAA7E,EAAA,OAAA4E,GACAV,EAAA,EACAW,EAAA7E,EAAAuH,EAAAzG,MAAA8D,IAEAA,EAAAuC,OAAAI,GACAI,EAAApB,EAAAvG,EAAA,EAAAuH,QAGA1C,GAAA7E,EAAAqB,EAAAuD,GAKA,QAAA+C,GAAApB,EAAAqB,EAAA/C,GACA,IAAA,GAAA7E,GAAA4H,EAAA5H,EAAAuG,EAAA9E,SAAAzB,EACA6H,EAAAlC,EAAAY,EAAAvG,IAAA6E,GAIA,QAAAgD,GAAAN,EAAA1C,GACA,GAAA0C,IAAA1C,EAAA,CAIA,GAAAX,GAAAqD,EAAAE,OACA,KAAAvD,EACAqD,EAAAO,MAAAP,EAAA,OAAAA,EAAAQ,WACA,EAAA7D,GACAqD,EAAAQ,aAkBA,QAAAC,GAAAzB,GACA,MAAA,gBAAAA,IAAA,OAAAA,EACAlB,EAAA,GAAA4C,WAAA,kCAKA,IAAA1B,EAAA9E,OAAAoE,IACA,IAAAU,EAAA9E,OAAAyD,EAAAqB,EAAA,IACA2B,EAAA3B,GAGA,QAAA2B,GAAA3B,GACA,GACAvG,GAAAqB,EAAAkG,EADA3C,EAAA,GAAAY,EAEA,KAAAxF,EAAA,EAAAA,EAAAuG,EAAA9E,SAAAzB,EAEA,GADAqB,EAAAkF,EAAAvG,GACA,SAAAqB,GAAArB,IAAAuG,GAAA,CAKA,GADAgB,EAAA5B,EAAAtE,GACA,IAAAkG,EAAAE,QAAA,CACA7C,EAAAuC,OAAAI,GACAI,EAAApB,EAAAvG,EAAA,EAAAuH,EACA,OAEAA,EAAAO,MAAAlD,EAAAA,EAAAM,QAAAN,EAAAS,QAGA,MAAA,IAAA7G,GAAAuG,EAAAH,GAWA,QAAAe,GAAAtE,GACA,MAAAoE,GAAApE,GACAA,EAAAyD,SAAAsB,OAEAkB,EAAAjG,GAAA8G,EAAA9G,GAAA,GAAA+F,GAAA/F,GASA,QAAAmG,GAAAnG,GACA,MAAAoE,GAAApE,GAAAA,EAAAyD,SAAAsB,OAAA+B,EAAA9G,GAQA,QAAA8G,GAAA9G,GACA,IACA,GAAA+G,GAAA/G,EAAAgH,IACA,OAAA,kBAAAD,GACA,GAAAE,GAAAF,EAAA/G,GACA,GAAA+F,GAAA/F,GACA,MAAAnB,GACA,MAAA,IAAA0F,GAAA1F,IAQA,QAAA6E,MAmDA,QAAAwD,MAcA,QAAA/C,GAAAW,EAAAqC,GACAhK,EAAAiK,cAAAvJ,KAAAsJ,GAEAtJ,KAAAwJ,UAAA,OACAxJ,KAAAiH,SAAAA,EACAjH,KAAA2F,QAAA,OACA3F,KAAA4H,UAAA,EAsGA,QAAApB,GAAAb,GACA3F,KAAA2F,QAAAA,EAuBA,QAAAyD,GAAAD,EAAAM,GACAnD,EAAAhD,KAAAtD,MACAoC,EAAA1B,QAAA,GAAAgJ,GAAAP,EAAAM,EAAAzJ,OAUA,QAAAkI,GAAA/F,GACA7C,EAAAiK,cAAAvJ,MACAA,KAAA4B,MAAAO,EAsBA,QAAAuE,GAAAvE,GACA7C,EAAAiK,cAAAvJ,MAEAA,KAAA0B,KAAAiI,EACA3J,KAAA4B,MAAAO,EACAnC,KAAAsB,SAAA,EACAtB,KAAAuB,UAAA,EAEAvB,KAAA4J,UAoCA,QAAAC,GAAA9G,EAAAoE,GACAnH,KAAA+C,UAAAA,EACA/C,KAAAmH,QAAAA,EAWA,QAAA2C,GAAA/G,GACA/C,KAAA+C,UAAAA,EA0BA,QAAAgH,KACA,MAAA,IAAArD,GAAA,GAAAqC,WAAA,kBASA,QAAAiB,GAAAC,EAAAtE,GACA3F,KAAAiK,aAAAA,EACAjK,KAAA2F,QAAAA,EAWA,QAAAuE,GAAAtI,EAAA+D,GACA3F,KAAA2F,QAAAA,EACA3F,KAAA4B,MAAAA,EAsBA,QAAA8H,GAAAP,EAAAM,EAAA/D,GACA1F,KAAAmK,MAAAhB,EACAnJ,KAAAyJ,SAAAA,EACAzJ,KAAA0F,SAAAA,EAYA,QAAA0E,GAAAjB,EAAAM,EAAAzD,EAAAG,EAAAE,GACA,IACA8C,EAAA7F,KAAAmG,EAAAzD,EAAAG,EAAAE,GACA,MAAArF,GACAmF,EAAAnF,IAQA,QAAAqJ,GAAAnI,EAAAoI,EAAAC,EAAAC,GACAxK,KAAAkC,EAAAA,EAAAlC,KAAAsK,EAAAA,EAAAtK,KAAAuK,EAAAA,EAAAvK,KAAAwK,GAAAA,EACAxK,KAAA0F,SAAA+E,EACAzK,KAAAiH,SAAAjH,KAqBA,QAAAuG,GAAApE,GACA,MAAAA,aAAA7C,GASA,QAAA8I,GAAAjG,GACA,OAAA,gBAAAA,IAAA,kBAAAA,KAAA,OAAAA,EAGA,QAAAuI,GAAAxI,EAAAmG,EAAApB,EAAA0D,GACA,MAAA,kBAAAzI,GACAyI,EAAA1C,OAAAI,IAGA/I,EAAAsL,aAAAvC,GACAwC,EAAA3I,EAAAmG,EAAAzG,MAAAqF,EAAA0D,OACArL,GAAAwL,eAGA,QAAAC,GAAA7I,EAAAC,EAAAkG,EAAApB,EAAA0D,GACA,MAAA,kBAAAzI,GACAyI,EAAA1C,OAAAI,IAGA/I,EAAAsL,aAAAvC,GACA2C,EAAA9I,EAAAC,EAAAkG,EAAAzG,MAAAqF,EAAA0D,OACArL,GAAAwL,eAMA,QAAAG,GAAA/I,EAAAC,EAAAkG,EAAApB,EAAA0D,GACA,MAAA,kBAAAzI,GACAyI,EAAAtE,OAAAlE,IAGA7C,EAAAsL,aAAAvC,GACA6C,EAAAhJ,EAAAC,EAAA8E,EAAA0D,OACArL,GAAAwL,eAGA,QAAArD,GAAAvF,EAAAiJ,EAAAC,GACA,IACA,MAAAlJ,GAAAiJ,EAAAC,GACA,MAAApK,GACA,MAAAmF,GAAAnF,IAQA,QAAA6J,GAAA3I,EAAAC,EAAAkJ,EAAAV,GACA,IACAA,EAAA1C,OAAAxB,EAAAvE,EAAAoB,KAAA+H,EAAAlJ,KACA,MAAAnB,GACA2J,EAAA1C,OAAA,GAAAvB,GAAA1F,KAOA,QAAAgK,GAAA9I,EAAAC,EAAAmJ,EAAAD,EAAAV,GACA,IACAzI,EAAAoB,KAAA+H,EAAAlJ,EAAAmJ,EAAAX,GACA,MAAA3J,GACA2J,EAAA1C,OAAA,GAAAvB,GAAA1F,KAQA,QAAAkK,GAAAhJ,EAAAC,EAAAkJ,EAAAV,GACA,IACAA,EAAAtE,OAAAnE,EAAAoB,KAAA+H,EAAAlJ,IACA,MAAAnB,GACA2J,EAAAtE,OAAArF,IAIA,QAAAuK,GAAAC,EAAAC,GACAA,EAAAhL,UAAAiL,EAAAF,EAAA/K,WACAgL,EAAAhL,UAAAkL,YAAAF,EAGA,QAAAlE,GAAApF,EAAAmJ,GACA,MAAAA,GAGA,QAAArK,MAEA,QAAA2K,KACA,GAAA,kBAAAC,aACA,IACA,GAAAC,GAAA,GAAAD,aAAA,qBACA,OAAAC,aAAAD,aACA,MAAAE,IAEA,OAAA,EAGA,QAAAC,KACA,GAAA,mBAAAnI,WAAA,kBAAAA,UAAAoI,YACA,IAEA,GAAAH,GAAAjI,SAAAoI,YAAA,cAEA,OADAH,GAAAI,gBAAA,aAAA,GAAA,OACA,EACA,MAAAH,IAEA,OAAA,EAGA,QAAAI,KAEA,MAAA,mBAAAhJ,UAAA,OAAAA,SACA,kBAAAA,SAAAiJ,KAKA,SAAAC,EAAAtJ,GACA,MAAA,uBAAAsJ,EACAlJ,QAAAiJ,KAAAC,EAAAtJ,EAAAnB,MAAAmB,GACAI,QAAAiJ,KAAAC,EAAAtJ,IAEA,mBAAAxD,OAAAqM,IACA,SAAArM,EAAAsM,GACA,MAAA,UAAAQ,EAAAtJ,GACA,GAAA+I,GAAA,GAAAD,GAAAQ,GACAC,QACApG,OAAAnD,EAAAnB,MACA2K,IAAAxJ,GAEAyJ,SAAA,EACAC,YAAA,GAGA,QAAAlN,EAAAmN,cAAAZ,KAEAvM,KAAAsM,aACA,mBAAAtM,OAAAyM,IACA,SAAAzM,EAAAsE,GACA,MAAA,UAAAwI,EAAAtJ,GACA,GAAA+I,GAAAjI,EAAAoI,YAAA,cAMA,OALAH,GAAAI,gBAAAG,GAAA,GAAA,GACAnG,OAAAnD,EAAAnB,MACA2K,IAAAxJ,KAGAxD,EAAAmN,cAAAZ,KAEAvM,KAAAsE,UAGA5C,EA36BA,GAAAmB,GAAAqD,EAAA5F,UACA8M,EAAAR,IAEAT,EAAAtI,OAAAwJ,QACA,SAAAC,GACA,QAAApB,MAEA,MADAA,GAAAhL,UAAAoM,EACA,GAAApB,GA0DAnM,GAAA0G,QAAAA,EACA1G,EAAA6G,OAAAA,EACA7G,EAAAqH,MAAAA,EAEArH,EAAAwN,OAAAjG,EACAvH,EAAAsG,SAAAa,EAmDAnH,EAAAmB,UAAA0I,KAAA,SAAA4D,EAAAC,EAAAC,GACA,GAAAlG,GAAA/G,KAAA4F,SACA2C,EAAAxB,EAAAG,OAAAqB,OAEA,IAAA,kBAAAwE,IAAAxE,EAAA,GACA,kBAAAyE,IAAA,EAAAzE,EAEA,MAAA,IAAAvI,MAAA2L,YAAA9F,EAAAkB,EAGA,IAAAmG,GAAAlN,KAAAmN,SACAnG,EAAAkG,EAAAtH,QAIA,OAFAmB,GAAAqG,MAAApG,EAAAD,EAAAE,SAAA8F,EAAAC,EAAAC,GAEAC,GASA5N,EAAAmB,UAAA,SAAA,SAAAuM,GACA,MAAAhN,MAAAmJ,KAAA,OAAA6D,IAQA1N,EAAAmB,UAAA0M,OAAA,WACA,MAAArG,GAAA9G,KAAA4F,SAAA5F,KAAA2L,cAUArM,EAAA8H,IAAAA,EACA9H,EAAAwJ,KAAAA,EACAxJ,EAAA+N,UAAA7F,EAgFAlI,EAAAgO,gBAAA7E,EAkHA5C,EAAApF,UAAA8M,KACA1H,EAAApF,UAAAwH,OACApC,EAAApF,UAAA4F,OACAR,EAAApF,UAAA+M,KACA3H,EAAApF,UAAAoI,UACAhD,EAAApF,UAAAmJ,QACA3I,EAEA4E,EAAApF,UAAAgN,OAAA,EAEA5H,EAAApF,UAAA8H,MAAA,WACA,MAAAvI,MAAAyN,QAQA5H,EAAApF,UAAAyG,KAAA,WAEA,IADA,GAAAmB,GAAArI,KACA,SAAAqI,EAAA1C,SACA0C,EAAAA,EAAA1C,OAEA,OAAA0C,IAGAxC,EAAApF,UAAA2M,MAAA,SAAA5C,EAAAvD,EAAAyG,EAAAC,EAAAC,GACA5N,KAAAuN,MACA7H,SAAA8E,EACAvD,SAAAA,EACAyG,UAAAA,EACAC,SAAAA,EACAC,SAAAA,KAIA/H,EAAApF,UAAAmI,MAAA,SAAA3B,EAAAyG,EAAAC,EAAAC,GACA5N,KAAAoN,MAAA3C,EAAAxD,EAAAyG,EAAAC,EAAAC,IAGA/H,EAAApF,UAAA+H,KAAA,SAAAtG,EAAAoI,EAAAC,EAAAC,GACAxK,KAAAuN,KAAA,GAAAlD,GAAAnI,EAAAoI,EAAAC,EAAAC,KASAe,EAAA1F,EAAAwD,GAEAA,EAAA5I,UAAAwH,OAAA,SAAAI,GACAA,EAAAmF,OAGA,IAAA/C,GAAA,GAAApB,EAeAkC,GAAA1F,EAAAS,GAEAA,EAAA7F,UAAAgN,OAAA,EAEAnH,EAAA7F,UAAAuF,QAAA,SAAA7D,GACAnC,KAAAiI,OAAAxB,EAAAtE,KAGAmE,EAAA7F,UAAA0F,OAAA,SAAAhE,GACAnC,KAAA4H,UAIA5H,KAAAiI,OAAA,GAAAvB,GAAAvE,KAGAmE,EAAA7F,UAAAyG,KAAA,WACA,IAAAlH,KAAA4H,SACA,MAAA5H,KAKA,KAFA,GAAAqI,GAAArI,KAEA,SAAAqI,EAAA1C,SAEA,GADA0C,EAAAA,EAAA1C,QACA0C,IAAArI,KACA,MAAAA,MAAA2F,QAAAoE,GAIA,OAAA1B,IAGA/B,EAAA7F,UAAAG,IAAA,WACA,GAAAiN,GAAA7N,KAAAwJ,UACA7D,EAAA3F,KAAA2F,OACA3F,MAAA2F,QAAA3F,KAAA2F,QAAAuB,OACAlH,KAAAwJ,UAAA,MAEA,KAAA,GAAA1I,GAAA,EAAAA,EAAA+M,EAAAtL,SAAAzB,EACA6E,EAAA4H,KAAAM,EAAA/M,KAIAwF,EAAA7F,UAAAwH,OAAA,SAAAtC,GACA3F,KAAA4H,WAIA5H,KAAA4H,UAAA,EACA5H,KAAA2F,QAAAA,EACA,SAAA3F,KAAAwJ,WACApH,EAAA1B,QAAAV,MAGA,SAAAA,KAAAmH,SACAxB,EAAAiE,QAAA5J,KAAAmH,WAIAb,EAAA7F,UAAA8M,KAAA,SAAAtD,GACAjK,KAAA4H,SACAxF,EAAA1B,QAAA,GAAAsJ,GAAAC,EAAAjK,KAAA2F,UAEA,SAAA3F,KAAAwJ,UACAxJ,KAAAwJ,WAAAS,GAEAjK,KAAAwJ,UAAAhI,KAAAyI,IAQA3D,EAAA7F,UAAA4F,OAAA,SAAAlE,GACAnC,KAAA4H,UACAxF,EAAA1B,QAAA,GAAAwJ,GAAA/H,EAAAnC,QAIAsG,EAAA7F,UAAA+M,KAAA,SAAArG,GACA,GAAAoD,GAAA,mBAAApD,GAAAnH,KAAAmH,QAAAA,CACAnH,MAAA4H,UAAA5H,KAAA2F,QAAAuB,OAAAsG,KAAAjD,IAGAjE,EAAA7F,UAAAmJ,QAAA,SAAAzC,GACAnH,KAAA4H,UAAA5H,KAAA2F,QAAAuB,OAAA0C,QAAAzC,IAGAb,EAAA7F,UAAAoI,UAAA,WACA7I,KAAA4H,UAAA5H,KAAA2F,QAAAuB,OAAA2B,aAYA0C,EAAA1F,EAAAW,GAEAA,EAAA/F,UAAA8M,KAAA,SAAAtD,GACA7H,EAAA1B,QAAA,GAAAsJ,GAAAC,EAAAjK,QAGAwG,EAAA/F,UAAAmJ,QAAA,SAAAzC,GACAnH,KAAAkH,OAAA0C,QAAAzC,IAGAX,EAAA/F,UAAAoI,UAAA,WACA7I,KAAAkH,OAAA2B,aAcA0C,EAAAjF,EAAA8C,GAYAmC,EAAA1F,EAAAqC,GAEAA,EAAAzH,UAAAgN,OAAA,EAEAvF,EAAAzH,UAAA+H,KAAA,SAAAtG,EAAAoI,EAAAC,EAAAC,GACAO,EAAA7I,EAAAoI,EAAAtK,KAAAuK,EAAAC,IAGAtC,EAAAzH,UAAA8M,KAAA,SAAAO,GACApD,EAAAoD,EAAAJ,UAAA1N,KAAA8N,EAAA7G,SAAA6G,EAAApI,UAGA,IAAAiE,GAAA,CAkBA4B,GAAA1F,EAAAa,GAEAA,EAAAjG,UAAAgN,OAAA,GAEA/G,EAAAjG,UAAA+H,KAAA,SAAAtG,EAAAoI,EAAAC,EAAAC,GACAA,EAAAvC,OAAAjI,OAGA0G,EAAAjG,UAAA8M,KAAA,SAAAO,GACA,kBAAAA,GAAAH,UACA3N,KAAA6I,YAEA6B,EAAAoD,EAAAH,SAAA3N,KAAA8N,EAAA7G,SAAA6G,EAAApI,WAGAgB,EAAAjG,UAAAmJ,QAAA,SAAAzC,GACA/E,EAAAvB,WAAA,GAAAgJ,GAAA7J,KAAAmH,KAGAT,EAAAjG,UAAAoI,UAAA,WACA7I,KAAAsB,UAGAtB,KAAAsB,SAAA,EACAc,EAAAvB,WAAA,GAAAiJ,GAAA9J,SAGA0G,EAAAjG,UAAA+M,KAAA,SAAArG,GACAnH,KAAAuB,UAAA,EACAoL,EAAA,qBAAA3M,MACAV,EAAA2D,iBAAAjD,KAAA,SAAAmH,EAAAnH,KAAAmH,QAAAA,IAQA0C,EAAApJ,UAAAG,IAAA,WACAZ,KAAA+C,UAAAzB,SAAAtB,KAAA+C,UAAAxB,WACAvB,KAAA+C,UAAAxB,UAAA,EACAoL,EAAA,qBAAA3M,KAAA+C,YACAzD,EAAAwD,gCAAA9C,KAAA+C,UAAA/C,KAAAmH,WAQA2C,EAAArJ,UAAAG,IAAA,WACAZ,KAAA+C,UAAAxB,WACAoL,EAAA,mBAAA3M,KAAA+C,YACAzD,EAAA0D,uCAAAhD,KAAA+C,aAOAzD,EAAAiK,cACAjK,EAAAsL,aACAtL,EAAAwL,YACAxL,EAAAwD,gCACAxD,EAAA0D,uCACA1D,EAAA2D,iBACAhC,CAIA,IAAA8M,IAAA,GAAAlI,GACAe,GAAA,GAAAtH,GAAAuG,EAAAkI,GA4QA,OA3PA/D,GAAAvJ,UAAAG,IAAA,WACAZ,KAAA2F,QAAAuB,OAAAqG,KAAAvN,KAAAiK,eAYAC,EAAAzJ,UAAAG,IAAA,WACA,GAAAiN,GAAA7N,KAAA2F,QAAA6D,SACA,IAAA,SAAAqE,EAIA,IAAA,GAAAtD,GAAAzJ,EAAA,EAAAA,EAAA+M,EAAAtL,SAAAzB,EACAyJ,EAAAsD,EAAA/M,GACAmK,EAAAV,EAAAqD,SAAA5N,KAAA4B,MAAA5B,KAAA2F,QAAA4E,EAAAtD,SAAAsD,EAAA7E,WAiBAgE,EAAAjJ,UAAAG,IAAA,WAIA,QAAAoN,GAAA7L,GAAAkG,EAAArC,QAAA7D,GACA,QAAA8L,GAAA9L,GAAAkG,EAAAlC,OAAAhE,GACA,QAAA+L,GAAA/L,GAAAkG,EAAAhC,OAAAlE,GALA,GAAAkG,GAAArI,KAAA0F,QACA0E,GAAApK,KAAAmK,MAAAnK,KAAAyJ,SAAAuE,EAAAC,EAAAC,IAyBA7D,EAAA5J,UAAAiN,UAAA,SAAAvL,GACAnC,KAAAkC,EAAAoB,KAAAtD,KAAAuK,EAAAvK,KAAAsK,EAAAnI,EAAAnC,KAAAwK,KAGAH,EAAA5J,UAAAkN,SAAA,SAAAxL,GACAnC,KAAAwK,GAAArE,OAAAhE,IAGAkI,EAAA5J,UAAAmN,SAAA,SAAAzL,GACAnC,KAAAwK,GAAAnE,OAAAlE,IAiLA7C,MAGA,kBAAAE,IAAAA,EAAAM,IAAAN,EAAA,SAAAO,GAAAZ,EAAAC,QAAAW;Ab17BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AChFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACtFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACzEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACxDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "generated.js", "sourceRoot": "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08", "sourcesContent": ["/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n/**\n * ES6 global Promise shim\n */\nvar unhandledRejections = require('../lib/decorators/unhandledRejection');\nvar PromiseConstructor = unhandledRejections(require('../lib/Promise'));\n\nmodule.exports = typeof global != 'undefined' ? (global.Promise = PromiseConstructor)\n\t           : typeof self   != 'undefined' ? (self.Promise   = PromiseConstructor)\n\t           : PromiseConstructor;\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function (require) {\n\n\tvar makePromise = require('./makePromise');\n\tvar Scheduler = require('./Scheduler');\n\tvar async = require('./env').asap;\n\n\treturn makePromise({\n\t\tscheduler: new Scheduler(async)\n\t});\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\t// Credit to Twisol (https://github.com/Twisol) for suggesting\n\t// this type of extensible queue + trampoline approach for next-tick conflation.\n\n\t/**\n\t * Async task scheduler\n\t * @param {function} async function to schedule a single async function\n\t * @constructor\n\t */\n\tfunction Scheduler(async) {\n\t\tthis._async = async;\n\t\tthis._running = false;\n\n\t\tthis._queue = this;\n\t\tthis._queueLen = 0;\n\t\tthis._afterQueue = {};\n\t\tthis._afterQueueLen = 0;\n\n\t\tvar self = this;\n\t\tthis.drain = function() {\n\t\t\tself._drain();\n\t\t};\n\t}\n\n\t/**\n\t * Enqueue a task\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.enqueue = function(task) {\n\t\tthis._queue[this._queueLen++] = task;\n\t\tthis.run();\n\t};\n\n\t/**\n\t * Enqueue a task to run after the main task queue\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.afterQueue = function(task) {\n\t\tthis._afterQueue[this._afterQueueLen++] = task;\n\t\tthis.run();\n\t};\n\n\tScheduler.prototype.run = function() {\n\t\tif (!this._running) {\n\t\t\tthis._running = true;\n\t\t\tthis._async(this.drain);\n\t\t}\n\t};\n\n\t/**\n\t * Drain the handler queue entirely, and then the after queue\n\t */\n\tScheduler.prototype._drain = function() {\n\t\tvar i = 0;\n\t\tfor (; i < this._queueLen; ++i) {\n\t\t\tthis._queue[i].run();\n\t\t\tthis._queue[i] = void 0;\n\t\t}\n\n\t\tthis._queueLen = 0;\n\t\tthis._running = false;\n\n\t\tfor (i = 0; i < this._afterQueueLen; ++i) {\n\t\t\tthis._afterQueue[i].run();\n\t\t\tthis._afterQueue[i] = void 0;\n\t\t}\n\n\t\tthis._afterQueueLen = 0;\n\t};\n\n\treturn Scheduler;\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar setTimer = require('../env').setTimer;\n\tvar format = require('../format');\n\n\treturn function unhandledRejection(Promise) {\n\n\t\tvar logError = noop;\n\t\tvar logInfo = noop;\n\t\tvar localConsole;\n\n\t\tif(typeof console !== 'undefined') {\n\t\t\t// Alias console to prevent things like uglify's drop_console option from\n\t\t\t// removing console.log/error. Unhandled rejections fall into the same\n\t\t\t// category as uncaught exceptions, and build tools shouldn't silence them.\n\t\t\tlocalConsole = console;\n\t\t\tlogError = typeof localConsole.error !== 'undefined'\n\t\t\t\t? function (e) { localConsole.error(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\n\t\t\tlogInfo = typeof localConsole.info !== 'undefined'\n\t\t\t\t? function (e) { localConsole.info(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\t\t}\n\n\t\tPromise.onPotentiallyUnhandledRejection = function(rejection) {\n\t\t\tenqueue(report, rejection);\n\t\t};\n\n\t\tPromise.onPotentiallyUnhandledRejectionHandled = function(rejection) {\n\t\t\tenqueue(unreport, rejection);\n\t\t};\n\n\t\tPromise.onFatalRejection = function(rejection) {\n\t\t\tenqueue(throwit, rejection.value);\n\t\t};\n\n\t\tvar tasks = [];\n\t\tvar reported = [];\n\t\tvar running = null;\n\n\t\tfunction report(r) {\n\t\t\tif(!r.handled) {\n\t\t\t\treported.push(r);\n\t\t\t\tlogError('Potentially unhandled rejection [' + r.id + '] ' + format.formatError(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction unreport(r) {\n\t\t\tvar i = reported.indexOf(r);\n\t\t\tif(i >= 0) {\n\t\t\t\treported.splice(i, 1);\n\t\t\t\tlogInfo('Handled previous rejection [' + r.id + '] ' + format.formatObject(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction enqueue(f, x) {\n\t\t\ttasks.push(f, x);\n\t\t\tif(running === null) {\n\t\t\t\trunning = setTimer(flush, 0);\n\t\t\t}\n\t\t}\n\n\t\tfunction flush() {\n\t\t\trunning = null;\n\t\t\twhile(tasks.length > 0) {\n\t\t\t\ttasks.shift()(tasks.shift());\n\t\t\t}\n\t\t}\n\n\t\treturn Promise;\n\t};\n\n\tfunction throwit(e) {\n\t\tthrow e;\n\t}\n\n\tfunction noop() {}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n/*global process,document,setTimeout,clearTimeout,MutationObserver,WebKitMutationObserver*/\n(function(define) { 'use strict';\ndefine(function(require) {\n\t/*jshint maxcomplexity:6*/\n\n\t// Sniff \"best\" async scheduling option\n\t// Prefer process.nextTick or MutationObserver, then check for\n\t// setTimeout, and finally vertx, since its the only env that doesn't\n\t// have setTimeout\n\n\tvar MutationObs;\n\tvar capturedSetTimeout = typeof setTimeout !== 'undefined' && setTimeout;\n\n\t// Default env\n\tvar setTimer = function(f, ms) { return setTimeout(f, ms); };\n\tvar clearTimer = function(t) { return clearTimeout(t); };\n\tvar asap = function (f) { return capturedSetTimeout(f, 0); };\n\n\t// Detect specific env\n\tif (isNode()) { // Node\n\t\tasap = function (f) { return process.nextTick(f); };\n\n\t} else if (MutationObs = hasMutationObserver()) { // Modern browser\n\t\tasap = initMutationObserver(MutationObs);\n\n\t} else if (!capturedSetTimeout) { // vert.x\n\t\tvar vertxRequire = require;\n\t\tvar vertx = vertxRequire('vertx');\n\t\tsetTimer = function (f, ms) { return vertx.setTimer(ms, f); };\n\t\tclearTimer = vertx.cancelTimer;\n\t\tasap = vertx.runOnLoop || vertx.runOnContext;\n\t}\n\n\treturn {\n\t\tsetTimer: setTimer,\n\t\tclearTimer: clearTimer,\n\t\tasap: asap\n\t};\n\n\tfunction isNode () {\n\t\treturn typeof process !== 'undefined' &&\n\t\t\tObject.prototype.toString.call(process) === '[object process]';\n\t}\n\n\tfunction hasMutationObserver () {\n\t    return (typeof MutationObserver !== 'undefined' && MutationObserver) ||\n\t\t\t(typeof WebKitMutationObserver !== 'undefined' && WebKitMutationObserver);\n\t}\n\n\tfunction initMutationObserver(MutationObserver) {\n\t\tvar scheduled;\n\t\tvar node = document.createTextNode('');\n\t\tvar o = new MutationObserver(run);\n\t\to.observe(node, { characterData: true });\n\n\t\tfunction run() {\n\t\t\tvar f = scheduled;\n\t\t\tscheduled = void 0;\n\t\t\tf();\n\t\t}\n\n\t\tvar i = 0;\n\t\treturn function (f) {\n\t\t\tscheduled = f;\n\t\t\tnode.data = (i ^= 1);\n\t\t};\n\t}\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn {\n\t\tformatError: formatError,\n\t\tformatObject: formatObject,\n\t\ttryStringify: tryStringify\n\t};\n\n\t/**\n\t * Format an error into a string.  If e is an Error and has a stack property,\n\t * it's returned.  Otherwise, e is formatted using formatObject, with a\n\t * warning added about e not being a proper Error.\n\t * @param {*} e\n\t * @returns {String} formatted string, suitable for output to developers\n\t */\n\tfunction formatError(e) {\n\t\tvar s = typeof e === 'object' && e !== null && (e.stack || e.message) ? e.stack || e.message : formatObject(e);\n\t\treturn e instanceof Error ? s : s + ' (WARNING: non-Error used)';\n\t}\n\n\t/**\n\t * Format an object, detecting \"plain\" objects and running them through\n\t * JSON.stringify if possible.\n\t * @param {Object} o\n\t * @returns {string}\n\t */\n\tfunction formatObject(o) {\n\t\tvar s = String(o);\n\t\tif(s === '[object Object]' && typeof JSON !== 'undefined') {\n\t\t\ts = tryStringify(o, s);\n\t\t}\n\t\treturn s;\n\t}\n\n\t/**\n\t * Try to return the result of JSON.stringify(x).  If that fails, return\n\t * defaultValue\n\t * @param {*} x\n\t * @param {*} defaultValue\n\t * @returns {String|*} JSON.stringify(x) or defaultValue\n\t */\n\tfunction tryStringify(x, defaultValue) {\n\t\ttry {\n\t\t\treturn JSON.stringify(x);\n\t\t} catch(e) {\n\t\t\treturn defaultValue;\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function makePromise(environment) {\n\n\t\tvar tasks = environment.scheduler;\n\t\tvar emitRejection = initEmitRejection();\n\n\t\tvar objectCreate = Object.create ||\n\t\t\tfunction(proto) {\n\t\t\t\tfunction Child() {}\n\t\t\t\tChild.prototype = proto;\n\t\t\t\treturn new Child();\n\t\t\t};\n\n\t\t/**\n\t\t * Create a promise whose fate is determined by resolver\n\t\t * @constructor\n\t\t * @returns {Promise} promise\n\t\t * @name Promise\n\t\t */\n\t\tfunction Promise(resolver, handler) {\n\t\t\tthis._handler = resolver === Handler ? handler : init(resolver);\n\t\t}\n\n\t\t/**\n\t\t * Run the supplied resolver\n\t\t * @param resolver\n\t\t * @returns {Pending}\n\t\t */\n\t\tfunction init(resolver) {\n\t\t\tvar handler = new Pending();\n\n\t\t\ttry {\n\t\t\t\tresolver(promiseResolve, promiseReject, promiseNotify);\n\t\t\t} catch (e) {\n\t\t\t\tpromiseReject(e);\n\t\t\t}\n\n\t\t\treturn handler;\n\n\t\t\t/**\n\t\t\t * Transition from pre-resolution state to post-resolution state, notifying\n\t\t\t * all listeners of the ultimate fulfillment or rejection\n\t\t\t * @param {*} x resolution value\n\t\t\t */\n\t\t\tfunction promiseResolve (x) {\n\t\t\t\thandler.resolve(x);\n\t\t\t}\n\t\t\t/**\n\t\t\t * Reject this promise with reason, which will be used verbatim\n\t\t\t * @param {Error|*} reason rejection reason, strongly suggested\n\t\t\t *   to be an Error type\n\t\t\t */\n\t\t\tfunction promiseReject (reason) {\n\t\t\t\thandler.reject(reason);\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * @deprecated\n\t\t\t * Issue a progress event, notifying all progress listeners\n\t\t\t * @param {*} x progress event payload to pass to all listeners\n\t\t\t */\n\t\t\tfunction promiseNotify (x) {\n\t\t\t\thandler.notify(x);\n\t\t\t}\n\t\t}\n\n\t\t// Creation\n\n\t\tPromise.resolve = resolve;\n\t\tPromise.reject = reject;\n\t\tPromise.never = never;\n\n\t\tPromise._defer = defer;\n\t\tPromise._handler = getHandler;\n\n\t\t/**\n\t\t * Returns a trusted promise. If x is already a trusted promise, it is\n\t\t * returned, otherwise returns a new trusted Promise which follows x.\n\t\t * @param  {*} x\n\t\t * @return {Promise} promise\n\t\t */\n\t\tfunction resolve(x) {\n\t\t\treturn isPromise(x) ? x\n\t\t\t\t: new Promise(Handler, new Async(getHandler(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a reject promise with x as its reason (x is used verbatim)\n\t\t * @param {*} x\n\t\t * @returns {Promise} rejected promise\n\t\t */\n\t\tfunction reject(x) {\n\t\t\treturn new Promise(Handler, new Async(new Rejected(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a promise that remains pending forever\n\t\t * @returns {Promise} forever-pending promise.\n\t\t */\n\t\tfunction never() {\n\t\t\treturn foreverPendingPromise; // Should be frozen\n\t\t}\n\n\t\t/**\n\t\t * Creates an internal {promise, resolver} pair\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tfunction defer() {\n\t\t\treturn new Promise(Handler, new Pending());\n\t\t}\n\n\t\t// Transformation and flow control\n\n\t\t/**\n\t\t * Transform this promise's fulfillment value, returning a new Promise\n\t\t * for the transformed result.  If the promise cannot be fulfilled, onRejected\n\t\t * is called with the reason.  onProgress *may* be called with updates toward\n\t\t * this promise's fulfillment.\n\t\t * @param {function=} onFulfilled fulfillment handler\n\t\t * @param {function=} onRejected rejection handler\n\t\t * @param {function=} onProgress @deprecated progress handler\n\t\t * @return {Promise} new promise\n\t\t */\n\t\tPromise.prototype.then = function(onFulfilled, onRejected, onProgress) {\n\t\t\tvar parent = this._handler;\n\t\t\tvar state = parent.join().state();\n\n\t\t\tif ((typeof onFulfilled !== 'function' && state > 0) ||\n\t\t\t\t(typeof onRejected !== 'function' && state < 0)) {\n\t\t\t\t// Short circuit: value will not change, simply share handler\n\t\t\t\treturn new this.constructor(Handler, parent);\n\t\t\t}\n\n\t\t\tvar p = this._beget();\n\t\t\tvar child = p._handler;\n\n\t\t\tparent.chain(child, parent.receiver, onFulfilled, onRejected, onProgress);\n\n\t\t\treturn p;\n\t\t};\n\n\t\t/**\n\t\t * If this promise cannot be fulfilled due to an error, call onRejected to\n\t\t * handle the error. Shortcut for .then(undefined, onRejected)\n\t\t * @param {function?} onRejected\n\t\t * @return {Promise}\n\t\t */\n\t\tPromise.prototype['catch'] = function(onRejected) {\n\t\t\treturn this.then(void 0, onRejected);\n\t\t};\n\n\t\t/**\n\t\t * Creates a new, pending promise of the same type as this promise\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype._beget = function() {\n\t\t\treturn begetFrom(this._handler, this.constructor);\n\t\t};\n\n\t\tfunction begetFrom(parent, Promise) {\n\t\t\tvar child = new Pending(parent.receiver, parent.join().context);\n\t\t\treturn new Promise(Handler, child);\n\t\t}\n\n\t\t// Array combinators\n\n\t\tPromise.all = all;\n\t\tPromise.race = race;\n\t\tPromise._traverse = traverse;\n\n\t\t/**\n\t\t * Return a promise that will fulfill when all promises in the\n\t\t * input array have fulfilled, or will reject when one of the\n\t\t * promises rejects.\n\t\t * @param {array} promises array of promises\n\t\t * @returns {Promise} promise for array of fulfillment values\n\t\t */\n\t\tfunction all(promises) {\n\t\t\treturn traverseWith(snd, null, promises);\n\t\t}\n\n\t\t/**\n\t\t * Array<Promise<X>> -> Promise<Array<f(X)>>\n\t\t * @private\n\t\t * @param {function} f function to apply to each promise's value\n\t\t * @param {Array} promises array of promises\n\t\t * @returns {Promise} promise for transformed values\n\t\t */\n\t\tfunction traverse(f, promises) {\n\t\t\treturn traverseWith(tryCatch2, f, promises);\n\t\t}\n\n\t\tfunction traverseWith(tryMap, f, promises) {\n\t\t\tvar handler = typeof f === 'function' ? mapAt : settleAt;\n\n\t\t\tvar resolver = new Pending();\n\t\t\tvar pending = promises.length >>> 0;\n\t\t\tvar results = new Array(pending);\n\n\t\t\tfor (var i = 0, x; i < promises.length && !resolver.resolved; ++i) {\n\t\t\t\tx = promises[i];\n\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\t--pending;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\ttraverseAt(promises, handler, i, x, resolver);\n\t\t\t}\n\n\t\t\tif(pending === 0) {\n\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t}\n\n\t\t\treturn new Promise(Handler, resolver);\n\n\t\t\tfunction mapAt(i, x, resolver) {\n\t\t\t\tif(!resolver.resolved) {\n\t\t\t\t\ttraverseAt(promises, settleAt, i, tryMap(f, x, i), resolver);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction settleAt(i, x, resolver) {\n\t\t\t\tresults[i] = x;\n\t\t\t\tif(--pending === 0) {\n\t\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfunction traverseAt(promises, handler, i, x, resolver) {\n\t\t\tif (maybeThenable(x)) {\n\t\t\t\tvar h = getHandlerMaybeThenable(x);\n\t\t\t\tvar s = h.state();\n\n\t\t\t\tif (s === 0) {\n\t\t\t\t\th.fold(handler, i, void 0, resolver);\n\t\t\t\t} else if (s > 0) {\n\t\t\t\t\thandler(i, h.value, resolver);\n\t\t\t\t} else {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thandler(i, x, resolver);\n\t\t\t}\n\t\t}\n\n\t\tPromise._visitRemaining = visitRemaining;\n\t\tfunction visitRemaining(promises, start, handler) {\n\t\t\tfor(var i=start; i<promises.length; ++i) {\n\t\t\t\tmarkAsHandled(getHandler(promises[i]), handler);\n\t\t\t}\n\t\t}\n\n\t\tfunction markAsHandled(h, handler) {\n\t\t\tif(h === handler) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar s = h.state();\n\t\t\tif(s === 0) {\n\t\t\t\th.visit(h, void 0, h._unreport);\n\t\t\t} else if(s < 0) {\n\t\t\t\th._unreport();\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fulfill-reject competitive race. Return a promise that will settle\n\t\t * to the same state as the earliest input promise to settle.\n\t\t *\n\t\t * WARNING: The ES6 Promise spec requires that race()ing an empty array\n\t\t * must return a promise that is pending forever.  This implementation\n\t\t * returns a singleton forever-pending promise, the same singleton that is\n\t\t * returned by Promise.never(), thus can be checked with ===\n\t\t *\n\t\t * @param {array} promises array of promises to race\n\t\t * @returns {Promise} if input is non-empty, a promise that will settle\n\t\t * to the same outcome as the earliest input promise to settle. if empty\n\t\t * is empty, returns a promise that will never settle.\n\t\t */\n\t\tfunction race(promises) {\n\t\t\tif(typeof promises !== 'object' || promises === null) {\n\t\t\t\treturn reject(new TypeError('non-iterable passed to race()'));\n\t\t\t}\n\n\t\t\t// Sigh, race([]) is untestable unless we return *something*\n\t\t\t// that is recognizable without calling .then() on it.\n\t\t\treturn promises.length === 0 ? never()\n\t\t\t\t : promises.length === 1 ? resolve(promises[0])\n\t\t\t\t : runRace(promises);\n\t\t}\n\n\t\tfunction runRace(promises) {\n\t\t\tvar resolver = new Pending();\n\t\t\tvar i, x, h;\n\t\t\tfor(i=0; i<promises.length; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\th = getHandler(x);\n\t\t\t\tif(h.state() !== 0) {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\th.visit(resolver, resolver.resolve, resolver.reject);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn new Promise(Handler, resolver);\n\t\t}\n\n\t\t// Promise internals\n\t\t// Below this, everything is @private\n\n\t\t/**\n\t\t * Get an appropriate handler for x, without checking for cycles\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandler(x) {\n\t\t\tif(isPromise(x)) {\n\t\t\t\treturn x._handler.join();\n\t\t\t}\n\t\t\treturn maybeThenable(x) ? getHandlerUntrusted(x) : new Fulfilled(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for thenable x.\n\t\t * NOTE: You must only call this if maybeThenable(x) == true\n\t\t * @param {object|function|Promise} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerMaybeThenable(x) {\n\t\t\treturn isPromise(x) ? x._handler.join() : getHandlerUntrusted(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for potentially untrusted thenable x\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerUntrusted(x) {\n\t\t\ttry {\n\t\t\t\tvar untrustedThen = x.then;\n\t\t\t\treturn typeof untrustedThen === 'function'\n\t\t\t\t\t? new Thenable(untrustedThen, x)\n\t\t\t\t\t: new Fulfilled(x);\n\t\t\t} catch(e) {\n\t\t\t\treturn new Rejected(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Handler for a promise that is pending forever\n\t\t * @constructor\n\t\t */\n\t\tfunction Handler() {}\n\n\t\tHandler.prototype.when\n\t\t\t= Handler.prototype.become\n\t\t\t= Handler.prototype.notify // deprecated\n\t\t\t= Handler.prototype.fail\n\t\t\t= Handler.prototype._unreport\n\t\t\t= Handler.prototype._report\n\t\t\t= noop;\n\n\t\tHandler.prototype._state = 0;\n\n\t\tHandler.prototype.state = function() {\n\t\t\treturn this._state;\n\t\t};\n\n\t\t/**\n\t\t * Recursively collapse handler chain to find the handler\n\t\t * nearest to the fully resolved value.\n\t\t * @returns {object} handler nearest the fully resolved value\n\t\t */\n\t\tHandler.prototype.join = function() {\n\t\t\tvar h = this;\n\t\t\twhile(h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t}\n\t\t\treturn h;\n\t\t};\n\n\t\tHandler.prototype.chain = function(to, receiver, fulfilled, rejected, progress) {\n\t\t\tthis.when({\n\t\t\t\tresolver: to,\n\t\t\t\treceiver: receiver,\n\t\t\t\tfulfilled: fulfilled,\n\t\t\t\trejected: rejected,\n\t\t\t\tprogress: progress\n\t\t\t});\n\t\t};\n\n\t\tHandler.prototype.visit = function(receiver, fulfilled, rejected, progress) {\n\t\t\tthis.chain(failIfRejected, receiver, fulfilled, rejected, progress);\n\t\t};\n\n\t\tHandler.prototype.fold = function(f, z, c, to) {\n\t\t\tthis.when(new Fold(f, z, c, to));\n\t\t};\n\n\t\t/**\n\t\t * Handler that invokes fail() on any handler it becomes\n\t\t * @constructor\n\t\t */\n\t\tfunction FailIfRejected() {}\n\n\t\tinherit(Handler, FailIfRejected);\n\n\t\tFailIfRejected.prototype.become = function(h) {\n\t\t\th.fail();\n\t\t};\n\n\t\tvar failIfRejected = new FailIfRejected();\n\n\t\t/**\n\t\t * Handler that manages a queue of consumers waiting on a pending promise\n\t\t * @constructor\n\t\t */\n\t\tfunction Pending(receiver, inheritedContext) {\n\t\t\tPromise.createContext(this, inheritedContext);\n\n\t\t\tthis.consumers = void 0;\n\t\t\tthis.receiver = receiver;\n\t\t\tthis.handler = void 0;\n\t\t\tthis.resolved = false;\n\t\t}\n\n\t\tinherit(Handler, Pending);\n\n\t\tPending.prototype._state = 0;\n\n\t\tPending.prototype.resolve = function(x) {\n\t\t\tthis.become(getHandler(x));\n\t\t};\n\n\t\tPending.prototype.reject = function(x) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.become(new Rejected(x));\n\t\t};\n\n\t\tPending.prototype.join = function() {\n\t\t\tif (!this.resolved) {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\tvar h = this;\n\n\t\t\twhile (h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t\tif (h === this) {\n\t\t\t\t\treturn this.handler = cycle();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn h;\n\t\t};\n\n\t\tPending.prototype.run = function() {\n\t\t\tvar q = this.consumers;\n\t\t\tvar handler = this.handler;\n\t\t\tthis.handler = this.handler.join();\n\t\t\tthis.consumers = void 0;\n\n\t\t\tfor (var i = 0; i < q.length; ++i) {\n\t\t\t\thandler.when(q[i]);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.become = function(handler) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.resolved = true;\n\t\t\tthis.handler = handler;\n\t\t\tif(this.consumers !== void 0) {\n\t\t\t\ttasks.enqueue(this);\n\t\t\t}\n\n\t\t\tif(this.context !== void 0) {\n\t\t\t\thandler._report(this.context);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.when = function(continuation) {\n\t\t\tif(this.resolved) {\n\t\t\t\ttasks.enqueue(new ContinuationTask(continuation, this.handler));\n\t\t\t} else {\n\t\t\t\tif(this.consumers === void 0) {\n\t\t\t\t\tthis.consumers = [continuation];\n\t\t\t\t} else {\n\t\t\t\t\tthis.consumers.push(continuation);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tPending.prototype.notify = function(x) {\n\t\t\tif(!this.resolved) {\n\t\t\t\ttasks.enqueue(new ProgressTask(x, this));\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.fail = function(context) {\n\t\t\tvar c = typeof context === 'undefined' ? this.context : context;\n\t\t\tthis.resolved && this.handler.join().fail(c);\n\t\t};\n\n\t\tPending.prototype._report = function(context) {\n\t\t\tthis.resolved && this.handler.join()._report(context);\n\t\t};\n\n\t\tPending.prototype._unreport = function() {\n\t\t\tthis.resolved && this.handler.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Wrap another handler and force it into a future stack\n\t\t * @param {object} handler\n\t\t * @constructor\n\t\t */\n\t\tfunction Async(handler) {\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tinherit(Handler, Async);\n\n\t\tAsync.prototype.when = function(continuation) {\n\t\t\ttasks.enqueue(new ContinuationTask(continuation, this));\n\t\t};\n\n\t\tAsync.prototype._report = function(context) {\n\t\t\tthis.join()._report(context);\n\t\t};\n\n\t\tAsync.prototype._unreport = function() {\n\t\t\tthis.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Handler that wraps an untrusted thenable and assimilates it in a future stack\n\t\t * @param {function} then\n\t\t * @param {{then: function}} thenable\n\t\t * @constructor\n\t\t */\n\t\tfunction Thenable(then, thenable) {\n\t\t\tPending.call(this);\n\t\t\ttasks.enqueue(new AssimilateTask(then, thenable, this));\n\t\t}\n\n\t\tinherit(Pending, Thenable);\n\n\t\t/**\n\t\t * Handler for a fulfilled promise\n\t\t * @param {*} x fulfillment value\n\t\t * @constructor\n\t\t */\n\t\tfunction Fulfilled(x) {\n\t\t\tPromise.createContext(this);\n\t\t\tthis.value = x;\n\t\t}\n\n\t\tinherit(Handler, Fulfilled);\n\n\t\tFulfilled.prototype._state = 1;\n\n\t\tFulfilled.prototype.fold = function(f, z, c, to) {\n\t\t\trunContinuation3(f, z, this, c, to);\n\t\t};\n\n\t\tFulfilled.prototype.when = function(cont) {\n\t\t\trunContinuation1(cont.fulfilled, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tvar errorId = 0;\n\n\t\t/**\n\t\t * Handler for a rejected promise\n\t\t * @param {*} x rejection reason\n\t\t * @constructor\n\t\t */\n\t\tfunction Rejected(x) {\n\t\t\tPromise.createContext(this);\n\n\t\t\tthis.id = ++errorId;\n\t\t\tthis.value = x;\n\t\t\tthis.handled = false;\n\t\t\tthis.reported = false;\n\n\t\t\tthis._report();\n\t\t}\n\n\t\tinherit(Handler, Rejected);\n\n\t\tRejected.prototype._state = -1;\n\n\t\tRejected.prototype.fold = function(f, z, c, to) {\n\t\t\tto.become(this);\n\t\t};\n\n\t\tRejected.prototype.when = function(cont) {\n\t\t\tif(typeof cont.rejected === 'function') {\n\t\t\t\tthis._unreport();\n\t\t\t}\n\t\t\trunContinuation1(cont.rejected, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tRejected.prototype._report = function(context) {\n\t\t\ttasks.afterQueue(new ReportTask(this, context));\n\t\t};\n\n\t\tRejected.prototype._unreport = function() {\n\t\t\tif(this.handled) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.handled = true;\n\t\t\ttasks.afterQueue(new UnreportTask(this));\n\t\t};\n\n\t\tRejected.prototype.fail = function(context) {\n\t\t\tthis.reported = true;\n\t\t\temitRejection('unhandledRejection', this);\n\t\t\tPromise.onFatalRejection(this, context === void 0 ? this.context : context);\n\t\t};\n\n\t\tfunction ReportTask(rejection, context) {\n\t\t\tthis.rejection = rejection;\n\t\t\tthis.context = context;\n\t\t}\n\n\t\tReportTask.prototype.run = function() {\n\t\t\tif(!this.rejection.handled && !this.rejection.reported) {\n\t\t\t\tthis.rejection.reported = true;\n\t\t\t\temitRejection('unhandledRejection', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejection(this.rejection, this.context);\n\t\t\t}\n\t\t};\n\n\t\tfunction UnreportTask(rejection) {\n\t\t\tthis.rejection = rejection;\n\t\t}\n\n\t\tUnreportTask.prototype.run = function() {\n\t\t\tif(this.rejection.reported) {\n\t\t\t\temitRejection('rejectionHandled', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejectionHandled(this.rejection);\n\t\t\t}\n\t\t};\n\n\t\t// Unhandled rejection hooks\n\t\t// By default, everything is a noop\n\n\t\tPromise.createContext\n\t\t\t= Promise.enterContext\n\t\t\t= Promise.exitContext\n\t\t\t= Promise.onPotentiallyUnhandledRejection\n\t\t\t= Promise.onPotentiallyUnhandledRejectionHandled\n\t\t\t= Promise.onFatalRejection\n\t\t\t= noop;\n\n\t\t// Errors and singletons\n\n\t\tvar foreverPendingHandler = new Handler();\n\t\tvar foreverPendingPromise = new Promise(Handler, foreverPendingHandler);\n\n\t\tfunction cycle() {\n\t\t\treturn new Rejected(new TypeError('Promise cycle'));\n\t\t}\n\n\t\t// Task runners\n\n\t\t/**\n\t\t * Run a single consumer\n\t\t * @constructor\n\t\t */\n\t\tfunction ContinuationTask(continuation, handler) {\n\t\t\tthis.continuation = continuation;\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tContinuationTask.prototype.run = function() {\n\t\t\tthis.handler.join().when(this.continuation);\n\t\t};\n\n\t\t/**\n\t\t * Run a queue of progress handlers\n\t\t * @constructor\n\t\t */\n\t\tfunction ProgressTask(value, handler) {\n\t\t\tthis.handler = handler;\n\t\t\tthis.value = value;\n\t\t}\n\n\t\tProgressTask.prototype.run = function() {\n\t\t\tvar q = this.handler.consumers;\n\t\t\tif(q === void 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tfor (var c, i = 0; i < q.length; ++i) {\n\t\t\t\tc = q[i];\n\t\t\t\trunNotify(c.progress, this.value, this.handler, c.receiver, c.resolver);\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Assimilate a thenable, sending it's value to resolver\n\t\t * @param {function} then\n\t\t * @param {object|function} thenable\n\t\t * @param {object} resolver\n\t\t * @constructor\n\t\t */\n\t\tfunction AssimilateTask(then, thenable, resolver) {\n\t\t\tthis._then = then;\n\t\t\tthis.thenable = thenable;\n\t\t\tthis.resolver = resolver;\n\t\t}\n\n\t\tAssimilateTask.prototype.run = function() {\n\t\t\tvar h = this.resolver;\n\t\t\ttryAssimilate(this._then, this.thenable, _resolve, _reject, _notify);\n\n\t\t\tfunction _resolve(x) { h.resolve(x); }\n\t\t\tfunction _reject(x)  { h.reject(x); }\n\t\t\tfunction _notify(x)  { h.notify(x); }\n\t\t};\n\n\t\tfunction tryAssimilate(then, thenable, resolve, reject, notify) {\n\t\t\ttry {\n\t\t\t\tthen.call(thenable, resolve, reject, notify);\n\t\t\t} catch (e) {\n\t\t\t\treject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fold a handler value with z\n\t\t * @constructor\n\t\t */\n\t\tfunction Fold(f, z, c, to) {\n\t\t\tthis.f = f; this.z = z; this.c = c; this.to = to;\n\t\t\tthis.resolver = failIfRejected;\n\t\t\tthis.receiver = this;\n\t\t}\n\n\t\tFold.prototype.fulfilled = function(x) {\n\t\t\tthis.f.call(this.c, this.z, x, this.to);\n\t\t};\n\n\t\tFold.prototype.rejected = function(x) {\n\t\t\tthis.to.reject(x);\n\t\t};\n\n\t\tFold.prototype.progress = function(x) {\n\t\t\tthis.to.notify(x);\n\t\t};\n\n\t\t// Other helpers\n\n\t\t/**\n\t\t * @param {*} x\n\t\t * @returns {boolean} true iff x is a trusted Promise\n\t\t */\n\t\tfunction isPromise(x) {\n\t\t\treturn x instanceof Promise;\n\t\t}\n\n\t\t/**\n\t\t * Test just enough to rule out primitives, in order to take faster\n\t\t * paths in some code\n\t\t * @param {*} x\n\t\t * @returns {boolean} false iff x is guaranteed *not* to be a thenable\n\t\t */\n\t\tfunction maybeThenable(x) {\n\t\t\treturn (typeof x === 'object' || typeof x === 'function') && x !== null;\n\t\t}\n\n\t\tfunction runContinuation1(f, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject(f, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction runContinuation3(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject3(f, x, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tfunction runNotify(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.notify(x);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReturn(f, x, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction tryCatch2(f, a, b) {\n\t\t\ttry {\n\t\t\t\treturn f(a, b);\n\t\t\t} catch(e) {\n\t\t\t\treturn reject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Return f.call(thisArg, x), or if it throws return a rejected promise for\n\t\t * the thrown exception\n\t\t */\n\t\tfunction tryCatchReject(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.become(getHandler(f.call(thisArg, x)));\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Same as above, but includes the extra argument parameter.\n\t\t */\n\t\tfunction tryCatchReject3(f, x, y, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tf.call(thisArg, x, y, next);\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t * Return f.call(thisArg, x), or if it throws, *return* the exception\n\t\t */\n\t\tfunction tryCatchReturn(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.notify(f.call(thisArg, x));\n\t\t\t} catch(e) {\n\t\t\t\tnext.notify(e);\n\t\t\t}\n\t\t}\n\n\t\tfunction inherit(Parent, Child) {\n\t\t\tChild.prototype = objectCreate(Parent.prototype);\n\t\t\tChild.prototype.constructor = Child;\n\t\t}\n\n\t\tfunction snd(x, y) {\n\t\t\treturn y;\n\t\t}\n\n\t\tfunction noop() {}\n\n\t\tfunction hasCustomEvent() {\n\t\t\tif(typeof CustomEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\tvar ev = new CustomEvent('unhandledRejection');\n\t\t\t\t\treturn ev instanceof CustomEvent;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction hasInternetExplorerCustomEvent() {\n\t\t\tif(typeof document !== 'undefined' && typeof document.createEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\t// Try to create one event to make sure it's supported\n\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\tev.initCustomEvent('eventType', false, true, {});\n\t\t\t\t\treturn true;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction initEmitRejection() {\n\t\t\t/*global process, self, CustomEvent*/\n\t\t\tif(typeof process !== 'undefined' && process !== null\n\t\t\t\t&& typeof process.emit === 'function') {\n\t\t\t\t// Returning falsy here means to call the default\n\t\t\t\t// onPotentiallyUnhandledRejection API.  This is safe even in\n\t\t\t\t// browserify since process.emit always returns falsy in browserify:\n\t\t\t\t// https://github.com/defunctzombie/node-process/blob/master/browser.js#L40-L46\n\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\treturn type === 'unhandledRejection'\n\t\t\t\t\t\t? process.emit(type, rejection.value, rejection)\n\t\t\t\t\t\t: process.emit(type, rejection);\n\t\t\t\t};\n\t\t\t} else if(typeof self !== 'undefined' && hasCustomEvent()) {\n\t\t\t\treturn (function (self, CustomEvent) {\n\t\t\t\t\treturn function (type, rejection) {\n\t\t\t\t\t\tvar ev = new CustomEvent(type, {\n\t\t\t\t\t\t\tdetail: {\n\t\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tbubbles: false,\n\t\t\t\t\t\t\tcancelable: true\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, CustomEvent));\n\t\t\t} else if(typeof self !== 'undefined' && hasInternetExplorerCustomEvent()) {\n\t\t\t\treturn (function(self, document) {\n\t\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\t\tev.initCustomEvent(type, false, true, {\n\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, document));\n\t\t\t}\n\n\t\t\treturn noop;\n\t\t}\n\n\t\treturn Promise;\n\t};\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n/**\n * ES6 global Promise shim\n */\nvar unhandledRejections = require('../lib/decorators/unhandledRejection');\nvar PromiseConstructor = unhandledRejections(require('../lib/Promise'));\n\nmodule.exports = typeof global != 'undefined' ? (global.Promise = PromiseConstructor)\n\t           : typeof self   != 'undefined' ? (self.Promise   = PromiseConstructor)\n\t           : PromiseConstructor;\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function (require) {\n\n\tvar makePromise = require('./makePromise');\n\tvar Scheduler = require('./Scheduler');\n\tvar async = require('./env').asap;\n\n\treturn makePromise({\n\t\tscheduler: new Scheduler(async)\n\t});\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\t// Credit to Twisol (https://github.com/Twisol) for suggesting\n\t// this type of extensible queue + trampoline approach for next-tick conflation.\n\n\t/**\n\t * Async task scheduler\n\t * @param {function} async function to schedule a single async function\n\t * @constructor\n\t */\n\tfunction Scheduler(async) {\n\t\tthis._async = async;\n\t\tthis._running = false;\n\n\t\tthis._queue = this;\n\t\tthis._queueLen = 0;\n\t\tthis._afterQueue = {};\n\t\tthis._afterQueueLen = 0;\n\n\t\tvar self = this;\n\t\tthis.drain = function() {\n\t\t\tself._drain();\n\t\t};\n\t}\n\n\t/**\n\t * Enqueue a task\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.enqueue = function(task) {\n\t\tthis._queue[this._queueLen++] = task;\n\t\tthis.run();\n\t};\n\n\t/**\n\t * Enqueue a task to run after the main task queue\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.afterQueue = function(task) {\n\t\tthis._afterQueue[this._afterQueueLen++] = task;\n\t\tthis.run();\n\t};\n\n\tScheduler.prototype.run = function() {\n\t\tif (!this._running) {\n\t\t\tthis._running = true;\n\t\t\tthis._async(this.drain);\n\t\t}\n\t};\n\n\t/**\n\t * Drain the handler queue entirely, and then the after queue\n\t */\n\tScheduler.prototype._drain = function() {\n\t\tvar i = 0;\n\t\tfor (; i < this._queueLen; ++i) {\n\t\t\tthis._queue[i].run();\n\t\t\tthis._queue[i] = void 0;\n\t\t}\n\n\t\tthis._queueLen = 0;\n\t\tthis._running = false;\n\n\t\tfor (i = 0; i < this._afterQueueLen; ++i) {\n\t\t\tthis._afterQueue[i].run();\n\t\t\tthis._afterQueue[i] = void 0;\n\t\t}\n\n\t\tthis._afterQueueLen = 0;\n\t};\n\n\treturn Scheduler;\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar setTimer = require('../env').setTimer;\n\tvar format = require('../format');\n\n\treturn function unhandledRejection(Promise) {\n\n\t\tvar logError = noop;\n\t\tvar logInfo = noop;\n\t\tvar localConsole;\n\n\t\tif(typeof console !== 'undefined') {\n\t\t\t// Alias console to prevent things like uglify's drop_console option from\n\t\t\t// removing console.log/error. Unhandled rejections fall into the same\n\t\t\t// category as uncaught exceptions, and build tools shouldn't silence them.\n\t\t\tlocalConsole = console;\n\t\t\tlogError = typeof localConsole.error !== 'undefined'\n\t\t\t\t? function (e) { localConsole.error(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\n\t\t\tlogInfo = typeof localConsole.info !== 'undefined'\n\t\t\t\t? function (e) { localConsole.info(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\t\t}\n\n\t\tPromise.onPotentiallyUnhandledRejection = function(rejection) {\n\t\t\tenqueue(report, rejection);\n\t\t};\n\n\t\tPromise.onPotentiallyUnhandledRejectionHandled = function(rejection) {\n\t\t\tenqueue(unreport, rejection);\n\t\t};\n\n\t\tPromise.onFatalRejection = function(rejection) {\n\t\t\tenqueue(throwit, rejection.value);\n\t\t};\n\n\t\tvar tasks = [];\n\t\tvar reported = [];\n\t\tvar running = null;\n\n\t\tfunction report(r) {\n\t\t\tif(!r.handled) {\n\t\t\t\treported.push(r);\n\t\t\t\tlogError('Potentially unhandled rejection [' + r.id + '] ' + format.formatError(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction unreport(r) {\n\t\t\tvar i = reported.indexOf(r);\n\t\t\tif(i >= 0) {\n\t\t\t\treported.splice(i, 1);\n\t\t\t\tlogInfo('Handled previous rejection [' + r.id + '] ' + format.formatObject(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction enqueue(f, x) {\n\t\t\ttasks.push(f, x);\n\t\t\tif(running === null) {\n\t\t\t\trunning = setTimer(flush, 0);\n\t\t\t}\n\t\t}\n\n\t\tfunction flush() {\n\t\t\trunning = null;\n\t\t\twhile(tasks.length > 0) {\n\t\t\t\ttasks.shift()(tasks.shift());\n\t\t\t}\n\t\t}\n\n\t\treturn Promise;\n\t};\n\n\tfunction throwit(e) {\n\t\tthrow e;\n\t}\n\n\tfunction noop() {}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n/*global process,document,setTimeout,clearTimeout,MutationObserver,WebKitMutationObserver*/\n(function(define) { 'use strict';\ndefine(function(require) {\n\t/*jshint maxcomplexity:6*/\n\n\t// Sniff \"best\" async scheduling option\n\t// Prefer process.nextTick or MutationObserver, then check for\n\t// setTimeout, and finally vertx, since its the only env that doesn't\n\t// have setTimeout\n\n\tvar MutationObs;\n\tvar capturedSetTimeout = typeof setTimeout !== 'undefined' && setTimeout;\n\n\t// Default env\n\tvar setTimer = function(f, ms) { return setTimeout(f, ms); };\n\tvar clearTimer = function(t) { return clearTimeout(t); };\n\tvar asap = function (f) { return capturedSetTimeout(f, 0); };\n\n\t// Detect specific env\n\tif (isNode()) { // Node\n\t\tasap = function (f) { return process.nextTick(f); };\n\n\t} else if (MutationObs = hasMutationObserver()) { // Modern browser\n\t\tasap = initMutationObserver(MutationObs);\n\n\t} else if (!capturedSetTimeout) { // vert.x\n\t\tvar vertxRequire = require;\n\t\tvar vertx = vertxRequire('vertx');\n\t\tsetTimer = function (f, ms) { return vertx.setTimer(ms, f); };\n\t\tclearTimer = vertx.cancelTimer;\n\t\tasap = vertx.runOnLoop || vertx.runOnContext;\n\t}\n\n\treturn {\n\t\tsetTimer: setTimer,\n\t\tclearTimer: clearTimer,\n\t\tasap: asap\n\t};\n\n\tfunction isNode () {\n\t\treturn typeof process !== 'undefined' &&\n\t\t\tObject.prototype.toString.call(process) === '[object process]';\n\t}\n\n\tfunction hasMutationObserver () {\n\t    return (typeof MutationObserver !== 'undefined' && MutationObserver) ||\n\t\t\t(typeof WebKitMutationObserver !== 'undefined' && WebKitMutationObserver);\n\t}\n\n\tfunction initMutationObserver(MutationObserver) {\n\t\tvar scheduled;\n\t\tvar node = document.createTextNode('');\n\t\tvar o = new MutationObserver(run);\n\t\to.observe(node, { characterData: true });\n\n\t\tfunction run() {\n\t\t\tvar f = scheduled;\n\t\t\tscheduled = void 0;\n\t\t\tf();\n\t\t}\n\n\t\tvar i = 0;\n\t\treturn function (f) {\n\t\t\tscheduled = f;\n\t\t\tnode.data = (i ^= 1);\n\t\t};\n\t}\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn {\n\t\tformatError: formatError,\n\t\tformatObject: formatObject,\n\t\ttryStringify: tryStringify\n\t};\n\n\t/**\n\t * Format an error into a string.  If e is an Error and has a stack property,\n\t * it's returned.  Otherwise, e is formatted using formatObject, with a\n\t * warning added about e not being a proper Error.\n\t * @param {*} e\n\t * @returns {String} formatted string, suitable for output to developers\n\t */\n\tfunction formatError(e) {\n\t\tvar s = typeof e === 'object' && e !== null && (e.stack || e.message) ? e.stack || e.message : formatObject(e);\n\t\treturn e instanceof Error ? s : s + ' (WARNING: non-Error used)';\n\t}\n\n\t/**\n\t * Format an object, detecting \"plain\" objects and running them through\n\t * JSON.stringify if possible.\n\t * @param {Object} o\n\t * @returns {string}\n\t */\n\tfunction formatObject(o) {\n\t\tvar s = String(o);\n\t\tif(s === '[object Object]' && typeof JSON !== 'undefined') {\n\t\t\ts = tryStringify(o, s);\n\t\t}\n\t\treturn s;\n\t}\n\n\t/**\n\t * Try to return the result of JSON.stringify(x).  If that fails, return\n\t * defaultValue\n\t * @param {*} x\n\t * @param {*} defaultValue\n\t * @returns {String|*} JSON.stringify(x) or defaultValue\n\t */\n\tfunction tryStringify(x, defaultValue) {\n\t\ttry {\n\t\t\treturn JSON.stringify(x);\n\t\t} catch(e) {\n\t\t\treturn defaultValue;\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function makePromise(environment) {\n\n\t\tvar tasks = environment.scheduler;\n\t\tvar emitRejection = initEmitRejection();\n\n\t\tvar objectCreate = Object.create ||\n\t\t\tfunction(proto) {\n\t\t\t\tfunction Child() {}\n\t\t\t\tChild.prototype = proto;\n\t\t\t\treturn new Child();\n\t\t\t};\n\n\t\t/**\n\t\t * Create a promise whose fate is determined by resolver\n\t\t * @constructor\n\t\t * @returns {Promise} promise\n\t\t * @name Promise\n\t\t */\n\t\tfunction Promise(resolver, handler) {\n\t\t\tthis._handler = resolver === Handler ? handler : init(resolver);\n\t\t}\n\n\t\t/**\n\t\t * Run the supplied resolver\n\t\t * @param resolver\n\t\t * @returns {Pending}\n\t\t */\n\t\tfunction init(resolver) {\n\t\t\tvar handler = new Pending();\n\n\t\t\ttry {\n\t\t\t\tresolver(promiseResolve, promiseReject, promiseNotify);\n\t\t\t} catch (e) {\n\t\t\t\tpromiseReject(e);\n\t\t\t}\n\n\t\t\treturn handler;\n\n\t\t\t/**\n\t\t\t * Transition from pre-resolution state to post-resolution state, notifying\n\t\t\t * all listeners of the ultimate fulfillment or rejection\n\t\t\t * @param {*} x resolution value\n\t\t\t */\n\t\t\tfunction promiseResolve (x) {\n\t\t\t\thandler.resolve(x);\n\t\t\t}\n\t\t\t/**\n\t\t\t * Reject this promise with reason, which will be used verbatim\n\t\t\t * @param {Error|*} reason rejection reason, strongly suggested\n\t\t\t *   to be an Error type\n\t\t\t */\n\t\t\tfunction promiseReject (reason) {\n\t\t\t\thandler.reject(reason);\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * @deprecated\n\t\t\t * Issue a progress event, notifying all progress listeners\n\t\t\t * @param {*} x progress event payload to pass to all listeners\n\t\t\t */\n\t\t\tfunction promiseNotify (x) {\n\t\t\t\thandler.notify(x);\n\t\t\t}\n\t\t}\n\n\t\t// Creation\n\n\t\tPromise.resolve = resolve;\n\t\tPromise.reject = reject;\n\t\tPromise.never = never;\n\n\t\tPromise._defer = defer;\n\t\tPromise._handler = getHandler;\n\n\t\t/**\n\t\t * Returns a trusted promise. If x is already a trusted promise, it is\n\t\t * returned, otherwise returns a new trusted Promise which follows x.\n\t\t * @param  {*} x\n\t\t * @return {Promise} promise\n\t\t */\n\t\tfunction resolve(x) {\n\t\t\treturn isPromise(x) ? x\n\t\t\t\t: new Promise(Handler, new Async(getHandler(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a reject promise with x as its reason (x is used verbatim)\n\t\t * @param {*} x\n\t\t * @returns {Promise} rejected promise\n\t\t */\n\t\tfunction reject(x) {\n\t\t\treturn new Promise(Handler, new Async(new Rejected(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a promise that remains pending forever\n\t\t * @returns {Promise} forever-pending promise.\n\t\t */\n\t\tfunction never() {\n\t\t\treturn foreverPendingPromise; // Should be frozen\n\t\t}\n\n\t\t/**\n\t\t * Creates an internal {promise, resolver} pair\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tfunction defer() {\n\t\t\treturn new Promise(Handler, new Pending());\n\t\t}\n\n\t\t// Transformation and flow control\n\n\t\t/**\n\t\t * Transform this promise's fulfillment value, returning a new Promise\n\t\t * for the transformed result.  If the promise cannot be fulfilled, onRejected\n\t\t * is called with the reason.  onProgress *may* be called with updates toward\n\t\t * this promise's fulfillment.\n\t\t * @param {function=} onFulfilled fulfillment handler\n\t\t * @param {function=} onRejected rejection handler\n\t\t * @param {function=} onProgress @deprecated progress handler\n\t\t * @return {Promise} new promise\n\t\t */\n\t\tPromise.prototype.then = function(onFulfilled, onRejected, onProgress) {\n\t\t\tvar parent = this._handler;\n\t\t\tvar state = parent.join().state();\n\n\t\t\tif ((typeof onFulfilled !== 'function' && state > 0) ||\n\t\t\t\t(typeof onRejected !== 'function' && state < 0)) {\n\t\t\t\t// Short circuit: value will not change, simply share handler\n\t\t\t\treturn new this.constructor(Handler, parent);\n\t\t\t}\n\n\t\t\tvar p = this._beget();\n\t\t\tvar child = p._handler;\n\n\t\t\tparent.chain(child, parent.receiver, onFulfilled, onRejected, onProgress);\n\n\t\t\treturn p;\n\t\t};\n\n\t\t/**\n\t\t * If this promise cannot be fulfilled due to an error, call onRejected to\n\t\t * handle the error. Shortcut for .then(undefined, onRejected)\n\t\t * @param {function?} onRejected\n\t\t * @return {Promise}\n\t\t */\n\t\tPromise.prototype['catch'] = function(onRejected) {\n\t\t\treturn this.then(void 0, onRejected);\n\t\t};\n\n\t\t/**\n\t\t * Creates a new, pending promise of the same type as this promise\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype._beget = function() {\n\t\t\treturn begetFrom(this._handler, this.constructor);\n\t\t};\n\n\t\tfunction begetFrom(parent, Promise) {\n\t\t\tvar child = new Pending(parent.receiver, parent.join().context);\n\t\t\treturn new Promise(Handler, child);\n\t\t}\n\n\t\t// Array combinators\n\n\t\tPromise.all = all;\n\t\tPromise.race = race;\n\t\tPromise._traverse = traverse;\n\n\t\t/**\n\t\t * Return a promise that will fulfill when all promises in the\n\t\t * input array have fulfilled, or will reject when one of the\n\t\t * promises rejects.\n\t\t * @param {array} promises array of promises\n\t\t * @returns {Promise} promise for array of fulfillment values\n\t\t */\n\t\tfunction all(promises) {\n\t\t\treturn traverseWith(snd, null, promises);\n\t\t}\n\n\t\t/**\n\t\t * Array<Promise<X>> -> Promise<Array<f(X)>>\n\t\t * @private\n\t\t * @param {function} f function to apply to each promise's value\n\t\t * @param {Array} promises array of promises\n\t\t * @returns {Promise} promise for transformed values\n\t\t */\n\t\tfunction traverse(f, promises) {\n\t\t\treturn traverseWith(tryCatch2, f, promises);\n\t\t}\n\n\t\tfunction traverseWith(tryMap, f, promises) {\n\t\t\tvar handler = typeof f === 'function' ? mapAt : settleAt;\n\n\t\t\tvar resolver = new Pending();\n\t\t\tvar pending = promises.length >>> 0;\n\t\t\tvar results = new Array(pending);\n\n\t\t\tfor (var i = 0, x; i < promises.length && !resolver.resolved; ++i) {\n\t\t\t\tx = promises[i];\n\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\t--pending;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\ttraverseAt(promises, handler, i, x, resolver);\n\t\t\t}\n\n\t\t\tif(pending === 0) {\n\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t}\n\n\t\t\treturn new Promise(Handler, resolver);\n\n\t\t\tfunction mapAt(i, x, resolver) {\n\t\t\t\tif(!resolver.resolved) {\n\t\t\t\t\ttraverseAt(promises, settleAt, i, tryMap(f, x, i), resolver);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction settleAt(i, x, resolver) {\n\t\t\t\tresults[i] = x;\n\t\t\t\tif(--pending === 0) {\n\t\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfunction traverseAt(promises, handler, i, x, resolver) {\n\t\t\tif (maybeThenable(x)) {\n\t\t\t\tvar h = getHandlerMaybeThenable(x);\n\t\t\t\tvar s = h.state();\n\n\t\t\t\tif (s === 0) {\n\t\t\t\t\th.fold(handler, i, void 0, resolver);\n\t\t\t\t} else if (s > 0) {\n\t\t\t\t\thandler(i, h.value, resolver);\n\t\t\t\t} else {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thandler(i, x, resolver);\n\t\t\t}\n\t\t}\n\n\t\tPromise._visitRemaining = visitRemaining;\n\t\tfunction visitRemaining(promises, start, handler) {\n\t\t\tfor(var i=start; i<promises.length; ++i) {\n\t\t\t\tmarkAsHandled(getHandler(promises[i]), handler);\n\t\t\t}\n\t\t}\n\n\t\tfunction markAsHandled(h, handler) {\n\t\t\tif(h === handler) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar s = h.state();\n\t\t\tif(s === 0) {\n\t\t\t\th.visit(h, void 0, h._unreport);\n\t\t\t} else if(s < 0) {\n\t\t\t\th._unreport();\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fulfill-reject competitive race. Return a promise that will settle\n\t\t * to the same state as the earliest input promise to settle.\n\t\t *\n\t\t * WARNING: The ES6 Promise spec requires that race()ing an empty array\n\t\t * must return a promise that is pending forever.  This implementation\n\t\t * returns a singleton forever-pending promise, the same singleton that is\n\t\t * returned by Promise.never(), thus can be checked with ===\n\t\t *\n\t\t * @param {array} promises array of promises to race\n\t\t * @returns {Promise} if input is non-empty, a promise that will settle\n\t\t * to the same outcome as the earliest input promise to settle. if empty\n\t\t * is empty, returns a promise that will never settle.\n\t\t */\n\t\tfunction race(promises) {\n\t\t\tif(typeof promises !== 'object' || promises === null) {\n\t\t\t\treturn reject(new TypeError('non-iterable passed to race()'));\n\t\t\t}\n\n\t\t\t// Sigh, race([]) is untestable unless we return *something*\n\t\t\t// that is recognizable without calling .then() on it.\n\t\t\treturn promises.length === 0 ? never()\n\t\t\t\t : promises.length === 1 ? resolve(promises[0])\n\t\t\t\t : runRace(promises);\n\t\t}\n\n\t\tfunction runRace(promises) {\n\t\t\tvar resolver = new Pending();\n\t\t\tvar i, x, h;\n\t\t\tfor(i=0; i<promises.length; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\th = getHandler(x);\n\t\t\t\tif(h.state() !== 0) {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\th.visit(resolver, resolver.resolve, resolver.reject);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn new Promise(Handler, resolver);\n\t\t}\n\n\t\t// Promise internals\n\t\t// Below this, everything is @private\n\n\t\t/**\n\t\t * Get an appropriate handler for x, without checking for cycles\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandler(x) {\n\t\t\tif(isPromise(x)) {\n\t\t\t\treturn x._handler.join();\n\t\t\t}\n\t\t\treturn maybeThenable(x) ? getHandlerUntrusted(x) : new Fulfilled(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for thenable x.\n\t\t * NOTE: You must only call this if maybeThenable(x) == true\n\t\t * @param {object|function|Promise} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerMaybeThenable(x) {\n\t\t\treturn isPromise(x) ? x._handler.join() : getHandlerUntrusted(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for potentially untrusted thenable x\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerUntrusted(x) {\n\t\t\ttry {\n\t\t\t\tvar untrustedThen = x.then;\n\t\t\t\treturn typeof untrustedThen === 'function'\n\t\t\t\t\t? new Thenable(untrustedThen, x)\n\t\t\t\t\t: new Fulfilled(x);\n\t\t\t} catch(e) {\n\t\t\t\treturn new Rejected(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Handler for a promise that is pending forever\n\t\t * @constructor\n\t\t */\n\t\tfunction Handler() {}\n\n\t\tHandler.prototype.when\n\t\t\t= Handler.prototype.become\n\t\t\t= Handler.prototype.notify // deprecated\n\t\t\t= Handler.prototype.fail\n\t\t\t= Handler.prototype._unreport\n\t\t\t= Handler.prototype._report\n\t\t\t= noop;\n\n\t\tHandler.prototype._state = 0;\n\n\t\tHandler.prototype.state = function() {\n\t\t\treturn this._state;\n\t\t};\n\n\t\t/**\n\t\t * Recursively collapse handler chain to find the handler\n\t\t * nearest to the fully resolved value.\n\t\t * @returns {object} handler nearest the fully resolved value\n\t\t */\n\t\tHandler.prototype.join = function() {\n\t\t\tvar h = this;\n\t\t\twhile(h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t}\n\t\t\treturn h;\n\t\t};\n\n\t\tHandler.prototype.chain = function(to, receiver, fulfilled, rejected, progress) {\n\t\t\tthis.when({\n\t\t\t\tresolver: to,\n\t\t\t\treceiver: receiver,\n\t\t\t\tfulfilled: fulfilled,\n\t\t\t\trejected: rejected,\n\t\t\t\tprogress: progress\n\t\t\t});\n\t\t};\n\n\t\tHandler.prototype.visit = function(receiver, fulfilled, rejected, progress) {\n\t\t\tthis.chain(failIfRejected, receiver, fulfilled, rejected, progress);\n\t\t};\n\n\t\tHandler.prototype.fold = function(f, z, c, to) {\n\t\t\tthis.when(new Fold(f, z, c, to));\n\t\t};\n\n\t\t/**\n\t\t * Handler that invokes fail() on any handler it becomes\n\t\t * @constructor\n\t\t */\n\t\tfunction FailIfRejected() {}\n\n\t\tinherit(Handler, FailIfRejected);\n\n\t\tFailIfRejected.prototype.become = function(h) {\n\t\t\th.fail();\n\t\t};\n\n\t\tvar failIfRejected = new FailIfRejected();\n\n\t\t/**\n\t\t * Handler that manages a queue of consumers waiting on a pending promise\n\t\t * @constructor\n\t\t */\n\t\tfunction Pending(receiver, inheritedContext) {\n\t\t\tPromise.createContext(this, inheritedContext);\n\n\t\t\tthis.consumers = void 0;\n\t\t\tthis.receiver = receiver;\n\t\t\tthis.handler = void 0;\n\t\t\tthis.resolved = false;\n\t\t}\n\n\t\tinherit(Handler, Pending);\n\n\t\tPending.prototype._state = 0;\n\n\t\tPending.prototype.resolve = function(x) {\n\t\t\tthis.become(getHandler(x));\n\t\t};\n\n\t\tPending.prototype.reject = function(x) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.become(new Rejected(x));\n\t\t};\n\n\t\tPending.prototype.join = function() {\n\t\t\tif (!this.resolved) {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\tvar h = this;\n\n\t\t\twhile (h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t\tif (h === this) {\n\t\t\t\t\treturn this.handler = cycle();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn h;\n\t\t};\n\n\t\tPending.prototype.run = function() {\n\t\t\tvar q = this.consumers;\n\t\t\tvar handler = this.handler;\n\t\t\tthis.handler = this.handler.join();\n\t\t\tthis.consumers = void 0;\n\n\t\t\tfor (var i = 0; i < q.length; ++i) {\n\t\t\t\thandler.when(q[i]);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.become = function(handler) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.resolved = true;\n\t\t\tthis.handler = handler;\n\t\t\tif(this.consumers !== void 0) {\n\t\t\t\ttasks.enqueue(this);\n\t\t\t}\n\n\t\t\tif(this.context !== void 0) {\n\t\t\t\thandler._report(this.context);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.when = function(continuation) {\n\t\t\tif(this.resolved) {\n\t\t\t\ttasks.enqueue(new ContinuationTask(continuation, this.handler));\n\t\t\t} else {\n\t\t\t\tif(this.consumers === void 0) {\n\t\t\t\t\tthis.consumers = [continuation];\n\t\t\t\t} else {\n\t\t\t\t\tthis.consumers.push(continuation);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tPending.prototype.notify = function(x) {\n\t\t\tif(!this.resolved) {\n\t\t\t\ttasks.enqueue(new ProgressTask(x, this));\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.fail = function(context) {\n\t\t\tvar c = typeof context === 'undefined' ? this.context : context;\n\t\t\tthis.resolved && this.handler.join().fail(c);\n\t\t};\n\n\t\tPending.prototype._report = function(context) {\n\t\t\tthis.resolved && this.handler.join()._report(context);\n\t\t};\n\n\t\tPending.prototype._unreport = function() {\n\t\t\tthis.resolved && this.handler.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Wrap another handler and force it into a future stack\n\t\t * @param {object} handler\n\t\t * @constructor\n\t\t */\n\t\tfunction Async(handler) {\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tinherit(Handler, Async);\n\n\t\tAsync.prototype.when = function(continuation) {\n\t\t\ttasks.enqueue(new ContinuationTask(continuation, this));\n\t\t};\n\n\t\tAsync.prototype._report = function(context) {\n\t\t\tthis.join()._report(context);\n\t\t};\n\n\t\tAsync.prototype._unreport = function() {\n\t\t\tthis.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Handler that wraps an untrusted thenable and assimilates it in a future stack\n\t\t * @param {function} then\n\t\t * @param {{then: function}} thenable\n\t\t * @constructor\n\t\t */\n\t\tfunction Thenable(then, thenable) {\n\t\t\tPending.call(this);\n\t\t\ttasks.enqueue(new AssimilateTask(then, thenable, this));\n\t\t}\n\n\t\tinherit(Pending, Thenable);\n\n\t\t/**\n\t\t * Handler for a fulfilled promise\n\t\t * @param {*} x fulfillment value\n\t\t * @constructor\n\t\t */\n\t\tfunction Fulfilled(x) {\n\t\t\tPromise.createContext(this);\n\t\t\tthis.value = x;\n\t\t}\n\n\t\tinherit(Handler, Fulfilled);\n\n\t\tFulfilled.prototype._state = 1;\n\n\t\tFulfilled.prototype.fold = function(f, z, c, to) {\n\t\t\trunContinuation3(f, z, this, c, to);\n\t\t};\n\n\t\tFulfilled.prototype.when = function(cont) {\n\t\t\trunContinuation1(cont.fulfilled, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tvar errorId = 0;\n\n\t\t/**\n\t\t * Handler for a rejected promise\n\t\t * @param {*} x rejection reason\n\t\t * @constructor\n\t\t */\n\t\tfunction Rejected(x) {\n\t\t\tPromise.createContext(this);\n\n\t\t\tthis.id = ++errorId;\n\t\t\tthis.value = x;\n\t\t\tthis.handled = false;\n\t\t\tthis.reported = false;\n\n\t\t\tthis._report();\n\t\t}\n\n\t\tinherit(Handler, Rejected);\n\n\t\tRejected.prototype._state = -1;\n\n\t\tRejected.prototype.fold = function(f, z, c, to) {\n\t\t\tto.become(this);\n\t\t};\n\n\t\tRejected.prototype.when = function(cont) {\n\t\t\tif(typeof cont.rejected === 'function') {\n\t\t\t\tthis._unreport();\n\t\t\t}\n\t\t\trunContinuation1(cont.rejected, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tRejected.prototype._report = function(context) {\n\t\t\ttasks.afterQueue(new ReportTask(this, context));\n\t\t};\n\n\t\tRejected.prototype._unreport = function() {\n\t\t\tif(this.handled) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.handled = true;\n\t\t\ttasks.afterQueue(new UnreportTask(this));\n\t\t};\n\n\t\tRejected.prototype.fail = function(context) {\n\t\t\tthis.reported = true;\n\t\t\temitRejection('unhandledRejection', this);\n\t\t\tPromise.onFatalRejection(this, context === void 0 ? this.context : context);\n\t\t};\n\n\t\tfunction ReportTask(rejection, context) {\n\t\t\tthis.rejection = rejection;\n\t\t\tthis.context = context;\n\t\t}\n\n\t\tReportTask.prototype.run = function() {\n\t\t\tif(!this.rejection.handled && !this.rejection.reported) {\n\t\t\t\tthis.rejection.reported = true;\n\t\t\t\temitRejection('unhandledRejection', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejection(this.rejection, this.context);\n\t\t\t}\n\t\t};\n\n\t\tfunction UnreportTask(rejection) {\n\t\t\tthis.rejection = rejection;\n\t\t}\n\n\t\tUnreportTask.prototype.run = function() {\n\t\t\tif(this.rejection.reported) {\n\t\t\t\temitRejection('rejectionHandled', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejectionHandled(this.rejection);\n\t\t\t}\n\t\t};\n\n\t\t// Unhandled rejection hooks\n\t\t// By default, everything is a noop\n\n\t\tPromise.createContext\n\t\t\t= Promise.enterContext\n\t\t\t= Promise.exitContext\n\t\t\t= Promise.onPotentiallyUnhandledRejection\n\t\t\t= Promise.onPotentiallyUnhandledRejectionHandled\n\t\t\t= Promise.onFatalRejection\n\t\t\t= noop;\n\n\t\t// Errors and singletons\n\n\t\tvar foreverPendingHandler = new Handler();\n\t\tvar foreverPendingPromise = new Promise(Handler, foreverPendingHandler);\n\n\t\tfunction cycle() {\n\t\t\treturn new Rejected(new TypeError('Promise cycle'));\n\t\t}\n\n\t\t// Task runners\n\n\t\t/**\n\t\t * Run a single consumer\n\t\t * @constructor\n\t\t */\n\t\tfunction ContinuationTask(continuation, handler) {\n\t\t\tthis.continuation = continuation;\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tContinuationTask.prototype.run = function() {\n\t\t\tthis.handler.join().when(this.continuation);\n\t\t};\n\n\t\t/**\n\t\t * Run a queue of progress handlers\n\t\t * @constructor\n\t\t */\n\t\tfunction ProgressTask(value, handler) {\n\t\t\tthis.handler = handler;\n\t\t\tthis.value = value;\n\t\t}\n\n\t\tProgressTask.prototype.run = function() {\n\t\t\tvar q = this.handler.consumers;\n\t\t\tif(q === void 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tfor (var c, i = 0; i < q.length; ++i) {\n\t\t\t\tc = q[i];\n\t\t\t\trunNotify(c.progress, this.value, this.handler, c.receiver, c.resolver);\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Assimilate a thenable, sending it's value to resolver\n\t\t * @param {function} then\n\t\t * @param {object|function} thenable\n\t\t * @param {object} resolver\n\t\t * @constructor\n\t\t */\n\t\tfunction AssimilateTask(then, thenable, resolver) {\n\t\t\tthis._then = then;\n\t\t\tthis.thenable = thenable;\n\t\t\tthis.resolver = resolver;\n\t\t}\n\n\t\tAssimilateTask.prototype.run = function() {\n\t\t\tvar h = this.resolver;\n\t\t\ttryAssimilate(this._then, this.thenable, _resolve, _reject, _notify);\n\n\t\t\tfunction _resolve(x) { h.resolve(x); }\n\t\t\tfunction _reject(x)  { h.reject(x); }\n\t\t\tfunction _notify(x)  { h.notify(x); }\n\t\t};\n\n\t\tfunction tryAssimilate(then, thenable, resolve, reject, notify) {\n\t\t\ttry {\n\t\t\t\tthen.call(thenable, resolve, reject, notify);\n\t\t\t} catch (e) {\n\t\t\t\treject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fold a handler value with z\n\t\t * @constructor\n\t\t */\n\t\tfunction Fold(f, z, c, to) {\n\t\t\tthis.f = f; this.z = z; this.c = c; this.to = to;\n\t\t\tthis.resolver = failIfRejected;\n\t\t\tthis.receiver = this;\n\t\t}\n\n\t\tFold.prototype.fulfilled = function(x) {\n\t\t\tthis.f.call(this.c, this.z, x, this.to);\n\t\t};\n\n\t\tFold.prototype.rejected = function(x) {\n\t\t\tthis.to.reject(x);\n\t\t};\n\n\t\tFold.prototype.progress = function(x) {\n\t\t\tthis.to.notify(x);\n\t\t};\n\n\t\t// Other helpers\n\n\t\t/**\n\t\t * @param {*} x\n\t\t * @returns {boolean} true iff x is a trusted Promise\n\t\t */\n\t\tfunction isPromise(x) {\n\t\t\treturn x instanceof Promise;\n\t\t}\n\n\t\t/**\n\t\t * Test just enough to rule out primitives, in order to take faster\n\t\t * paths in some code\n\t\t * @param {*} x\n\t\t * @returns {boolean} false iff x is guaranteed *not* to be a thenable\n\t\t */\n\t\tfunction maybeThenable(x) {\n\t\t\treturn (typeof x === 'object' || typeof x === 'function') && x !== null;\n\t\t}\n\n\t\tfunction runContinuation1(f, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject(f, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction runContinuation3(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject3(f, x, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tfunction runNotify(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.notify(x);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReturn(f, x, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction tryCatch2(f, a, b) {\n\t\t\ttry {\n\t\t\t\treturn f(a, b);\n\t\t\t} catch(e) {\n\t\t\t\treturn reject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Return f.call(thisArg, x), or if it throws return a rejected promise for\n\t\t * the thrown exception\n\t\t */\n\t\tfunction tryCatchReject(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.become(getHandler(f.call(thisArg, x)));\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Same as above, but includes the extra argument parameter.\n\t\t */\n\t\tfunction tryCatchReject3(f, x, y, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tf.call(thisArg, x, y, next);\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t * Return f.call(thisArg, x), or if it throws, *return* the exception\n\t\t */\n\t\tfunction tryCatchReturn(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.notify(f.call(thisArg, x));\n\t\t\t} catch(e) {\n\t\t\t\tnext.notify(e);\n\t\t\t}\n\t\t}\n\n\t\tfunction inherit(Parent, Child) {\n\t\t\tChild.prototype = objectCreate(Parent.prototype);\n\t\t\tChild.prototype.constructor = Child;\n\t\t}\n\n\t\tfunction snd(x, y) {\n\t\t\treturn y;\n\t\t}\n\n\t\tfunction noop() {}\n\n\t\tfunction hasCustomEvent() {\n\t\t\tif(typeof CustomEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\tvar ev = new CustomEvent('unhandledRejection');\n\t\t\t\t\treturn ev instanceof CustomEvent;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction hasInternetExplorerCustomEvent() {\n\t\t\tif(typeof document !== 'undefined' && typeof document.createEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\t// Try to create one event to make sure it's supported\n\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\tev.initCustomEvent('eventType', false, true, {});\n\t\t\t\t\treturn true;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction initEmitRejection() {\n\t\t\t/*global process, self, CustomEvent*/\n\t\t\tif(typeof process !== 'undefined' && process !== null\n\t\t\t\t&& typeof process.emit === 'function') {\n\t\t\t\t// Returning falsy here means to call the default\n\t\t\t\t// onPotentiallyUnhandledRejection API.  This is safe even in\n\t\t\t\t// browserify since process.emit always returns falsy in browserify:\n\t\t\t\t// https://github.com/defunctzombie/node-process/blob/master/browser.js#L40-L46\n\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\treturn type === 'unhandledRejection'\n\t\t\t\t\t\t? process.emit(type, rejection.value, rejection)\n\t\t\t\t\t\t: process.emit(type, rejection);\n\t\t\t\t};\n\t\t\t} else if(typeof self !== 'undefined' && hasCustomEvent()) {\n\t\t\t\treturn (function (self, CustomEvent) {\n\t\t\t\t\treturn function (type, rejection) {\n\t\t\t\t\t\tvar ev = new CustomEvent(type, {\n\t\t\t\t\t\t\tdetail: {\n\t\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tbubbles: false,\n\t\t\t\t\t\t\tcancelable: true\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, CustomEvent));\n\t\t\t} else if(typeof self !== 'undefined' && hasInternetExplorerCustomEvent()) {\n\t\t\t\treturn (function(self, document) {\n\t\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\t\tev.initCustomEvent(type, false, true, {\n\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, document));\n\t\t\t}\n\n\t\t\treturn noop;\n\t\t}\n\n\t\treturn Promise;\n\t};\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n"]}