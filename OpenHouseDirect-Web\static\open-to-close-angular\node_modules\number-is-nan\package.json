{"_from": "number-is-nan@^1.0.0", "_id": "number-is-nan@1.0.1", "_inBundle": false, "_integrity": "sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ==", "_location": "/number-is-nan", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "number-is-nan@^1.0.0", "name": "number-is-nan", "escapedName": "number-is-nan", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/is-fullwidth-code-point"], "_resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "_shasum": "097b602b53422a522c1afb8790318336941a011d", "_spec": "number-is-nan@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\is-fullwidth-code-point", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/number-is-nan/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ES2015 Number.isNaN() ponyfill", "devDependencies": {"ava": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/number-is-nan#readme", "keywords": ["es2015", "ecmascript", "ponyfill", "polyfill", "shim", "number", "is", "nan", "not"], "license": "MIT", "name": "number-is-nan", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/number-is-nan.git"}, "scripts": {"test": "ava"}, "version": "1.0.1"}