{"version": 3, "file": "FromObservable.js", "sourceRoot": "", "sources": ["../../src/observable/FromObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,wBAAwB,iBAAiB,CAAC,CAAA;AAC1C,4BAA4B,qBAAqB,CAAC,CAAA;AAClD,0BAA0B,mBAAmB,CAAC,CAAA;AAC9C,kCAAkC,qBAAqB,CAAC,CAAA;AACxD,mCAAkC,sBAAsB,CAAC,CAAA;AACzD,gCAAgC,mBAAmB,CAAC,CAAA;AACpD,oCAAoC,uBAAuB,CAAC,CAAA;AAG5D,yBAA4C,oBAAoB,CAAC,CAAA;AACjE,2BAA4C,eAAe,CAAC,CAAA;AAE5D,0BAAoC,wBAAwB,CAAC,CAAA;AAC7D,2BAAgD,sBAAsB,CAAC,CAAA;AAEvE;;;;GAIG;AACH;IAAuC,kCAAa;IAClD,wBAAoB,GAAuB,EAAU,SAAsB;QACzE,kBAAM,IAAI,CAAC,CAAC;QADM,QAAG,GAAH,GAAG,CAAoB;QAAU,cAAS,GAAT,SAAS,CAAa;IAE3E,CAAC;IAKD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuDG;IACI,qBAAM,GAAb,UAAiB,GAAuB,EAAE,SAAsB;QAC9D,EAAE,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;YAChB,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,uBAAiB,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC;gBACjD,EAAE,CAAC,CAAC,GAAG,YAAY,uBAAU,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC5C,MAAM,CAAC,GAAG,CAAC;gBACb,CAAC;gBACD,MAAM,CAAC,IAAI,cAAc,CAAI,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/C,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,iBAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,CAAC,IAAI,iCAAe,CAAI,GAAG,EAAE,SAAS,CAAC,CAAC;YAChD,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,qBAAS,CAAI,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,CAAC,IAAI,qCAAiB,CAAI,GAAG,EAAE,SAAS,CAAC,CAAC;YAClD,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,mBAAe,CAAC,KAAK,UAAU,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACjF,MAAM,CAAC,IAAI,uCAAkB,CAAI,GAAG,EAAE,SAAS,CAAC,CAAC;YACnD,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,yBAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM,CAAC,IAAI,yCAAmB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,oBAAoB,CAAC,CAAC;IAClF,CAAC;IAED,oCAAoC,CAAC,mCAAU,GAAV,UAAW,UAAyB;QACvE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,EAAE,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,GAAG,CAAC,uBAAiB,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACxD,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,GAAG,CAAC,uBAAiB,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,+BAAmB,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IACH,qBAAC;AAAD,CAAC,AA9FD,CAAuC,uBAAU,GA8FhD;AA9FY,sBAAc,iBA8F1B,CAAA", "sourcesContent": ["import { isArray } from '../util/isArray';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { PromiseObservable } from './PromiseObservable';\nimport { IteratorObservable } from'./IteratorObservable';\nimport { ArrayObservable } from './ArrayObservable';\nimport { ArrayLikeObservable } from './ArrayLikeObservable';\n\nimport { IScheduler } from '../Scheduler';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { Observable, ObservableInput } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { ObserveOnSubscriber } from '../operators/observeOn';\nimport { observable as Symbol_observable } from '../symbol/observable';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class FromObservable<T> extends Observable<T> {\n  constructor(private ish: ObservableInput<T>, private scheduler?: IScheduler) {\n    super(null);\n  }\n\n  static create<T>(ish: ObservableInput<T>, scheduler?: IScheduler): Observable<T>;\n  static create<T, R>(ish: ArrayLike<T>, scheduler?: IScheduler): Observable<R>;\n\n  /**\n   * Creates an Observable from an Array, an array-like object, a Promise, an\n   * iterable object, or an Observable-like object.\n   *\n   * <span class=\"informal\">Converts almost anything to an Observable.</span>\n   *\n   * <img src=\"./img/from.png\" width=\"100%\">\n   *\n   * Convert various other objects and data types into Observables. `from`\n   * converts a Promise or an array-like or an\n   * [iterable](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols#iterable)\n   * object into an Observable that emits the items in that promise or array or\n   * iterable. A String, in this context, is treated as an array of characters.\n   * Observable-like objects (contains a function named with the ES2015 Symbol\n   * for Observable) can also be converted through this operator.\n   *\n   * @example <caption>Converts an array to an Observable</caption>\n   * var array = [10, 20, 30];\n   * var result = Rx.Observable.from(array);\n   * result.subscribe(x => console.log(x));\n   *\n   * // Results in the following:\n   * // 10 20 30\n   *\n   * @example <caption>Convert an infinite iterable (from a generator) to an Observable</caption>\n   * function* generateDoubles(seed) {\n   *   var i = seed;\n   *   while (true) {\n   *     yield i;\n   *     i = 2 * i; // double it\n   *   }\n   * }\n   *\n   * var iterator = generateDoubles(3);\n   * var result = Rx.Observable.from(iterator).take(10);\n   * result.subscribe(x => console.log(x));\n   *\n   * // Results in the following:\n   * // 3 6 12 24 48 96 192 384 768 1536\n   *\n   * @see {@link create}\n   * @see {@link fromEvent}\n   * @see {@link fromEventPattern}\n   * @see {@link fromPromise}\n   *\n   * @param {ObservableInput<T>} ish A subscribable object, a Promise, an\n   * Observable-like, an Array, an iterable or an array-like object to be\n   * converted.\n   * @param {Scheduler} [scheduler] The scheduler on which to schedule the\n   * emissions of values.\n   * @return {Observable<T>} The Observable whose values are originally from the\n   * input object that was converted.\n   * @static true\n   * @name from\n   * @owner Observable\n   */\n  static create<T>(ish: ObservableInput<T>, scheduler?: IScheduler): Observable<T> {\n    if (ish != null) {\n      if (typeof ish[Symbol_observable] === 'function') {\n        if (ish instanceof Observable && !scheduler) {\n          return ish;\n        }\n        return new FromObservable<T>(ish, scheduler);\n      } else if (isArray(ish)) {\n        return new ArrayObservable<T>(ish, scheduler);\n      } else if (isPromise<T>(ish)) {\n        return new PromiseObservable<T>(ish, scheduler);\n      } else if (typeof ish[Symbol_iterator] === 'function' || typeof ish === 'string') {\n        return new IteratorObservable<T>(ish, scheduler);\n      } else if (isArrayLike(ish)) {\n        return new ArrayLikeObservable(ish, scheduler);\n      }\n    }\n\n    throw new TypeError((ish !== null && typeof ish || ish) + ' is not observable');\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>) {\n    const ish = this.ish;\n    const scheduler = this.scheduler;\n    if (scheduler == null) {\n      return ish[Symbol_observable]().subscribe(subscriber);\n    } else {\n      return ish[Symbol_observable]().subscribe(new ObserveOnSubscriber(subscriber, scheduler, 0));\n    }\n  }\n}\n"]}