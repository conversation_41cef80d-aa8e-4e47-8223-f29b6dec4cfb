{"uuid": "37d58de4-deea-4808-bb77-d27685bd1501", "parent": null, "pid": 93238, "argv": ["/usr/local/bin/node", "/usr/local/bin/tap", "test/00-setup-fixtures.js", "test/dir-normalization.js", "test/error-on-broken.js", "test/extract-move.js", "test/extract.js", "test/header.js", "test/pack-no-proprietary.js", "test/pack.js", "test/parse-discard.js", "test/parse.js", "test/zz-cleanup.js"], "execArgv": [], "cwd": "/Users/<USER>/dev/js/tar", "time": 1557878798122, "ppid": 93237, "root": "e52f8603-1293-44df-8bfa-ed740bdd2b77", "coverageFilename": "/Users/<USER>/dev/js/tar/.nyc_output/37d58de4-deea-4808-bb77-d27685bd1501.json", "files": []}