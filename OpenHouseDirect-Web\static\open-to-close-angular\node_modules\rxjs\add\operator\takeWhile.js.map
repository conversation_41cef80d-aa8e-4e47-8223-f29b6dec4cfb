{"version": 3, "file": "takeWhile.js", "sourceRoot": "", "sources": ["../../../src/add/operator/takeWhile.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,0BAA0B,0BAA0B,CAAC,CAAA;AAErD,uBAAU,CAAC,SAAS,CAAC,SAAS,GAAG,qBAAS,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { takeWhile } from '../../operator/takeWhile';\n\nObservable.prototype.takeWhile = takeWhile;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    takeWhile: typeof takeWhile;\n  }\n}"]}