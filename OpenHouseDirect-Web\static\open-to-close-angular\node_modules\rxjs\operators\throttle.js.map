{"version": 3, "file": "throttle.js", "sourceRoot": "", "sources": ["../../src/operators/throttle.ts"], "names": [], "mappings": ";;;;;;AAKA,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AASjD,6BAAqB,GAAmB;IACnD,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,KAAK;CAChB,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuCG;AACH,kBAA4B,gBAA6D,EAC7D,MAA8C;IAA9C,sBAA8C,GAA9C,sCAA8C;IACxE,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAApF,CAAoF,CAAC;AACzH,CAAC;AAHe,gBAAQ,WAGvB,CAAA;AAED;IACE,0BAAoB,gBAA6D,EAC7D,OAAgB,EAChB,QAAiB;QAFjB,qBAAgB,GAAhB,gBAAgB,CAA6C;QAC7D,YAAO,GAAP,OAAO,CAAS;QAChB,aAAQ,GAAR,QAAQ,CAAS;IACrC,CAAC;IAED,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CACrB,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CACvF,CAAC;IACJ,CAAC;IACH,uBAAC;AAAD,CAAC,AAXD,IAWC;AAED;;;;GAIG;AACH;IAAuC,sCAAqB;IAK1D,4BAAsB,WAA0B,EAC5B,gBAA6D,EAC7D,QAAiB,EACjB,SAAkB;QACpC,kBAAM,WAAW,CAAC,CAAC;QAJC,gBAAW,GAAX,WAAW,CAAe;QAC5B,qBAAgB,GAAhB,gBAAgB,CAA6C;QAC7D,aAAQ,GAAR,QAAQ,CAAS;QACjB,cAAS,GAAT,SAAS,CAAS;QAL9B,sBAAiB,GAAG,KAAK,CAAC;IAOlC,CAAC;IAES,kCAAK,GAAf,UAAgB,KAAQ;QACtB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACnB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAC9B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC9B,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACjD,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,qCAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC/D,CAAC;YACD,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7B,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBACnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC9B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,gDAAmB,GAA3B,UAA4B,KAAQ;QAClC,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,oCAAoC,CAAC,yCAAY,GAAZ;QACnC,IAAA,SAAwE,EAAhE,wBAAS,EAAE,kCAAc,EAAE,wCAAiB,EAAE,wBAAS,CAAU;QAEzE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAE/B,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,SAAS,CAAC,WAAW,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAEO,0CAAa,GAArB;QACE,IAAA,SAAqF,EAA7E,4BAAW,EAAE,wBAAS,EAAE,wBAAS,EAAE,kCAAc,EAAE,wCAAiB,CAAU;QACtF,EAAE,CAAC,CAAC,SAAS,IAAI,SAAS,IAAI,iBAAiB,CAAC,CAAC,CAAC;YAChD,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QACjC,CAAC;IACH,CAAC;IAED,uCAAU,GAAV,UAAW,UAAa,EAAE,UAAa,EAC5B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACxC,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED,2CAAc,GAAd;QACE,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IACH,yBAAC;AAAD,CAAC,AA3ED,CAAuC,iCAAe,GA2ErD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Observable, SubscribableOrPromise } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription, TeardownLogic } from '../Subscription';\n\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\n\nimport { MonoTypeOperatorFunction } from '../interfaces';\n\nexport interface ThrottleConfig {\n  leading?: boolean;\n  trailing?: boolean;\n}\n\nexport const defaultThrottleConfig: ThrottleConfig = {\n  leading: true,\n  trailing: false\n};\n\n/**\n * Emits a value from the source Observable, then ignores subsequent source\n * values for a duration determined by another Observable, then repeats this\n * process.\n *\n * <span class=\"informal\">It's like {@link throttleTime}, but the silencing\n * duration is determined by a second Observable.</span>\n *\n * <img src=\"./img/throttle.png\" width=\"100%\">\n *\n * `throttle` emits the source Observable values on the output Observable\n * when its internal timer is disabled, and ignores source values when the timer\n * is enabled. Initially, the timer is disabled. As soon as the first source\n * value arrives, it is forwarded to the output Observable, and then the timer\n * is enabled by calling the `durationSelector` function with the source value,\n * which returns the \"duration\" Observable. When the duration Observable emits a\n * value or completes, the timer is disabled, and this process repeats for the\n * next source value.\n *\n * @example <caption>Emit clicks at a rate of at most one click per second</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var result = clicks.throttle(ev => Rx.Observable.interval(1000));\n * result.subscribe(x => console.log(x));\n *\n * @see {@link audit}\n * @see {@link debounce}\n * @see {@link delayWhen}\n * @see {@link sample}\n * @see {@link throttleTime}\n *\n * @param {function(value: T): SubscribableOrPromise} durationSelector A function\n * that receives a value from the source Observable, for computing the silencing\n * duration for each source value, returned as an Observable or a Promise.\n * @param {Object} config a configuration object to define `leading` and `trailing` behavior. Defaults\n * to `{ leading: true, trailing: false }`.\n * @return {Observable<T>} An Observable that performs the throttle operation to\n * limit the rate of emissions from the source.\n * @method throttle\n * @owner Observable\n */\nexport function throttle<T>(durationSelector: (value: T) => SubscribableOrPromise<number>,\n                            config: ThrottleConfig = defaultThrottleConfig): MonoTypeOperatorFunction<T> {\n  return (source: Observable<T>) => source.lift(new ThrottleOperator(durationSelector, config.leading, config.trailing));\n}\n\nclass ThrottleOperator<T> implements Operator<T, T> {\n  constructor(private durationSelector: (value: T) => SubscribableOrPromise<number>,\n              private leading: boolean,\n              private trailing: boolean) {\n  }\n\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n    return source.subscribe(\n      new ThrottleSubscriber(subscriber, this.durationSelector, this.leading, this.trailing)\n    );\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc\n * @ignore\n * @extends {Ignored}\n */\nclass ThrottleSubscriber<T, R> extends OuterSubscriber<T, R> {\n  private throttled: Subscription;\n  private _trailingValue: T;\n  private _hasTrailingValue = false;\n\n  constructor(protected destination: Subscriber<T>,\n              private durationSelector: (value: T) => SubscribableOrPromise<number>,\n              private _leading: boolean,\n              private _trailing: boolean) {\n    super(destination);\n  }\n\n  protected _next(value: T): void {\n    if (this.throttled) {\n      if (this._trailing) {\n        this._hasTrailingValue = true;\n        this._trailingValue = value;\n      }\n    } else {\n      const duration = this.tryDurationSelector(value);\n      if (duration) {\n        this.add(this.throttled = subscribeToResult(this, duration));\n      }\n      if (this._leading) {\n        this.destination.next(value);\n        if (this._trailing) {\n          this._hasTrailingValue = true;\n          this._trailingValue = value;\n        }\n      }\n    }\n  }\n\n  private tryDurationSelector(value: T): SubscribableOrPromise<any> {\n    try {\n      return this.durationSelector(value);\n    } catch (err) {\n      this.destination.error(err);\n      return null;\n    }\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    const { throttled, _trailingValue, _hasTrailingValue, _trailing } = this;\n\n    this._trailingValue = null;\n    this._hasTrailingValue = false;\n\n    if (throttled) {\n      this.remove(throttled);\n      this.throttled = null;\n      throttled.unsubscribe();\n    }\n  }\n\n  private _sendTrailing() {\n    const { destination, throttled, _trailing, _trailingValue, _hasTrailingValue } = this;\n    if (throttled && _trailing && _hasTrailingValue) {\n      destination.next(_trailingValue);\n      this._trailingValue = null;\n      this._hasTrailingValue = false;\n    }\n  }\n\n  notifyNext(outerValue: T, innerValue: R,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, R>): void {\n    this._sendTrailing();\n    this._unsubscribe();\n  }\n\n  notifyComplete(): void {\n    this._sendTrailing();\n    this._unsubscribe();\n  }\n}\n"]}