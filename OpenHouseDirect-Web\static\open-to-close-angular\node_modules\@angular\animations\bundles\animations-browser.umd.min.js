/**
 * @license Angular v5.2.11
 * (c) 2010-2018 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/animations")):"function"==typeof define&&define.amd?define("@angular/animations/browser",["exports","@angular/animations"],factory):factory((global.ng=global.ng||{},global.ng.animations=global.ng.animations||{},global.ng.animations.browser={}),global.ng.animations)}(this,function(exports,_angular_animations){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}/**
 * @license Angular v5.2.11
 * (c) 2010-2018 Google, Inc. https://angular.io/
 * License: MIT
 */
function optimizeGroupPlayer(players){switch(players.length){case 0:return new _angular_animations.NoopAnimationPlayer;case 1:return players[0];default:return new _angular_animations.ɵAnimationGroupPlayer(players)}}function normalizeKeyframes(driver,normalizer,element,keyframes,preStyles,postStyles){void 0===preStyles&&(preStyles={}),void 0===postStyles&&(postStyles={});var errors=[],normalizedKeyframes=[],previousOffset=-1,previousKeyframe=null;if(keyframes.forEach(function(kf){var offset=kf.offset,isSameOffset=offset==previousOffset,normalizedKeyframe=isSameOffset&&previousKeyframe||{};Object.keys(kf).forEach(function(prop){var normalizedProp=prop,normalizedValue=kf[prop];if("offset"!==prop)switch(normalizedProp=normalizer.normalizePropertyName(normalizedProp,errors),normalizedValue){case _angular_animations.ɵPRE_STYLE:normalizedValue=preStyles[prop];break;case _angular_animations.AUTO_STYLE:normalizedValue=postStyles[prop];break;default:normalizedValue=normalizer.normalizeStyleValue(prop,normalizedProp,normalizedValue,errors)}normalizedKeyframe[normalizedProp]=normalizedValue}),isSameOffset||normalizedKeyframes.push(normalizedKeyframe),previousKeyframe=normalizedKeyframe,previousOffset=offset}),errors.length){throw new Error("Unable to animate due to the following errors:\n - "+errors.join("\n - "))}return normalizedKeyframes}function listenOnPlayer(player,eventName,event,callback){switch(eventName){case"start":player.onStart(function(){return callback(event&&copyAnimationEvent(event,"start",player.totalTime))});break;case"done":player.onDone(function(){return callback(event&&copyAnimationEvent(event,"done",player.totalTime))});break;case"destroy":player.onDestroy(function(){return callback(event&&copyAnimationEvent(event,"destroy",player.totalTime))})}}function copyAnimationEvent(e,phaseName,totalTime){var event=makeAnimationEvent(e.element,e.triggerName,e.fromState,e.toState,phaseName||e.phaseName,void 0==totalTime?e.totalTime:totalTime),data=e._data;return null!=data&&(event._data=data),event}function makeAnimationEvent(element,triggerName,fromState,toState,phaseName,totalTime){return void 0===phaseName&&(phaseName=""),void 0===totalTime&&(totalTime=0),{element:element,triggerName:triggerName,fromState:fromState,toState:toState,phaseName:phaseName,totalTime:totalTime}}function getOrSetAsInMap(map,key,defaultValue){var value;return map instanceof Map?(value=map.get(key))||map.set(key,value=defaultValue):(value=map[key])||(value=map[key]=defaultValue),value}function parseTimelineCommand(command){var separatorPos=command.indexOf(":");return[command.substring(1,separatorPos),command.substr(separatorPos+1)]}function containsVendorPrefix(prop){return"ebkit"==prop.substring(1,6)}function validateStyleProperty(prop){_CACHED_BODY||(_CACHED_BODY=getBodyNode()||{},_IS_WEBKIT=!!_CACHED_BODY.style&&"WebkitAppearance"in _CACHED_BODY.style);var result=!0;if(_CACHED_BODY.style&&!containsVendorPrefix(prop)&&!(result=prop in _CACHED_BODY.style)&&_IS_WEBKIT){result="Webkit"+prop.charAt(0).toUpperCase()+prop.substr(1)in _CACHED_BODY.style}return result}function getBodyNode(){return"undefined"!=typeof document?document.body:null}function resolveTimingValue(value){if("number"==typeof value)return value;var matches=value.match(/^(-?[\.\d]+)(m?s)/);return!matches||matches.length<2?0:_convertTimeValueToMS(parseFloat(matches[1]),matches[2])}function _convertTimeValueToMS(value,unit){switch(unit){case"s":return value*ONE_SECOND;default:return value}}function resolveTiming(timings,errors,allowNegativeValues){return timings.hasOwnProperty("duration")?timings:parseTimeExpression(timings,errors,allowNegativeValues)}function parseTimeExpression(exp,errors,allowNegativeValues){var duration,regex=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,delay=0,easing="";if("string"==typeof exp){var matches=exp.match(regex);if(null===matches)return errors.push('The provided timing value "'+exp+'" is invalid.'),{duration:0,delay:0,easing:""};duration=_convertTimeValueToMS(parseFloat(matches[1]),matches[2]);var delayMatch=matches[3];null!=delayMatch&&(delay=_convertTimeValueToMS(Math.floor(parseFloat(delayMatch)),matches[4]));var easingVal=matches[5];easingVal&&(easing=easingVal)}else duration=exp;if(!allowNegativeValues){var containsErrors=!1,startIndex=errors.length;duration<0&&(errors.push("Duration values below 0 are not allowed for this animation step."),containsErrors=!0),delay<0&&(errors.push("Delay values below 0 are not allowed for this animation step."),containsErrors=!0),containsErrors&&errors.splice(startIndex,0,'The provided timing value "'+exp+'" is invalid.')}return{duration:duration,delay:delay,easing:easing}}function copyObj(obj,destination){return void 0===destination&&(destination={}),Object.keys(obj).forEach(function(prop){destination[prop]=obj[prop]}),destination}function normalizeStyles(styles){var normalizedStyles={};return Array.isArray(styles)?styles.forEach(function(data){return copyStyles(data,!1,normalizedStyles)}):copyStyles(styles,!1,normalizedStyles),normalizedStyles}function copyStyles(styles,readPrototype,destination){if(void 0===destination&&(destination={}),readPrototype)for(var prop in styles)destination[prop]=styles[prop];else copyObj(styles,destination);return destination}function setStyles(element,styles){element.style&&Object.keys(styles).forEach(function(prop){var camelProp=dashCaseToCamelCase(prop);element.style[camelProp]=styles[prop]})}function eraseStyles(element,styles){element.style&&Object.keys(styles).forEach(function(prop){var camelProp=dashCaseToCamelCase(prop);element.style[camelProp]=""})}function normalizeAnimationEntry(steps){return Array.isArray(steps)?1==steps.length?steps[0]:_angular_animations.sequence(steps):steps}function validateStyleParams(value,options,errors){var params=options.params||{},matches=extractStyleParams(value);matches.length&&matches.forEach(function(varName){params.hasOwnProperty(varName)||errors.push("Unable to resolve the local animation param "+varName+" in the given list of values")})}function extractStyleParams(value){var params=[];if("string"==typeof value){for(var val=value.toString(),match=void 0;match=PARAM_REGEX.exec(val);)params.push(match[1]);PARAM_REGEX.lastIndex=0}return params}function interpolateParams(value,params,errors){var original=value.toString(),str=original.replace(PARAM_REGEX,function(_,varName){var localVal=params[varName];return params.hasOwnProperty(varName)||(errors.push("Please provide a value for the animation param "+varName),localVal=""),localVal.toString()});return str==original?value:str}function iteratorToArray(iterator){for(var arr=[],item=iterator.next();!item.done;)arr.push(item.value),item=iterator.next();return arr}function dashCaseToCamelCase(input){return input.replace(DASH_CASE_REGEXP,function(){for(var m=[],_i=0;_i<arguments.length;_i++)m[_i]=arguments[_i];return m[1].toUpperCase()})}function allowPreviousPlayerStylesMerge(duration,delay){return 0===duration||0===delay}function visitDslNode(visitor,node,context){switch(node.type){case 7:return visitor.visitTrigger(node,context);case 0:return visitor.visitState(node,context);case 1:return visitor.visitTransition(node,context);case 2:return visitor.visitSequence(node,context);case 3:return visitor.visitGroup(node,context);case 4:return visitor.visitAnimate(node,context);case 5:return visitor.visitKeyframes(node,context);case 6:return visitor.visitStyle(node,context);case 8:return visitor.visitReference(node,context);case 9:return visitor.visitAnimateChild(node,context);case 10:return visitor.visitAnimateRef(node,context);case 11:return visitor.visitQuery(node,context);case 12:return visitor.visitStagger(node,context);default:throw new Error("Unable to resolve animation metadata node #"+node.type)}}function parseTransitionExpr(transitionValue,errors){var expressions=[];return"string"==typeof transitionValue?transitionValue.split(/\s*,\s*/).forEach(function(str){return parseInnerTransitionStr(str,expressions,errors)}):expressions.push(transitionValue),expressions}function parseInnerTransitionStr(eventStr,expressions,errors){if(":"==eventStr[0]){var result=parseAnimationAlias(eventStr,errors);if("function"==typeof result)return void expressions.push(result);eventStr=result}var match=eventStr.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==match||match.length<4)return errors.push('The provided transition expression "'+eventStr+'" is not supported'),expressions;var fromState=match[1],separator=match[2],toState=match[3];expressions.push(makeLambdaFromStates(fromState,toState));var isFullAnyStateExpr=fromState==ANY_STATE&&toState==ANY_STATE;"<"!=separator[0]||isFullAnyStateExpr||expressions.push(makeLambdaFromStates(toState,fromState))}function parseAnimationAlias(alias,errors){switch(alias){case":enter":return"void => *";case":leave":return"* => void";case":increment":return function(fromState,toState){return parseFloat(toState)>parseFloat(fromState)};case":decrement":return function(fromState,toState){return parseFloat(toState)<parseFloat(fromState)};default:return errors.push('The transition alias value "'+alias+'" is not supported'),"* => *"}}function makeLambdaFromStates(lhs,rhs){var LHS_MATCH_BOOLEAN=TRUE_BOOLEAN_VALUES.has(lhs)||FALSE_BOOLEAN_VALUES.has(lhs),RHS_MATCH_BOOLEAN=TRUE_BOOLEAN_VALUES.has(rhs)||FALSE_BOOLEAN_VALUES.has(rhs);return function(fromState,toState){var lhsMatch=lhs==ANY_STATE||lhs==fromState,rhsMatch=rhs==ANY_STATE||rhs==toState;return!lhsMatch&&LHS_MATCH_BOOLEAN&&"boolean"==typeof fromState&&(lhsMatch=fromState?TRUE_BOOLEAN_VALUES.has(lhs):FALSE_BOOLEAN_VALUES.has(lhs)),!rhsMatch&&RHS_MATCH_BOOLEAN&&"boolean"==typeof toState&&(rhsMatch=toState?TRUE_BOOLEAN_VALUES.has(rhs):FALSE_BOOLEAN_VALUES.has(rhs)),lhsMatch&&rhsMatch}}function buildAnimationAst(driver,metadata,errors){return new AnimationAstBuilderVisitor(driver).build(metadata,errors)}function normalizeSelector(selector){var hasAmpersand=!!selector.split(/\s*,\s*/).find(function(token){return token==SELF_TOKEN});return hasAmpersand&&(selector=selector.replace(SELF_TOKEN_REGEX,"")),selector=selector.replace(/@\*/g,NG_TRIGGER_SELECTOR).replace(/@\w+/g,function(match){return NG_TRIGGER_SELECTOR+"-"+match.substr(1)}).replace(/:animating/g,NG_ANIMATING_SELECTOR),[selector,hasAmpersand]}function normalizeParams(obj){return obj?copyObj(obj):null}function consumeOffset(styles){if("string"==typeof styles)return null;var offset=null;if(Array.isArray(styles))styles.forEach(function(styleTuple){if(isObject(styleTuple)&&styleTuple.hasOwnProperty("offset")){var obj=styleTuple;offset=parseFloat(obj.offset),delete obj.offset}});else if(isObject(styles)&&styles.hasOwnProperty("offset")){var obj=styles;offset=parseFloat(obj.offset),delete obj.offset}return offset}function isObject(value){return!Array.isArray(value)&&"object"==typeof value}function constructTimingAst(value,errors){var timings=null;if(value.hasOwnProperty("duration"))timings=value;else if("number"==typeof value){var duration=resolveTiming(value,errors).duration;return makeTimingAst(duration,0,"")}var strValue=value;if(strValue.split(/\s+/).some(function(v){return"{"==v.charAt(0)&&"{"==v.charAt(1)})){var ast=makeTimingAst(0,0,"");return ast.dynamic=!0,ast.strValue=strValue,ast}return timings=timings||resolveTiming(strValue,errors),makeTimingAst(timings.duration,timings.delay,timings.easing)}function normalizeAnimationOptions(options){return options?(options=copyObj(options),options.params&&(options.params=normalizeParams(options.params))):options={},options}function makeTimingAst(duration,delay,easing){return{duration:duration,delay:delay,easing:easing}}function createTimelineInstruction(element,keyframes,preStyleProps,postStyleProps,duration,delay,easing,subTimeline){return void 0===easing&&(easing=null),void 0===subTimeline&&(subTimeline=!1),{type:1,element:element,keyframes:keyframes,preStyleProps:preStyleProps,postStyleProps:postStyleProps,duration:duration,delay:delay,totalTime:duration+delay,easing:easing,subTimeline:subTimeline}}function buildAnimationTimelines(driver,rootElement,ast,enterClassName,leaveClassName,startingStyles,finalStyles,options,subInstructions,errors){return void 0===startingStyles&&(startingStyles={}),void 0===finalStyles&&(finalStyles={}),void 0===errors&&(errors=[]),(new AnimationTimelineBuilderVisitor).buildKeyframes(driver,rootElement,ast,enterClassName,leaveClassName,startingStyles,finalStyles,options,subInstructions,errors)}function roundOffset(offset,decimalPoints){void 0===decimalPoints&&(decimalPoints=3);var mult=Math.pow(10,decimalPoints-1);return Math.round(offset*mult)/mult}function flattenStyles(input,allStyles){var allProperties,styles={};return input.forEach(function(token){"*"===token?(allProperties=allProperties||Object.keys(allStyles),allProperties.forEach(function(prop){styles[prop]=_angular_animations.AUTO_STYLE})):copyStyles(token,!1,styles)}),styles}function createTransitionInstruction(element,triggerName,fromState,toState,isRemovalTransition,fromStyles,toStyles,timelines,queriedElements,preStyleProps,postStyleProps,errors){return{type:0,element:element,triggerName:triggerName,isRemovalTransition:isRemovalTransition,fromState:fromState,fromStyles:fromStyles,toState:toState,toStyles:toStyles,timelines:timelines,queriedElements:queriedElements,preStyleProps:preStyleProps,postStyleProps:postStyleProps,errors:errors}}function oneOrMoreTransitionsMatch(matchFns,currentState,nextState){return matchFns.some(function(fn){return fn(currentState,nextState)})}function buildTrigger(name,ast){return new AnimationTrigger(name,ast)}function createFallbackTransition(triggerName,states){return new AnimationTransitionFactory(triggerName,{type:1,animation:{type:2,steps:[],options:null},matchers:[function(fromState,toState){return!0}],options:null,queryCount:0,depCount:0},states)}function balanceProperties(obj,key1,key2){obj.hasOwnProperty(key1)?obj.hasOwnProperty(key2)||(obj[key2]=obj[key1]):obj.hasOwnProperty(key2)&&(obj[key1]=obj[key2])}function deleteOrUnsetInMap(map,key,value){var currentValues;if(map instanceof Map){if(currentValues=map.get(key)){if(currentValues.length){var index=currentValues.indexOf(value);currentValues.splice(index,1)}0==currentValues.length&&map.delete(key)}}else if(currentValues=map[key]){if(currentValues.length){var index=currentValues.indexOf(value);currentValues.splice(index,1)}0==currentValues.length&&delete map[key]}return currentValues}function normalizeTriggerValue(value){return null!=value?value:null}function isElementNode(node){return node&&1===node.nodeType}function isTriggerEventValid(eventName){return"start"==eventName||"done"==eventName}function cloakElement(element,value){var oldValue=element.style.display;return element.style.display=null!=value?value:"none",oldValue}function cloakAndComputeStyles(valuesMap,driver,elements,elementPropsMap,defaultStyle){var cloakVals=[];elements.forEach(function(element){return cloakVals.push(cloakElement(element))});var failedElements=[];elementPropsMap.forEach(function(props,element){var styles={};props.forEach(function(prop){var value=styles[prop]=driver.computeStyle(element,prop,defaultStyle);value&&0!=value.length||(element[REMOVAL_FLAG]=NULL_REMOVED_QUERIED_STATE,failedElements.push(element))}),valuesMap.set(element,styles)});var i=0;return elements.forEach(function(element){return cloakElement(element,cloakVals[i++])}),failedElements}function buildRootMap(roots,nodes){function getRoot(node){if(!node)return NULL_NODE;var root=localRootMap.get(node);if(root)return root;var parent=node.parentNode;return root=rootMap.has(parent)?parent:nodeSet.has(parent)?NULL_NODE:getRoot(parent),localRootMap.set(node,root),root}var rootMap=new Map;if(roots.forEach(function(root){return rootMap.set(root,[])}),0==nodes.length)return rootMap;var NULL_NODE=1,nodeSet=new Set(nodes),localRootMap=new Map;return nodes.forEach(function(node){var root=getRoot(node);root!==NULL_NODE&&rootMap.get(root).push(node)}),rootMap}function addClass(element,className){if(element.classList)element.classList.add(className);else{var classes=element[CLASSES_CACHE_KEY];classes||(classes=element[CLASSES_CACHE_KEY]={}),classes[className]=!0}}function removeClass(element,className){if(element.classList)element.classList.remove(className);else{var classes=element[CLASSES_CACHE_KEY];classes&&delete classes[className]}}function removeNodesAfterAnimationDone(engine,element,players){optimizeGroupPlayer(players).onDone(function(){return engine.processLeaveNode(element)})}function flattenGroupPlayers(players){var finalPlayers=[];return _flattenGroupPlayersRecur(players,finalPlayers),finalPlayers}function _flattenGroupPlayersRecur(players,finalPlayers){for(var i=0;i<players.length;i++){var player=players[i];player instanceof _angular_animations.ɵAnimationGroupPlayer?_flattenGroupPlayersRecur(player.players,finalPlayers):finalPlayers.push(player)}}function objEquals(a,b){var k1=Object.keys(a),k2=Object.keys(b);if(k1.length!=k2.length)return!1;for(var i=0;i<k1.length;i++){var prop=k1[i];if(!b.hasOwnProperty(prop)||a[prop]!==b[prop])return!1}return!0}function replacePostStylesAsPre(element,allPreStyleElements,allPostStyleElements){var postEntry=allPostStyleElements.get(element);if(!postEntry)return!1;var preEntry=allPreStyleElements.get(element);return preEntry?postEntry.forEach(function(data){return preEntry.add(data)}):allPreStyleElements.set(element,postEntry),allPostStyleElements.delete(element),!0}function _computeStyle(element,prop){return window.getComputedStyle(element)[prop]}function supportsWebAnimations(){return"undefined"!=typeof Element&&"function"==typeof Element.prototype.animate}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},__assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p])}return t},_contains=function(elm1,elm2){return!1},_matches=function(element,selector){return!1},_query=function(element,selector,multi){return[]};if("undefined"!=typeof Element){if(_contains=function(elm1,elm2){return elm1.contains(elm2)},Element.prototype.matches)_matches=function(element,selector){return element.matches(selector)};else{var proto=Element.prototype,fn_1=proto.matchesSelector||proto.mozMatchesSelector||proto.msMatchesSelector||proto.oMatchesSelector||proto.webkitMatchesSelector;fn_1&&(_matches=function(element,selector){return fn_1.apply(element,[selector])})}_query=function(element,selector,multi){var results=[];if(multi)results.push.apply(results,element.querySelectorAll(selector));else{var elm=element.querySelector(selector);elm&&results.push(elm)}return results}}var _CACHED_BODY=null,_IS_WEBKIT=!1,matchesElement=_matches,containsElement=_contains,invokeQuery=_query,NoopAnimationDriver=function(){function NoopAnimationDriver(){}return NoopAnimationDriver.prototype.validateStyleProperty=function(prop){return validateStyleProperty(prop)},NoopAnimationDriver.prototype.matchesElement=function(element,selector){return matchesElement(element,selector)},NoopAnimationDriver.prototype.containsElement=function(elm1,elm2){return containsElement(elm1,elm2)},NoopAnimationDriver.prototype.query=function(element,selector,multi){return invokeQuery(element,selector,multi)},NoopAnimationDriver.prototype.computeStyle=function(element,prop,defaultValue){return defaultValue||""},NoopAnimationDriver.prototype.animate=function(element,keyframes,duration,delay,easing,previousPlayers){return void 0===previousPlayers&&(previousPlayers=[]),new _angular_animations.NoopAnimationPlayer},NoopAnimationDriver}(),AnimationDriver=function(){function AnimationDriver(){}return AnimationDriver.NOOP=new NoopAnimationDriver,AnimationDriver}(),ONE_SECOND=1e3,NG_TRIGGER_SELECTOR=".ng-trigger",NG_ANIMATING_SELECTOR=".ng-animating",PARAM_REGEX=new RegExp("{{\\s*(.+?)\\s*}}","g"),DASH_CASE_REGEXP=/-+([a-z0-9])/g,ANY_STATE="*",TRUE_BOOLEAN_VALUES=new Set(["true","1"]),FALSE_BOOLEAN_VALUES=new Set(["false","0"]),SELF_TOKEN=":self",SELF_TOKEN_REGEX=new RegExp("s*"+SELF_TOKEN+"s*,?","g"),AnimationAstBuilderVisitor=function(){function AnimationAstBuilderVisitor(_driver){this._driver=_driver}return AnimationAstBuilderVisitor.prototype.build=function(metadata,errors){var context=new AnimationAstBuilderContext(errors);return this._resetContextStyleTimingState(context),visitDslNode(this,normalizeAnimationEntry(metadata),context)},AnimationAstBuilderVisitor.prototype._resetContextStyleTimingState=function(context){context.currentQuerySelector="",context.collectedStyles={},context.collectedStyles[""]={},context.currentTime=0},AnimationAstBuilderVisitor.prototype.visitTrigger=function(metadata,context){var _this=this,queryCount=context.queryCount=0,depCount=context.depCount=0,states=[],transitions=[];return"@"==metadata.name.charAt(0)&&context.errors.push("animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))"),metadata.definitions.forEach(function(def){if(_this._resetContextStyleTimingState(context),0==def.type){var stateDef_1=def,name_1=stateDef_1.name;name_1.split(/\s*,\s*/).forEach(function(n){stateDef_1.name=n,states.push(_this.visitState(stateDef_1,context))}),stateDef_1.name=name_1}else if(1==def.type){var transition=_this.visitTransition(def,context);queryCount+=transition.queryCount,depCount+=transition.depCount,transitions.push(transition)}else context.errors.push("only state() and transition() definitions can sit inside of a trigger()")}),{type:7,name:metadata.name,states:states,transitions:transitions,queryCount:queryCount,depCount:depCount,options:null}},AnimationAstBuilderVisitor.prototype.visitState=function(metadata,context){var styleAst=this.visitStyle(metadata.styles,context),astParams=metadata.options&&metadata.options.params||null;if(styleAst.containsDynamicStyles){var missingSubs_1=new Set,params_1=astParams||{};if(styleAst.styles.forEach(function(value){if(isObject(value)){var stylesObj_1=value;Object.keys(stylesObj_1).forEach(function(prop){extractStyleParams(stylesObj_1[prop]).forEach(function(sub){params_1.hasOwnProperty(sub)||missingSubs_1.add(sub)})})}}),missingSubs_1.size){var missingSubsArr=iteratorToArray(missingSubs_1.values());context.errors.push('state("'+metadata.name+'", ...) must define default values for all the following style substitutions: '+missingSubsArr.join(", "))}}return{type:0,name:metadata.name,style:styleAst,options:astParams?{params:astParams}:null}},AnimationAstBuilderVisitor.prototype.visitTransition=function(metadata,context){context.queryCount=0,context.depCount=0;var animation=visitDslNode(this,normalizeAnimationEntry(metadata.animation),context);return{type:1,matchers:parseTransitionExpr(metadata.expr,context.errors),animation:animation,queryCount:context.queryCount,depCount:context.depCount,options:normalizeAnimationOptions(metadata.options)}},AnimationAstBuilderVisitor.prototype.visitSequence=function(metadata,context){var _this=this;return{type:2,steps:metadata.steps.map(function(s){return visitDslNode(_this,s,context)}),options:normalizeAnimationOptions(metadata.options)}},AnimationAstBuilderVisitor.prototype.visitGroup=function(metadata,context){var _this=this,currentTime=context.currentTime,furthestTime=0,steps=metadata.steps.map(function(step){context.currentTime=currentTime;var innerAst=visitDslNode(_this,step,context);return furthestTime=Math.max(furthestTime,context.currentTime),innerAst});return context.currentTime=furthestTime,{type:3,steps:steps,options:normalizeAnimationOptions(metadata.options)}},AnimationAstBuilderVisitor.prototype.visitAnimate=function(metadata,context){var timingAst=constructTimingAst(metadata.timings,context.errors);context.currentAnimateTimings=timingAst;var styleAst,styleMetadata=metadata.styles?metadata.styles:_angular_animations.style({});if(5==styleMetadata.type)styleAst=this.visitKeyframes(styleMetadata,context);else{var styleMetadata_1=metadata.styles,isEmpty=!1;if(!styleMetadata_1){isEmpty=!0;var newStyleData={};timingAst.easing&&(newStyleData.easing=timingAst.easing),styleMetadata_1=_angular_animations.style(newStyleData)}context.currentTime+=timingAst.duration+timingAst.delay;var _styleAst=this.visitStyle(styleMetadata_1,context);_styleAst.isEmptyStep=isEmpty,styleAst=_styleAst}return context.currentAnimateTimings=null,{type:4,timings:timingAst,style:styleAst,options:null}},AnimationAstBuilderVisitor.prototype.visitStyle=function(metadata,context){var ast=this._makeStyleAst(metadata,context);return this._validateStyleAst(ast,context),ast},AnimationAstBuilderVisitor.prototype._makeStyleAst=function(metadata,context){var styles=[];Array.isArray(metadata.styles)?metadata.styles.forEach(function(styleTuple){"string"==typeof styleTuple?styleTuple==_angular_animations.AUTO_STYLE?styles.push(styleTuple):context.errors.push("The provided style string value "+styleTuple+" is not allowed."):styles.push(styleTuple)}):styles.push(metadata.styles);var containsDynamicStyles=!1,collectedEasing=null;return styles.forEach(function(styleData){if(isObject(styleData)){var styleMap=styleData,easing=styleMap.easing;if(easing&&(collectedEasing=easing,delete styleMap.easing),!containsDynamicStyles)for(var prop in styleMap){var value=styleMap[prop];if(value.toString().indexOf("{{")>=0){containsDynamicStyles=!0;break}}}}),{type:6,styles:styles,easing:collectedEasing,offset:metadata.offset,containsDynamicStyles:containsDynamicStyles,options:null}},AnimationAstBuilderVisitor.prototype._validateStyleAst=function(ast,context){var _this=this,timings=context.currentAnimateTimings,endTime=context.currentTime,startTime=context.currentTime;timings&&startTime>0&&(startTime-=timings.duration+timings.delay),ast.styles.forEach(function(tuple){"string"!=typeof tuple&&Object.keys(tuple).forEach(function(prop){if(!_this._driver.validateStyleProperty(prop))return void context.errors.push('The provided animation property "'+prop+'" is not a supported CSS property for animations');var collectedStyles=context.collectedStyles[context.currentQuerySelector],collectedEntry=collectedStyles[prop],updateCollectedStyle=!0;collectedEntry&&(startTime!=endTime&&startTime>=collectedEntry.startTime&&endTime<=collectedEntry.endTime&&(context.errors.push('The CSS property "'+prop+'" that exists between the times of "'+collectedEntry.startTime+'ms" and "'+collectedEntry.endTime+'ms" is also being animated in a parallel animation between the times of "'+startTime+'ms" and "'+endTime+'ms"'),updateCollectedStyle=!1),startTime=collectedEntry.startTime),updateCollectedStyle&&(collectedStyles[prop]={startTime:startTime,endTime:endTime}),context.options&&validateStyleParams(tuple[prop],context.options,context.errors)})})},AnimationAstBuilderVisitor.prototype.visitKeyframes=function(metadata,context){var _this=this,ast={type:5,styles:[],options:null};if(!context.currentAnimateTimings)return context.errors.push("keyframes() must be placed inside of a call to animate()"),ast;var totalKeyframesWithOffsets=0,offsets=[],offsetsOutOfOrder=!1,keyframesOutOfRange=!1,previousOffset=0,keyframes=metadata.steps.map(function(styles){var style$$1=_this._makeStyleAst(styles,context),offsetVal=null!=style$$1.offset?style$$1.offset:consumeOffset(style$$1.styles),offset=0;return null!=offsetVal&&(totalKeyframesWithOffsets++,offset=style$$1.offset=offsetVal),keyframesOutOfRange=keyframesOutOfRange||offset<0||offset>1,offsetsOutOfOrder=offsetsOutOfOrder||offset<previousOffset,previousOffset=offset,offsets.push(offset),style$$1});keyframesOutOfRange&&context.errors.push("Please ensure that all keyframe offsets are between 0 and 1"),offsetsOutOfOrder&&context.errors.push("Please ensure that all keyframe offsets are in order");var length=metadata.steps.length,generatedOffset=0;totalKeyframesWithOffsets>0&&totalKeyframesWithOffsets<length?context.errors.push("Not all style() steps within the declared keyframes() contain offsets"):0==totalKeyframesWithOffsets&&(generatedOffset=1/(length-1));var limit=length-1,currentTime=context.currentTime,currentAnimateTimings=context.currentAnimateTimings,animateDuration=currentAnimateTimings.duration;return keyframes.forEach(function(kf,i){var offset=generatedOffset>0?i==limit?1:generatedOffset*i:offsets[i],durationUpToThisFrame=offset*animateDuration;context.currentTime=currentTime+currentAnimateTimings.delay+durationUpToThisFrame,currentAnimateTimings.duration=durationUpToThisFrame,_this._validateStyleAst(kf,context),kf.offset=offset,ast.styles.push(kf)}),ast},AnimationAstBuilderVisitor.prototype.visitReference=function(metadata,context){return{type:8,animation:visitDslNode(this,normalizeAnimationEntry(metadata.animation),context),options:normalizeAnimationOptions(metadata.options)}},AnimationAstBuilderVisitor.prototype.visitAnimateChild=function(metadata,context){return context.depCount++,{type:9,options:normalizeAnimationOptions(metadata.options)}},AnimationAstBuilderVisitor.prototype.visitAnimateRef=function(metadata,context){return{type:10,animation:this.visitReference(metadata.animation,context),options:normalizeAnimationOptions(metadata.options)}},AnimationAstBuilderVisitor.prototype.visitQuery=function(metadata,context){var parentSelector=context.currentQuerySelector,options=metadata.options||{};context.queryCount++,context.currentQuery=metadata;var _a=normalizeSelector(metadata.selector),selector=_a[0],includeSelf=_a[1];context.currentQuerySelector=parentSelector.length?parentSelector+" "+selector:selector,getOrSetAsInMap(context.collectedStyles,context.currentQuerySelector,{});var animation=visitDslNode(this,normalizeAnimationEntry(metadata.animation),context);return context.currentQuery=null,context.currentQuerySelector=parentSelector,{type:11,selector:selector,limit:options.limit||0,optional:!!options.optional,includeSelf:includeSelf,animation:animation,originalSelector:metadata.selector,options:normalizeAnimationOptions(metadata.options)}},AnimationAstBuilderVisitor.prototype.visitStagger=function(metadata,context){context.currentQuery||context.errors.push("stagger() can only be used inside of query()");var timings="full"===metadata.timings?{duration:0,delay:0,easing:"full"}:resolveTiming(metadata.timings,context.errors,!0);return{type:12,animation:visitDslNode(this,normalizeAnimationEntry(metadata.animation),context),timings:timings,options:null}},AnimationAstBuilderVisitor}(),AnimationAstBuilderContext=function(){function AnimationAstBuilderContext(errors){this.errors=errors,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles={},this.options=null}return AnimationAstBuilderContext}(),ElementInstructionMap=function(){function ElementInstructionMap(){this._map=new Map}return ElementInstructionMap.prototype.consume=function(element){var instructions=this._map.get(element);return instructions?this._map.delete(element):instructions=[],instructions},ElementInstructionMap.prototype.append=function(element,instructions){var existingInstructions=this._map.get(element);existingInstructions||this._map.set(element,existingInstructions=[]),existingInstructions.push.apply(existingInstructions,instructions)},ElementInstructionMap.prototype.has=function(element){return this._map.has(element)},ElementInstructionMap.prototype.clear=function(){this._map.clear()},ElementInstructionMap}(),ENTER_TOKEN_REGEX=new RegExp(":enter","g"),LEAVE_TOKEN_REGEX=new RegExp(":leave","g"),AnimationTimelineBuilderVisitor=function(){function AnimationTimelineBuilderVisitor(){}return AnimationTimelineBuilderVisitor.prototype.buildKeyframes=function(driver,rootElement,ast,enterClassName,leaveClassName,startingStyles,finalStyles,options,subInstructions,errors){void 0===errors&&(errors=[]),subInstructions=subInstructions||new ElementInstructionMap
;var context=new AnimationTimelineContext(driver,rootElement,subInstructions,enterClassName,leaveClassName,errors,[]);context.options=options,context.currentTimeline.setStyles([startingStyles],null,context.errors,options),visitDslNode(this,ast,context);var timelines=context.timelines.filter(function(timeline){return timeline.containsAnimation()});if(timelines.length&&Object.keys(finalStyles).length){var tl=timelines[timelines.length-1];tl.allowOnlyTimelineStyles()||tl.setStyles([finalStyles],null,context.errors,options)}return timelines.length?timelines.map(function(timeline){return timeline.buildKeyframes()}):[createTimelineInstruction(rootElement,[],[],[],0,0,"",!1)]},AnimationTimelineBuilderVisitor.prototype.visitTrigger=function(ast,context){},AnimationTimelineBuilderVisitor.prototype.visitState=function(ast,context){},AnimationTimelineBuilderVisitor.prototype.visitTransition=function(ast,context){},AnimationTimelineBuilderVisitor.prototype.visitAnimateChild=function(ast,context){var elementInstructions=context.subInstructions.consume(context.element);if(elementInstructions){var innerContext=context.createSubContext(ast.options),startTime=context.currentTimeline.currentTime,endTime=this._visitSubInstructions(elementInstructions,innerContext,innerContext.options);startTime!=endTime&&context.transformIntoNewTimeline(endTime)}context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype.visitAnimateRef=function(ast,context){var innerContext=context.createSubContext(ast.options);innerContext.transformIntoNewTimeline(),this.visitReference(ast.animation,innerContext),context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime),context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype._visitSubInstructions=function(instructions,context,options){var startTime=context.currentTimeline.currentTime,furthestTime=startTime,duration=null!=options.duration?resolveTimingValue(options.duration):null,delay=null!=options.delay?resolveTimingValue(options.delay):null;return 0!==duration&&instructions.forEach(function(instruction){var instructionTimings=context.appendInstructionToTimeline(instruction,duration,delay);furthestTime=Math.max(furthestTime,instructionTimings.duration+instructionTimings.delay)}),furthestTime},AnimationTimelineBuilderVisitor.prototype.visitReference=function(ast,context){context.updateOptions(ast.options,!0),visitDslNode(this,ast.animation,context),context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype.visitSequence=function(ast,context){var _this=this,subContextCount=context.subContextCount,ctx=context,options=ast.options;if(options&&(options.params||options.delay)&&(ctx=context.createSubContext(options),ctx.transformIntoNewTimeline(),null!=options.delay)){6==ctx.previousNode.type&&(ctx.currentTimeline.snapshotCurrentStyles(),ctx.previousNode=DEFAULT_NOOP_PREVIOUS_NODE);var delay=resolveTimingValue(options.delay);ctx.delayNextStep(delay)}ast.steps.length&&(ast.steps.forEach(function(s){return visitDslNode(_this,s,ctx)}),ctx.currentTimeline.applyStylesToKeyframe(),ctx.subContextCount>subContextCount&&ctx.transformIntoNewTimeline()),context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype.visitGroup=function(ast,context){var _this=this,innerTimelines=[],furthestTime=context.currentTimeline.currentTime,delay=ast.options&&ast.options.delay?resolveTimingValue(ast.options.delay):0;ast.steps.forEach(function(s){var innerContext=context.createSubContext(ast.options);delay&&innerContext.delayNextStep(delay),visitDslNode(_this,s,innerContext),furthestTime=Math.max(furthestTime,innerContext.currentTimeline.currentTime),innerTimelines.push(innerContext.currentTimeline)}),innerTimelines.forEach(function(timeline){return context.currentTimeline.mergeTimelineCollectedStyles(timeline)}),context.transformIntoNewTimeline(furthestTime),context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype._visitTiming=function(ast,context){if(ast.dynamic){var strValue=ast.strValue;return resolveTiming(context.params?interpolateParams(strValue,context.params,context.errors):strValue,context.errors)}return{duration:ast.duration,delay:ast.delay,easing:ast.easing}},AnimationTimelineBuilderVisitor.prototype.visitAnimate=function(ast,context){var timings=context.currentAnimateTimings=this._visitTiming(ast.timings,context),timeline=context.currentTimeline;timings.delay&&(context.incrementTime(timings.delay),timeline.snapshotCurrentStyles());var style$$1=ast.style;5==style$$1.type?this.visitKeyframes(style$$1,context):(context.incrementTime(timings.duration),this.visitStyle(style$$1,context),timeline.applyStylesToKeyframe()),context.currentAnimateTimings=null,context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype.visitStyle=function(ast,context){var timeline=context.currentTimeline,timings=context.currentAnimateTimings;!timings&&timeline.getCurrentStyleProperties().length&&timeline.forwardFrame();var easing=timings&&timings.easing||ast.easing;ast.isEmptyStep?timeline.applyEmptyStep(easing):timeline.setStyles(ast.styles,easing,context.errors,context.options),context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype.visitKeyframes=function(ast,context){var currentAnimateTimings=context.currentAnimateTimings,startTime=context.currentTimeline.duration,duration=currentAnimateTimings.duration,innerContext=context.createSubContext(),innerTimeline=innerContext.currentTimeline;innerTimeline.easing=currentAnimateTimings.easing,ast.styles.forEach(function(step){var offset=step.offset||0;innerTimeline.forwardTime(offset*duration),innerTimeline.setStyles(step.styles,step.easing,context.errors,context.options),innerTimeline.applyStylesToKeyframe()}),context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline),context.transformIntoNewTimeline(startTime+duration),context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype.visitQuery=function(ast,context){var _this=this,startTime=context.currentTimeline.currentTime,options=ast.options||{},delay=options.delay?resolveTimingValue(options.delay):0;delay&&(6===context.previousNode.type||0==startTime&&context.currentTimeline.getCurrentStyleProperties().length)&&(context.currentTimeline.snapshotCurrentStyles(),context.previousNode=DEFAULT_NOOP_PREVIOUS_NODE);var furthestTime=startTime,elms=context.invokeQuery(ast.selector,ast.originalSelector,ast.limit,ast.includeSelf,!!options.optional,context.errors);context.currentQueryTotal=elms.length;var sameElementTimeline=null;elms.forEach(function(element,i){context.currentQueryIndex=i;var innerContext=context.createSubContext(ast.options,element);delay&&innerContext.delayNextStep(delay),element===context.element&&(sameElementTimeline=innerContext.currentTimeline),visitDslNode(_this,ast.animation,innerContext),innerContext.currentTimeline.applyStylesToKeyframe();var endTime=innerContext.currentTimeline.currentTime;furthestTime=Math.max(furthestTime,endTime)}),context.currentQueryIndex=0,context.currentQueryTotal=0,context.transformIntoNewTimeline(furthestTime),sameElementTimeline&&(context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline),context.currentTimeline.snapshotCurrentStyles()),context.previousNode=ast},AnimationTimelineBuilderVisitor.prototype.visitStagger=function(ast,context){var parentContext=context.parentContext,tl=context.currentTimeline,timings=ast.timings,duration=Math.abs(timings.duration),maxTime=duration*(context.currentQueryTotal-1),delay=duration*context.currentQueryIndex;switch(timings.duration<0?"reverse":timings.easing){case"reverse":delay=maxTime-delay;break;case"full":delay=parentContext.currentStaggerTime}var timeline=context.currentTimeline;delay&&timeline.delayNextStep(delay);var startingTime=timeline.currentTime;visitDslNode(this,ast.animation,context),context.previousNode=ast,parentContext.currentStaggerTime=tl.currentTime-startingTime+(tl.startTime-parentContext.currentTimeline.startTime)},AnimationTimelineBuilderVisitor}(),DEFAULT_NOOP_PREVIOUS_NODE={},AnimationTimelineContext=function(){function AnimationTimelineContext(_driver,element,subInstructions,_enterClassName,_leaveClassName,errors,timelines,initialTimeline){this._driver=_driver,this.element=element,this.subInstructions=subInstructions,this._enterClassName=_enterClassName,this._leaveClassName=_leaveClassName,this.errors=errors,this.timelines=timelines,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=DEFAULT_NOOP_PREVIOUS_NODE,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=initialTimeline||new TimelineBuilder(this._driver,element,0),timelines.push(this.currentTimeline)}return Object.defineProperty(AnimationTimelineContext.prototype,"params",{get:function(){return this.options.params},enumerable:!0,configurable:!0}),AnimationTimelineContext.prototype.updateOptions=function(options,skipIfExists){var _this=this;if(options){var newOptions=options,optionsToUpdate=this.options;null!=newOptions.duration&&(optionsToUpdate.duration=resolveTimingValue(newOptions.duration)),null!=newOptions.delay&&(optionsToUpdate.delay=resolveTimingValue(newOptions.delay));var newParams=newOptions.params;if(newParams){var paramsToUpdate_1=optionsToUpdate.params;paramsToUpdate_1||(paramsToUpdate_1=this.options.params={}),Object.keys(newParams).forEach(function(name){skipIfExists&&paramsToUpdate_1.hasOwnProperty(name)||(paramsToUpdate_1[name]=interpolateParams(newParams[name],paramsToUpdate_1,_this.errors))})}}},AnimationTimelineContext.prototype._copyOptions=function(){var options={};if(this.options){var oldParams_1=this.options.params;if(oldParams_1){var params_1=options.params={};Object.keys(oldParams_1).forEach(function(name){params_1[name]=oldParams_1[name]})}}return options},AnimationTimelineContext.prototype.createSubContext=function(options,element,newTime){void 0===options&&(options=null);var target=element||this.element,context=new AnimationTimelineContext(this._driver,target,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(target,newTime||0));return context.previousNode=this.previousNode,context.currentAnimateTimings=this.currentAnimateTimings,context.options=this._copyOptions(),context.updateOptions(options),context.currentQueryIndex=this.currentQueryIndex,context.currentQueryTotal=this.currentQueryTotal,context.parentContext=this,this.subContextCount++,context},AnimationTimelineContext.prototype.transformIntoNewTimeline=function(newTime){return this.previousNode=DEFAULT_NOOP_PREVIOUS_NODE,this.currentTimeline=this.currentTimeline.fork(this.element,newTime),this.timelines.push(this.currentTimeline),this.currentTimeline},AnimationTimelineContext.prototype.appendInstructionToTimeline=function(instruction,duration,delay){var updatedTimings={duration:null!=duration?duration:instruction.duration,delay:this.currentTimeline.currentTime+(null!=delay?delay:0)+instruction.delay,easing:""},builder=new SubTimelineBuilder(this._driver,instruction.element,instruction.keyframes,instruction.preStyleProps,instruction.postStyleProps,updatedTimings,instruction.stretchStartingKeyframe);return this.timelines.push(builder),updatedTimings},AnimationTimelineContext.prototype.incrementTime=function(time){this.currentTimeline.forwardTime(this.currentTimeline.duration+time)},AnimationTimelineContext.prototype.delayNextStep=function(delay){delay>0&&this.currentTimeline.delayNextStep(delay)},AnimationTimelineContext.prototype.invokeQuery=function(selector,originalSelector,limit,includeSelf,optional,errors){var results=[];if(includeSelf&&results.push(this.element),selector.length>0){selector=selector.replace(ENTER_TOKEN_REGEX,"."+this._enterClassName),selector=selector.replace(LEAVE_TOKEN_REGEX,"."+this._leaveClassName);var multi=1!=limit,elements=this._driver.query(this.element,selector,multi);0!==limit&&(elements=limit<0?elements.slice(elements.length+limit,elements.length):elements.slice(0,limit)),results.push.apply(results,elements)}return optional||0!=results.length||errors.push('`query("'+originalSelector+'")` returned zero elements. (Use `query("'+originalSelector+'", { optional: true })` if you wish to allow this.)'),results},AnimationTimelineContext}(),TimelineBuilder=function(){function TimelineBuilder(_driver,element,startTime,_elementTimelineStylesLookup){this._driver=_driver,this.element=element,this.startTime=startTime,this._elementTimelineStylesLookup=_elementTimelineStylesLookup,this.duration=0,this._previousKeyframe={},this._currentKeyframe={},this._keyframes=new Map,this._styleSummary={},this._pendingStyles={},this._backFill={},this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._localTimelineStyles=Object.create(this._backFill,{}),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(element),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(element,this._localTimelineStyles)),this._loadKeyframe()}return TimelineBuilder.prototype.containsAnimation=function(){switch(this._keyframes.size){case 0:return!1;case 1:return this.getCurrentStyleProperties().length>0;default:return!0}},TimelineBuilder.prototype.getCurrentStyleProperties=function(){return Object.keys(this._currentKeyframe)},Object.defineProperty(TimelineBuilder.prototype,"currentTime",{get:function(){return this.startTime+this.duration},enumerable:!0,configurable:!0}),TimelineBuilder.prototype.delayNextStep=function(delay){var hasPreStyleStep=1==this._keyframes.size&&Object.keys(this._pendingStyles).length;this.duration||hasPreStyleStep?(this.forwardTime(this.currentTime+delay),hasPreStyleStep&&this.snapshotCurrentStyles()):this.startTime+=delay},TimelineBuilder.prototype.fork=function(element,currentTime){return this.applyStylesToKeyframe(),new TimelineBuilder(this._driver,element,currentTime||this.currentTime,this._elementTimelineStylesLookup)},TimelineBuilder.prototype._loadKeyframe=function(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=Object.create(this._backFill,{}),this._keyframes.set(this.duration,this._currentKeyframe))},TimelineBuilder.prototype.forwardFrame=function(){this.duration+=1,this._loadKeyframe()},TimelineBuilder.prototype.forwardTime=function(time){this.applyStylesToKeyframe(),this.duration=time,this._loadKeyframe()},TimelineBuilder.prototype._updateStyle=function(prop,value){this._localTimelineStyles[prop]=value,this._globalTimelineStyles[prop]=value,this._styleSummary[prop]={time:this.currentTime,value:value}},TimelineBuilder.prototype.allowOnlyTimelineStyles=function(){return this._currentEmptyStepKeyframe!==this._currentKeyframe},TimelineBuilder.prototype.applyEmptyStep=function(easing){var _this=this;easing&&(this._previousKeyframe.easing=easing),Object.keys(this._globalTimelineStyles).forEach(function(prop){_this._backFill[prop]=_this._globalTimelineStyles[prop]||_angular_animations.AUTO_STYLE,_this._currentKeyframe[prop]=_angular_animations.AUTO_STYLE}),this._currentEmptyStepKeyframe=this._currentKeyframe},TimelineBuilder.prototype.setStyles=function(input,easing,errors,options){var _this=this;easing&&(this._previousKeyframe.easing=easing);var params=options&&options.params||{},styles=flattenStyles(input,this._globalTimelineStyles);Object.keys(styles).forEach(function(prop){var val=interpolateParams(styles[prop],params,errors);_this._pendingStyles[prop]=val,_this._localTimelineStyles.hasOwnProperty(prop)||(_this._backFill[prop]=_this._globalTimelineStyles.hasOwnProperty(prop)?_this._globalTimelineStyles[prop]:_angular_animations.AUTO_STYLE),_this._updateStyle(prop,val)})},TimelineBuilder.prototype.applyStylesToKeyframe=function(){var _this=this,styles=this._pendingStyles,props=Object.keys(styles);0!=props.length&&(this._pendingStyles={},props.forEach(function(prop){var val=styles[prop];_this._currentKeyframe[prop]=val}),Object.keys(this._localTimelineStyles).forEach(function(prop){_this._currentKeyframe.hasOwnProperty(prop)||(_this._currentKeyframe[prop]=_this._localTimelineStyles[prop])}))},TimelineBuilder.prototype.snapshotCurrentStyles=function(){var _this=this;Object.keys(this._localTimelineStyles).forEach(function(prop){var val=_this._localTimelineStyles[prop];_this._pendingStyles[prop]=val,_this._updateStyle(prop,val)})},TimelineBuilder.prototype.getFinalKeyframe=function(){return this._keyframes.get(this.duration)},Object.defineProperty(TimelineBuilder.prototype,"properties",{get:function(){var properties=[];for(var prop in this._currentKeyframe)properties.push(prop);return properties},enumerable:!0,configurable:!0}),TimelineBuilder.prototype.mergeTimelineCollectedStyles=function(timeline){var _this=this;Object.keys(timeline._styleSummary).forEach(function(prop){var details0=_this._styleSummary[prop],details1=timeline._styleSummary[prop];(!details0||details1.time>details0.time)&&_this._updateStyle(prop,details1.value)})},TimelineBuilder.prototype.buildKeyframes=function(){var _this=this;this.applyStylesToKeyframe();var preStyleProps=new Set,postStyleProps=new Set,isEmpty=1===this._keyframes.size&&0===this.duration,finalKeyframes=[];this._keyframes.forEach(function(keyframe,time){var finalKeyframe=copyStyles(keyframe,!0);Object.keys(finalKeyframe).forEach(function(prop){var value=finalKeyframe[prop];value==_angular_animations.ɵPRE_STYLE?preStyleProps.add(prop):value==_angular_animations.AUTO_STYLE&&postStyleProps.add(prop)}),isEmpty||(finalKeyframe.offset=time/_this.duration),finalKeyframes.push(finalKeyframe)});var preProps=preStyleProps.size?iteratorToArray(preStyleProps.values()):[],postProps=postStyleProps.size?iteratorToArray(postStyleProps.values()):[];if(isEmpty){var kf0=finalKeyframes[0],kf1=copyObj(kf0);kf0.offset=0,kf1.offset=1,finalKeyframes=[kf0,kf1]}return createTimelineInstruction(this.element,finalKeyframes,preProps,postProps,this.duration,this.startTime,this.easing,!1)},TimelineBuilder}(),SubTimelineBuilder=function(_super){function SubTimelineBuilder(driver,element,keyframes,preStyleProps,postStyleProps,timings,_stretchStartingKeyframe){void 0===_stretchStartingKeyframe&&(_stretchStartingKeyframe=!1);var _this=_super.call(this,driver,element,timings.delay)||this;return _this.element=element,_this.keyframes=keyframes,_this.preStyleProps=preStyleProps,_this.postStyleProps=postStyleProps,_this._stretchStartingKeyframe=_stretchStartingKeyframe,_this.timings={duration:timings.duration,delay:timings.delay,easing:timings.easing},_this}return __extends(SubTimelineBuilder,_super),SubTimelineBuilder.prototype.containsAnimation=function(){return this.keyframes.length>1},SubTimelineBuilder.prototype.buildKeyframes=function(){var keyframes=this.keyframes,_a=this.timings,delay=_a.delay,duration=_a.duration,easing=_a.easing;if(this._stretchStartingKeyframe&&delay){var newKeyframes=[],totalTime=duration+delay,startingGap=delay/totalTime,newFirstKeyframe=copyStyles(keyframes[0],!1);newFirstKeyframe.offset=0,newKeyframes.push(newFirstKeyframe);var oldFirstKeyframe=copyStyles(keyframes[0],!1);oldFirstKeyframe.offset=roundOffset(startingGap),newKeyframes.push(oldFirstKeyframe);for(var limit=keyframes.length-1,i=1;i<=limit;i++){var kf=copyStyles(keyframes[i],!1),oldOffset=kf.offset,timeAtKeyframe=delay+oldOffset*duration;kf.offset=roundOffset(timeAtKeyframe/totalTime),newKeyframes.push(kf)}duration=totalTime,delay=0,easing="",keyframes=newKeyframes}return createTimelineInstruction(this.element,keyframes,this.preStyleProps,this.postStyleProps,duration,delay,easing,!0)},SubTimelineBuilder}(TimelineBuilder),Animation=function(){function Animation(_driver,input){this._driver=_driver;var errors=[],ast=buildAnimationAst(_driver,input,errors);if(errors.length){var errorMessage="animation validation failed:\n"+errors.join("\n");throw new Error(errorMessage)}this._animationAst=ast}return Animation.prototype.buildTimelines=function(element,startingStyles,destinationStyles,options,subInstructions){var start=Array.isArray(startingStyles)?normalizeStyles(startingStyles):startingStyles,dest=Array.isArray(destinationStyles)?normalizeStyles(destinationStyles):destinationStyles,errors=[];subInstructions=subInstructions||new ElementInstructionMap;var result=buildAnimationTimelines(this._driver,element,this._animationAst,"ng-enter","ng-leave",start,dest,options,subInstructions,errors);if(errors.length){var errorMessage="animation building failed:\n"+errors.join("\n");throw new Error(errorMessage)}return result},Animation}(),AnimationStyleNormalizer=function(){function AnimationStyleNormalizer(){}return AnimationStyleNormalizer}(),NoopAnimationStyleNormalizer=function(){function NoopAnimationStyleNormalizer(){}return NoopAnimationStyleNormalizer.prototype.normalizePropertyName=function(propertyName,errors){return propertyName},NoopAnimationStyleNormalizer.prototype.normalizeStyleValue=function(userProvidedProperty,normalizedProperty,value,errors){return value},NoopAnimationStyleNormalizer}(),WebAnimationsStyleNormalizer=function(_super){function WebAnimationsStyleNormalizer(){return null!==_super&&_super.apply(this,arguments)||this}return __extends(WebAnimationsStyleNormalizer,_super),WebAnimationsStyleNormalizer.prototype.normalizePropertyName=function(propertyName,errors){return dashCaseToCamelCase(propertyName)},WebAnimationsStyleNormalizer.prototype.normalizeStyleValue=function(userProvidedProperty,normalizedProperty,value,errors){var unit="",strVal=value.toString().trim();if(DIMENSIONAL_PROP_MAP[normalizedProperty]&&0!==value&&"0"!==value)if("number"==typeof value)unit="px";else{var valAndSuffixMatch=value.match(/^[+-]?[\d\.]+([a-z]*)$/);valAndSuffixMatch&&0==valAndSuffixMatch[1].length&&errors.push("Please provide a CSS unit value for "+userProvidedProperty+":"+value)}return strVal+unit},WebAnimationsStyleNormalizer}(AnimationStyleNormalizer),DIMENSIONAL_PROP_MAP=function(keys){var map={};return keys.forEach(function(key){return map[key]=!0}),map}("width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective".split(",")),EMPTY_OBJECT={},AnimationTransitionFactory=function(){function AnimationTransitionFactory(_triggerName,ast,_stateStyles){this._triggerName=_triggerName,this.ast=ast,this._stateStyles=_stateStyles}return AnimationTransitionFactory.prototype.match=function(currentState,nextState){return oneOrMoreTransitionsMatch(this.ast.matchers,currentState,nextState)},AnimationTransitionFactory.prototype.buildStyles=function(stateName,params,errors){var backupStateStyler=this._stateStyles["*"],stateStyler=this._stateStyles[stateName],backupStyles=backupStateStyler?backupStateStyler.buildStyles(params,errors):{};return stateStyler?stateStyler.buildStyles(params,errors):backupStyles},AnimationTransitionFactory.prototype.build=function(driver,element,currentState,nextState,enterClassName,leaveClassName,currentOptions,nextOptions,subInstructions){var errors=[],transitionAnimationParams=this.ast.options&&this.ast.options.params||EMPTY_OBJECT,currentAnimationParams=currentOptions&&currentOptions.params||EMPTY_OBJECT,currentStateStyles=this.buildStyles(currentState,currentAnimationParams,errors),nextAnimationParams=nextOptions&&nextOptions.params||EMPTY_OBJECT,nextStateStyles=this.buildStyles(nextState,nextAnimationParams,errors),queriedElements=new Set,preStyleMap=new Map,postStyleMap=new Map,isRemoval="void"===nextState,animationOptions={params:__assign({},transitionAnimationParams,nextAnimationParams)},timelines=buildAnimationTimelines(driver,element,this.ast.animation,enterClassName,leaveClassName,currentStateStyles,nextStateStyles,animationOptions,subInstructions,errors);if(errors.length)return createTransitionInstruction(element,this._triggerName,currentState,nextState,isRemoval,currentStateStyles,nextStateStyles,[],[],preStyleMap,postStyleMap,errors);timelines.forEach(function(tl){var elm=tl.element,preProps=getOrSetAsInMap(preStyleMap,elm,{});tl.preStyleProps.forEach(function(prop){return preProps[prop]=!0});var postProps=getOrSetAsInMap(postStyleMap,elm,{});tl.postStyleProps.forEach(function(prop){return postProps[prop]=!0}),elm!==element&&queriedElements.add(elm)});var queriedElementsList=iteratorToArray(queriedElements.values());return createTransitionInstruction(element,this._triggerName,currentState,nextState,isRemoval,currentStateStyles,nextStateStyles,timelines,queriedElementsList,preStyleMap,postStyleMap)},AnimationTransitionFactory}(),AnimationStateStyles=function(){function AnimationStateStyles(styles,defaultParams){this.styles=styles,this.defaultParams=defaultParams}return AnimationStateStyles.prototype.buildStyles=function(params,errors){var finalStyles={},combinedParams=copyObj(this.defaultParams);return Object.keys(params).forEach(function(key){var value=params[key];null!=value&&(combinedParams[key]=value)}),this.styles.styles.forEach(function(value){if("string"!=typeof value){var styleObj_1=value;Object.keys(styleObj_1).forEach(function(prop){var val=styleObj_1[prop];val.length>1&&(val=interpolateParams(val,combinedParams,errors)),finalStyles[prop]=val})}}),finalStyles},AnimationStateStyles}(),AnimationTrigger=function(){function AnimationTrigger(name,ast){var _this=this;this.name=name,this.ast=ast,this.transitionFactories=[],this.states={},ast.states.forEach(function(ast){var defaultParams=ast.options&&ast.options.params||{};_this.states[ast.name]=new AnimationStateStyles(ast.style,defaultParams)}),balanceProperties(this.states,"true","1"),balanceProperties(this.states,"false","0"),ast.transitions.forEach(function(ast){_this.transitionFactories.push(new AnimationTransitionFactory(name,ast,_this.states))}),this.fallbackTransition=createFallbackTransition(name,this.states)}return Object.defineProperty(AnimationTrigger.prototype,"containsQueries",{get:function(){return this.ast.queryCount>0},enumerable:!0,configurable:!0}),AnimationTrigger.prototype.matchTransition=function(currentState,nextState){return this.transitionFactories.find(function(f){return f.match(currentState,nextState)})||null},AnimationTrigger.prototype.matchStyles=function(currentState,params,errors){return this.fallbackTransition.buildStyles(currentState,params,errors)},AnimationTrigger}(),EMPTY_INSTRUCTION_MAP=new ElementInstructionMap,TimelineAnimationEngine=function(){function TimelineAnimationEngine(_driver,_normalizer){this._driver=_driver,this._normalizer=_normalizer,this._animations={},this._playersById={},this.players=[]}return TimelineAnimationEngine.prototype.register=function(id,metadata){var errors=[],ast=buildAnimationAst(this._driver,metadata,errors);if(errors.length)throw new Error("Unable to build the animation due to the following errors: "+errors.join("\n"));this._animations[id]=ast},TimelineAnimationEngine.prototype._buildPlayer=function(i,preStyles,postStyles){var element=i.element,keyframes=normalizeKeyframes(this._driver,this._normalizer,element,i.keyframes,preStyles,postStyles);return this._driver.animate(element,keyframes,i.duration,i.delay,i.easing,[])},TimelineAnimationEngine.prototype.create=function(id,element,options){var _this=this;void 0===options&&(options={});var instructions,errors=[],ast=this._animations[id],autoStylesMap=new Map;if(ast?(instructions=buildAnimationTimelines(this._driver,element,ast,"ng-enter","ng-leave",{},{},options,EMPTY_INSTRUCTION_MAP,errors),instructions.forEach(function(inst){var styles=getOrSetAsInMap(autoStylesMap,inst.element,{});inst.postStyleProps.forEach(function(prop){return styles[prop]=null})})):(errors.push("The requested animation doesn't exist or has already been destroyed"),instructions=[]),errors.length)throw new Error("Unable to create the animation due to the following errors: "+errors.join("\n"));autoStylesMap.forEach(function(styles,element){Object.keys(styles).forEach(function(prop){styles[prop]=_this._driver.computeStyle(element,prop,_angular_animations.AUTO_STYLE)})});var players=instructions.map(function(i){var styles=autoStylesMap.get(i.element);return _this._buildPlayer(i,{},styles)}),player=optimizeGroupPlayer(players);return this._playersById[id]=player,player.onDestroy(function(){return _this.destroy(id)}),this.players.push(player),player},TimelineAnimationEngine.prototype.destroy=function(id){var player=this._getPlayer(id);player.destroy(),delete this._playersById[id];var index=this.players.indexOf(player);index>=0&&this.players.splice(index,1)},TimelineAnimationEngine.prototype._getPlayer=function(id){var player=this._playersById[id];if(!player)throw new Error("Unable to find the timeline player referenced by "+id);return player},TimelineAnimationEngine.prototype.listen=function(id,element,eventName,callback){var baseEvent=makeAnimationEvent(element,"","","");return listenOnPlayer(this._getPlayer(id),eventName,baseEvent,callback),function(){}},TimelineAnimationEngine.prototype.command=function(id,element,command,args){if("register"==command)return void this.register(id,args[0]);if("create"==command){var options=args[0]||{};return void this.create(id,element,options)}var player=this._getPlayer(id);switch(command){case"play":player.play();break;case"pause":player.pause();break;case"reset":player.reset();break;case"restart":player.restart();break;case"finish":player.finish();break;case"init":player.init();break;case"setPosition":player.setPosition(parseFloat(args[0]));break;case"destroy":this.destroy(id)}},TimelineAnimationEngine}(),EMPTY_PLAYER_ARRAY=[],NULL_REMOVAL_STATE={namespaceId:"",setForRemoval:null,hasAnimation:!1,removedBeforeQueried:!1},NULL_REMOVED_QUERIED_STATE={namespaceId:"",setForRemoval:null,hasAnimation:!1,removedBeforeQueried:!0},REMOVAL_FLAG="__ng_removed",StateValue=function(){function StateValue(input,namespaceId){void 0===namespaceId&&(namespaceId=""),this.namespaceId=namespaceId;var isObj=input&&input.hasOwnProperty("value"),value=isObj?input.value:input;if(this.value=normalizeTriggerValue(value),isObj){var options=copyObj(input);delete options.value,this.options=options}else this.options={};this.options.params||(this.options.params={})}return Object.defineProperty(StateValue.prototype,"params",{get:function(){return this.options.params},enumerable:!0,configurable:!0}),StateValue.prototype.absorbOptions=function(options){var newParams=options.params;if(newParams){var oldParams_1=this.options.params;Object.keys(newParams).forEach(function(prop){null==oldParams_1[prop]&&(oldParams_1[prop]=newParams[prop])})}},StateValue}(),DEFAULT_STATE_VALUE=new StateValue("void"),DELETED_STATE_VALUE=new StateValue("DELETED"),AnimationTransitionNamespace=function(){function AnimationTransitionNamespace(id,hostElement,_engine){this.id=id,this.hostElement=hostElement,this._engine=_engine,this.players=[],this._triggers={},this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+id,addClass(hostElement,this._hostClassName)}return AnimationTransitionNamespace.prototype.listen=function(element,name,phase,callback){var _this=this;if(!this._triggers.hasOwnProperty(name))throw new Error('Unable to listen on the animation trigger event "'+phase+'" because the animation trigger "'+name+"\" doesn't exist!");if(null==phase||0==phase.length)throw new Error('Unable to listen on the animation trigger "'+name+'" because the provided event is undefined!');if(!isTriggerEventValid(phase))throw new Error('The provided animation trigger event "'+phase+'" for the animation trigger "'+name+'" is not supported!');var listeners=getOrSetAsInMap(this._elementListeners,element,[]),data={name:name,phase:phase,callback:callback};listeners.push(data)
;var triggersWithStates=getOrSetAsInMap(this._engine.statesByElement,element,{});return triggersWithStates.hasOwnProperty(name)||(addClass(element,"ng-trigger"),addClass(element,"ng-trigger-"+name),triggersWithStates[name]=DEFAULT_STATE_VALUE),function(){_this._engine.afterFlush(function(){var index=listeners.indexOf(data);index>=0&&listeners.splice(index,1),_this._triggers[name]||delete triggersWithStates[name]})}},AnimationTransitionNamespace.prototype.register=function(name,ast){return!this._triggers[name]&&(this._triggers[name]=ast,!0)},AnimationTransitionNamespace.prototype._getTrigger=function(name){var trigger=this._triggers[name];if(!trigger)throw new Error('The provided animation trigger "'+name+'" has not been registered!');return trigger},AnimationTransitionNamespace.prototype.trigger=function(element,triggerName,value,defaultToFallback){var _this=this;void 0===defaultToFallback&&(defaultToFallback=!0);var trigger=this._getTrigger(triggerName),player=new TransitionAnimationPlayer(this.id,triggerName,element),triggersWithStates=this._engine.statesByElement.get(element);triggersWithStates||(addClass(element,"ng-trigger"),addClass(element,"ng-trigger-"+triggerName),this._engine.statesByElement.set(element,triggersWithStates={}));var fromState=triggersWithStates[triggerName],toState=new StateValue(value,this.id);if(!(value&&value.hasOwnProperty("value"))&&fromState&&toState.absorbOptions(fromState.options),triggersWithStates[triggerName]=toState,fromState){if(fromState===DELETED_STATE_VALUE)return player}else fromState=DEFAULT_STATE_VALUE;if("void"===toState.value||fromState.value!==toState.value){var playersOnElement=getOrSetAsInMap(this._engine.playersByElement,element,[]);playersOnElement.forEach(function(player){player.namespaceId==_this.id&&player.triggerName==triggerName&&player.queued&&player.destroy()});var transition=trigger.matchTransition(fromState.value,toState.value),isFallbackTransition=!1;if(!transition){if(!defaultToFallback)return;transition=trigger.fallbackTransition,isFallbackTransition=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:element,triggerName:triggerName,transition:transition,fromState:fromState,toState:toState,player:player,isFallbackTransition:isFallbackTransition}),isFallbackTransition||(addClass(element,"ng-animate-queued"),player.onStart(function(){removeClass(element,"ng-animate-queued")})),player.onDone(function(){var index=_this.players.indexOf(player);index>=0&&_this.players.splice(index,1);var players=_this._engine.playersByElement.get(element);if(players){var index_1=players.indexOf(player);index_1>=0&&players.splice(index_1,1)}}),this.players.push(player),playersOnElement.push(player),player}if(!objEquals(fromState.params,toState.params)){var errors=[],fromStyles_1=trigger.matchStyles(fromState.value,fromState.params,errors),toStyles_1=trigger.matchStyles(toState.value,toState.params,errors);errors.length?this._engine.reportError(errors):this._engine.afterFlush(function(){eraseStyles(element,fromStyles_1),setStyles(element,toStyles_1)})}},AnimationTransitionNamespace.prototype.deregister=function(name){var _this=this;delete this._triggers[name],this._engine.statesByElement.forEach(function(stateMap,element){delete stateMap[name]}),this._elementListeners.forEach(function(listeners,element){_this._elementListeners.set(element,listeners.filter(function(entry){return entry.name!=name}))})},AnimationTransitionNamespace.prototype.clearElementCache=function(element){this._engine.statesByElement.delete(element),this._elementListeners.delete(element);var elementPlayers=this._engine.playersByElement.get(element);elementPlayers&&(elementPlayers.forEach(function(player){return player.destroy()}),this._engine.playersByElement.delete(element))},AnimationTransitionNamespace.prototype._signalRemovalForInnerTriggers=function(rootElement,context,animate){var _this=this;void 0===animate&&(animate=!1),this._engine.driver.query(rootElement,NG_TRIGGER_SELECTOR,!0).forEach(function(elm){if(!elm[REMOVAL_FLAG]){var namespaces=_this._engine.fetchNamespacesByElement(elm);namespaces.size?namespaces.forEach(function(ns){return ns.triggerLeaveAnimation(elm,context,!1,!0)}):_this.clearElementCache(elm)}})},AnimationTransitionNamespace.prototype.triggerLeaveAnimation=function(element,context,destroyAfterComplete,defaultToFallback){var _this=this,triggerStates=this._engine.statesByElement.get(element);if(triggerStates){var players_1=[];if(Object.keys(triggerStates).forEach(function(triggerName){if(_this._triggers[triggerName]){var player=_this.trigger(element,triggerName,"void",defaultToFallback);player&&players_1.push(player)}}),players_1.length)return this._engine.markElementAsRemoved(this.id,element,!0,context),destroyAfterComplete&&optimizeGroupPlayer(players_1).onDone(function(){return _this._engine.processLeaveNode(element)}),!0}return!1},AnimationTransitionNamespace.prototype.prepareLeaveAnimationListeners=function(element){var _this=this,listeners=this._elementListeners.get(element);if(listeners){var visitedTriggers_1=new Set;listeners.forEach(function(listener){var triggerName=listener.name;if(!visitedTriggers_1.has(triggerName)){visitedTriggers_1.add(triggerName);var trigger=_this._triggers[triggerName],transition=trigger.fallbackTransition,elementStates=_this._engine.statesByElement.get(element),fromState=elementStates[triggerName]||DEFAULT_STATE_VALUE,toState=new StateValue("void"),player=new TransitionAnimationPlayer(_this.id,triggerName,element);_this._engine.totalQueuedPlayers++,_this._queue.push({element:element,triggerName:triggerName,transition:transition,fromState:fromState,toState:toState,player:player,isFallbackTransition:!0})}})}},AnimationTransitionNamespace.prototype.removeNode=function(element,context){var _this=this,engine=this._engine;if(element.childElementCount&&this._signalRemovalForInnerTriggers(element,context,!0),!this.triggerLeaveAnimation(element,context,!0)){var containsPotentialParentTransition=!1;if(engine.totalAnimations){var currentPlayers=engine.players.length?engine.playersByQueriedElement.get(element):[];if(currentPlayers&&currentPlayers.length)containsPotentialParentTransition=!0;else for(var parent_1=element;parent_1=parent_1.parentNode;){var triggers=engine.statesByElement.get(parent_1);if(triggers){containsPotentialParentTransition=!0;break}}}this.prepareLeaveAnimationListeners(element),containsPotentialParentTransition?engine.markElementAsRemoved(this.id,element,!1,context):(engine.afterFlush(function(){return _this.clearElementCache(element)}),engine.destroyInnerAnimations(element),engine._onRemovalComplete(element,context))}},AnimationTransitionNamespace.prototype.insertNode=function(element,parent){addClass(element,this._hostClassName)},AnimationTransitionNamespace.prototype.drainQueuedTransitions=function(microtaskId){var _this=this,instructions=[];return this._queue.forEach(function(entry){var player=entry.player;if(!player.destroyed){var element=entry.element,listeners=_this._elementListeners.get(element);listeners&&listeners.forEach(function(listener){if(listener.name==entry.triggerName){var baseEvent=makeAnimationEvent(element,entry.triggerName,entry.fromState.value,entry.toState.value);baseEvent._data=microtaskId,listenOnPlayer(entry.player,listener.phase,baseEvent,listener.callback)}}),player.markedForDestroy?_this._engine.afterFlush(function(){player.destroy()}):instructions.push(entry)}}),this._queue=[],instructions.sort(function(a,b){var d0=a.transition.ast.depCount,d1=b.transition.ast.depCount;return 0==d0||0==d1?d0-d1:_this._engine.driver.containsElement(a.element,b.element)?1:-1})},AnimationTransitionNamespace.prototype.destroy=function(context){this.players.forEach(function(p){return p.destroy()}),this._signalRemovalForInnerTriggers(this.hostElement,context)},AnimationTransitionNamespace.prototype.elementContainsData=function(element){var containsData=!1;return this._elementListeners.has(element)&&(containsData=!0),containsData=!!this._queue.find(function(entry){return entry.element===element})||containsData},AnimationTransitionNamespace}(),TransitionAnimationEngine=function(){function TransitionAnimationEngine(driver,_normalizer){this.driver=driver,this._normalizer=_normalizer,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=function(element,context){}}return TransitionAnimationEngine.prototype._onRemovalComplete=function(element,context){this.onRemovalComplete(element,context)},Object.defineProperty(TransitionAnimationEngine.prototype,"queuedPlayers",{get:function(){var players=[];return this._namespaceList.forEach(function(ns){ns.players.forEach(function(player){player.queued&&players.push(player)})}),players},enumerable:!0,configurable:!0}),TransitionAnimationEngine.prototype.createNamespace=function(namespaceId,hostElement){var ns=new AnimationTransitionNamespace(namespaceId,hostElement,this);return hostElement.parentNode?this._balanceNamespaceList(ns,hostElement):(this.newHostElements.set(hostElement,ns),this.collectEnterElement(hostElement)),this._namespaceLookup[namespaceId]=ns},TransitionAnimationEngine.prototype._balanceNamespaceList=function(ns,hostElement){var limit=this._namespaceList.length-1;if(limit>=0){for(var found=!1,i=limit;i>=0;i--){var nextNamespace=this._namespaceList[i];if(this.driver.containsElement(nextNamespace.hostElement,hostElement)){this._namespaceList.splice(i+1,0,ns),found=!0;break}}found||this._namespaceList.splice(0,0,ns)}else this._namespaceList.push(ns);return this.namespacesByHostElement.set(hostElement,ns),ns},TransitionAnimationEngine.prototype.register=function(namespaceId,hostElement){var ns=this._namespaceLookup[namespaceId];return ns||(ns=this.createNamespace(namespaceId,hostElement)),ns},TransitionAnimationEngine.prototype.registerTrigger=function(namespaceId,name,trigger){var ns=this._namespaceLookup[namespaceId];ns&&ns.register(name,trigger)&&this.totalAnimations++},TransitionAnimationEngine.prototype.destroy=function(namespaceId,context){var _this=this;if(namespaceId){var ns=this._fetchNamespace(namespaceId);this.afterFlush(function(){_this.namespacesByHostElement.delete(ns.hostElement),delete _this._namespaceLookup[namespaceId];var index=_this._namespaceList.indexOf(ns);index>=0&&_this._namespaceList.splice(index,1)}),this.afterFlushAnimationsDone(function(){return ns.destroy(context)})}},TransitionAnimationEngine.prototype._fetchNamespace=function(id){return this._namespaceLookup[id]},TransitionAnimationEngine.prototype.fetchNamespacesByElement=function(element){var namespaces=new Set,elementStates=this.statesByElement.get(element);if(elementStates)for(var keys=Object.keys(elementStates),i=0;i<keys.length;i++){var nsId=elementStates[keys[i]].namespaceId;if(nsId){var ns=this._fetchNamespace(nsId);ns&&namespaces.add(ns)}}return namespaces},TransitionAnimationEngine.prototype.trigger=function(namespaceId,element,name,value){return!!isElementNode(element)&&(this._fetchNamespace(namespaceId).trigger(element,name,value),!0)},TransitionAnimationEngine.prototype.insertNode=function(namespaceId,element,parent,insertBefore){if(isElementNode(element)){var details=element[REMOVAL_FLAG];if(details&&details.setForRemoval&&(details.setForRemoval=!1),namespaceId){var ns=this._fetchNamespace(namespaceId);ns&&ns.insertNode(element,parent)}insertBefore&&this.collectEnterElement(element)}},TransitionAnimationEngine.prototype.collectEnterElement=function(element){this.collectedEnterElements.push(element)},TransitionAnimationEngine.prototype.markElementAsDisabled=function(element,value){value?this.disabledNodes.has(element)||(this.disabledNodes.add(element),addClass(element,"ng-animate-disabled")):this.disabledNodes.has(element)&&(this.disabledNodes.delete(element),removeClass(element,"ng-animate-disabled"))},TransitionAnimationEngine.prototype.removeNode=function(namespaceId,element,context){if(!isElementNode(element))return void this._onRemovalComplete(element,context);var ns=namespaceId?this._fetchNamespace(namespaceId):null;ns?ns.removeNode(element,context):this.markElementAsRemoved(namespaceId,element,!1,context)},TransitionAnimationEngine.prototype.markElementAsRemoved=function(namespaceId,element,hasAnimation,context){this.collectedLeaveElements.push(element),element[REMOVAL_FLAG]={namespaceId:namespaceId,setForRemoval:context,hasAnimation:hasAnimation,removedBeforeQueried:!1}},TransitionAnimationEngine.prototype.listen=function(namespaceId,element,name,phase,callback){return isElementNode(element)?this._fetchNamespace(namespaceId).listen(element,name,phase,callback):function(){}},TransitionAnimationEngine.prototype._buildInstruction=function(entry,subTimelines,enterClassName,leaveClassName){return entry.transition.build(this.driver,entry.element,entry.fromState.value,entry.toState.value,enterClassName,leaveClassName,entry.fromState.options,entry.toState.options,subTimelines)},TransitionAnimationEngine.prototype.destroyInnerAnimations=function(containerElement){var _this=this,elements=this.driver.query(containerElement,NG_TRIGGER_SELECTOR,!0);elements.forEach(function(element){return _this.destroyActiveAnimationsForElement(element)}),0!=this.playersByQueriedElement.size&&(elements=this.driver.query(containerElement,NG_ANIMATING_SELECTOR,!0),elements.forEach(function(element){return _this.finishActiveQueriedAnimationOnElement(element)}))},TransitionAnimationEngine.prototype.destroyActiveAnimationsForElement=function(element){var players=this.playersByElement.get(element);players&&players.forEach(function(player){player.queued?player.markedForDestroy=!0:player.destroy()});var stateMap=this.statesByElement.get(element);stateMap&&Object.keys(stateMap).forEach(function(triggerName){return stateMap[triggerName]=DELETED_STATE_VALUE})},TransitionAnimationEngine.prototype.finishActiveQueriedAnimationOnElement=function(element){var players=this.playersByQueriedElement.get(element);players&&players.forEach(function(player){return player.finish()})},TransitionAnimationEngine.prototype.whenRenderingDone=function(){var _this=this;return new Promise(function(resolve){if(_this.players.length)return optimizeGroupPlayer(_this.players).onDone(function(){return resolve()});resolve()})},TransitionAnimationEngine.prototype.processLeaveNode=function(element){var _this=this,details=element[REMOVAL_FLAG];if(details&&details.setForRemoval){if(element[REMOVAL_FLAG]=NULL_REMOVAL_STATE,details.namespaceId){this.destroyInnerAnimations(element);var ns=this._fetchNamespace(details.namespaceId);ns&&ns.clearElementCache(element)}this._onRemovalComplete(element,details.setForRemoval)}this.driver.matchesElement(element,".ng-animate-disabled")&&this.markElementAsDisabled(element,!1),this.driver.query(element,".ng-animate-disabled",!0).forEach(function(node){_this.markElementAsDisabled(element,!1)})},TransitionAnimationEngine.prototype.flush=function(microtaskId){var _this=this;void 0===microtaskId&&(microtaskId=-1);var players=[];if(this.newHostElements.size&&(this.newHostElements.forEach(function(ns,element){return _this._balanceNamespaceList(ns,element)}),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(var i=0;i<this.collectedEnterElements.length;i++){var elm=this.collectedEnterElements[i];addClass(elm,"ng-star-inserted")}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){var cleanupFns=[];try{players=this._flushAnimations(cleanupFns,microtaskId)}finally{for(var i=0;i<cleanupFns.length;i++)cleanupFns[i]()}}else for(var i=0;i<this.collectedLeaveElements.length;i++){var element=this.collectedLeaveElements[i];this.processLeaveNode(element)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(function(fn){return fn()}),this._flushFns=[],this._whenQuietFns.length){var quietFns_1=this._whenQuietFns;this._whenQuietFns=[],players.length?optimizeGroupPlayer(players).onDone(function(){quietFns_1.forEach(function(fn){return fn()})}):quietFns_1.forEach(function(fn){return fn()})}},TransitionAnimationEngine.prototype.reportError=function(errors){throw new Error("Unable to process animations due to the following failed trigger transitions\n "+errors.join("\n"))},TransitionAnimationEngine.prototype._flushAnimations=function(cleanupFns,microtaskId){var _this=this,subTimelines=new ElementInstructionMap,skippedPlayers=[],skippedPlayersMap=new Map,queuedInstructions=[],queriedElements=new Map,allPreStyleElements=new Map,allPostStyleElements=new Map,disabledElementsSet=new Set;this.disabledNodes.forEach(function(node){disabledElementsSet.add(node);for(var nodesThatAreDisabled=_this.driver.query(node,".ng-animate-queued",!0),i_1=0;i_1<nodesThatAreDisabled.length;i_1++)disabledElementsSet.add(nodesThatAreDisabled[i_1])});var bodyNode=getBodyNode(),allTriggerElements=Array.from(this.statesByElement.keys()),enterNodeMap=buildRootMap(allTriggerElements,this.collectedEnterElements),enterNodeMapIds=new Map,i=0;enterNodeMap.forEach(function(nodes,root){var className="ng-enter"+i++;enterNodeMapIds.set(root,className),nodes.forEach(function(node){return addClass(node,className)})});for(var allLeaveNodes=[],mergedLeaveNodes=new Set,leaveNodesWithoutAnimations=new Set,i_2=0;i_2<this.collectedLeaveElements.length;i_2++){var element=this.collectedLeaveElements[i_2],details=element[REMOVAL_FLAG];details&&details.setForRemoval&&(allLeaveNodes.push(element),mergedLeaveNodes.add(element),details.hasAnimation?this.driver.query(element,".ng-star-inserted",!0).forEach(function(elm){return mergedLeaveNodes.add(elm)}):leaveNodesWithoutAnimations.add(element))}var leaveNodeMapIds=new Map,leaveNodeMap=buildRootMap(allTriggerElements,Array.from(mergedLeaveNodes));leaveNodeMap.forEach(function(nodes,root){var className="ng-leave"+i++;leaveNodeMapIds.set(root,className),nodes.forEach(function(node){return addClass(node,className)})}),cleanupFns.push(function(){enterNodeMap.forEach(function(nodes,root){var className=enterNodeMapIds.get(root);nodes.forEach(function(node){return removeClass(node,className)})}),leaveNodeMap.forEach(function(nodes,root){var className=leaveNodeMapIds.get(root);nodes.forEach(function(node){return removeClass(node,className)})}),allLeaveNodes.forEach(function(element){_this.processLeaveNode(element)})});for(var allPlayers=[],erroneousTransitions=[],i_3=this._namespaceList.length-1;i_3>=0;i_3--){this._namespaceList[i_3].drainQueuedTransitions(microtaskId).forEach(function(entry){var player=entry.player;allPlayers.push(player);var element=entry.element;if(!bodyNode||!_this.driver.containsElement(bodyNode,element))return void player.destroy();var leaveClassName=leaveNodeMapIds.get(element),enterClassName=enterNodeMapIds.get(element),instruction=_this._buildInstruction(entry,subTimelines,enterClassName,leaveClassName);if(instruction.errors&&instruction.errors.length)return void erroneousTransitions.push(instruction);if(entry.isFallbackTransition)return player.onStart(function(){return eraseStyles(element,instruction.fromStyles)}),player.onDestroy(function(){return setStyles(element,instruction.toStyles)}),void skippedPlayers.push(player);instruction.timelines.forEach(function(tl){return tl.stretchStartingKeyframe=!0}),subTimelines.append(element,instruction.timelines);var tuple={instruction:instruction,player:player,element:element};queuedInstructions.push(tuple),instruction.queriedElements.forEach(function(element){return getOrSetAsInMap(queriedElements,element,[]).push(player)}),instruction.preStyleProps.forEach(function(stringMap,element){var props=Object.keys(stringMap);if(props.length){var setVal_1=allPreStyleElements.get(element);setVal_1||allPreStyleElements.set(element,setVal_1=new Set),props.forEach(function(prop){return setVal_1.add(prop)})}}),instruction.postStyleProps.forEach(function(stringMap,element){var props=Object.keys(stringMap),setVal=allPostStyleElements.get(element);setVal||allPostStyleElements.set(element,setVal=new Set),props.forEach(function(prop){return setVal.add(prop)})})})}if(erroneousTransitions.length){var errors_1=[];erroneousTransitions.forEach(function(instruction){errors_1.push("@"+instruction.triggerName+" has failed due to:\n"),instruction.errors.forEach(function(error){return errors_1.push("- "+error+"\n")})}),allPlayers.forEach(function(player){return player.destroy()}),this.reportError(errors_1)}var allPreviousPlayersMap=new Map,animationElementMap=new Map;queuedInstructions.forEach(function(entry){var element=entry.element;subTimelines.has(element)&&(animationElementMap.set(element,element),_this._beforeAnimationBuild(entry.player.namespaceId,entry.instruction,allPreviousPlayersMap))}),skippedPlayers.forEach(function(player){var element=player.element;_this._getPreviousPlayers(element,!1,player.namespaceId,player.triggerName,null).forEach(function(prevPlayer){getOrSetAsInMap(allPreviousPlayersMap,element,[]).push(prevPlayer),prevPlayer.destroy()})});var replaceNodes=allLeaveNodes.filter(function(node){return replacePostStylesAsPre(node,allPreStyleElements,allPostStyleElements)}),postStylesMap=new Map;cloakAndComputeStyles(postStylesMap,this.driver,leaveNodesWithoutAnimations,allPostStyleElements,_angular_animations.AUTO_STYLE).forEach(function(node){replacePostStylesAsPre(node,allPreStyleElements,allPostStyleElements)&&replaceNodes.push(node)});var preStylesMap=new Map;enterNodeMap.forEach(function(nodes,root){cloakAndComputeStyles(preStylesMap,_this.driver,new Set(nodes),allPreStyleElements,_angular_animations.ɵPRE_STYLE)}),replaceNodes.forEach(function(node){var post=postStylesMap.get(node),pre=preStylesMap.get(node);postStylesMap.set(node,__assign({},post,pre))});var rootPlayers=[],subPlayers=[],NO_PARENT_ANIMATION_ELEMENT_DETECTED={};queuedInstructions.forEach(function(entry){var element=entry.element,player=entry.player,instruction=entry.instruction;if(subTimelines.has(element)){if(disabledElementsSet.has(element))return player.onDestroy(function(){return setStyles(element,instruction.toStyles)}),void skippedPlayers.push(player);var parentWithAnimation_1=NO_PARENT_ANIMATION_ELEMENT_DETECTED;if(animationElementMap.size>1){for(var elm=element,parentsToAdd=[];elm=elm.parentNode;){var detectedParent=animationElementMap.get(elm);if(detectedParent){parentWithAnimation_1=detectedParent;break}parentsToAdd.push(elm)}parentsToAdd.forEach(function(parent){return animationElementMap.set(parent,parentWithAnimation_1)})}var innerPlayer=_this._buildAnimation(player.namespaceId,instruction,allPreviousPlayersMap,skippedPlayersMap,preStylesMap,postStylesMap);if(player.setRealPlayer(innerPlayer),parentWithAnimation_1===NO_PARENT_ANIMATION_ELEMENT_DETECTED)rootPlayers.push(player);else{var parentPlayers=_this.playersByElement.get(parentWithAnimation_1);parentPlayers&&parentPlayers.length&&(player.parentPlayer=optimizeGroupPlayer(parentPlayers)),skippedPlayers.push(player)}}else eraseStyles(element,instruction.fromStyles),player.onDestroy(function(){return setStyles(element,instruction.toStyles)}),subPlayers.push(player),disabledElementsSet.has(element)&&skippedPlayers.push(player)}),subPlayers.forEach(function(player){var playersForElement=skippedPlayersMap.get(player.element);if(playersForElement&&playersForElement.length){var innerPlayer=optimizeGroupPlayer(playersForElement);player.setRealPlayer(innerPlayer)}}),skippedPlayers.forEach(function(player){player.parentPlayer?player.syncPlayerEvents(player.parentPlayer):player.destroy()});for(var i_4=0;i_4<allLeaveNodes.length;i_4++){var element=allLeaveNodes[i_4],details=element[REMOVAL_FLAG];if(removeClass(element,"ng-leave"),!details||!details.hasAnimation){var players=[];if(queriedElements.size){var queriedPlayerResults=queriedElements.get(element);queriedPlayerResults&&queriedPlayerResults.length&&players.push.apply(players,queriedPlayerResults);for(var queriedInnerElements=this.driver.query(element,NG_ANIMATING_SELECTOR,!0),j=0;j<queriedInnerElements.length;j++){var queriedPlayers=queriedElements.get(queriedInnerElements[j]);queriedPlayers&&queriedPlayers.length&&players.push.apply(players,queriedPlayers)}}var activePlayers=players.filter(function(p){return!p.destroyed});activePlayers.length?removeNodesAfterAnimationDone(this,element,activePlayers):this.processLeaveNode(element)}}return allLeaveNodes.length=0,rootPlayers.forEach(function(player){_this.players.push(player),player.onDone(function(){player.destroy();var index=_this.players.indexOf(player);_this.players.splice(index,1)}),player.play()}),rootPlayers},TransitionAnimationEngine.prototype.elementContainsData=function(namespaceId,element){var containsData=!1,details=element[REMOVAL_FLAG];return details&&details.setForRemoval&&(containsData=!0),this.playersByElement.has(element)&&(containsData=!0),this.playersByQueriedElement.has(element)&&(containsData=!0),this.statesByElement.has(element)&&(containsData=!0),this._fetchNamespace(namespaceId).elementContainsData(element)||containsData},TransitionAnimationEngine.prototype.afterFlush=function(callback){this._flushFns.push(callback)},TransitionAnimationEngine.prototype.afterFlushAnimationsDone=function(callback){this._whenQuietFns.push(callback)},TransitionAnimationEngine.prototype._getPreviousPlayers=function(element,isQueriedElement,namespaceId,triggerName,toStateValue){var players=[];if(isQueriedElement){var queriedElementPlayers=this.playersByQueriedElement.get(element);queriedElementPlayers&&(players=queriedElementPlayers)}else{var elementPlayers=this.playersByElement.get(element);if(elementPlayers){var isRemovalAnimation_1=!toStateValue||"void"==toStateValue;elementPlayers.forEach(function(player){player.queued||(isRemovalAnimation_1||player.triggerName==triggerName)&&players.push(player)})}}return(namespaceId||triggerName)&&(players=players.filter(function(player){return(!namespaceId||namespaceId==player.namespaceId)&&(!triggerName||triggerName==player.triggerName)})),players},TransitionAnimationEngine.prototype._beforeAnimationBuild=function(namespaceId,instruction,allPreviousPlayersMap){for(var triggerName=instruction.triggerName,rootElement=instruction.element,targetNameSpaceId=instruction.isRemovalTransition?void 0:namespaceId,targetTriggerName=instruction.isRemovalTransition?void 0:triggerName,this_1=this,_i=0,_a=instruction.timelines;_i<_a.length;_i++){var timelineInstruction=_a[_i];!function(timelineInstruction){var element=timelineInstruction.element,isQueriedElement=element!==rootElement,players=getOrSetAsInMap(allPreviousPlayersMap,element,[]);this_1._getPreviousPlayers(element,isQueriedElement,targetNameSpaceId,targetTriggerName,instruction.toState).forEach(function(player){var realPlayer=player.getRealPlayer();realPlayer.beforeDestroy&&realPlayer.beforeDestroy(),player.destroy(),players.push(player)})}(timelineInstruction)}eraseStyles(rootElement,instruction.fromStyles)},TransitionAnimationEngine.prototype._buildAnimation=function(namespaceId,instruction,allPreviousPlayersMap,skippedPlayersMap,preStylesMap,postStylesMap){var _this=this,triggerName=instruction.triggerName,rootElement=instruction.element,allQueriedPlayers=[],allConsumedElements=new Set,allSubElements=new Set,allNewPlayers=instruction.timelines.map(function(timelineInstruction){var element=timelineInstruction.element;allConsumedElements.add(element);var details=element[REMOVAL_FLAG];if(details&&details.removedBeforeQueried)return new _angular_animations.NoopAnimationPlayer;var isQueriedElement=element!==rootElement,previousPlayers=flattenGroupPlayers((allPreviousPlayersMap.get(element)||EMPTY_PLAYER_ARRAY).map(function(p){return p.getRealPlayer()})).filter(function(p){var pp=p;return!!pp.element&&pp.element===element}),preStyles=preStylesMap.get(element),postStyles=postStylesMap.get(element),keyframes=normalizeKeyframes(_this.driver,_this._normalizer,element,timelineInstruction.keyframes,preStyles,postStyles),player=_this._buildPlayer(timelineInstruction,keyframes,previousPlayers);if(timelineInstruction.subTimeline&&skippedPlayersMap&&allSubElements.add(element),isQueriedElement){var wrappedPlayer=new TransitionAnimationPlayer(namespaceId,triggerName,element);wrappedPlayer.setRealPlayer(player),allQueriedPlayers.push(wrappedPlayer)}return player});allQueriedPlayers.forEach(function(player){getOrSetAsInMap(_this.playersByQueriedElement,player.element,[]).push(player),player.onDone(function(){return deleteOrUnsetInMap(_this.playersByQueriedElement,player.element,player)})}),allConsumedElements.forEach(function(element){return addClass(element,"ng-animating")});var player=optimizeGroupPlayer(allNewPlayers);return player.onDestroy(function(){allConsumedElements.forEach(function(element){return removeClass(element,"ng-animating")}),setStyles(rootElement,instruction.toStyles)}),allSubElements.forEach(function(element){getOrSetAsInMap(skippedPlayersMap,element,[]).push(player)}),player},TransitionAnimationEngine.prototype._buildPlayer=function(instruction,keyframes,previousPlayers){return keyframes.length>0?this.driver.animate(instruction.element,keyframes,instruction.duration,instruction.delay,instruction.easing,previousPlayers):new _angular_animations.NoopAnimationPlayer},TransitionAnimationEngine}(),TransitionAnimationPlayer=function(){function TransitionAnimationPlayer(namespaceId,triggerName,element){this.namespaceId=namespaceId,this.triggerName=triggerName,this.element=element,this._player=new _angular_animations.NoopAnimationPlayer,this._containsRealPlayer=!1,this._queuedCallbacks={},this.destroyed=!1,this.markedForDestroy=!1,this.queued=!0}return TransitionAnimationPlayer.prototype.setRealPlayer=function(player){var _this=this;this._containsRealPlayer||(this._player=player,Object.keys(this._queuedCallbacks).forEach(function(phase){_this._queuedCallbacks[phase].forEach(function(callback){return listenOnPlayer(player,phase,void 0,callback)})}),this._queuedCallbacks={},this._containsRealPlayer=!0,this.queued=!1)},TransitionAnimationPlayer.prototype.getRealPlayer=function(){return this._player},TransitionAnimationPlayer.prototype.syncPlayerEvents=function(player){var _this=this,p=this._player;p.triggerCallback&&player.onStart(function(){return p.triggerCallback("start")}),player.onDone(function(){return _this.finish()}),player.onDestroy(function(){return _this.destroy()})},TransitionAnimationPlayer.prototype._queueEvent=function(name,callback){getOrSetAsInMap(this._queuedCallbacks,name,[]).push(callback)},TransitionAnimationPlayer.prototype.onDone=function(fn){this.queued&&this._queueEvent("done",fn),this._player.onDone(fn)},TransitionAnimationPlayer.prototype.onStart=function(fn){this.queued&&this._queueEvent("start",fn),this._player.onStart(fn)},TransitionAnimationPlayer.prototype.onDestroy=function(fn){this.queued&&this._queueEvent("destroy",fn),this._player.onDestroy(fn)},TransitionAnimationPlayer.prototype.init=function(){this._player.init()},TransitionAnimationPlayer.prototype.hasStarted=function(){return!this.queued&&this._player.hasStarted()},TransitionAnimationPlayer.prototype.play=function(){!this.queued&&this._player.play()},TransitionAnimationPlayer.prototype.pause=function(){!this.queued&&this._player.pause()},TransitionAnimationPlayer.prototype.restart=function(){!this.queued&&this._player.restart()},TransitionAnimationPlayer.prototype.finish=function(){this._player.finish()},TransitionAnimationPlayer.prototype.destroy=function(){this.destroyed=!0,this._player.destroy()},TransitionAnimationPlayer.prototype.reset=function(){!this.queued&&this._player.reset()},TransitionAnimationPlayer.prototype.setPosition=function(p){this.queued||this._player.setPosition(p)},
TransitionAnimationPlayer.prototype.getPosition=function(){return this.queued?0:this._player.getPosition()},Object.defineProperty(TransitionAnimationPlayer.prototype,"totalTime",{get:function(){return this._player.totalTime},enumerable:!0,configurable:!0}),TransitionAnimationPlayer.prototype.triggerCallback=function(phaseName){var p=this._player;p.triggerCallback&&p.triggerCallback(phaseName)},TransitionAnimationPlayer}(),CLASSES_CACHE_KEY="$$classes",AnimationEngine=function(){function AnimationEngine(_driver,normalizer){var _this=this;this._driver=_driver,this._triggerCache={},this.onRemovalComplete=function(element,context){},this._transitionEngine=new TransitionAnimationEngine(_driver,normalizer),this._timelineEngine=new TimelineAnimationEngine(_driver,normalizer),this._transitionEngine.onRemovalComplete=function(element,context){return _this.onRemovalComplete(element,context)}}return AnimationEngine.prototype.registerTrigger=function(componentId,namespaceId,hostElement,name,metadata){var cacheKey=componentId+"-"+name,trigger=this._triggerCache[cacheKey];if(!trigger){var errors=[],ast=buildAnimationAst(this._driver,metadata,errors);if(errors.length)throw new Error('The animation trigger "'+name+'" has failed to build due to the following errors:\n - '+errors.join("\n - "));trigger=buildTrigger(name,ast),this._triggerCache[cacheKey]=trigger}this._transitionEngine.registerTrigger(namespaceId,name,trigger)},AnimationEngine.prototype.register=function(namespaceId,hostElement){this._transitionEngine.register(namespaceId,hostElement)},AnimationEngine.prototype.destroy=function(namespaceId,context){this._transitionEngine.destroy(namespaceId,context)},AnimationEngine.prototype.onInsert=function(namespaceId,element,parent,insertBefore){this._transitionEngine.insertNode(namespaceId,element,parent,insertBefore)},AnimationEngine.prototype.onRemove=function(namespaceId,element,context){this._transitionEngine.removeNode(namespaceId,element,context)},AnimationEngine.prototype.disableAnimations=function(element,disable){this._transitionEngine.markElementAsDisabled(element,disable)},AnimationEngine.prototype.process=function(namespaceId,element,property,value){if("@"==property.charAt(0)){var _a=parseTimelineCommand(property),id=_a[0],action=_a[1],args=value;this._timelineEngine.command(id,element,action,args)}else this._transitionEngine.trigger(namespaceId,element,property,value)},AnimationEngine.prototype.listen=function(namespaceId,element,eventName,eventPhase,callback){if("@"==eventName.charAt(0)){var _a=parseTimelineCommand(eventName),id=_a[0],action=_a[1];return this._timelineEngine.listen(id,element,action,callback)}return this._transitionEngine.listen(namespaceId,element,eventName,eventPhase,callback)},AnimationEngine.prototype.flush=function(microtaskId){void 0===microtaskId&&(microtaskId=-1),this._transitionEngine.flush(microtaskId)},Object.defineProperty(AnimationEngine.prototype,"players",{get:function(){return this._transitionEngine.players.concat(this._timelineEngine.players)},enumerable:!0,configurable:!0}),AnimationEngine.prototype.whenRenderingDone=function(){return this._transitionEngine.whenRenderingDone()},AnimationEngine}(),WebAnimationsPlayer=function(){function WebAnimationsPlayer(element,keyframes,options,previousPlayers){void 0===previousPlayers&&(previousPlayers=[]);var _this=this;this.element=element,this.keyframes=keyframes,this.options=options,this.previousPlayers=previousPlayers,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this.time=0,this.parentPlayer=null,this.previousStyles={},this.currentSnapshot={},this._duration=options.duration,this._delay=options.delay||0,this.time=this._duration+this._delay,allowPreviousPlayerStylesMerge(this._duration,this._delay)&&previousPlayers.forEach(function(player){var styles=player.currentSnapshot;Object.keys(styles).forEach(function(prop){return _this.previousStyles[prop]=styles[prop]})})}return WebAnimationsPlayer.prototype._onFinish=function(){this._finished||(this._finished=!0,this._onDoneFns.forEach(function(fn){return fn()}),this._onDoneFns=[])},WebAnimationsPlayer.prototype.init=function(){this._buildPlayer(),this._preparePlayerBeforeStart()},WebAnimationsPlayer.prototype._buildPlayer=function(){var _this=this;if(!this._initialized){this._initialized=!0;var keyframes=this.keyframes.map(function(styles){return copyStyles(styles,!1)}),previousStyleProps=Object.keys(this.previousStyles);if(previousStyleProps.length&&keyframes.length){var startingKeyframe_1=keyframes[0],missingStyleProps_1=[];if(previousStyleProps.forEach(function(prop){startingKeyframe_1.hasOwnProperty(prop)||missingStyleProps_1.push(prop),startingKeyframe_1[prop]=_this.previousStyles[prop]}),missingStyleProps_1.length)for(var self_1=this,i=1;i<keyframes.length;i++)!function(){var kf=keyframes[i];missingStyleProps_1.forEach(function(prop){kf[prop]=_computeStyle(self_1.element,prop)})}()}this.domPlayer=this._triggerWebAnimation(this.element,keyframes,this.options),this._finalKeyframe=keyframes.length?keyframes[keyframes.length-1]:{},this.domPlayer.addEventListener("finish",function(){return _this._onFinish()})}},WebAnimationsPlayer.prototype._preparePlayerBeforeStart=function(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()},WebAnimationsPlayer.prototype._triggerWebAnimation=function(element,keyframes,options){return element.animate(keyframes,options)},WebAnimationsPlayer.prototype.onStart=function(fn){this._onStartFns.push(fn)},WebAnimationsPlayer.prototype.onDone=function(fn){this._onDoneFns.push(fn)},WebAnimationsPlayer.prototype.onDestroy=function(fn){this._onDestroyFns.push(fn)},WebAnimationsPlayer.prototype.play=function(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(function(fn){return fn()}),this._onStartFns=[],this._started=!0),this.domPlayer.play()},WebAnimationsPlayer.prototype.pause=function(){this.init(),this.domPlayer.pause()},WebAnimationsPlayer.prototype.finish=function(){this.init(),this._onFinish(),this.domPlayer.finish()},WebAnimationsPlayer.prototype.reset=function(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1},WebAnimationsPlayer.prototype._resetDomPlayerState=function(){this.domPlayer&&this.domPlayer.cancel()},WebAnimationsPlayer.prototype.restart=function(){this.reset(),this.play()},WebAnimationsPlayer.prototype.hasStarted=function(){return this._started},WebAnimationsPlayer.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._onDestroyFns.forEach(function(fn){return fn()}),this._onDestroyFns=[])},WebAnimationsPlayer.prototype.setPosition=function(p){this.domPlayer.currentTime=p*this.time},WebAnimationsPlayer.prototype.getPosition=function(){return this.domPlayer.currentTime/this.time},Object.defineProperty(WebAnimationsPlayer.prototype,"totalTime",{get:function(){return this._delay+this._duration},enumerable:!0,configurable:!0}),WebAnimationsPlayer.prototype.beforeDestroy=function(){var _this=this,styles={};this.hasStarted()&&Object.keys(this._finalKeyframe).forEach(function(prop){"offset"!=prop&&(styles[prop]=_this._finished?_this._finalKeyframe[prop]:_computeStyle(_this.element,prop))}),this.currentSnapshot=styles},WebAnimationsPlayer.prototype.triggerCallback=function(phaseName){var methods="start"==phaseName?this._onStartFns:this._onDoneFns;methods.forEach(function(fn){return fn()}),methods.length=0},WebAnimationsPlayer}(),WebAnimationsDriver=function(){function WebAnimationsDriver(){}return WebAnimationsDriver.prototype.validateStyleProperty=function(prop){return validateStyleProperty(prop)},WebAnimationsDriver.prototype.matchesElement=function(element,selector){return matchesElement(element,selector)},WebAnimationsDriver.prototype.containsElement=function(elm1,elm2){return containsElement(elm1,elm2)},WebAnimationsDriver.prototype.query=function(element,selector,multi){return invokeQuery(element,selector,multi)},WebAnimationsDriver.prototype.computeStyle=function(element,prop,defaultValue){return window.getComputedStyle(element)[prop]},WebAnimationsDriver.prototype.animate=function(element,keyframes,duration,delay,easing,previousPlayers){void 0===previousPlayers&&(previousPlayers=[]);var fill=0==delay?"both":"forwards",playerOptions={duration:duration,delay:delay,fill:fill};easing&&(playerOptions.easing=easing);var previousWebAnimationPlayers=previousPlayers.filter(function(player){return player instanceof WebAnimationsPlayer});return new WebAnimationsPlayer(element,keyframes,playerOptions,previousWebAnimationPlayers)},WebAnimationsDriver}();exports.AnimationDriver=AnimationDriver,exports.ɵAnimation=Animation,exports.ɵAnimationStyleNormalizer=AnimationStyleNormalizer,exports.ɵNoopAnimationStyleNormalizer=NoopAnimationStyleNormalizer,exports.ɵWebAnimationsStyleNormalizer=WebAnimationsStyleNormalizer,exports.ɵNoopAnimationDriver=NoopAnimationDriver,exports.ɵAnimationEngine=AnimationEngine,exports.ɵWebAnimationsDriver=WebAnimationsDriver,exports.ɵsupportsWebAnimations=supportsWebAnimations,exports.ɵWebAnimationsPlayer=WebAnimationsPlayer,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=animations-browser.umd.min.js.map