{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../../../src/add/operator/timeout.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,wBAAwB,wBAAwB,CAAC,CAAA;AAEjD,uBAAU,CAAC,SAAS,CAAC,OAAO,GAAG,iBAAO,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { timeout } from '../../operator/timeout';\n\nObservable.prototype.timeout = timeout;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    timeout: typeof timeout;\n  }\n}"]}