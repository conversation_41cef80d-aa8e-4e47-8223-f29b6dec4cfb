{"_from": "assert-plus@^1.0.0", "_id": "assert-plus@1.0.0", "_inBundle": false, "_integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "_location": "/verror/assert-plus", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "assert-plus@^1.0.0", "name": "assert-plus", "escapedName": "assert-plus", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/verror"], "_resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "_shasum": "f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525", "_spec": "assert-plus@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\verror", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mcavage/node-assert-plus/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "deprecated": false, "description": "Extra assertions on top of node's assert module", "devDependencies": {"faucet": "0.0.1", "tape": "4.2.2"}, "engines": {"node": ">=0.8"}, "homepage": "https://github.com/mcavage/node-assert-plus#readme", "license": "MIT", "main": "./assert.js", "name": "assert-plus", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/mcavage/node-assert-plus.git"}, "scripts": {"test": "tape tests/*.js | ./node_modules/.bin/faucet"}, "version": "1.0.0"}