{"version": 3, "file": "race.js", "sourceRoot": "", "sources": ["../../../src/add/observable/race.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,qBAAmC,uBAAuB,CAAC,CAAA;AAE3D,uBAAU,CAAC,IAAI,GAAG,WAAU,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { race as staticRace } from '../../observable/race';\n\nObservable.race = staticRace;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let race: typeof staticRace;\n  }\n}"]}