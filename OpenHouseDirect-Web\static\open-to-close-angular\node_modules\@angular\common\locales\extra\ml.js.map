{"version": 3, "file": "ml.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ml.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ;YACjC,YAAY,EAAE,cAAc;YAC5B,YAAY,EAAE,QAAQ,EAAE,QAAQ;SACjC;QACD;YACE,aAAa,EAAE,MAAM,EAAE,UAAU;YACjC,QAAQ,EAAE,YAAY;YACtB,cAAc,EAAE,YAAY;YAC5B,QAAQ,EAAE,QAAQ;SACnB;KACF;IACD;QACE;YACE,aAAa,EAAE,MAAM,EAAE,UAAU;YACjC,QAAQ,EAAE,YAAY;YACtB,cAAc,EAAE,YAAY;YAC5B,QAAQ,EAAE,QAAQ;SACnB;QACD,AADE;KAEH;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC/E;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'അ', 'ഉച്ച', 'പുലർച്ചെ', 'രാവിലെ',\n      'ഉച്ചയ്ക്ക്', 'ഉച്ചതിരിഞ്ഞ്',\n      'വൈകുന്നേരം', 'സന്ധ്യ', 'രാത്രി'\n    ],\n    [\n      'അർദ്ധരാത്രി', 'ഉച്ച', 'പുലർച്ചെ',\n      'രാവിലെ', 'ഉച്ചയ്ക്ക്',\n      'ഉച്ചതിരിഞ്ഞ്', 'വൈകുന്നേരം',\n      'സന്ധ്യ', 'രാത്രി'\n    ],\n  ],\n  [\n    [\n      'അർദ്ധരാത്രി', 'ഉച്ച', 'പുലർച്ചെ',\n      'രാവിലെ', 'ഉച്ചയ്ക്ക്',\n      'ഉച്ചതിരിഞ്ഞ്', 'വൈകുന്നേരം',\n      'സന്ധ്യ', 'രാത്രി'\n    ],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['03:00', '06:00'], ['06:00', '12:00'], ['12:00', '14:00'],\n    ['14:00', '15:00'], ['15:00', '18:00'], ['18:00', '19:00'], ['19:00', '03:00']\n  ]\n];\n"]}