{"__symbolic": "module", "version": 4, "exports": [{"export": [{"name": "DataSource", "as": "DataSource"}], "from": "@angular/cdk/collections"}], "metadata": {"RowPlaceholder": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 53, "character": 1}, "arguments": [{"selector": "[rowPlaceholder]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 55, "character": 36}]}]}}, "HeaderRowPlaceholder": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 62, "character": 1}, "arguments": [{"selector": "[headerRowPlaceholder]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 55, "character": 36}]}]}}, "CDK_TABLE_TEMPLATE": "\n  <ng-container headerRowPlaceholder></ng-container>\n  <ng-container rowPlaceholder></ng-container>", "CdkTable": {"__symbolic": "class", "arity": 1, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 87, "character": 1}, "arguments": [{"moduleId": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "module"}, "member": "id"}, "selector": "cdk-table", "exportAs": "cdkTable", "template": {"__symbolic": "reference", "name": "CDK_TABLE_TEMPLATE"}, "host": {"class": "cdk-table"}, "encapsulation": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewEncapsulation", "line": 95, "character": 17}, "member": "None"}, "preserveWhitespaces": false, "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 97, "character": 19}, "member": "OnPush"}}]}], "members": {"trackBy": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 146, "character": 3}}]}], "dataSource": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 178, "character": 3}}]}], "_rowPlaceholder": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 197, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "RowPlaceholder"}]}]}], "_headerRowPlaceholder": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 198, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "HeaderRowPlaceholder"}]}]}], "_contentColumnDefs": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChildren", "line": 204, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "CdkColumnDef"}]}]}], "_contentRowDefs": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChildren", "line": 207, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "CdkRowDef"}]}]}], "_headerRowDef": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChild", "line": 215, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "CdkHeaderRowDef"}]}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Attribute", "line": 220, "character": 15}, "arguments": ["role"]}]], "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Iterable<PERSON><PERSON><PERSON>", "line": 217, "character": 41}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 218, "character": 51}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 219, "character": 26}, {"__symbolic": "reference", "name": "string"}]}], "ngOnInit": [{"__symbolic": "method"}], "ngAfterContentChecked": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "renderRows": [{"__symbolic": "method"}], "setHeaderRowDef": [{"__symbolic": "method"}], "addColumnDef": [{"__symbolic": "method"}], "removeColumnDef": [{"__symbolic": "method"}], "addRowDef": [{"__symbolic": "method"}], "removeRowDef": [{"__symbolic": "method"}], "_cacheColumnDefs": [{"__symbolic": "method"}], "_cacheRowDefs": [{"__symbolic": "method"}], "_renderUpdatedColumns": [{"__symbolic": "method"}], "_switchDataSource": [{"__symbolic": "method"}], "_observeRenderChanges": [{"__symbolic": "method"}], "_renderHeaderRow": [{"__symbolic": "method"}], "_getRowDef": [{"__symbolic": "method"}], "_insertRow": [{"__symbolic": "method"}], "_updateRowIndexContext": [{"__symbolic": "method"}], "_getHeaderCellTemplatesForRow": [{"__symbolic": "method"}], "_getCellTemplatesForRow": [{"__symbolic": "method"}]}}, "CdkCellDef": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 14, "character": 1}, "arguments": [{"selector": "[cdkCellDef]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}]}}, "CdkHeaderCellDef": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 23, "character": 1}, "arguments": [{"selector": "[cdkHeaderCellDef]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}]}}, "CdkColumnDef": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 32, "character": 1}, "arguments": [{"selector": "[cdkColumnDef]"}]}], "members": {"name": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 35, "character": 3}, "arguments": ["cdkColumnDef"]}]}], "cell": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChild", "line": 48, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "CdkCellDef"}]}]}], "headerCell": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChild", "line": 51, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "CdkHeaderCellDef"}]}]}]}}, "CdkHeaderCell": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 62, "character": 1}, "arguments": [{"selector": "cdk-header-cell", "host": {"class": "cdk-header-cell", "role": "columnheader"}}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CdkColumnDef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 70, "character": 51}]}]}}, "CdkCell": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 76, "character": 1}, "arguments": [{"selector": "cdk-cell", "host": {"class": "cdk-cell", "role": "gridcell"}}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CdkColumnDef"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 70, "character": 51}]}]}}, "CDK_ROW_TEMPLATE": "<ng-container cdkCellOutlet></ng-container>", "BaseRowDef": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "Iterable<PERSON><PERSON><PERSON>", "line": 40, "character": 34}]}], "ngOnChanges": [{"__symbolic": "method"}], "getColumnsDiff": [{"__symbolic": "method"}]}}, "CdkHeaderRowDef": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "BaseRowDef"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 65, "character": 1}, "arguments": [{"selector": "[cdkHeaderRowDef]", "inputs": ["columns: cdkHeaderRowDef"]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "Iterable<PERSON><PERSON><PERSON>", "line": 40, "character": 34}]}]}}, "CdkRowDef": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "BaseRowDef"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 80, "character": 1}, "arguments": [{"selector": "[cdkRowDef]", "inputs": ["columns: cdkRowDefColumns", "when: cdkRowDefWhen"]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "Iterable<PERSON><PERSON><PERSON>", "line": 40, "character": 34}]}]}}, "CdkCellOutletRowContext": {"__symbolic": "interface"}, "CdkCellOutlet": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 128, "character": 1}, "arguments": [{"selector": "[cdkCellOutlet]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 145, "character": 37}]}]}, "statics": {"mostRecentCellOutlet": null}}, "CdkHeaderRow": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 151, "character": 1}, "arguments": [{"moduleId": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "module"}, "member": "id"}, "selector": "cdk-header-row", "template": {"__symbolic": "reference", "name": "CDK_ROW_TEMPLATE"}, "host": {"class": "cdk-header-row", "role": "row"}, "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 159, "character": 19}, "member": "OnPush"}, "encapsulation": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewEncapsulation", "line": 160, "character": 17}, "member": "None"}, "preserveWhitespaces": false}]}], "members": {}}, "CdkRow": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 166, "character": 1}, "arguments": [{"moduleId": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "module"}, "member": "id"}, "selector": "cdk-row", "template": {"__symbolic": "reference", "name": "CDK_ROW_TEMPLATE"}, "host": {"class": "cdk-row", "role": "row"}, "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 174, "character": 19}, "member": "OnPush"}, "encapsulation": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewEncapsulation", "line": 175, "character": 17}, "member": "None"}, "preserveWhitespaces": false}]}], "members": {}}, "CdkTableModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 30, "character": 1}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule", "line": 31, "character": 12}], "exports": [[{"__symbolic": "reference", "name": "CdkTable"}, {"__symbolic": "reference", "name": "CdkRowDef"}, {"__symbolic": "reference", "name": "CdkCellDef"}, {"__symbolic": "reference", "name": "CdkCellOutlet"}, {"__symbolic": "reference", "name": "CdkHeaderCellDef"}, {"__symbolic": "reference", "name": "CdkColumnDef"}, {"__symbolic": "reference", "name": "CdkCell"}, {"__symbolic": "reference", "name": "CdkRow"}, {"__symbolic": "reference", "name": "CdkHeaderCell"}, {"__symbolic": "reference", "name": "CdkHeaderRow"}, {"__symbolic": "reference", "name": "CdkHeaderRowDef"}, {"__symbolic": "reference", "name": "RowPlaceholder"}, {"__symbolic": "reference", "name": "HeaderRowPlaceholder"}]], "declarations": [[{"__symbolic": "reference", "name": "CdkTable"}, {"__symbolic": "reference", "name": "CdkRowDef"}, {"__symbolic": "reference", "name": "CdkCellDef"}, {"__symbolic": "reference", "name": "CdkCellOutlet"}, {"__symbolic": "reference", "name": "CdkHeaderCellDef"}, {"__symbolic": "reference", "name": "CdkColumnDef"}, {"__symbolic": "reference", "name": "CdkCell"}, {"__symbolic": "reference", "name": "CdkRow"}, {"__symbolic": "reference", "name": "CdkHeaderCell"}, {"__symbolic": "reference", "name": "CdkHeaderRow"}, {"__symbolic": "reference", "name": "CdkHeaderRowDef"}, {"__symbolic": "reference", "name": "RowPlaceholder"}, {"__symbolic": "reference", "name": "HeaderRowPlaceholder"}]]}]}], "members": {}}}, "origins": {"RowPlaceholder": "./table", "HeaderRowPlaceholder": "./table", "CDK_TABLE_TEMPLATE": "./table", "CdkTable": "./table", "CdkCellDef": "./cell", "CdkHeaderCellDef": "./cell", "CdkColumnDef": "./cell", "CdkHeaderCell": "./cell", "CdkCell": "./cell", "CDK_ROW_TEMPLATE": "./row", "BaseRowDef": "./row", "CdkHeaderRowDef": "./row", "CdkRowDef": "./row", "CdkCellOutletRowContext": "./row", "CdkCellOutlet": "./row", "CdkHeaderRow": "./row", "CdkRow": "./row", "CdkTableModule": "./table-module"}, "importAs": "@angular/cdk/table"}