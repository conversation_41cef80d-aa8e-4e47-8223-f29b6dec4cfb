{"_from": "extend-shallow@^2.0.1", "_id": "extend-shallow@2.0.1", "_inBundle": false, "_integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==", "_location": "/watchpack-chokidar2/expand-brackets/extend-shallow", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "extend-shallow@^2.0.1", "name": "extend-shallow", "escapedName": "extend-shallow", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/watchpack-chokidar2/expand-brackets"], "_resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "_shasum": "51af7d614ad9a9f610ea1bafbb989d6b1c56890f", "_spec": "extend-shallow@^2.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\watchpack-chokidar2\\node_modules\\expand-brackets", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/extend-shallow/issues"}, "bundleDependencies": false, "dependencies": {"is-extendable": "^0.1.0"}, "deprecated": false, "description": "Extend an object with the properties of additional objects. node.js/javascript util.", "devDependencies": {"array-slice": "^0.2.3", "benchmarked": "^0.1.4", "chalk": "^1.0.0", "for-own": "^0.1.3", "glob": "^5.0.12", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "minimist": "^1.1.1", "mocha": "^2.2.5", "should": "^7.0.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/extend-shallow", "keywords": ["assign", "extend", "javascript", "js", "keys", "merge", "obj", "object", "prop", "properties", "property", "props", "shallow", "util", "utility", "utils", "value"], "license": "MIT", "main": "index.js", "name": "extend-shallow", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/extend-shallow.git"}, "scripts": {"test": "mocha"}, "version": "2.0.1"}