{"_from": "thunkify@^2.1.2", "_id": "thunkify@2.1.2", "_inBundle": false, "_integrity": "sha512-w9foI80XcGImrhMQ19pxunaEC5Rp2uzxZZg4XBAFRfiLOplk3F0l7wo+bO16vC2/nlQfR/mXZxcduo0MF2GWLg==", "_location": "/thunkify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "thunkify@^2.1.2", "name": "thunkify", "escapedName": "thunkify", "rawSpec": "^2.1.2", "saveSpec": null, "fetchSpec": "^2.1.2"}, "_requiredBy": ["/pac-resolver"], "_resolved": "https://registry.npmjs.org/thunkify/-/thunkify-2.1.2.tgz", "_shasum": "faa0e9d230c51acc95ca13a361ac05ca7e04553d", "_spec": "thunkify@^2.1.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\pac-resolver", "bugs": {"url": "https://github.com/visionmedia/node-thunkify/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Turn callbacks, arrays, generators, generator functions, and promises into a thunk", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/node-thunkify#readme", "keywords": ["thunk", "co", "generator", "generators", "promise"], "license": "MIT", "name": "thunkify", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-thunkify.git"}, "version": "2.1.2"}