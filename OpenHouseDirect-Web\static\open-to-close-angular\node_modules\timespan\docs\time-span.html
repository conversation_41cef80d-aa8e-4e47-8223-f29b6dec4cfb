<!DOCTYPE html>  <html> <head>   <title>time-span.js</title>   <meta http-equiv="content-type" content="text/html; charset=UTF-8">   <link rel="stylesheet" media="all" href="docco.css" /> </head> <body>   <div id="container">     <div id="background"></div>          <table cellpadding="0" cellspacing="0">       <thead>         <tr>           <th class="docs">             <h1>               time-span.js             </h1>           </th>           <th class="code">           </th>         </tr>       </thead>       <tbody>                               <tr id="section-1">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-1">&#182;</a>               </div>                            </td>             <td class="code">               <div class="highlight"><pre><span class="cm">/*</span>
<span class="cm">* JavaScript TimeSpan Library</span>
<span class="cm">*</span>
<span class="cm">* Copyright (c) 2010 <PERSON>, <PERSON></span>
<span class="cm">* </span>
<span class="cm">* Permission is hereby granted, free of charge, to any person obtaining</span>
<span class="cm">* a copy of this software and associated documentation files (the</span>
<span class="cm">* &quot;Software&quot;), to deal in the Software without restriction, including</span>
<span class="cm">* without limitation the rights to use, copy, modify, merge, publish,</span>
<span class="cm">* distribute, sublicense, and/or sell copies of the Software, and to</span>
<span class="cm">* permit persons to whom the Software is furnished to do so, subject to</span>
<span class="cm">* the following conditions:</span>
<span class="cm">* </span>
<span class="cm">* The above copyright notice and this permission notice shall be</span>
<span class="cm">* included in all copies or substantial portions of the Software.</span>
<span class="cm">* </span>
<span class="cm">* THE SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND,</span>
<span class="cm">* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF</span>
<span class="cm">* MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND</span>
<span class="cm">* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE</span>
<span class="cm">* LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION</span>
<span class="cm">* OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION</span>
<span class="cm">* WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.</span>
<span class="cm">*/</span></pre></div>             </td>           </tr>                               <tr id="section-2">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-2">&#182;</a>               </div>               <h3>Time constants</h3>             </td>             <td class="code">               <div class="highlight"><pre><span class="kd">var</span> <span class="nx">msecPerSecond</span> <span class="o">=</span> <span class="mi">1000</span><span class="p">,</span>
    <span class="nx">msecPerMinute</span> <span class="o">=</span> <span class="mi">60000</span><span class="p">,</span>
    <span class="nx">msecPerHour</span> <span class="o">=</span> <span class="mi">3600000</span><span class="p">,</span>
    <span class="nx">msecPerDay</span> <span class="o">=</span> <span class="mi">86400000</span><span class="p">;</span></pre></div>             </td>           </tr>                               <tr id="section-3">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-3">&#182;</a>               </div>               <h3>Timespan Parsers</h3>             </td>             <td class="code">               <div class="highlight"><pre><span class="kd">var</span> <span class="nx">timeSpanWithDays</span> <span class="o">=</span> <span class="sr">/^(\d+):(\d+):(\d+):(\d+)(\.\d+)?/</span><span class="p">,</span>
    <span class="nx">timeSpanNoDays</span> <span class="o">=</span> <span class="sr">/^(\d+):(\d+):(\d+)(\.\d+)?/</span><span class="p">;</span></pre></div>             </td>           </tr>                               <tr id="section-4">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-4">&#182;</a>               </div>               <h3>function TimeSpan (milliseconds, seconds, minutes, hours, days)</h3>

<h4>@milliseconds {Number} Number of milliseconds for this instance.</h4>

<h4>@seconds {Number} Number of seconds for this instance.</h4>

<h4>@minutes {Number} Number of minutes for this instance.</h4>

<h4>@hours {Number} Number of hours for this instance.</h4>

<h4>@days {Number} Number of days for this instance.</h4>

<p>Constructor function for the <code>TimeSpan</code> object which represents a length
of positive or negative milliseconds componentized into milliseconds, 
seconds, hours, and days.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="kd">var</span> <span class="nx">TimeSpan</span> <span class="o">=</span> <span class="nx">exports</span><span class="p">.</span><span class="nx">TimeSpan</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">milliseconds</span><span class="p">,</span> <span class="nx">seconds</span><span class="p">,</span> <span class="nx">minutes</span><span class="p">,</span> <span class="nx">hours</span><span class="p">,</span> <span class="nx">days</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  
  <span class="k">if</span> <span class="p">(</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">days</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">days</span> <span class="o">*</span> <span class="nx">msecPerDay</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">if</span> <span class="p">(</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">hours</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">hours</span> <span class="o">*</span> <span class="nx">msecPerHour</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">if</span> <span class="p">(</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">minutes</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">minutes</span> <span class="o">*</span> <span class="nx">msecPerMinute</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">if</span> <span class="p">(</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">seconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">seconds</span> <span class="o">*</span> <span class="nx">msecPerSecond</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">if</span> <span class="p">(</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">milliseconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="nx">milliseconds</span><span class="p">;</span>
  <span class="p">}</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-5">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-5">&#182;</a>               </div>               <h2>Factory methods</h2>

<p>Helper methods for creating new TimeSpan objects
from various criteria: milliseconds, seconds, minutes,
hours, days, strings and other <code>TimeSpan</code> instances.</p>             </td>             <td class="code">               <div class="highlight"><pre></pre></div>             </td>           </tr>                               <tr id="section-6">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-6">&#182;</a>               </div>               <h3>function fromMilliseconds (milliseconds)</h3>

<h4>@milliseconds {Number} Amount of milliseconds for the new TimeSpan instance.</h4>

<p>Creates a new <code>TimeSpan</code> instance with the specified <code>milliseconds</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">fromMilliseconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">milliseconds</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">milliseconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="nx">milliseconds</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">);</span>
<span class="p">}</span></pre></div>             </td>           </tr>                               <tr id="section-7">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-7">&#182;</a>               </div>               <h3>function fromSeconds (seconds)</h3>

<h4>@milliseconds {Number} Amount of seconds for the new TimeSpan instance.</h4>

<p>Creates a new <code>TimeSpan</code> instance with the specified <code>seconds</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">fromSeconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">seconds</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">seconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="nx">seconds</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-8">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-8">&#182;</a>               </div>               <h3>function fromMinutes (milliseconds)</h3>

<h4>@milliseconds {Number} Amount of minutes for the new TimeSpan instance.</h4>

<p>Creates a new <code>TimeSpan</code> instance with the specified <code>minutes</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">fromMinutes</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">minutes</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">minutes</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="nx">minutes</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-9">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-9">&#182;</a>               </div>               <h3>function fromHours (hours)</h3>

<h4>@milliseconds {Number} Amount of hours for the new TimeSpan instance.</h4>

<p>Creates a new <code>TimeSpan</code> instance with the specified <code>hours</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">fromHours</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">hours</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">hours</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="nx">hours</span><span class="p">,</span> <span class="mi">0</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-10">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-10">&#182;</a>               </div>               <h3>function fromDays (days)</h3>

<h4>@milliseconds {Number} Amount of days for the new TimeSpan instance.</h4>

<p>Creates a new <code>TimeSpan</code> instance with the specified <code>days</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">fromDays</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">days</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">days</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="nx">days</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-11">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-11">&#182;</a>               </div>               <h3>function parse (str)</h3>

<h4>@str {string} Timespan string to parse.</h4>

<p>Creates a new <code>TimeSpan</code> instance from the specified
string, <code>str</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">parse</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">str</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">match</span><span class="p">,</span> <span class="nx">milliseconds</span><span class="p">;</span>
  
  <span class="kd">function</span> <span class="nx">parseMilliseconds</span> <span class="p">(</span><span class="nx">value</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">return</span> <span class="nx">value</span> <span class="o">?</span> <span class="nb">parseFloat</span><span class="p">(</span><span class="s1">&#39;0&#39;</span> <span class="o">+</span> <span class="nx">value</span><span class="p">)</span> <span class="o">*</span> <span class="mi">1000</span> <span class="o">:</span> <span class="mi">0</span><span class="p">;</span>
  <span class="p">}</span>
  </pre></div>             </td>           </tr>                               <tr id="section-12">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-12">&#182;</a>               </div>               <p>If we match against a full TimeSpan: </p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="k">if</span> <span class="p">((</span><span class="nx">match</span> <span class="o">=</span> <span class="nx">str</span><span class="p">.</span><span class="nx">match</span><span class="p">(</span><span class="nx">timeSpanWithDays</span><span class="p">)))</span> <span class="p">{</span>
    <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="nx">parseMilliseconds</span><span class="p">(</span><span class="nx">match</span><span class="p">[</span><span class="mi">5</span><span class="p">]),</span> <span class="nx">match</span><span class="p">[</span><span class="mi">4</span><span class="p">],</span> <span class="nx">match</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="nx">match</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="nx">match</span><span class="p">[</span><span class="mi">1</span><span class="p">]);</span>
  <span class="p">}</span>
  </pre></div>             </td>           </tr>                               <tr id="section-13">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-13">&#182;</a>               </div>               <p>If we match against a partial TimeSpan:</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="k">if</span> <span class="p">((</span><span class="nx">match</span> <span class="o">=</span> <span class="nx">str</span><span class="p">.</span><span class="nx">match</span><span class="p">(</span><span class="nx">timeSpanNoDays</span><span class="p">)))</span> <span class="p">{</span>
    <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="nx">parseMilliseconds</span><span class="p">(</span><span class="nx">match</span><span class="p">[</span><span class="mi">4</span><span class="p">]),</span> <span class="nx">match</span><span class="p">[</span><span class="mi">3</span><span class="p">],</span> <span class="nx">match</span><span class="p">[</span><span class="mi">2</span><span class="p">],</span> <span class="nx">match</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="mi">0</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="kc">null</span><span class="p">;</span>
<span class="p">};</span>

<span class="kd">var</span> <span class="nx">months</span>  <span class="o">=</span> <span class="p">[</span><span class="mi">31</span><span class="p">,</span> <span class="mi">28</span><span class="p">,</span> <span class="mi">31</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">31</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">31</span><span class="p">,</span> <span class="mi">31</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">31</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">31</span><span class="p">];</span></pre></div>             </td>           </tr>                               <tr id="section-14">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-14">&#182;</a>               </div>               <p>List of default singular time modifiers and associated
computation algoritm. Assumes in order, smallest to greatest
performing carry forward additiona / subtraction for each
Date-Time component.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="kd">var</span> <span class="nx">parsers</span> <span class="o">=</span> <span class="p">{</span>
  <span class="s1">&#39;milliseconds&#39;</span><span class="o">:</span> <span class="p">{</span>
    <span class="nx">exp</span><span class="o">:</span> <span class="sr">/(\d+)milli[second]?[s]?/i</span><span class="p">,</span>
    <span class="nx">get</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="nx">date</span><span class="p">.</span><span class="nx">getMilliseconds</span><span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">set</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">val</span><span class="p">,</span> <span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="nx">date</span><span class="p">.</span><span class="nx">setMilliseconds</span><span class="p">(</span><span class="nx">val</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">compute</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">delta</span><span class="p">,</span> <span class="nx">date</span><span class="p">,</span> <span class="nx">computed</span><span class="p">)</span> <span class="p">{</span>
      <span class="kd">var</span> <span class="nx">round</span> <span class="o">=</span> <span class="nx">delta</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="o">?</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span> <span class="o">:</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">ceil</span><span class="p">;</span>
      <span class="k">if</span> <span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">delta</span> <span class="o">/</span> <span class="mi">1000</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">milliseconds</span> <span class="o">+=</span> <span class="nx">delta</span> <span class="o">%</span> <span class="mi">1000</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nb">Math</span><span class="p">.</span><span class="nx">abs</span><span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">milliseconds</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">1000</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">milliseconds</span> <span class="o">/</span> <span class="mi">1000</span><span class="p">)</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">milliseconds</span> <span class="o">=</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">milliseconds</span> <span class="o">%</span> <span class="mi">1000</span><span class="p">;</span>
      <span class="p">}</span>

      <span class="k">return</span> <span class="nx">computed</span><span class="p">;</span>
    <span class="p">}</span>
  <span class="p">},</span>
  <span class="s1">&#39;seconds&#39;</span><span class="o">:</span> <span class="p">{</span>
    <span class="nx">exp</span><span class="o">:</span> <span class="sr">/(\d+)second[s]?/i</span><span class="p">,</span>
    <span class="nx">get</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="nx">date</span><span class="p">.</span><span class="nx">getSeconds</span><span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">set</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">val</span><span class="p">,</span> <span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="nx">date</span><span class="p">.</span><span class="nx">setSeconds</span><span class="p">(</span><span class="nx">val</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">compute</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">delta</span><span class="p">,</span> <span class="nx">date</span><span class="p">,</span> <span class="nx">computed</span><span class="p">)</span> <span class="p">{</span>
      <span class="kd">var</span> <span class="nx">round</span> <span class="o">=</span> <span class="nx">delta</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="o">?</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span> <span class="o">:</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">ceil</span><span class="p">;</span>
      <span class="k">if</span> <span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">delta</span> <span class="o">/</span> <span class="mi">60</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span> <span class="o">+=</span> <span class="nx">delta</span> <span class="o">%</span> <span class="mi">60</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nb">Math</span><span class="p">.</span><span class="nx">abs</span><span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">60</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span> <span class="o">/</span> <span class="mi">60</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span> <span class="o">=</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span> <span class="o">%</span> <span class="mi">60</span><span class="p">;</span> 
      <span class="p">}</span>
      
      <span class="k">return</span> <span class="nx">computed</span><span class="p">;</span>
    <span class="p">}</span>
  <span class="p">},</span>
  <span class="s1">&#39;minutes&#39;</span><span class="o">:</span> <span class="p">{</span>
    <span class="nx">exp</span><span class="o">:</span> <span class="sr">/(\d+)minute[s]?/i</span><span class="p">,</span>
    <span class="nx">get</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="nx">date</span><span class="p">.</span><span class="nx">getMinutes</span><span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">set</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">val</span><span class="p">,</span> <span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="nx">date</span><span class="p">.</span><span class="nx">setMinutes</span><span class="p">(</span><span class="nx">val</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">compute</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">delta</span><span class="p">,</span> <span class="nx">date</span><span class="p">,</span> <span class="nx">computed</span><span class="p">)</span> <span class="p">{</span>
      <span class="kd">var</span> <span class="nx">round</span> <span class="o">=</span> <span class="nx">delta</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="o">?</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span> <span class="o">:</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">ceil</span><span class="p">;</span>
      <span class="k">if</span> <span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="p">{</span> 
        <span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">delta</span> <span class="o">/</span> <span class="mi">60</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span> <span class="o">+=</span> <span class="nx">delta</span> <span class="o">%</span> <span class="mi">60</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nb">Math</span><span class="p">.</span><span class="nx">abs</span><span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">60</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span> <span class="o">/</span> <span class="mi">60</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span> <span class="o">=</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span> <span class="o">%</span> <span class="mi">60</span><span class="p">;</span> 
      <span class="p">}</span>
      
      <span class="k">return</span> <span class="nx">computed</span><span class="p">;</span>
    <span class="p">}</span>
  <span class="p">},</span>
  <span class="s1">&#39;hours&#39;</span><span class="o">:</span> <span class="p">{</span>
    <span class="nx">exp</span><span class="o">:</span> <span class="sr">/(\d+)hour[s]?/i</span><span class="p">,</span>
    <span class="nx">get</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="nx">date</span><span class="p">.</span><span class="nx">getHours</span><span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">set</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">val</span><span class="p">,</span> <span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="nx">date</span><span class="p">.</span><span class="nx">setHours</span><span class="p">(</span><span class="nx">val</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">compute</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">delta</span><span class="p">,</span> <span class="nx">date</span><span class="p">,</span> <span class="nx">computed</span><span class="p">)</span> <span class="p">{</span>
      <span class="kd">var</span> <span class="nx">round</span> <span class="o">=</span> <span class="nx">delta</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="o">?</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span> <span class="o">:</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">ceil</span><span class="p">;</span>
      <span class="k">if</span> <span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="p">{</span> 
        <span class="nx">computed</span><span class="p">.</span><span class="nx">days</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">delta</span> <span class="o">/</span> <span class="mi">24</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span> <span class="o">+=</span> <span class="nx">delta</span> <span class="o">%</span> <span class="mi">24</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nb">Math</span><span class="p">.</span><span class="nx">abs</span><span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">24</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">days</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span> <span class="o">/</span> <span class="mi">24</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span> <span class="o">=</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span> <span class="o">%</span> <span class="mi">24</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">return</span> <span class="nx">computed</span><span class="p">;</span>
    <span class="p">}</span>
  <span class="p">},</span>
  <span class="s1">&#39;days&#39;</span><span class="o">:</span> <span class="p">{</span>
    <span class="nx">exp</span><span class="o">:</span> <span class="sr">/(\d+)day[s]?/i</span><span class="p">,</span>
    <span class="nx">get</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="nx">date</span><span class="p">.</span><span class="nx">getDate</span><span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">set</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">val</span><span class="p">,</span> <span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="nx">date</span><span class="p">.</span><span class="nx">setDate</span><span class="p">(</span><span class="nx">val</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">compute</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">delta</span><span class="p">,</span> <span class="nx">date</span><span class="p">,</span> <span class="nx">computed</span><span class="p">)</span> <span class="p">{</span>
      <span class="kd">var</span> <span class="nx">sign</span>     <span class="o">=</span> <span class="nx">delta</span> <span class="o">&gt;=</span> <span class="mi">0</span> <span class="o">?</span> <span class="mi">1</span> <span class="o">:</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span>
          <span class="nx">opsign</span>   <span class="o">=</span> <span class="nx">delta</span> <span class="o">&gt;=</span> <span class="mi">0</span> <span class="o">?</span> <span class="o">-</span><span class="mi">1</span> <span class="o">:</span> <span class="mi">1</span><span class="p">,</span>
          <span class="nx">clean</span>    <span class="o">=</span> <span class="mi">0</span><span class="p">,</span>
          <span class="nx">original</span> <span class="o">=</span> <span class="nx">delta</span><span class="p">,</span>
          <span class="nx">month</span>    <span class="o">=</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span><span class="p">,</span>
          <span class="nx">days</span>     <span class="o">=</span> <span class="nx">months</span><span class="p">[</span><span class="nx">month</span><span class="p">];</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="p">{</span>          
        <span class="k">while</span> <span class="p">(</span><span class="nb">Math</span><span class="p">.</span><span class="nx">abs</span><span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="nx">days</span><span class="p">)</span> <span class="p">{</span>
          <span class="nx">month</span> <span class="o">+=</span> <span class="nx">sign</span> <span class="o">*</span> <span class="mi">1</span><span class="p">;</span>
          <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">+=</span> <span class="nx">sign</span> <span class="o">*</span> <span class="mi">1</span><span class="p">;</span>
          <span class="nx">delta</span> <span class="o">+=</span> <span class="nx">opsign</span> <span class="o">*</span> <span class="nx">days</span><span class="p">;</span>
        
          <span class="k">if</span> <span class="p">(</span><span class="nx">month</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">)</span> <span class="p">{</span> <span class="nx">month</span> <span class="o">=</span> <span class="mi">11</span> <span class="p">}</span>
          <span class="k">else</span> <span class="k">if</span> <span class="p">(</span><span class="nx">month</span> <span class="o">&gt;</span> <span class="mi">11</span><span class="p">)</span> <span class="p">{</span> <span class="nx">month</span> <span class="o">=</span> <span class="mi">0</span> <span class="p">}</span>
        
          <span class="nx">days</span> <span class="o">=</span> <span class="nx">months</span><span class="p">[</span><span class="nx">month</span><span class="p">];</span>
        <span class="p">}</span>
      
        <span class="nx">computed</span><span class="p">.</span><span class="nx">days</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">sign</span> <span class="o">*</span> <span class="nx">delta</span><span class="p">);</span>
      <span class="p">}</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">days</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">clean</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span><span class="p">;</span>
      <span class="p">}</span>
      <span class="k">else</span> <span class="k">if</span> <span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">days</span> <span class="o">&gt;</span> <span class="nx">months</span><span class="p">[</span><span class="nx">computed</span><span class="p">.</span><span class="nx">months</span><span class="p">])</span> <span class="p">{</span>
        <span class="nx">clean</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nx">clean</span> <span class="o">!==</span> <span class="mi">0</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">+=</span> <span class="nx">clean</span><span class="p">;</span>
        <span class="k">if</span> <span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">)</span> <span class="p">{</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">=</span> <span class="mi">11</span> <span class="p">}</span>
        <span class="k">else</span> <span class="k">if</span> <span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">&gt;</span> <span class="mi">11</span><span class="p">)</span> <span class="p">{</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">=</span> <span class="mi">0</span> <span class="p">}</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">days</span> <span class="o">=</span> <span class="nx">months</span><span class="p">[</span><span class="nx">computed</span><span class="p">.</span><span class="nx">months</span><span class="p">]</span> <span class="o">+</span> <span class="nx">computed</span><span class="p">.</span><span class="nx">days</span><span class="p">;</span>
      <span class="p">}</span>
            
      <span class="k">return</span> <span class="nx">computed</span><span class="p">;</span>
    <span class="p">}</span>
  <span class="p">},</span>
  <span class="s1">&#39;months&#39;</span><span class="o">:</span> <span class="p">{</span>
    <span class="nx">exp</span><span class="o">:</span> <span class="sr">/(\d+)month[s]?/i</span><span class="p">,</span>
    <span class="nx">get</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="nx">date</span><span class="p">.</span><span class="nx">getMonth</span><span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">set</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">val</span><span class="p">,</span> <span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="nx">date</span><span class="p">.</span><span class="nx">setMonth</span><span class="p">(</span><span class="nx">val</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">compute</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">delta</span><span class="p">,</span> <span class="nx">date</span><span class="p">,</span> <span class="nx">computed</span><span class="p">)</span> <span class="p">{</span>
      <span class="kd">var</span> <span class="nx">round</span> <span class="o">=</span> <span class="nx">delta</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="o">?</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span> <span class="o">:</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">ceil</span><span class="p">;</span>
      <span class="k">if</span> <span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="p">{</span> 
        <span class="nx">computed</span><span class="p">.</span><span class="nx">years</span> <span class="o">+=</span> <span class="nx">round</span><span class="p">.</span><span class="nx">call</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">delta</span> <span class="o">/</span> <span class="mi">12</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">+=</span> <span class="nx">delta</span> <span class="o">%</span> <span class="mi">12</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">if</span> <span class="p">(</span><span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">&gt;</span> <span class="mi">11</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">years</span> <span class="o">+=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">((</span><span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">/</span> <span class="mi">12</span><span class="p">);</span>
        <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">=</span> <span class="p">((</span><span class="nx">computed</span><span class="p">.</span><span class="nx">months</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">%</span> <span class="mi">12</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">return</span> <span class="nx">computed</span><span class="p">;</span>
    <span class="p">}</span>
  <span class="p">},</span>
  <span class="s1">&#39;years&#39;</span><span class="o">:</span> <span class="p">{</span>
    <span class="nx">exp</span><span class="o">:</span> <span class="sr">/(\d+)year[s]?/i</span><span class="p">,</span>
    <span class="nx">get</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="k">return</span> <span class="nx">date</span><span class="p">.</span><span class="nx">getFullYear</span><span class="p">(</span><span class="nx">date</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">set</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">val</span><span class="p">,</span> <span class="nx">date</span><span class="p">)</span> <span class="p">{</span> <span class="nx">date</span><span class="p">.</span><span class="nx">setFullYear</span><span class="p">(</span><span class="nx">val</span><span class="p">)</span> <span class="p">},</span>
    <span class="nx">compute</span><span class="o">:</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">delta</span><span class="p">,</span> <span class="nx">date</span><span class="p">,</span> <span class="nx">computed</span><span class="p">)</span> <span class="p">{</span>
      <span class="k">if</span> <span class="p">(</span><span class="nx">delta</span><span class="p">)</span> <span class="p">{</span> 
        <span class="nx">computed</span><span class="p">.</span><span class="nx">years</span> <span class="o">+=</span> <span class="nx">delta</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="k">return</span> <span class="nx">computed</span><span class="p">;</span>
    <span class="p">}</span>
  <span class="p">}</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-15">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-15">&#182;</a>               </div>               <p>Compute the list of parser names for
later use.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="kd">var</span> <span class="nx">parserNames</span> <span class="o">=</span> <span class="nb">Object</span><span class="p">.</span><span class="nx">keys</span><span class="p">(</span><span class="nx">parsers</span><span class="p">);</span></pre></div>             </td>           </tr>                               <tr id="section-16">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-16">&#182;</a>               </div>               <h3>function parseDate (str)</h3>

<h4>@str {string} String to parse into a date</h4>

<p>Parses the specified liberal Date-Time string according to
ISO8601 <strong>and</strong>:</p>

<ol>
<li><code>2010-04-03T12:34:15Z+12MINUTES</code></li>
<li><code>NOW-4HOURS</code></li>
</ol>

<p>Valid modifiers for the more liberal Date-Time string(s):</p>

<pre><code>YEAR, YEARS
MONTH, MONTHS
DAY, DAYS
HOUR, HOURS
MINUTE, MINUTES
SECOND, SECONDS
MILLI, MILLIS, MILLISECOND, MILLISECONDS
</code></pre>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">parseDate</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">str</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">simple</span> <span class="o">=</span> <span class="nb">Date</span><span class="p">.</span><span class="nx">parse</span><span class="p">(</span><span class="nx">str</span><span class="p">),</span>
      <span class="nx">iso</span> <span class="o">=</span> <span class="s1">&#39;^([^Z]+)&#39;</span><span class="p">,</span>
      <span class="nx">zulu</span> <span class="o">=</span> <span class="s1">&#39;Z([\\+|\\-])?&#39;</span><span class="p">,</span>
      <span class="nx">diff</span> <span class="o">=</span> <span class="p">{},</span>
      <span class="nx">base</span><span class="p">,</span>
      <span class="nx">sign</span><span class="p">,</span>
      <span class="nx">complex</span><span class="p">,</span>
      <span class="nx">inspect</span><span class="p">,</span>
      <span class="nx">dateTime</span><span class="p">,</span>
      <span class="nx">modifiers</span><span class="p">;</span>

  <span class="k">if</span> <span class="p">(</span><span class="sr">/now/i</span><span class="p">.</span><span class="nx">test</span><span class="p">(</span><span class="nx">str</span><span class="p">))</span> <span class="p">{</span>
    <span class="nx">iso</span> <span class="o">=</span> <span class="s1">&#39;^(NOW)&#39;</span><span class="p">;</span>
    <span class="nx">zulu</span> <span class="o">=</span> <span class="nx">zulu</span><span class="p">.</span><span class="nx">replace</span><span class="p">(</span><span class="sr">/Z/</span><span class="p">,</span> <span class="s1">&#39;NOW&#39;</span><span class="p">);</span>
  <span class="p">}</span></pre></div>             </td>           </tr>                               <tr id="section-17">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-17">&#182;</a>               </div>               <p>If Date string supplied actually conforms 
to UTC Time (ISO8601), return a new Date.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nb">isNaN</span><span class="p">(</span><span class="nx">simple</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span> <span class="k">new</span> <span class="nb">Date</span><span class="p">(</span><span class="nx">simple</span><span class="p">);</span>
  <span class="p">}</span>
  </pre></div>             </td>           </tr>                               <tr id="section-18">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-18">&#182;</a>               </div>               <p>Create the <code>RegExp</code> for the end component
of the target <code>str</code> to parse.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="nx">parserNames</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="kd">function</span> <span class="p">(</span><span class="nx">group</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">zulu</span> <span class="o">+=</span> <span class="s1">&#39;(\\d+[a-zA-Z]+)?&#39;</span><span class="p">;</span>
  <span class="p">});</span>
  </pre></div>             </td>           </tr>                               <tr id="section-19">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-19">&#182;</a>               </div>               <p>Parse the <code>ISO8601</code> component, and the end
component from the target <code>str</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="nx">dateTime</span> <span class="o">=</span> <span class="nx">str</span><span class="p">.</span><span class="nx">match</span><span class="p">(</span><span class="k">new</span> <span class="nb">RegExp</span><span class="p">(</span><span class="nx">iso</span><span class="p">,</span> <span class="s1">&#39;i&#39;</span><span class="p">));</span>
  <span class="nx">modifiers</span> <span class="o">=</span> <span class="nx">str</span><span class="p">.</span><span class="nx">match</span><span class="p">(</span><span class="k">new</span> <span class="nb">RegExp</span><span class="p">(</span><span class="nx">zulu</span><span class="p">,</span> <span class="s1">&#39;i&#39;</span><span class="p">));</span>
  </pre></div>             </td>           </tr>                               <tr id="section-20">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-20">&#182;</a>               </div>               <p>If there was no match on either part then 
it must be a bad value.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">dateTime</span> <span class="o">||</span> <span class="o">!</span><span class="nx">modifiers</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">return</span> <span class="kc">null</span><span class="p">;</span>
  <span class="p">}</span>
    </pre></div>             </td>           </tr>                               <tr id="section-21">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-21">&#182;</a>               </div>               <p>Create a new <code>Date</code> object from the <code>ISO8601</code>
component of the target <code>str</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="nx">base</span> <span class="o">=</span> <span class="sr">/now/i</span><span class="p">.</span><span class="nx">test</span><span class="p">(</span><span class="nx">dateTime</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span> <span class="o">?</span> <span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span> <span class="o">:</span> <span class="nb">Date</span><span class="p">.</span><span class="nx">parse</span><span class="p">(</span><span class="nx">dateTime</span><span class="p">[</span><span class="mi">1</span><span class="p">]);</span>
  <span class="nx">complex</span> <span class="o">=</span> <span class="k">new</span> <span class="nb">Date</span><span class="p">(</span><span class="nx">base</span><span class="p">);</span>
  <span class="nx">sign</span> <span class="o">=</span> <span class="nx">modifiers</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">===</span> <span class="s1">&#39;+&#39;</span> <span class="o">?</span> <span class="mi">1</span> <span class="o">:</span> <span class="o">-</span><span class="mi">1</span><span class="p">;</span>
  </pre></div>             </td>           </tr>                               <tr id="section-22">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-22">&#182;</a>               </div>               <p>Parse the individual component spans (months, years, etc)
from the modifier strings that we parsed from the end 
of the target <code>str</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="nx">modifiers</span><span class="p">.</span><span class="nx">slice</span><span class="p">(</span><span class="mi">2</span><span class="p">).</span><span class="nx">filter</span><span class="p">(</span><span class="nb">Boolean</span><span class="p">).</span><span class="nx">forEach</span><span class="p">(</span><span class="kd">function</span> <span class="p">(</span><span class="nx">modifier</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">parserNames</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="kd">function</span> <span class="p">(</span><span class="nx">name</span><span class="p">)</span> <span class="p">{</span>
      <span class="kd">var</span> <span class="nx">match</span><span class="p">;</span>
      <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="p">(</span><span class="nx">match</span> <span class="o">=</span> <span class="nx">modifier</span><span class="p">.</span><span class="nx">match</span><span class="p">(</span><span class="nx">parsers</span><span class="p">[</span><span class="nx">name</span><span class="p">].</span><span class="nx">exp</span><span class="p">)))</span> <span class="p">{</span>
        <span class="k">return</span><span class="p">;</span>
      <span class="p">}</span>
      
      <span class="nx">diff</span><span class="p">[</span><span class="nx">name</span><span class="p">]</span> <span class="o">=</span> <span class="nx">sign</span> <span class="o">*</span> <span class="nb">parseInt</span><span class="p">(</span><span class="nx">match</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="mi">10</span><span class="p">);</span>
    <span class="p">})</span>
  <span class="p">});</span>
  </pre></div>             </td>           </tr>                               <tr id="section-23">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-23">&#182;</a>               </div>               <p>Compute the total <code>diff</code> by iteratively computing 
the partial components from smallest to largest.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="kd">var</span> <span class="nx">computed</span> <span class="o">=</span> <span class="p">{</span>
    <span class="nx">milliseconds</span><span class="o">:</span> <span class="nx">complex</span><span class="p">.</span><span class="nx">getMilliseconds</span><span class="p">(),</span>
    <span class="nx">seconds</span><span class="o">:</span> <span class="nx">complex</span><span class="p">.</span><span class="nx">getSeconds</span><span class="p">(),</span>
    <span class="nx">minutes</span><span class="o">:</span> <span class="nx">complex</span><span class="p">.</span><span class="nx">getMinutes</span><span class="p">(),</span>
    <span class="nx">hours</span><span class="o">:</span> <span class="nx">complex</span><span class="p">.</span><span class="nx">getHours</span><span class="p">(),</span>
    <span class="nx">days</span><span class="o">:</span> <span class="nx">complex</span><span class="p">.</span><span class="nx">getDate</span><span class="p">(),</span>
    <span class="nx">months</span><span class="o">:</span> <span class="nx">complex</span><span class="p">.</span><span class="nx">getMonth</span><span class="p">(),</span>
    <span class="nx">years</span><span class="o">:</span> <span class="nx">complex</span><span class="p">.</span><span class="nx">getFullYear</span><span class="p">()</span>
  <span class="p">};</span>
  
  <span class="nx">parserNames</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="kd">function</span> <span class="p">(</span><span class="nx">name</span><span class="p">)</span> <span class="p">{</span>    
    <span class="nx">computed</span> <span class="o">=</span> <span class="nx">parsers</span><span class="p">[</span><span class="nx">name</span><span class="p">].</span><span class="nx">compute</span><span class="p">(</span><span class="nx">diff</span><span class="p">[</span><span class="nx">name</span><span class="p">],</span> <span class="nx">complex</span><span class="p">,</span> <span class="nx">computed</span><span class="p">);</span>
  <span class="p">});</span>
  
  <span class="k">return</span> <span class="k">new</span> <span class="nb">Date</span><span class="p">(</span>
    <span class="nb">Date</span><span class="p">.</span><span class="nx">UTC</span><span class="p">(</span>
      <span class="nx">computed</span><span class="p">.</span><span class="nx">years</span><span class="p">,</span>
      <span class="nx">computed</span><span class="p">.</span><span class="nx">months</span><span class="p">,</span>
      <span class="nx">computed</span><span class="p">.</span><span class="nx">days</span><span class="p">,</span>
      <span class="nx">computed</span><span class="p">.</span><span class="nx">hours</span><span class="p">,</span>
      <span class="nx">computed</span><span class="p">.</span><span class="nx">minutes</span><span class="p">,</span>
      <span class="nx">computed</span><span class="p">.</span><span class="nx">seconds</span><span class="p">,</span>
      <span class="nx">computed</span><span class="p">.</span><span class="nx">milliseconds</span>
    <span class="p">)</span>
  <span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-24">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-24">&#182;</a>               </div>               <h3>function fromDates (start, end, abs)</h3>

<h4>@start {Date} Start date of the <code>TimeSpan</code> instance to return</h4>

<h4>@end {Date} End date of the <code>TimeSpan</code> instance to return</h4>

<h4>@abs {boolean} Value indicating to return an absolute value</h4>

<p>Returns a new <code>TimeSpan</code> instance representing the difference between
the <code>start</code> and <code>end</code> Dates.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">fromDates</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">start</span><span class="p">,</span> <span class="nx">end</span><span class="p">,</span> <span class="nx">abs</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">start</span> <span class="k">instanceof</span> <span class="nb">Date</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">start</span> <span class="o">=</span> <span class="nx">exports</span><span class="p">.</span><span class="nx">parseDate</span><span class="p">(</span><span class="nx">start</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">end</span> <span class="k">instanceof</span> <span class="nb">Date</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">end</span> <span class="o">=</span> <span class="nx">exports</span><span class="p">.</span><span class="nx">parseDate</span><span class="p">(</span><span class="nx">end</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="kd">var</span> <span class="nx">differenceMsecs</span> <span class="o">=</span> <span class="nx">end</span><span class="p">.</span><span class="nx">valueOf</span><span class="p">()</span> <span class="o">-</span> <span class="nx">start</span><span class="p">.</span><span class="nx">valueOf</span><span class="p">();</span>
  <span class="k">if</span> <span class="p">(</span><span class="nx">abs</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">differenceMsecs</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">abs</span><span class="p">(</span><span class="nx">differenceMsecs</span><span class="p">);</span>
  <span class="p">}</span>

  <span class="k">return</span> <span class="k">new</span> <span class="nx">TimeSpan</span><span class="p">(</span><span class="nx">differenceMsecs</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-25">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-25">&#182;</a>               </div>               <h2>Module Helpers</h2>

<p>Module-level helpers for various utilities such as:
instanceOf, parsability, and cloning.</p>             </td>             <td class="code">               <div class="highlight"><pre></pre></div>             </td>           </tr>                               <tr id="section-26">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-26">&#182;</a>               </div>               <h3>function test (str)</h3>

<h4>@str {string} String value to test if it is a TimeSpan</h4>

<p>Returns a value indicating if the specified string, <code>str</code>,
is a parsable <code>TimeSpan</code> value.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">test</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">str</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nx">timeSpanWithDays</span><span class="p">.</span><span class="nx">test</span><span class="p">(</span><span class="nx">str</span><span class="p">)</span> <span class="o">||</span> <span class="nx">timeSpanNoDays</span><span class="p">.</span><span class="nx">test</span><span class="p">(</span><span class="nx">str</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-27">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-27">&#182;</a>               </div>               <h3>function instanceOf (timeSpan)</h3>

<h4>@timeSpan {Object} Object to check TimeSpan quality.</h4>

<p>Returns a value indicating if the specified <code>timeSpan</code> is
in fact a <code>TimeSpan</code> instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">instanceOf</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">timeSpan</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nx">timeSpan</span> <span class="k">instanceof</span> <span class="nx">TimeSpan</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-28">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-28">&#182;</a>               </div>               <h3>function clone (timeSpan)</h3>

<h4>@timeSpan {TimeSpan} TimeSpan object to clone.</h4>

<p>Returns a new <code>TimeSpan</code> instance with the same value
as the <code>timeSpan</code> object supplied.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">exports</span><span class="p">.</span><span class="nx">clone</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">timeSpan</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="p">(</span><span class="nx">timeSpan</span> <span class="k">instanceof</span> <span class="nx">TimeSpan</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="nx">exports</span><span class="p">.</span><span class="nx">fromMilliseconds</span><span class="p">(</span><span class="nx">timeSpan</span><span class="p">.</span><span class="nx">totalMilliseconds</span><span class="p">());</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-29">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-29">&#182;</a>               </div>               <h2>Addition</h2>

<p>Methods for adding <code>TimeSpan</code> instances, 
milliseconds, seconds, hours, and days to other
<code>TimeSpan</code> instances.</p>             </td>             <td class="code">               <div class="highlight"><pre></pre></div>             </td>           </tr>                               <tr id="section-30">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-30">&#182;</a>               </div>               <h3>function add (timeSpan)</h3>

<h4>@timeSpan {TimeSpan} TimeSpan to add to this instance</h4>

<p>Adds the specified <code>timeSpan</code> to this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">add</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">timeSpan</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="p">(</span><span class="nx">timeSpan</span> <span class="k">instanceof</span> <span class="nx">TimeSpan</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="nx">timeSpan</span><span class="p">.</span><span class="nx">totalMilliseconds</span><span class="p">();</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-31">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-31">&#182;</a>               </div>               <h3>function addMilliseconds (milliseconds)</h3>

<h4>@milliseconds {Number} Number of milliseconds to add.</h4>

<p>Adds the specified <code>milliseconds</code> to this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">addMilliseconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">milliseconds</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">milliseconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="nx">milliseconds</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-32">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-32">&#182;</a>               </div>               <h3>function addSeconds (seconds)</h3>

<h4>@seconds {Number} Number of seconds to add.</h4>

<p>Adds the specified <code>seconds</code> to this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">addSeconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">seconds</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">seconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">seconds</span> <span class="o">*</span> <span class="nx">msecPerSecond</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-33">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-33">&#182;</a>               </div>               <h3>function addMinutes (minutes)</h3>

<h4>@minutes {Number} Number of minutes to add.</h4>

<p>Adds the specified <code>minutes</code> to this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">addMinutes</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">minutes</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">minutes</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">minutes</span> <span class="o">*</span> <span class="nx">msecPerMinute</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-34">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-34">&#182;</a>               </div>               <h3>function addHours (hours)</h3>

<h4>@hours {Number} Number of hours to add.</h4>

<p>Adds the specified <code>hours</code> to this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">addHours</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">hours</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">hours</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">hours</span> <span class="o">*</span> <span class="nx">msecPerHour</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-35">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-35">&#182;</a>               </div>               <h3>function addDays (days)</h3>

<h4>@days {Number} Number of days to add.</h4>

<p>Adds the specified <code>days</code> to this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">addDays</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">days</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">days</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">+=</span> <span class="p">(</span><span class="nx">days</span> <span class="o">*</span> <span class="nx">msecPerDay</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-36">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-36">&#182;</a>               </div>               <h2>Subtraction</h2>

<p>Methods for subtracting <code>TimeSpan</code> instances, 
milliseconds, seconds, hours, and days from other
<code>TimeSpan</code> instances.</p>             </td>             <td class="code">               <div class="highlight"><pre></pre></div>             </td>           </tr>                               <tr id="section-37">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-37">&#182;</a>               </div>               <h3>function subtract (timeSpan)</h3>

<h4>@timeSpan {TimeSpan} TimeSpan to subtract from this instance.</h4>

<p>Subtracts the specified <code>timeSpan</code> from this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">subtract</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">timeSpan</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="p">(</span><span class="nx">timeSpan</span> <span class="k">instanceof</span> <span class="nx">TimeSpan</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">-=</span> <span class="nx">timeSpan</span><span class="p">.</span><span class="nx">totalMilliseconds</span><span class="p">();</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-38">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-38">&#182;</a>               </div>               <h3>function subtractMilliseconds (milliseconds)</h3>

<h4>@milliseconds {Number} Number of milliseconds to subtract.</h4>

<p>Subtracts the specified <code>milliseconds</code> from this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">subtractMilliseconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">milliseconds</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">milliseconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">-=</span> <span class="nx">milliseconds</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-39">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-39">&#182;</a>               </div>               <h3>function subtractSeconds (seconds)</h3>

<h4>@seconds {Number} Number of seconds to subtract.</h4>

<p>Subtracts the specified <code>seconds</code> from this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">subtractSeconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">seconds</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">seconds</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">-=</span> <span class="p">(</span><span class="nx">seconds</span> <span class="o">*</span> <span class="nx">msecPerSecond</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-40">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-40">&#182;</a>               </div>               <h3>function subtractMinutes (minutes)</h3>

<h4>@minutes {Number} Number of minutes to subtract.</h4>

<p>Subtracts the specified <code>minutes</code> from this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">subtractMinutes</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">minutes</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">minutes</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">-=</span> <span class="p">(</span><span class="nx">minutes</span> <span class="o">*</span> <span class="nx">msecPerMinute</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-41">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-41">&#182;</a>               </div>               <h3>function subtractHours (hours)</h3>

<h4>@hours {Number} Number of hours to subtract.</h4>

<p>Subtracts the specified <code>hours</code> from this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">subtractHours</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">hours</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">hours</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">-=</span> <span class="p">(</span><span class="nx">hours</span> <span class="o">*</span> <span class="nx">msecPerHour</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-42">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-42">&#182;</a>               </div>               <h3>function subtractDays (days)</h3>

<h4>@days {Number} Number of days to subtract.</h4>

<p>Subtracts the specified <code>days</code> from this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">subtractDays</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">days</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nx">isNumeric</span><span class="p">(</span><span class="nx">days</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">-=</span> <span class="p">(</span><span class="nx">days</span> <span class="o">*</span> <span class="nx">msecPerDay</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-43">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-43">&#182;</a>               </div>               <h2>Getters</h2>

<p>Methods for retrieving components of a <code>TimeSpan</code>
instance: milliseconds, seconds, minutes, hours, and days.</p>             </td>             <td class="code">               <div class="highlight"><pre></pre></div>             </td>           </tr>                               <tr id="section-44">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-44">&#182;</a>               </div>               <h3>function totalMilliseconds (roundDown)</h3>

<h4>@roundDown {boolean} Value indicating if the value should be rounded down.</h4>

<p>Returns the total number of milliseconds for this instance, rounding down
to the nearest integer if <code>roundDown</code> is set.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">totalMilliseconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">roundDown</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">result</span> <span class="o">=</span> <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span><span class="p">;</span>
  <span class="k">if</span> <span class="p">(</span><span class="nx">roundDown</span> <span class="o">===</span> <span class="kc">true</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">result</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="nx">result</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="nx">result</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-45">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-45">&#182;</a>               </div>               <h3>function totalSeconds (roundDown)</h3>

<h4>@roundDown {boolean} Value indicating if the value should be rounded down.</h4>

<p>Returns the total number of seconds for this instance, rounding down
to the nearest integer if <code>roundDown</code> is set.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">totalSeconds</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">roundDown</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">result</span> <span class="o">=</span> <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerSecond</span><span class="p">;</span>
  <span class="k">if</span> <span class="p">(</span><span class="nx">roundDown</span> <span class="o">===</span> <span class="kc">true</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">result</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="nx">result</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="nx">result</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-46">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-46">&#182;</a>               </div>               <h3>function totalMinutes (roundDown)</h3>

<h4>@roundDown {boolean} Value indicating if the value should be rounded down.</h4>

<p>Returns the total number of minutes for this instance, rounding down
to the nearest integer if <code>roundDown</code> is set.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">totalMinutes</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">roundDown</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">result</span> <span class="o">=</span> <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerMinute</span><span class="p">;</span>
  <span class="k">if</span> <span class="p">(</span><span class="nx">roundDown</span> <span class="o">===</span> <span class="kc">true</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">result</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="nx">result</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="nx">result</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-47">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-47">&#182;</a>               </div>               <h3>function totalHours (roundDown)</h3>

<h4>@roundDown {boolean} Value indicating if the value should be rounded down.</h4>

<p>Returns the total number of hours for this instance, rounding down
to the nearest integer if <code>roundDown</code> is set.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">totalHours</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">roundDown</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">result</span> <span class="o">=</span> <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerHour</span><span class="p">;</span>
  <span class="k">if</span> <span class="p">(</span><span class="nx">roundDown</span> <span class="o">===</span> <span class="kc">true</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">result</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="nx">result</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="nx">result</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-48">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-48">&#182;</a>               </div>               <h3>function totalDays (roundDown)</h3>

<h4>@roundDown {boolean} Value indicating if the value should be rounded down.</h4>

<p>Returns the total number of days for this instance, rounding down
to the nearest integer if <code>roundDown</code> is set.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">totalDays</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">roundDown</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">result</span> <span class="o">=</span> <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerDay</span><span class="p">;</span>
  <span class="k">if</span> <span class="p">(</span><span class="nx">roundDown</span> <span class="o">===</span> <span class="kc">true</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">result</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="nx">result</span><span class="p">);</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="nx">result</span><span class="p">;</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-49">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-49">&#182;</a>               </div>               <h3>@milliseconds</h3>

<p>Returns the length of this <code>TimeSpan</code> instance in milliseconds.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">__defineGetter__</span><span class="p">(</span><span class="s1">&#39;milliseconds&#39;</span><span class="p">,</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
  <span class="k">return</span> <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">%</span> <span class="mi">1000</span><span class="p">;</span>
<span class="p">});</span></pre></div>             </td>           </tr>                               <tr id="section-50">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-50">&#182;</a>               </div>               <h3>@seconds</h3>

<p>Returns the length of this <code>TimeSpan</code> instance in seconds.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">__defineGetter__</span><span class="p">(</span><span class="s1">&#39;seconds&#39;</span><span class="p">,</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerSecond</span><span class="p">)</span> <span class="o">%</span> <span class="mi">60</span><span class="p">;</span>
<span class="p">});</span></pre></div>             </td>           </tr>                               <tr id="section-51">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-51">&#182;</a>               </div>               <h3>@minutes</h3>

<p>Returns the length of this <code>TimeSpan</code> instance in minutes.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">__defineGetter__</span><span class="p">(</span><span class="s1">&#39;minutes&#39;</span><span class="p">,</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerMinute</span><span class="p">)</span> <span class="o">%</span> <span class="mi">60</span><span class="p">;</span>
<span class="p">});</span></pre></div>             </td>           </tr>                               <tr id="section-52">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-52">&#182;</a>               </div>               <h3>@hours</h3>

<p>Returns the length of this <code>TimeSpan</code> instance in hours.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">__defineGetter__</span><span class="p">(</span><span class="s1">&#39;hours&#39;</span><span class="p">,</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerHour</span><span class="p">)</span> <span class="o">%</span> <span class="mi">24</span><span class="p">;</span>
<span class="p">});</span></pre></div>             </td>           </tr>                               <tr id="section-53">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-53">&#182;</a>               </div>               <h3>@days</h3>

<p>Returns the length of this <code>TimeSpan</code> instance in days.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">__defineGetter__</span><span class="p">(</span><span class="s1">&#39;days&#39;</span><span class="p">,</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">floor</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">/</span> <span class="nx">msecPerDay</span><span class="p">);</span>
<span class="p">});</span></pre></div>             </td>           </tr>                               <tr id="section-54">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-54">&#182;</a>               </div>               <h2>Instance Helpers</h2>

<p>Various help methods for performing utilities
such as equality and serialization</p>             </td>             <td class="code">               <div class="highlight"><pre></pre></div>             </td>           </tr>                               <tr id="section-55">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-55">&#182;</a>               </div>               <h3>function equals (timeSpan)</h3>

<h4>@timeSpan {TimeSpan} TimeSpan instance to assert equal</h4>

<p>Returns a value indicating if the specified <code>timeSpan</code> is equal
in milliseconds to this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">equals</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">(</span><span class="nx">timeSpan</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="p">(</span><span class="nx">timeSpan</span> <span class="k">instanceof</span> <span class="nx">TimeSpan</span><span class="p">))</span> <span class="p">{</span>
    <span class="k">return</span><span class="p">;</span>
  <span class="p">}</span>
  
  <span class="k">return</span> <span class="k">this</span><span class="p">.</span><span class="nx">msecs</span> <span class="o">===</span> <span class="nx">timeSpan</span><span class="p">.</span><span class="nx">totalMilliseconds</span><span class="p">();</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-56">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-56">&#182;</a>               </div>               <h3>function toString ()</h3>

<p>Returns a string representation of this <code>TimeSpan</code>
instance according to current <code>format</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">toString</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="k">this</span><span class="p">.</span><span class="nx">format</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">return</span> <span class="k">this</span><span class="p">.</span><span class="nx">_format</span><span class="p">();</span>
  <span class="p">};</span>
  
  <span class="k">return</span> <span class="k">this</span><span class="p">.</span><span class="nx">format</span><span class="p">(</span><span class="k">this</span><span class="p">);</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-57">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-57">&#182;</a>               </div>               <h3>@private function _format ()</h3>

<p>Returns the default string representation of this instance.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nx">TimeSpan</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">_format</span> <span class="o">=</span> <span class="kd">function</span> <span class="p">()</span> <span class="p">{</span>
  <span class="k">return</span> <span class="p">[</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">days</span><span class="p">,</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">hours</span><span class="p">,</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">minutes</span><span class="p">,</span>
    <span class="k">this</span><span class="p">.</span><span class="nx">seconds</span> <span class="o">+</span> <span class="s1">&#39;.&#39;</span> <span class="o">+</span> <span class="k">this</span><span class="p">.</span><span class="nx">milliseconds</span>
  <span class="p">].</span><span class="nx">join</span><span class="p">(</span><span class="s1">&#39;:&#39;</span><span class="p">)</span>
<span class="p">};</span></pre></div>             </td>           </tr>                               <tr id="section-58">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-58">&#182;</a>               </div>               <h3>@private function isNumeric (input)</h3>

<h4>@input {Number} Value to check numeric quality of.</h4>

<p>Returns a value indicating the numeric quality of the 
specified <code>input</code>.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="kd">function</span> <span class="nx">isNumeric</span> <span class="p">(</span><span class="nx">input</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nx">input</span> <span class="o">&amp;&amp;</span> <span class="o">!</span><span class="nb">isNaN</span><span class="p">(</span><span class="nb">parseFloat</span><span class="p">(</span><span class="nx">input</span><span class="p">))</span> <span class="o">&amp;&amp;</span> <span class="nb">isFinite</span><span class="p">(</span><span class="nx">input</span><span class="p">);</span>
<span class="p">};</span>

</pre></div>             </td>           </tr>                </tbody>     </table>   </div> </body> </html> 