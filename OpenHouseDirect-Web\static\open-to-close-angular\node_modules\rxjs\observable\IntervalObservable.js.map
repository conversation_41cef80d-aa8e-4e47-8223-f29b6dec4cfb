{"version": 3, "file": "IntervalObservable.js", "sourceRoot": "", "sources": ["../../src/observable/IntervalObservable.ts"], "names": [], "mappings": ";;;;;;AACA,0BAA0B,mBAAmB,CAAC,CAAA;AAE9C,2BAA2B,eAAe,CAAC,CAAA;AAC3C,sBAAsB,oBAAoB,CAAC,CAAA;AAE3C;;;;GAIG;AACH;IAAwC,sCAAkB;IAqDxD,4BAAoB,MAAkB,EAClB,SAA6B;QADrC,sBAA0B,GAA1B,UAA0B;QAC1B,yBAAqC,GAArC,yBAAqC;QAC/C,iBAAO,CAAC;QAFU,WAAM,GAAN,MAAM,CAAY;QAClB,cAAS,GAAT,SAAS,CAAoB;QAE/C,EAAE,CAAC,CAAC,CAAC,qBAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAClB,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,SAAS,GAAG,aAAK,CAAC;QACzB,CAAC;IACH,CAAC;IA7DD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACI,yBAAM,GAAb,UAAc,MAAkB,EAClB,SAA6B;QAD7B,sBAAkB,GAAlB,UAAkB;QAClB,yBAA6B,GAA7B,yBAA6B;QACzC,MAAM,CAAC,IAAI,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAEM,2BAAQ,GAAf,UAAgB,KAAU;QAChB,uBAAK,EAAE,6BAAU,EAAE,qBAAM,CAAW;QAE5C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;QAEV,IAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IAaD,oCAAoC,CAAC,uCAAU,GAAV,UAAW,UAA8B;QAC5E,IAAM,KAAK,GAAG,CAAC,CAAC;QAChB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE;YACrE,YAAK,EAAE,sBAAU,EAAE,cAAM;SAC1B,CAAC,CAAC,CAAC;IACN,CAAC;IACH,yBAAC;AAAD,CAAC,AAzED,CAAwC,uBAAU,GAyEjD;AAzEY,0BAAkB,qBAyE9B,CAAA", "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { isNumeric } from '../util/isNumeric';\nimport { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class IntervalObservable extends Observable<number> {\n  /**\n   * Creates an Observable that emits sequential numbers every specified\n   * interval of time, on a specified IScheduler.\n   *\n   * <span class=\"informal\">Emits incremental numbers periodically in time.\n   * </span>\n   *\n   * <img src=\"./img/interval.png\" width=\"100%\">\n   *\n   * `interval` returns an Observable that emits an infinite sequence of\n   * ascending integers, with a constant interval of time of your choosing\n   * between those emissions. The first emission is not sent immediately, but\n   * only after the first period has passed. By default, this operator uses the\n   * `async` IScheduler to provide a notion of time, but you may pass any\n   * IScheduler to it.\n   *\n   * @example <caption>Emits ascending numbers, one every second (1000ms)</caption>\n   * var numbers = Rx.Observable.interval(1000);\n   * numbers.subscribe(x => console.log(x));\n   *\n   * @see {@link timer}\n   * @see {@link delay}\n   *\n   * @param {number} [period=0] The interval size in milliseconds (by default)\n   * or the time unit determined by the scheduler's clock.\n   * @param {Scheduler} [scheduler=async] The IScheduler to use for scheduling\n   * the emission of values, and providing a notion of \"time\".\n   * @return {Observable} An Observable that emits a sequential number each time\n   * interval.\n   * @static true\n   * @name interval\n   * @owner Observable\n   */\n  static create(period: number = 0,\n                scheduler: IScheduler = async): Observable<number> {\n    return new IntervalObservable(period, scheduler);\n  }\n\n  static dispatch(state: any): void {\n    const { index, subscriber, period } = state;\n\n    subscriber.next(index);\n\n    if (subscriber.closed) {\n      return;\n    }\n\n    state.index += 1;\n\n    (<any> this).schedule(state, period);\n  }\n\n  constructor(private period: number = 0,\n              private scheduler: IScheduler = async) {\n    super();\n    if (!isNumeric(period) || period < 0) {\n      this.period = 0;\n    }\n    if (!scheduler || typeof scheduler.schedule !== 'function') {\n      this.scheduler = async;\n    }\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<number>) {\n    const index = 0;\n    const period = this.period;\n    const scheduler = this.scheduler;\n\n    subscriber.add(scheduler.schedule(IntervalObservable.dispatch, period, {\n      index, subscriber, period\n    }));\n  }\n}\n"]}