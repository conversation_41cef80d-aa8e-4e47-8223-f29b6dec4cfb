{"__symbolic": "module", "version": 4, "metadata": {"MutationObserverFactory": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 30, "character": 1}}], "members": {"create": [{"__symbolic": "method"}]}}, "CdkObserveContent": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 41, "character": 1}, "arguments": [{"selector": "[cdkObserveContent]", "exportAs": "cdkObserveContent"}]}], "members": {"event": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 50, "character": 3}, "arguments": ["cdkObserveContent"]}]}], "disabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 56, "character": 3}, "arguments": ["cdkObserveContentDisabled"]}]}], "debounce": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 66, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "MutationObserverFactory"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 70, "character": 25}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 71, "character": 21}]}], "ngAfterContentInit": [{"__symbolic": "method"}], "ngOnChanges": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "_disable": [{"__symbolic": "method"}], "_enable": [{"__symbolic": "method"}]}}, "ObserversModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 123, "character": 1}, "arguments": [{"exports": [{"__symbolic": "reference", "name": "ObserveContent"}], "declarations": [{"__symbolic": "reference", "name": "ObserveContent"}], "providers": [{"__symbolic": "reference", "name": "MutationObserverFactory"}]}]}], "members": {}}, "ObserveContent": {"__symbolic": "reference", "name": "CdkObserveContent"}}, "origins": {"MutationObserverFactory": "./observe-content", "CdkObserveContent": "./observe-content", "ObserversModule": "./observe-content", "ObserveContent": "./observe-content"}, "importAs": "@angular/cdk/observers"}