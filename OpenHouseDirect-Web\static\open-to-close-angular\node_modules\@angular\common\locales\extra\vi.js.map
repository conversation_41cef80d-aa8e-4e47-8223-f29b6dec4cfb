{"version": 3, "file": "vi.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/vi.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;QAChD,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;KACjD;IACD;QACE,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;QAClD,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;QAChD,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;KACnD;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['nửa đêm', 'tr', 'sáng', 'chiều', 'tối', 'đêm'],\n    ['nửa đêm', 'TR', 'sáng', 'chiều', 'tối', 'đêm'],\n  ],\n  [\n    ['nửa đêm', 'trưa', 'sáng', 'chiều', 'tối', 'đêm'],\n    ['nửa đêm', 'TR', 'sáng', 'chiều', 'tối', 'đêm'],\n    ['nửa đêm', 'trưa', 'sáng', 'chiều', 'tối', 'đêm']\n  ],\n  [\n    '00:00', '12:00', ['04:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '04:00']\n  ]\n];\n"]}