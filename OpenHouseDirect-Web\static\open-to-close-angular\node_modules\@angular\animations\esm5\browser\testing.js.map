{"version": 3, "file": "testing.js", "sources": ["../../../../packages/animations/esm5/browser/src/render/shared.js", "../../../../packages/animations/esm5/browser/src/util.js", "../../../../packages/animations/esm5/browser/testing/src/mock_animation_driver.js", "../../../../packages/animations/esm5/browser/testing/src/testing.js", "../../../../packages/animations/esm5/browser/testing/public_api.js", "../../../../packages/animations/esm5/browser/testing/testing.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\n/**\n * @param {?} players\n * @return {?}\n */\nexport function optimizeGroupPlayer(players) {\n    switch (players.length) {\n        case 0:\n            return new NoopAnimationPlayer();\n        case 1:\n            return players[0];\n        default:\n            return new ɵAnimationGroupPlayer(players);\n    }\n}\n/**\n * @param {?} driver\n * @param {?} normalizer\n * @param {?} element\n * @param {?} keyframes\n * @param {?=} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nexport function normalizeKeyframes(driver, normalizer, element, keyframes, preStyles, postStyles) {\n    if (preStyles === void 0) { preStyles = {}; }\n    if (postStyles === void 0) { postStyles = {}; }\n    var /** @type {?} */ errors = [];\n    var /** @type {?} */ normalizedKeyframes = [];\n    var /** @type {?} */ previousOffset = -1;\n    var /** @type {?} */ previousKeyframe = null;\n    keyframes.forEach(function (kf) {\n        var /** @type {?} */ offset = /** @type {?} */ (kf['offset']);\n        var /** @type {?} */ isSameOffset = offset == previousOffset;\n        var /** @type {?} */ normalizedKeyframe = (isSameOffset && previousKeyframe) || {};\n        Object.keys(kf).forEach(function (prop) {\n            var /** @type {?} */ normalizedProp = prop;\n            var /** @type {?} */ normalizedValue = kf[prop];\n            if (prop !== 'offset') {\n                normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n                switch (normalizedValue) {\n                    case PRE_STYLE:\n                        normalizedValue = preStyles[prop];\n                        break;\n                    case AUTO_STYLE:\n                        normalizedValue = postStyles[prop];\n                        break;\n                    default:\n                        normalizedValue =\n                            normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n                        break;\n                }\n            }\n            normalizedKeyframe[normalizedProp] = normalizedValue;\n        });\n        if (!isSameOffset) {\n            normalizedKeyframes.push(normalizedKeyframe);\n        }\n        previousKeyframe = normalizedKeyframe;\n        previousOffset = offset;\n    });\n    if (errors.length) {\n        var /** @type {?} */ LINE_START = '\\n - ';\n        throw new Error(\"Unable to animate due to the following errors:\" + LINE_START + errors.join(LINE_START));\n    }\n    return normalizedKeyframes;\n}\n/**\n * @param {?} player\n * @param {?} eventName\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nexport function listenOnPlayer(player, eventName, event, callback) {\n    switch (eventName) {\n        case 'start':\n            player.onStart(function () { return callback(event && copyAnimationEvent(event, 'start', player.totalTime)); });\n            break;\n        case 'done':\n            player.onDone(function () { return callback(event && copyAnimationEvent(event, 'done', player.totalTime)); });\n            break;\n        case 'destroy':\n            player.onDestroy(function () { return callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)); });\n            break;\n    }\n}\n/**\n * @param {?} e\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function copyAnimationEvent(e, phaseName, totalTime) {\n    var /** @type {?} */ event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime);\n    var /** @type {?} */ data = (/** @type {?} */ (e))['_data'];\n    if (data != null) {\n        (/** @type {?} */ (event))['_data'] = data;\n    }\n    return event;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function makeAnimationEvent(element, triggerName, fromState, toState, phaseName, totalTime) {\n    if (phaseName === void 0) { phaseName = ''; }\n    if (totalTime === void 0) { totalTime = 0; }\n    return { element: element, triggerName: triggerName, fromState: fromState, toState: toState, phaseName: phaseName, totalTime: totalTime };\n}\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} defaultValue\n * @return {?}\n */\nexport function getOrSetAsInMap(map, key, defaultValue) {\n    var /** @type {?} */ value;\n    if (map instanceof Map) {\n        value = map.get(key);\n        if (!value) {\n            map.set(key, value = defaultValue);\n        }\n    }\n    else {\n        value = map[key];\n        if (!value) {\n            value = map[key] = defaultValue;\n        }\n    }\n    return value;\n}\n/**\n * @param {?} command\n * @return {?}\n */\nexport function parseTimelineCommand(command) {\n    var /** @type {?} */ separatorPos = command.indexOf(':');\n    var /** @type {?} */ id = command.substring(1, separatorPos);\n    var /** @type {?} */ action = command.substr(separatorPos + 1);\n    return [id, action];\n}\nvar /** @type {?} */ _contains = function (elm1, elm2) { return false; };\nvar ɵ0 = _contains;\nvar /** @type {?} */ _matches = function (element, selector) {\n    return false;\n};\nvar ɵ1 = _matches;\nvar /** @type {?} */ _query = function (element, selector, multi) {\n    return [];\n};\nvar ɵ2 = _query;\nif (typeof Element != 'undefined') {\n    // this is well supported in all browsers\n    _contains = function (elm1, elm2) { return /** @type {?} */ (elm1.contains(elm2)); };\n    if (Element.prototype.matches) {\n        _matches = function (element, selector) { return element.matches(selector); };\n    }\n    else {\n        var /** @type {?} */ proto = /** @type {?} */ (Element.prototype);\n        var /** @type {?} */ fn_1 = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n            proto.oMatchesSelector || proto.webkitMatchesSelector;\n        if (fn_1) {\n            _matches = function (element, selector) { return fn_1.apply(element, [selector]); };\n        }\n    }\n    _query = function (element, selector, multi) {\n        var /** @type {?} */ results = [];\n        if (multi) {\n            results.push.apply(results, element.querySelectorAll(selector));\n        }\n        else {\n            var /** @type {?} */ elm = element.querySelector(selector);\n            if (elm) {\n                results.push(elm);\n            }\n        }\n        return results;\n    };\n}\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction containsVendorPrefix(prop) {\n    // Webkit is the only real popular vendor prefix nowadays\n    // cc: http://shouldiprefix.com/\n    return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nvar /** @type {?} */ _CACHED_BODY = null;\nvar /** @type {?} */ _IS_WEBKIT = false;\n/**\n * @param {?} prop\n * @return {?}\n */\nexport function validateStyleProperty(prop) {\n    if (!_CACHED_BODY) {\n        _CACHED_BODY = getBodyNode() || {};\n        _IS_WEBKIT = /** @type {?} */ ((_CACHED_BODY)).style ? ('WebkitAppearance' in /** @type {?} */ ((_CACHED_BODY)).style) : false;\n    }\n    var /** @type {?} */ result = true;\n    if (/** @type {?} */ ((_CACHED_BODY)).style && !containsVendorPrefix(prop)) {\n        result = prop in /** @type {?} */ ((_CACHED_BODY)).style;\n        if (!result && _IS_WEBKIT) {\n            var /** @type {?} */ camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n            result = camelProp in /** @type {?} */ ((_CACHED_BODY)).style;\n        }\n    }\n    return result;\n}\n/**\n * @return {?}\n */\nexport function getBodyNode() {\n    if (typeof document != 'undefined') {\n        return document.body;\n    }\n    return null;\n}\nexport var /** @type {?} */ matchesElement = _matches;\nexport var /** @type {?} */ containsElement = _contains;\nexport var /** @type {?} */ invokeQuery = _query;\nexport { ɵ0, ɵ1, ɵ2 };\n//# sourceMappingURL=shared.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { sequence } from '@angular/animations';\nexport var /** @type {?} */ ONE_SECOND = 1000;\nexport var /** @type {?} */ SUBSTITUTION_EXPR_START = '{{';\nexport var /** @type {?} */ SUBSTITUTION_EXPR_END = '}}';\nexport var /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport var /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport var /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport var /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport var /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport var /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport var /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport var /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value) {\n    if (typeof value == 'number')\n        return value;\n    var /** @type {?} */ matches = (/** @type {?} */ (value)).match(/^(-?[\\.\\d]+)(m?s)/);\n    if (!matches || matches.length < 2)\n        return 0;\n    return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value, unit) {\n    switch (unit) {\n        case 's':\n            return value * ONE_SECOND;\n        default:\n            // ms or something else\n            return value;\n    }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(timings, errors, allowNegativeValues) {\n    return timings.hasOwnProperty('duration') ? /** @type {?} */ (timings) :\n        parseTimeExpression(/** @type {?} */ (timings), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n    var /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n    var /** @type {?} */ duration;\n    var /** @type {?} */ delay = 0;\n    var /** @type {?} */ easing = '';\n    if (typeof exp === 'string') {\n        var /** @type {?} */ matches = exp.match(regex);\n        if (matches === null) {\n            errors.push(\"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n            return { duration: 0, delay: 0, easing: '' };\n        }\n        duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n        var /** @type {?} */ delayMatch = matches[3];\n        if (delayMatch != null) {\n            delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n        }\n        var /** @type {?} */ easingVal = matches[5];\n        if (easingVal) {\n            easing = easingVal;\n        }\n    }\n    else {\n        duration = /** @type {?} */ (exp);\n    }\n    if (!allowNegativeValues) {\n        var /** @type {?} */ containsErrors = false;\n        var /** @type {?} */ startIndex = errors.length;\n        if (duration < 0) {\n            errors.push(\"Duration values below 0 are not allowed for this animation step.\");\n            containsErrors = true;\n        }\n        if (delay < 0) {\n            errors.push(\"Delay values below 0 are not allowed for this animation step.\");\n            containsErrors = true;\n        }\n        if (containsErrors) {\n            errors.splice(startIndex, 0, \"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n        }\n    }\n    return { duration: duration, delay: delay, easing: easing };\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(obj, destination) {\n    if (destination === void 0) { destination = {}; }\n    Object.keys(obj).forEach(function (prop) { destination[prop] = obj[prop]; });\n    return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles) {\n    var /** @type {?} */ normalizedStyles = {};\n    if (Array.isArray(styles)) {\n        styles.forEach(function (data) { return copyStyles(data, false, normalizedStyles); });\n    }\n    else {\n        copyStyles(styles, false, normalizedStyles);\n    }\n    return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(styles, readPrototype, destination) {\n    if (destination === void 0) { destination = {}; }\n    if (readPrototype) {\n        // we make use of a for-in loop so that the\n        // prototypically inherited properties are\n        // revealed from the backFill map\n        for (var /** @type {?} */ prop in styles) {\n            destination[prop] = styles[prop];\n        }\n    }\n    else {\n        copyObj(styles, destination);\n    }\n    return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = styles[prop];\n        });\n    }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = '';\n        });\n    }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps) {\n    if (Array.isArray(steps)) {\n        if (steps.length == 1)\n            return steps[0];\n        return sequence(steps);\n    }\n    return /** @type {?} */ (steps);\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(value, options, errors) {\n    var /** @type {?} */ params = options.params || {};\n    var /** @type {?} */ matches = extractStyleParams(value);\n    if (matches.length) {\n        matches.forEach(function (varName) {\n            if (!params.hasOwnProperty(varName)) {\n                errors.push(\"Unable to resolve the local animation param \" + varName + \" in the given list of values\");\n            }\n        });\n    }\n}\nvar /** @type {?} */ PARAM_REGEX = new RegExp(SUBSTITUTION_EXPR_START + \"\\\\s*(.+?)\\\\s*\" + SUBSTITUTION_EXPR_END, 'g');\n/**\n * @param {?} value\n * @return {?}\n */\nexport function extractStyleParams(value) {\n    var /** @type {?} */ params = [];\n    if (typeof value === 'string') {\n        var /** @type {?} */ val = value.toString();\n        var /** @type {?} */ match = void 0;\n        while (match = PARAM_REGEX.exec(val)) {\n            params.push(/** @type {?} */ (match[1]));\n        }\n        PARAM_REGEX.lastIndex = 0;\n    }\n    return params;\n}\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(value, params, errors) {\n    var /** @type {?} */ original = value.toString();\n    var /** @type {?} */ str = original.replace(PARAM_REGEX, function (_, varName) {\n        var /** @type {?} */ localVal = params[varName];\n        // this means that the value was never overidden by the data passed in by the user\n        if (!params.hasOwnProperty(varName)) {\n            errors.push(\"Please provide a value for the animation param \" + varName);\n            localVal = '';\n        }\n        return localVal.toString();\n    });\n    // we do this to assert that numeric values stay as they are\n    return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator) {\n    var /** @type {?} */ arr = [];\n    var /** @type {?} */ item = iterator.next();\n    while (!item.done) {\n        arr.push(item.value);\n        item = iterator.next();\n    }\n    return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(source, destination) {\n    if (source.params) {\n        var /** @type {?} */ p0_1 = source.params;\n        if (!destination.params) {\n            destination.params = {};\n        }\n        var /** @type {?} */ p1_1 = destination.params;\n        Object.keys(p0_1).forEach(function (param) {\n            if (!p1_1.hasOwnProperty(param)) {\n                p1_1[param] = p0_1[param];\n            }\n        });\n    }\n    return destination;\n}\nvar /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, function () {\n        var m = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            m[_i] = arguments[_i];\n        }\n        return m[1].toUpperCase();\n    });\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nexport function allowPreviousPlayerStylesMerge(duration, delay) {\n    return duration === 0 || delay === 0;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitDslNode(visitor, node, context) {\n    switch (node.type) {\n        case 7 /* Trigger */:\n            return visitor.visitTrigger(node, context);\n        case 0 /* State */:\n            return visitor.visitState(node, context);\n        case 1 /* Transition */:\n            return visitor.visitTransition(node, context);\n        case 2 /* Sequence */:\n            return visitor.visitSequence(node, context);\n        case 3 /* Group */:\n            return visitor.visitGroup(node, context);\n        case 4 /* Animate */:\n            return visitor.visitAnimate(node, context);\n        case 5 /* Keyframes */:\n            return visitor.visitKeyframes(node, context);\n        case 6 /* Style */:\n            return visitor.visitStyle(node, context);\n        case 8 /* Reference */:\n            return visitor.visitReference(node, context);\n        case 9 /* AnimateChild */:\n            return visitor.visitAnimateChild(node, context);\n        case 10 /* AnimateRef */:\n            return visitor.visitAnimateRef(node, context);\n        case 11 /* Query */:\n            return visitor.visitQuery(node, context);\n        case 12 /* Stagger */:\n            return visitor.visitStagger(node, context);\n        default:\n            throw new Error(\"Unable to resolve animation metadata node #\" + node.type);\n    }\n}\n//# sourceMappingURL=util.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport * as tslib_1 from \"tslib\";\nimport { AUTO_STYLE, NoopAnimationPlayer } from '@angular/animations';\nimport { containsElement, invokeQuery, matchesElement, validateStyleProperty } from '../../src/render/shared';\nimport { allowPreviousPlayerStylesMerge } from '../../src/util';\n/**\n * \\@experimental Animation support is experimental.\n */\nvar MockAnimationDriver = /** @class */ (function () {\n    function MockAnimationDriver() {\n    }\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.validateStyleProperty = /**\n     * @param {?} prop\n     * @return {?}\n     */\n    function (prop) { return validateStyleProperty(prop); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.matchesElement = /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    function (element, selector) {\n        return matchesElement(element, selector);\n    };\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.containsElement = /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    function (elm1, elm2) { return containsElement(elm1, elm2); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.query = /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    function (element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    };\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.computeStyle = /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    function (element, prop, defaultValue) {\n        return defaultValue || '';\n    };\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.animate = /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    function (element, keyframes, duration, delay, easing, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        var /** @type {?} */ player = new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n        MockAnimationDriver.log.push(/** @type {?} */ (player));\n        return player;\n    };\n    MockAnimationDriver.log = [];\n    return MockAnimationDriver;\n}());\nexport { MockAnimationDriver };\nfunction MockAnimationDriver_tsickle_Closure_declarations() {\n    /** @type {?} */\n    MockAnimationDriver.log;\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nvar /**\n * \\@experimental Animation support is experimental.\n */\nMockAnimationPlayer = /** @class */ (function (_super) {\n    tslib_1.__extends(MockAnimationPlayer, _super);\n    function MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers) {\n        var _this = _super.call(this) || this;\n        _this.element = element;\n        _this.keyframes = keyframes;\n        _this.duration = duration;\n        _this.delay = delay;\n        _this.easing = easing;\n        _this.previousPlayers = previousPlayers;\n        _this.__finished = false;\n        _this.__started = false;\n        _this.previousStyles = {};\n        _this._onInitFns = [];\n        _this.currentSnapshot = {};\n        if (allowPreviousPlayerStylesMerge(duration, delay)) {\n            previousPlayers.forEach(function (player) {\n                if (player instanceof MockAnimationPlayer) {\n                    var /** @type {?} */ styles_1 = player.currentSnapshot;\n                    Object.keys(styles_1).forEach(function (prop) { return _this.previousStyles[prop] = styles_1[prop]; });\n                }\n            });\n        }\n        _this.totalTime = delay + duration;\n        return _this;\n    }\n    /* @internal */\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.onInit = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onInitFns.push(fn); };\n    /* @internal */\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.init.call(this);\n        this._onInitFns.forEach(function (fn) { return fn(); });\n        this._onInitFns = [];\n    };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.finish.call(this);\n        this.__finished = true;\n    };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.destroy.call(this);\n        this.__finished = true;\n    };\n    /* @internal */\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.triggerMicrotask = /**\n     * @return {?}\n     */\n    function () { };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.play.call(this);\n        this.__started = true;\n    };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this.__started; };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.beforeDestroy = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        var /** @type {?} */ captures = {};\n        Object.keys(this.previousStyles).forEach(function (prop) {\n            captures[prop] = _this.previousStyles[prop];\n        });\n        if (this.hasStarted()) {\n            // when assembling the captured styles, it's important that\n            // we build the keyframe styles in the following order:\n            // {other styles within keyframes, ... previousStyles }\n            this.keyframes.forEach(function (kf) {\n                Object.keys(kf).forEach(function (prop) {\n                    if (prop != 'offset') {\n                        captures[prop] = _this.__finished ? kf[prop] : AUTO_STYLE;\n                    }\n                });\n            });\n        }\n        this.currentSnapshot = captures;\n    };\n    return MockAnimationPlayer;\n}(NoopAnimationPlayer));\n/**\n * \\@experimental Animation support is experimental.\n */\nexport { MockAnimationPlayer };\nfunction MockAnimationPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    MockAnimationPlayer.prototype.__finished;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.__started;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.previousStyles;\n    /** @type {?} */\n    MockAnimationPlayer.prototype._onInitFns;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.currentSnapshot;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.element;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.keyframes;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.duration;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.delay;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.easing;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.previousPlayers;\n}\n//# sourceMappingURL=mock_animation_driver.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nexport { MockAnimationDriver, MockAnimationPlayer } from './mock_animation_driver';\n//# sourceMappingURL=testing.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport { MockAnimationDriver, MockAnimationPlayer } from './src/testing';\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { MockAnimationDriver, MockAnimationPlayer } from './public_api';\n//# sourceMappingURL=testing.js.map"], "names": ["tslib_1.__extends"], "mappings": ";;;;;;;;AAAA;;;;AAIA,AACA;;;;AAIA,AASC;;;;;;;;;;AAUD,AA0CC;;;;;;;;AAQD,AAYC;;;;;;;AAOD,AAOC;;;;;;;;;;AAUD,AAIC;;;;;;;AAOD,AAeC;;;;;AAKD,AAKC;AACD,IAAqB,SAAS,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;AACzE,AACA,IAAqB,QAAQ,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE;IACzD,OAAO,KAAK,CAAC;CAChB,CAAC;AACF,AACA,IAAqB,MAAM,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;IAC9D,OAAO,EAAE,CAAC;CACb,CAAC;AACF,AACA,IAAI,OAAO,OAAO,IAAI,WAAW,EAAE;;IAE/B,SAAS,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,yBAAyB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACrF,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC3B,QAAQ,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;KACjF;SACI;QACD,qBAAqB,KAAK,qBAAqB,OAAO,CAAC,SAAS,CAAC,CAAC;QAClE,qBAAqB,IAAI,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,iBAAiB;YACpG,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,qBAAqB,CAAC;QAC1D,IAAI,IAAI,EAAE;YACN,QAAQ,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;SACvF;KACJ;IACD,MAAM,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QACzC,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,KAAK,EAAE;YACP,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACnE;aACI;YACD,qBAAqB,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC3D,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrB;SACJ;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;CACL;;;;;AAKD,SAAS,oBAAoB,CAAC,IAAI,EAAE;;;IAGhC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC;CAC1C;AACD,IAAqB,YAAY,GAAG,IAAI,CAAC;AACzC,IAAqB,UAAU,GAAG,KAAK,CAAC;;;;;AAKxC,AAAO,SAAS,qBAAqB,CAAC,IAAI,EAAE;IACxC,IAAI,CAAC,YAAY,EAAE;QACf,YAAY,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC;QACnC,UAAU,oBAAoB,EAAE,YAAY,GAAG,KAAK,IAAI,kBAAkB,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,KAAK,CAAC;KAClI;IACD,qBAAqB,MAAM,GAAG,IAAI,CAAC;IACnC,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;QACxE,MAAM,GAAG,IAAI,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;QACzD,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;YACvB,qBAAqB,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1F,MAAM,GAAG,SAAS,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;SACjE;KACJ;IACD,OAAO,MAAM,CAAC;CACjB;;;;AAID,AAAO,SAAS,WAAW,GAAG;IAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;QAChC,OAAO,QAAQ,CAAC,IAAI,CAAC;KACxB;IACD,OAAO,IAAI,CAAC;CACf;AACD,AAAO,IAAqB,cAAc,GAAG,QAAQ,CAAC;AACtD,AAAO,IAAqB,eAAe,GAAG,SAAS,CAAC;AACxD,AAAO,IAAqB,WAAW,GAAG,MAAM;;ACtOhD;;;;AAIA,AAC8C;AAC9C,AAA2D;AAC3D,AAAyD;AACzD,AAAyD;AACzD,AAAyD;AACzD,AAAyD;AACzD,AAAyD;AACzD,AAAgE;AAChE,AAAgE;AAChE,AAAoE;AACpE,AAAoE;;;;;AAKpE,AAOC;AACD,AAcA;;;;;;AAMA,AAGC;AACD,AA+CA;;;;;AAKA,AAIC;;;;;AAKD,AASC;;;;;;;AAOD,AAcC;;;;;;AAMD,AAOC;;;;;;AAMD,AAOC;;;;;AAKD,AAOC;;;;;;;AAOD,AAUC;AACD,AACA;;;;AAIA,AAWC;;;;;;;AAOD,AAaC;;;;;AAKD,AAQC;;;;;;AAMD,AAcC;AACD,AACA;;;;AAIA,AAQC;;;;;;AAMD,AAAO,SAAS,8BAA8B,CAAC,QAAQ,EAAE,KAAK,EAAE;IAC5D,OAAO,QAAQ,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;CACxC;;;;;;GAME;;ACxSH;;;;AAIA,AAIA;;;AAGA,IAAI,mBAAmB,kBAAkB,YAAY;IACjD,SAAS,mBAAmB,GAAG;KAC9B;;;;;IAKD,mBAAmB,CAAC,SAAS,CAAC,qBAAqB;;;;IAInD,UAAU,IAAI,EAAE,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;;;;;;IAMxD,mBAAmB,CAAC,SAAS,CAAC,cAAc;;;;;IAK5C,UAAU,OAAO,EAAE,QAAQ,EAAE;QACzB,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC5C,CAAC;;;;;;IAMF,mBAAmB,CAAC,SAAS,CAAC,eAAe;;;;;IAK7C,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;;;;;;;IAO9D,mBAAmB,CAAC,SAAS,CAAC,KAAK;;;;;;IAMnC,UAAU,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAChC,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAChD,CAAC;;;;;;;IAOF,mBAAmB,CAAC,SAAS,CAAC,YAAY;;;;;;IAM1C,UAAU,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE;QACnC,OAAO,YAAY,IAAI,EAAE,CAAC;KAC7B,CAAC;;;;;;;;;;IAUF,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;;;;;;;IASrC,UAAU,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE;QACpE,IAAI,eAAe,KAAK,KAAK,CAAC,EAAE,EAAE,eAAe,GAAG,EAAE,CAAC,EAAE;QACzD,qBAAqB,MAAM,GAAG,IAAI,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;QACpH,mBAAmB,CAAC,GAAG,CAAC,IAAI,mBAAmB,MAAM,EAAE,CAAC;QACxD,OAAO,MAAM,CAAC;KACjB,CAAC;IACF,mBAAmB,CAAC,GAAG,GAAG,EAAE,CAAC;IAC7B,OAAO,mBAAmB,CAAC;CAC9B,EAAE,CAAC,CAAC;AACL,AAKA;;;AAGA,IAGA,mBAAmB,kBAAkB,UAAU,MAAM,EAAE;IACnDA,SAAiB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;IAC/C,SAAS,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE;QACvF,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACxB,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC5B,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC1B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACpB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACtB,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;QACxC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;QACzB,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;QACxB,KAAK,CAAC,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;QACtB,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;YACjD,eAAe,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;gBACtC,IAAI,MAAM,YAAY,mBAAmB,EAAE;oBACvC,qBAAqB,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;oBACvD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC1G;aACJ,CAAC,CAAC;SACN;QACD,KAAK,CAAC,SAAS,GAAG,KAAK,GAAG,QAAQ,CAAC;QACnC,OAAO,KAAK,CAAC;KAChB;;;;;;IAMD,mBAAmB,CAAC,SAAS,CAAC,MAAM;;;;IAIpC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAK5C,mBAAmB,CAAC,SAAS,CAAC,IAAI;;;IAGlC,YAAY;QACR,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACxB,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,MAAM;;;IAGpC,YAAY;QACR,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;IAGrC,YAAY;QACR,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B,CAAC;;;;;IAKF,mBAAmB,CAAC,SAAS,CAAC,gBAAgB;;;IAG9C,YAAY,GAAG,CAAC;;;;IAIhB,mBAAmB,CAAC,SAAS,CAAC,IAAI;;;IAGlC,YAAY;QACR,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,UAAU;;;IAGxC,YAAY,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;;;;IAIvC,mBAAmB,CAAC,SAAS,CAAC,aAAa;;;IAG3C,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,QAAQ,GAAG,EAAE,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACrD,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC/C,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;YAInB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;gBACjC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;oBACpC,IAAI,IAAI,IAAI,QAAQ,EAAE;wBAClB,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;qBAC7D;iBACJ,CAAC,CAAC;aACN,CAAC,CAAC;SACN;QACD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;KACnC,CAAC;IACF,OAAO,mBAAmB,CAAC;CAC9B,CAAC,mBAAmB,CAAC,CAAC;;AC3OvB;;;GAGG;;ACHH;;;;;;;;;;;;;;;GAeG;;ACfH;;;;;;GAMG;;;;"}