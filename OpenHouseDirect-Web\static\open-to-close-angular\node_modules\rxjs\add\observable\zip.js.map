{"version": 3, "file": "zip.js", "sourceRoot": "", "sources": ["../../../src/add/observable/zip.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,oBAAiC,sBAAsB,CAAC,CAAA;AAExD,uBAAU,CAAC,GAAG,GAAG,SAAS,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { zip as zipStatic } from '../../observable/zip';\n\nObservable.zip = zipStatic;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let zip: typeof zipStatic;\n  }\n}"]}