/// <amd-module name="tsickle/src/util" />
import * as ts from 'typescript';
/**
 * Constructs a new ts.CompilerHost that overlays sources in substituteSource
 * over another ts.CompilerHost.
 *
 * @param substituteSource A map of source file name -> overlay source text.
 */
export declare function createSourceReplacingCompilerHost(substituteSource: Map<string, string>, delegate: ts.CompilerHost): ts.CompilerHost;
/**
 * Returns the input string with line endings normalized to '\n'.
 */
export declare function normalizeLineEndings(input: string): string;
/** @return true if node has the specified modifier flag set. */
export declare function hasModifierFlag(node: ts.Node, flag: ts.ModifierFlags): boolean;
export declare function isDtsFileName(fileName: string): boolean;
/**
 * Determine the lowest-level common parent directory of the given list of files.
 */
export declare function getCommonParentDirectory(fileNames: string[]): string;
