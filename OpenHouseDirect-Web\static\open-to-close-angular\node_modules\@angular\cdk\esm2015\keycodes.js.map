{"version": 3, "file": "keycodes.js", "sources": ["../../../src/cdk/keycodes/index.ts", "../../../src/cdk/keycodes/public-api.ts", "../../../src/cdk/keycodes/keycodes.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './keycodes';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport const UP_ARROW = 38;\nexport const DOWN_ARROW = 40;\nexport const RIGHT_ARROW = 39;\nexport const LEFT_ARROW = 37;\nexport const PAGE_UP = 33;\nexport const PAGE_DOWN = 34;\nexport const HOME = 36;\nexport const END = 35;\nexport const ENTER = 13;\nexport const SPACE = 32;\nexport const TAB = 9;\nexport const ESCAPE = 27;\nexport const BACKSPACE = 8;\nexport const DELETE = 46;\nexport const A = 65;\nexport const Z = 90;\nexport const ZERO = 48;\nexport const NINE = 57;\nexport const COMMA = 188;\n"], "names": [], "mappings": ";;;;;;;;;;;;AEQA,AAAO,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC3B,AAAO,MAAM,UAAU,GAAG,EAAE,CAAC;AAC7B,AAAO,MAAM,WAAW,GAAG,EAAE,CAAC;AAC9B,AAAO,MAAM,UAAU,GAAG,EAAE,CAAC;AAC7B,AAAO,MAAM,OAAO,GAAG,EAAE,CAAC;AAC1B,AAAO,MAAM,SAAS,GAAG,EAAE,CAAC;AAC5B,AAAO,MAAM,IAAI,GAAG,EAAE,CAAC;AACvB,AAAO,MAAM,GAAG,GAAG,EAAE,CAAC;AACtB,AAAO,MAAM,KAAK,GAAG,EAAE,CAAC;AACxB,AAAO,MAAM,KAAK,GAAG,EAAE,CAAC;AACxB,AAAO,MAAM,GAAG,GAAG,CAAC,CAAC;AACrB,AAAO,MAAM,MAAM,GAAG,EAAE,CAAC;AACzB,AAAO,MAAM,SAAS,GAAG,CAAC,CAAC;AAC3B,AAAO,MAAM,MAAM,GAAG,EAAE,CAAC;AACzB,AAAO,MAAM,CAAC,GAAG,EAAE,CAAC;AACpB,AAAO,MAAM,CAAC,GAAG,EAAE,CAAC;AACpB,AAAO,MAAM,IAAI,GAAG,EAAE,CAAC;AACvB,AAAO,MAAM,IAAI,GAAG,EAAE,CAAC;AACvB,AAAO,MAAM,KAAK,GAAG,GAAG,CAAC;;;;;GDlBzB,AAA2B;;;;;;;;GDJ3B,AAA6B;;"}