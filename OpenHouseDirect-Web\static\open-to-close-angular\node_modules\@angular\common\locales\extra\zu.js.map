{"version": 3, "file": "zu.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/zu.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC;QAC1D,AAD2D;KAE5D;IACD,AADE;IAEF;QACE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC9E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['entathakusa', 'ekuseni', 'emini', 'ntambama', 'ebusuku'],\n    ,\n  ],\n  ,\n  [\n    ['00:00', '06:00'], ['06:00', '10:00'], ['10:00', '13:00'], ['13:00', '19:00'],\n    ['19:00', '24:00']\n  ]\n];\n"]}