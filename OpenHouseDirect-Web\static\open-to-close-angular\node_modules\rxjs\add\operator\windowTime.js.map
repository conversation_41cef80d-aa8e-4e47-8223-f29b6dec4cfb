{"version": 3, "file": "windowTime.js", "sourceRoot": "", "sources": ["../../../src/add/operator/windowTime.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,2BAA2B,2BAA2B,CAAC,CAAA;AAEvD,uBAAU,CAAC,SAAS,CAAC,UAAU,GAAG,uBAAU,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { windowTime } from '../../operator/windowTime';\n\nObservable.prototype.windowTime = windowTime;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    windowTime: typeof windowTime;\n  }\n}"]}