{"_from": "to-fast-properties@^1.0.3", "_id": "to-fast-properties@1.0.3", "_inBundle": false, "_integrity": "sha512-lxrWP8ejsq+7E3nNjwYmUBMAgjMTZoTI+sdBOpvNyijeDLa29LUn9QaoXAHv4+Z578hbmHHJKZknzxVtvo77og==", "_location": "/to-fast-properties", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "to-fast-properties@^1.0.3", "name": "to-fast-properties", "escapedName": "to-fast-properties", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/babel-types"], "_resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz", "_shasum": "b83571fa4d8c25b82e231b06e3a3055de4ca1a47", "_spec": "to-fast-properties@^1.0.3", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\babel-types", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/to-fast-properties/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Force V8 to use fast properties for an object", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/to-fast-properties#readme", "keywords": ["object", "obj", "properties", "props", "v8", "optimize", "fast", "convert", "mode"], "license": "MIT", "name": "to-fast-properties", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/to-fast-properties.git"}, "scripts": {"test": "node --allow-natives-syntax test.js"}, "version": "1.0.3"}