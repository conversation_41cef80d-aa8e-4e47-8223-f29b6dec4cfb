{"_from": "tsconfig@^7.0.0", "_id": "tsconfig@7.0.0", "_inBundle": false, "_integrity": "sha512-vZXmzPrL+EmC4T/4rVlT2jNVMWCi/O4DIiSj3UHg1OE5kCKbk4mfrXc6dZksLgRM/TZlKnousKH9bbTazUWRRw==", "_location": "/tsconfig", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tsconfig@^7.0.0", "name": "tsconfig", "escapedName": "tsconfig", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/ts-node"], "_resolved": "https://registry.npmjs.org/tsconfig/-/tsconfig-7.0.0.tgz", "_shasum": "84538875a4dc216e5c4a5432b3a4dec3d54e91b7", "_spec": "tsconfig@^7.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\ts-node", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/TypeStrong/tsconfig/issues"}, "bundleDependencies": false, "contributors": [{"name": "basa<PERSON><PERSON>@gmail.com"}], "dependencies": {"@types/strip-bom": "^3.0.0", "@types/strip-json-comments": "0.0.30", "strip-bom": "^3.0.0", "strip-json-comments": "^2.0.0"}, "deprecated": false, "description": "Resole and parse `tsconfig.json`, replicating to TypeScript's behaviour", "devDependencies": {"@types/chai": "^4.0.4", "@types/mocha": "^2.2.42", "@types/node": "^8.0.25", "bluebird": "^3.4.1", "chai": "^3.0.0", "istanbul": "^0.4.0", "mocha": "^3.2.0", "tslint": "^4.5.1", "tslint-config-standard": "^4.0.0", "typescript": "^2.2.1"}, "files": ["dist/", "LICENSE"], "homepage": "https://github.com/TypeStrong/tsconfig", "keywords": ["TypeScript", "compiler", "config", "tsconfig", "json", "resolve"], "license": "MIT", "main": "dist/tsconfig.js", "name": "tsconfig", "repository": {"type": "git", "url": "git+https://github.com/TypeStrong/tsconfig.git"}, "scripts": {"build": "npm run build-ts", "build-ts": "rm -rf dist && tsc", "lint": "tslint \"src/**/*.ts\"", "prepublish": "npm run build", "test": "npm run build && npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- dist/**/*.spec.js -R spec --bail", "test-spec": "mocha dist/**/*.spec.js -R spec --bail"}, "typings": "dist/tsconfig.d.ts", "version": "7.0.0"}