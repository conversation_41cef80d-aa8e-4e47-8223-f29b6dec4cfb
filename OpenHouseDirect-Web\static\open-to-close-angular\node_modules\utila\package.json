{"_from": "utila@~0.4", "_id": "utila@0.4.0", "_inBundle": false, "_integrity": "sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==", "_location": "/utila", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "utila@~0.4", "name": "utila", "escapedName": "utila", "rawSpec": "~0.4", "saveSpec": null, "fetchSpec": "~0.4"}, "_requiredBy": ["/dom-converter"], "_resolved": "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz", "_shasum": "8a16a05d445657a3aea5eecc5b12a4fa5379772c", "_spec": "utila@~0.4", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\dom-converter", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/AriaMinaei/utila/issues"}, "bundleDependencies": false, "deprecated": false, "description": "notareplacementforunderscore", "devDependencies": {"coffee-script": "~1.6.3", "little-popo": "~0.1"}, "homepage": "https://github.com/AriaMinaei/utila#readme", "keywords": ["utilities"], "license": "MIT", "main": "lib/utila.js", "name": "utila", "repository": {"type": "git", "url": "git+https://github.com/AriaMinaei/utila.git"}, "scripts": {"prepublish": "coffee --bare --compile --output ./lib ./src"}, "version": "0.4.0"}