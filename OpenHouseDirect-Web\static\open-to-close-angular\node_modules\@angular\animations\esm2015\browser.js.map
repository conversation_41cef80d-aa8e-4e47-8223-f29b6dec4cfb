{"version": 3, "file": "browser.js", "sources": ["../../../packages/animations/browser/src/render/shared.js", "../../../packages/animations/browser/src/render/animation_driver.js", "../../../packages/animations/browser/src/util.js", "../../../packages/animations/browser/src/dsl/animation_transition_expr.js", "../../../packages/animations/browser/src/dsl/animation_ast_builder.js", "../../../packages/animations/browser/src/dsl/animation_timeline_instruction.js", "../../../packages/animations/browser/src/dsl/element_instruction_map.js", "../../../packages/animations/browser/src/dsl/animation_timeline_builder.js", "../../../packages/animations/browser/src/dsl/animation.js", "../../../packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.js", "../../../packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.js", "../../../packages/animations/browser/src/dsl/animation_transition_instruction.js", "../../../packages/animations/browser/src/dsl/animation_transition_factory.js", "../../../packages/animations/browser/src/dsl/animation_trigger.js", "../../../packages/animations/browser/src/render/timeline_animation_engine.js", "../../../packages/animations/browser/src/render/transition_animation_engine.js", "../../../packages/animations/browser/src/render/animation_engine_next.js", "../../../packages/animations/browser/src/render/web_animations/web_animations_player.js", "../../../packages/animations/browser/src/render/web_animations/web_animations_driver.js", "../../../packages/animations/browser/src/private_export.js", "../../../packages/animations/browser/src/browser.js", "../../../packages/animations/browser/public_api.js", "../../../packages/animations/browser/browser.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\n/**\n * @param {?} players\n * @return {?}\n */\nexport function optimizeGroupPlayer(players) {\n    switch (players.length) {\n        case 0:\n            return new NoopAnimationPlayer();\n        case 1:\n            return players[0];\n        default:\n            return new ɵAnimationGroupPlayer(players);\n    }\n}\n/**\n * @param {?} driver\n * @param {?} normalizer\n * @param {?} element\n * @param {?} keyframes\n * @param {?=} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nexport function normalizeKeyframes(driver, normalizer, element, keyframes, preStyles = {}, postStyles = {}) {\n    const /** @type {?} */ errors = [];\n    const /** @type {?} */ normalizedKeyframes = [];\n    let /** @type {?} */ previousOffset = -1;\n    let /** @type {?} */ previousKeyframe = null;\n    keyframes.forEach(kf => {\n        const /** @type {?} */ offset = /** @type {?} */ (kf['offset']);\n        const /** @type {?} */ isSameOffset = offset == previousOffset;\n        const /** @type {?} */ normalizedKeyframe = (isSameOffset && previousKeyframe) || {};\n        Object.keys(kf).forEach(prop => {\n            let /** @type {?} */ normalizedProp = prop;\n            let /** @type {?} */ normalizedValue = kf[prop];\n            if (prop !== 'offset') {\n                normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n                switch (normalizedValue) {\n                    case PRE_STYLE:\n                        normalizedValue = preStyles[prop];\n                        break;\n                    case AUTO_STYLE:\n                        normalizedValue = postStyles[prop];\n                        break;\n                    default:\n                        normalizedValue =\n                            normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n                        break;\n                }\n            }\n            normalizedKeyframe[normalizedProp] = normalizedValue;\n        });\n        if (!isSameOffset) {\n            normalizedKeyframes.push(normalizedKeyframe);\n        }\n        previousKeyframe = normalizedKeyframe;\n        previousOffset = offset;\n    });\n    if (errors.length) {\n        const /** @type {?} */ LINE_START = '\\n - ';\n        throw new Error(`Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n    }\n    return normalizedKeyframes;\n}\n/**\n * @param {?} player\n * @param {?} eventName\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nexport function listenOnPlayer(player, eventName, event, callback) {\n    switch (eventName) {\n        case 'start':\n            player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n            break;\n        case 'done':\n            player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n            break;\n        case 'destroy':\n            player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n            break;\n    }\n}\n/**\n * @param {?} e\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function copyAnimationEvent(e, phaseName, totalTime) {\n    const /** @type {?} */ event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime);\n    const /** @type {?} */ data = (/** @type {?} */ (e))['_data'];\n    if (data != null) {\n        (/** @type {?} */ (event))['_data'] = data;\n    }\n    return event;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function makeAnimationEvent(element, triggerName, fromState, toState, phaseName = '', totalTime = 0) {\n    return { element, triggerName, fromState, toState, phaseName, totalTime };\n}\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} defaultValue\n * @return {?}\n */\nexport function getOrSetAsInMap(map, key, defaultValue) {\n    let /** @type {?} */ value;\n    if (map instanceof Map) {\n        value = map.get(key);\n        if (!value) {\n            map.set(key, value = defaultValue);\n        }\n    }\n    else {\n        value = map[key];\n        if (!value) {\n            value = map[key] = defaultValue;\n        }\n    }\n    return value;\n}\n/**\n * @param {?} command\n * @return {?}\n */\nexport function parseTimelineCommand(command) {\n    const /** @type {?} */ separatorPos = command.indexOf(':');\n    const /** @type {?} */ id = command.substring(1, separatorPos);\n    const /** @type {?} */ action = command.substr(separatorPos + 1);\n    return [id, action];\n}\nlet /** @type {?} */ _contains = (elm1, elm2) => false;\nconst ɵ0 = _contains;\nlet /** @type {?} */ _matches = (element, selector) => false;\nconst ɵ1 = _matches;\nlet /** @type {?} */ _query = (element, selector, multi) => {\n    return [];\n};\nconst ɵ2 = _query;\nif (typeof Element != 'undefined') {\n    // this is well supported in all browsers\n    _contains = (elm1, elm2) => { return /** @type {?} */ (elm1.contains(elm2)); };\n    if (Element.prototype.matches) {\n        _matches = (element, selector) => element.matches(selector);\n    }\n    else {\n        const /** @type {?} */ proto = /** @type {?} */ (Element.prototype);\n        const /** @type {?} */ fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n            proto.oMatchesSelector || proto.webkitMatchesSelector;\n        if (fn) {\n            _matches = (element, selector) => fn.apply(element, [selector]);\n        }\n    }\n    _query = (element, selector, multi) => {\n        let /** @type {?} */ results = [];\n        if (multi) {\n            results.push(...element.querySelectorAll(selector));\n        }\n        else {\n            const /** @type {?} */ elm = element.querySelector(selector);\n            if (elm) {\n                results.push(elm);\n            }\n        }\n        return results;\n    };\n}\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction containsVendorPrefix(prop) {\n    // Webkit is the only real popular vendor prefix nowadays\n    // cc: http://shouldiprefix.com/\n    return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nlet /** @type {?} */ _CACHED_BODY = null;\nlet /** @type {?} */ _IS_WEBKIT = false;\n/**\n * @param {?} prop\n * @return {?}\n */\nexport function validateStyleProperty(prop) {\n    if (!_CACHED_BODY) {\n        _CACHED_BODY = getBodyNode() || {};\n        _IS_WEBKIT = /** @type {?} */ ((_CACHED_BODY)).style ? ('WebkitAppearance' in /** @type {?} */ ((_CACHED_BODY)).style) : false;\n    }\n    let /** @type {?} */ result = true;\n    if (/** @type {?} */ ((_CACHED_BODY)).style && !containsVendorPrefix(prop)) {\n        result = prop in /** @type {?} */ ((_CACHED_BODY)).style;\n        if (!result && _IS_WEBKIT) {\n            const /** @type {?} */ camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n            result = camelProp in /** @type {?} */ ((_CACHED_BODY)).style;\n        }\n    }\n    return result;\n}\n/**\n * @return {?}\n */\nexport function getBodyNode() {\n    if (typeof document != 'undefined') {\n        return document.body;\n    }\n    return null;\n}\nexport const /** @type {?} */ matchesElement = _matches;\nexport const /** @type {?} */ containsElement = _contains;\nexport const /** @type {?} */ invokeQuery = _query;\nexport { ɵ0, ɵ1, ɵ2 };\n//# sourceMappingURL=shared.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { NoopAnimationPlayer } from '@angular/animations';\nimport { containsElement, invokeQuery, matchesElement, validateStyleProperty } from './shared';\n/**\n * \\@experimental\n */\nexport class NoopAnimationDriver {\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    validateStyleProperty(prop) { return validateStyleProperty(prop); }\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    matchesElement(element, selector) {\n        return matchesElement(element, selector);\n    }\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    containsElement(elm1, elm2) { return containsElement(elm1, elm2); }\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    query(element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    }\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    computeStyle(element, prop, defaultValue) {\n        return defaultValue || '';\n    }\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n        return new NoopAnimationPlayer();\n    }\n}\n/**\n * \\@experimental\n * @abstract\n */\nexport class AnimationDriver {\n}\nAnimationDriver.NOOP = new NoopAnimationDriver();\nfunction AnimationDriver_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationDriver.NOOP;\n    /**\n     * @abstract\n     * @param {?} prop\n     * @return {?}\n     */\n    AnimationDriver.prototype.validateStyleProperty = function (prop) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    AnimationDriver.prototype.matchesElement = function (element, selector) { };\n    /**\n     * @abstract\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    AnimationDriver.prototype.containsElement = function (elm1, elm2) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    AnimationDriver.prototype.query = function (element, selector, multi) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    AnimationDriver.prototype.computeStyle = function (element, prop, defaultValue) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?=} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    AnimationDriver.prototype.animate = function (element, keyframes, duration, delay, easing, previousPlayers) { };\n}\n//# sourceMappingURL=animation_driver.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { sequence } from '@angular/animations';\nexport const /** @type {?} */ ONE_SECOND = 1000;\nexport const /** @type {?} */ SUBSTITUTION_EXPR_START = '{{';\nexport const /** @type {?} */ SUBSTITUTION_EXPR_END = '}}';\nexport const /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport const /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport const /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport const /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport const /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value) {\n    if (typeof value == 'number')\n        return value;\n    const /** @type {?} */ matches = (/** @type {?} */ (value)).match(/^(-?[\\.\\d]+)(m?s)/);\n    if (!matches || matches.length < 2)\n        return 0;\n    return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value, unit) {\n    switch (unit) {\n        case 's':\n            return value * ONE_SECOND;\n        default:\n            // ms or something else\n            return value;\n    }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(timings, errors, allowNegativeValues) {\n    return timings.hasOwnProperty('duration') ? /** @type {?} */ (timings) :\n        parseTimeExpression(/** @type {?} */ (timings), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n    const /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n    let /** @type {?} */ duration;\n    let /** @type {?} */ delay = 0;\n    let /** @type {?} */ easing = '';\n    if (typeof exp === 'string') {\n        const /** @type {?} */ matches = exp.match(regex);\n        if (matches === null) {\n            errors.push(`The provided timing value \"${exp}\" is invalid.`);\n            return { duration: 0, delay: 0, easing: '' };\n        }\n        duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n        const /** @type {?} */ delayMatch = matches[3];\n        if (delayMatch != null) {\n            delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n        }\n        const /** @type {?} */ easingVal = matches[5];\n        if (easingVal) {\n            easing = easingVal;\n        }\n    }\n    else {\n        duration = /** @type {?} */ (exp);\n    }\n    if (!allowNegativeValues) {\n        let /** @type {?} */ containsErrors = false;\n        let /** @type {?} */ startIndex = errors.length;\n        if (duration < 0) {\n            errors.push(`Duration values below 0 are not allowed for this animation step.`);\n            containsErrors = true;\n        }\n        if (delay < 0) {\n            errors.push(`Delay values below 0 are not allowed for this animation step.`);\n            containsErrors = true;\n        }\n        if (containsErrors) {\n            errors.splice(startIndex, 0, `The provided timing value \"${exp}\" is invalid.`);\n        }\n    }\n    return { duration, delay, easing };\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(obj, destination = {}) {\n    Object.keys(obj).forEach(prop => { destination[prop] = obj[prop]; });\n    return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles) {\n    const /** @type {?} */ normalizedStyles = {};\n    if (Array.isArray(styles)) {\n        styles.forEach(data => copyStyles(data, false, normalizedStyles));\n    }\n    else {\n        copyStyles(styles, false, normalizedStyles);\n    }\n    return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(styles, readPrototype, destination = {}) {\n    if (readPrototype) {\n        // we make use of a for-in loop so that the\n        // prototypically inherited properties are\n        // revealed from the backFill map\n        for (let /** @type {?} */ prop in styles) {\n            destination[prop] = styles[prop];\n        }\n    }\n    else {\n        copyObj(styles, destination);\n    }\n    return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(prop => {\n            const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = styles[prop];\n        });\n    }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(prop => {\n            const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = '';\n        });\n    }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps) {\n    if (Array.isArray(steps)) {\n        if (steps.length == 1)\n            return steps[0];\n        return sequence(steps);\n    }\n    return /** @type {?} */ (steps);\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(value, options, errors) {\n    const /** @type {?} */ params = options.params || {};\n    const /** @type {?} */ matches = extractStyleParams(value);\n    if (matches.length) {\n        matches.forEach(varName => {\n            if (!params.hasOwnProperty(varName)) {\n                errors.push(`Unable to resolve the local animation param ${varName} in the given list of values`);\n            }\n        });\n    }\n}\nconst /** @type {?} */ PARAM_REGEX = new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\n/**\n * @param {?} value\n * @return {?}\n */\nexport function extractStyleParams(value) {\n    let /** @type {?} */ params = [];\n    if (typeof value === 'string') {\n        const /** @type {?} */ val = value.toString();\n        let /** @type {?} */ match;\n        while (match = PARAM_REGEX.exec(val)) {\n            params.push(/** @type {?} */ (match[1]));\n        }\n        PARAM_REGEX.lastIndex = 0;\n    }\n    return params;\n}\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(value, params, errors) {\n    const /** @type {?} */ original = value.toString();\n    const /** @type {?} */ str = original.replace(PARAM_REGEX, (_, varName) => {\n        let /** @type {?} */ localVal = params[varName];\n        // this means that the value was never overidden by the data passed in by the user\n        if (!params.hasOwnProperty(varName)) {\n            errors.push(`Please provide a value for the animation param ${varName}`);\n            localVal = '';\n        }\n        return localVal.toString();\n    });\n    // we do this to assert that numeric values stay as they are\n    return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator) {\n    const /** @type {?} */ arr = [];\n    let /** @type {?} */ item = iterator.next();\n    while (!item.done) {\n        arr.push(item.value);\n        item = iterator.next();\n    }\n    return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(source, destination) {\n    if (source.params) {\n        const /** @type {?} */ p0 = source.params;\n        if (!destination.params) {\n            destination.params = {};\n        }\n        const /** @type {?} */ p1 = destination.params;\n        Object.keys(p0).forEach(param => {\n            if (!p1.hasOwnProperty(param)) {\n                p1[param] = p0[param];\n            }\n        });\n    }\n    return destination;\n}\nconst /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nexport function allowPreviousPlayerStylesMerge(duration, delay) {\n    return duration === 0 || delay === 0;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitDslNode(visitor, node, context) {\n    switch (node.type) {\n        case 7 /* Trigger */:\n            return visitor.visitTrigger(node, context);\n        case 0 /* State */:\n            return visitor.visitState(node, context);\n        case 1 /* Transition */:\n            return visitor.visitTransition(node, context);\n        case 2 /* Sequence */:\n            return visitor.visitSequence(node, context);\n        case 3 /* Group */:\n            return visitor.visitGroup(node, context);\n        case 4 /* Animate */:\n            return visitor.visitAnimate(node, context);\n        case 5 /* Keyframes */:\n            return visitor.visitKeyframes(node, context);\n        case 6 /* Style */:\n            return visitor.visitStyle(node, context);\n        case 8 /* Reference */:\n            return visitor.visitReference(node, context);\n        case 9 /* AnimateChild */:\n            return visitor.visitAnimateChild(node, context);\n        case 10 /* AnimateRef */:\n            return visitor.visitAnimateRef(node, context);\n        case 11 /* Query */:\n            return visitor.visitQuery(node, context);\n        case 12 /* Stagger */:\n            return visitor.visitStagger(node, context);\n        default:\n            throw new Error(`Unable to resolve animation metadata node #${node.type}`);\n    }\n}\n//# sourceMappingURL=util.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport const /** @type {?} */ ANY_STATE = '*';\n/**\n * @param {?} transitionValue\n * @param {?} errors\n * @return {?}\n */\nexport function parseTransitionExpr(transitionValue, errors) {\n    const /** @type {?} */ expressions = [];\n    if (typeof transitionValue == 'string') {\n        (/** @type {?} */ (transitionValue))\n            .split(/\\s*,\\s*/)\n            .forEach(str => parseInnerTransitionStr(str, expressions, errors));\n    }\n    else {\n        expressions.push(/** @type {?} */ (transitionValue));\n    }\n    return expressions;\n}\n/**\n * @param {?} eventStr\n * @param {?} expressions\n * @param {?} errors\n * @return {?}\n */\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n    if (eventStr[0] == ':') {\n        const /** @type {?} */ result = parseAnimationAlias(eventStr, errors);\n        if (typeof result == 'function') {\n            expressions.push(result);\n            return;\n        }\n        eventStr = /** @type {?} */ (result);\n    }\n    const /** @type {?} */ match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n    if (match == null || match.length < 4) {\n        errors.push(`The provided transition expression \"${eventStr}\" is not supported`);\n        return expressions;\n    }\n    const /** @type {?} */ fromState = match[1];\n    const /** @type {?} */ separator = match[2];\n    const /** @type {?} */ toState = match[3];\n    expressions.push(makeLambdaFromStates(fromState, toState));\n    const /** @type {?} */ isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n    if (separator[0] == '<' && !isFullAnyStateExpr) {\n        expressions.push(makeLambdaFromStates(toState, fromState));\n    }\n}\n/**\n * @param {?} alias\n * @param {?} errors\n * @return {?}\n */\nfunction parseAnimationAlias(alias, errors) {\n    switch (alias) {\n        case ':enter':\n            return 'void => *';\n        case ':leave':\n            return '* => void';\n        case ':increment':\n            return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n        case ':decrement':\n            return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n        default:\n            errors.push(`The transition alias value \"${alias}\" is not supported`);\n            return '* => *';\n    }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst /** @type {?} */ TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\nconst /** @type {?} */ FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\n/**\n * @param {?} lhs\n * @param {?} rhs\n * @return {?}\n */\nfunction makeLambdaFromStates(lhs, rhs) {\n    const /** @type {?} */ LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n    const /** @type {?} */ RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n    return (fromState, toState) => {\n        let /** @type {?} */ lhsMatch = lhs == ANY_STATE || lhs == fromState;\n        let /** @type {?} */ rhsMatch = rhs == ANY_STATE || rhs == toState;\n        if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n            lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n        }\n        if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n            rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n        }\n        return lhsMatch && rhsMatch;\n    };\n}\n//# sourceMappingURL=animation_transition_expr.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, style } from '@angular/animations';\nimport { getOrSetAsInMap } from '../render/shared';\nimport { NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, SUBSTITUTION_EXPR_START, copyObj, extractStyleParams, iteratorToArray, normalizeAnimationEntry, resolveTiming, validateStyleParams, visitDslNode } from '../util';\nimport { parseTransitionExpr } from './animation_transition_expr';\nconst /** @type {?} */ SELF_TOKEN = ':self';\nconst /** @type {?} */ SELF_TOKEN_REGEX = new RegExp(`\\s*${SELF_TOKEN}\\s*,?`, 'g');\n/**\n * @param {?} driver\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nexport function buildAnimationAst(driver, metadata, errors) {\n    return new AnimationAstBuilderVisitor(driver).build(metadata, errors);\n}\nconst /** @type {?} */ ROOT_SELECTOR = '';\nexport class AnimationAstBuilderVisitor {\n    /**\n     * @param {?} _driver\n     */\n    constructor(_driver) {\n        this._driver = _driver;\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} errors\n     * @return {?}\n     */\n    build(metadata, errors) {\n        const /** @type {?} */ context = new AnimationAstBuilderContext(errors);\n        this._resetContextStyleTimingState(context);\n        return /** @type {?} */ (visitDslNode(this, normalizeAnimationEntry(metadata), context));\n    }\n    /**\n     * @param {?} context\n     * @return {?}\n     */\n    _resetContextStyleTimingState(context) {\n        context.currentQuerySelector = ROOT_SELECTOR;\n        context.collectedStyles = {};\n        context.collectedStyles[ROOT_SELECTOR] = {};\n        context.currentTime = 0;\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitTrigger(metadata, context) {\n        let /** @type {?} */ queryCount = context.queryCount = 0;\n        let /** @type {?} */ depCount = context.depCount = 0;\n        const /** @type {?} */ states = [];\n        const /** @type {?} */ transitions = [];\n        if (metadata.name.charAt(0) == '@') {\n            context.errors.push('animation triggers cannot be prefixed with an `@` sign (e.g. trigger(\\'@foo\\', [...]))');\n        }\n        metadata.definitions.forEach(def => {\n            this._resetContextStyleTimingState(context);\n            if (def.type == 0 /* State */) {\n                const /** @type {?} */ stateDef = /** @type {?} */ (def);\n                const /** @type {?} */ name = stateDef.name;\n                name.split(/\\s*,\\s*/).forEach(n => {\n                    stateDef.name = n;\n                    states.push(this.visitState(stateDef, context));\n                });\n                stateDef.name = name;\n            }\n            else if (def.type == 1 /* Transition */) {\n                const /** @type {?} */ transition = this.visitTransition(/** @type {?} */ (def), context);\n                queryCount += transition.queryCount;\n                depCount += transition.depCount;\n                transitions.push(transition);\n            }\n            else {\n                context.errors.push('only state() and transition() definitions can sit inside of a trigger()');\n            }\n        });\n        return {\n            type: 7 /* Trigger */,\n            name: metadata.name, states, transitions, queryCount, depCount,\n            options: null\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitState(metadata, context) {\n        const /** @type {?} */ styleAst = this.visitStyle(metadata.styles, context);\n        const /** @type {?} */ astParams = (metadata.options && metadata.options.params) || null;\n        if (styleAst.containsDynamicStyles) {\n            const /** @type {?} */ missingSubs = new Set();\n            const /** @type {?} */ params = astParams || {};\n            styleAst.styles.forEach(value => {\n                if (isObject(value)) {\n                    const /** @type {?} */ stylesObj = /** @type {?} */ (value);\n                    Object.keys(stylesObj).forEach(prop => {\n                        extractStyleParams(stylesObj[prop]).forEach(sub => {\n                            if (!params.hasOwnProperty(sub)) {\n                                missingSubs.add(sub);\n                            }\n                        });\n                    });\n                }\n            });\n            if (missingSubs.size) {\n                const /** @type {?} */ missingSubsArr = iteratorToArray(missingSubs.values());\n                context.errors.push(`state(\"${metadata.name}\", ...) must define default values for all the following style substitutions: ${missingSubsArr.join(', ')}`);\n            }\n        }\n        return {\n            type: 0 /* State */,\n            name: metadata.name,\n            style: styleAst,\n            options: astParams ? { params: astParams } : null\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitTransition(metadata, context) {\n        context.queryCount = 0;\n        context.depCount = 0;\n        const /** @type {?} */ animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        const /** @type {?} */ matchers = parseTransitionExpr(metadata.expr, context.errors);\n        return {\n            type: 1 /* Transition */,\n            matchers,\n            animation,\n            queryCount: context.queryCount,\n            depCount: context.depCount,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitSequence(metadata, context) {\n        return {\n            type: 2 /* Sequence */,\n            steps: metadata.steps.map(s => visitDslNode(this, s, context)),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitGroup(metadata, context) {\n        const /** @type {?} */ currentTime = context.currentTime;\n        let /** @type {?} */ furthestTime = 0;\n        const /** @type {?} */ steps = metadata.steps.map(step => {\n            context.currentTime = currentTime;\n            const /** @type {?} */ innerAst = visitDslNode(this, step, context);\n            furthestTime = Math.max(furthestTime, context.currentTime);\n            return innerAst;\n        });\n        context.currentTime = furthestTime;\n        return {\n            type: 3 /* Group */,\n            steps,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitAnimate(metadata, context) {\n        const /** @type {?} */ timingAst = constructTimingAst(metadata.timings, context.errors);\n        context.currentAnimateTimings = timingAst;\n        let /** @type {?} */ styleAst;\n        let /** @type {?} */ styleMetadata = metadata.styles ? metadata.styles : style({});\n        if (styleMetadata.type == 5 /* Keyframes */) {\n            styleAst = this.visitKeyframes(/** @type {?} */ (styleMetadata), context);\n        }\n        else {\n            let /** @type {?} */ styleMetadata = /** @type {?} */ (metadata.styles);\n            let /** @type {?} */ isEmpty = false;\n            if (!styleMetadata) {\n                isEmpty = true;\n                const /** @type {?} */ newStyleData = {};\n                if (timingAst.easing) {\n                    newStyleData['easing'] = timingAst.easing;\n                }\n                styleMetadata = style(newStyleData);\n            }\n            context.currentTime += timingAst.duration + timingAst.delay;\n            const /** @type {?} */ _styleAst = this.visitStyle(styleMetadata, context);\n            _styleAst.isEmptyStep = isEmpty;\n            styleAst = _styleAst;\n        }\n        context.currentAnimateTimings = null;\n        return {\n            type: 4 /* Animate */,\n            timings: timingAst,\n            style: styleAst,\n            options: null\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitStyle(metadata, context) {\n        const /** @type {?} */ ast = this._makeStyleAst(metadata, context);\n        this._validateStyleAst(ast, context);\n        return ast;\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    _makeStyleAst(metadata, context) {\n        const /** @type {?} */ styles = [];\n        if (Array.isArray(metadata.styles)) {\n            (/** @type {?} */ (metadata.styles)).forEach(styleTuple => {\n                if (typeof styleTuple == 'string') {\n                    if (styleTuple == AUTO_STYLE) {\n                        styles.push(/** @type {?} */ (styleTuple));\n                    }\n                    else {\n                        context.errors.push(`The provided style string value ${styleTuple} is not allowed.`);\n                    }\n                }\n                else {\n                    styles.push(/** @type {?} */ (styleTuple));\n                }\n            });\n        }\n        else {\n            styles.push(metadata.styles);\n        }\n        let /** @type {?} */ containsDynamicStyles = false;\n        let /** @type {?} */ collectedEasing = null;\n        styles.forEach(styleData => {\n            if (isObject(styleData)) {\n                const /** @type {?} */ styleMap = /** @type {?} */ (styleData);\n                const /** @type {?} */ easing = styleMap['easing'];\n                if (easing) {\n                    collectedEasing = /** @type {?} */ (easing);\n                    delete styleMap['easing'];\n                }\n                if (!containsDynamicStyles) {\n                    for (let /** @type {?} */ prop in styleMap) {\n                        const /** @type {?} */ value = styleMap[prop];\n                        if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n                            containsDynamicStyles = true;\n                            break;\n                        }\n                    }\n                }\n            }\n        });\n        return {\n            type: 6 /* Style */,\n            styles,\n            easing: collectedEasing,\n            offset: metadata.offset, containsDynamicStyles,\n            options: null\n        };\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    _validateStyleAst(ast, context) {\n        const /** @type {?} */ timings = context.currentAnimateTimings;\n        let /** @type {?} */ endTime = context.currentTime;\n        let /** @type {?} */ startTime = context.currentTime;\n        if (timings && startTime > 0) {\n            startTime -= timings.duration + timings.delay;\n        }\n        ast.styles.forEach(tuple => {\n            if (typeof tuple == 'string')\n                return;\n            Object.keys(tuple).forEach(prop => {\n                if (!this._driver.validateStyleProperty(prop)) {\n                    context.errors.push(`The provided animation property \"${prop}\" is not a supported CSS property for animations`);\n                    return;\n                }\n                const /** @type {?} */ collectedStyles = context.collectedStyles[/** @type {?} */ ((context.currentQuerySelector))];\n                const /** @type {?} */ collectedEntry = collectedStyles[prop];\n                let /** @type {?} */ updateCollectedStyle = true;\n                if (collectedEntry) {\n                    if (startTime != endTime && startTime >= collectedEntry.startTime &&\n                        endTime <= collectedEntry.endTime) {\n                        context.errors.push(`The CSS property \"${prop}\" that exists between the times of \"${collectedEntry.startTime}ms\" and \"${collectedEntry.endTime}ms\" is also being animated in a parallel animation between the times of \"${startTime}ms\" and \"${endTime}ms\"`);\n                        updateCollectedStyle = false;\n                    }\n                    // we always choose the smaller start time value since we\n                    // want to have a record of the entire animation window where\n                    // the style property is being animated in between\n                    startTime = collectedEntry.startTime;\n                }\n                if (updateCollectedStyle) {\n                    collectedStyles[prop] = { startTime, endTime };\n                }\n                if (context.options) {\n                    validateStyleParams(tuple[prop], context.options, context.errors);\n                }\n            });\n        });\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitKeyframes(metadata, context) {\n        const /** @type {?} */ ast = { type: 5 /* Keyframes */, styles: [], options: null };\n        if (!context.currentAnimateTimings) {\n            context.errors.push(`keyframes() must be placed inside of a call to animate()`);\n            return ast;\n        }\n        const /** @type {?} */ MAX_KEYFRAME_OFFSET = 1;\n        let /** @type {?} */ totalKeyframesWithOffsets = 0;\n        const /** @type {?} */ offsets = [];\n        let /** @type {?} */ offsetsOutOfOrder = false;\n        let /** @type {?} */ keyframesOutOfRange = false;\n        let /** @type {?} */ previousOffset = 0;\n        const /** @type {?} */ keyframes = metadata.steps.map(styles => {\n            const /** @type {?} */ style = this._makeStyleAst(styles, context);\n            let /** @type {?} */ offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n            let /** @type {?} */ offset = 0;\n            if (offsetVal != null) {\n                totalKeyframesWithOffsets++;\n                offset = style.offset = offsetVal;\n            }\n            keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n            offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n            previousOffset = offset;\n            offsets.push(offset);\n            return style;\n        });\n        if (keyframesOutOfRange) {\n            context.errors.push(`Please ensure that all keyframe offsets are between 0 and 1`);\n        }\n        if (offsetsOutOfOrder) {\n            context.errors.push(`Please ensure that all keyframe offsets are in order`);\n        }\n        const /** @type {?} */ length = metadata.steps.length;\n        let /** @type {?} */ generatedOffset = 0;\n        if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n            context.errors.push(`Not all style() steps within the declared keyframes() contain offsets`);\n        }\n        else if (totalKeyframesWithOffsets == 0) {\n            generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n        }\n        const /** @type {?} */ limit = length - 1;\n        const /** @type {?} */ currentTime = context.currentTime;\n        const /** @type {?} */ currentAnimateTimings = /** @type {?} */ ((context.currentAnimateTimings));\n        const /** @type {?} */ animateDuration = currentAnimateTimings.duration;\n        keyframes.forEach((kf, i) => {\n            const /** @type {?} */ offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n            const /** @type {?} */ durationUpToThisFrame = offset * animateDuration;\n            context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n            currentAnimateTimings.duration = durationUpToThisFrame;\n            this._validateStyleAst(kf, context);\n            kf.offset = offset;\n            ast.styles.push(kf);\n        });\n        return ast;\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitReference(metadata, context) {\n        return {\n            type: 8 /* Reference */,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitAnimateChild(metadata, context) {\n        context.depCount++;\n        return {\n            type: 9 /* AnimateChild */,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitAnimateRef(metadata, context) {\n        return {\n            type: 10 /* AnimateRef */,\n            animation: this.visitReference(metadata.animation, context),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitQuery(metadata, context) {\n        const /** @type {?} */ parentSelector = /** @type {?} */ ((context.currentQuerySelector));\n        const /** @type {?} */ options = /** @type {?} */ ((metadata.options || {}));\n        context.queryCount++;\n        context.currentQuery = metadata;\n        const [selector, includeSelf] = normalizeSelector(metadata.selector);\n        context.currentQuerySelector =\n            parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n        getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n        const /** @type {?} */ animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        context.currentQuery = null;\n        context.currentQuerySelector = parentSelector;\n        return {\n            type: 11 /* Query */,\n            selector,\n            limit: options.limit || 0,\n            optional: !!options.optional, includeSelf, animation,\n            originalSelector: metadata.selector,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    visitStagger(metadata, context) {\n        if (!context.currentQuery) {\n            context.errors.push(`stagger() can only be used inside of query()`);\n        }\n        const /** @type {?} */ timings = metadata.timings === 'full' ?\n            { duration: 0, delay: 0, easing: 'full' } :\n            resolveTiming(metadata.timings, context.errors, true);\n        return {\n            type: 12 /* Stagger */,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context), timings,\n            options: null\n        };\n    }\n}\nfunction AnimationAstBuilderVisitor_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationAstBuilderVisitor.prototype._driver;\n}\n/**\n * @param {?} selector\n * @return {?}\n */\nfunction normalizeSelector(selector) {\n    const /** @type {?} */ hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n    if (hasAmpersand) {\n        selector = selector.replace(SELF_TOKEN_REGEX, '');\n    }\n    // the :enter and :leave selectors are filled in at runtime during timeline building\n    selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n        .replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.substr(1))\n        .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n    return [selector, hasAmpersand];\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction normalizeParams(obj) {\n    return obj ? copyObj(obj) : null;\n}\nexport class AnimationAstBuilderContext {\n    /**\n     * @param {?} errors\n     */\n    constructor(errors) {\n        this.errors = errors;\n        this.queryCount = 0;\n        this.depCount = 0;\n        this.currentTransition = null;\n        this.currentQuery = null;\n        this.currentQuerySelector = null;\n        this.currentAnimateTimings = null;\n        this.currentTime = 0;\n        this.collectedStyles = {};\n        this.options = null;\n    }\n}\nfunction AnimationAstBuilderContext_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.queryCount;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.depCount;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentTransition;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentQuery;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentQuerySelector;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentAnimateTimings;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentTime;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.collectedStyles;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.options;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.errors;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction consumeOffset(styles) {\n    if (typeof styles == 'string')\n        return null;\n    let /** @type {?} */ offset = null;\n    if (Array.isArray(styles)) {\n        styles.forEach(styleTuple => {\n            if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n                const /** @type {?} */ obj = /** @type {?} */ (styleTuple);\n                offset = parseFloat(/** @type {?} */ (obj['offset']));\n                delete obj['offset'];\n            }\n        });\n    }\n    else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n        const /** @type {?} */ obj = /** @type {?} */ (styles);\n        offset = parseFloat(/** @type {?} */ (obj['offset']));\n        delete obj['offset'];\n    }\n    return offset;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isObject(value) {\n    return !Array.isArray(value) && typeof value == 'object';\n}\n/**\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nfunction constructTimingAst(value, errors) {\n    let /** @type {?} */ timings = null;\n    if (value.hasOwnProperty('duration')) {\n        timings = /** @type {?} */ (value);\n    }\n    else if (typeof value == 'number') {\n        const /** @type {?} */ duration = resolveTiming(/** @type {?} */ (value), errors).duration;\n        return makeTimingAst(/** @type {?} */ (duration), 0, '');\n    }\n    const /** @type {?} */ strValue = /** @type {?} */ (value);\n    const /** @type {?} */ isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n    if (isDynamic) {\n        const /** @type {?} */ ast = /** @type {?} */ (makeTimingAst(0, 0, ''));\n        ast.dynamic = true;\n        ast.strValue = strValue;\n        return /** @type {?} */ (ast);\n    }\n    timings = timings || resolveTiming(strValue, errors);\n    return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction normalizeAnimationOptions(options) {\n    if (options) {\n        options = copyObj(options);\n        if (options['params']) {\n            options['params'] = /** @type {?} */ ((normalizeParams(options['params'])));\n        }\n    }\n    else {\n        options = {};\n    }\n    return options;\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @param {?} easing\n * @return {?}\n */\nfunction makeTimingAst(duration, delay, easing) {\n    return { duration, delay, easing };\n}\n//# sourceMappingURL=animation_ast_builder.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @record\n */\nexport function AnimationTimelineInstruction() { }\nfunction AnimationTimelineInstruction_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.element;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.keyframes;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.preStyleProps;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.postStyleProps;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.duration;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.delay;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.totalTime;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.easing;\n    /** @type {?|undefined} */\n    AnimationTimelineInstruction.prototype.stretchStartingKeyframe;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.subTimeline;\n}\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} duration\n * @param {?} delay\n * @param {?=} easing\n * @param {?=} subTimeline\n * @return {?}\n */\nexport function createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n    return {\n        type: 1 /* TimelineAnimation */,\n        element,\n        keyframes,\n        preStyleProps,\n        postStyleProps,\n        duration,\n        delay,\n        totalTime: duration + delay, easing, subTimeline\n    };\n}\n//# sourceMappingURL=animation_timeline_instruction.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nexport class ElementInstructionMap {\n    constructor() {\n        this._map = new Map();\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    consume(element) {\n        let /** @type {?} */ instructions = this._map.get(element);\n        if (instructions) {\n            this._map.delete(element);\n        }\n        else {\n            instructions = [];\n        }\n        return instructions;\n    }\n    /**\n     * @param {?} element\n     * @param {?} instructions\n     * @return {?}\n     */\n    append(element, instructions) {\n        let /** @type {?} */ existingInstructions = this._map.get(element);\n        if (!existingInstructions) {\n            this._map.set(element, existingInstructions = []);\n        }\n        existingInstructions.push(...instructions);\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    has(element) { return this._map.has(element); }\n    /**\n     * @return {?}\n     */\n    clear() { this._map.clear(); }\n}\nfunction ElementInstructionMap_tsickle_Closure_declarations() {\n    /** @type {?} */\n    ElementInstructionMap.prototype._map;\n}\n//# sourceMappingURL=element_instruction_map.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\nimport { copyObj, copyStyles, interpolateParams, iteratorToArray, resolveTiming, resolveTimingValue, visitDslNode } from '../util';\nimport { createTimelineInstruction } from './animation_timeline_instruction';\nimport { ElementInstructionMap } from './element_instruction_map';\nconst /** @type {?} */ ONE_FRAME_IN_MILLISECONDS = 1;\nconst /** @type {?} */ ENTER_TOKEN = ':enter';\nconst /** @type {?} */ ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nconst /** @type {?} */ LEAVE_TOKEN = ':leave';\nconst /** @type {?} */ LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?} enterClassName\n * @param {?} leaveClassName\n * @param {?=} startingStyles\n * @param {?=} finalStyles\n * @param {?=} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nexport function buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = {}, finalStyles = {}, options, subInstructions, errors = []) {\n    return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nexport class AnimationTimelineBuilderVisitor {\n    /**\n     * @param {?} driver\n     * @param {?} rootElement\n     * @param {?} ast\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?} startingStyles\n     * @param {?} finalStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @param {?=} errors\n     * @return {?}\n     */\n    buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n        subInstructions = subInstructions || new ElementInstructionMap();\n        const /** @type {?} */ context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n        context.options = options;\n        context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n        visitDslNode(this, ast, context);\n        // this checks to see if an actual animation happened\n        const /** @type {?} */ timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n        if (timelines.length && Object.keys(finalStyles).length) {\n            const /** @type {?} */ tl = timelines[timelines.length - 1];\n            if (!tl.allowOnlyTimelineStyles()) {\n                tl.setStyles([finalStyles], null, context.errors, options);\n            }\n        }\n        return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) :\n            [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitTrigger(ast, context) {\n        // these values are not visited in this AST\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitState(ast, context) {\n        // these values are not visited in this AST\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitTransition(ast, context) {\n        // these values are not visited in this AST\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitAnimateChild(ast, context) {\n        const /** @type {?} */ elementInstructions = context.subInstructions.consume(context.element);\n        if (elementInstructions) {\n            const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n            const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n            const /** @type {?} */ endTime = this._visitSubInstructions(elementInstructions, innerContext, /** @type {?} */ (innerContext.options));\n            if (startTime != endTime) {\n                // we do this on the upper context because we created a sub context for\n                // the sub child animations\n                context.transformIntoNewTimeline(endTime);\n            }\n        }\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitAnimateRef(ast, context) {\n        const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n        innerContext.transformIntoNewTimeline();\n        this.visitReference(ast.animation, innerContext);\n        context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} instructions\n     * @param {?} context\n     * @param {?} options\n     * @return {?}\n     */\n    _visitSubInstructions(instructions, context, options) {\n        const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n        let /** @type {?} */ furthestTime = startTime;\n        // this is a special-case for when a user wants to skip a sub\n        // animation from being fired entirely.\n        const /** @type {?} */ duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n        const /** @type {?} */ delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n        if (duration !== 0) {\n            instructions.forEach(instruction => {\n                const /** @type {?} */ instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n                furthestTime =\n                    Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n            });\n        }\n        return furthestTime;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitReference(ast, context) {\n        context.updateOptions(ast.options, true);\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitSequence(ast, context) {\n        const /** @type {?} */ subContextCount = context.subContextCount;\n        let /** @type {?} */ ctx = context;\n        const /** @type {?} */ options = ast.options;\n        if (options && (options.params || options.delay)) {\n            ctx = context.createSubContext(options);\n            ctx.transformIntoNewTimeline();\n            if (options.delay != null) {\n                if (ctx.previousNode.type == 6 /* Style */) {\n                    ctx.currentTimeline.snapshotCurrentStyles();\n                    ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n                }\n                const /** @type {?} */ delay = resolveTimingValue(options.delay);\n                ctx.delayNextStep(delay);\n            }\n        }\n        if (ast.steps.length) {\n            ast.steps.forEach(s => visitDslNode(this, s, ctx));\n            // this is here just incase the inner steps only contain or end with a style() call\n            ctx.currentTimeline.applyStylesToKeyframe();\n            // this means that some animation function within the sequence\n            // ended up creating a sub timeline (which means the current\n            // timeline cannot overlap with the contents of the sequence)\n            if (ctx.subContextCount > subContextCount) {\n                ctx.transformIntoNewTimeline();\n            }\n        }\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitGroup(ast, context) {\n        const /** @type {?} */ innerTimelines = [];\n        let /** @type {?} */ furthestTime = context.currentTimeline.currentTime;\n        const /** @type {?} */ delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n        ast.steps.forEach(s => {\n            const /** @type {?} */ innerContext = context.createSubContext(ast.options);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            visitDslNode(this, s, innerContext);\n            furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n            innerTimelines.push(innerContext.currentTimeline);\n        });\n        // this operation is run after the AST loop because otherwise\n        // if the parent timeline's collected styles were updated then\n        // it would pass in invalid data into the new-to-be forked items\n        innerTimelines.forEach(timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n        context.transformIntoNewTimeline(furthestTime);\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    _visitTiming(ast, context) {\n        if ((/** @type {?} */ (ast)).dynamic) {\n            const /** @type {?} */ strValue = (/** @type {?} */ (ast)).strValue;\n            const /** @type {?} */ timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n            return resolveTiming(timingValue, context.errors);\n        }\n        else {\n            return { duration: ast.duration, delay: ast.delay, easing: ast.easing };\n        }\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitAnimate(ast, context) {\n        const /** @type {?} */ timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n        const /** @type {?} */ timeline = context.currentTimeline;\n        if (timings.delay) {\n            context.incrementTime(timings.delay);\n            timeline.snapshotCurrentStyles();\n        }\n        const /** @type {?} */ style = ast.style;\n        if (style.type == 5 /* Keyframes */) {\n            this.visitKeyframes(style, context);\n        }\n        else {\n            context.incrementTime(timings.duration);\n            this.visitStyle(/** @type {?} */ (style), context);\n            timeline.applyStylesToKeyframe();\n        }\n        context.currentAnimateTimings = null;\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitStyle(ast, context) {\n        const /** @type {?} */ timeline = context.currentTimeline;\n        const /** @type {?} */ timings = /** @type {?} */ ((context.currentAnimateTimings));\n        // this is a special case for when a style() call\n        // directly follows  an animate() call (but not inside of an animate() call)\n        if (!timings && timeline.getCurrentStyleProperties().length) {\n            timeline.forwardFrame();\n        }\n        const /** @type {?} */ easing = (timings && timings.easing) || ast.easing;\n        if (ast.isEmptyStep) {\n            timeline.applyEmptyStep(easing);\n        }\n        else {\n            timeline.setStyles(ast.styles, easing, context.errors, context.options);\n        }\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitKeyframes(ast, context) {\n        const /** @type {?} */ currentAnimateTimings = /** @type {?} */ ((context.currentAnimateTimings));\n        const /** @type {?} */ startTime = (/** @type {?} */ ((context.currentTimeline))).duration;\n        const /** @type {?} */ duration = currentAnimateTimings.duration;\n        const /** @type {?} */ innerContext = context.createSubContext();\n        const /** @type {?} */ innerTimeline = innerContext.currentTimeline;\n        innerTimeline.easing = currentAnimateTimings.easing;\n        ast.styles.forEach(step => {\n            const /** @type {?} */ offset = step.offset || 0;\n            innerTimeline.forwardTime(offset * duration);\n            innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n            innerTimeline.applyStylesToKeyframe();\n        });\n        // this will ensure that the parent timeline gets all the styles from\n        // the child even if the new timeline below is not used\n        context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n        // we do this because the window between this timeline and the sub timeline\n        // should ensure that the styles within are exactly the same as they were before\n        context.transformIntoNewTimeline(startTime + duration);\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitQuery(ast, context) {\n        // in the event that the first step before this is a style step we need\n        // to ensure the styles are applied before the children are animated\n        const /** @type {?} */ startTime = context.currentTimeline.currentTime;\n        const /** @type {?} */ options = /** @type {?} */ ((ast.options || {}));\n        const /** @type {?} */ delay = options.delay ? resolveTimingValue(options.delay) : 0;\n        if (delay && (context.previousNode.type === 6 /* Style */ ||\n            (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n            context.currentTimeline.snapshotCurrentStyles();\n            context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        let /** @type {?} */ furthestTime = startTime;\n        const /** @type {?} */ elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n        context.currentQueryTotal = elms.length;\n        let /** @type {?} */ sameElementTimeline = null;\n        elms.forEach((element, i) => {\n            context.currentQueryIndex = i;\n            const /** @type {?} */ innerContext = context.createSubContext(ast.options, element);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            if (element === context.element) {\n                sameElementTimeline = innerContext.currentTimeline;\n            }\n            visitDslNode(this, ast.animation, innerContext);\n            // this is here just incase the inner steps only contain or end\n            // with a style() call (which is here to signal that this is a preparatory\n            // call to style an element before it is animated again)\n            innerContext.currentTimeline.applyStylesToKeyframe();\n            const /** @type {?} */ endTime = innerContext.currentTimeline.currentTime;\n            furthestTime = Math.max(furthestTime, endTime);\n        });\n        context.currentQueryIndex = 0;\n        context.currentQueryTotal = 0;\n        context.transformIntoNewTimeline(furthestTime);\n        if (sameElementTimeline) {\n            context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n            context.currentTimeline.snapshotCurrentStyles();\n        }\n        context.previousNode = ast;\n    }\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    visitStagger(ast, context) {\n        const /** @type {?} */ parentContext = /** @type {?} */ ((context.parentContext));\n        const /** @type {?} */ tl = context.currentTimeline;\n        const /** @type {?} */ timings = ast.timings;\n        const /** @type {?} */ duration = Math.abs(timings.duration);\n        const /** @type {?} */ maxTime = duration * (context.currentQueryTotal - 1);\n        let /** @type {?} */ delay = duration * context.currentQueryIndex;\n        let /** @type {?} */ staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n        switch (staggerTransformer) {\n            case 'reverse':\n                delay = maxTime - delay;\n                break;\n            case 'full':\n                delay = parentContext.currentStaggerTime;\n                break;\n        }\n        const /** @type {?} */ timeline = context.currentTimeline;\n        if (delay) {\n            timeline.delayNextStep(delay);\n        }\n        const /** @type {?} */ startingTime = timeline.currentTime;\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n        // time = duration + delay\n        // the reason why this computation is so complex is because\n        // the inner timeline may either have a delay value or a stretched\n        // keyframe depending on if a subtimeline is not used or is used.\n        parentContext.currentStaggerTime =\n            (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n    }\n}\nconst /** @type {?} */ DEFAULT_NOOP_PREVIOUS_NODE = /** @type {?} */ ({});\nexport class AnimationTimelineContext {\n    /**\n     * @param {?} _driver\n     * @param {?} element\n     * @param {?} subInstructions\n     * @param {?} _enterClassName\n     * @param {?} _leaveClassName\n     * @param {?} errors\n     * @param {?} timelines\n     * @param {?=} initialTimeline\n     */\n    constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n        this._driver = _driver;\n        this.element = element;\n        this.subInstructions = subInstructions;\n        this._enterClassName = _enterClassName;\n        this._leaveClassName = _leaveClassName;\n        this.errors = errors;\n        this.timelines = timelines;\n        this.parentContext = null;\n        this.currentAnimateTimings = null;\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.subContextCount = 0;\n        this.options = {};\n        this.currentQueryIndex = 0;\n        this.currentQueryTotal = 0;\n        this.currentStaggerTime = 0;\n        this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n        timelines.push(this.currentTimeline);\n    }\n    /**\n     * @return {?}\n     */\n    get params() { return this.options.params; }\n    /**\n     * @param {?} options\n     * @param {?=} skipIfExists\n     * @return {?}\n     */\n    updateOptions(options, skipIfExists) {\n        if (!options)\n            return;\n        const /** @type {?} */ newOptions = /** @type {?} */ (options);\n        let /** @type {?} */ optionsToUpdate = this.options;\n        // NOTE: this will get patched up when other animation methods support duration overrides\n        if (newOptions.duration != null) {\n            (/** @type {?} */ (optionsToUpdate)).duration = resolveTimingValue(newOptions.duration);\n        }\n        if (newOptions.delay != null) {\n            optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n        }\n        const /** @type {?} */ newParams = newOptions.params;\n        if (newParams) {\n            let /** @type {?} */ paramsToUpdate = /** @type {?} */ ((optionsToUpdate.params));\n            if (!paramsToUpdate) {\n                paramsToUpdate = this.options.params = {};\n            }\n            Object.keys(newParams).forEach(name => {\n                if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n                    paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n                }\n            });\n        }\n    }\n    /**\n     * @return {?}\n     */\n    _copyOptions() {\n        const /** @type {?} */ options = {};\n        if (this.options) {\n            const /** @type {?} */ oldParams = this.options.params;\n            if (oldParams) {\n                const /** @type {?} */ params = options['params'] = {};\n                Object.keys(oldParams).forEach(name => { params[name] = oldParams[name]; });\n            }\n        }\n        return options;\n    }\n    /**\n     * @param {?=} options\n     * @param {?=} element\n     * @param {?=} newTime\n     * @return {?}\n     */\n    createSubContext(options = null, element, newTime) {\n        const /** @type {?} */ target = element || this.element;\n        const /** @type {?} */ context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n        context.previousNode = this.previousNode;\n        context.currentAnimateTimings = this.currentAnimateTimings;\n        context.options = this._copyOptions();\n        context.updateOptions(options);\n        context.currentQueryIndex = this.currentQueryIndex;\n        context.currentQueryTotal = this.currentQueryTotal;\n        context.parentContext = this;\n        this.subContextCount++;\n        return context;\n    }\n    /**\n     * @param {?=} newTime\n     * @return {?}\n     */\n    transformIntoNewTimeline(newTime) {\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n        this.timelines.push(this.currentTimeline);\n        return this.currentTimeline;\n    }\n    /**\n     * @param {?} instruction\n     * @param {?} duration\n     * @param {?} delay\n     * @return {?}\n     */\n    appendInstructionToTimeline(instruction, duration, delay) {\n        const /** @type {?} */ updatedTimings = {\n            duration: duration != null ? duration : instruction.duration,\n            delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n            easing: ''\n        };\n        const /** @type {?} */ builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n        this.timelines.push(builder);\n        return updatedTimings;\n    }\n    /**\n     * @param {?} time\n     * @return {?}\n     */\n    incrementTime(time) {\n        this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n    }\n    /**\n     * @param {?} delay\n     * @return {?}\n     */\n    delayNextStep(delay) {\n        // negative delays are not yet supported\n        if (delay > 0) {\n            this.currentTimeline.delayNextStep(delay);\n        }\n    }\n    /**\n     * @param {?} selector\n     * @param {?} originalSelector\n     * @param {?} limit\n     * @param {?} includeSelf\n     * @param {?} optional\n     * @param {?} errors\n     * @return {?}\n     */\n    invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n        let /** @type {?} */ results = [];\n        if (includeSelf) {\n            results.push(this.element);\n        }\n        if (selector.length > 0) {\n            // if :self is only used then the selector is empty\n            selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n            selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n            const /** @type {?} */ multi = limit != 1;\n            let /** @type {?} */ elements = this._driver.query(this.element, selector, multi);\n            if (limit !== 0) {\n                elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) :\n                    elements.slice(0, limit);\n            }\n            results.push(...elements);\n        }\n        if (!optional && results.length == 0) {\n            errors.push(`\\`query(\"${originalSelector}\")\\` returned zero elements. (Use \\`query(\"${originalSelector}\", { optional: true })\\` if you wish to allow this.)`);\n        }\n        return results;\n    }\n}\nfunction AnimationTimelineContext_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTimelineContext.prototype.parentContext;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentTimeline;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentAnimateTimings;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.previousNode;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.subContextCount;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.options;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentQueryIndex;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentQueryTotal;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentStaggerTime;\n    /** @type {?} */\n    AnimationTimelineContext.prototype._driver;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.element;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.subInstructions;\n    /** @type {?} */\n    AnimationTimelineContext.prototype._enterClassName;\n    /** @type {?} */\n    AnimationTimelineContext.prototype._leaveClassName;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.errors;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.timelines;\n}\nexport class TimelineBuilder {\n    /**\n     * @param {?} _driver\n     * @param {?} element\n     * @param {?} startTime\n     * @param {?=} _elementTimelineStylesLookup\n     */\n    constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n        this._driver = _driver;\n        this.element = element;\n        this.startTime = startTime;\n        this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n        this.duration = 0;\n        this._previousKeyframe = {};\n        this._currentKeyframe = {};\n        this._keyframes = new Map();\n        this._styleSummary = {};\n        this._pendingStyles = {};\n        this._backFill = {};\n        this._currentEmptyStepKeyframe = null;\n        if (!this._elementTimelineStylesLookup) {\n            this._elementTimelineStylesLookup = new Map();\n        }\n        this._localTimelineStyles = Object.create(this._backFill, {});\n        this._globalTimelineStyles = /** @type {?} */ ((this._elementTimelineStylesLookup.get(element)));\n        if (!this._globalTimelineStyles) {\n            this._globalTimelineStyles = this._localTimelineStyles;\n            this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n        }\n        this._loadKeyframe();\n    }\n    /**\n     * @return {?}\n     */\n    containsAnimation() {\n        switch (this._keyframes.size) {\n            case 0:\n                return false;\n            case 1:\n                return this.getCurrentStyleProperties().length > 0;\n            default:\n                return true;\n        }\n    }\n    /**\n     * @return {?}\n     */\n    getCurrentStyleProperties() { return Object.keys(this._currentKeyframe); }\n    /**\n     * @return {?}\n     */\n    get currentTime() { return this.startTime + this.duration; }\n    /**\n     * @param {?} delay\n     * @return {?}\n     */\n    delayNextStep(delay) {\n        // in the event that a style() step is placed right before a stagger()\n        // and that style() step is the very first style() value in the animation\n        // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n        // properly applies the style() values to work with the stagger...\n        const /** @type {?} */ hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n        if (this.duration || hasPreStyleStep) {\n            this.forwardTime(this.currentTime + delay);\n            if (hasPreStyleStep) {\n                this.snapshotCurrentStyles();\n            }\n        }\n        else {\n            this.startTime += delay;\n        }\n    }\n    /**\n     * @param {?} element\n     * @param {?=} currentTime\n     * @return {?}\n     */\n    fork(element, currentTime) {\n        this.applyStylesToKeyframe();\n        return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n    }\n    /**\n     * @return {?}\n     */\n    _loadKeyframe() {\n        if (this._currentKeyframe) {\n            this._previousKeyframe = this._currentKeyframe;\n        }\n        this._currentKeyframe = /** @type {?} */ ((this._keyframes.get(this.duration)));\n        if (!this._currentKeyframe) {\n            this._currentKeyframe = Object.create(this._backFill, {});\n            this._keyframes.set(this.duration, this._currentKeyframe);\n        }\n    }\n    /**\n     * @return {?}\n     */\n    forwardFrame() {\n        this.duration += ONE_FRAME_IN_MILLISECONDS;\n        this._loadKeyframe();\n    }\n    /**\n     * @param {?} time\n     * @return {?}\n     */\n    forwardTime(time) {\n        this.applyStylesToKeyframe();\n        this.duration = time;\n        this._loadKeyframe();\n    }\n    /**\n     * @param {?} prop\n     * @param {?} value\n     * @return {?}\n     */\n    _updateStyle(prop, value) {\n        this._localTimelineStyles[prop] = value;\n        this._globalTimelineStyles[prop] = value;\n        this._styleSummary[prop] = { time: this.currentTime, value };\n    }\n    /**\n     * @return {?}\n     */\n    allowOnlyTimelineStyles() { return this._currentEmptyStepKeyframe !== this._currentKeyframe; }\n    /**\n     * @param {?} easing\n     * @return {?}\n     */\n    applyEmptyStep(easing) {\n        if (easing) {\n            this._previousKeyframe['easing'] = easing;\n        }\n        // special case for animate(duration):\n        // all missing styles are filled with a `*` value then\n        // if any destination styles are filled in later on the same\n        // keyframe then they will override the overridden styles\n        // We use `_globalTimelineStyles` here because there may be\n        // styles in previous keyframes that are not present in this timeline\n        Object.keys(this._globalTimelineStyles).forEach(prop => {\n            this._backFill[prop] = this._globalTimelineStyles[prop] || AUTO_STYLE;\n            this._currentKeyframe[prop] = AUTO_STYLE;\n        });\n        this._currentEmptyStepKeyframe = this._currentKeyframe;\n    }\n    /**\n     * @param {?} input\n     * @param {?} easing\n     * @param {?} errors\n     * @param {?=} options\n     * @return {?}\n     */\n    setStyles(input, easing, errors, options) {\n        if (easing) {\n            this._previousKeyframe['easing'] = easing;\n        }\n        const /** @type {?} */ params = (options && options.params) || {};\n        const /** @type {?} */ styles = flattenStyles(input, this._globalTimelineStyles);\n        Object.keys(styles).forEach(prop => {\n            const /** @type {?} */ val = interpolateParams(styles[prop], params, errors);\n            this._pendingStyles[prop] = val;\n            if (!this._localTimelineStyles.hasOwnProperty(prop)) {\n                this._backFill[prop] = this._globalTimelineStyles.hasOwnProperty(prop) ?\n                    this._globalTimelineStyles[prop] :\n                    AUTO_STYLE;\n            }\n            this._updateStyle(prop, val);\n        });\n    }\n    /**\n     * @return {?}\n     */\n    applyStylesToKeyframe() {\n        const /** @type {?} */ styles = this._pendingStyles;\n        const /** @type {?} */ props = Object.keys(styles);\n        if (props.length == 0)\n            return;\n        this._pendingStyles = {};\n        props.forEach(prop => {\n            const /** @type {?} */ val = styles[prop];\n            this._currentKeyframe[prop] = val;\n        });\n        Object.keys(this._localTimelineStyles).forEach(prop => {\n            if (!this._currentKeyframe.hasOwnProperty(prop)) {\n                this._currentKeyframe[prop] = this._localTimelineStyles[prop];\n            }\n        });\n    }\n    /**\n     * @return {?}\n     */\n    snapshotCurrentStyles() {\n        Object.keys(this._localTimelineStyles).forEach(prop => {\n            const /** @type {?} */ val = this._localTimelineStyles[prop];\n            this._pendingStyles[prop] = val;\n            this._updateStyle(prop, val);\n        });\n    }\n    /**\n     * @return {?}\n     */\n    getFinalKeyframe() { return this._keyframes.get(this.duration); }\n    /**\n     * @return {?}\n     */\n    get properties() {\n        const /** @type {?} */ properties = [];\n        for (let /** @type {?} */ prop in this._currentKeyframe) {\n            properties.push(prop);\n        }\n        return properties;\n    }\n    /**\n     * @param {?} timeline\n     * @return {?}\n     */\n    mergeTimelineCollectedStyles(timeline) {\n        Object.keys(timeline._styleSummary).forEach(prop => {\n            const /** @type {?} */ details0 = this._styleSummary[prop];\n            const /** @type {?} */ details1 = timeline._styleSummary[prop];\n            if (!details0 || details1.time > details0.time) {\n                this._updateStyle(prop, details1.value);\n            }\n        });\n    }\n    /**\n     * @return {?}\n     */\n    buildKeyframes() {\n        this.applyStylesToKeyframe();\n        const /** @type {?} */ preStyleProps = new Set();\n        const /** @type {?} */ postStyleProps = new Set();\n        const /** @type {?} */ isEmpty = this._keyframes.size === 1 && this.duration === 0;\n        let /** @type {?} */ finalKeyframes = [];\n        this._keyframes.forEach((keyframe, time) => {\n            const /** @type {?} */ finalKeyframe = copyStyles(keyframe, true);\n            Object.keys(finalKeyframe).forEach(prop => {\n                const /** @type {?} */ value = finalKeyframe[prop];\n                if (value == PRE_STYLE) {\n                    preStyleProps.add(prop);\n                }\n                else if (value == AUTO_STYLE) {\n                    postStyleProps.add(prop);\n                }\n            });\n            if (!isEmpty) {\n                finalKeyframe['offset'] = time / this.duration;\n            }\n            finalKeyframes.push(finalKeyframe);\n        });\n        const /** @type {?} */ preProps = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n        const /** @type {?} */ postProps = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n        // special case for a 0-second animation (which is designed just to place styles onscreen)\n        if (isEmpty) {\n            const /** @type {?} */ kf0 = finalKeyframes[0];\n            const /** @type {?} */ kf1 = copyObj(kf0);\n            kf0['offset'] = 0;\n            kf1['offset'] = 1;\n            finalKeyframes = [kf0, kf1];\n        }\n        return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n    }\n}\nfunction TimelineBuilder_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TimelineBuilder.prototype.duration;\n    /** @type {?} */\n    TimelineBuilder.prototype.easing;\n    /** @type {?} */\n    TimelineBuilder.prototype._previousKeyframe;\n    /** @type {?} */\n    TimelineBuilder.prototype._currentKeyframe;\n    /** @type {?} */\n    TimelineBuilder.prototype._keyframes;\n    /** @type {?} */\n    TimelineBuilder.prototype._styleSummary;\n    /** @type {?} */\n    TimelineBuilder.prototype._localTimelineStyles;\n    /** @type {?} */\n    TimelineBuilder.prototype._globalTimelineStyles;\n    /** @type {?} */\n    TimelineBuilder.prototype._pendingStyles;\n    /** @type {?} */\n    TimelineBuilder.prototype._backFill;\n    /** @type {?} */\n    TimelineBuilder.prototype._currentEmptyStepKeyframe;\n    /** @type {?} */\n    TimelineBuilder.prototype._driver;\n    /** @type {?} */\n    TimelineBuilder.prototype.element;\n    /** @type {?} */\n    TimelineBuilder.prototype.startTime;\n    /** @type {?} */\n    TimelineBuilder.prototype._elementTimelineStylesLookup;\n}\nclass SubTimelineBuilder extends TimelineBuilder {\n    /**\n     * @param {?} driver\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} preStyleProps\n     * @param {?} postStyleProps\n     * @param {?} timings\n     * @param {?=} _stretchStartingKeyframe\n     */\n    constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n        super(driver, element, timings.delay);\n        this.element = element;\n        this.keyframes = keyframes;\n        this.preStyleProps = preStyleProps;\n        this.postStyleProps = postStyleProps;\n        this._stretchStartingKeyframe = _stretchStartingKeyframe;\n        this.timings = { duration: timings.duration, delay: timings.delay, easing: timings.easing };\n    }\n    /**\n     * @return {?}\n     */\n    containsAnimation() { return this.keyframes.length > 1; }\n    /**\n     * @return {?}\n     */\n    buildKeyframes() {\n        let /** @type {?} */ keyframes = this.keyframes;\n        let { delay, duration, easing } = this.timings;\n        if (this._stretchStartingKeyframe && delay) {\n            const /** @type {?} */ newKeyframes = [];\n            const /** @type {?} */ totalTime = duration + delay;\n            const /** @type {?} */ startingGap = delay / totalTime;\n            // the original starting keyframe now starts once the delay is done\n            const /** @type {?} */ newFirstKeyframe = copyStyles(keyframes[0], false);\n            newFirstKeyframe['offset'] = 0;\n            newKeyframes.push(newFirstKeyframe);\n            const /** @type {?} */ oldFirstKeyframe = copyStyles(keyframes[0], false);\n            oldFirstKeyframe['offset'] = roundOffset(startingGap);\n            newKeyframes.push(oldFirstKeyframe);\n            /*\n                    When the keyframe is stretched then it means that the delay before the animation\n                    starts is gone. Instead the first keyframe is placed at the start of the animation\n                    and it is then copied to where it starts when the original delay is over. This basically\n                    means nothing animates during that delay, but the styles are still renderered. For this\n                    to work the original offset values that exist in the original keyframes must be \"warped\"\n                    so that they can take the new keyframe + delay into account.\n            \n                    delay=1000, duration=1000, keyframes = 0 .5 1\n            \n                    turns into\n            \n                    delay=0, duration=2000, keyframes = 0 .33 .66 1\n                   */\n            // offsets between 1 ... n -1 are all warped by the keyframe stretch\n            const /** @type {?} */ limit = keyframes.length - 1;\n            for (let /** @type {?} */ i = 1; i <= limit; i++) {\n                let /** @type {?} */ kf = copyStyles(keyframes[i], false);\n                const /** @type {?} */ oldOffset = /** @type {?} */ (kf['offset']);\n                const /** @type {?} */ timeAtKeyframe = delay + oldOffset * duration;\n                kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n                newKeyframes.push(kf);\n            }\n            // the new starting keyframe should be added at the start\n            duration = totalTime;\n            delay = 0;\n            easing = '';\n            keyframes = newKeyframes;\n        }\n        return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n    }\n}\nfunction SubTimelineBuilder_tsickle_Closure_declarations() {\n    /** @type {?} */\n    SubTimelineBuilder.prototype.timings;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.element;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.keyframes;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.preStyleProps;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.postStyleProps;\n    /** @type {?} */\n    SubTimelineBuilder.prototype._stretchStartingKeyframe;\n}\n/**\n * @param {?} offset\n * @param {?=} decimalPoints\n * @return {?}\n */\nfunction roundOffset(offset, decimalPoints = 3) {\n    const /** @type {?} */ mult = Math.pow(10, decimalPoints - 1);\n    return Math.round(offset * mult) / mult;\n}\n/**\n * @param {?} input\n * @param {?} allStyles\n * @return {?}\n */\nfunction flattenStyles(input, allStyles) {\n    const /** @type {?} */ styles = {};\n    let /** @type {?} */ allProperties;\n    input.forEach(token => {\n        if (token === '*') {\n            allProperties = allProperties || Object.keys(allStyles);\n            allProperties.forEach(prop => { styles[prop] = AUTO_STYLE; });\n        }\n        else {\n            copyStyles(/** @type {?} */ (token), false, styles);\n        }\n    });\n    return styles;\n}\n//# sourceMappingURL=animation_timeline_builder.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { ENTER_CLASSNAME, LEAVE_CLASSNAME, normalizeStyles } from '../util';\nimport { buildAnimationAst } from './animation_ast_builder';\nimport { buildAnimationTimelines } from './animation_timeline_builder';\nimport { ElementInstructionMap } from './element_instruction_map';\nexport class Animation {\n    /**\n     * @param {?} _driver\n     * @param {?} input\n     */\n    constructor(_driver, input) {\n        this._driver = _driver;\n        const /** @type {?} */ errors = [];\n        const /** @type {?} */ ast = buildAnimationAst(_driver, input, errors);\n        if (errors.length) {\n            const /** @type {?} */ errorMessage = `animation validation failed:\\n${errors.join(\"\\n\")}`;\n            throw new Error(errorMessage);\n        }\n        this._animationAst = ast;\n    }\n    /**\n     * @param {?} element\n     * @param {?} startingStyles\n     * @param {?} destinationStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n        const /** @type {?} */ start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : /** @type {?} */ (startingStyles);\n        const /** @type {?} */ dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : /** @type {?} */ (destinationStyles);\n        const /** @type {?} */ errors = [];\n        subInstructions = subInstructions || new ElementInstructionMap();\n        const /** @type {?} */ result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n        if (errors.length) {\n            const /** @type {?} */ errorMessage = `animation building failed:\\n${errors.join(\"\\n\")}`;\n            throw new Error(errorMessage);\n        }\n        return result;\n    }\n}\nfunction Animation_tsickle_Closure_declarations() {\n    /** @type {?} */\n    Animation.prototype._animationAst;\n    /** @type {?} */\n    Animation.prototype._driver;\n}\n//# sourceMappingURL=animation.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport class AnimationStyleNormalizer {\n}\nfunction AnimationStyleNormalizer_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationStyleNormalizer.prototype.normalizePropertyName = function (propertyName, errors) { };\n    /**\n     * @abstract\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationStyleNormalizer.prototype.normalizeStyleValue = function (userProvidedProperty, normalizedProperty, value, errors) { };\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class NoopAnimationStyleNormalizer {\n    /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    normalizePropertyName(propertyName, errors) { return propertyName; }\n    /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n        return /** @type {?} */ (value);\n    }\n}\n//# sourceMappingURL=animation_style_normalizer.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { dashCaseToCamelCase } from '../../util';\nimport { AnimationStyleNormalizer } from './animation_style_normalizer';\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n    /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    normalizePropertyName(propertyName, errors) {\n        return dashCaseToCamelCase(propertyName);\n    }\n    /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n        let /** @type {?} */ unit = '';\n        const /** @type {?} */ strVal = value.toString().trim();\n        if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n            if (typeof value === 'number') {\n                unit = 'px';\n            }\n            else {\n                const /** @type {?} */ valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n                if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n                    errors.push(`Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n                }\n            }\n        }\n        return strVal + unit;\n    }\n}\nconst /** @type {?} */ DIMENSIONAL_PROP_MAP = makeBooleanMap('width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n    .split(','));\n/**\n * @param {?} keys\n * @return {?}\n */\nfunction makeBooleanMap(keys) {\n    const /** @type {?} */ map = {};\n    keys.forEach(key => map[key] = true);\n    return map;\n}\n//# sourceMappingURL=web_animations_style_normalizer.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @record\n */\nexport function AnimationTransitionInstruction() { }\nfunction AnimationTransitionInstruction_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.element;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.triggerName;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.isRemovalTransition;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.fromState;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.fromStyles;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.toState;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.toStyles;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.timelines;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.queriedElements;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.preStyleProps;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.postStyleProps;\n    /** @type {?|undefined} */\n    AnimationTransitionInstruction.prototype.errors;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?} isRemovalTransition\n * @param {?} fromStyles\n * @param {?} toStyles\n * @param {?} timelines\n * @param {?} queriedElements\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?=} errors\n * @return {?}\n */\nexport function createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, errors) {\n    return {\n        type: 0 /* TransitionAnimation */,\n        element,\n        triggerName,\n        isRemovalTransition,\n        fromState,\n        fromStyles,\n        toState,\n        toStyles,\n        timelines,\n        queriedElements,\n        preStyleProps,\n        postStyleProps,\n        errors\n    };\n}\n//# sourceMappingURL=animation_transition_instruction.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { getOrSetAsInMap } from '../render/shared';\nimport { copyObj, interpolateParams, iteratorToArray } from '../util';\nimport { buildAnimationTimelines } from './animation_timeline_builder';\nimport { createTransitionInstruction } from './animation_transition_instruction';\nconst /** @type {?} */ EMPTY_OBJECT = {};\nexport class AnimationTransitionFactory {\n    /**\n     * @param {?} _triggerName\n     * @param {?} ast\n     * @param {?} _stateStyles\n     */\n    constructor(_triggerName, ast, _stateStyles) {\n        this._triggerName = _triggerName;\n        this.ast = ast;\n        this._stateStyles = _stateStyles;\n    }\n    /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    match(currentState, nextState) {\n        return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState);\n    }\n    /**\n     * @param {?} stateName\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    buildStyles(stateName, params, errors) {\n        const /** @type {?} */ backupStateStyler = this._stateStyles['*'];\n        const /** @type {?} */ stateStyler = this._stateStyles[stateName];\n        const /** @type {?} */ backupStyles = backupStateStyler ? backupStateStyler.buildStyles(params, errors) : {};\n        return stateStyler ? stateStyler.buildStyles(params, errors) : backupStyles;\n    }\n    /**\n     * @param {?} driver\n     * @param {?} element\n     * @param {?} currentState\n     * @param {?} nextState\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?=} currentOptions\n     * @param {?=} nextOptions\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions) {\n        const /** @type {?} */ errors = [];\n        const /** @type {?} */ transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n        const /** @type {?} */ currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n        const /** @type {?} */ currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n        const /** @type {?} */ nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n        const /** @type {?} */ nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n        const /** @type {?} */ queriedElements = new Set();\n        const /** @type {?} */ preStyleMap = new Map();\n        const /** @type {?} */ postStyleMap = new Map();\n        const /** @type {?} */ isRemoval = nextState === 'void';\n        const /** @type {?} */ animationOptions = { params: Object.assign({}, transitionAnimationParams, nextAnimationParams) };\n        const /** @type {?} */ timelines = buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n        if (errors.length) {\n            return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, errors);\n        }\n        timelines.forEach(tl => {\n            const /** @type {?} */ elm = tl.element;\n            const /** @type {?} */ preProps = getOrSetAsInMap(preStyleMap, elm, {});\n            tl.preStyleProps.forEach(prop => preProps[prop] = true);\n            const /** @type {?} */ postProps = getOrSetAsInMap(postStyleMap, elm, {});\n            tl.postStyleProps.forEach(prop => postProps[prop] = true);\n            if (elm !== element) {\n                queriedElements.add(elm);\n            }\n        });\n        const /** @type {?} */ queriedElementsList = iteratorToArray(queriedElements.values());\n        return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap, postStyleMap);\n    }\n}\nfunction AnimationTransitionFactory_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTransitionFactory.prototype._triggerName;\n    /** @type {?} */\n    AnimationTransitionFactory.prototype.ast;\n    /** @type {?} */\n    AnimationTransitionFactory.prototype._stateStyles;\n}\n/**\n * @param {?} matchFns\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState) {\n    return matchFns.some(fn => fn(currentState, nextState));\n}\nexport class AnimationStateStyles {\n    /**\n     * @param {?} styles\n     * @param {?} defaultParams\n     */\n    constructor(styles, defaultParams) {\n        this.styles = styles;\n        this.defaultParams = defaultParams;\n    }\n    /**\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    buildStyles(params, errors) {\n        const /** @type {?} */ finalStyles = {};\n        const /** @type {?} */ combinedParams = copyObj(this.defaultParams);\n        Object.keys(params).forEach(key => {\n            const /** @type {?} */ value = params[key];\n            if (value != null) {\n                combinedParams[key] = value;\n            }\n        });\n        this.styles.styles.forEach(value => {\n            if (typeof value !== 'string') {\n                const /** @type {?} */ styleObj = /** @type {?} */ (value);\n                Object.keys(styleObj).forEach(prop => {\n                    let /** @type {?} */ val = styleObj[prop];\n                    if (val.length > 1) {\n                        val = interpolateParams(val, combinedParams, errors);\n                    }\n                    finalStyles[prop] = val;\n                });\n            }\n        });\n        return finalStyles;\n    }\n}\nfunction AnimationStateStyles_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationStateStyles.prototype.styles;\n    /** @type {?} */\n    AnimationStateStyles.prototype.defaultParams;\n}\n//# sourceMappingURL=animation_transition_factory.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AnimationStateStyles, AnimationTransitionFactory } from './animation_transition_factory';\n/**\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nexport function buildTrigger(name, ast) {\n    return new AnimationTrigger(name, ast);\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class AnimationTrigger {\n    /**\n     * @param {?} name\n     * @param {?} ast\n     */\n    constructor(name, ast) {\n        this.name = name;\n        this.ast = ast;\n        this.transitionFactories = [];\n        this.states = {};\n        ast.states.forEach(ast => {\n            const /** @type {?} */ defaultParams = (ast.options && ast.options.params) || {};\n            this.states[ast.name] = new AnimationStateStyles(ast.style, defaultParams);\n        });\n        balanceProperties(this.states, 'true', '1');\n        balanceProperties(this.states, 'false', '0');\n        ast.transitions.forEach(ast => {\n            this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n        });\n        this.fallbackTransition = createFallbackTransition(name, this.states);\n    }\n    /**\n     * @return {?}\n     */\n    get containsQueries() { return this.ast.queryCount > 0; }\n    /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    matchTransition(currentState, nextState) {\n        const /** @type {?} */ entry = this.transitionFactories.find(f => f.match(currentState, nextState));\n        return entry || null;\n    }\n    /**\n     * @param {?} currentState\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    matchStyles(currentState, params, errors) {\n        return this.fallbackTransition.buildStyles(currentState, params, errors);\n    }\n}\nfunction AnimationTrigger_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTrigger.prototype.transitionFactories;\n    /** @type {?} */\n    AnimationTrigger.prototype.fallbackTransition;\n    /** @type {?} */\n    AnimationTrigger.prototype.states;\n    /** @type {?} */\n    AnimationTrigger.prototype.name;\n    /** @type {?} */\n    AnimationTrigger.prototype.ast;\n}\n/**\n * @param {?} triggerName\n * @param {?} states\n * @return {?}\n */\nfunction createFallbackTransition(triggerName, states) {\n    const /** @type {?} */ matchers = [(fromState, toState) => true];\n    const /** @type {?} */ animation = { type: 2 /* Sequence */, steps: [], options: null };\n    const /** @type {?} */ transition = {\n        type: 1 /* Transition */,\n        animation,\n        matchers,\n        options: null,\n        queryCount: 0,\n        depCount: 0\n    };\n    return new AnimationTransitionFactory(triggerName, transition, states);\n}\n/**\n * @param {?} obj\n * @param {?} key1\n * @param {?} key2\n * @return {?}\n */\nfunction balanceProperties(obj, key1, key2) {\n    if (obj.hasOwnProperty(key1)) {\n        if (!obj.hasOwnProperty(key2)) {\n            obj[key2] = obj[key1];\n        }\n    }\n    else if (obj.hasOwnProperty(key2)) {\n        obj[key1] = obj[key2];\n    }\n}\n//# sourceMappingURL=animation_trigger.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE } from '@angular/animations';\nimport { buildAnimationAst } from '../dsl/animation_ast_builder';\nimport { buildAnimationTimelines } from '../dsl/animation_timeline_builder';\nimport { ElementInstructionMap } from '../dsl/element_instruction_map';\nimport { ENTER_CLASSNAME, LEAVE_CLASSNAME } from '../util';\nimport { getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer } from './shared';\nconst /** @type {?} */ EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nexport class TimelineAnimationEngine {\n    /**\n     * @param {?} _driver\n     * @param {?} _normalizer\n     */\n    constructor(_driver, _normalizer) {\n        this._driver = _driver;\n        this._normalizer = _normalizer;\n        this._animations = {};\n        this._playersById = {};\n        this.players = [];\n    }\n    /**\n     * @param {?} id\n     * @param {?} metadata\n     * @return {?}\n     */\n    register(id, metadata) {\n        const /** @type {?} */ errors = [];\n        const /** @type {?} */ ast = buildAnimationAst(this._driver, metadata, errors);\n        if (errors.length) {\n            throw new Error(`Unable to build the animation due to the following errors: ${errors.join(\"\\n\")}`);\n        }\n        else {\n            this._animations[id] = ast;\n        }\n    }\n    /**\n     * @param {?} i\n     * @param {?} preStyles\n     * @param {?=} postStyles\n     * @return {?}\n     */\n    _buildPlayer(i, preStyles, postStyles) {\n        const /** @type {?} */ element = i.element;\n        const /** @type {?} */ keyframes = normalizeKeyframes(this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n        return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, []);\n    }\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?=} options\n     * @return {?}\n     */\n    create(id, element, options = {}) {\n        const /** @type {?} */ errors = [];\n        const /** @type {?} */ ast = this._animations[id];\n        let /** @type {?} */ instructions;\n        const /** @type {?} */ autoStylesMap = new Map();\n        if (ast) {\n            instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n            instructions.forEach(inst => {\n                const /** @type {?} */ styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n                inst.postStyleProps.forEach(prop => styles[prop] = null);\n            });\n        }\n        else {\n            errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n            instructions = [];\n        }\n        if (errors.length) {\n            throw new Error(`Unable to create the animation due to the following errors: ${errors.join(\"\\n\")}`);\n        }\n        autoStylesMap.forEach((styles, element) => {\n            Object.keys(styles).forEach(prop => { styles[prop] = this._driver.computeStyle(element, prop, AUTO_STYLE); });\n        });\n        const /** @type {?} */ players = instructions.map(i => {\n            const /** @type {?} */ styles = autoStylesMap.get(i.element);\n            return this._buildPlayer(i, {}, styles);\n        });\n        const /** @type {?} */ player = optimizeGroupPlayer(players);\n        this._playersById[id] = player;\n        player.onDestroy(() => this.destroy(id));\n        this.players.push(player);\n        return player;\n    }\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    destroy(id) {\n        const /** @type {?} */ player = this._getPlayer(id);\n        player.destroy();\n        delete this._playersById[id];\n        const /** @type {?} */ index = this.players.indexOf(player);\n        if (index >= 0) {\n            this.players.splice(index, 1);\n        }\n    }\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    _getPlayer(id) {\n        const /** @type {?} */ player = this._playersById[id];\n        if (!player) {\n            throw new Error(`Unable to find the timeline player referenced by ${id}`);\n        }\n        return player;\n    }\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} callback\n     * @return {?}\n     */\n    listen(id, element, eventName, callback) {\n        // triggerName, fromState, toState are all ignored for timeline animations\n        const /** @type {?} */ baseEvent = makeAnimationEvent(element, '', '', '');\n        listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n        return () => { };\n    }\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} command\n     * @param {?} args\n     * @return {?}\n     */\n    command(id, element, command, args) {\n        if (command == 'register') {\n            this.register(id, /** @type {?} */ (args[0]));\n            return;\n        }\n        if (command == 'create') {\n            const /** @type {?} */ options = /** @type {?} */ ((args[0] || {}));\n            this.create(id, element, options);\n            return;\n        }\n        const /** @type {?} */ player = this._getPlayer(id);\n        switch (command) {\n            case 'play':\n                player.play();\n                break;\n            case 'pause':\n                player.pause();\n                break;\n            case 'reset':\n                player.reset();\n                break;\n            case 'restart':\n                player.restart();\n                break;\n            case 'finish':\n                player.finish();\n                break;\n            case 'init':\n                player.init();\n                break;\n            case 'setPosition':\n                player.setPosition(parseFloat(/** @type {?} */ (args[0])));\n                break;\n            case 'destroy':\n                this.destroy(id);\n                break;\n        }\n    }\n}\nfunction TimelineAnimationEngine_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._animations;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._playersById;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype.players;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._driver;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._normalizer;\n}\n//# sourceMappingURL=timeline_animation_engine.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, NoopAnimationPlayer, ɵAnimationGroupPlayer as AnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\nimport { ElementInstructionMap } from '../dsl/element_instruction_map';\nimport { ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, copyObj, eraseStyles, setStyles } from '../util';\nimport { getBodyNode, getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer } from './shared';\nconst /** @type {?} */ QUEUED_CLASSNAME = 'ng-animate-queued';\nconst /** @type {?} */ QUEUED_SELECTOR = '.ng-animate-queued';\nconst /** @type {?} */ DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst /** @type {?} */ DISABLED_SELECTOR = '.ng-animate-disabled';\nconst /** @type {?} */ STAR_CLASSNAME = 'ng-star-inserted';\nconst /** @type {?} */ STAR_SELECTOR = '.ng-star-inserted';\nconst /** @type {?} */ EMPTY_PLAYER_ARRAY = [];\nconst /** @type {?} */ NULL_REMOVAL_STATE = {\n    namespaceId: '',\n    setForRemoval: null,\n    hasAnimation: false,\n    removedBeforeQueried: false\n};\nconst /** @type {?} */ NULL_REMOVED_QUERIED_STATE = {\n    namespaceId: '',\n    setForRemoval: null,\n    hasAnimation: false,\n    removedBeforeQueried: true\n};\n/**\n * @record\n */\nfunction TriggerListener() { }\nfunction TriggerListener_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TriggerListener.prototype.name;\n    /** @type {?} */\n    TriggerListener.prototype.phase;\n    /** @type {?} */\n    TriggerListener.prototype.callback;\n}\n/**\n * @record\n */\nexport function QueueInstruction() { }\nfunction QueueInstruction_tsickle_Closure_declarations() {\n    /** @type {?} */\n    QueueInstruction.prototype.element;\n    /** @type {?} */\n    QueueInstruction.prototype.triggerName;\n    /** @type {?} */\n    QueueInstruction.prototype.fromState;\n    /** @type {?} */\n    QueueInstruction.prototype.toState;\n    /** @type {?} */\n    QueueInstruction.prototype.transition;\n    /** @type {?} */\n    QueueInstruction.prototype.player;\n    /** @type {?} */\n    QueueInstruction.prototype.isFallbackTransition;\n}\nexport const /** @type {?} */ REMOVAL_FLAG = '__ng_removed';\n/**\n * @record\n */\nexport function ElementAnimationState() { }\nfunction ElementAnimationState_tsickle_Closure_declarations() {\n    /** @type {?} */\n    ElementAnimationState.prototype.setForRemoval;\n    /** @type {?} */\n    ElementAnimationState.prototype.hasAnimation;\n    /** @type {?} */\n    ElementAnimationState.prototype.namespaceId;\n    /** @type {?} */\n    ElementAnimationState.prototype.removedBeforeQueried;\n}\nexport class StateValue {\n    /**\n     * @param {?} input\n     * @param {?=} namespaceId\n     */\n    constructor(input, namespaceId = '') {\n        this.namespaceId = namespaceId;\n        const /** @type {?} */ isObj = input && input.hasOwnProperty('value');\n        const /** @type {?} */ value = isObj ? input['value'] : input;\n        this.value = normalizeTriggerValue(value);\n        if (isObj) {\n            const /** @type {?} */ options = copyObj(/** @type {?} */ (input));\n            delete options['value'];\n            this.options = /** @type {?} */ (options);\n        }\n        else {\n            this.options = {};\n        }\n        if (!this.options.params) {\n            this.options.params = {};\n        }\n    }\n    /**\n     * @return {?}\n     */\n    get params() { return /** @type {?} */ (this.options.params); }\n    /**\n     * @param {?} options\n     * @return {?}\n     */\n    absorbOptions(options) {\n        const /** @type {?} */ newParams = options.params;\n        if (newParams) {\n            const /** @type {?} */ oldParams = /** @type {?} */ ((this.options.params));\n            Object.keys(newParams).forEach(prop => {\n                if (oldParams[prop] == null) {\n                    oldParams[prop] = newParams[prop];\n                }\n            });\n        }\n    }\n}\nfunction StateValue_tsickle_Closure_declarations() {\n    /** @type {?} */\n    StateValue.prototype.value;\n    /** @type {?} */\n    StateValue.prototype.options;\n    /** @type {?} */\n    StateValue.prototype.namespaceId;\n}\nexport const /** @type {?} */ VOID_VALUE = 'void';\nexport const /** @type {?} */ DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nexport const /** @type {?} */ DELETED_STATE_VALUE = new StateValue('DELETED');\nexport class AnimationTransitionNamespace {\n    /**\n     * @param {?} id\n     * @param {?} hostElement\n     * @param {?} _engine\n     */\n    constructor(id, hostElement, _engine) {\n        this.id = id;\n        this.hostElement = hostElement;\n        this._engine = _engine;\n        this.players = [];\n        this._triggers = {};\n        this._queue = [];\n        this._elementListeners = new Map();\n        this._hostClassName = 'ng-tns-' + id;\n        addClass(hostElement, this._hostClassName);\n    }\n    /**\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    listen(element, name, phase, callback) {\n        if (!this._triggers.hasOwnProperty(name)) {\n            throw new Error(`Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n        }\n        if (phase == null || phase.length == 0) {\n            throw new Error(`Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n        }\n        if (!isTriggerEventValid(phase)) {\n            throw new Error(`The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n        }\n        const /** @type {?} */ listeners = getOrSetAsInMap(this._elementListeners, element, []);\n        const /** @type {?} */ data = { name, phase, callback };\n        listeners.push(data);\n        const /** @type {?} */ triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n        if (!triggersWithStates.hasOwnProperty(name)) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n            triggersWithStates[name] = DEFAULT_STATE_VALUE;\n        }\n        return () => {\n            // the event listener is removed AFTER the flush has occurred such\n            // that leave animations callbacks can fire (otherwise if the node\n            // is removed in between then the listeners would be deregistered)\n            this._engine.afterFlush(() => {\n                const /** @type {?} */ index = listeners.indexOf(data);\n                if (index >= 0) {\n                    listeners.splice(index, 1);\n                }\n                if (!this._triggers[name]) {\n                    delete triggersWithStates[name];\n                }\n            });\n        };\n    }\n    /**\n     * @param {?} name\n     * @param {?} ast\n     * @return {?}\n     */\n    register(name, ast) {\n        if (this._triggers[name]) {\n            // throw\n            return false;\n        }\n        else {\n            this._triggers[name] = ast;\n            return true;\n        }\n    }\n    /**\n     * @param {?} name\n     * @return {?}\n     */\n    _getTrigger(name) {\n        const /** @type {?} */ trigger = this._triggers[name];\n        if (!trigger) {\n            throw new Error(`The provided animation trigger \"${name}\" has not been registered!`);\n        }\n        return trigger;\n    }\n    /**\n     * @param {?} element\n     * @param {?} triggerName\n     * @param {?} value\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    trigger(element, triggerName, value, defaultToFallback = true) {\n        const /** @type {?} */ trigger = this._getTrigger(triggerName);\n        const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        let /** @type {?} */ triggersWithStates = this._engine.statesByElement.get(element);\n        if (!triggersWithStates) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n            this._engine.statesByElement.set(element, triggersWithStates = {});\n        }\n        let /** @type {?} */ fromState = triggersWithStates[triggerName];\n        const /** @type {?} */ toState = new StateValue(value, this.id);\n        const /** @type {?} */ isObj = value && value.hasOwnProperty('value');\n        if (!isObj && fromState) {\n            toState.absorbOptions(fromState.options);\n        }\n        triggersWithStates[triggerName] = toState;\n        if (!fromState) {\n            fromState = DEFAULT_STATE_VALUE;\n        }\n        else if (fromState === DELETED_STATE_VALUE) {\n            return player;\n        }\n        const /** @type {?} */ isRemoval = toState.value === VOID_VALUE;\n        // normally this isn't reached by here, however, if an object expression\n        // is passed in then it may be a new object each time. Comparing the value\n        // is important since that will stay the same despite there being a new object.\n        // The removal arc here is special cased because the same element is triggered\n        // twice in the event that it contains animations on the outer/inner portions\n        // of the host container\n        if (!isRemoval && fromState.value === toState.value) {\n            // this means that despite the value not changing, some inner params\n            // have changed which means that the animation final styles need to be applied\n            if (!objEquals(fromState.params, toState.params)) {\n                const /** @type {?} */ errors = [];\n                const /** @type {?} */ fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n                const /** @type {?} */ toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n                if (errors.length) {\n                    this._engine.reportError(errors);\n                }\n                else {\n                    this._engine.afterFlush(() => {\n                        eraseStyles(element, fromStyles);\n                        setStyles(element, toStyles);\n                    });\n                }\n            }\n            return;\n        }\n        const /** @type {?} */ playersOnElement = getOrSetAsInMap(this._engine.playersByElement, element, []);\n        playersOnElement.forEach(player => {\n            // only remove the player if it is queued on the EXACT same trigger/namespace\n            // we only also deal with queued players here because if the animation has\n            // started then we want to keep the player alive until the flush happens\n            // (which is where the previousPlayers are passed into the new palyer)\n            if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n                player.destroy();\n            }\n        });\n        let /** @type {?} */ transition = trigger.matchTransition(fromState.value, toState.value);\n        let /** @type {?} */ isFallbackTransition = false;\n        if (!transition) {\n            if (!defaultToFallback)\n                return;\n            transition = trigger.fallbackTransition;\n            isFallbackTransition = true;\n        }\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({ element, triggerName, transition, fromState, toState, player, isFallbackTransition });\n        if (!isFallbackTransition) {\n            addClass(element, QUEUED_CLASSNAME);\n            player.onStart(() => { removeClass(element, QUEUED_CLASSNAME); });\n        }\n        player.onDone(() => {\n            let /** @type {?} */ index = this.players.indexOf(player);\n            if (index >= 0) {\n                this.players.splice(index, 1);\n            }\n            const /** @type {?} */ players = this._engine.playersByElement.get(element);\n            if (players) {\n                let /** @type {?} */ index = players.indexOf(player);\n                if (index >= 0) {\n                    players.splice(index, 1);\n                }\n            }\n        });\n        this.players.push(player);\n        playersOnElement.push(player);\n        return player;\n    }\n    /**\n     * @param {?} name\n     * @return {?}\n     */\n    deregister(name) {\n        delete this._triggers[name];\n        this._engine.statesByElement.forEach((stateMap, element) => { delete stateMap[name]; });\n        this._elementListeners.forEach((listeners, element) => {\n            this._elementListeners.set(element, listeners.filter(entry => { return entry.name != name; }));\n        });\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    clearElementCache(element) {\n        this._engine.statesByElement.delete(element);\n        this._elementListeners.delete(element);\n        const /** @type {?} */ elementPlayers = this._engine.playersByElement.get(element);\n        if (elementPlayers) {\n            elementPlayers.forEach(player => player.destroy());\n            this._engine.playersByElement.delete(element);\n        }\n    }\n    /**\n     * @param {?} rootElement\n     * @param {?} context\n     * @param {?=} animate\n     * @return {?}\n     */\n    _signalRemovalForInnerTriggers(rootElement, context, animate = false) {\n        // emulate a leave animation for all inner nodes within this node.\n        // If there are no animations found for any of the nodes then clear the cache\n        // for the element.\n        this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(elm => {\n            // this means that an inner remove() operation has already kicked off\n            // the animation on this element...\n            if (elm[REMOVAL_FLAG])\n                return;\n            const /** @type {?} */ namespaces = this._engine.fetchNamespacesByElement(elm);\n            if (namespaces.size) {\n                namespaces.forEach(ns => ns.triggerLeaveAnimation(elm, context, false, true));\n            }\n            else {\n                this.clearElementCache(elm);\n            }\n        });\n    }\n    /**\n     * @param {?} element\n     * @param {?} context\n     * @param {?=} destroyAfterComplete\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n        const /** @type {?} */ triggerStates = this._engine.statesByElement.get(element);\n        if (triggerStates) {\n            const /** @type {?} */ players = [];\n            Object.keys(triggerStates).forEach(triggerName => {\n                // this check is here in the event that an element is removed\n                // twice (both on the host level and the component level)\n                if (this._triggers[triggerName]) {\n                    const /** @type {?} */ player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n                    if (player) {\n                        players.push(player);\n                    }\n                }\n            });\n            if (players.length) {\n                this._engine.markElementAsRemoved(this.id, element, true, context);\n                if (destroyAfterComplete) {\n                    optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n                }\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    prepareLeaveAnimationListeners(element) {\n        const /** @type {?} */ listeners = this._elementListeners.get(element);\n        if (listeners) {\n            const /** @type {?} */ visitedTriggers = new Set();\n            listeners.forEach(listener => {\n                const /** @type {?} */ triggerName = listener.name;\n                if (visitedTriggers.has(triggerName))\n                    return;\n                visitedTriggers.add(triggerName);\n                const /** @type {?} */ trigger = this._triggers[triggerName];\n                const /** @type {?} */ transition = trigger.fallbackTransition;\n                const /** @type {?} */ elementStates = /** @type {?} */ ((this._engine.statesByElement.get(element)));\n                const /** @type {?} */ fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n                const /** @type {?} */ toState = new StateValue(VOID_VALUE);\n                const /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n                this._engine.totalQueuedPlayers++;\n                this._queue.push({\n                    element,\n                    triggerName,\n                    transition,\n                    fromState,\n                    toState,\n                    player,\n                    isFallbackTransition: true\n                });\n            });\n        }\n    }\n    /**\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    removeNode(element, context) {\n        const /** @type {?} */ engine = this._engine;\n        if (element.childElementCount) {\n            this._signalRemovalForInnerTriggers(element, context, true);\n        }\n        // this means that a * => VOID animation was detected and kicked off\n        if (this.triggerLeaveAnimation(element, context, true))\n            return;\n        // find the player that is animating and make sure that the\n        // removal is delayed until that player has completed\n        let /** @type {?} */ containsPotentialParentTransition = false;\n        if (engine.totalAnimations) {\n            const /** @type {?} */ currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n            // when this `if statement` does not continue forward it means that\n            // a previous animation query has selected the current element and\n            // is animating it. In this situation want to continue fowards and\n            // allow the element to be queued up for animation later.\n            if (currentPlayers && currentPlayers.length) {\n                containsPotentialParentTransition = true;\n            }\n            else {\n                let /** @type {?} */ parent = element;\n                while (parent = parent.parentNode) {\n                    const /** @type {?} */ triggers = engine.statesByElement.get(parent);\n                    if (triggers) {\n                        containsPotentialParentTransition = true;\n                        break;\n                    }\n                }\n            }\n        }\n        // at this stage we know that the element will either get removed\n        // during flush or will be picked up by a parent query. Either way\n        // we need to fire the listeners for this element when it DOES get\n        // removed (once the query parent animation is done or after flush)\n        this.prepareLeaveAnimationListeners(element);\n        // whether or not a parent has an animation we need to delay the deferral of the leave\n        // operation until we have more information (which we do after flush() has been called)\n        if (containsPotentialParentTransition) {\n            engine.markElementAsRemoved(this.id, element, false, context);\n        }\n        else {\n            // we do this after the flush has occurred such\n            // that the callbacks can be fired\n            engine.afterFlush(() => this.clearElementCache(element));\n            engine.destroyInnerAnimations(element);\n            engine._onRemovalComplete(element, context);\n        }\n    }\n    /**\n     * @param {?} element\n     * @param {?} parent\n     * @return {?}\n     */\n    insertNode(element, parent) { addClass(element, this._hostClassName); }\n    /**\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    drainQueuedTransitions(microtaskId) {\n        const /** @type {?} */ instructions = [];\n        this._queue.forEach(entry => {\n            const /** @type {?} */ player = entry.player;\n            if (player.destroyed)\n                return;\n            const /** @type {?} */ element = entry.element;\n            const /** @type {?} */ listeners = this._elementListeners.get(element);\n            if (listeners) {\n                listeners.forEach((listener) => {\n                    if (listener.name == entry.triggerName) {\n                        const /** @type {?} */ baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n                        (/** @type {?} */ (baseEvent))['_data'] = microtaskId;\n                        listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n                    }\n                });\n            }\n            if (player.markedForDestroy) {\n                this._engine.afterFlush(() => {\n                    // now we can destroy the element properly since the event listeners have\n                    // been bound to the player\n                    player.destroy();\n                });\n            }\n            else {\n                instructions.push(entry);\n            }\n        });\n        this._queue = [];\n        return instructions.sort((a, b) => {\n            // if depCount == 0 them move to front\n            // otherwise if a contains b then move back\n            const /** @type {?} */ d0 = a.transition.ast.depCount;\n            const /** @type {?} */ d1 = b.transition.ast.depCount;\n            if (d0 == 0 || d1 == 0) {\n                return d0 - d1;\n            }\n            return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n        });\n    }\n    /**\n     * @param {?} context\n     * @return {?}\n     */\n    destroy(context) {\n        this.players.forEach(p => p.destroy());\n        this._signalRemovalForInnerTriggers(this.hostElement, context);\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    elementContainsData(element) {\n        let /** @type {?} */ containsData = false;\n        if (this._elementListeners.has(element))\n            containsData = true;\n        containsData =\n            (this._queue.find(entry => entry.element === element) ? true : false) || containsData;\n        return containsData;\n    }\n}\nfunction AnimationTransitionNamespace_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype.players;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._triggers;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._queue;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._elementListeners;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._hostClassName;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype.id;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype.hostElement;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._engine;\n}\n/**\n * @record\n */\nexport function QueuedTransition() { }\nfunction QueuedTransition_tsickle_Closure_declarations() {\n    /** @type {?} */\n    QueuedTransition.prototype.element;\n    /** @type {?} */\n    QueuedTransition.prototype.instruction;\n    /** @type {?} */\n    QueuedTransition.prototype.player;\n}\nexport class TransitionAnimationEngine {\n    /**\n     * @param {?} driver\n     * @param {?} _normalizer\n     */\n    constructor(driver, _normalizer) {\n        this.driver = driver;\n        this._normalizer = _normalizer;\n        this.players = [];\n        this.newHostElements = new Map();\n        this.playersByElement = new Map();\n        this.playersByQueriedElement = new Map();\n        this.statesByElement = new Map();\n        this.disabledNodes = new Set();\n        this.totalAnimations = 0;\n        this.totalQueuedPlayers = 0;\n        this._namespaceLookup = {};\n        this._namespaceList = [];\n        this._flushFns = [];\n        this._whenQuietFns = [];\n        this.namespacesByHostElement = new Map();\n        this.collectedEnterElements = [];\n        this.collectedLeaveElements = [];\n        this.onRemovalComplete = (element, context) => { };\n    }\n    /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    _onRemovalComplete(element, context) { this.onRemovalComplete(element, context); }\n    /**\n     * @return {?}\n     */\n    get queuedPlayers() {\n        const /** @type {?} */ players = [];\n        this._namespaceList.forEach(ns => {\n            ns.players.forEach(player => {\n                if (player.queued) {\n                    players.push(player);\n                }\n            });\n        });\n        return players;\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    createNamespace(namespaceId, hostElement) {\n        const /** @type {?} */ ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n        if (hostElement.parentNode) {\n            this._balanceNamespaceList(ns, hostElement);\n        }\n        else {\n            // defer this later until flush during when the host element has\n            // been inserted so that we know exactly where to place it in\n            // the namespace list\n            this.newHostElements.set(hostElement, ns);\n            // given that this host element is apart of the animation code, it\n            // may or may not be inserted by a parent node that is an of an\n            // animation renderer type. If this happens then we can still have\n            // access to this item when we query for :enter nodes. If the parent\n            // is a renderer then the set data-structure will normalize the entry\n            this.collectEnterElement(hostElement);\n        }\n        return this._namespaceLookup[namespaceId] = ns;\n    }\n    /**\n     * @param {?} ns\n     * @param {?} hostElement\n     * @return {?}\n     */\n    _balanceNamespaceList(ns, hostElement) {\n        const /** @type {?} */ limit = this._namespaceList.length - 1;\n        if (limit >= 0) {\n            let /** @type {?} */ found = false;\n            for (let /** @type {?} */ i = limit; i >= 0; i--) {\n                const /** @type {?} */ nextNamespace = this._namespaceList[i];\n                if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n                    this._namespaceList.splice(i + 1, 0, ns);\n                    found = true;\n                    break;\n                }\n            }\n            if (!found) {\n                this._namespaceList.splice(0, 0, ns);\n            }\n        }\n        else {\n            this._namespaceList.push(ns);\n        }\n        this.namespacesByHostElement.set(hostElement, ns);\n        return ns;\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    register(namespaceId, hostElement) {\n        let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n        if (!ns) {\n            ns = this.createNamespace(namespaceId, hostElement);\n        }\n        return ns;\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} name\n     * @param {?} trigger\n     * @return {?}\n     */\n    registerTrigger(namespaceId, name, trigger) {\n        let /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n        if (ns && ns.register(name, trigger)) {\n            this.totalAnimations++;\n        }\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    destroy(namespaceId, context) {\n        if (!namespaceId)\n            return;\n        const /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n        this.afterFlush(() => {\n            this.namespacesByHostElement.delete(ns.hostElement);\n            delete this._namespaceLookup[namespaceId];\n            const /** @type {?} */ index = this._namespaceList.indexOf(ns);\n            if (index >= 0) {\n                this._namespaceList.splice(index, 1);\n            }\n        });\n        this.afterFlushAnimationsDone(() => ns.destroy(context));\n    }\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    _fetchNamespace(id) { return this._namespaceLookup[id]; }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    fetchNamespacesByElement(element) {\n        // normally there should only be one namespace per element, however\n        // if @triggers are placed on both the component element and then\n        // its host element (within the component code) then there will be\n        // two namespaces returned. We use a set here to simply the dedupe\n        // of namespaces incase there are multiple triggers both the elm and host\n        const /** @type {?} */ namespaces = new Set();\n        const /** @type {?} */ elementStates = this.statesByElement.get(element);\n        if (elementStates) {\n            const /** @type {?} */ keys = Object.keys(elementStates);\n            for (let /** @type {?} */ i = 0; i < keys.length; i++) {\n                const /** @type {?} */ nsId = elementStates[keys[i]].namespaceId;\n                if (nsId) {\n                    const /** @type {?} */ ns = this._fetchNamespace(nsId);\n                    if (ns) {\n                        namespaces.add(ns);\n                    }\n                }\n            }\n        }\n        return namespaces;\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    trigger(namespaceId, element, name, value) {\n        if (isElementNode(element)) {\n            this._fetchNamespace(namespaceId).trigger(element, name, value);\n            return true;\n        }\n        return false;\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    insertNode(namespaceId, element, parent, insertBefore) {\n        if (!isElementNode(element))\n            return;\n        // special case for when an element is removed and reinserted (move operation)\n        // when this occurs we do not want to use the element for deletion later\n        const /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval) {\n            details.setForRemoval = false;\n        }\n        // in the event that the namespaceId is blank then the caller\n        // code does not contain any animation code in it, but it is\n        // just being called so that the node is marked as being inserted\n        if (namespaceId) {\n            const /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n            // This if-statement is a workaround for router issue #21947.\n            // The router sometimes hits a race condition where while a route\n            // is being instantiated a new navigation arrives, triggering leave\n            // animation of DOM that has not been fully initialized, until this\n            // is resolved, we need to handle the scenario when DOM is not in a\n            // consistent state during the animation.\n            if (ns) {\n                ns.insertNode(element, parent);\n            }\n        }\n        // only *directives and host elements are inserted before\n        if (insertBefore) {\n            this.collectEnterElement(element);\n        }\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    collectEnterElement(element) { this.collectedEnterElements.push(element); }\n    /**\n     * @param {?} element\n     * @param {?} value\n     * @return {?}\n     */\n    markElementAsDisabled(element, value) {\n        if (value) {\n            if (!this.disabledNodes.has(element)) {\n                this.disabledNodes.add(element);\n                addClass(element, DISABLED_CLASSNAME);\n            }\n        }\n        else if (this.disabledNodes.has(element)) {\n            this.disabledNodes.delete(element);\n            removeClass(element, DISABLED_CLASSNAME);\n        }\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    removeNode(namespaceId, element, context) {\n        if (!isElementNode(element)) {\n            this._onRemovalComplete(element, context);\n            return;\n        }\n        const /** @type {?} */ ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n        if (ns) {\n            ns.removeNode(element, context);\n        }\n        else {\n            this.markElementAsRemoved(namespaceId, element, false, context);\n        }\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?=} hasAnimation\n     * @param {?=} context\n     * @return {?}\n     */\n    markElementAsRemoved(namespaceId, element, hasAnimation, context) {\n        this.collectedLeaveElements.push(element);\n        element[REMOVAL_FLAG] = {\n            namespaceId,\n            setForRemoval: context, hasAnimation,\n            removedBeforeQueried: false\n        };\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    listen(namespaceId, element, name, phase, callback) {\n        if (isElementNode(element)) {\n            return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n        }\n        return () => { };\n    }\n    /**\n     * @param {?} entry\n     * @param {?} subTimelines\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @return {?}\n     */\n    _buildInstruction(entry, subTimelines, enterClassName, leaveClassName) {\n        return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines);\n    }\n    /**\n     * @param {?} containerElement\n     * @return {?}\n     */\n    destroyInnerAnimations(containerElement) {\n        let /** @type {?} */ elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n        elements.forEach(element => this.destroyActiveAnimationsForElement(element));\n        if (this.playersByQueriedElement.size == 0)\n            return;\n        elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n        elements.forEach(element => this.finishActiveQueriedAnimationOnElement(element));\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    destroyActiveAnimationsForElement(element) {\n        const /** @type {?} */ players = this.playersByElement.get(element);\n        if (players) {\n            players.forEach(player => {\n                // special case for when an element is set for destruction, but hasn't started.\n                // in this situation we want to delay the destruction until the flush occurs\n                // so that any event listeners attached to the player are triggered.\n                if (player.queued) {\n                    player.markedForDestroy = true;\n                }\n                else {\n                    player.destroy();\n                }\n            });\n        }\n        const /** @type {?} */ stateMap = this.statesByElement.get(element);\n        if (stateMap) {\n            Object.keys(stateMap).forEach(triggerName => stateMap[triggerName] = DELETED_STATE_VALUE);\n        }\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    finishActiveQueriedAnimationOnElement(element) {\n        const /** @type {?} */ players = this.playersByQueriedElement.get(element);\n        if (players) {\n            players.forEach(player => player.finish());\n        }\n    }\n    /**\n     * @return {?}\n     */\n    whenRenderingDone() {\n        return new Promise(resolve => {\n            if (this.players.length) {\n                return optimizeGroupPlayer(this.players).onDone(() => resolve());\n            }\n            else {\n                resolve();\n            }\n        });\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    processLeaveNode(element) {\n        const /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval) {\n            // this will prevent it from removing it twice\n            element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n            if (details.namespaceId) {\n                this.destroyInnerAnimations(element);\n                const /** @type {?} */ ns = this._fetchNamespace(details.namespaceId);\n                if (ns) {\n                    ns.clearElementCache(element);\n                }\n            }\n            this._onRemovalComplete(element, details.setForRemoval);\n        }\n        if (this.driver.matchesElement(element, DISABLED_SELECTOR)) {\n            this.markElementAsDisabled(element, false);\n        }\n        this.driver.query(element, DISABLED_SELECTOR, true).forEach(node => {\n            this.markElementAsDisabled(element, false);\n        });\n    }\n    /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    flush(microtaskId = -1) {\n        let /** @type {?} */ players = [];\n        if (this.newHostElements.size) {\n            this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n            this.newHostElements.clear();\n        }\n        if (this.totalAnimations && this.collectedEnterElements.length) {\n            for (let /** @type {?} */ i = 0; i < this.collectedEnterElements.length; i++) {\n                const /** @type {?} */ elm = this.collectedEnterElements[i];\n                addClass(elm, STAR_CLASSNAME);\n            }\n        }\n        if (this._namespaceList.length &&\n            (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n            const /** @type {?} */ cleanupFns = [];\n            try {\n                players = this._flushAnimations(cleanupFns, microtaskId);\n            }\n            finally {\n                for (let /** @type {?} */ i = 0; i < cleanupFns.length; i++) {\n                    cleanupFns[i]();\n                }\n            }\n        }\n        else {\n            for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n                const /** @type {?} */ element = this.collectedLeaveElements[i];\n                this.processLeaveNode(element);\n            }\n        }\n        this.totalQueuedPlayers = 0;\n        this.collectedEnterElements.length = 0;\n        this.collectedLeaveElements.length = 0;\n        this._flushFns.forEach(fn => fn());\n        this._flushFns = [];\n        if (this._whenQuietFns.length) {\n            // we move these over to a variable so that\n            // if any new callbacks are registered in another\n            // flush they do not populate the existing set\n            const /** @type {?} */ quietFns = this._whenQuietFns;\n            this._whenQuietFns = [];\n            if (players.length) {\n                optimizeGroupPlayer(players).onDone(() => { quietFns.forEach(fn => fn()); });\n            }\n            else {\n                quietFns.forEach(fn => fn());\n            }\n        }\n    }\n    /**\n     * @param {?} errors\n     * @return {?}\n     */\n    reportError(errors) {\n        throw new Error(`Unable to process animations due to the following failed trigger transitions\\n ${errors.join('\\n')}`);\n    }\n    /**\n     * @param {?} cleanupFns\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    _flushAnimations(cleanupFns, microtaskId) {\n        const /** @type {?} */ subTimelines = new ElementInstructionMap();\n        const /** @type {?} */ skippedPlayers = [];\n        const /** @type {?} */ skippedPlayersMap = new Map();\n        const /** @type {?} */ queuedInstructions = [];\n        const /** @type {?} */ queriedElements = new Map();\n        const /** @type {?} */ allPreStyleElements = new Map();\n        const /** @type {?} */ allPostStyleElements = new Map();\n        const /** @type {?} */ disabledElementsSet = new Set();\n        this.disabledNodes.forEach(node => {\n            disabledElementsSet.add(node);\n            const /** @type {?} */ nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n            for (let /** @type {?} */ i = 0; i < nodesThatAreDisabled.length; i++) {\n                disabledElementsSet.add(nodesThatAreDisabled[i]);\n            }\n        });\n        const /** @type {?} */ bodyNode = getBodyNode();\n        const /** @type {?} */ allTriggerElements = Array.from(this.statesByElement.keys());\n        const /** @type {?} */ enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n        // this must occur before the instructions are built below such that\n        // the :enter queries match the elements (since the timeline queries\n        // are fired during instruction building).\n        const /** @type {?} */ enterNodeMapIds = new Map();\n        let /** @type {?} */ i = 0;\n        enterNodeMap.forEach((nodes, root) => {\n            const /** @type {?} */ className = ENTER_CLASSNAME + i++;\n            enterNodeMapIds.set(root, className);\n            nodes.forEach(node => addClass(node, className));\n        });\n        const /** @type {?} */ allLeaveNodes = [];\n        const /** @type {?} */ mergedLeaveNodes = new Set();\n        const /** @type {?} */ leaveNodesWithoutAnimations = new Set();\n        for (let /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n            const /** @type {?} */ element = this.collectedLeaveElements[i];\n            const /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n            if (details && details.setForRemoval) {\n                allLeaveNodes.push(element);\n                mergedLeaveNodes.add(element);\n                if (details.hasAnimation) {\n                    this.driver.query(element, STAR_SELECTOR, true).forEach(elm => mergedLeaveNodes.add(elm));\n                }\n                else {\n                    leaveNodesWithoutAnimations.add(element);\n                }\n            }\n        }\n        const /** @type {?} */ leaveNodeMapIds = new Map();\n        const /** @type {?} */ leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n        leaveNodeMap.forEach((nodes, root) => {\n            const /** @type {?} */ className = LEAVE_CLASSNAME + i++;\n            leaveNodeMapIds.set(root, className);\n            nodes.forEach(node => addClass(node, className));\n        });\n        cleanupFns.push(() => {\n            enterNodeMap.forEach((nodes, root) => {\n                const /** @type {?} */ className = /** @type {?} */ ((enterNodeMapIds.get(root)));\n                nodes.forEach(node => removeClass(node, className));\n            });\n            leaveNodeMap.forEach((nodes, root) => {\n                const /** @type {?} */ className = /** @type {?} */ ((leaveNodeMapIds.get(root)));\n                nodes.forEach(node => removeClass(node, className));\n            });\n            allLeaveNodes.forEach(element => { this.processLeaveNode(element); });\n        });\n        const /** @type {?} */ allPlayers = [];\n        const /** @type {?} */ erroneousTransitions = [];\n        for (let /** @type {?} */ i = this._namespaceList.length - 1; i >= 0; i--) {\n            const /** @type {?} */ ns = this._namespaceList[i];\n            ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n                const /** @type {?} */ player = entry.player;\n                allPlayers.push(player);\n                const /** @type {?} */ element = entry.element;\n                if (!bodyNode || !this.driver.containsElement(bodyNode, element)) {\n                    player.destroy();\n                    return;\n                }\n                const /** @type {?} */ leaveClassName = /** @type {?} */ ((leaveNodeMapIds.get(element)));\n                const /** @type {?} */ enterClassName = /** @type {?} */ ((enterNodeMapIds.get(element)));\n                const /** @type {?} */ instruction = /** @type {?} */ ((this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName)));\n                if (instruction.errors && instruction.errors.length) {\n                    erroneousTransitions.push(instruction);\n                    return;\n                }\n                // if a unmatched transition is queued to go then it SHOULD NOT render\n                // an animation and cancel the previously running animations.\n                if (entry.isFallbackTransition) {\n                    player.onStart(() => eraseStyles(element, instruction.fromStyles));\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this means that if a parent animation uses this animation as a sub trigger\n                // then it will instruct the timeline builder to not add a player delay, but\n                // instead stretch the first keyframe gap up until the animation starts. The\n                // reason this is important is to prevent extra initialization styles from being\n                // required by the user in the animation.\n                instruction.timelines.forEach(tl => tl.stretchStartingKeyframe = true);\n                subTimelines.append(element, instruction.timelines);\n                const /** @type {?} */ tuple = { instruction, player, element };\n                queuedInstructions.push(tuple);\n                instruction.queriedElements.forEach(element => getOrSetAsInMap(queriedElements, element, []).push(player));\n                instruction.preStyleProps.forEach((stringMap, element) => {\n                    const /** @type {?} */ props = Object.keys(stringMap);\n                    if (props.length) {\n                        let /** @type {?} */ setVal = /** @type {?} */ ((allPreStyleElements.get(element)));\n                        if (!setVal) {\n                            allPreStyleElements.set(element, setVal = new Set());\n                        }\n                        props.forEach(prop => setVal.add(prop));\n                    }\n                });\n                instruction.postStyleProps.forEach((stringMap, element) => {\n                    const /** @type {?} */ props = Object.keys(stringMap);\n                    let /** @type {?} */ setVal = /** @type {?} */ ((allPostStyleElements.get(element)));\n                    if (!setVal) {\n                        allPostStyleElements.set(element, setVal = new Set());\n                    }\n                    props.forEach(prop => setVal.add(prop));\n                });\n            });\n        }\n        if (erroneousTransitions.length) {\n            const /** @type {?} */ errors = [];\n            erroneousTransitions.forEach(instruction => {\n                errors.push(`@${instruction.triggerName} has failed due to:\\n`); /** @type {?} */\n                ((instruction.errors)).forEach(error => errors.push(`- ${error}\\n`));\n            });\n            allPlayers.forEach(player => player.destroy());\n            this.reportError(errors);\n        }\n        const /** @type {?} */ allPreviousPlayersMap = new Map();\n        // this map works to tell which element in the DOM tree is contained by\n        // which animation. Further down below this map will get populated once\n        // the players are built and in doing so it can efficiently figure out\n        // if a sub player is skipped due to a parent player having priority.\n        const /** @type {?} */ animationElementMap = new Map();\n        queuedInstructions.forEach(entry => {\n            const /** @type {?} */ element = entry.element;\n            if (subTimelines.has(element)) {\n                animationElementMap.set(element, element);\n                this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n            }\n        });\n        skippedPlayers.forEach(player => {\n            const /** @type {?} */ element = player.element;\n            const /** @type {?} */ previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n            previousPlayers.forEach(prevPlayer => {\n                getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer);\n                prevPlayer.destroy();\n            });\n        });\n        // this is a special case for nodes that will be removed (either by)\n        // having their own leave animations or by being queried in a container\n        // that will be removed once a parent animation is complete. The idea\n        // here is that * styles must be identical to ! styles because of\n        // backwards compatibility (* is also filled in by default in many places).\n        // Otherwise * styles will return an empty value or auto since the element\n        // that is being getComputedStyle'd will not be visible (since * = destination)\n        const /** @type {?} */ replaceNodes = allLeaveNodes.filter(node => {\n            return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n        });\n        // POST STAGE: fill the * styles\n        const /** @type {?} */ postStylesMap = new Map();\n        const /** @type {?} */ allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n        allLeaveQueriedNodes.forEach(node => {\n            if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n                replaceNodes.push(node);\n            }\n        });\n        // PRE STAGE: fill the ! styles\n        const /** @type {?} */ preStylesMap = new Map();\n        enterNodeMap.forEach((nodes, root) => {\n            cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, PRE_STYLE);\n        });\n        replaceNodes.forEach(node => {\n            const /** @type {?} */ post = postStylesMap.get(node);\n            const /** @type {?} */ pre = preStylesMap.get(node);\n            postStylesMap.set(node, /** @type {?} */ (Object.assign({}, post, pre)));\n        });\n        const /** @type {?} */ rootPlayers = [];\n        const /** @type {?} */ subPlayers = [];\n        const /** @type {?} */ NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n        queuedInstructions.forEach(entry => {\n            const { element, player, instruction } = entry;\n            // this means that it was never consumed by a parent animation which\n            // means that it is independent and therefore should be set for animation\n            if (subTimelines.has(element)) {\n                if (disabledElementsSet.has(element)) {\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this will flow up the DOM and query the map to figure out\n                // if a parent animation has priority over it. In the situation\n                // that a parent is detected then it will cancel the loop. If\n                // nothing is detected, or it takes a few hops to find a parent,\n                // then it will fill in the missing nodes and signal them as having\n                // a detected parent (or a NO_PARENT value via a special constant).\n                let /** @type {?} */ parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n                if (animationElementMap.size > 1) {\n                    let /** @type {?} */ elm = element;\n                    const /** @type {?} */ parentsToAdd = [];\n                    while (elm = elm.parentNode) {\n                        const /** @type {?} */ detectedParent = animationElementMap.get(elm);\n                        if (detectedParent) {\n                            parentWithAnimation = detectedParent;\n                            break;\n                        }\n                        parentsToAdd.push(elm);\n                    }\n                    parentsToAdd.forEach(parent => animationElementMap.set(parent, parentWithAnimation));\n                }\n                const /** @type {?} */ innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n                player.setRealPlayer(innerPlayer);\n                if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n                    rootPlayers.push(player);\n                }\n                else {\n                    const /** @type {?} */ parentPlayers = this.playersByElement.get(parentWithAnimation);\n                    if (parentPlayers && parentPlayers.length) {\n                        player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n                    }\n                    skippedPlayers.push(player);\n                }\n            }\n            else {\n                eraseStyles(element, instruction.fromStyles);\n                player.onDestroy(() => setStyles(element, instruction.toStyles));\n                // there still might be a ancestor player animating this\n                // element therefore we will still add it as a sub player\n                // even if its animation may be disabled\n                subPlayers.push(player);\n                if (disabledElementsSet.has(element)) {\n                    skippedPlayers.push(player);\n                }\n            }\n        });\n        // find all of the sub players' corresponding inner animation player\n        subPlayers.forEach(player => {\n            // even if any players are not found for a sub animation then it\n            // will still complete itself after the next tick since it's Noop\n            const /** @type {?} */ playersForElement = skippedPlayersMap.get(player.element);\n            if (playersForElement && playersForElement.length) {\n                const /** @type {?} */ innerPlayer = optimizeGroupPlayer(playersForElement);\n                player.setRealPlayer(innerPlayer);\n            }\n        });\n        // the reason why we don't actually play the animation is\n        // because all that a skipped player is designed to do is to\n        // fire the start/done transition callback events\n        skippedPlayers.forEach(player => {\n            if (player.parentPlayer) {\n                player.syncPlayerEvents(player.parentPlayer);\n            }\n            else {\n                player.destroy();\n            }\n        });\n        // run through all of the queued removals and see if they\n        // were picked up by a query. If not then perform the removal\n        // operation right away unless a parent animation is ongoing.\n        for (let /** @type {?} */ i = 0; i < allLeaveNodes.length; i++) {\n            const /** @type {?} */ element = allLeaveNodes[i];\n            const /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n            removeClass(element, LEAVE_CLASSNAME);\n            // this means the element has a removal animation that is being\n            // taken care of and therefore the inner elements will hang around\n            // until that animation is over (or the parent queried animation)\n            if (details && details.hasAnimation)\n                continue;\n            let /** @type {?} */ players = [];\n            // if this element is queried or if it contains queried children\n            // then we want for the element not to be removed from the page\n            // until the queried animations have finished\n            if (queriedElements.size) {\n                let /** @type {?} */ queriedPlayerResults = queriedElements.get(element);\n                if (queriedPlayerResults && queriedPlayerResults.length) {\n                    players.push(...queriedPlayerResults);\n                }\n                let /** @type {?} */ queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n                for (let /** @type {?} */ j = 0; j < queriedInnerElements.length; j++) {\n                    let /** @type {?} */ queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n                    if (queriedPlayers && queriedPlayers.length) {\n                        players.push(...queriedPlayers);\n                    }\n                }\n            }\n            const /** @type {?} */ activePlayers = players.filter(p => !p.destroyed);\n            if (activePlayers.length) {\n                removeNodesAfterAnimationDone(this, element, activePlayers);\n            }\n            else {\n                this.processLeaveNode(element);\n            }\n        }\n        // this is required so the cleanup method doesn't remove them\n        allLeaveNodes.length = 0;\n        rootPlayers.forEach(player => {\n            this.players.push(player);\n            player.onDone(() => {\n                player.destroy();\n                const /** @type {?} */ index = this.players.indexOf(player);\n                this.players.splice(index, 1);\n            });\n            player.play();\n        });\n        return rootPlayers;\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @return {?}\n     */\n    elementContainsData(namespaceId, element) {\n        let /** @type {?} */ containsData = false;\n        const /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval)\n            containsData = true;\n        if (this.playersByElement.has(element))\n            containsData = true;\n        if (this.playersByQueriedElement.has(element))\n            containsData = true;\n        if (this.statesByElement.has(element))\n            containsData = true;\n        return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n    }\n    /**\n     * @param {?} callback\n     * @return {?}\n     */\n    afterFlush(callback) { this._flushFns.push(callback); }\n    /**\n     * @param {?} callback\n     * @return {?}\n     */\n    afterFlushAnimationsDone(callback) { this._whenQuietFns.push(callback); }\n    /**\n     * @param {?} element\n     * @param {?} isQueriedElement\n     * @param {?=} namespaceId\n     * @param {?=} triggerName\n     * @param {?=} toStateValue\n     * @return {?}\n     */\n    _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n        let /** @type {?} */ players = [];\n        if (isQueriedElement) {\n            const /** @type {?} */ queriedElementPlayers = this.playersByQueriedElement.get(element);\n            if (queriedElementPlayers) {\n                players = queriedElementPlayers;\n            }\n        }\n        else {\n            const /** @type {?} */ elementPlayers = this.playersByElement.get(element);\n            if (elementPlayers) {\n                const /** @type {?} */ isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n                elementPlayers.forEach(player => {\n                    if (player.queued)\n                        return;\n                    if (!isRemovalAnimation && player.triggerName != triggerName)\n                        return;\n                    players.push(player);\n                });\n            }\n        }\n        if (namespaceId || triggerName) {\n            players = players.filter(player => {\n                if (namespaceId && namespaceId != player.namespaceId)\n                    return false;\n                if (triggerName && triggerName != player.triggerName)\n                    return false;\n                return true;\n            });\n        }\n        return players;\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @return {?}\n     */\n    _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n        const /** @type {?} */ triggerName = instruction.triggerName;\n        const /** @type {?} */ rootElement = instruction.element;\n        // when a removal animation occurs, ALL previous players are collected\n        // and destroyed (even if they are outside of the current namespace)\n        const /** @type {?} */ targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n        const /** @type {?} */ targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n        for (const /** @type {?} */ timelineInstruction of instruction.timelines) {\n            const /** @type {?} */ element = timelineInstruction.element;\n            const /** @type {?} */ isQueriedElement = element !== rootElement;\n            const /** @type {?} */ players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n            const /** @type {?} */ previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n            previousPlayers.forEach(player => {\n                const /** @type {?} */ realPlayer = /** @type {?} */ (player.getRealPlayer());\n                if (realPlayer.beforeDestroy) {\n                    realPlayer.beforeDestroy();\n                }\n                player.destroy();\n                players.push(player);\n            });\n        }\n        // this needs to be done so that the PRE/POST styles can be\n        // computed properly without interfering with the previous animation\n        eraseStyles(rootElement, instruction.fromStyles);\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @param {?} skippedPlayersMap\n     * @param {?} preStylesMap\n     * @param {?} postStylesMap\n     * @return {?}\n     */\n    _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n        const /** @type {?} */ triggerName = instruction.triggerName;\n        const /** @type {?} */ rootElement = instruction.element;\n        // we first run this so that the previous animation player\n        // data can be passed into the successive animation players\n        const /** @type {?} */ allQueriedPlayers = [];\n        const /** @type {?} */ allConsumedElements = new Set();\n        const /** @type {?} */ allSubElements = new Set();\n        const /** @type {?} */ allNewPlayers = instruction.timelines.map(timelineInstruction => {\n            const /** @type {?} */ element = timelineInstruction.element;\n            allConsumedElements.add(element);\n            // FIXME (matsko): make sure to-be-removed animations are removed properly\n            const /** @type {?} */ details = element[REMOVAL_FLAG];\n            if (details && details.removedBeforeQueried)\n                return new NoopAnimationPlayer();\n            const /** @type {?} */ isQueriedElement = element !== rootElement;\n            const /** @type {?} */ previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY)\n                .map(p => p.getRealPlayer()))\n                .filter(p => {\n                // the `element` is not apart of the AnimationPlayer definition, but\n                // Mock/WebAnimations\n                // use the element within their implementation. This will be added in Angular5 to\n                // AnimationPlayer\n                const /** @type {?} */ pp = /** @type {?} */ (p);\n                return pp.element ? pp.element === element : false;\n            });\n            const /** @type {?} */ preStyles = preStylesMap.get(element);\n            const /** @type {?} */ postStyles = postStylesMap.get(element);\n            const /** @type {?} */ keyframes = normalizeKeyframes(this.driver, this._normalizer, element, timelineInstruction.keyframes, preStyles, postStyles);\n            const /** @type {?} */ player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n            // this means that this particular player belongs to a sub trigger. It is\n            // important that we match this player up with the corresponding (@trigger.listener)\n            if (timelineInstruction.subTimeline && skippedPlayersMap) {\n                allSubElements.add(element);\n            }\n            if (isQueriedElement) {\n                const /** @type {?} */ wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n                wrappedPlayer.setRealPlayer(player);\n                allQueriedPlayers.push(wrappedPlayer);\n            }\n            return player;\n        });\n        allQueriedPlayers.forEach(player => {\n            getOrSetAsInMap(this.playersByQueriedElement, player.element, []).push(player);\n            player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n        });\n        allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n        const /** @type {?} */ player = optimizeGroupPlayer(allNewPlayers);\n        player.onDestroy(() => {\n            allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n            setStyles(rootElement, instruction.toStyles);\n        });\n        // this basically makes all of the callbacks for sub element animations\n        // be dependent on the upper players for when they finish\n        allSubElements.forEach(element => { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n        return player;\n    }\n    /**\n     * @param {?} instruction\n     * @param {?} keyframes\n     * @param {?} previousPlayers\n     * @return {?}\n     */\n    _buildPlayer(instruction, keyframes, previousPlayers) {\n        if (keyframes.length > 0) {\n            return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n        }\n        // special case for when an empty transition|definition is provided\n        // ... there is no point in rendering an empty animation\n        return new NoopAnimationPlayer();\n    }\n}\nfunction TransitionAnimationEngine_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.players;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.newHostElements;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.playersByElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.playersByQueriedElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.statesByElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.disabledNodes;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.totalAnimations;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.totalQueuedPlayers;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._namespaceLookup;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._namespaceList;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._flushFns;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._whenQuietFns;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.namespacesByHostElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.collectedEnterElements;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.collectedLeaveElements;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.onRemovalComplete;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.driver;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._normalizer;\n}\nexport class TransitionAnimationPlayer {\n    /**\n     * @param {?} namespaceId\n     * @param {?} triggerName\n     * @param {?} element\n     */\n    constructor(namespaceId, triggerName, element) {\n        this.namespaceId = namespaceId;\n        this.triggerName = triggerName;\n        this.element = element;\n        this._player = new NoopAnimationPlayer();\n        this._containsRealPlayer = false;\n        this._queuedCallbacks = {};\n        this.destroyed = false;\n        this.markedForDestroy = false;\n        this.queued = true;\n    }\n    /**\n     * @param {?} player\n     * @return {?}\n     */\n    setRealPlayer(player) {\n        if (this._containsRealPlayer)\n            return;\n        this._player = player;\n        Object.keys(this._queuedCallbacks).forEach(phase => {\n            this._queuedCallbacks[phase].forEach(callback => listenOnPlayer(player, phase, undefined, callback));\n        });\n        this._queuedCallbacks = {};\n        this._containsRealPlayer = true;\n        (/** @type {?} */ (this)).queued = false;\n    }\n    /**\n     * @return {?}\n     */\n    getRealPlayer() { return this._player; }\n    /**\n     * @param {?} player\n     * @return {?}\n     */\n    syncPlayerEvents(player) {\n        const /** @type {?} */ p = /** @type {?} */ (this._player);\n        if (p.triggerCallback) {\n            player.onStart(() => p.triggerCallback('start'));\n        }\n        player.onDone(() => this.finish());\n        player.onDestroy(() => this.destroy());\n    }\n    /**\n     * @param {?} name\n     * @param {?} callback\n     * @return {?}\n     */\n    _queueEvent(name, callback) {\n        getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    onDone(fn) {\n        if (this.queued) {\n            this._queueEvent('done', fn);\n        }\n        this._player.onDone(fn);\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    onStart(fn) {\n        if (this.queued) {\n            this._queueEvent('start', fn);\n        }\n        this._player.onStart(fn);\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    onDestroy(fn) {\n        if (this.queued) {\n            this._queueEvent('destroy', fn);\n        }\n        this._player.onDestroy(fn);\n    }\n    /**\n     * @return {?}\n     */\n    init() { this._player.init(); }\n    /**\n     * @return {?}\n     */\n    hasStarted() { return this.queued ? false : this._player.hasStarted(); }\n    /**\n     * @return {?}\n     */\n    play() { !this.queued && this._player.play(); }\n    /**\n     * @return {?}\n     */\n    pause() { !this.queued && this._player.pause(); }\n    /**\n     * @return {?}\n     */\n    restart() { !this.queued && this._player.restart(); }\n    /**\n     * @return {?}\n     */\n    finish() { this._player.finish(); }\n    /**\n     * @return {?}\n     */\n    destroy() {\n        (/** @type {?} */ (this)).destroyed = true;\n        this._player.destroy();\n    }\n    /**\n     * @return {?}\n     */\n    reset() { !this.queued && this._player.reset(); }\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    setPosition(p) {\n        if (!this.queued) {\n            this._player.setPosition(p);\n        }\n    }\n    /**\n     * @return {?}\n     */\n    getPosition() { return this.queued ? 0 : this._player.getPosition(); }\n    /**\n     * @return {?}\n     */\n    get totalTime() { return this._player.totalTime; }\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    triggerCallback(phaseName) {\n        const /** @type {?} */ p = /** @type {?} */ (this._player);\n        if (p.triggerCallback) {\n            p.triggerCallback(phaseName);\n        }\n    }\n}\nfunction TransitionAnimationPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype._player;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype._containsRealPlayer;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype._queuedCallbacks;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.destroyed;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.parentPlayer;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.markedForDestroy;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.queued;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.namespaceId;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.triggerName;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.element;\n}\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} value\n * @return {?}\n */\nfunction deleteOrUnsetInMap(map, key, value) {\n    let /** @type {?} */ currentValues;\n    if (map instanceof Map) {\n        currentValues = map.get(key);\n        if (currentValues) {\n            if (currentValues.length) {\n                const /** @type {?} */ index = currentValues.indexOf(value);\n                currentValues.splice(index, 1);\n            }\n            if (currentValues.length == 0) {\n                map.delete(key);\n            }\n        }\n    }\n    else {\n        currentValues = map[key];\n        if (currentValues) {\n            if (currentValues.length) {\n                const /** @type {?} */ index = currentValues.indexOf(value);\n                currentValues.splice(index, 1);\n            }\n            if (currentValues.length == 0) {\n                delete map[key];\n            }\n        }\n    }\n    return currentValues;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction normalizeTriggerValue(value) {\n    // we use `!= null` here because it's the most simple\n    // way to test against a \"falsy\" value without mixing\n    // in empty strings or a zero value. DO NOT OPTIMIZE.\n    return value != null ? value : null;\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction isElementNode(node) {\n    return node && node['nodeType'] === 1;\n}\n/**\n * @param {?} eventName\n * @return {?}\n */\nfunction isTriggerEventValid(eventName) {\n    return eventName == 'start' || eventName == 'done';\n}\n/**\n * @param {?} element\n * @param {?=} value\n * @return {?}\n */\nfunction cloakElement(element, value) {\n    const /** @type {?} */ oldValue = element.style.display;\n    element.style.display = value != null ? value : 'none';\n    return oldValue;\n}\n/**\n * @param {?} valuesMap\n * @param {?} driver\n * @param {?} elements\n * @param {?} elementPropsMap\n * @param {?} defaultStyle\n * @return {?}\n */\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n    const /** @type {?} */ cloakVals = [];\n    elements.forEach(element => cloakVals.push(cloakElement(element)));\n    const /** @type {?} */ failedElements = [];\n    elementPropsMap.forEach((props, element) => {\n        const /** @type {?} */ styles = {};\n        props.forEach(prop => {\n            const /** @type {?} */ value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n            // there is no easy way to detect this because a sub element could be removed\n            // by a parent animation element being detached.\n            if (!value || value.length == 0) {\n                element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n                failedElements.push(element);\n            }\n        });\n        valuesMap.set(element, styles);\n    });\n    // we use a index variable here since Set.forEach(a, i) does not return\n    // an index value for the closure (but instead just the value)\n    let /** @type {?} */ i = 0;\n    elements.forEach(element => cloakElement(element, cloakVals[i++]));\n    return failedElements;\n}\n/**\n * @param {?} roots\n * @param {?} nodes\n * @return {?}\n */\nfunction buildRootMap(roots, nodes) {\n    const /** @type {?} */ rootMap = new Map();\n    roots.forEach(root => rootMap.set(root, []));\n    if (nodes.length == 0)\n        return rootMap;\n    const /** @type {?} */ NULL_NODE = 1;\n    const /** @type {?} */ nodeSet = new Set(nodes);\n    const /** @type {?} */ localRootMap = new Map();\n    /**\n     * @param {?} node\n     * @return {?}\n     */\n    function getRoot(node) {\n        if (!node)\n            return NULL_NODE;\n        let /** @type {?} */ root = localRootMap.get(node);\n        if (root)\n            return root;\n        const /** @type {?} */ parent = node.parentNode;\n        if (rootMap.has(parent)) {\n            // ngIf inside @trigger\n            root = parent;\n        }\n        else if (nodeSet.has(parent)) {\n            // ngIf inside ngIf\n            root = NULL_NODE;\n        }\n        else {\n            // recurse upwards\n            root = getRoot(parent);\n        }\n        localRootMap.set(node, root);\n        return root;\n    }\n    nodes.forEach(node => {\n        const /** @type {?} */ root = getRoot(node);\n        if (root !== NULL_NODE) {\n            /** @type {?} */ ((rootMap.get(root))).push(node);\n        }\n    });\n    return rootMap;\n}\nconst /** @type {?} */ CLASSES_CACHE_KEY = '$$classes';\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction containsClass(element, className) {\n    if (element.classList) {\n        return element.classList.contains(className);\n    }\n    else {\n        const /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        return classes && classes[className];\n    }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction addClass(element, className) {\n    if (element.classList) {\n        element.classList.add(className);\n    }\n    else {\n        let /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        if (!classes) {\n            classes = element[CLASSES_CACHE_KEY] = {};\n        }\n        classes[className] = true;\n    }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction removeClass(element, className) {\n    if (element.classList) {\n        element.classList.remove(className);\n    }\n    else {\n        let /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        if (classes) {\n            delete classes[className];\n        }\n    }\n}\n/**\n * @param {?} engine\n * @param {?} element\n * @param {?} players\n * @return {?}\n */\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n    optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n/**\n * @param {?} players\n * @return {?}\n */\nfunction flattenGroupPlayers(players) {\n    const /** @type {?} */ finalPlayers = [];\n    _flattenGroupPlayersRecur(players, finalPlayers);\n    return finalPlayers;\n}\n/**\n * @param {?} players\n * @param {?} finalPlayers\n * @return {?}\n */\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n    for (let /** @type {?} */ i = 0; i < players.length; i++) {\n        const /** @type {?} */ player = players[i];\n        if (player instanceof AnimationGroupPlayer) {\n            _flattenGroupPlayersRecur(player.players, finalPlayers);\n        }\n        else {\n            finalPlayers.push(/** @type {?} */ (player));\n        }\n    }\n}\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nfunction objEquals(a, b) {\n    const /** @type {?} */ k1 = Object.keys(a);\n    const /** @type {?} */ k2 = Object.keys(b);\n    if (k1.length != k2.length)\n        return false;\n    for (let /** @type {?} */ i = 0; i < k1.length; i++) {\n        const /** @type {?} */ prop = k1[i];\n        if (!b.hasOwnProperty(prop) || a[prop] !== b[prop])\n            return false;\n    }\n    return true;\n}\n/**\n * @param {?} element\n * @param {?} allPreStyleElements\n * @param {?} allPostStyleElements\n * @return {?}\n */\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n    const /** @type {?} */ postEntry = allPostStyleElements.get(element);\n    if (!postEntry)\n        return false;\n    let /** @type {?} */ preEntry = allPreStyleElements.get(element);\n    if (preEntry) {\n        postEntry.forEach(data => /** @type {?} */ ((preEntry)).add(data));\n    }\n    else {\n        allPreStyleElements.set(element, postEntry);\n    }\n    allPostStyleElements.delete(element);\n    return true;\n}\n//# sourceMappingURL=transition_animation_engine.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { buildAnimationAst } from '../dsl/animation_ast_builder';\nimport { buildTrigger } from '../dsl/animation_trigger';\nimport { parseTimelineCommand } from './shared';\nimport { TimelineAnimationEngine } from './timeline_animation_engine';\nimport { TransitionAnimationEngine } from './transition_animation_engine';\nexport class AnimationEngine {\n    /**\n     * @param {?} _driver\n     * @param {?} normalizer\n     */\n    constructor(_driver, normalizer) {\n        this._driver = _driver;\n        this._triggerCache = {};\n        this.onRemovalComplete = (element, context) => { };\n        this._transitionEngine = new TransitionAnimationEngine(_driver, normalizer);\n        this._timelineEngine = new TimelineAnimationEngine(_driver, normalizer);\n        this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n    }\n    /**\n     * @param {?} componentId\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @param {?} name\n     * @param {?} metadata\n     * @return {?}\n     */\n    registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n        const /** @type {?} */ cacheKey = componentId + '-' + name;\n        let /** @type {?} */ trigger = this._triggerCache[cacheKey];\n        if (!trigger) {\n            const /** @type {?} */ errors = [];\n            const /** @type {?} */ ast = /** @type {?} */ (buildAnimationAst(this._driver, /** @type {?} */ (metadata), errors));\n            if (errors.length) {\n                throw new Error(`The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.join(\"\\n - \")}`);\n            }\n            trigger = buildTrigger(name, ast);\n            this._triggerCache[cacheKey] = trigger;\n        }\n        this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    register(namespaceId, hostElement) {\n        this._transitionEngine.register(namespaceId, hostElement);\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    destroy(namespaceId, context) {\n        this._transitionEngine.destroy(namespaceId, context);\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    onInsert(namespaceId, element, parent, insertBefore) {\n        this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    onRemove(namespaceId, element, context) {\n        this._transitionEngine.removeNode(namespaceId, element, context);\n    }\n    /**\n     * @param {?} element\n     * @param {?} disable\n     * @return {?}\n     */\n    disableAnimations(element, disable) {\n        this._transitionEngine.markElementAsDisabled(element, disable);\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} property\n     * @param {?} value\n     * @return {?}\n     */\n    process(namespaceId, element, property, value) {\n        if (property.charAt(0) == '@') {\n            const [id, action] = parseTimelineCommand(property);\n            const /** @type {?} */ args = /** @type {?} */ (value);\n            this._timelineEngine.command(id, element, action, args);\n        }\n        else {\n            this._transitionEngine.trigger(namespaceId, element, property, value);\n        }\n    }\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} eventPhase\n     * @param {?} callback\n     * @return {?}\n     */\n    listen(namespaceId, element, eventName, eventPhase, callback) {\n        // @@listen\n        if (eventName.charAt(0) == '@') {\n            const [id, action] = parseTimelineCommand(eventName);\n            return this._timelineEngine.listen(id, element, action, callback);\n        }\n        return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n    }\n    /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    flush(microtaskId = -1) { this._transitionEngine.flush(microtaskId); }\n    /**\n     * @return {?}\n     */\n    get players() {\n        return (/** @type {?} */ (this._transitionEngine.players))\n            .concat(/** @type {?} */ (this._timelineEngine.players));\n    }\n    /**\n     * @return {?}\n     */\n    whenRenderingDone() { return this._transitionEngine.whenRenderingDone(); }\n}\nfunction AnimationEngine_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationEngine.prototype._transitionEngine;\n    /** @type {?} */\n    AnimationEngine.prototype._timelineEngine;\n    /** @type {?} */\n    AnimationEngine.prototype._triggerCache;\n    /** @type {?} */\n    AnimationEngine.prototype.onRemovalComplete;\n    /** @type {?} */\n    AnimationEngine.prototype._driver;\n}\n//# sourceMappingURL=animation_engine_next.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { allowPreviousPlayerStylesMerge, copyStyles } from '../../util';\nexport class WebAnimationsPlayer {\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} options\n     * @param {?=} previousPlayers\n     */\n    constructor(element, keyframes, options, previousPlayers = []) {\n        this.element = element;\n        this.keyframes = keyframes;\n        this.options = options;\n        this.previousPlayers = previousPlayers;\n        this._onDoneFns = [];\n        this._onStartFns = [];\n        this._onDestroyFns = [];\n        this._initialized = false;\n        this._finished = false;\n        this._started = false;\n        this._destroyed = false;\n        this.time = 0;\n        this.parentPlayer = null;\n        this.previousStyles = {};\n        this.currentSnapshot = {};\n        this._duration = /** @type {?} */ (options['duration']);\n        this._delay = /** @type {?} */ (options['delay']) || 0;\n        this.time = this._duration + this._delay;\n        if (allowPreviousPlayerStylesMerge(this._duration, this._delay)) {\n            previousPlayers.forEach(player => {\n                let /** @type {?} */ styles = player.currentSnapshot;\n                Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n            });\n        }\n    }\n    /**\n     * @return {?}\n     */\n    _onFinish() {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach(fn => fn());\n            this._onDoneFns = [];\n        }\n    }\n    /**\n     * @return {?}\n     */\n    init() {\n        this._buildPlayer();\n        this._preparePlayerBeforeStart();\n    }\n    /**\n     * @return {?}\n     */\n    _buildPlayer() {\n        if (this._initialized)\n            return;\n        this._initialized = true;\n        const /** @type {?} */ keyframes = this.keyframes.map(styles => copyStyles(styles, false));\n        const /** @type {?} */ previousStyleProps = Object.keys(this.previousStyles);\n        if (previousStyleProps.length && keyframes.length) {\n            let /** @type {?} */ startingKeyframe = keyframes[0];\n            let /** @type {?} */ missingStyleProps = [];\n            previousStyleProps.forEach(prop => {\n                if (!startingKeyframe.hasOwnProperty(prop)) {\n                    missingStyleProps.push(prop);\n                }\n                startingKeyframe[prop] = this.previousStyles[prop];\n            });\n            if (missingStyleProps.length) {\n                const /** @type {?} */ self = this;\n                // tslint:disable-next-line\n                for (var /** @type {?} */ i = 1; i < keyframes.length; i++) {\n                    let /** @type {?} */ kf = keyframes[i];\n                    missingStyleProps.forEach(function (prop) {\n                        kf[prop] = _computeStyle(self.element, prop);\n                    });\n                }\n            }\n        }\n        (/** @type {?} */ (this)).domPlayer =\n            this._triggerWebAnimation(this.element, keyframes, this.options);\n        this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n        this.domPlayer.addEventListener('finish', () => this._onFinish());\n    }\n    /**\n     * @return {?}\n     */\n    _preparePlayerBeforeStart() {\n        // this is required so that the player doesn't start to animate right away\n        if (this._delay) {\n            this._resetDomPlayerState();\n        }\n        else {\n            this.domPlayer.pause();\n        }\n    }\n    /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} options\n     * @return {?}\n     */\n    _triggerWebAnimation(element, keyframes, options) {\n        // jscompiler doesn't seem to know animate is a native property because it's not fully\n        // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n        return /** @type {?} */ (element['animate'](keyframes, options));\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    onStart(fn) { this._onStartFns.push(fn); }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    onDone(fn) { this._onDoneFns.push(fn); }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    onDestroy(fn) { this._onDestroyFns.push(fn); }\n    /**\n     * @return {?}\n     */\n    play() {\n        this._buildPlayer();\n        if (!this.hasStarted()) {\n            this._onStartFns.forEach(fn => fn());\n            this._onStartFns = [];\n            this._started = true;\n        }\n        this.domPlayer.play();\n    }\n    /**\n     * @return {?}\n     */\n    pause() {\n        this.init();\n        this.domPlayer.pause();\n    }\n    /**\n     * @return {?}\n     */\n    finish() {\n        this.init();\n        this._onFinish();\n        this.domPlayer.finish();\n    }\n    /**\n     * @return {?}\n     */\n    reset() {\n        this._resetDomPlayerState();\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n    }\n    /**\n     * @return {?}\n     */\n    _resetDomPlayerState() {\n        if (this.domPlayer) {\n            this.domPlayer.cancel();\n        }\n    }\n    /**\n     * @return {?}\n     */\n    restart() {\n        this.reset();\n        this.play();\n    }\n    /**\n     * @return {?}\n     */\n    hasStarted() { return this._started; }\n    /**\n     * @return {?}\n     */\n    destroy() {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._resetDomPlayerState();\n            this._onFinish();\n            this._onDestroyFns.forEach(fn => fn());\n            this._onDestroyFns = [];\n        }\n    }\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    setPosition(p) { this.domPlayer.currentTime = p * this.time; }\n    /**\n     * @return {?}\n     */\n    getPosition() { return this.domPlayer.currentTime / this.time; }\n    /**\n     * @return {?}\n     */\n    get totalTime() { return this._delay + this._duration; }\n    /**\n     * @return {?}\n     */\n    beforeDestroy() {\n        const /** @type {?} */ styles = {};\n        if (this.hasStarted()) {\n            Object.keys(this._finalKeyframe).forEach(prop => {\n                if (prop != 'offset') {\n                    styles[prop] =\n                        this._finished ? this._finalKeyframe[prop] : _computeStyle(this.element, prop);\n                }\n            });\n        }\n        this.currentSnapshot = styles;\n    }\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    triggerCallback(phaseName) {\n        const /** @type {?} */ methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach(fn => fn());\n        methods.length = 0;\n    }\n}\nfunction WebAnimationsPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._onDoneFns;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._onStartFns;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._onDestroyFns;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._duration;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._delay;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._initialized;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._finished;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._started;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._destroyed;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._finalKeyframe;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.domPlayer;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.time;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.parentPlayer;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.previousStyles;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.currentSnapshot;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.element;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.keyframes;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.options;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.previousPlayers;\n}\n/**\n * @param {?} element\n * @param {?} prop\n * @return {?}\n */\nfunction _computeStyle(element, prop) {\n    return (/** @type {?} */ (window.getComputedStyle(element)))[prop];\n}\n//# sourceMappingURL=web_animations_player.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { containsElement, invokeQuery, matchesElement, validateStyleProperty } from '../shared';\nimport { WebAnimationsPlayer } from './web_animations_player';\nexport class WebAnimationsDriver {\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    validateStyleProperty(prop) { return validateStyleProperty(prop); }\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    matchesElement(element, selector) {\n        return matchesElement(element, selector);\n    }\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    containsElement(elm1, elm2) { return containsElement(elm1, elm2); }\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    query(element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    }\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    computeStyle(element, prop, defaultValue) {\n        return /** @type {?} */ ((/** @type {?} */ (window.getComputedStyle(element)))[prop]);\n    }\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n        const /** @type {?} */ fill = delay == 0 ? 'both' : 'forwards';\n        const /** @type {?} */ playerOptions = { duration, delay, fill };\n        // we check for this to avoid having a null|undefined value be present\n        // for the easing (which results in an error for certain browsers #9752)\n        if (easing) {\n            playerOptions['easing'] = easing;\n        }\n        const /** @type {?} */ previousWebAnimationPlayers = /** @type {?} */ (previousPlayers.filter(player => { return player instanceof WebAnimationsPlayer; }));\n        return new WebAnimationsPlayer(element, keyframes, playerOptions, previousWebAnimationPlayers);\n    }\n}\n/**\n * @return {?}\n */\nexport function supportsWebAnimations() {\n    return typeof Element !== 'undefined' && typeof (/** @type {?} */ (Element)).prototype['animate'] === 'function';\n}\n//# sourceMappingURL=web_animations_driver.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nexport { Animation as ɵAnimation } from './dsl/animation';\nexport { AnimationStyleNormalizer as ɵAnimationStyleNormalizer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer } from './dsl/style_normalization/animation_style_normalizer';\nexport { WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer } from './dsl/style_normalization/web_animations_style_normalizer';\nexport { NoopAnimationDriver as ɵNoopAnimationDriver } from './render/animation_driver';\nexport { AnimationEngine as ɵAnimationEngine } from './render/animation_engine_next';\nexport { WebAnimationsDriver as ɵWebAnimationsDriver, supportsWebAnimations as ɵsupportsWebAnimations } from './render/web_animations/web_animations_driver';\nexport { WebAnimationsPlayer as ɵWebAnimationsPlayer } from './render/web_animations/web_animations_player';\n//# sourceMappingURL=private_export.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport { AnimationDriver } from './render/animation_driver';\nexport { ɵAnimation, ɵAnimationStyleNormalizer, ɵNoopAnimationStyleNormalizer, ɵWebAnimationsStyleNormalizer, ɵNoopAnimationDriver, ɵAnimationEngine, ɵWebAnimationsDriver, ɵsupportsWebAnimations, ɵWebAnimationsPlayer } from './private_export';\n//# sourceMappingURL=browser.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport { AnimationDriver, ɵAnimation, ɵAnimationStyleNormalizer, ɵNoopAnimationStyleNormalizer, ɵWebAnimationsStyleNormalizer, ɵNoopAnimationDriver, ɵAnimationEngine, ɵWebAnimationsDriver, ɵsupportsWebAnimations, ɵWebAnimationsPlayer } from './src/browser';\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { AnimationDriver, ɵAnimation, ɵAnimationStyleNormalizer, ɵNoopAnimationStyleNormalizer, ɵWebAnimationsStyleNormalizer, ɵNoopAnimationDriver, ɵAnimationEngine, ɵWebAnimationsDriver, ɵsupportsWebAnimations, ɵWebAnimationsPlayer } from './public_api';\n//# sourceMappingURL=browser.js.map"], "names": ["PRE_STYLE", "style", "AnimationGroupPlayer"], "mappings": ";;;;;;;AAAA;;;;AAIA,AACA;;;;AAIA,AAAO,SAAS,mBAAmB,CAAC,OAAO,EAAE;IACzC,QAAQ,OAAO,CAAC,MAAM;QAClB,KAAK,CAAC;YACF,OAAO,IAAI,mBAAmB,EAAE,CAAC;QACrC,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;QACtB;YACI,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;KACjD;CACJ;;;;;;;;;;AAUD,AAAO,SAAS,kBAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,GAAG,EAAE,EAAE,UAAU,GAAG,EAAE,EAAE;IACxG,uBAAuB,MAAM,GAAG,EAAE,CAAC;IACnC,uBAAuB,mBAAmB,GAAG,EAAE,CAAC;IAChD,qBAAqB,cAAc,GAAG,CAAC,CAAC,CAAC;IACzC,qBAAqB,gBAAgB,GAAG,IAAI,CAAC;IAC7C,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI;QACpB,uBAAuB,MAAM,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChE,uBAAuB,YAAY,GAAG,MAAM,IAAI,cAAc,CAAC;QAC/D,uBAAuB,kBAAkB,GAAG,CAAC,YAAY,IAAI,gBAAgB,KAAK,EAAE,CAAC;QACrF,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YAC5B,qBAAqB,cAAc,GAAG,IAAI,CAAC;YAC3C,qBAAqB,eAAe,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACnB,cAAc,GAAG,UAAU,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAC1E,QAAQ,eAAe;oBACnB,KAAKA,UAAS;wBACV,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM;oBACV,KAAK,UAAU;wBACX,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;wBACnC,MAAM;oBACV;wBACI,eAAe;4BACX,UAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;wBAClF,MAAM;iBACb;aACJ;YACD,kBAAkB,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC;SACxD,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE;YACf,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAChD;QACD,gBAAgB,GAAG,kBAAkB,CAAC;QACtC,cAAc,GAAG,MAAM,CAAC;KAC3B,CAAC,CAAC;IACH,IAAI,MAAM,CAAC,MAAM,EAAE;QACf,uBAAuB,UAAU,GAAG,OAAO,CAAC;QAC5C,MAAM,IAAI,KAAK,CAAC,CAAC,8CAA8C,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5G;IACD,OAAO,mBAAmB,CAAC;CAC9B;;;;;;;;AAQD,AAAO,SAAS,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC/D,QAAQ,SAAS;QACb,KAAK,OAAO;YACR,MAAM,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9F,MAAM;QACV,KAAK,MAAM;YACP,MAAM,CAAC,MAAM,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5F,MAAM;QACV,KAAK,SAAS;YACV,MAAM,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAClG,MAAM;KACb;CACJ;;;;;;;AAOD,AAAO,SAAS,kBAAkB,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE;IACxD,uBAAuB,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;IAChL,uBAAuB,IAAI,GAAG,mBAAmB,CAAC,GAAG,OAAO,CAAC,CAAC;IAC9D,IAAI,IAAI,IAAI,IAAI,EAAE;QACd,mBAAmB,KAAK,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC;CAChB;;;;;;;;;;AAUD,AAAO,SAAS,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,EAAE;IACxG,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;CAC7E;;;;;;;AAOD,AAAO,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE;IACpD,qBAAqB,KAAK,CAAC;IAC3B,IAAI,GAAG,YAAY,GAAG,EAAE;QACpB,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,KAAK,EAAE;YACR,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;SACtC;KACJ;SACI;QACD,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;SACnC;KACJ;IACD,OAAO,KAAK,CAAC;CAChB;;;;;AAKD,AAAO,SAAS,oBAAoB,CAAC,OAAO,EAAE;IAC1C,uBAAuB,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3D,uBAAuB,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IAC/D,uBAAuB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IACjE,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;CACvB;AACD,IAAqB,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,KAAK,CAAC;AACvD,AACA,IAAqB,QAAQ,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK,KAAK,CAAC;AAC7D,AACA,IAAqB,MAAM,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,KAAK;IACxD,OAAO,EAAE,CAAC;CACb,CAAC;AACF,AACA,IAAI,OAAO,OAAO,IAAI,WAAW,EAAE;;IAE/B,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,EAAE,yBAAyB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAC/E,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC3B,QAAQ,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC/D;SACI;QACD,uBAAuB,KAAK,qBAAqB,OAAO,CAAC,SAAS,CAAC,CAAC;QACpE,uBAAuB,EAAE,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,iBAAiB;YACpG,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,qBAAqB,CAAC;QAC1D,IAAI,EAAE,EAAE;YACJ,QAAQ,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;SACnE;KACJ;IACD,MAAM,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,KAAK;QACnC,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,KAAK,EAAE;YACP,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACvD;aACI;YACD,uBAAuB,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC7D,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrB;SACJ;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;CACL;;;;;AAKD,SAAS,oBAAoB,CAAC,IAAI,EAAE;;;IAGhC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC;CAC1C;AACD,IAAqB,YAAY,GAAG,IAAI,CAAC;AACzC,IAAqB,UAAU,GAAG,KAAK,CAAC;;;;;AAKxC,AAAO,SAAS,qBAAqB,CAAC,IAAI,EAAE;IACxC,IAAI,CAAC,YAAY,EAAE;QACf,YAAY,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC;QACnC,UAAU,oBAAoB,EAAE,YAAY,GAAG,KAAK,IAAI,kBAAkB,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,KAAK,CAAC;KAClI;IACD,qBAAqB,MAAM,GAAG,IAAI,CAAC;IACnC,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;QACxE,MAAM,GAAG,IAAI,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;QACzD,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;YACvB,uBAAuB,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5F,MAAM,GAAG,SAAS,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;SACjE;KACJ;IACD,OAAO,MAAM,CAAC;CACjB;;;;AAID,AAAO,SAAS,WAAW,GAAG;IAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;QAChC,OAAO,QAAQ,CAAC,IAAI,CAAC;KACxB;IACD,OAAO,IAAI,CAAC;CACf;AACD,AAAO,MAAuB,cAAc,GAAG,QAAQ,CAAC;AACxD,AAAO,MAAuB,eAAe,GAAG,SAAS,CAAC;AAC1D,AAAO,MAAuB,WAAW,GAAG,MAAM;;AChOlD;;;;AAIA,AAEA;;;AAGA,AAAO,MAAM,mBAAmB,CAAC;;;;;IAK7B,qBAAqB,CAAC,IAAI,EAAE,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;;;;;;IAMnE,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE;QAC9B,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC5C;;;;;;IAMD,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;;;;;;;IAOnE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC5B,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAChD;;;;;;;IAOD,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE;QACtC,OAAO,YAAY,IAAI,EAAE,CAAC;KAC7B;;;;;;;;;;IAUD,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,GAAG,EAAE,EAAE;QACvE,OAAO,IAAI,mBAAmB,EAAE,CAAC;KACpC;CACJ;;;;;AAKD,AAAO,MAAM,eAAe,CAAC;CAC5B;AACD,eAAe,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;;AClEjD;;;;AAIA,AACO,MAAuB,UAAU,GAAG,IAAI,CAAC;AAChD,AAAO,MAAuB,uBAAuB,GAAG,IAAI,CAAC;AAC7D,AAAO,MAAuB,qBAAqB,GAAG,IAAI,CAAC;AAC3D,AAAO,MAAuB,eAAe,GAAG,UAAU,CAAC;AAC3D,AAAO,MAAuB,eAAe,GAAG,UAAU,CAAC;AAC3D,AAA2D;AAC3D,AAA2D;AAC3D,AAAO,MAAuB,oBAAoB,GAAG,YAAY,CAAC;AAClE,AAAO,MAAuB,mBAAmB,GAAG,aAAa,CAAC;AAClE,AAAO,MAAuB,sBAAsB,GAAG,cAAc,CAAC;AACtE,AAAO,MAAuB,qBAAqB,GAAG,eAAe,CAAC;;;;;AAKtE,AAAO,SAAS,kBAAkB,CAAC,KAAK,EAAE;IACtC,IAAI,OAAO,KAAK,IAAI,QAAQ;QACxB,OAAO,KAAK,CAAC;IACjB,uBAAuB,OAAO,GAAG,mBAAmB,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACvF,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;QAC9B,OAAO,CAAC,CAAC;IACb,OAAO,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;CACpE;;;;;;AAMD,SAAS,qBAAqB,CAAC,KAAK,EAAE,IAAI,EAAE;IACxC,QAAQ,IAAI;QACR,KAAK,GAAG;YACJ,OAAO,KAAK,GAAG,UAAU,CAAC;QAC9B;;YAEI,OAAO,KAAK,CAAC;KACpB;CACJ;;;;;;;AAOD,AAAO,SAAS,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE;IAChE,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,qBAAqB,OAAO;QACjE,mBAAmB,mBAAmB,OAAO,GAAG,MAAM,EAAE,mBAAmB,CAAC,CAAC;CACpF;;;;;;;AAOD,SAAS,mBAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,mBAAmB,EAAE;IAC3D,uBAAuB,KAAK,GAAG,0EAA0E,CAAC;IAC1G,qBAAqB,QAAQ,CAAC;IAC9B,qBAAqB,KAAK,GAAG,CAAC,CAAC;IAC/B,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACzB,uBAAuB,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,OAAO,KAAK,IAAI,EAAE;YAClB,MAAM,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;YAC9D,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;SAChD;QACD,QAAQ,GAAG,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,uBAAuB,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,UAAU,IAAI,IAAI,EAAE;YACpB,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACjF;QACD,uBAAuB,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,SAAS,EAAE;YACX,MAAM,GAAG,SAAS,CAAC;SACtB;KACJ;SACI;QACD,QAAQ,qBAAqB,GAAG,CAAC,CAAC;KACrC;IACD,IAAI,CAAC,mBAAmB,EAAE;QACtB,qBAAqB,cAAc,GAAG,KAAK,CAAC;QAC5C,qBAAqB,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;QAChD,IAAI,QAAQ,GAAG,CAAC,EAAE;YACd,MAAM,CAAC,IAAI,CAAC,CAAC,gEAAgE,CAAC,CAAC,CAAC;YAChF,cAAc,GAAG,IAAI,CAAC;SACzB;QACD,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,MAAM,CAAC,IAAI,CAAC,CAAC,6DAA6D,CAAC,CAAC,CAAC;YAC7E,cAAc,GAAG,IAAI,CAAC;SACzB;QACD,IAAI,cAAc,EAAE;YAChB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,2BAA2B,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;SAClF;KACJ;IACD,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CACtC;;;;;;AAMD,AAAO,SAAS,OAAO,CAAC,GAAG,EAAE,WAAW,GAAG,EAAE,EAAE;IAC3C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,OAAO,WAAW,CAAC;CACtB;;;;;AAKD,AAAO,SAAS,eAAe,CAAC,MAAM,EAAE;IACpC,uBAAuB,gBAAgB,GAAG,EAAE,CAAC;IAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC;KACrE;SACI;QACD,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;KAC/C;IACD,OAAO,gBAAgB,CAAC;CAC3B;;;;;;;AAOD,AAAO,SAAS,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,GAAG,EAAE,EAAE;IAChE,IAAI,aAAa,EAAE;;;;QAIf,KAAK,qBAAqB,IAAI,IAAI,MAAM,EAAE;YACtC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SACpC;KACJ;SACI;QACD,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;KAChC;IACD,OAAO,WAAW,CAAC;CACtB;;;;;;AAMD,AAAO,SAAS,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE;IACvC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YAChC,uBAAuB,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC7D,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SAC3C,CAAC,CAAC;KACN;CACJ;;;;;;AAMD,AAAO,SAAS,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE;IACzC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YAChC,uBAAuB,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC7D,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;SACjC,CAAC,CAAC;KACN;CACJ;;;;;AAKD,AAAO,SAAS,uBAAuB,CAAC,KAAK,EAAE;IAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACtB,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;YACjB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QACpB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC1B;IACD,yBAAyB,KAAK,EAAE;CACnC;;;;;;;AAOD,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE;IACxD,uBAAuB,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;IACrD,uBAAuB,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC3D,IAAI,OAAO,CAAC,MAAM,EAAE;QAChB,OAAO,CAAC,OAAO,CAAC,OAAO,IAAI;YACvB,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBACjC,MAAM,CAAC,IAAI,CAAC,CAAC,4CAA4C,EAAE,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;aACrG;SACJ,CAAC,CAAC;KACN;CACJ;AACD,MAAuB,WAAW,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,uBAAuB,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;;;;AAKxH,AAAO,SAAS,kBAAkB,CAAC,KAAK,EAAE;IACtC,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,uBAAuB,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC9C,qBAAqB,KAAK,CAAC;QAC3B,OAAO,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClC,MAAM,CAAC,IAAI,mBAAmB,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;SAC5C;QACD,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;KAC7B;IACD,OAAO,MAAM,CAAC;CACjB;;;;;;;AAOD,AAAO,SAAS,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;IACrD,uBAAuB,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;IACnD,uBAAuB,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK;QACvE,qBAAqB,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;;QAEhD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,CAAC,+CAA+C,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YACzE,QAAQ,GAAG,EAAE,CAAC;SACjB;QACD,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;KAC9B,CAAC,CAAC;;IAEH,OAAO,GAAG,IAAI,QAAQ,GAAG,KAAK,GAAG,GAAG,CAAC;CACxC;;;;;AAKD,AAAO,SAAS,eAAe,CAAC,QAAQ,EAAE;IACtC,uBAAuB,GAAG,GAAG,EAAE,CAAC;IAChC,qBAAqB,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC5C,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;QACf,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC1B;IACD,OAAO,GAAG,CAAC;CACd;;;;;;AAMD,AAcC;AACD,MAAuB,gBAAgB,GAAG,eAAe,CAAC;;;;;AAK1D,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;IACvC,OAAO,KAAK,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;CACxE;;;;;;AAMD,AAAO,SAAS,8BAA8B,CAAC,QAAQ,EAAE,KAAK,EAAE;IAC5D,OAAO,QAAQ,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;CACxC;;;;;;;AAOD,AAAO,SAAS,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;IACjD,QAAQ,IAAI,CAAC,IAAI;QACb,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACpD,KAAK,EAAE;YACH,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClD,KAAK,EAAE;YACH,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,EAAE;YACH,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C;YACI,MAAM,IAAI,KAAK,CAAC,CAAC,2CAA2C,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAClF;CACJ;;AChUD;;;;;;;;;;;AAWA,AAAO,MAAuB,SAAS,GAAG,GAAG,CAAC;;;;;;AAM9C,AAAO,SAAS,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAE;IACzD,uBAAuB,WAAW,GAAG,EAAE,CAAC;IACxC,IAAI,OAAO,eAAe,IAAI,QAAQ,EAAE;QACpC,mBAAmB,eAAe;aAC7B,KAAK,CAAC,SAAS,CAAC;aAChB,OAAO,CAAC,GAAG,IAAI,uBAAuB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;KAC1E;SACI;QACD,WAAW,CAAC,IAAI,mBAAmB,eAAe,EAAE,CAAC;KACxD;IACD,OAAO,WAAW,CAAC;CACtB;;;;;;;AAOD,SAAS,uBAAuB,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE;IAC5D,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;QACpB,uBAAuB,MAAM,GAAG,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACtE,IAAI,OAAO,MAAM,IAAI,UAAU,EAAE;YAC7B,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,OAAO;SACV;QACD,QAAQ,qBAAqB,MAAM,CAAC,CAAC;KACxC;IACD,uBAAuB,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;IACzF,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACnC,MAAM,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACjF,OAAO,WAAW,CAAC;KACtB;IACD,uBAAuB,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5C,uBAAuB,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5C,uBAAuB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1C,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IAC3D,uBAAuB,kBAAkB,GAAG,SAAS,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,CAAC;IAC3F,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE;QAC5C,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;KAC9D;CACJ;;;;;;AAMD,SAAS,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE;IACxC,QAAQ,KAAK;QACT,KAAK,QAAQ;YACT,OAAO,WAAW,CAAC;QACvB,KAAK,QAAQ;YACT,OAAO,WAAW,CAAC;QACvB,KAAK,YAAY;YACb,OAAO,CAAC,SAAS,EAAE,OAAO,KAAK,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QAC/E,KAAK,YAAY;YACb,OAAO,CAAC,SAAS,EAAE,OAAO,KAAK,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QAC/E;YACI,MAAM,CAAC,IAAI,CAAC,CAAC,4BAA4B,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YACtE,OAAO,QAAQ,CAAC;KACvB;CACJ;;;;;AAKD,MAAuB,mBAAmB,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AACpE,MAAuB,oBAAoB,GAAG,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;;;;;;AAMtE,SAAS,oBAAoB,CAAC,GAAG,EAAE,GAAG,EAAE;IACpC,uBAAuB,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzG,uBAAuB,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzG,OAAO,CAAC,SAAS,EAAE,OAAO,KAAK;QAC3B,qBAAqB,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS,CAAC;QACrE,qBAAqB,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,OAAO,CAAC;QACnE,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;YAClE,QAAQ,GAAG,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACvF;QACD,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAChE,QAAQ,GAAG,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACrF;QACD,OAAO,QAAQ,IAAI,QAAQ,CAAC;KAC/B,CAAC;CACL;;ACvGD;;;;AAIA,AAIA,MAAuB,UAAU,GAAG,OAAO,CAAC;AAC5C,MAAuB,gBAAgB,GAAG,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;;;;;;;AAOnF,AAAO,SAAS,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE;IACxD,OAAO,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;CACzE;AACD,MAAuB,aAAa,GAAG,EAAE,CAAC;AAC1C,AAAO,MAAM,0BAA0B,CAAC;;;;IAIpC,WAAW,CAAC,OAAO,EAAE;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;IAMD,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE;QACpB,uBAAuB,OAAO,GAAG,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACxE,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;QAC5C,yBAAyB,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,EAAE;KAC5F;;;;;IAKD,6BAA6B,CAAC,OAAO,EAAE;QACnC,OAAO,CAAC,oBAAoB,GAAG,aAAa,CAAC;QAC7C,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;QAC7B,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;QAC5C,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;KAC3B;;;;;;IAMD,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC5B,qBAAqB,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;QACzD,qBAAqB,QAAQ,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrD,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,uBAAuB,WAAW,GAAG,EAAE,CAAC;QACxC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAChC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;SACjH;QACD,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI;YAChC,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc;gBAC3B,uBAAuB,QAAQ,qBAAqB,GAAG,CAAC,CAAC;gBACzD,uBAAuB,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC5C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI;oBAC/B,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;oBAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;iBACnD,CAAC,CAAC;gBACH,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;aACxB;iBACI,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,mBAAmB;gBACrC,uBAAuB,UAAU,GAAG,IAAI,CAAC,eAAe,mBAAmB,GAAG,GAAG,OAAO,CAAC,CAAC;gBAC1F,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC;gBACpC,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC;gBAChC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC;iBACI;gBACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;aAClG;SACJ,CAAC,CAAC;QACH,OAAO;YACH,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ;YAC9D,OAAO,EAAE,IAAI;SAChB,CAAC;KACL;;;;;;IAMD,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC1B,uBAAuB,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5E,uBAAuB,SAAS,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;QACzF,IAAI,QAAQ,CAAC,qBAAqB,EAAE;YAChC,uBAAuB,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;YAC/C,uBAAuB,MAAM,GAAG,SAAS,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;gBAC7B,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;oBACjB,uBAAuB,SAAS,qBAAqB,KAAK,CAAC,CAAC;oBAC5D,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;wBACnC,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;4BAC/C,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gCAC7B,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;6BACxB;yBACJ,CAAC,CAAC;qBACN,CAAC,CAAC;iBACN;aACJ,CAAC,CAAC;YACH,IAAI,WAAW,CAAC,IAAI,EAAE;gBAClB,uBAAuB,cAAc,GAAG,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC9E,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,8EAA8E,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5J;SACJ;QACD,OAAO;YACH,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,SAAS,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI;SACpD,CAAC;KACL;;;;;;IAMD,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC/B,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;QACvB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrB,uBAAuB,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;QAC5G,uBAAuB,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACrF,OAAO;YACH,IAAI,EAAE,CAAC;YACP,QAAQ;YACR,SAAS;YACT,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL;;;;;;IAMD,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC7B,OAAO;YACH,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;YAC9D,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL;;;;;;IAMD,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC1B,uBAAuB,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACzD,qBAAqB,YAAY,GAAG,CAAC,CAAC;QACtC,uBAAuB,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI;YACtD,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;YAClC,uBAAuB,QAAQ,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACpE,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;YAC3D,OAAO,QAAQ,CAAC;SACnB,CAAC,CAAC;QACH,OAAO,CAAC,WAAW,GAAG,YAAY,CAAC;QACnC,OAAO;YACH,IAAI,EAAE,CAAC;YACP,KAAK;YACL,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL;;;;;;IAMD,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC5B,uBAAuB,SAAS,GAAG,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACxF,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;QAC1C,qBAAqB,QAAQ,CAAC;QAC9B,qBAAqB,aAAa,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;QACnF,IAAI,aAAa,CAAC,IAAI,IAAI,CAAC,kBAAkB;YACzC,QAAQ,GAAG,IAAI,CAAC,cAAc,mBAAmB,aAAa,GAAG,OAAO,CAAC,CAAC;SAC7E;aACI;YACD,qBAAqB,aAAa,qBAAqB,QAAQ,CAAC,MAAM,CAAC,CAAC;YACxE,qBAAqB,OAAO,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,aAAa,EAAE;gBAChB,OAAO,GAAG,IAAI,CAAC;gBACf,uBAAuB,YAAY,GAAG,EAAE,CAAC;gBACzC,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClB,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;iBAC7C;gBACD,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;aACvC;YACD,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;YAC5D,uBAAuB,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAC3E,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;YAChC,QAAQ,GAAG,SAAS,CAAC;SACxB;QACD,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACrC,OAAO;YACH,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,IAAI;SAChB,CAAC;KACL;;;;;;IAMD,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC1B,uBAAuB,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACrC,OAAO,GAAG,CAAC;KACd;;;;;;IAMD,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC7B,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAChC,mBAAmB,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,IAAI;gBACvD,IAAI,OAAO,UAAU,IAAI,QAAQ,EAAE;oBAC/B,IAAI,UAAU,IAAI,UAAU,EAAE;wBAC1B,MAAM,CAAC,IAAI,mBAAmB,UAAU,EAAE,CAAC;qBAC9C;yBACI;wBACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,gCAAgC,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC;qBACxF;iBACJ;qBACI;oBACD,MAAM,CAAC,IAAI,mBAAmB,UAAU,EAAE,CAAC;iBAC9C;aACJ,CAAC,CAAC;SACN;aACI;YACD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SAChC;QACD,qBAAqB,qBAAqB,GAAG,KAAK,CAAC;QACnD,qBAAqB,eAAe,GAAG,IAAI,CAAC;QAC5C,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI;YACxB,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;gBACrB,uBAAuB,QAAQ,qBAAqB,SAAS,CAAC,CAAC;gBAC/D,uBAAuB,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACnD,IAAI,MAAM,EAAE;oBACR,eAAe,qBAAqB,MAAM,CAAC,CAAC;oBAC5C,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBAC7B;gBACD,IAAI,CAAC,qBAAqB,EAAE;oBACxB,KAAK,qBAAqB,IAAI,IAAI,QAAQ,EAAE;wBACxC,uBAAuB,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAC9C,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;4BACxD,qBAAqB,GAAG,IAAI,CAAC;4BAC7B,MAAM;yBACT;qBACJ;iBACJ;aACJ;SACJ,CAAC,CAAC;QACH,OAAO;YACH,IAAI,EAAE,CAAC;YACP,MAAM;YACN,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,qBAAqB;YAC9C,OAAO,EAAE,IAAI;SAChB,CAAC;KACL;;;;;;IAMD,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE;QAC5B,uBAAuB,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAC/D,qBAAqB,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC;QACnD,qBAAqB,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC;QACrD,IAAI,OAAO,IAAI,SAAS,GAAG,CAAC,EAAE;YAC1B,SAAS,IAAI,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;SACjD;QACD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;YACxB,IAAI,OAAO,KAAK,IAAI,QAAQ;gBACxB,OAAO;YACX,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;gBAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;oBAC3C,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,gDAAgD,CAAC,CAAC,CAAC;oBAChH,OAAO;iBACV;gBACD,uBAAuB,eAAe,GAAG,OAAO,CAAC,eAAe,oBAAoB,OAAO,CAAC,oBAAoB,GAAG,CAAC;gBACpH,uBAAuB,cAAc,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC9D,qBAAqB,oBAAoB,GAAG,IAAI,CAAC;gBACjD,IAAI,cAAc,EAAE;oBAChB,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,cAAc,CAAC,SAAS;wBAC7D,OAAO,IAAI,cAAc,CAAC,OAAO,EAAE;wBACnC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,oCAAoC,EAAE,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,yEAAyE,EAAE,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;wBAC7P,oBAAoB,GAAG,KAAK,CAAC;qBAChC;;;;oBAID,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;iBACxC;gBACD,IAAI,oBAAoB,EAAE;oBACtB,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;iBAClD;gBACD,IAAI,OAAO,CAAC,OAAO,EAAE;oBACjB,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;iBACrE;aACJ,CAAC,CAAC;SACN,CAAC,CAAC;KACN;;;;;;IAMD,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC9B,uBAAuB,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC,kBAAkB,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QACpF,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YAChC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,wDAAwD,CAAC,CAAC,CAAC;YAChF,OAAO,GAAG,CAAC;SACd;QACD,uBAAuB,mBAAmB,GAAG,CAAC,CAAC;QAC/C,qBAAqB,yBAAyB,GAAG,CAAC,CAAC;QACnD,uBAAuB,OAAO,GAAG,EAAE,CAAC;QACpC,qBAAqB,iBAAiB,GAAG,KAAK,CAAC;QAC/C,qBAAqB,mBAAmB,GAAG,KAAK,CAAC;QACjD,qBAAqB,cAAc,GAAG,CAAC,CAAC;QACxC,uBAAuB,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,IAAI;YAC5D,uBAAuBC,QAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACnE,qBAAqB,SAAS,GAAGA,QAAK,CAAC,MAAM,IAAI,IAAI,GAAGA,QAAK,CAAC,MAAM,GAAG,aAAa,CAACA,QAAK,CAAC,MAAM,CAAC,CAAC;YACnG,qBAAqB,MAAM,GAAG,CAAC,CAAC;YAChC,IAAI,SAAS,IAAI,IAAI,EAAE;gBACnB,yBAAyB,EAAE,CAAC;gBAC5B,MAAM,GAAGA,QAAK,CAAC,MAAM,GAAG,SAAS,CAAC;aACrC;YACD,mBAAmB,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;YACtE,iBAAiB,GAAG,iBAAiB,IAAI,MAAM,GAAG,cAAc,CAAC;YACjE,cAAc,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,OAAOA,QAAK,CAAC;SAChB,CAAC,CAAC;QACH,IAAI,mBAAmB,EAAE;YACrB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,2DAA2D,CAAC,CAAC,CAAC;SACtF;QACD,IAAI,iBAAiB,EAAE;YACnB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,oDAAoD,CAAC,CAAC,CAAC;SAC/E;QACD,uBAAuB,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QACtD,qBAAqB,eAAe,GAAG,CAAC,CAAC;QACzC,IAAI,yBAAyB,GAAG,CAAC,IAAI,yBAAyB,GAAG,MAAM,EAAE;YACrE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,qEAAqE,CAAC,CAAC,CAAC;SAChG;aACI,IAAI,yBAAyB,IAAI,CAAC,EAAE;YACrC,eAAe,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;SACxD;QACD,uBAAuB,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;QAC1C,uBAAuB,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACzD,uBAAuB,qBAAqB,sBAAsB,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAClG,uBAAuB,eAAe,GAAG,qBAAqB,CAAC,QAAQ,CAAC;QACxE,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;YACzB,uBAAuB,MAAM,GAAG,eAAe,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;YAC5G,uBAAuB,qBAAqB,GAAG,MAAM,GAAG,eAAe,CAAC;YACxE,OAAO,CAAC,WAAW,GAAG,WAAW,GAAG,qBAAqB,CAAC,KAAK,GAAG,qBAAqB,CAAC;YACxF,qBAAqB,CAAC,QAAQ,GAAG,qBAAqB,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YACpC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;YACnB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACvB,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;KACd;;;;;;IAMD,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC9B,OAAO;YACH,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;YACnF,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL;;;;;;IAMD,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE;QACjC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO;YACH,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL;;;;;;IAMD,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC/B,OAAO;YACH,IAAI,EAAE,EAAE;YACR,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC;YAC3D,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL;;;;;;IAMD,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC1B,uBAAuB,cAAc,sBAAsB,OAAO,CAAC,oBAAoB,EAAE,CAAC;QAC1F,uBAAuB,OAAO,sBAAsB,QAAQ,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QAC7E,OAAO,CAAC,UAAU,EAAE,CAAC;QACrB,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;QAChC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACrE,OAAO,CAAC,oBAAoB;YACxB,cAAc,CAAC,MAAM,IAAI,cAAc,GAAG,GAAG,GAAG,QAAQ,IAAI,QAAQ,CAAC;QACzE,eAAe,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;QAC3E,uBAAuB,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;QAC5G,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;QAC5B,OAAO,CAAC,oBAAoB,GAAG,cAAc,CAAC;QAC9C,OAAO;YACH,IAAI,EAAE,EAAE;YACR,QAAQ;YACR,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YACzB,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS;YACpD,gBAAgB,EAAE,QAAQ,CAAC,QAAQ;YACnC,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL;;;;;;IAMD,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;QAC5B,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YACvB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC;SACvE;QACD,uBAAuB,OAAO,GAAG,QAAQ,CAAC,OAAO,KAAK,MAAM;YACxD,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE;YACzC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1D,OAAO;YACH,IAAI,EAAE,EAAE;YACR,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO;YAC5F,OAAO,EAAE,IAAI;SAChB,CAAC;KACL;CACJ;AACD,AAIA;;;;AAIA,SAAS,iBAAiB,CAAC,QAAQ,EAAE;IACjC,uBAAuB,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;IAClH,IAAI,YAAY,EAAE;QACd,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;KACrD;;IAED,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,mBAAmB,CAAC;SACnD,OAAO,CAAC,OAAO,EAAE,KAAK,IAAI,mBAAmB,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACtE,OAAO,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;IACnD,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;CACnC;;;;;AAKD,SAAS,eAAe,CAAC,GAAG,EAAE;IAC1B,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;CACpC;AACD,AAAO,MAAM,0BAA0B,CAAC;;;;IAIpC,WAAW,CAAC,MAAM,EAAE;QAChB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;CACJ;AACD,AAsBA;;;;AAIA,SAAS,aAAa,CAAC,MAAM,EAAE;IAC3B,IAAI,OAAO,MAAM,IAAI,QAAQ;QACzB,OAAO,IAAI,CAAC;IAChB,qBAAqB,MAAM,GAAG,IAAI,CAAC;IACnC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI;YACzB,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC7D,uBAAuB,GAAG,qBAAqB,UAAU,CAAC,CAAC;gBAC3D,MAAM,GAAG,UAAU,mBAAmB,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtD,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;aACxB;SACJ,CAAC,CAAC;KACN;SACI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;QAC1D,uBAAuB,GAAG,qBAAqB,MAAM,CAAC,CAAC;QACvD,MAAM,GAAG,UAAU,mBAAmB,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;QACtD,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;KACxB;IACD,OAAO,MAAM,CAAC;CACjB;;;;;AAKD,SAAS,QAAQ,CAAC,KAAK,EAAE;IACrB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,IAAI,QAAQ,CAAC;CAC5D;;;;;;AAMD,SAAS,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE;IACvC,qBAAqB,OAAO,GAAG,IAAI,CAAC;IACpC,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;QAClC,OAAO,qBAAqB,KAAK,CAAC,CAAC;KACtC;SACI,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;QAC/B,uBAAuB,QAAQ,GAAG,aAAa,mBAAmB,KAAK,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC;QAC3F,OAAO,aAAa,mBAAmB,QAAQ,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;KAC5D;IACD,uBAAuB,QAAQ,qBAAqB,KAAK,CAAC,CAAC;IAC3D,uBAAuB,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;IAC7G,IAAI,SAAS,EAAE;QACX,uBAAuB,GAAG,qBAAqB,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACxE,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;QACnB,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxB,yBAAyB,GAAG,EAAE;KACjC;IACD,OAAO,GAAG,OAAO,IAAI,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACrD,OAAO,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;CACzE;;;;;AAKD,SAAS,yBAAyB,CAAC,OAAO,EAAE;IACxC,IAAI,OAAO,EAAE;QACT,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;YACnB,OAAO,CAAC,QAAQ,CAAC,sBAAsB,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;SAC/E;KACJ;SACI;QACD,OAAO,GAAG,EAAE,CAAC;KAChB;IACD,OAAO,OAAO,CAAC;CAClB;;;;;;;AAOD,SAAS,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;IAC5C,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CACtC;;AC5lBD;;;;;;;AAOA,AAAkD;AAClD,AAsBA;;;;;;;;;;;AAWA,AAAO,SAAS,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE;IAC9I,OAAO;QACH,IAAI,EAAE,CAAC;QACP,OAAO;QACP,SAAS;QACT,aAAa;QACb,cAAc;QACd,QAAQ;QACR,KAAK;QACL,SAAS,EAAE,QAAQ,GAAG,KAAK,EAAE,MAAM,EAAE,WAAW;KACnD,CAAC;CACL;;ACpDD;;;;AAIA,AAAO,MAAM,qBAAqB,CAAC;IAC/B,WAAW,GAAG;QACV,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;KACzB;;;;;IAKD,OAAO,CAAC,OAAO,EAAE;QACb,qBAAqB,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC7B;aACI;YACD,YAAY,GAAG,EAAE,CAAC;SACrB;QACD,OAAO,YAAY,CAAC;KACvB;;;;;;IAMD,MAAM,CAAC,OAAO,EAAE,YAAY,EAAE;QAC1B,qBAAqB,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,oBAAoB,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,oBAAoB,GAAG,EAAE,CAAC,CAAC;SACrD;QACD,oBAAoB,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;KAC9C;;;;;IAKD,GAAG,CAAC,OAAO,EAAE,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE;;;;IAI/C,KAAK,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE;CACjC;;AC3CD;;;;AAIA,AAIA,MAAuB,yBAAyB,GAAG,CAAC,CAAC;AACrD,MAAuB,WAAW,GAAG,QAAQ,CAAC;AAC9C,MAAuB,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACxE,MAAuB,WAAW,GAAG,QAAQ,CAAC;AAC9C,MAAuB,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;;;;;;;;;;;;;;AAcxE,AAAO,SAAS,uBAAuB,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,GAAG,EAAE,EAAE,WAAW,GAAG,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,GAAG,EAAE,EAAE;IAC5K,OAAO,IAAI,+BAA+B,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;CACxL;AACD,AAAO,MAAM,+BAA+B,CAAC;;;;;;;;;;;;;;IAczC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,GAAG,EAAE,EAAE;QACzI,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;QACjE,uBAAuB,OAAO,GAAG,IAAI,wBAAwB,CAAC,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAChJ,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnF,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;;QAEjC,uBAAuB,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC;QACtG,IAAI,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;YACrD,uBAAuB,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,EAAE,CAAC,uBAAuB,EAAE,EAAE;gBAC/B,EAAE,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC9D;SACJ;QACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC1E,CAAC,yBAAyB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;KAC7E;;;;;;IAMD,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE;;KAE1B;;;;;;IAMD,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE;;KAExB;;;;;;IAMD,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE;;KAE7B;;;;;;IAMD,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE;QAC5B,uBAAuB,mBAAmB,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9F,IAAI,mBAAmB,EAAE;YACrB,uBAAuB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5E,uBAAuB,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;YACvE,uBAAuB,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,YAAY,oBAAoB,YAAY,CAAC,OAAO,EAAE,CAAC;YACxI,IAAI,SAAS,IAAI,OAAO,EAAE;;;gBAGtB,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;aAC7C;SACJ;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE;QAC1B,uBAAuB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5E,YAAY,CAAC,wBAAwB,EAAE,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACjD,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC3E,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;;IAOD,qBAAqB,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE;QAClD,uBAAuB,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACvE,qBAAqB,YAAY,GAAG,SAAS,CAAC;;;QAG9C,uBAAuB,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QACzG,uBAAuB,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QAChG,IAAI,QAAQ,KAAK,CAAC,EAAE;YAChB,YAAY,CAAC,OAAO,CAAC,WAAW,IAAI;gBAChC,uBAAuB,kBAAkB,GAAG,OAAO,CAAC,2BAA2B,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC9G,YAAY;oBACR,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACtF,CAAC,CAAC;SACN;QACD,OAAO,YAAY,CAAC;KACvB;;;;;;IAMD,cAAc,CAAC,GAAG,EAAE,OAAO,EAAE;QACzB,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC3C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE;QACxB,uBAAuB,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QACjE,qBAAqB,GAAG,GAAG,OAAO,CAAC;QACnC,uBAAuB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC7C,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9C,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACxC,GAAG,CAAC,wBAAwB,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;gBACvB,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,cAAc;oBACxC,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;oBAC5C,GAAG,CAAC,YAAY,GAAG,0BAA0B,CAAC;iBACjD;gBACD,uBAAuB,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjE,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aAC5B;SACJ;QACD,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;YAClB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;YAEnD,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;;;;YAI5C,IAAI,GAAG,CAAC,eAAe,GAAG,eAAe,EAAE;gBACvC,GAAG,CAAC,wBAAwB,EAAE,CAAC;aAClC;SACJ;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE;QACrB,uBAAuB,cAAc,GAAG,EAAE,CAAC;QAC3C,qBAAqB,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACxE,uBAAuB,KAAK,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5G,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI;YACnB,uBAAuB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5E,IAAI,KAAK,EAAE;gBACP,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACrC;YACD,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YACpC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAChF,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;SACrD,CAAC,CAAC;;;;QAIH,cAAc,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnG,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC/C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE;QACvB,IAAI,mBAAmB,GAAG,GAAG,OAAO,EAAE;YAClC,uBAAuB,QAAQ,GAAG,mBAAmB,GAAG,GAAG,QAAQ,CAAC;YACpE,uBAAuB,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;YAC7H,OAAO,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;SACrD;aACI;YACD,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC;SAC3E;KACJ;;;;;;IAMD,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE;QACvB,uBAAuB,OAAO,GAAG,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACzG,uBAAuB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;QAC1D,IAAI,OAAO,CAAC,KAAK,EAAE;YACf,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;SACpC;QACD,uBAAuBA,QAAK,GAAG,GAAG,CAAC,KAAK,CAAC;QACzC,IAAIA,QAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB;YACjC,IAAI,CAAC,cAAc,CAACA,QAAK,EAAE,OAAO,CAAC,CAAC;SACvC;aACI;YACD,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACxC,IAAI,CAAC,UAAU,mBAAmBA,QAAK,GAAG,OAAO,CAAC,CAAC;YACnD,QAAQ,CAAC,qBAAqB,EAAE,CAAC;SACpC;QACD,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACrC,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE;QACrB,uBAAuB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;QAC1D,uBAAuB,OAAO,sBAAsB,OAAO,CAAC,qBAAqB,EAAE,CAAC;;;QAGpF,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE;YACzD,QAAQ,CAAC,YAAY,EAAE,CAAC;SAC3B;QACD,uBAAuB,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC;QAC1E,IAAI,GAAG,CAAC,WAAW,EAAE;YACjB,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACnC;aACI;YACD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3E;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,cAAc,CAAC,GAAG,EAAE,OAAO,EAAE;QACzB,uBAAuB,qBAAqB,sBAAsB,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAClG,uBAAuB,SAAS,GAAG,oBAAoB,OAAO,CAAC,eAAe,IAAI,QAAQ,CAAC;QAC3F,uBAAuB,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC;QACjE,uBAAuB,YAAY,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjE,uBAAuB,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC;QACpE,aAAa,CAAC,MAAM,GAAG,qBAAqB,CAAC,MAAM,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI;YACvB,uBAAuB,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;YACjD,aAAa,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;YAC7C,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACnF,aAAa,CAAC,qBAAqB,EAAE,CAAC;SACzC,CAAC,CAAC;;;QAGH,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;;;QAGpE,OAAO,CAAC,wBAAwB,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;QACvD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE;;;QAGrB,uBAAuB,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACvE,uBAAuB,OAAO,sBAAsB,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QACxE,uBAAuB,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrF,IAAI,KAAK,KAAK,OAAO,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;aACxC,SAAS,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;YACjF,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAChD,OAAO,CAAC,YAAY,GAAG,0BAA0B,CAAC;SACrD;QACD,qBAAqB,YAAY,GAAG,SAAS,CAAC;QAC9C,uBAAuB,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACnK,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;QACxC,qBAAqB,mBAAmB,GAAG,IAAI,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK;YACzB,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC9B,uBAAuB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrF,IAAI,KAAK,EAAE;gBACP,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACrC;YACD,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE;gBAC7B,mBAAmB,GAAG,YAAY,CAAC,eAAe,CAAC;aACtD;YACD,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;;;;YAIhD,YAAY,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;YACrD,uBAAuB,OAAO,GAAG,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC;YAC1E,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;SAClD,CAAC,CAAC;QACH,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC9B,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC9B,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,mBAAmB,EAAE;YACrB,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,CAAC;YAC1E,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;SACnD;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B;;;;;;IAMD,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE;QACvB,uBAAuB,aAAa,sBAAsB,OAAO,CAAC,aAAa,EAAE,CAAC;QAClF,uBAAuB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC;QACpD,uBAAuB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC7C,uBAAuB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7D,uBAAuB,OAAO,GAAG,QAAQ,IAAI,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC5E,qBAAqB,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAClE,qBAAqB,kBAAkB,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;QAC5F,QAAQ,kBAAkB;YACtB,KAAK,SAAS;gBACV,KAAK,GAAG,OAAO,GAAG,KAAK,CAAC;gBACxB,MAAM;YACV,KAAK,MAAM;gBACP,KAAK,GAAG,aAAa,CAAC,kBAAkB,CAAC;gBACzC,MAAM;SACb;QACD,uBAAuB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;QAC1D,IAAI,KAAK,EAAE;YACP,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,uBAAuB,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC;QAC3D,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC3C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;;;;;QAK3B,aAAa,CAAC,kBAAkB;YAC5B,CAAC,EAAE,CAAC,WAAW,GAAG,YAAY,KAAK,EAAE,CAAC,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;KAClG;CACJ;AACD,MAAuB,0BAA0B,qBAAqB,EAAE,CAAC,CAAC;AAC1E,AAAO,MAAM,wBAAwB,CAAC;;;;;;;;;;;IAWlC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE;QACjH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,0BAA0B,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACxF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACxC;;;;IAID,IAAI,MAAM,GAAG,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;;;;;IAM5C,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE;QACjC,IAAI,CAAC,OAAO;YACR,OAAO;QACX,uBAAuB,UAAU,qBAAqB,OAAO,CAAC,CAAC;QAC/D,qBAAqB,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;;QAEpD,IAAI,UAAU,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC7B,mBAAmB,eAAe,GAAG,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;SAC3F;QACD,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE;YAC1B,eAAe,CAAC,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAChE;QACD,uBAAuB,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;QACrD,IAAI,SAAS,EAAE;YACX,qBAAqB,cAAc,sBAAsB,eAAe,CAAC,MAAM,EAAE,CAAC;YAClF,IAAI,CAAC,cAAc,EAAE;gBACjB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;aAC7C;YACD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;gBACnC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBACvD,cAAc,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC1F;aACJ,CAAC,CAAC;SACN;KACJ;;;;IAID,YAAY,GAAG;QACX,uBAAuB,OAAO,GAAG,EAAE,CAAC;QACpC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,uBAAuB,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACvD,IAAI,SAAS,EAAE;gBACX,uBAAuB,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACvD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;aAC/E;SACJ;QACD,OAAO,OAAO,CAAC;KAClB;;;;;;;IAOD,gBAAgB,CAAC,OAAO,GAAG,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;QAC/C,uBAAuB,MAAM,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QACxD,uBAAuB,OAAO,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;QACpO,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACzC,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAC3D,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACnD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACnD,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,OAAO,CAAC;KAClB;;;;;IAKD,wBAAwB,CAAC,OAAO,EAAE;QAC9B,IAAI,CAAC,YAAY,GAAG,0BAA0B,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;;;;;;;IAOD,2BAA2B,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE;QACtD,uBAAuB,cAAc,GAAG;YACpC,QAAQ,EAAE,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,WAAW,CAAC,QAAQ;YAC5D,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK;YACzF,MAAM,EAAE,EAAE;SACb,CAAC;QACF,uBAAuB,OAAO,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW,CAAC,uBAAuB,CAAC,CAAC;QAC9N,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO,cAAc,CAAC;KACzB;;;;;IAKD,aAAa,CAAC,IAAI,EAAE;QAChB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;KAC1E;;;;;IAKD,aAAa,CAAC,KAAK,EAAE;;QAEjB,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7C;KACJ;;;;;;;;;;IAUD,WAAW,CAAC,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE;QAC1E,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC9B;QACD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;;YAErB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3E,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3E,uBAAuB,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;YAC1C,qBAAqB,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAClF,IAAI,KAAK,KAAK,CAAC,EAAE;gBACb,QAAQ,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;oBAC3E,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aAChC;YACD,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,gBAAgB,CAAC,2CAA2C,EAAE,gBAAgB,CAAC,oDAAoD,CAAC,CAAC,CAAC;SACjK;QACD,OAAO,OAAO,CAAC;KAClB;CACJ;AACD,AAkCO,MAAM,eAAe,CAAC;;;;;;;IAOzB,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,4BAA4B,EAAE;QACnE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,4BAA4B,GAAG,4BAA4B,CAAC;QACjE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACpC,IAAI,CAAC,4BAA4B,GAAG,IAAI,GAAG,EAAE,CAAC;SACjD;QACD,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,sBAAsB,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;QACjG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACvD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC7E;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB;;;;IAID,iBAAiB,GAAG;QAChB,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI;YACxB,KAAK,CAAC;gBACF,OAAO,KAAK,CAAC;YACjB,KAAK,CAAC;gBACF,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;YACvD;gBACI,OAAO,IAAI,CAAC;SACnB;KACJ;;;;IAID,yBAAyB,GAAG,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE;;;;IAI1E,IAAI,WAAW,GAAG,EAAE,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;;;;;IAK5D,aAAa,CAAC,KAAK,EAAE;;;;;QAKjB,uBAAuB,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;QAC9G,IAAI,IAAI,CAAC,QAAQ,IAAI,eAAe,EAAE;YAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;YAC3C,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAChC;SACJ;aACI;YACD,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC;SAC3B;KACJ;;;;;;IAMD,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;KACzH;;;;IAID,aAAa,GAAG;QACZ,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC;SAClD;QACD,IAAI,CAAC,gBAAgB,sBAAsB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAChF,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC7D;KACJ;;;;IAID,YAAY,GAAG;QACX,IAAI,CAAC,QAAQ,IAAI,yBAAyB,CAAC;QAC3C,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB;;;;;IAKD,WAAW,CAAC,IAAI,EAAE;QACd,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB;;;;;;IAMD,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE;QACtB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;KAChE;;;;IAID,uBAAuB,GAAG,EAAE,OAAO,IAAI,CAAC,yBAAyB,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;;;;;IAK9F,cAAc,CAAC,MAAM,EAAE;QACnB,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SAC7C;;;;;;;QAOD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YACpD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC;YACtE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;SAC5C,CAAC,CAAC;QACH,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,gBAAgB,CAAC;KAC1D;;;;;;;;IAQD,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;QACtC,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SAC7C;QACD,uBAAuB,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;QAClE,uBAAuB,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACjF,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YAChC,uBAAuB,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAC7E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBACjD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC;oBAClE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;oBAChC,UAAU,CAAC;aAClB;YACD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAChC,CAAC,CAAC;KACN;;;;IAID,qBAAqB,GAAG;QACpB,uBAAuB,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;QACpD,uBAAuB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;YACjB,OAAO;QACX,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI;YAClB,uBAAuB,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;SACrC,CAAC,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YACnD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;aACjE;SACJ,CAAC,CAAC;KACN;;;;IAID,qBAAqB,GAAG;QACpB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YACnD,uBAAuB,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC7D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAChC,CAAC,CAAC;KACN;;;;IAID,gBAAgB,GAAG,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;;;;IAIjE,IAAI,UAAU,GAAG;QACb,uBAAuB,UAAU,GAAG,EAAE,CAAC;QACvC,KAAK,qBAAqB,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACrD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzB;QACD,OAAO,UAAU,CAAC;KACrB;;;;;IAKD,4BAA4B,CAAC,QAAQ,EAAE;QACnC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YAChD,uBAAuB,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC3D,uBAAuB,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE;gBAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC3C;SACJ,CAAC,CAAC;KACN;;;;IAID,cAAc,GAAG;QACb,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,uBAAuB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QACjD,uBAAuB,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAClD,uBAAuB,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;QACnF,qBAAqB,cAAc,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,KAAK;YACxC,uBAAuB,aAAa,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;gBACvC,uBAAuB,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;gBACnD,IAAI,KAAK,IAAID,UAAS,EAAE;oBACpB,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC3B;qBACI,IAAI,KAAK,IAAI,UAAU,EAAE;oBAC1B,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC5B;aACJ,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,EAAE;gBACV,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;aAClD;YACD,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACtC,CAAC,CAAC;QACH,uBAAuB,QAAQ,GAAG,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;QACpG,uBAAuB,SAAS,GAAG,cAAc,CAAC,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;;QAEvG,IAAI,OAAO,EAAE;YACT,uBAAuB,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC/C,uBAAuB,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAC1C,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAClB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAClB,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;SAC/B;QACD,OAAO,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC1I;CACJ;AACD,AAgCA,MAAM,kBAAkB,SAAS,eAAe,CAAC;;;;;;;;;;IAU7C,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,wBAAwB,GAAG,KAAK,EAAE;QAC9G,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;QACzD,IAAI,CAAC,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;KAC/F;;;;IAID,iBAAiB,GAAG,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;;;;IAIzD,cAAc,GAAG;QACb,qBAAqB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAChD,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/C,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,EAAE;YACxC,uBAAuB,YAAY,GAAG,EAAE,CAAC;YACzC,uBAAuB,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;YACpD,uBAAuB,WAAW,GAAG,KAAK,GAAG,SAAS,CAAC;;YAEvD,uBAAuB,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC1E,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/B,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACpC,uBAAuB,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC1E,gBAAgB,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;YACtD,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;;;;;;;;;;;;;;;YAgBpC,uBAAuB,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACpD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC9C,qBAAqB,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC1D,uBAAuB,SAAS,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACnE,uBAAuB,cAAc,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAC;gBACrE,EAAE,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC;gBACvD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACzB;;YAED,QAAQ,GAAG,SAAS,CAAC;YACrB,KAAK,GAAG,CAAC,CAAC;YACV,MAAM,GAAG,EAAE,CAAC;YACZ,SAAS,GAAG,YAAY,CAAC;SAC5B;QACD,OAAO,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KACrI;CACJ;AACD,AAcA;;;;;AAKA,SAAS,WAAW,CAAC,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE;IAC5C,uBAAuB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC;IAC9D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;CAC3C;;;;;;AAMD,SAAS,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE;IACrC,uBAAuB,MAAM,GAAG,EAAE,CAAC;IACnC,qBAAqB,aAAa,CAAC;IACnC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI;QACnB,IAAI,KAAK,KAAK,GAAG,EAAE;YACf,aAAa,GAAG,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxD,aAAa,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;SACjE;aACI;YACD,UAAU,mBAAmB,KAAK,GAAG,KAAK,EAAE,MAAM,CAAC,CAAC;SACvD;KACJ,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;CACjB;;AC79BD;;;;AAIA,AAIO,MAAM,SAAS,CAAC;;;;;IAKnB,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE;QACxB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,uBAAuB,GAAG,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,uBAAuB,YAAY,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3F,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;KAC5B;;;;;;;;;IASD,cAAc,CAAC,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,OAAO,EAAE,eAAe,EAAE;QACjF,uBAAuB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC,cAAc,CAAC,qBAAqB,cAAc,CAAC,CAAC;QACnI,uBAAuB,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,iBAAiB,CAAC,CAAC;QAC3I,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;QACjE,uBAAuB,MAAM,GAAG,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QACpL,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,uBAAuB,YAAY,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SACjC;QACD,OAAO,MAAM,CAAC;KACjB;CACJ;;AC3CD;;;;;;;;;;;;;;;AAeA,AAAO,MAAM,wBAAwB,CAAC;CACrC;AACD,AAkBA;;;AAGA,AAAO,MAAM,4BAA4B,CAAC;;;;;;IAMtC,qBAAqB,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE,OAAO,YAAY,CAAC,EAAE;;;;;;;;IAQpE,mBAAmB,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE;QACzE,yBAAyB,KAAK,EAAE;KACnC;CACJ;;ACvDD;;;;AAIA,AAEO,MAAM,4BAA4B,SAAS,wBAAwB,CAAC;;;;;;IAMvE,qBAAqB,CAAC,YAAY,EAAE,MAAM,EAAE;QACxC,OAAO,mBAAmB,CAAC,YAAY,CAAC,CAAC;KAC5C;;;;;;;;IAQD,mBAAmB,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE;QACzE,qBAAqB,IAAI,GAAG,EAAE,CAAC;QAC/B,uBAAuB,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;QACxD,IAAI,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;YAC1E,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC3B,IAAI,GAAG,IAAI,CAAC;aACf;iBACI;gBACD,uBAAuB,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBACjF,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;oBACvD,MAAM,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,oBAAoB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;iBACvF;aACJ;SACJ;QACD,OAAO,MAAM,GAAG,IAAI,CAAC;KACxB;CACJ;AACD,MAAuB,oBAAoB,GAAG,cAAc,CAAC,gUAAgU;KACxX,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;;;;AAKjB,SAAS,cAAc,CAAC,IAAI,EAAE;IAC1B,uBAAuB,GAAG,GAAG,EAAE,CAAC;IAChC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IACrC,OAAO,GAAG,CAAC;CACd;;ACjDD;;;;;;;AAOA,AAAoD;AACpD,AA0BA;;;;;;;;;;;;;;;AAeA,AAAO,SAAS,2BAA2B,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE;IAChM,OAAO;QACH,IAAI,EAAE,CAAC;QACP,OAAO;QACP,WAAW;QACX,mBAAmB;QACnB,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,eAAe;QACf,aAAa;QACb,cAAc;QACd,MAAM;KACT,CAAC;CACL;;ACjED;;;;AAIA,AAIA,MAAuB,YAAY,GAAG,EAAE,CAAC;AACzC,AAAO,MAAM,0BAA0B,CAAC;;;;;;IAMpC,WAAW,CAAC,YAAY,EAAE,GAAG,EAAE,YAAY,EAAE;QACzC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KACpC;;;;;;IAMD,KAAK,CAAC,YAAY,EAAE,SAAS,EAAE;QAC3B,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;KAChF;;;;;;;IAOD,WAAW,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE;QACnC,uBAAuB,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAClE,uBAAuB,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAClE,uBAAuB,YAAY,GAAG,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC;QAC7G,OAAO,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC;KAC/E;;;;;;;;;;;;;IAaD,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE;QAC1H,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,uBAAuB,yBAAyB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,YAAY,CAAC;QAC/G,uBAAuB,sBAAsB,GAAG,cAAc,IAAI,cAAc,CAAC,MAAM,IAAI,YAAY,CAAC;QACxG,uBAAuB,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,sBAAsB,EAAE,MAAM,CAAC,CAAC;QAC3G,uBAAuB,mBAAmB,GAAG,WAAW,IAAI,WAAW,CAAC,MAAM,IAAI,YAAY,CAAC;QAC/F,uBAAuB,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAClG,uBAAuB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACnD,uBAAuB,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/C,uBAAuB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAChD,uBAAuB,SAAS,GAAG,SAAS,KAAK,MAAM,CAAC;QACxD,uBAAuB,gBAAgB,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,yBAAyB,EAAE,mBAAmB,CAAC,EAAE,CAAC;QACxH,uBAAuB,SAAS,GAAG,uBAAuB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QAChN,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,OAAO,2BAA2B,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;SACtL;QACD,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI;YACpB,uBAAuB,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC;YACxC,uBAAuB,QAAQ,GAAG,eAAe,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YACxE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACxD,uBAAuB,SAAS,GAAG,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YAC1E,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAC1D,IAAI,GAAG,KAAK,OAAO,EAAE;gBACjB,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAC5B;SACJ,CAAC,CAAC;QACH,uBAAuB,mBAAmB,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QACvF,OAAO,2BAA2B,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,eAAe,EAAE,SAAS,EAAE,mBAAmB,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;KACtM;CACJ;AACD,AAQA;;;;;;AAMA,SAAS,yBAAyB,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE;IAClE,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;CAC3D;AACD,AAAO,MAAM,oBAAoB,CAAC;;;;;IAK9B,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;;;;;;IAMD,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE;QACxB,uBAAuB,WAAW,GAAG,EAAE,CAAC;QACxC,uBAAuB,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACpE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;YAC/B,uBAAuB,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,IAAI,EAAE;gBACf,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aAC/B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;YAChC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC3B,uBAAuB,QAAQ,qBAAqB,KAAK,CAAC,CAAC;gBAC3D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;oBAClC,qBAAqB,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC1C,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;wBAChB,GAAG,GAAG,iBAAiB,CAAC,GAAG,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;qBACxD;oBACD,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;iBAC3B,CAAC,CAAC;aACN;SACJ,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;KACtB;CACJ;;ACxID;;;;AAIA,AACA;;;;;;AAMA,AAAO,SAAS,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;IACpC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;CAC1C;;;;AAID,AAAO,MAAM,gBAAgB,CAAC;;;;;IAK1B,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI;YACtB,uBAAuB,aAAa,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;YACjF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;SAC9E,CAAC,CAAC;QACH,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC5C,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAC7C,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI;YAC3B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SACzF,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACzE;;;;IAID,IAAI,eAAe,GAAG,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE;;;;;;IAMzD,eAAe,CAAC,YAAY,EAAE,SAAS,EAAE;QACrC,uBAAuB,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;QACpG,OAAO,KAAK,IAAI,IAAI,CAAC;KACxB;;;;;;;IAOD,WAAW,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE;QACtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;KAC5E;CACJ;AACD,AAYA;;;;;AAKA,SAAS,wBAAwB,CAAC,WAAW,EAAE,MAAM,EAAE;IACnD,uBAAuB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,KAAK,IAAI,CAAC,CAAC;IACjE,uBAAuB,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,iBAAiB,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACxF,uBAAuB,UAAU,GAAG;QAChC,IAAI,EAAE,CAAC;QACP,SAAS;QACT,QAAQ;QACR,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,CAAC;QACb,QAAQ,EAAE,CAAC;KACd,CAAC;IACF,OAAO,IAAI,0BAA0B,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;CAC1E;;;;;;;AAOD,SAAS,iBAAiB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;IACxC,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;SACzB;KACJ;SACI,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC/B,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;KACzB;CACJ;;AC1GD;;;;AAIA,AAMA,MAAuB,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;AAC3E,AAAO,MAAM,uBAAuB,CAAC;;;;;IAKjC,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;KACrB;;;;;;IAMD,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE;QACnB,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,uBAAuB,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC/E,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,CAAC,2DAA2D,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACtG;aACI;YACD,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;SAC9B;KACJ;;;;;;;IAOD,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE;QACnC,uBAAuB,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;QAC3C,uBAAuB,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QACnI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;KACtF;;;;;;;IAOD,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;QAC9B,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,uBAAuB,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAClD,qBAAqB,YAAY,CAAC;QAClC,uBAAuB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QACjD,IAAI,GAAG,EAAE;YACL,YAAY,GAAG,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,eAAe,EAAE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,MAAM,CAAC,CAAC;YACrJ,YAAY,CAAC,OAAO,CAAC,IAAI,IAAI;gBACzB,uBAAuB,MAAM,GAAG,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBACjF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;aAC5D,CAAC,CAAC;SACN;aACI;YACD,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;YACpF,YAAY,GAAG,EAAE,CAAC;SACrB;QACD,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,CAAC,4DAA4D,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACvG;QACD,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;YACvC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;SACjH,CAAC,CAAC;QACH,uBAAuB,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI;YACnD,uBAAuB,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SAC3C,CAAC,CAAC;QACH,uBAAuB,MAAM,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QAC/B,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,OAAO,MAAM,CAAC;KACjB;;;;;IAKD,OAAO,CAAC,EAAE,EAAE;QACR,uBAAuB,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpD,MAAM,CAAC,OAAO,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAC7B,uBAAuB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACjC;KACJ;;;;;IAKD,UAAU,CAAC,EAAE,EAAE;QACX,uBAAuB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,CAAC,iDAAiD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;SAC7E;QACD,OAAO,MAAM,CAAC;KACjB;;;;;;;;IAQD,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE;;QAErC,uBAAuB,SAAS,GAAG,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC3E,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QACpE,OAAO,MAAM,GAAG,CAAC;KACpB;;;;;;;;IAQD,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE;QAChC,IAAI,OAAO,IAAI,UAAU,EAAE;YACvB,IAAI,CAAC,QAAQ,CAAC,EAAE,oBAAoB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9C,OAAO;SACV;QACD,IAAI,OAAO,IAAI,QAAQ,EAAE;YACrB,uBAAuB,OAAO,sBAAsB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAClC,OAAO;SACV;QACD,uBAAuB,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpD,QAAQ,OAAO;YACX,KAAK,MAAM;gBACP,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM;YACV,KAAK,SAAS;gBACV,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM;YACV,KAAK,QAAQ;gBACT,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM;YACV,KAAK,MAAM;gBACP,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM;YACV,KAAK,aAAa;gBACd,MAAM,CAAC,WAAW,CAAC,UAAU,mBAAmB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC3D,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACjB,MAAM;SACb;KACJ;CACJ;;ACzKD;;;;AAIA,AAIA,MAAuB,gBAAgB,GAAG,mBAAmB,CAAC;AAC9D,MAAuB,eAAe,GAAG,oBAAoB,CAAC;AAC9D,MAAuB,kBAAkB,GAAG,qBAAqB,CAAC;AAClE,MAAuB,iBAAiB,GAAG,sBAAsB,CAAC;AAClE,MAAuB,cAAc,GAAG,kBAAkB,CAAC;AAC3D,MAAuB,aAAa,GAAG,mBAAmB,CAAC;AAC3D,MAAuB,kBAAkB,GAAG,EAAE,CAAC;AAC/C,MAAuB,kBAAkB,GAAG;IACxC,WAAW,EAAE,EAAE;IACf,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,KAAK;IACnB,oBAAoB,EAAE,KAAK;CAC9B,CAAC;AACF,MAAuB,0BAA0B,GAAG;IAChD,WAAW,EAAE,EAAE;IACf,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,KAAK;IACnB,oBAAoB,EAAE,IAAI;CAC7B,CAAC;AACF,AAYA;;;AAGA,AAAsC;AACtC,AAgBO,MAAuB,YAAY,GAAG,cAAc,CAAC;;;;AAI5D,AAA2C;AAC3C,AAUO,MAAM,UAAU,CAAC;;;;;IAKpB,WAAW,CAAC,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,uBAAuB,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACtE,uBAAuB,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;QAC9D,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACP,uBAAuB,OAAO,GAAG,OAAO,mBAAmB,KAAK,EAAE,CAAC;YACnE,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;YACxB,IAAI,CAAC,OAAO,qBAAqB,OAAO,CAAC,CAAC;SAC7C;aACI;YACD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACrB;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;SAC5B;KACJ;;;;IAID,IAAI,MAAM,GAAG,EAAE,yBAAyB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE;;;;;IAK/D,aAAa,CAAC,OAAO,EAAE;QACnB,uBAAuB,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;QAClD,IAAI,SAAS,EAAE;YACX,uBAAuB,SAAS,sBAAsB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC5E,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;gBACnC,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;oBACzB,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;iBACrC;aACJ,CAAC,CAAC;SACN;KACJ;CACJ;AACD,AAQO,MAAuB,UAAU,GAAG,MAAM,CAAC;AAClD,AAAO,MAAuB,mBAAmB,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;AAC/E,AAAO,MAAuB,mBAAmB,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;AAC9E,AAAO,MAAM,4BAA4B,CAAC;;;;;;IAMtC,WAAW,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE;QAClC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,SAAS,GAAG,EAAE,CAAC;QACrC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KAC9C;;;;;;;;IAQD,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK,CAAC,iCAAiC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACzI;QACD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,CAAC,2CAA2C,EAAE,IAAI,CAAC,0CAA0C,CAAC,CAAC,CAAC;SACnH;QACD,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,CAAC,sCAAsC,EAAE,KAAK,CAAC,6BAA6B,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;SAC5H;QACD,uBAAuB,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QACxF,uBAAuB,IAAI,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;QACxD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,uBAAuB,kBAAkB,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QACvG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC1C,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;YACrD,kBAAkB,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC;SAClD;QACD,OAAO,MAAM;;;;YAIT,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM;gBAC1B,uBAAuB,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACvD,IAAI,KAAK,IAAI,CAAC,EAAE;oBACZ,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC9B;gBACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;oBACvB,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;iBACnC;aACJ,CAAC,CAAC;SACN,CAAC;KACL;;;;;;IAMD,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE;QAChB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;;YAEtB,OAAO,KAAK,CAAC;SAChB;aACI;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YAC3B,OAAO,IAAI,CAAC;SACf;KACJ;;;;;IAKD,WAAW,CAAC,IAAI,EAAE;QACd,uBAAuB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;SACxF;QACD,OAAO,OAAO,CAAC;KAClB;;;;;;;;IAQD,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,iBAAiB,GAAG,IAAI,EAAE;QAC3D,uBAAuB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC/D,uBAAuB,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAC7F,qBAAqB,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpF,IAAI,CAAC,kBAAkB,EAAE;YACrB,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,GAAG,EAAE,CAAC,CAAC;SACtE;QACD,qBAAqB,SAAS,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;QACjE,uBAAuB,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAChE,uBAAuB,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACtE,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE;YACrB,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAC5C;QACD,kBAAkB,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC;QAC1C,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS,GAAG,mBAAmB,CAAC;SACnC;aACI,IAAI,SAAS,KAAK,mBAAmB,EAAE;YACxC,OAAO,MAAM,CAAC;SACjB;QACD,uBAAuB,SAAS,GAAG,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC;;;;;;;QAOhE,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;;;YAGjD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC9C,uBAAuB,MAAM,GAAG,EAAE,CAAC;gBACnC,uBAAuB,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACnG,uBAAuB,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC7F,IAAI,MAAM,CAAC,MAAM,EAAE;oBACf,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBACpC;qBACI;oBACD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM;wBAC1B,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;wBACjC,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;qBAChC,CAAC,CAAC;iBACN;aACJ;YACD,OAAO;SACV;QACD,uBAAuB,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QACtG,gBAAgB,CAAC,OAAO,CAAC,MAAM,IAAI;;;;;YAK/B,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;gBACrF,MAAM,CAAC,OAAO,EAAE,CAAC;aACpB;SACJ,CAAC,CAAC;QACH,qBAAqB,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC1F,qBAAqB,oBAAoB,GAAG,KAAK,CAAC;QAClD,IAAI,CAAC,UAAU,EAAE;YACb,IAAI,CAAC,iBAAiB;gBAClB,OAAO;YACX,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACxC,oBAAoB,GAAG,IAAI,CAAC;SAC/B;QACD,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC,CAAC;QACzG,IAAI,CAAC,oBAAoB,EAAE;YACvB,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;SACrE;QACD,MAAM,CAAC,MAAM,CAAC,MAAM;YAChB,qBAAqB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACjC;YACD,uBAAuB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5E,IAAI,OAAO,EAAE;gBACT,qBAAqB,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrD,IAAI,KAAK,IAAI,CAAC,EAAE;oBACZ,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC5B;aACJ;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC;KACjB;;;;;IAKD,UAAU,CAAC,IAAI,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACxF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAK;YACnD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SAClG,CAAC,CAAC;KACN;;;;;IAKD,iBAAiB,CAAC,OAAO,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,uBAAuB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnF,IAAI,cAAc,EAAE;YAChB,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACjD;KACJ;;;;;;;IAOD,8BAA8B,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,GAAG,KAAK,EAAE;;;;QAIlE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;;;YAG7E,IAAI,GAAG,CAAC,YAAY,CAAC;gBACjB,OAAO;YACX,uBAAuB,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;YAC/E,IAAI,UAAU,CAAC,IAAI,EAAE;gBACjB,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;aACjF;iBACI;gBACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;aAC/B;SACJ,CAAC,CAAC;KACN;;;;;;;;IAQD,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,oBAAoB,EAAE,iBAAiB,EAAE;QAC7E,uBAAuB,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACjF,IAAI,aAAa,EAAE;YACf,uBAAuB,OAAO,GAAG,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,WAAW,IAAI;;;gBAG9C,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;oBAC7B,uBAAuB,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;oBAClG,IAAI,MAAM,EAAE;wBACR,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACxB;iBACJ;aACJ,CAAC,CAAC;YACH,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACnE,IAAI,oBAAoB,EAAE;oBACtB,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;iBACrF;gBACD,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;KAChB;;;;;IAKD,8BAA8B,CAAC,OAAO,EAAE;QACpC,uBAAuB,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,SAAS,EAAE;YACX,uBAAuB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YACnD,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI;gBAC1B,uBAAuB,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACnD,IAAI,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC;oBAChC,OAAO;gBACX,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACjC,uBAAuB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC7D,uBAAuB,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;gBAC/D,uBAAuB,aAAa,sBAAsB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtG,uBAAuB,SAAS,GAAG,aAAa,CAAC,WAAW,CAAC,IAAI,mBAAmB,CAAC;gBACrF,uBAAuB,OAAO,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC5D,uBAAuB,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBAC7F,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACb,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,SAAS;oBACT,OAAO;oBACP,MAAM;oBACN,oBAAoB,EAAE,IAAI;iBAC7B,CAAC,CAAC;aACN,CAAC,CAAC;SACN;KACJ;;;;;;IAMD,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE;QACzB,uBAAuB,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7C,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;SAC/D;;QAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;YAClD,OAAO;;;QAGX,qBAAqB,iCAAiC,GAAG,KAAK,CAAC;QAC/D,IAAI,MAAM,CAAC,eAAe,EAAE;YACxB,uBAAuB,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;;;;;YAKjH,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;gBACzC,iCAAiC,GAAG,IAAI,CAAC;aAC5C;iBACI;gBACD,qBAAqB,MAAM,GAAG,OAAO,CAAC;gBACtC,OAAO,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE;oBAC/B,uBAAuB,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACrE,IAAI,QAAQ,EAAE;wBACV,iCAAiC,GAAG,IAAI,CAAC;wBACzC,MAAM;qBACT;iBACJ;aACJ;SACJ;;;;;QAKD,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;;;QAG7C,IAAI,iCAAiC,EAAE;YACnC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACjE;aACI;;;YAGD,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC/C;KACJ;;;;;;IAMD,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE;;;;;IAKvE,sBAAsB,CAAC,WAAW,EAAE;QAChC,uBAAuB,YAAY,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;YACzB,uBAAuB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC7C,IAAI,MAAM,CAAC,SAAS;gBAChB,OAAO;YACX,uBAAuB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/C,uBAAuB,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACvE,IAAI,SAAS,EAAE;gBACX,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;oBAC5B,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE;wBACpC,uBAAuB,SAAS,GAAG,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC9H,mBAAmB,SAAS,GAAG,OAAO,CAAC,GAAG,WAAW,CAAC;wBACtD,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;qBAC9E;iBACJ,CAAC,CAAC;aACN;YACD,IAAI,MAAM,CAAC,gBAAgB,EAAE;gBACzB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM;;;oBAG1B,MAAM,CAAC,OAAO,EAAE,CAAC;iBACpB,CAAC,CAAC;aACN;iBACI;gBACD,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC5B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;;;YAG/B,uBAAuB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACtD,uBAAuB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACtD,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC;aAClB;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7E,CAAC,CAAC;KACN;;;;;IAKD,OAAO,CAAC,OAAO,EAAE;QACb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACvC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;KAClE;;;;;IAKD,mBAAmB,CAAC,OAAO,EAAE;QACzB,qBAAqB,YAAY,GAAG,KAAK,CAAC;QAC1C,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACnC,YAAY,GAAG,IAAI,CAAC;QACxB,YAAY;YACR,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,YAAY,CAAC;QAC1F,OAAO,YAAY,CAAC;KACvB;CACJ;AACD,AAkBA;;;AAGA,AAAsC;AACtC,AAQO,MAAM,yBAAyB,CAAC;;;;;IAKnC,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK,GAAG,CAAC;KACtD;;;;;;;IAOD,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;;;;IAIlF,IAAI,aAAa,GAAG;QAChB,uBAAuB,OAAO,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,IAAI;YAC9B,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI;gBACzB,IAAI,MAAM,CAAC,MAAM,EAAE;oBACf,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACxB;aACJ,CAAC,CAAC;SACN,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;KAClB;;;;;;IAMD,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE;QACtC,uBAAuB,EAAE,GAAG,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAC7F,IAAI,WAAW,CAAC,UAAU,EAAE;YACxB,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;SAC/C;aACI;;;;YAID,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;;;;;;YAM1C,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;SACzC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;KAClD;;;;;;IAMD,qBAAqB,CAAC,EAAE,EAAE,WAAW,EAAE;QACnC,uBAAuB,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9D,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,qBAAqB,KAAK,GAAG,KAAK,CAAC;YACnC,KAAK,qBAAqB,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC9C,uBAAuB,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;oBACrE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACzC,KAAK,GAAG,IAAI,CAAC;oBACb,MAAM;iBACT;aACJ;YACD,IAAI,CAAC,KAAK,EAAE;gBACR,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;aACxC;SACJ;aACI;YACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,EAAE,CAAC;KACb;;;;;;IAMD,QAAQ,CAAC,WAAW,EAAE,WAAW,EAAE;QAC/B,qBAAqB,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,EAAE,EAAE;YACL,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;SACvD;QACD,OAAO,EAAE,CAAC;KACb;;;;;;;IAOD,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE;QACxC,qBAAqB,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YAClC,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;KACJ;;;;;;IAMD,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE;QAC1B,IAAI,CAAC,WAAW;YACZ,OAAO;QACX,uBAAuB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC9D,IAAI,CAAC,UAAU,CAAC,MAAM;YAClB,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC1C,uBAAuB,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC/D,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACxC;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;KAC5D;;;;;IAKD,eAAe,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;;;;;IAKzD,wBAAwB,CAAC,OAAO,EAAE;;;;;;QAM9B,uBAAuB,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9C,uBAAuB,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,aAAa,EAAE;YACf,uBAAuB,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnD,uBAAuB,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;gBACjE,IAAI,IAAI,EAAE;oBACN,uBAAuB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBACvD,IAAI,EAAE,EAAE;wBACJ,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;qBACtB;iBACJ;aACJ;SACJ;QACD,OAAO,UAAU,CAAC;KACrB;;;;;;;;IAQD,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;QACvC,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;KAChB;;;;;;;;IAQD,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE;QACnD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACvB,OAAO;;;QAGX,uBAAuB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1E,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;YAClC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;SACjC;;;;QAID,IAAI,WAAW,EAAE;YACb,uBAAuB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;;;;;;;YAO9D,IAAI,EAAE,EAAE;gBACJ,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAClC;SACJ;;QAED,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SACrC;KACJ;;;;;IAKD,mBAAmB,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;;;;;;IAM3E,qBAAqB,CAAC,OAAO,EAAE,KAAK,EAAE;QAClC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAClC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAChC,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aACzC;SACJ;aACI,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACtC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;SAC5C;KACJ;;;;;;;IAOD,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;QACtC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC1C,OAAO;SACV;QACD,uBAAuB,EAAE,GAAG,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACnF,IAAI,EAAE,EAAE;YACJ,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACnC;aACI;YACD,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACnE;KACJ;;;;;;;;IAQD,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE;QAC9D,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,OAAO,CAAC,YAAY,CAAC,GAAG;YACpB,WAAW;YACX,aAAa,EAAE,OAAO,EAAE,YAAY;YACpC,oBAAoB,EAAE,KAAK;SAC9B,CAAC;KACL;;;;;;;;;IASD,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE;QAChD,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;SACnF;QACD,OAAO,MAAM,GAAG,CAAC;KACpB;;;;;;;;IAQD,iBAAiB,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE;QACnE,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;KACvM;;;;;IAKD,sBAAsB,CAAC,gBAAgB,EAAE;QACrC,qBAAqB,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC/F,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,CAAC,CAAC;QAC7E,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC;YACtC,OAAO;QACX,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC5E,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC;KACpF;;;;;IAKD,iCAAiC,CAAC,OAAO,EAAE;QACvC,uBAAuB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI;;;;gBAItB,IAAI,MAAM,CAAC,MAAM,EAAE;oBACf,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC;iBAClC;qBACI;oBACD,MAAM,CAAC,OAAO,EAAE,CAAC;iBACpB;aACJ,CAAC,CAAC;SACN;QACD,uBAAuB,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,QAAQ,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,CAAC,GAAG,mBAAmB,CAAC,CAAC;SAC7F;KACJ;;;;;IAKD,qCAAqC,CAAC,OAAO,EAAE;QAC3C,uBAAuB,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3E,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;SAC9C;KACJ;;;;IAID,iBAAiB,GAAG;QAChB,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI;YAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACrB,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC;aACpE;iBACI;gBACD,OAAO,EAAE,CAAC;aACb;SACJ,CAAC,CAAC;KACN;;;;;IAKD,gBAAgB,CAAC,OAAO,EAAE;QACtB,uBAAuB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1E,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;;YAElC,OAAO,CAAC,YAAY,CAAC,GAAG,kBAAkB,CAAC;YAC3C,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBACrC,uBAAuB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACtE,IAAI,EAAE,EAAE;oBACJ,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;iBACjC;aACJ;YACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;SAC3D;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE;YACxD,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC9C;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YAChE,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC9C,CAAC,CAAC;KACN;;;;;IAKD,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;QACpB,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;YACvF,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAChC;QACD,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;YAC5D,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1E,uBAAuB,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC5D,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;aACjC;SACJ;QACD,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;aACzB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE;YACjE,uBAAuB,UAAU,GAAG,EAAE,CAAC;YACvC,IAAI;gBACA,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;aAC5D;oBACO;gBACJ,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACzD,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;iBACnB;aACJ;SACJ;aACI;YACD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1E,uBAAuB,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAChE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAClC;SACJ;QACD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;;;YAI3B,uBAAuB,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;YACrD,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;YACxB,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;aAChF;iBACI;gBACD,QAAQ,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;aAChC;SACJ;KACJ;;;;;IAKD,WAAW,CAAC,MAAM,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,CAAC,+EAA+E,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1H;;;;;;IAMD,gBAAgB,CAAC,UAAU,EAAE,WAAW,EAAE;QACtC,uBAAuB,YAAY,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAClE,uBAAuB,cAAc,GAAG,EAAE,CAAC;QAC3C,uBAAuB,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrD,uBAAuB,kBAAkB,GAAG,EAAE,CAAC;QAC/C,uBAAuB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACnD,uBAAuB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACvD,uBAAuB,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;QACxD,uBAAuB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACvD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,IAAI;YAC/B,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9B,uBAAuB,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YAC7F,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnE,mBAAmB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;aACpD;SACJ,CAAC,CAAC;QACH,uBAAuB,QAAQ,GAAG,WAAW,EAAE,CAAC;QAChD,uBAAuB,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACpF,uBAAuB,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;;;;QAIpG,uBAAuB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACnD,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAC3B,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK;YAClC,uBAAuB,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;YACzD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACrC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;SACpD,CAAC,CAAC;QACH,uBAAuB,aAAa,GAAG,EAAE,CAAC;QAC1C,uBAAuB,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QACpD,uBAAuB,2BAA2B,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/D,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1E,uBAAuB,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAChE,uBAAuB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YAC1E,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;gBAClC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC9B,IAAI,OAAO,CAAC,YAAY,EAAE;oBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC7F;qBACI;oBACD,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;iBAC5C;aACJ;SACJ;QACD,uBAAuB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACnD,uBAAuB,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACrG,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK;YAClC,uBAAuB,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;YACzD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACrC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;SACpD,CAAC,CAAC;QACH,UAAU,CAAC,IAAI,CAAC,MAAM;YAClB,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK;gBAClC,uBAAuB,SAAS,sBAAsB,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClF,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;aACvD,CAAC,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK;gBAClC,uBAAuB,SAAS,sBAAsB,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClF,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;aACvD,CAAC,CAAC;YACH,aAAa,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;SACzE,CAAC,CAAC;QACH,uBAAuB,UAAU,GAAG,EAAE,CAAC;QACvC,uBAAuB,oBAAoB,GAAG,EAAE,CAAC;QACjD,KAAK,qBAAqB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACvE,uBAAuB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACnD,EAAE,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI;gBACpD,uBAAuB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC7C,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,uBAAuB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC/C,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;oBAC9D,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO;iBACV;gBACD,uBAAuB,cAAc,sBAAsB,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1F,uBAAuB,cAAc,sBAAsB,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1F,uBAAuB,WAAW,sBAAsB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC;gBACtI,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE;oBACjD,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACvC,OAAO;iBACV;;;gBAGD,IAAI,KAAK,CAAC,oBAAoB,EAAE;oBAC5B,MAAM,CAAC,OAAO,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;oBACnE,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACjE,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5B,OAAO;iBACV;;;;;;gBAMD,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAC;gBACvE,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;gBACpD,uBAAuB,KAAK,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;gBAChE,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/B,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,IAAI,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC3G,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAK;oBACtD,uBAAuB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACtD,IAAI,KAAK,CAAC,MAAM,EAAE;wBACd,qBAAqB,MAAM,sBAAsB,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;wBACpF,IAAI,CAAC,MAAM,EAAE;4BACT,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;yBACxD;wBACD,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;qBAC3C;iBACJ,CAAC,CAAC;gBACH,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAK;oBACvD,uBAAuB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACtD,qBAAqB,MAAM,sBAAsB,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrF,IAAI,CAAC,MAAM,EAAE;wBACT,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;qBACzD;oBACD,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC3C,CAAC,CAAC;aACN,CAAC,CAAC;SACN;QACD,IAAI,oBAAoB,CAAC,MAAM,EAAE;YAC7B,uBAAuB,MAAM,GAAG,EAAE,CAAC;YACnC,oBAAoB,CAAC,OAAO,CAAC,WAAW,IAAI;gBACxC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC;gBAChE,EAAE,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACxE,CAAC,CAAC;YACH,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC5B;QACD,uBAAuB,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;;;;;QAKzD,uBAAuB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACvD,kBAAkB,CAAC,OAAO,CAAC,KAAK,IAAI;YAChC,uBAAuB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/C,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC3B,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC1C,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;aAClG;SACJ,CAAC,CAAC;QACH,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI;YAC7B,uBAAuB,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAChD,uBAAuB,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAChI,eAAe,CAAC,OAAO,CAAC,UAAU,IAAI;gBAClC,eAAe,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACrE,UAAU,CAAC,OAAO,EAAE,CAAC;aACxB,CAAC,CAAC;SACN,CAAC,CAAC;;;;;;;;QAQH,uBAAuB,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI;YAC/D,OAAO,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;SAClF,CAAC,CAAC;;QAEH,uBAAuB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QACjD,uBAAuB,oBAAoB,GAAG,qBAAqB,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,2BAA2B,EAAE,oBAAoB,EAAE,UAAU,CAAC,CAAC;QAC/J,oBAAoB,CAAC,OAAO,CAAC,IAAI,IAAI;YACjC,IAAI,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,EAAE;gBACzE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3B;SACJ,CAAC,CAAC;;QAEH,uBAAuB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAChD,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK;YAClC,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,mBAAmB,EAAEA,UAAS,CAAC,CAAC;SACpG,CAAC,CAAC;QACH,YAAY,CAAC,OAAO,CAAC,IAAI,IAAI;YACzB,uBAAuB,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACtD,uBAAuB,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpD,aAAa,CAAC,GAAG,CAAC,IAAI,oBAAoB,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;SAC5E,CAAC,CAAC;QACH,uBAAuB,WAAW,GAAG,EAAE,CAAC;QACxC,uBAAuB,UAAU,GAAG,EAAE,CAAC;QACvC,uBAAuB,oCAAoC,GAAG,EAAE,CAAC;QACjE,kBAAkB,CAAC,OAAO,CAAC,KAAK,IAAI;YAChC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;;;YAG/C,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC3B,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAClC,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACjE,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5B,OAAO;iBACV;;;;;;;gBAOD,qBAAqB,mBAAmB,GAAG,oCAAoC,CAAC;gBAChF,IAAI,mBAAmB,CAAC,IAAI,GAAG,CAAC,EAAE;oBAC9B,qBAAqB,GAAG,GAAG,OAAO,CAAC;oBACnC,uBAAuB,YAAY,GAAG,EAAE,CAAC;oBACzC,OAAO,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE;wBACzB,uBAAuB,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBACrE,IAAI,cAAc,EAAE;4BAChB,mBAAmB,GAAG,cAAc,CAAC;4BACrC,MAAM;yBACT;wBACD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;qBAC1B;oBACD,YAAY,CAAC,OAAO,CAAC,MAAM,IAAI,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC,CAAC;iBACxF;gBACD,uBAAuB,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;gBAClK,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;gBAClC,IAAI,mBAAmB,KAAK,oCAAoC,EAAE;oBAC9D,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC5B;qBACI;oBACD,uBAAuB,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;oBACtF,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE;wBACvC,MAAM,CAAC,YAAY,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;qBAC5D;oBACD,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC/B;aACJ;iBACI;gBACD,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;;;;gBAIjE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAClC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC/B;aACJ;SACJ,CAAC,CAAC;;QAEH,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI;;;YAGzB,uBAAuB,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjF,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAC/C,uBAAuB,WAAW,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;gBAC5E,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;aACrC;SACJ,CAAC,CAAC;;;;QAIH,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI;YAC7B,IAAI,MAAM,CAAC,YAAY,EAAE;gBACrB,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aAChD;iBACI;gBACD,MAAM,CAAC,OAAO,EAAE,CAAC;aACpB;SACJ,CAAC,CAAC;;;;QAIH,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,uBAAuB,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAClD,uBAAuB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YAC1E,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;;;;YAItC,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY;gBAC/B,SAAS;YACb,qBAAqB,OAAO,GAAG,EAAE,CAAC;;;;YAIlC,IAAI,eAAe,CAAC,IAAI,EAAE;gBACtB,qBAAqB,oBAAoB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACzE,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,EAAE;oBACrD,OAAO,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;iBACzC;gBACD,qBAAqB,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;gBACpG,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACnE,qBAAqB,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnF,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;wBACzC,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;qBACnC;iBACJ;aACJ;YACD,uBAAuB,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACzE,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtB,6BAA6B,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;aAC/D;iBACI;gBACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAClC;SACJ;;QAED,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI;YAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,MAAM;gBAChB,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,uBAAuB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACjC,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,EAAE,CAAC;SACjB,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;KACtB;;;;;;IAMD,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE;QACtC,qBAAqB,YAAY,GAAG,KAAK,CAAC;QAC1C,uBAAuB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1E,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa;YAChC,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;YAClC,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC;YACzC,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;YACjC,YAAY,GAAG,IAAI,CAAC;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC;KACzF;;;;;IAKD,UAAU,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;;;;;IAKvD,wBAAwB,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;;;;;;;;;IASzE,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE;QACnF,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,gBAAgB,EAAE;YAClB,uBAAuB,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACzF,IAAI,qBAAqB,EAAE;gBACvB,OAAO,GAAG,qBAAqB,CAAC;aACnC;SACJ;aACI;YACD,uBAAuB,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3E,IAAI,cAAc,EAAE;gBAChB,uBAAuB,kBAAkB,GAAG,CAAC,YAAY,IAAI,YAAY,IAAI,UAAU,CAAC;gBACxF,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI;oBAC7B,IAAI,MAAM,CAAC,MAAM;wBACb,OAAO;oBACX,IAAI,CAAC,kBAAkB,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW;wBACxD,OAAO;oBACX,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACxB,CAAC,CAAC;aACN;SACJ;QACD,IAAI,WAAW,IAAI,WAAW,EAAE;YAC5B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI;gBAC/B,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;oBAChD,OAAO,KAAK,CAAC;gBACjB,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;oBAChD,OAAO,KAAK,CAAC;gBACjB,OAAO,IAAI,CAAC;aACf,CAAC,CAAC;SACN;QACD,OAAO,OAAO,CAAC;KAClB;;;;;;;IAOD,qBAAqB,CAAC,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAE;QACnE,uBAAuB,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC7D,uBAAuB,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;QAGzD,uBAAuB,iBAAiB,GAAG,WAAW,CAAC,mBAAmB,GAAG,SAAS,GAAG,WAAW,CAAC;QACrG,uBAAuB,iBAAiB,GAAG,WAAW,CAAC,mBAAmB,GAAG,SAAS,GAAG,WAAW,CAAC;QACrG,KAAK,uBAAuB,mBAAmB,IAAI,WAAW,CAAC,SAAS,EAAE;YACtE,uBAAuB,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;YAC7D,uBAAuB,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;YAClE,uBAAuB,OAAO,GAAG,eAAe,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACrF,uBAAuB,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;YACxJ,eAAe,CAAC,OAAO,CAAC,MAAM,IAAI;gBAC9B,uBAAuB,UAAU,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;gBAC9E,IAAI,UAAU,CAAC,aAAa,EAAE;oBAC1B,UAAU,CAAC,aAAa,EAAE,CAAC;iBAC9B;gBACD,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACxB,CAAC,CAAC;SACN;;;QAGD,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;KACpD;;;;;;;;;;IAUD,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,EAAE;QAC7G,uBAAuB,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC7D,uBAAuB,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;QAGzD,uBAAuB,iBAAiB,GAAG,EAAE,CAAC;QAC9C,uBAAuB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACvD,uBAAuB,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAClD,uBAAuB,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,IAAI;YACpF,uBAAuB,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;YAC7D,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;YAEjC,uBAAuB,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;YACvD,IAAI,OAAO,IAAI,OAAO,CAAC,oBAAoB;gBACvC,OAAO,IAAI,mBAAmB,EAAE,CAAC;YACrC,uBAAuB,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;YAClE,uBAAuB,eAAe,GAAG,mBAAmB,CAAC,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,kBAAkB;iBACjH,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC;iBAC5B,MAAM,CAAC,CAAC,IAAI;;;;;gBAKb,uBAAuB,EAAE,qBAAqB,CAAC,CAAC,CAAC;gBACjD,OAAO,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,CAAC;aACtD,CAAC,CAAC;YACH,uBAAuB,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC7D,uBAAuB,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC/D,uBAAuB,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,mBAAmB,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YACpJ,uBAAuB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;;;YAGnG,IAAI,mBAAmB,CAAC,WAAW,IAAI,iBAAiB,EAAE;gBACtD,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aAC/B;YACD,IAAI,gBAAgB,EAAE;gBAClB,uBAAuB,aAAa,GAAG,IAAI,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBACxG,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACpC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACzC;YACD,OAAO,MAAM,CAAC;SACjB,CAAC,CAAC;QACH,iBAAiB,CAAC,OAAO,CAAC,MAAM,IAAI;YAChC,eAAe,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/E,MAAM,CAAC,MAAM,CAAC,MAAM,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;SACjG,CAAC,CAAC;QACH,mBAAmB,CAAC,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC,CAAC;QAClF,uBAAuB,MAAM,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;QACnE,MAAM,CAAC,SAAS,CAAC,MAAM;YACnB,mBAAmB,CAAC,OAAO,CAAC,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC,CAAC;YACrF,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;SAChD,CAAC,CAAC;;;QAGH,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,eAAe,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QACrG,OAAO,MAAM,CAAC;KACjB;;;;;;;IAOD,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE;QAClD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;SAC5I;;;QAGD,OAAO,IAAI,mBAAmB,EAAE,CAAC;KACpC;CACJ;AACD,AAsCO,MAAM,yBAAyB,CAAC;;;;;;IAMnC,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE;QAC3C,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACzC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB;;;;;IAKD,aAAa,CAAC,MAAM,EAAE;QAClB,IAAI,IAAI,CAAC,mBAAmB;YACxB,OAAO;QACX,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI;YAChD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;SACxG,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,mBAAmB,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;KAC5C;;;;IAID,aAAa,GAAG,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;;;;;IAKxC,gBAAgB,CAAC,MAAM,EAAE;QACrB,uBAAuB,CAAC,qBAAqB,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,CAAC,eAAe,EAAE;YACnB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;SACpD;QACD,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACnC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KAC1C;;;;;;IAMD,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;QACxB,eAAe,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACnE;;;;;IAKD,MAAM,CAAC,EAAE,EAAE;QACP,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KAC3B;;;;;IAKD,OAAO,CAAC,EAAE,EAAE;QACR,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAC5B;;;;;IAKD,SAAS,CAAC,EAAE,EAAE;QACV,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC9B;;;;IAID,IAAI,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE;;;;IAI/B,UAAU,GAAG,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE;;;;IAIxE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE;;;;IAI/C,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE;;;;IAIjD,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE;;;;IAIrD,MAAM,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;;;;IAInC,OAAO,GAAG;QACN,mBAAmB,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;;;;IAID,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE;;;;;IAKjD,WAAW,CAAC,CAAC,EAAE;QACX,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC/B;KACJ;;;;IAID,WAAW,GAAG,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE;;;;IAItE,IAAI,SAAS,GAAG,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;;;;;IAKlD,eAAe,CAAC,SAAS,EAAE;QACvB,uBAAuB,CAAC,qBAAqB,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,CAAC,eAAe,EAAE;YACnB,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SAChC;KACJ;CACJ;AACD,AAsBA;;;;;;AAMA,SAAS,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IACzC,qBAAqB,aAAa,CAAC;IACnC,IAAI,GAAG,YAAY,GAAG,EAAE;QACpB,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,aAAa,EAAE;YACf,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtB,uBAAuB,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC5D,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClC;YACD,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC3B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACnB;SACJ;KACJ;SACI;QACD,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,IAAI,aAAa,EAAE;YACf,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtB,uBAAuB,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC5D,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClC;YACD,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC3B,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;aACnB;SACJ;KACJ;IACD,OAAO,aAAa,CAAC;CACxB;;;;;AAKD,SAAS,qBAAqB,CAAC,KAAK,EAAE;;;;IAIlC,OAAO,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;CACvC;;;;;AAKD,SAAS,aAAa,CAAC,IAAI,EAAE;IACzB,OAAO,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;CACzC;;;;;AAKD,SAAS,mBAAmB,CAAC,SAAS,EAAE;IACpC,OAAO,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,MAAM,CAAC;CACtD;;;;;;AAMD,SAAS,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE;IAClC,uBAAuB,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;IACxD,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC;IACvD,OAAO,QAAQ,CAAC;CACnB;;;;;;;;;AASD,SAAS,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE;IACvF,uBAAuB,SAAS,GAAG,EAAE,CAAC;IACtC,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnE,uBAAuB,cAAc,GAAG,EAAE,CAAC;IAC3C,eAAe,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK;QACxC,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI;YAClB,uBAAuB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;;;YAG/F,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC7B,OAAO,CAAC,YAAY,CAAC,GAAG,0BAA0B,CAAC;gBACnD,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAChC;SACJ,CAAC,CAAC;QACH,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAClC,CAAC,CAAC;;;IAGH,qBAAqB,CAAC,GAAG,CAAC,CAAC;IAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnE,OAAO,cAAc,CAAC;CACzB;;;;;;AAMD,SAAS,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE;IAChC,uBAAuB,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;IAC3C,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7C,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;QACjB,OAAO,OAAO,CAAC;IACnB,uBAAuB,SAAS,GAAG,CAAC,CAAC;IACrC,uBAAuB,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IAChD,uBAAuB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;;;;;IAKhD,SAAS,OAAO,CAAC,IAAI,EAAE;QACnB,IAAI,CAAC,IAAI;YACL,OAAO,SAAS,CAAC;QACrB,qBAAqB,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,IAAI;YACJ,OAAO,IAAI,CAAC;QAChB,uBAAuB,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAChD,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAErB,IAAI,GAAG,MAAM,CAAC;SACjB;aACI,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAE1B,IAAI,GAAG,SAAS,CAAC;SACpB;aACI;;YAED,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;SAC1B;QACD,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;KACf;IACD,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI;QAClB,uBAAuB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,IAAI,KAAK,SAAS,EAAE;6BACH,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;SACrD;KACJ,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;CAClB;AACD,MAAuB,iBAAiB,GAAG,WAAW,CAAC;AACvD,AAcA;;;;;AAKA,SAAS,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE;IAClC,IAAI,OAAO,CAAC,SAAS,EAAE;QACnB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KACpC;SACI;QACD,qBAAqB,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;SAC7C;QACD,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;KAC7B;CACJ;;;;;;AAMD,SAAS,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE;IACrC,IAAI,OAAO,CAAC,SAAS,EAAE;QACnB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KACvC;SACI;QACD,qBAAqB,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC1D,IAAI,OAAO,EAAE;YACT,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;SAC7B;KACJ;CACJ;;;;;;;AAOD,SAAS,6BAA6B,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;IAC7D,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;CAC/E;;;;;AAKD,SAAS,mBAAmB,CAAC,OAAO,EAAE;IAClC,uBAAuB,YAAY,GAAG,EAAE,CAAC;IACzC,yBAAyB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACjD,OAAO,YAAY,CAAC;CACvB;;;;;;AAMD,SAAS,yBAAyB,CAAC,OAAO,EAAE,YAAY,EAAE;IACtD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtD,uBAAuB,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,IAAI,MAAM,YAAYE,qBAAoB,EAAE;YACxC,yBAAyB,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;SAC3D;aACI;YACD,YAAY,CAAC,IAAI,mBAAmB,MAAM,EAAE,CAAC;SAChD;KACJ;CACJ;;;;;;AAMD,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IACrB,uBAAuB,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3C,uBAAuB,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM;QACtB,OAAO,KAAK,CAAC;IACjB,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,uBAAuB,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;YAC9C,OAAO,KAAK,CAAC;KACpB;IACD,OAAO,IAAI,CAAC;CACf;;;;;;;AAOD,SAAS,sBAAsB,CAAC,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE;IAChF,uBAAuB,SAAS,GAAG,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrE,IAAI,CAAC,SAAS;QACV,OAAO,KAAK,CAAC;IACjB,qBAAqB,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACjE,IAAI,QAAQ,EAAE;QACV,SAAS,CAAC,OAAO,CAAC,IAAI,qBAAqB,EAAE,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;KACtE;SACI;QACD,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;KAC/C;IACD,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC;CACf;;ACh8DD;;;;AAIA,AAKO,MAAM,eAAe,CAAC;;;;;IAKzB,WAAW,CAAC,OAAO,EAAE,UAAU,EAAE;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK,GAAG,CAAC;QACnD,IAAI,CAAC,iBAAiB,GAAG,IAAI,yBAAyB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAC7G;;;;;;;;;IASD,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;QACnE,uBAAuB,QAAQ,GAAG,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC;QAC3D,qBAAqB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE;YACV,uBAAuB,MAAM,GAAG,EAAE,CAAC;YACnC,uBAAuB,GAAG,qBAAqB,iBAAiB,CAAC,IAAI,CAAC,OAAO,oBAAoB,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;YACrH,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,uDAAuD,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACnI;YACD,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;SAC1C;QACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KACtE;;;;;;IAMD,QAAQ,CAAC,WAAW,EAAE,WAAW,EAAE;QAC/B,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;KAC7D;;;;;;IAMD,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE;QAC1B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;KACxD;;;;;;;;IAQD,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE;QACjD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;KACjF;;;;;;;IAOD,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;QACpC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KACpE;;;;;;IAMD,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE;QAChC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAClE;;;;;;;;IAQD,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC3C,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC3B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACpD,uBAAuB,IAAI,qBAAqB,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SAC3D;aACI;YACD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SACzE;KACJ;;;;;;;;;IASD,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE;;QAE1D,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC5B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;SACrE;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KAC/F;;;;;IAKD,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;;;;IAItE,IAAI,OAAO,GAAG;QACV,OAAO,mBAAmB,IAAI,CAAC,iBAAiB,CAAC,OAAO;aACnD,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;KAChE;;;;IAID,iBAAiB,GAAG,EAAE,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,EAAE;CAC7E;;ACxID;;;;AAIA,AACO,MAAM,mBAAmB,CAAC;;;;;;;IAO7B,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,EAAE,EAAE;QAC3D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,qBAAqB,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,IAAI,8BAA8B,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7D,eAAe,CAAC,OAAO,CAAC,MAAM,IAAI;gBAC9B,qBAAqB,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;aACjF,CAAC,CAAC;SACN;KACJ;;;;IAID,SAAS,GAAG;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;KACJ;;;;IAID,IAAI,GAAG;QACH,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;KACpC;;;;IAID,YAAY,GAAG;QACX,IAAI,IAAI,CAAC,YAAY;YACjB,OAAO;QACX,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,uBAAuB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QAC3F,uBAAuB,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7E,IAAI,kBAAkB,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE;YAC/C,qBAAqB,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACrD,qBAAqB,iBAAiB,GAAG,EAAE,CAAC;YAC5C,kBAAkB,CAAC,OAAO,CAAC,IAAI,IAAI;gBAC/B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBACxC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAChC;gBACD,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aACtD,CAAC,CAAC;YACH,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAC1B,uBAAuB,IAAI,GAAG,IAAI,CAAC;;gBAEnC,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACxD,qBAAqB,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACvC,iBAAiB,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;wBACtC,EAAE,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBAChD,CAAC,CAAC;iBACN;aACJ;SACJ;QACD,mBAAmB,IAAI,GAAG,SAAS;YAC/B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACrE,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAC9E,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;KACrE;;;;IAID,yBAAyB,GAAG;;QAExB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;aACI;YACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SAC1B;KACJ;;;;;;;;IAQD,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;;;QAG9C,yBAAyB,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;KACpE;;;;;IAKD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;;;;;IAK1C,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;;;;;IAKxC,SAAS,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;;;;IAI9C,IAAI,GAAG;QACH,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;KACzB;;;;IAID,KAAK,GAAG;QACJ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;KAC1B;;;;IAID,MAAM,GAAG;QACL,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAC3B;;;;IAID,KAAK,GAAG;QACJ,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;KACzB;;;;IAID,oBAAoB,GAAG;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;SAC3B;KACJ;;;;IAID,OAAO,GAAG;QACN,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;;;;IAID,UAAU,GAAG,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;;;;IAItC,OAAO,GAAG;QACN,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACvC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SAC3B;KACJ;;;;;IAKD,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;;;;IAI9D,WAAW,GAAG,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;;;;IAIhE,IAAI,SAAS,GAAG,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE;;;;IAIxD,aAAa,GAAG;QACZ,uBAAuB,MAAM,GAAG,EAAE,CAAC;QACnC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;gBAC7C,IAAI,IAAI,IAAI,QAAQ,EAAE;oBAClB,MAAM,CAAC,IAAI,CAAC;wBACR,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;iBACtF;aACJ,CAAC,CAAC;SACN;QACD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;KACjC;;;;;IAKD,eAAe,CAAC,SAAS,EAAE;QACvB,uBAAuB,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QAC3F,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAC5B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KACtB;CACJ;AACD,AAwCA;;;;;AAKA,SAAS,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE;IAClC,OAAO,mBAAmB,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;CACtE;;ACxRD;;;;AAIA,AAEO,MAAM,mBAAmB,CAAC;;;;;IAK7B,qBAAqB,CAAC,IAAI,EAAE,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;;;;;;IAMnE,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE;QAC9B,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC5C;;;;;;IAMD,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;;;;;;;IAOnE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC5B,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAChD;;;;;;;IAOD,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE;QACtC,yBAAyB,mBAAmB,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE;KACzF;;;;;;;;;;IAUD,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,GAAG,EAAE,EAAE;QACvE,uBAAuB,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU,CAAC;QAC/D,uBAAuB,aAAa,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;;QAGjE,IAAI,MAAM,EAAE;YACR,aAAa,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SACpC;QACD,uBAAuB,2BAA2B,qBAAqB,eAAe,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,OAAO,MAAM,YAAY,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5J,OAAO,IAAI,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,2BAA2B,CAAC,CAAC;KAClG;CACJ;;;;AAID,AAAO,SAAS,qBAAqB,GAAG;IACpC,OAAO,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,mBAAmB,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,UAAU,CAAC;CACpH;;ACtED;;;GAGG;;ACHH;;;;;;;;;;GAUG;;ACVH;;;;;;;;;;;;;;;GAeG;;ACfH;;;;;;GAMG;;;;"}