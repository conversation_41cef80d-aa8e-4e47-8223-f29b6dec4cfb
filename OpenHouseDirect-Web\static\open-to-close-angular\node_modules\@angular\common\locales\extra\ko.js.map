{"version": 3, "file": "ko.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ko.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;QACzC,AAD0C;KAE3C;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['자정', '정오', '새벽', '오전', '오후', '저녁', '밤'],\n    ,\n  ],\n  ,\n  [\n    '00:00', '12:00', ['03:00', '06:00'], ['06:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '21:00'], ['21:00', '03:00']\n  ]\n];\n"]}