{"version": 3, "file": "pairs.js", "sourceRoot": "", "sources": ["../../../src/add/observable/pairs.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\r\nimport { pairs as staticPairs } from '../../observable/pairs';\r\n\r\nObservable.pairs = staticPairs;\r\n\r\ndeclare module '../../Observable' {\r\n  namespace Observable {\r\n    export let pairs: typeof staticPairs;\r\n  }\r\n}"]}