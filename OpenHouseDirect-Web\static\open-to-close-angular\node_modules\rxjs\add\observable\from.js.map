{"version": 3, "file": "from.js", "sourceRoot": "", "sources": ["../../../src/add/observable/from.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,qBAAmC,uBAAuB,CAAC,CAAA;AAE3D,uBAAU,CAAC,IAAI,GAAG,WAAU,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { from as staticFrom } from '../../observable/from';\n\nObservable.from = staticFrom;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let from: typeof staticFrom;\n  }\n}"]}