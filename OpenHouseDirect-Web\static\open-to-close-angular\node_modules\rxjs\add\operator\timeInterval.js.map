{"version": 3, "file": "timeInterval.js", "sourceRoot": "", "sources": ["../../../src/add/operator/timeInterval.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,6BAA6B,6BAA6B,CAAC,CAAA;AAE3D,uBAAU,CAAC,SAAS,CAAC,YAAY,GAAG,2BAAY,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { timeInterval } from '../../operator/timeInterval';\n\nObservable.prototype.timeInterval = timeInterval;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    timeInterval: typeof timeInterval;\n  }\n}"]}