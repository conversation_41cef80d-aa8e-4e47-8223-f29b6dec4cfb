/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/cdk/collections"),require("rxjs/operators/takeUntil"),require("rxjs/BehaviorSubject"),require("rxjs/Subject"),require("rxjs/Observable"),require("rxjs/observable/of"),require("@angular/common")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/collections","rxjs/operators/takeUntil","rxjs/BehaviorSubject","rxjs/Subject","rxjs/Observable","rxjs/observable/of","@angular/common"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.table=e.ng.cdk.table||{}),e.ng.core,e.ng.cdk.collections,e.Rx.operators,e.Rx,e.Rx,e.Rx,e.Rx.Observable,e.ng.common)}(this,function(e,t,r,n,o,i,a,c,s){"use strict";function u(e,t){function r(){this.constructor=e}w(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function l(e){return Error('Could not find column with id "'+e+'".')}function f(e){return Error('Duplicate column definition name provided: "'+e+'".')}function d(){return Error("There can only be one default row without a when predicate function.")}function h(){return Error("Could not find a matching row definition for the provided row data.")}function p(){return Error("Missing definitions for header and row, cannot determine which columns should be rendered.")}function m(){return Error("Provided data source did not match an array, Observable, or DataSource")}var w=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},_="<ng-container cdkCellOutlet></ng-container>",D=function(){function e(e,t){this.template=e,this._differs=t}return e.prototype.ngOnChanges=function(e){var t=e.columns.currentValue||[];this._columnsDiffer||(this._columnsDiffer=this._differs.find(t).create(),this._columnsDiffer.diff(t))},e.prototype.getColumnsDiff=function(){return this._columnsDiffer.diff(this.columns)},e}(),C=function(e){function r(t,r){return e.call(this,t,r)||this}return u(r,e),r.decorators=[{type:t.Directive,args:[{selector:"[cdkHeaderRowDef]",inputs:["columns: cdkHeaderRowDef"]}]}],r.ctorParameters=function(){return[{type:t.TemplateRef},{type:t.IterableDiffers}]},r}(D),y=function(e){function r(t,r){return e.call(this,t,r)||this}return u(r,e),r.decorators=[{type:t.Directive,args:[{selector:"[cdkRowDef]",inputs:["columns: cdkRowDefColumns","when: cdkRowDefWhen"]}]}],r.ctorParameters=function(){return[{type:t.TemplateRef},{type:t.IterableDiffers}]},r}(D),g=function(){function e(t){this._viewContainer=t,e.mostRecentCellOutlet=this}return e.mostRecentCellOutlet=null,e.decorators=[{type:t.Directive,args:[{selector:"[cdkCellOutlet]"}]}],e.ctorParameters=function(){return[{type:t.ViewContainerRef}]},e}(),R=function(){function e(){}return e.decorators=[{type:t.Component,args:[{selector:"cdk-header-row",template:_,host:{class:"cdk-header-row",role:"row"},changeDetection:t.ChangeDetectionStrategy.OnPush,encapsulation:t.ViewEncapsulation.None,preserveWhitespaces:!1}]}],e.ctorParameters=function(){return[]},e}(),v=function(){function e(){}return e.decorators=[{type:t.Component,args:[{selector:"cdk-row",template:_,host:{class:"cdk-row",role:"row"},changeDetection:t.ChangeDetectionStrategy.OnPush,encapsulation:t.ViewEncapsulation.None,preserveWhitespaces:!1}]}],e.ctorParameters=function(){return[]},e}(),b=function(){function e(e){this.template=e}return e.decorators=[{type:t.Directive,args:[{selector:"[cdkCellDef]"}]}],e.ctorParameters=function(){return[{type:t.TemplateRef}]},e}(),k=function(){function e(e){this.template=e}return e.decorators=[{type:t.Directive,args:[{selector:"[cdkHeaderCellDef]"}]}],e.ctorParameters=function(){return[{type:t.TemplateRef}]},e}(),P=function(){function e(){}return Object.defineProperty(e.prototype,"name",{get:function(){return this._name},set:function(e){e&&(this._name=e,this.cssClassFriendlyName=e.replace(/[^a-z0-9_-]/gi,"-"))},enumerable:!0,configurable:!0}),e.decorators=[{type:t.Directive,args:[{selector:"[cdkColumnDef]"}]}],e.ctorParameters=function(){return[]},e.propDecorators={name:[{type:t.Input,args:["cdkColumnDef"]}],cell:[{type:t.ContentChild,args:[b]}],headerCell:[{type:t.ContentChild,args:[k]}]},e}(),S=function(){function e(e,t){t.nativeElement.classList.add("cdk-column-"+e.cssClassFriendlyName)}return e.decorators=[{type:t.Directive,args:[{selector:"cdk-header-cell",host:{class:"cdk-header-cell",role:"columnheader"}}]}],e.ctorParameters=function(){return[{type:P},{type:t.ElementRef}]},e}(),x=function(){function e(e,t){t.nativeElement.classList.add("cdk-column-"+e.cssClassFriendlyName)}return e.decorators=[{type:t.Directive,args:[{selector:"cdk-cell",host:{class:"cdk-cell",role:"gridcell"}}]}],e.ctorParameters=function(){return[{type:P},{type:t.ElementRef}]},e}(),E=function(){function e(e){this.viewContainer=e}return e.decorators=[{type:t.Directive,args:[{selector:"[rowPlaceholder]"}]}],e.ctorParameters=function(){return[{type:t.ViewContainerRef}]},e}(),O=function(){function e(e){this.viewContainer=e}return e.decorators=[{type:t.Directive,args:[{selector:"[headerRowPlaceholder]"}]}],e.ctorParameters=function(){return[{type:t.ViewContainerRef}]},e}(),j="\n  <ng-container headerRowPlaceholder></ng-container>\n  <ng-container rowPlaceholder></ng-container>",B=(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}u(t,e)}(t.EmbeddedViewRef),function(){function e(e,t,r,n){this._differs=e,this._changeDetectorRef=t,this._onDestroy=new i.Subject,this._columnDefsByName=new Map,this._customColumnDefs=new Set,this._customRowDefs=new Set,this._headerRowDefChanged=!1,this.viewChange=new o.BehaviorSubject({start:0,end:Number.MAX_VALUE}),n||r.nativeElement.setAttribute("role","grid")}return Object.defineProperty(e.prototype,"trackBy",{get:function(){return this._trackByFn},set:function(e){t.isDevMode()&&null!=e&&"function"!=typeof e&&console&&console.warn&&console.warn("trackBy must be a function, but received "+JSON.stringify(e)+"."),this._trackByFn=e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dataSource",{get:function(){return this._dataSource},set:function(e){this._dataSource!==e&&this._switchDataSource(e)},enumerable:!0,configurable:!0}),e.prototype.ngOnInit=function(){this._dataDiffer=this._differs.find([]).create(this._trackByFn),this._headerRowDef&&(this._headerRowDefChanged=!0)},e.prototype.ngAfterContentChecked=function(){if(this._cacheRowDefs(),this._cacheColumnDefs(),!this._headerRowDef&&!this._rowDefs.length)throw p();this._renderUpdatedColumns(),this._headerRowDefChanged&&(this._renderHeaderRow(),this._headerRowDefChanged=!1),this.dataSource&&this._rowDefs.length>0&&!this._renderChangeSubscription&&this._observeRenderChanges()},e.prototype.ngOnDestroy=function(){this._rowPlaceholder.viewContainer.clear(),this._headerRowPlaceholder.viewContainer.clear(),this._onDestroy.next(),this._onDestroy.complete(),this.dataSource instanceof r.DataSource&&this.dataSource.disconnect(this)},e.prototype.renderRows=function(){var e=this,t=this._dataDiffer.diff(this._data);if(t){var r=this._rowPlaceholder.viewContainer;t.forEachOperation(function(t,n,o){if(null==t.previousIndex)e._insertRow(t.item,o);else if(null==o)r.remove(n);else{var i=r.get(n);r.move(i,o)}}),this._updateRowIndexContext(),t.forEachIdentityChange(function(e){r.get(e.currentIndex).context.$implicit=e.item})}},e.prototype.setHeaderRowDef=function(e){this._headerRowDef=e,this._headerRowDefChanged=!0},e.prototype.addColumnDef=function(e){this._customColumnDefs.add(e)},e.prototype.removeColumnDef=function(e){this._customColumnDefs.delete(e)},e.prototype.addRowDef=function(e){this._customRowDefs.add(e)},e.prototype.removeRowDef=function(e){this._customRowDefs.delete(e)},e.prototype._cacheColumnDefs=function(){var e=this;this._columnDefsByName.clear();var t=this._contentColumnDefs?this._contentColumnDefs.toArray():[];this._customColumnDefs.forEach(function(e){return t.push(e)}),t.forEach(function(t){if(e._columnDefsByName.has(t.name))throw f(t.name);e._columnDefsByName.set(t.name,t)})},e.prototype._cacheRowDefs=function(){var e=this;this._rowDefs=this._contentRowDefs?this._contentRowDefs.toArray():[],this._customRowDefs.forEach(function(t){return e._rowDefs.push(t)});var t=this._rowDefs.filter(function(e){return!e.when});if(t.length>1)throw d();this._defaultRowDef=t[0]},e.prototype._renderUpdatedColumns=function(){var e=this;this._rowDefs.forEach(function(t){t.getColumnsDiff()&&(e._dataDiffer.diff([]),e._rowPlaceholder.viewContainer.clear(),e.renderRows())}),this._headerRowDef&&this._headerRowDef.getColumnsDiff()&&this._renderHeaderRow()},e.prototype._switchDataSource=function(e){this._data=[],this.dataSource instanceof r.DataSource&&this.dataSource.disconnect(this),this._renderChangeSubscription&&(this._renderChangeSubscription.unsubscribe(),this._renderChangeSubscription=null),e||(this._dataDiffer&&this._dataDiffer.diff([]),this._rowPlaceholder.viewContainer.clear()),this._dataSource=e},e.prototype._observeRenderChanges=function(){var e=this;if(this.dataSource){var t;if(this.dataSource.connect instanceof Function?t=this.dataSource.connect(this):this.dataSource instanceof a.Observable?t=this.dataSource:Array.isArray(this.dataSource)&&(t=c.of(this.dataSource)),void 0===t)throw m();this._renderChangeSubscription=t.pipe(n.takeUntil(this._onDestroy)).subscribe(function(t){e._data=t,e.renderRows()})}},e.prototype._renderHeaderRow=function(){this._headerRowPlaceholder.viewContainer.length>0&&this._headerRowPlaceholder.viewContainer.clear();var e=this._getHeaderCellTemplatesForRow(this._headerRowDef);e.length&&(this._headerRowPlaceholder.viewContainer.createEmbeddedView(this._headerRowDef.template,{cells:e}),e.forEach(function(e){g.mostRecentCellOutlet&&g.mostRecentCellOutlet._viewContainer.createEmbeddedView(e.template,{})}),this._changeDetectorRef.markForCheck())},e.prototype._getRowDef=function(e,t){if(1==this._rowDefs.length)return this._rowDefs[0];var r=this._rowDefs.find(function(r){return r.when&&r.when(t,e)})||this._defaultRowDef;if(!r)throw h();return r},e.prototype._insertRow=function(e,t){var r=this._getRowDef(e,t),n={$implicit:e};this._rowPlaceholder.viewContainer.createEmbeddedView(r.template,n,t),this._getCellTemplatesForRow(r).forEach(function(e){g.mostRecentCellOutlet&&g.mostRecentCellOutlet._viewContainer.createEmbeddedView(e.template,n)}),this._changeDetectorRef.markForCheck()},e.prototype._updateRowIndexContext=function(){for(var e=this._rowPlaceholder.viewContainer,t=0,r=e.length;t<r;t++){var n=e.get(t);n.context.index=t,n.context.count=r,n.context.first=0===t,n.context.last=t===r-1,n.context.even=t%2==0,n.context.odd=!n.context.even}},e.prototype._getHeaderCellTemplatesForRow=function(e){var t=this;return e&&e.columns?e.columns.map(function(e){var r=t._columnDefsByName.get(e);if(!r)throw l(e);return r.headerCell}):[]},e.prototype._getCellTemplatesForRow=function(e){var t=this;return e.columns?e.columns.map(function(e){var r=t._columnDefsByName.get(e);if(!r)throw l(e);return r.cell}):[]},e.decorators=[{type:t.Component,args:[{selector:"cdk-table",exportAs:"cdkTable",template:j,host:{class:"cdk-table"},encapsulation:t.ViewEncapsulation.None,preserveWhitespaces:!1,changeDetection:t.ChangeDetectionStrategy.OnPush}]}],e.ctorParameters=function(){return[{type:t.IterableDiffers},{type:t.ChangeDetectorRef},{type:t.ElementRef},{type:void 0,decorators:[{type:t.Attribute,args:["role"]}]}]},e.propDecorators={trackBy:[{type:t.Input}],dataSource:[{type:t.Input}],_rowPlaceholder:[{type:t.ViewChild,args:[E]}],_headerRowPlaceholder:[{type:t.ViewChild,args:[O]}],_contentColumnDefs:[{type:t.ContentChildren,args:[P]}],_contentRowDefs:[{type:t.ContentChildren,args:[y]}],_headerRowDef:[{type:t.ContentChild,args:[C]}]},e}()),T=[B,y,b,g,k,P,x,v,S,R,C,E,O],N=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{imports:[s.CommonModule],exports:[T],declarations:[T]}]}],e.ctorParameters=function(){return[]},e}();e.DataSource=r.DataSource,e.RowPlaceholder=E,e.HeaderRowPlaceholder=O,e.CDK_TABLE_TEMPLATE=j,e.CdkTable=B,e.CdkCellDef=b,e.CdkHeaderCellDef=k,e.CdkColumnDef=P,e.CdkHeaderCell=S,e.CdkCell=x,e.CDK_ROW_TEMPLATE=_,e.BaseRowDef=D,e.CdkHeaderRowDef=C,e.CdkRowDef=y,e.CdkCellOutlet=g,e.CdkHeaderRow=R,e.CdkRow=v,e.CdkTableModule=N,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-table.umd.min.js.map
