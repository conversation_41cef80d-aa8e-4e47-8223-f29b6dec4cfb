{"__symbolic": "module", "version": 4, "metadata": {"ComponentType": {"__symbolic": "interface"}, "Portal": {"__symbolic": "class", "arity": 1, "members": {"attach": [{"__symbolic": "method"}], "detach": [{"__symbolic": "method"}], "setAttachedHost": [{"__symbolic": "method"}]}}, "ComponentPortal": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "Portal"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 96, "character": 31, "context": {"typeName": "T"}, "module": "./portal"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 97, "character": 25}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 98, "character": 17}]}]}}, "TemplatePortal": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "Portal"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 119, "character": 36, "context": {"typeName": "C"}, "module": "./portal"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 97, "character": 25}, {"__symbolic": "error", "message": "Could not resolve type", "line": 119, "character": 86, "context": {"typeName": "C"}, "module": "./portal"}]}], "attach": [{"__symbolic": "method"}], "detach": [{"__symbolic": "method"}]}}, "PortalOutlet": {"__symbolic": "interface"}, "BasePortalOutlet": {"__symbolic": "class", "members": {"hasAttached": [{"__symbolic": "method"}], "attach": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "attachComponentPortal": [{"__symbolic": "method"}], "attachTemplatePortal": [{"__symbolic": "method"}], "detach": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "setDisposeFn": [{"__symbolic": "method"}], "_invokeDisposeFn": [{"__symbolic": "method"}]}}, "DomPortalOutlet": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "BasePortalHost"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 25, "character": 28, "context": {"typeName": "Element"}, "module": "./dom-portal-outlet"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver", "line": 26, "character": 41}, {"__symbolic": "reference", "module": "@angular/core", "name": "ApplicationRef", "line": 27, "character": 23}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 28, "character": 32}]}], "attachComponentPortal": [{"__symbolic": "method"}], "attachTemplatePortal": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "_getComponentRootNode": [{"__symbolic": "method"}]}}, "CdkPortal": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "TemplatePortal"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 29, "character": 1}, "arguments": [{"selector": "[cdk-portal], [cdkPortal], [portal]", "exportAs": "cdkPortal"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 34, "character": 63}]}]}}, "CdkPortalOutletAttachedRef": {"__symbolic": "interface"}, "CdkPortalOutlet": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "BasePortalHost"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 52, "character": 1}, "arguments": [{"selector": "[cdkPortalOutlet], [cdkPortalHost], [portalHost]", "exportAs": "cdkPortalOutlet, cdkPortalHost", "inputs": ["portal: cdkPortalOutlet"]}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver", "line": 65, "character": 41}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 34, "character": 63}]}], "_deprecatedPortal": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 74, "character": 3}, "arguments": ["portalHost"]}]}], "_deprecatedPortalHost": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 82, "character": 3}, "arguments": ["cdkPortalHost"]}]}], "attached": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 111, "character": 3}, "arguments": ["attached"]}]}], "ngOnInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "attachComponentPortal": [{"__symbolic": "method"}], "attachTemplatePortal": [{"__symbolic": "method"}]}}, "PortalModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 177, "character": 1}, "arguments": [{"exports": [{"__symbolic": "reference", "name": "TemplatePortalDirective"}, {"__symbolic": "reference", "name": "PortalHostDirective"}], "declarations": [{"__symbolic": "reference", "name": "TemplatePortalDirective"}, {"__symbolic": "reference", "name": "PortalHostDirective"}]}]}], "members": {}}, "PortalInjector": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 17, "character": 29}, {"__symbolic": "error", "message": "Could not resolve type", "line": 18, "character": 27, "context": {"typeName": "WeakMap"}, "module": "./portal-injector"}]}], "get": [{"__symbolic": "method"}]}}, "DomPortalHost": {"__symbolic": "reference", "name": "DomPortalOutlet"}, "PortalHostDirective": {"__symbolic": "reference", "name": "CdkPortalOutlet"}, "TemplatePortalDirective": {"__symbolic": "reference", "name": "CdkPortal"}, "PortalHost": {"__symbolic": "reference", "name": "PortalOutlet"}, "BasePortalHost": {"__symbolic": "reference", "name": "BasePortalOutlet"}}, "origins": {"ComponentType": "./portal", "Portal": "./portal", "ComponentPortal": "./portal", "TemplatePortal": "./portal", "PortalOutlet": "./portal", "BasePortalOutlet": "./portal", "DomPortalOutlet": "./dom-portal-outlet", "CdkPortal": "./portal-directives", "CdkPortalOutletAttachedRef": "./portal-directives", "CdkPortalOutlet": "./portal-directives", "PortalModule": "./portal-directives", "PortalInjector": "./portal-injector", "DomPortalHost": "./dom-portal-outlet", "PortalHostDirective": "./portal-directives", "TemplatePortalDirective": "./portal-directives", "PortalHost": "./portal", "BasePortalHost": "./portal"}, "importAs": "@angular/cdk/portal"}