{"version": 3, "file": "NeverObservable.js", "sourceRoot": "", "sources": ["../../src/observable/NeverObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAA2B,eAAe,CAAC,CAAA;AAE3C,qBAAqB,cAAc,CAAC,CAAA;AAEpC;;;;GAIG;AACH;IAAwC,mCAAa;IAoCnD;QACE,iBAAO,CAAC;IACV,CAAC;IArCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACI,sBAAM,GAAb;QACE,MAAM,CAAC,IAAI,eAAe,EAAK,CAAC;IAClC,CAAC;IAMD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAAyB;QACvE,WAAI,EAAE,CAAC;IACT,CAAC;IACH,sBAAC;AAAD,CAAC,AA3CD,CAAwC,uBAAU,GA2CjD;AA3CY,uBAAe,kBA2C3B,CAAA", "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { noop } from '../util/noop';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class NeverObservable<T> extends Observable<T> {\n  /**\n   * Creates an Observable that emits no items to the Observer.\n   *\n   * <span class=\"informal\">An Observable that never emits anything.</span>\n   *\n   * <img src=\"./img/never.png\" width=\"100%\">\n   *\n   * This static operator is useful for creating a simple Observable that emits\n   * neither values nor errors nor the completion notification. It can be used\n   * for testing purposes or for composing with other Observables. Please note\n   * that by never emitting a complete notification, this Observable keeps the\n   * subscription from being disposed automatically. Subscriptions need to be\n   * manually disposed.\n   *\n   * @example <caption>Emit the number 7, then never emit anything else (not even complete).</caption>\n   * function info() {\n   *   console.log('Will not be called');\n   * }\n   * var result = Rx.Observable.never().startWith(7);\n   * result.subscribe(x => console.log(x), info, info);\n   *\n   * @see {@link create}\n   * @see {@link empty}\n   * @see {@link of}\n   * @see {@link throw}\n   *\n   * @return {Observable} A \"never\" Observable: never emits anything.\n   * @static true\n   * @name never\n   * @owner Observable\n   */\n  static create<T>() {\n    return new NeverObservable<T>();\n  }\n\n  constructor() {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): void {\n    noop();\n  }\n}\n"]}