{"version": 3, "file": "toArray.js", "sourceRoot": "", "sources": ["../../src/operators/toArray.ts"], "names": [], "mappings": ";AAAA,uBAAuB,UAAU,CAAC,CAAA;AAGlC,wBAA2B,GAAQ,EAAE,IAAO,EAAE,KAAa;IACzD,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;QAChB,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IACD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf,MAAM,CAAC,GAAG,CAAC;AACb,CAAC;AAED;IACE,MAAM,CAAC,eAAM,CAAC,cAAc,EAAE,EAAE,CAA6B,CAAC;AAChE,CAAC;AAFe,eAAO,UAEtB,CAAA", "sourcesContent": ["import { reduce } from './reduce';\nimport { OperatorFunction } from '../interfaces';\n\nfunction toArrayReducer<T>(arr: T[], item: T, index: number) {\n  if (index === 0) {\n    return [item];\n  }\n  arr.push(item);\n  return arr;\n}\n\nexport function toArray<T>(): OperatorFunction<T, T[]> {\n  return reduce(toArrayReducer, []) as OperatorFunction<T, T[]>;\n}\n"]}