{"_from": "tmp@0.0.33", "_id": "tmp@0.0.33", "_inBundle": false, "_integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "_location": "/tmp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tmp@0.0.33", "name": "tmp", "escapedName": "tmp", "rawSpec": "0.0.33", "saveSpec": null, "fetchSpec": "0.0.33"}, "_requiredBy": ["/karma", "/useragent"], "_resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "_shasum": "6d34335889768d21b2bcda0aa277ced3b1bfadf9", "_spec": "tmp@0.0.33", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\karma", "author": {"name": "KARASZI István", "email": "<EMAIL>", "url": "http://raszi.hu/"}, "bugs": {"url": "http://github.com/raszi/node-tmp/issues"}, "bundleDependencies": false, "dependencies": {"os-tmpdir": "~1.0.2"}, "deprecated": false, "description": "Temporary file and directory creator", "devDependencies": {"vows": "~0.7.0"}, "engines": {"node": ">=0.6.0"}, "files": ["lib/"], "homepage": "http://github.com/raszi/node-tmp", "keywords": ["temporary", "tmp", "temp", "tempdir", "tempfile", "tmpdir", "tmpfile"], "license": "MIT", "main": "lib/tmp.js", "name": "tmp", "repository": {"type": "git", "url": "git+https://github.com/raszi/node-tmp.git"}, "scripts": {"doc": "jsdoc -c .jsdoc.json", "test": "vows test/*-test.js"}, "version": "0.0.33"}