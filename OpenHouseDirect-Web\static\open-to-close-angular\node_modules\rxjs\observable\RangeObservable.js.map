{"version": 3, "file": "RangeObservable.js", "sourceRoot": "", "sources": ["../../src/observable/RangeObservable.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAI3C;;;;GAIG;AACH;IAAqC,mCAAkB;IA+DrD,yBAAY,KAAa,EACb,KAAa,EACb,SAAsB;QAChC,iBAAO,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IApED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACI,sBAAM,GAAb,UAAc,KAAiB,EACjB,KAAiB,EACjB,SAAsB;QAFtB,qBAAiB,GAAjB,SAAiB;QACjB,qBAAiB,GAAjB,SAAiB;QAE7B,MAAM,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACtD,CAAC;IAEM,wBAAQ,GAAf,UAAgB,KAAU;QAEhB,uBAAK,EAAE,mBAAK,EAAE,mBAAK,EAAE,6BAAU,CAAW;QAElD,EAAE,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;YACnB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QACxB,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QAEjB,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAeD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAA8B;QAC5E,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACrD,YAAK,EAAE,YAAK,EAAE,YAAK,EAAE,sBAAU;aAChC,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC;gBACF,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC;oBACrB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACtB,KAAK,CAAC;gBACR,CAAC;gBACD,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;gBACzB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACtB,KAAK,CAAC;gBACR,CAAC;YACH,CAAC,QAAQ,IAAI,EAAE;QACjB,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AA/FD,CAAqC,uBAAU,GA+F9C;AA/FY,uBAAe,kBA+F3B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { TeardownLogic } from '../Subscription';\nimport { Subscriber } from '../Subscriber';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class RangeObservable extends Observable<number> {\n\n  /**\n   * Creates an Observable that emits a sequence of numbers within a specified\n   * range.\n   *\n   * <span class=\"informal\">Emits a sequence of numbers in a range.</span>\n   *\n   * <img src=\"./img/range.png\" width=\"100%\">\n   *\n   * `range` operator emits a range of sequential integers, in order, where you\n   * select the `start` of the range and its `length`. By default, uses no\n   * IScheduler and just delivers the notifications synchronously, but may use\n   * an optional IScheduler to regulate those deliveries.\n   *\n   * @example <caption>Emits the numbers 1 to 10</caption>\n   * var numbers = Rx.Observable.range(1, 10);\n   * numbers.subscribe(x => console.log(x));\n   *\n   * @see {@link timer}\n   * @see {@link interval}\n   *\n   * @param {number} [start=0] The value of the first integer in the sequence.\n   * @param {number} [count=0] The number of sequential integers to generate.\n   * @param {Scheduler} [scheduler] A {@link IScheduler} to use for scheduling\n   * the emissions of the notifications.\n   * @return {Observable} An Observable of numbers that emits a finite range of\n   * sequential integers.\n   * @static true\n   * @name range\n   * @owner Observable\n   */\n  static create(start: number = 0,\n                count: number = 0,\n                scheduler?: IScheduler): Observable<number> {\n    return new RangeObservable(start, count, scheduler);\n  }\n\n  static dispatch(state: any) {\n\n    const { start, index, count, subscriber } = state;\n\n    if (index >= count) {\n      subscriber.complete();\n      return;\n    }\n\n    subscriber.next(start);\n\n    if (subscriber.closed) {\n      return;\n    }\n\n    state.index = index + 1;\n    state.start = start + 1;\n\n    (<any> this).schedule(state);\n  }\n\n  private start: number;\n  private _count: number;\n  private scheduler: IScheduler;\n\n  constructor(start: number,\n              count: number,\n              scheduler?: IScheduler) {\n    super();\n    this.start = start;\n    this._count = count;\n    this.scheduler = scheduler;\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<number>): TeardownLogic {\n    let index = 0;\n    let start = this.start;\n    const count = this._count;\n    const scheduler = this.scheduler;\n\n    if (scheduler) {\n      return scheduler.schedule(RangeObservable.dispatch, 0, {\n        index, count, start, subscriber\n      });\n    } else {\n      do {\n        if (index++ >= count) {\n          subscriber.complete();\n          break;\n        }\n        subscriber.next(start++);\n        if (subscriber.closed) {\n          break;\n        }\n      } while (true);\n    }\n  }\n}\n"]}