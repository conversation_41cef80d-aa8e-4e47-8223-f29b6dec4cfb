{"version": 3, "file": "platform.js", "sources": ["../../../src/cdk/platform/index.ts", "../../../src/cdk/platform/public-api.ts", "../../../src/cdk/platform/platform-module.ts", "../../../src/cdk/platform/features.ts", "../../../src/cdk/platform/platform.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './platform';\nexport * from './features';\nexport * from './platform-module';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {Platform} from './platform';\n\n\n@NgModule({\n  providers: [Platform]\n})\nexport class PlatformModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents: boolean;\n\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nexport function supportsPassiveEventListeners(): boolean {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null!, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n\n  return supportsPassiveEvents;\n}\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes: Set<string>;\n\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n  // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n  // first changing it to something else:\n  // The specified value \"\" does not conform to the required format.\n  // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n  'color',\n  'button',\n  'checkbox',\n  'date',\n  'datetime-local',\n  'email',\n  'file',\n  'hidden',\n  'image',\n  'month',\n  'number',\n  'password',\n  'radio',\n  'range',\n  'reset',\n  'search',\n  'submit',\n  'tel',\n  'text',\n  'time',\n  'url',\n  'week',\n];\n\n/** @returns The input types supported by this browser. */\nexport function getSupportedInputTypes(): Set<string> {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n\n  return supportedInputTypes;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nconst hasV8BreakIterator = (typeof Intl !== 'undefined' && (Intl as any).v8BreakIterator);\n\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\n@Injectable()\nexport class Platform {\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser: boolean = typeof document === 'object' && !!document;\n\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE: boolean = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT: boolean = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n\n  /** Whether the current rendering engine is Blink. */\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  BLINK: boolean = this.isBrowser && (!!((window as any).chrome || hasV8BreakIterator) &&\n    typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT);\n\n  /** Whether the current rendering engine is WebKit. */\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  WEBKIT: boolean = this.isBrowser &&\n      /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n\n  /** Whether the current platform is Apple iOS. */\n  IOS: boolean = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) &&\n      !(window as any).MSStream;\n\n  /** Whether the current browser is Firefox. */\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  FIREFOX: boolean = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID: boolean = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n\n  /** Whether the current browser is Safari. */\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  SAFARI: boolean = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AIQA;;AAIA,MAAM,kBAAkB,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,mBAAC,IAAW,GAAE,eAAe,CAAC,CAAC;;;;;AAO1F,AAAA,MAAA,QAAA,CAAA;;;;;QAEA,IAAA,CAAA,SAAA,GAAuB,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAjE;;;;QAGA,IAAA,CAAA,IAAA,GAAkB,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAvE;;;;QAGA,IAAA,CAAA,OAAA,GAAqB,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAlF;;;;QAIA,IAAA,CAAA,KAAA,GAAmB,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,mBAAC,MAAa,GAAE,MAAM,IAAI,kBAAkB,CAAC;YAClF,OAAO,GAAG,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAA9D;;;;QAKA,IAAA,CAAA,MAAA,GAAoB,IAAI,CAAC,SAAS;YAC5B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAA5F;;;;QAGA,IAAA,CAAA,GAAA,GAAiB,IAAI,CAAC,SAAS,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;YACzE,CAAC,mBAAC,MAAa,GAAE,QAAQ,CAA/B;;;;QAOA,IAAA,CAAA,OAAA,GAAqB,IAAI,CAAC,SAAS,IAAI,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAvF;;;;QAIA,IAAA,CAAA,OAAA,GAAqB,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAA5F;;;;QAMA,IAAA,CAAA,MAAA,GAAoB,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,MAAM,CAAxF;;;;IAzCA,EAAA,IAAA,EAAC,UAAU,EAAX;;;;;;;;;;;;;ADTA,IAAI,qBAA8B,CAAC;;;;;;AAMnC,AAAA,SAAA,6BAAA,GAAA;IACE,IAAI,qBAAqB,IAAI,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAClE,IAAI;YACF,MAAM,CAAC,gBAAgB,CAAC,MAAM,qBAAE,IAAI,IAAG,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;gBAC1E,GAAG,EAAE,MAAM,qBAAqB,GAAG,IAAI;aACxC,CAAC,CAAC,CAAC;SACL;gBAAS;YACR,qBAAqB,GAAG,qBAAqB,IAAI,KAAK,CAAC;SACxD;KACF;IAED,OAAO,qBAAqB,CAAC;CAC9B;;;;AAGD,IAAI,mBAAgC,CAAC;;;;AAGrC,MAAM,mBAAmB,GAAG;IAK1B,OAAO;IACP,QAAQ;IACR,UAAU;IACV,MAAM;IACN,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;CACP,CAAC;;;;AAGF,AAAA,SAAA,sBAAA,GAAA;;IAEE,IAAI,mBAAmB,EAAE;QACvB,OAAO,mBAAmB,CAAC;KAC5B;;;;IAKD,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;QAC7C,mBAAmB,GAAG,IAAI,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACnD,OAAO,mBAAmB,CAAC;KAC5B;IAED,qBAAI,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACvD,mBAAmB,GAAG,IAAI,GAAG,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,IAAhE;QACI,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,gBAAgB,CAAC,IAAI,KAAK,KAAK,CAAC;KACxC,CAAC,CAAC,CAAC;IAEJ,OAAO,mBAAmB,CAAC;CAC5B;;;;;;;AD5ED,AACA,AAMA,AAAA,MAAA,cAAA,CAAA;;;IAHA,EAAA,IAAA,EAAC,QAAQ,EAAT,IAAA,EAAA,CAAU;gBACR,SAAS,EAAE,CAAC,QAAQ,CAAC;aACtB,EAAD,EAAA;;;;;;;;GDNA,AACA,AACA,AAAkC;;;;;;;;GDNlC,AAA6B;;"}