{"version": 3, "file": "ajax.js", "sourceRoot": "", "sources": ["../../../../src/add/observable/dom/ajax.ts"], "names": [], "mappings": ";AAAA,2BAA2B,qBAAqB,CAAC,CAAA;AACjD,qBAAmC,8BAA8B,CAAC,CAAA;AAGlE,uBAAU,CAAC,IAAI,GAAG,WAAU,CAAC", "sourcesContent": ["import { Observable } from '../../../Observable';\nimport { ajax as staticAjax } from '../../../observable/dom/ajax';\nimport { AjaxCreationMethod } from '../../../observable/dom/AjaxObservable';\n\nObservable.ajax = staticAjax;\n\ndeclare module '../../../Observable' {\n  namespace Observable {\n    export let ajax: AjaxCreationMethod;\n  }\n}"]}