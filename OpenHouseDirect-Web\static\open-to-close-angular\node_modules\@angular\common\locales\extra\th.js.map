{"version": 3, "file": "th.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/th.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;YAC1C,MAAM,EAAE,KAAK,EAAE,SAAS;SACzB;QACD;YACE,WAAW,EAAE,QAAQ,EAAE,WAAW;YAClC,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK;YACvC,SAAS;SACV;KACF;IACD;QACE;YACE,WAAW,EAAE,QAAQ,EAAE,MAAM;YAC7B,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;YACnC,SAAS;SACV;QACD;YACE,WAAW,EAAE,QAAQ,EAAE,WAAW;YAClC,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK;YACvC,SAAS;SACV;KACF;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC3D;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'เที่ยงคืน', 'n', 'เช้า', 'เที่ยง', 'บ่าย',\n      'เย็น', 'ค่ำ', 'กลางคืน'\n    ],\n    [\n      'เที่ยงคืน', 'เที่ยง', 'ในตอนเช้า',\n      'ในตอนบ่าย', 'บ่าย', 'ในตอนเย็น', 'ค่ำ',\n      'กลางคืน'\n    ],\n  ],\n  [\n    [\n      'เที่ยงคืน', 'เที่ยง', 'เช้า',\n      'ช่วงเที่ยง', 'บ่าย', 'เย็น', 'ค่ำ',\n      'กลางคืน'\n    ],\n    [\n      'เที่ยงคืน', 'เที่ยง', 'ในตอนเช้า',\n      'ในตอนบ่าย', 'บ่าย', 'ในตอนเย็น', 'ค่ำ',\n      'กลางคืน'\n    ],\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '13:00'], ['13:00', '16:00'],\n    ['16:00', '18:00'], ['18:00', '21:00'], ['21:00', '06:00']\n  ]\n];\n"]}