{"_from": "normalize-package-data@^2.3.4", "_id": "normalize-package-data@2.5.0", "_inBundle": false, "_integrity": "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==", "_location": "/normalize-package-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize-package-data@^2.3.4", "name": "normalize-package-data", "escapedName": "normalize-package-data", "rawSpec": "^2.3.4", "saveSpec": null, "fetchSpec": "^2.3.4"}, "_requiredBy": ["/meow", "/read-pkg", "/webpack/read-pkg"], "_resolved": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "_shasum": "e66db1838b200c1dfc233225d12cb36520e234a8", "_spec": "normalize-package-data@^2.3.4", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\meow", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/npm/normalize-package-data/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Meryn Stol", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "deprecated": false, "description": "Normalizes data that can be found in package.json files.", "devDependencies": {"async": "^2.6.1", "tap": "^12.4.0", "underscore": "^1.8.3"}, "files": ["lib/*.js", "lib/*.json", "AUTHORS"], "homepage": "https://github.com/npm/normalize-package-data#readme", "license": "BSD-2-<PERSON><PERSON>", "main": "lib/normalize.js", "name": "normalize-package-data", "repository": {"type": "git", "url": "git://github.com/npm/normalize-package-data.git"}, "scripts": {"test": "tap test/*.js"}, "version": "2.5.0"}