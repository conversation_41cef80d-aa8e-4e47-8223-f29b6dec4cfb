{"version": 3, "file": "ScalarObservable.js", "sourceRoot": "", "sources": ["../../src/observable/ScalarObservable.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAI3C;;;;GAIG;AACH;IAAyC,oCAAa;IAwBpD,0BAAmB,KAAQ,EAAU,SAAsB;QACzD,iBAAO,CAAC;QADS,UAAK,GAAL,KAAK,CAAG;QAAU,cAAS,GAAT,SAAS,CAAa;QAF3D,cAAS,GAAY,IAAI,CAAC;QAIxB,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IA5BM,uBAAM,GAAb,UAAiB,KAAQ,EAAE,SAAsB;QAC/C,MAAM,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAEM,yBAAQ,GAAf,UAAgB,KAAU;QAChB,qBAAI,EAAE,mBAAK,EAAE,6BAAU,CAAW;QAE1C,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QACX,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAWD,oCAAoC,CAAC,qCAAU,GAAV,UAAW,UAAyB;QACvE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACtD,IAAI,EAAE,KAAK,EAAE,YAAK,EAAE,sBAAU;aAC/B,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gBACvB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IACH,uBAAC;AAAD,CAAC,AA9CD,CAAyC,uBAAU,GA8ClD;AA9CY,wBAAgB,mBA8C5B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { TeardownLogic } from '../Subscription';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class ScalarObservable<T> extends Observable<T> {\n  static create<T>(value: T, scheduler?: IScheduler): ScalarObservable<T> {\n    return new ScalarObservable(value, scheduler);\n  }\n\n  static dispatch(state: any): void {\n    const { done, value, subscriber } = state;\n\n    if (done) {\n      subscriber.complete();\n      return;\n    }\n\n    subscriber.next(value);\n    if (subscriber.closed) {\n      return;\n    }\n\n    state.done = true;\n    (<any> this).schedule(state);\n  }\n\n  _isScalar: boolean = true;\n\n  constructor(public value: T, private scheduler?: IScheduler) {\n    super();\n    if (scheduler) {\n      this._isScalar = false;\n    }\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n    const value = this.value;\n    const scheduler = this.scheduler;\n\n    if (scheduler) {\n      return scheduler.schedule(ScalarObservable.dispatch, 0, {\n        done: false, value, subscriber\n      });\n    } else {\n      subscriber.next(value);\n      if (!subscriber.closed) {\n        subscriber.complete();\n      }\n    }\n  }\n}\n"]}