{"_from": "extglob@^2.0.4", "_id": "extglob@2.0.4", "_inBundle": false, "_integrity": "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==", "_location": "/watchpack-chokidar2/extglob", "_phantomChildren": {"is-descriptor": "1.0.3", "is-extendable": "0.1.1"}, "_requested": {"type": "range", "registry": true, "raw": "extglob@^2.0.4", "name": "extglob", "escapedName": "extglob", "rawSpec": "^2.0.4", "saveSpec": null, "fetchSpec": "^2.0.4"}, "_requiredBy": ["/watchpack-chokidar2/micromatch"], "_resolved": "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz", "_shasum": "ad00fe4dc612a9232e8718711dc5cb5ab0285543", "_spec": "extglob@^2.0.4", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\watchpack-chokidar2\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/extglob/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "Devon Govett", "url": "http://badassjs.com"}, {"name": "<PERSON><PERSON>", "url": "https://www.isiahmeadows.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "http://mattbierner.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://shinnn.github.io"}], "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "deprecated": false, "description": "Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.", "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "helper-changelog": "^0.3.0", "is-windows": "^1.0.1", "micromatch": "^3.0.4", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.0", "multimatch": "^2.1.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/micromatch/extglob", "keywords": ["bash", "extended", "extglob", "glob", "globbing", "ksh", "match", "pattern", "patterns", "regex", "test", "wildcard"], "license": "MIT", "lintDeps": {"devDependencies": {"files": {"options": {"ignore": ["benchmark/**/*.js"]}}}}, "main": "index.js", "name": "extglob", "repository": {"type": "git", "url": "git+https://github.com/micromatch/extglob.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["braces", "expand-brackets", "expand-range", "fill-range", "micromatch"]}, "helpers": ["helper-changelog"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "2.0.4"}