{"version": 3, "file": "ArrayObservable.js", "sourceRoot": "", "sources": ["../../src/observable/ArrayObservable.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAC3C,iCAAiC,oBAAoB,CAAC,CAAA;AACtD,gCAAgC,mBAAmB,CAAC,CAAA;AAEpD,4BAA4B,qBAAqB,CAAC,CAAA;AAGlD;;;;GAIG;AACH;IAAwC,mCAAa;IA0FnD,yBAAoB,KAAU,EAAU,SAAsB;QAC5D,iBAAO,CAAC;QADU,UAAK,GAAL,KAAK,CAAK;QAAU,cAAS,GAAT,SAAS,CAAa;QAE5D,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IA9FM,sBAAM,GAAb,UAAiB,KAAU,EAAE,SAAsB;QACjD,MAAM,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC/C,CAAC;IASD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACI,kBAAE,GAAT;QAAa,eAA+B;aAA/B,WAA+B,CAA/B,sBAA+B,CAA/B,IAA+B;YAA/B,8BAA+B;;QAC1C,IAAI,SAAS,GAAe,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,EAAE,CAAC,CAAC,yBAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC3B,KAAK,CAAC,GAAG,EAAE,CAAC;QACd,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACZ,MAAM,CAAC,IAAI,eAAe,CAAS,KAAK,EAAE,SAAS,CAAC,CAAC;QACvD,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM,CAAC,IAAI,mCAAgB,CAAS,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAC3D,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,IAAI,iCAAe,CAAI,SAAS,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEM,wBAAQ,GAAf,UAAgB,KAAU;QAEhB,uBAAK,EAAE,mBAAK,EAAE,mBAAK,EAAE,6BAAU,CAAW;QAElD,EAAE,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;YACnB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QAE9B,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QAEjB,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAaD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAAyB;QACvE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACrD,YAAK,EAAE,YAAK,EAAE,YAAK,EAAE,sBAAU;aAChC,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AAnHD,CAAwC,uBAAU,GAmHjD;AAnHY,uBAAe,kBAmH3B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { ScalarObservable } from './ScalarObservable';\nimport { EmptyObservable } from './EmptyObservable';\nimport { Subscriber } from '../Subscriber';\nimport { isScheduler } from '../util/isScheduler';\nimport { TeardownLogic } from '../Subscription';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class ArrayObservable<T> extends Observable<T> {\n\n  static create<T>(array: T[], scheduler?: IScheduler): Observable<T> {\n    return new ArrayObservable(array, scheduler);\n  }\n\n  static of<T>(item1: T, scheduler?: IScheduler): Observable<T>;\n  static of<T>(item1: T, item2: T, scheduler?: IScheduler): Observable<T>;\n  static of<T>(item1: T, item2: T, item3: T, scheduler?: IScheduler): Observable<T>;\n  static of<T>(item1: T, item2: T, item3: T, item4: T, scheduler?: IScheduler): Observable<T>;\n  static of<T>(item1: T, item2: T, item3: T, item4: T, item5: T, scheduler?: IScheduler): Observable<T>;\n  static of<T>(item1: T, item2: T, item3: T, item4: T, item5: T, item6: T, scheduler?: IScheduler): Observable<T>;\n  static of<T>(...array: Array<T | IScheduler>): Observable<T>;\n  /**\n   * Creates an Observable that emits some values you specify as arguments,\n   * immediately one after the other, and then emits a complete notification.\n   *\n   * <span class=\"informal\">Emits the arguments you provide, then completes.\n   * </span>\n   *\n   * <img src=\"./img/of.png\" width=\"100%\">\n   *\n   * This static operator is useful for creating a simple Observable that only\n   * emits the arguments given, and the complete notification thereafter. It can\n   * be used for composing with other Observables, such as with {@link concat}.\n   * By default, it uses a `null` IScheduler, which means the `next`\n   * notifications are sent synchronously, although with a different IScheduler\n   * it is possible to determine when those notifications will be delivered.\n   *\n   * @example <caption>Emit 10, 20, 30, then 'a', 'b', 'c', then start ticking every second.</caption>\n   * var numbers = Rx.Observable.of(10, 20, 30);\n   * var letters = Rx.Observable.of('a', 'b', 'c');\n   * var interval = Rx.Observable.interval(1000);\n   * var result = numbers.concat(letters).concat(interval);\n   * result.subscribe(x => console.log(x));\n   *\n   * @see {@link create}\n   * @see {@link empty}\n   * @see {@link never}\n   * @see {@link throw}\n   *\n   * @param {...T} values Arguments that represent `next` values to be emitted.\n   * @param {Scheduler} [scheduler] A {@link IScheduler} to use for scheduling\n   * the emissions of the `next` notifications.\n   * @return {Observable<T>} An Observable that emits each given input value.\n   * @static true\n   * @name of\n   * @owner Observable\n   */\n  static of<T>(...array: Array<T | IScheduler>): Observable<T> {\n    let scheduler = <IScheduler>array[array.length - 1];\n    if (isScheduler(scheduler)) {\n      array.pop();\n    } else {\n      scheduler = null;\n    }\n\n    const len = array.length;\n    if (len > 1) {\n      return new ArrayObservable<T>(<any>array, scheduler);\n    } else if (len === 1) {\n      return new ScalarObservable<T>(<any>array[0], scheduler);\n    } else {\n      return new EmptyObservable<T>(scheduler);\n    }\n  }\n\n  static dispatch(state: any) {\n\n    const { array, index, count, subscriber } = state;\n\n    if (index >= count) {\n      subscriber.complete();\n      return;\n    }\n\n    subscriber.next(array[index]);\n\n    if (subscriber.closed) {\n      return;\n    }\n\n    state.index = index + 1;\n\n    (<any> this).schedule(state);\n  }\n\n  // value used if Array has one value and _isScalar\n  value: any;\n\n  constructor(private array: T[], private scheduler?: IScheduler) {\n    super();\n    if (!scheduler && array.length === 1) {\n      this._isScalar = true;\n      this.value = array[0];\n    }\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n    let index = 0;\n    const array = this.array;\n    const count = array.length;\n    const scheduler = this.scheduler;\n\n    if (scheduler) {\n      return scheduler.schedule(ArrayObservable.dispatch, 0, {\n        array, index, count, subscriber\n      });\n    } else {\n      for (let i = 0; i < count && !subscriber.closed; i++) {\n        subscriber.next(array[i]);\n      }\n      subscriber.complete();\n    }\n  }\n}\n"]}