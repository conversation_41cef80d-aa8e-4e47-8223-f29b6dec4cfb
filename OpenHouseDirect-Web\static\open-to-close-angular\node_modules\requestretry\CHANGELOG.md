<a name="1.12.3"></a>
## 1.12.3 (2018-01-18)

* Added testing for error object attempts reporting ([578870f](https://github.com/FGRibreau/node-request-retry/commit/578870f))
* Added the attempts property and assigned it to the error object returned when a network error occurs ([ecd9f5c](https://github.com/FGRibreau/node-request-retry/commit/ecd9f5c))
* Adds exponential backoff example to readme. ([184b84f](https://github.com/FGRibreau/node-request-retry/commit/184b84f))
* Release v1.12.3. ([57bb3d8](https://github.com/FGRibreau/node-request-retry/commit/57bb3d8))
* Typo in Readme for Exponential Backoff Delay. ([b9ef000](https://github.com/FGRibreau/node-request-retry/commit/b9ef000))
* Update attempts.test.js ([32a699d](https://github.com/FGRibreau/node-request-retry/commit/32a699d))
* Update README.md ([9d1367b](https://github.com/FGRibreau/node-request-retry/commit/9d1367b))
* Update README.md ([eed7f88](https://github.com/FGRibreau/node-request-retry/commit/eed7f88))
* chore(git): remove package-lock.json from gitignore ([08631fa](https://github.com/FGRibreau/node-request-retry/commit/08631fa))
* docs(changelog): updated ([cbe2355](https://github.com/FGRibreau/node-request-retry/commit/cbe2355))



<a name="1.12.2"></a>
## 1.12.2 (2017-08-01)

* Added .auth, .jar and .cookie implementations along with corresponding tests ([15afe79](https://github.com/FGRibreau/node-request-retry/commit/15afe79))
* formatting updated + version bump ([6ad6f09](https://github.com/FGRibreau/node-request-retry/commit/6ad6f09))
* Improve README. ([5052add](https://github.com/FGRibreau/node-request-retry/commit/5052add))
* minor fix ([5b3f6d1](https://github.com/FGRibreau/node-request-retry/commit/5b3f6d1))
* Release v1.12.1. ([a1d198a](https://github.com/FGRibreau/node-request-retry/commit/a1d198a))
* Release v1.12.2. ([eba306c](https://github.com/FGRibreau/node-request-retry/commit/eba306c))
* revert ([d6a840f](https://github.com/FGRibreau/node-request-retry/commit/d6a840f))
* split various.test.js in auth.test.js and cookie.test.js ([c80272b](https://github.com/FGRibreau/node-request-retry/commit/c80272b))
* Update package.json ([9619999](https://github.com/FGRibreau/node-request-retry/commit/9619999))
* Update README.md ([9a50640](https://github.com/FGRibreau/node-request-retry/commit/9a50640))
* Update README.md ([ada51e3](https://github.com/FGRibreau/node-request-retry/commit/ada51e3))
* Update README.md ([984fd17](https://github.com/FGRibreau/node-request-retry/commit/984fd17))
* Update README.md ([94b8c01](https://github.com/FGRibreau/node-request-retry/commit/94b8c01))
* chore(package): update dependencies ([85c62ac](https://github.com/FGRibreau/node-request-retry/commit/85c62ac))
* chore(package): update nyc to version 10.0.0 ([f652825](https://github.com/FGRibreau/node-request-retry/commit/f652825))
* chore(package): update nyc to version 9.0.1 ([e376616](https://github.com/FGRibreau/node-request-retry/commit/e376616))
* chore(package): update sinon to version 1.17.6 ([2735e7c](https://github.com/FGRibreau/node-request-retry/commit/2735e7c))
* docs(changelog): updated ([448b9e1](https://github.com/FGRibreau/node-request-retry/commit/448b9e1))



<a name="1.12.0"></a>
# 1.12.0 (2016-09-07)

* Add delay strategy ([ce99e46](https://github.com/FGRibreau/node-request-retry/commit/ce99e46))
* docs(delayStrategy) ([be1fdc3](https://github.com/FGRibreau/node-request-retry/commit/be1fdc3))
* Release v1.12.0. ([aef934c](https://github.com/FGRibreau/node-request-retry/commit/aef934c))
* Update README.md ([d1fc2ea](https://github.com/FGRibreau/node-request-retry/commit/d1fc2ea))
* Update README.md ([fecda40](https://github.com/FGRibreau/node-request-retry/commit/fecda40))
* Update README.md ([b7e4133](https://github.com/FGRibreau/node-request-retry/commit/b7e4133))
* Update README.md ([5db9276](https://github.com/FGRibreau/node-request-retry/commit/5db9276))
* docs(changelog): updated ([757ac28](https://github.com/FGRibreau/node-request-retry/commit/757ac28))



<a name="1.11.0"></a>
# 1.11.0 (2016-09-03)

* fix(circle) ([e6c2160](https://github.com/FGRibreau/node-request-retry/commit/e6c2160))
* fix(deps) ([35921bb](https://github.com/FGRibreau/node-request-retry/commit/35921bb))
* Release v1.11.0. ([560e402](https://github.com/FGRibreau/node-request-retry/commit/560e402))
* Update README.md ([85b63ad](https://github.com/FGRibreau/node-request-retry/commit/85b63ad))
* fix(tests): bring node-request-retry to a 100% code-coverage ([6275780](https://github.com/FGRibreau/node-request-retry/commit/6275780))
* feat(api): larger api-surface, just like `request` ([1527f9d](https://github.com/FGRibreau/node-request-retry/commit/1527f9d))



<a name="1.10.0"></a>
# 1.10.0 (2016-08-18)

* Release v1.10.0. ([b1fbef5](https://github.com/FGRibreau/node-request-retry/commit/b1fbef5))
* feat(updtr): use updtr ([85bc2c6](https://github.com/FGRibreau/node-request-retry/commit/85bc2c6))
* docs(changelog): updated ([4e174ab](https://github.com/FGRibreau/node-request-retry/commit/4e174ab))



<a name="1.9.1"></a>
## 1.9.1 (2016-07-29)

* fix(changelog) ([9715bd6](https://github.com/FGRibreau/node-request-retry/commit/9715bd6))
* fix(readme) ([7e8b8c7](https://github.com/FGRibreau/node-request-retry/commit/7e8b8c7))
* Release v1.9.1. ([852e81c](https://github.com/FGRibreau/node-request-retry/commit/852e81c))
* Update request to 2.74.0 ([7934049](https://github.com/FGRibreau/node-request-retry/commit/7934049))
* docs(changelog): updated ([cc717c7](https://github.com/FGRibreau/node-request-retry/commit/cc717c7))



<a name="1.9.0"></a>
# 1.9.0 (2016-06-22)

* Add support for body-dependent retry strategies ([0f472f9](https://github.com/FGRibreau/node-request-retry/commit/0f472f9))
* Amended for body-dependent retry strategies ([db7a4ef](https://github.com/FGRibreau/node-request-retry/commit/db7a4ef))
* Release v1.9.0. ([5bdee74](https://github.com/FGRibreau/node-request-retry/commit/5bdee74))
* docs(changelog): updated ([ca04ea7](https://github.com/FGRibreau/node-request-retry/commit/ca04ea7))



<a name="1.8.0"></a>
# 1.8.0 (2016-05-11)

* Release v1.8.0. ([6767b49](https://github.com/FGRibreau/node-request-retry/commit/6767b49))
* docs(changelog): updated ([97d6429](https://github.com/FGRibreau/node-request-retry/commit/97d6429))



<a name="1.7.1"></a>
## 1.7.1 (2016-05-11)

* Add support for .get/.post/... helpers ([0cb9e61](https://github.com/FGRibreau/node-request-retry/commit/0cb9e61))
* Release v1.7.1. ([55ab78a](https://github.com/FGRibreau/node-request-retry/commit/55ab78a))
* defaults(): use extend for "deep" defaulting ([b514a81](https://github.com/FGRibreau/node-request-retry/commit/b514a81))
* docs(changelog): updated ([9e29831](https://github.com/FGRibreau/node-request-retry/commit/9e29831))
* fix(changelog): migrated changelog from ruby to js ([777ca04](https://github.com/FGRibreau/node-request-retry/commit/777ca04))



<a name="1.7.0"></a>
# 1.7.0 (2016-05-06)

* docs(changelog) ([f8e793f](https://github.com/FGRibreau/node-request-retry/commit/f8e793f))
* docs(readme) ([f76572d](https://github.com/FGRibreau/node-request-retry/commit/f76572d))
* docs(README) ([29b98ea](https://github.com/FGRibreau/node-request-retry/commit/29b98ea))
* Release v1.7.0. ([762e150](https://github.com/FGRibreau/node-request-retry/commit/762e150))
* Support for request default options. Issue #11 ([70b27ec](https://github.com/FGRibreau/node-request-retry/commit/70b27ec))
* Update README.md ([0e684c8](https://github.com/FGRibreau/node-request-retry/commit/0e684c8))
* Update README.md ([b45d6b7](https://github.com/FGRibreau/node-request-retry/commit/b45d6b7))
* Update README.md ([239e75d](https://github.com/FGRibreau/node-request-retry/commit/239e75d))



<a name="1.6.0"></a>
# 1.6.0 (2015-12-25)

* Added bluebird and nock dependencies ([2062358](https://github.com/FGRibreau/node-request-retry/commit/2062358))
* Added tests for `promiseFactory` option, using (when.js, Q, kew, RSVP.js) promises libraries ([2cd5c6a](https://github.com/FGRibreau/node-request-retry/commit/2cd5c6a))
* docs(changelog) ([55efd7d](https://github.com/FGRibreau/node-request-retry/commit/55efd7d))
* docs(readme) ([41e893b](https://github.com/FGRibreau/node-request-retry/commit/41e893b))
* docs(readme) ([d291e9b](https://github.com/FGRibreau/node-request-retry/commit/d291e9b))
* fix(index) ([51d087c](https://github.com/FGRibreau/node-request-retry/commit/51d087c))
* Fixed typo ([f24205d](https://github.com/FGRibreau/node-request-retry/commit/f24205d))
* Implemented `promiseFactory` option, to allow usage of different promise libraries ([7a5e11c](https://github.com/FGRibreau/node-request-retry/commit/7a5e11c))
* Implemented promises support ([27484f0](https://github.com/FGRibreau/node-request-retry/commit/27484f0))
* Implemented tests for promises ([609d820](https://github.com/FGRibreau/node-request-retry/commit/609d820))
* Minor example text fix ([2edefc4](https://github.com/FGRibreau/node-request-retry/commit/2edefc4))
* Release v1.6.0. ([99118dd](https://github.com/FGRibreau/node-request-retry/commit/99118dd))
* Removed test suite exclusive `only` modifier ([71ec0d2](https://github.com/FGRibreau/node-request-retry/commit/71ec0d2))
* Updated README for usage with promises ([d66a77a](https://github.com/FGRibreau/node-request-retry/commit/d66a77a))
* feat(promise): using when.js by default instead of bluebird ([f9dddf9](https://github.com/FGRibreau/node-request-retry/commit/f9dddf9))



<a name="1.5.0"></a>
# 1.5.0 (2015-09-24)

* Actually add attempts test ([0db5402](https://github.com/FGRibreau/node-request-retry/commit/0db5402))
* Add the attempts property for retry attempt info.  Add a test for it.  Also fix maxAttempts being of ([a130355](https://github.com/FGRibreau/node-request-retry/commit/a130355))
* feat(changelog) ([bf0ff38](https://github.com/FGRibreau/node-request-retry/commit/bf0ff38))
* Release v1.5.0. ([1b7ca7c](https://github.com/FGRibreau/node-request-retry/commit/1b7ca7c))
* fix(dot-files): .env to ignored files ([145a33e](https://github.com/FGRibreau/node-request-retry/commit/145a33e))



<a name="1.4.1"></a>
## 1.4.1 (2015-09-21)

* docs(changelog) ([86ff058](https://github.com/FGRibreau/node-request-retry/commit/86ff058))
* Release v1.4.1. ([9f6cadf](https://github.com/FGRibreau/node-request-retry/commit/9f6cadf))
* Update dependencies ([5d51d4f](https://github.com/FGRibreau/node-request-retry/commit/5d51d4f))
* Update README.md ([2c92e64](https://github.com/FGRibreau/node-request-retry/commit/2c92e64))
* Update README.md ([4fc5e92](https://github.com/FGRibreau/node-request-retry/commit/4fc5e92))
* fix(package): rolled back version ([5a3628a](https://github.com/FGRibreau/node-request-retry/commit/5a3628a))



<a name="1.4.0"></a>
# 1.4.0 (2015-07-16)

* add EAI_AGAIN to the list of retriable network errors ([64b96ff](https://github.com/FGRibreau/node-request-retry/commit/64b96ff))
* add notes on request module to readme ([e7acf85](https://github.com/FGRibreau/node-request-retry/commit/e7acf85))
* Release v1.4.0. ([e890593](https://github.com/FGRibreau/node-request-retry/commit/e890593))
* feat(deps): upgrade request to 2.58.x ([e7d34a1](https://github.com/FGRibreau/node-request-retry/commit/e7d34a1))



<a name="1.3.1"></a>
## 1.3.1 (2015-05-06)

* Release v1.3.1. ([bef5dba](https://github.com/FGRibreau/node-request-retry/commit/bef5dba))
* docs(changelog): add changelog ([5ea057b](https://github.com/FGRibreau/node-request-retry/commit/5ea057b))
* feat(check-build): add check-build ([13c4029](https://github.com/FGRibreau/node-request-retry/commit/13c4029))



<a name="1.3.0"></a>
# 1.3.0 (2015-05-06)

* Release v1.3.0. ([07ac122](https://github.com/FGRibreau/node-request-retry/commit/07ac122))
* update dependencies for latest version ([30da1b0](https://github.com/FGRibreau/node-request-retry/commit/30da1b0))



<a name="1.2.2"></a>
## 1.2.2 (2015-01-03)

* docs(readme) ([94cc707](https://github.com/FGRibreau/node-request-retry/commit/94cc707))
* Release v1.2.2. ([4ee950b](https://github.com/FGRibreau/node-request-retry/commit/4ee950b))
* chore(package): update request deps ([ab0c20b](https://github.com/FGRibreau/node-request-retry/commit/ab0c20b))



<a name="1.2.1"></a>
## 1.2.1 (2014-11-10)

* add tags for versions ([1b9dddc](https://github.com/FGRibreau/node-request-retry/commit/1b9dddc))
* add write method ([ecce4c1](https://github.com/FGRibreau/node-request-retry/commit/ecce4c1))
* fix changelog ([fb9c2b0](https://github.com/FGRibreau/node-request-retry/commit/fb9c2b0))
* readme ([1e5e74a](https://github.com/FGRibreau/node-request-retry/commit/1e5e74a))
* Release v1.2.1. ([1a45d51](https://github.com/FGRibreau/node-request-retry/commit/1a45d51))
* update readme ([9c72e94](https://github.com/FGRibreau/node-request-retry/commit/9c72e94))
* Update README.md ([6db9b37](https://github.com/FGRibreau/node-request-retry/commit/6db9b37))



<a name="1.2.0"></a>
# 1.2.0 (2014-11-03)

* add editorconfig & jshintrc ([835d06a](https://github.com/FGRibreau/node-request-retry/commit/835d06a))
* add jshintrc ([145e422](https://github.com/FGRibreau/node-request-retry/commit/145e422))
* add strategies and support for user-defined `retryStrategy` ([12779c8](https://github.com/FGRibreau/node-request-retry/commit/12779c8))
* add tests for strateies ([fa48977](https://github.com/FGRibreau/node-request-retry/commit/fa48977))
* Release v1.2.0. ([103ae76](https://github.com/FGRibreau/node-request-retry/commit/103ae76))
* update readme ([e077d8e](https://github.com/FGRibreau/node-request-retry/commit/e077d8e))
* update readme ([d0b1750](https://github.com/FGRibreau/node-request-retry/commit/d0b1750))



<a name="1.1.0"></a>
# 1.1.0 (2014-10-27)

* add @juliendangers as contributor ([27206d8](https://github.com/FGRibreau/node-request-retry/commit/27206d8))
* apply original format ([04fa9c6](https://github.com/FGRibreau/node-request-retry/commit/04fa9c6))
* expose methods from internal Request (end, on, emit, once, setMaxListeners, start, removeListener, p ([7d2a0b5](https://github.com/FGRibreau/node-request-retry/commit/7d2a0b5))
* jsformat ([581179a](https://github.com/FGRibreau/node-request-retry/commit/581179a))
* Release v1.1.0. ([6708bb0](https://github.com/FGRibreau/node-request-retry/commit/6708bb0))
* setting the prototype inside the constructor, will do this at each instanciation. We don't need the  ([ea0f474](https://github.com/FGRibreau/node-request-retry/commit/ea0f474))
* update deps ([f62935c](https://github.com/FGRibreau/node-request-retry/commit/f62935c))
* update readme ([849b78b](https://github.com/FGRibreau/node-request-retry/commit/849b78b))



<a name="1.0.4"></a>
## 1.0.4 (2014-09-30)

* EPIPE support ([547ac71](https://github.com/FGRibreau/node-request-retry/commit/547ac71))
* Release v1.0.4. ([66e39be](https://github.com/FGRibreau/node-request-retry/commit/66e39be))



<a name="1.0.3"></a>
## 1.0.3 (2014-09-23)

* . ([370095e](https://github.com/FGRibreau/node-request-retry/commit/370095e))
* .gitignore ([f3b0566](https://github.com/FGRibreau/node-request-retry/commit/f3b0566))
* [v1.0.0] stable release, support for .abort() ([f235ed9](https://github.com/FGRibreau/node-request-retry/commit/f235ed9))
* Add cancelable ([c31eca0](https://github.com/FGRibreau/node-request-retry/commit/c31eca0))
* Added EHOSTUNREACH ([d29c41f](https://github.com/FGRibreau/node-request-retry/commit/d29c41f))
* First commit ([5f68f8f](https://github.com/FGRibreau/node-request-retry/commit/5f68f8f))
* Readme ([b6ac75f](https://github.com/FGRibreau/node-request-retry/commit/b6ac75f))
* Readme ([e524ce9](https://github.com/FGRibreau/node-request-retry/commit/e524ce9))
* Readme ([240f5c5](https://github.com/FGRibreau/node-request-retry/commit/240f5c5))
* Readme ([7e1a5cb](https://github.com/FGRibreau/node-request-retry/commit/7e1a5cb))
* Readme ([a8086e9](https://github.com/FGRibreau/node-request-retry/commit/a8086e9))
* Release v1.0.3. ([8ed86e6](https://github.com/FGRibreau/node-request-retry/commit/8ed86e6))
* Upgraded `request`, callback is now optional ([031a143](https://github.com/FGRibreau/node-request-retry/commit/031a143))
* v1.0.2 ([c0f3b97](https://github.com/FGRibreau/node-request-retry/commit/c0f3b97))



