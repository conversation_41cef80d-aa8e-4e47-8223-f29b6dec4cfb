{"version": 3, "file": "BoundNodeCallbackObservable.js", "sourceRoot": "", "sources": ["../../src/observable/BoundNodeCallbackObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAA2B,eAAe,CAAC,CAAA;AAK3C,yBAAyB,kBAAkB,CAAC,CAAA;AAC5C,4BAA4B,qBAAqB,CAAC,CAAA;AAClD,6BAA6B,iBAAiB,CAAC,CAAA;AAE/C;;;;GAIG;AACH;IAAoD,+CAAa;IAoJ/D,qCAAoB,YAAsB,EACtB,QAAkB,EAClB,IAAW,EACX,OAAY,EACb,SAAqB;QACtC,iBAAO,CAAC;QALU,iBAAY,GAAZ,YAAY,CAAU;QACtB,aAAQ,GAAR,QAAQ,CAAU;QAClB,SAAI,GAAJ,IAAI,CAAO;QACX,YAAO,GAAP,OAAO,CAAK;QACb,cAAS,GAAT,SAAS,CAAY;IAExC,CAAC;IA7ID,mCAAmC;IAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4HG;IACI,kCAAM,GAAb,UAAiB,IAAc,EACd,QAAqC,EACrC,SAAsB;QADtB,wBAAqC,GAArC,oBAAqC;QAEpD,MAAM,CAAC;YAAoB,cAAc;iBAAd,WAAc,CAAd,sBAAc,CAAd,IAAc;gBAAd,6BAAc;;YACvC,MAAM,CAAC,IAAI,2BAA2B,CAAI,IAAI,EAAO,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QACxF,CAAC,CAAC;IACJ,CAAC;IAUD,oCAAoC,CAAC,gDAAU,GAAV,UAAW,UAA+B;QAC7E,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE3B,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACf,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBACb,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,2BAAY,EAAK,CAAC;gBAC/C,IAAM,OAAO,GAAG;oBAA8B,mBAAmB;yBAAnB,WAAmB,CAAnB,sBAAmB,CAAnB,IAAmB;wBAAnB,kCAAmB;;oBAC/D,IAAM,MAAM,GAAS,SAAU,CAAC,MAAM,CAAC;oBAC/B,8BAAQ,EAAE,wBAAO,CAAY;oBACrC,IAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;oBAE9B,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wBACR,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACrB,CAAC;oBAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACpB,IAAM,QAAM,GAAG,mBAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;wBACzD,EAAE,CAAC,CAAC,QAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;4BAC3B,OAAO,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;wBAC/B,CAAC;wBAAC,IAAI,CAAC,CAAC;4BACN,OAAO,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;4BACrB,OAAO,CAAC,QAAQ,EAAE,CAAC;wBACrB,CAAC;oBACH,CAAC;oBAAC,IAAI,CAAC,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;wBAC/D,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACrB,CAAC;gBACH,CAAC,CAAC;gBACF,gDAAgD;gBAC1C,OAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;gBAE7B,IAAM,MAAM,GAAG,mBAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBAChF,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;oBAC3B,OAAO,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YACD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,sBAAU,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IACH,kCAAC;AAAD,CAAC,AAtMD,CAAoD,uBAAU,GAsM7D;AAtMY,mCAA2B,8BAsMvC,CAAA;AAQD,kBAAqD,KAAuB;IAC1E,IAAM,IAAI,GAAmB,IAAK,CAAC;IAC3B,yBAAM,EAAE,6BAAU,EAAE,uBAAO,CAAW;IAC9C,iEAAiE;IACjE,IAAA,WAAuD,EAA/C,8BAAY,EAAE,cAAI,EAAE,wBAAS,CAAmB;IACxD,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAE7B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACb,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,2BAAY,EAAK,CAAC;QAEjD,IAAM,OAAO,GAAG;YAA8B,mBAAmB;iBAAnB,WAAmB,CAAnB,sBAAmB,CAAnB,IAAmB;gBAAnB,kCAAmB;;YAC/D,IAAM,MAAM,GAAS,SAAU,CAAC,MAAM,CAAC;YAC/B,8BAAQ,EAAE,wBAAO,CAAY;YACrC,IAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YAE9B,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACR,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,QAAG,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;YACnE,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACpB,IAAM,QAAM,GAAG,mBAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACzD,EAAE,CAAC,CAAC,QAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;oBAC3B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,yBAAW,CAAC,CAAC,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;gBAClF,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,QAAM,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAM,KAAK,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;gBAC/D,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,YAAK,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC;QACF,uDAAuD;QACjD,OAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QAE/B,IAAM,MAAM,GAAG,mBAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3E,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;YAC3B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,yBAAW,CAAC,CAAC,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1C,CAAC;AAMD,sBAAyB,GAAuB;IACtC,qBAAK,EAAE,qBAAO,CAAS;IAC/B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC;AAMD,uBAA0B,GAAwB;IACxC,iBAAG,EAAE,qBAAO,CAAS;IAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC", "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { IScheduler } from '../Scheduler';\nimport { Action } from '../scheduler/Action';\nimport { tryCatch } from '../util/tryCatch';\nimport { errorObject } from '../util/errorObject';\nimport { AsyncSubject } from '../AsyncSubject';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class BoundNodeCallbackObservable<T> extends Observable<T> {\n  subject: AsyncSubject<T>;\n\n  /* tslint:disable:max-line-length */\n  static create<R>(callbackFunc: (callback: (err: any, result: R) => any) => any, selector?: void, scheduler?: IScheduler): () => Observable<R>;\n  static create<T, R>(callbackFunc: (v1: T, callback: (err: any, result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T) => Observable<R>;\n  static create<T, T2, R>(callbackFunc: (v1: T, v2: T2, callback: (err: any, result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2) => Observable<R>;\n  static create<T, T2, T3, R>(callbackFunc: (v1: T, v2: T2, v3: T3, callback: (err: any, result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3) => Observable<R>;\n  static create<T, T2, T3, T4, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, callback: (err: any, result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4) => Observable<R>;\n  static create<T, T2, T3, T4, T5, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, callback: (err: any, result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4, v5: T5) => Observable<R>;\n  static create<T, T2, T3, T4, T5, T6, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6, callback: (err: any, result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6) => Observable<R>;\n  static create<T>(callbackFunc: Function, selector?: void, scheduler?: IScheduler): (...args: any[]) => Observable<T>;\n  static create<T>(callbackFunc: Function, selector?: (...args: any[]) => T, scheduler?: IScheduler): (...args: any[]) => Observable<T>;\n  /* tslint:enable:max-line-length */\n\n  /**\n   * Converts a Node.js-style callback API to a function that returns an\n   * Observable.\n   *\n   * <span class=\"informal\">It's just like {@link bindCallback}, but the\n   * callback is expected to be of type `callback(error, result)`.</span>\n   *\n   * `bindNodeCallback` is not an operator because its input and output are not\n   * Observables. The input is a function `func` with some parameters, but the\n   * last parameter must be a callback function that `func` calls when it is\n   * done. The callback function is expected to follow Node.js conventions,\n   * where the first argument to the callback is an error object, signaling\n   * whether call was successful. If that object is passed to callback, it means\n   * something went wrong.\n   *\n   * The output of `bindNodeCallback` is a function that takes the same\n   * parameters as `func`, except the last one (the callback). When the output\n   * function is called with arguments, it will return an Observable.\n   * If `func` calls its callback with error parameter present, Observable will\n   * error with that value as well. If error parameter is not passed, Observable will emit\n   * second parameter. If there are more parameters (third and so on),\n   * Observable will emit an array with all arguments, except first error argument.\n   *\n   * Optionally `bindNodeCallback` accepts selector function, which allows you to\n   * make resulting Observable emit value computed by selector, instead of regular\n   * callback arguments. It works similarly to {@link bindCallback} selector, but\n   * Node.js-style error argument will never be passed to that function.\n   *\n   * Note that `func` will not be called at the same time output function is,\n   * but rather whenever resulting Observable is subscribed. By default call to\n   * `func` will happen synchronously after subscription, but that can be changed\n   * with proper {@link Scheduler} provided as optional third parameter. Scheduler\n   * can also control when values from callback will be emitted by Observable.\n   * To find out more, check out documentation for {@link bindCallback}, where\n   * Scheduler works exactly the same.\n   *\n   * As in {@link bindCallback}, context (`this` property) of input function will be set to context\n   * of returned function, when it is called.\n   *\n   * After Observable emits value, it will complete immediately. This means\n   * even if `func` calls callback again, values from second and consecutive\n   * calls will never appear on the stream. If you need to handle functions\n   * that call callbacks multiple times, check out {@link fromEvent} or\n   * {@link fromEventPattern} instead.\n   *\n   * Note that `bindNodeCallback` can be used in non-Node.js environments as well.\n   * \"Node.js-style\" callbacks are just a convention, so if you write for\n   * browsers or any other environment and API you use implements that callback style,\n   * `bindNodeCallback` can be safely used on that API functions as well.\n   *\n   * Remember that Error object passed to callback does not have to be an instance\n   * of JavaScript built-in `Error` object. In fact, it does not even have to an object.\n   * Error parameter of callback function is interpreted as \"present\", when value\n   * of that parameter is truthy. It could be, for example, non-zero number, non-empty\n   * string or boolean `true`. In all of these cases resulting Observable would error\n   * with that value. This means usually regular style callbacks will fail very often when\n   * `bindNodeCallback` is used. If your Observable errors much more often then you\n   * would expect, check if callback really is called in Node.js-style and, if not,\n   * switch to {@link bindCallback} instead.\n   *\n   * Note that even if error parameter is technically present in callback, but its value\n   * is falsy, it still won't appear in array emitted by Observable or in selector function.\n   *\n   *\n   * @example <caption>Read a file from the filesystem and get the data as an Observable</caption>\n   * import * as fs from 'fs';\n   * var readFileAsObservable = Rx.Observable.bindNodeCallback(fs.readFile);\n   * var result = readFileAsObservable('./roadNames.txt', 'utf8');\n   * result.subscribe(x => console.log(x), e => console.error(e));\n   *\n   *\n   * @example <caption>Use on function calling callback with multiple arguments</caption>\n   * someFunction((err, a, b) => {\n   *   console.log(err); // null\n   *   console.log(a); // 5\n   *   console.log(b); // \"some string\"\n   * });\n   * var boundSomeFunction = Rx.Observable.bindNodeCallback(someFunction);\n   * boundSomeFunction()\n   * .subscribe(value => {\n   *   console.log(value); // [5, \"some string\"]\n   * });\n   *\n   *\n   * @example <caption>Use with selector function</caption>\n   * someFunction((err, a, b) => {\n   *   console.log(err); // undefined\n   *   console.log(a); // \"abc\"\n   *   console.log(b); // \"DEF\"\n   * });\n   * var boundSomeFunction = Rx.Observable.bindNodeCallback(someFunction, (a, b) => a + b);\n   * boundSomeFunction()\n   * .subscribe(value => {\n   *   console.log(value); // \"abcDEF\"\n   * });\n   *\n   *\n   * @example <caption>Use on function calling callback in regular style</caption>\n   * someFunction(a => {\n   *   console.log(a); // 5\n   * });\n   * var boundSomeFunction = Rx.Observable.bindNodeCallback(someFunction);\n   * boundSomeFunction()\n   * .subscribe(\n   *   value => {}             // never gets called\n   *   err => console.log(err) // 5\n   *);\n   *\n   *\n   * @see {@link bindCallback}\n   * @see {@link from}\n   * @see {@link fromPromise}\n   *\n   * @param {function} func Function with a Node.js-style callback as the last parameter.\n   * @param {function} [selector] A function which takes the arguments from the\n   * callback and maps those to a value to emit on the output Observable.\n   * @param {Scheduler} [scheduler] The scheduler on which to schedule the\n   * callbacks.\n   * @return {function(...params: *): Observable} A function which returns the\n   * Observable that delivers the same values the Node.js callback would\n   * deliver.\n   * @static true\n   * @name bindNodeCallback\n   * @owner Observable\n   */\n  static create<T>(func: Function,\n                   selector: Function | void = undefined,\n                   scheduler?: IScheduler): (...args: any[]) => Observable<T> {\n    return function(this: any, ...args: any[]): Observable<T> {\n      return new BoundNodeCallbackObservable<T>(func, <any>selector, args, this, scheduler);\n    };\n  }\n\n  constructor(private callbackFunc: Function,\n              private selector: Function,\n              private args: any[],\n              private context: any,\n              public scheduler: IScheduler) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T | T[]>): Subscription {\n    const callbackFunc = this.callbackFunc;\n    const args = this.args;\n    const scheduler = this.scheduler;\n    let subject = this.subject;\n\n    if (!scheduler) {\n      if (!subject) {\n        subject = this.subject = new AsyncSubject<T>();\n        const handler = function handlerFn(this: any, ...innerArgs: any[]) {\n          const source = (<any>handlerFn).source;\n          const { selector, subject } = source;\n          const err = innerArgs.shift();\n\n          if (err) {\n            subject.error(err);\n          } else if (selector) {\n            const result = tryCatch(selector).apply(this, innerArgs);\n            if (result === errorObject) {\n              subject.error(errorObject.e);\n            } else {\n              subject.next(result);\n              subject.complete();\n            }\n          } else {\n            subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n            subject.complete();\n          }\n        };\n        // use named function instance to avoid closure.\n        (<any>handler).source = this;\n\n        const result = tryCatch(callbackFunc).apply(this.context, args.concat(handler));\n        if (result === errorObject) {\n          subject.error(errorObject.e);\n        }\n      }\n      return subject.subscribe(subscriber);\n    } else {\n      return scheduler.schedule(dispatch, 0, { source: this, subscriber, context: this.context });\n    }\n  }\n}\n\ninterface DispatchState<T> {\n  source: BoundNodeCallbackObservable<T>;\n  subscriber: Subscriber<T>;\n  context: any;\n}\n\nfunction dispatch<T>(this: Action<DispatchState<T>>, state: DispatchState<T>) {\n  const self = (<Subscription> this);\n  const { source, subscriber, context } = state;\n  // XXX: cast to `any` to access to the private field in `source`.\n  const { callbackFunc, args, scheduler } = source as any;\n  let subject = source.subject;\n\n  if (!subject) {\n    subject = source.subject = new AsyncSubject<T>();\n\n    const handler = function handlerFn(this: any, ...innerArgs: any[]) {\n      const source = (<any>handlerFn).source;\n      const { selector, subject } = source;\n      const err = innerArgs.shift();\n\n      if (err) {\n        self.add(scheduler.schedule(dispatchError, 0, { err, subject }));\n      } else if (selector) {\n        const result = tryCatch(selector).apply(this, innerArgs);\n        if (result === errorObject) {\n          self.add(scheduler.schedule(dispatchError, 0, { err: errorObject.e, subject }));\n        } else {\n          self.add(scheduler.schedule(dispatchNext, 0, { value: result, subject }));\n        }\n      } else {\n        const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n        self.add(scheduler.schedule(dispatchNext, 0, { value, subject }));\n      }\n    };\n    // use named function to pass values in without closure\n    (<any>handler).source = source;\n\n    const result = tryCatch(callbackFunc).apply(context, args.concat(handler));\n    if (result === errorObject) {\n      self.add(scheduler.schedule(dispatchError, 0, { err: errorObject.e, subject }));\n    }\n  }\n\n  self.add(subject.subscribe(subscriber));\n}\n\ninterface DispatchNextArg<T> {\n  subject: AsyncSubject<T>;\n  value: T;\n}\nfunction dispatchNext<T>(arg: DispatchNextArg<T>) {\n  const { value, subject } = arg;\n  subject.next(value);\n  subject.complete();\n}\n\ninterface DispatchErrorArg<T> {\n  subject: AsyncSubject<T>;\n  err: any;\n}\nfunction dispatchError<T>(arg: DispatchErrorArg<T>) {\n  const { err, subject } = arg;\n  subject.error(err);\n}\n"]}