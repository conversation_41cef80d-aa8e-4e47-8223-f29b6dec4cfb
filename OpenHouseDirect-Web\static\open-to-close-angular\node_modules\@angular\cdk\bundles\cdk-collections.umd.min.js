/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("rxjs/Subject"),require("@angular/core")):"function"==typeof define&&define.amd?define(["exports","rxjs/Subject","@angular/core"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.collections=e.ng.cdk.collections||{}),e.Rx,e.ng.core)}(this,function(e,t,n){"use strict";function i(){return Error("Cannot pass multiple values into SelectionModel with single-value mode.")}function s(e){return e||new l}var o=function(){function e(){}return e}(),r=function(){function e(e,n,i){void 0===e&&(e=!1),void 0===i&&(i=!0);var s=this;this._multiple=e,this._emitChanges=i,this._selection=new Set,this._deselectedToEmit=[],this._selectedToEmit=[],this.onChange=this._emitChanges?new t.Subject:null,n&&n.length&&(e?n.forEach(function(e){return s._markSelected(e)}):this._markSelected(n[0]),this._selectedToEmit.length=0)}return Object.defineProperty(e.prototype,"selected",{get:function(){return this._selected||(this._selected=Array.from(this._selection.values())),this._selected},enumerable:!0,configurable:!0}),e.prototype.select=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._verifyValueAssignment(t),t.forEach(function(t){return e._markSelected(t)}),this._emitChangeEvent()},e.prototype.deselect=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._verifyValueAssignment(t),t.forEach(function(t){return e._unmarkSelected(t)}),this._emitChangeEvent()},e.prototype.toggle=function(e){this.isSelected(e)?this.deselect(e):this.select(e)},e.prototype.clear=function(){this._unmarkAll(),this._emitChangeEvent()},e.prototype.isSelected=function(e){return this._selection.has(e)},e.prototype.isEmpty=function(){return 0===this._selection.size},e.prototype.hasValue=function(){return!this.isEmpty()},e.prototype.sort=function(e){this._multiple&&this._selected&&this._selected.sort(e)},e.prototype._emitChangeEvent=function(){if(this._selected=null,this._selectedToEmit.length||this._deselectedToEmit.length){var e=new c(this,this._selectedToEmit,this._deselectedToEmit);this.onChange&&this.onChange.next(e),this._deselectedToEmit=[],this._selectedToEmit=[]}},e.prototype._markSelected=function(e){this.isSelected(e)||(this._multiple||this._unmarkAll(),this._selection.add(e),this._emitChanges&&this._selectedToEmit.push(e))},e.prototype._unmarkSelected=function(e){this.isSelected(e)&&(this._selection.delete(e),this._emitChanges&&this._deselectedToEmit.push(e))},e.prototype._unmarkAll=function(){var e=this;this.isEmpty()||this._selection.forEach(function(t){return e._unmarkSelected(t)})},e.prototype._verifyValueAssignment=function(e){if(e.length>1&&!this._multiple)throw i()},e}(),c=function(){function e(e,t,n){this.source=e,this.added=t,this.removed=n}return e}(),l=function(){function e(){this._listeners=[]}return e.prototype.notify=function(e,t){for(var n=0,i=this._listeners;n<i.length;n++){(0,i[n])(e,t)}},e.prototype.listen=function(e){var t=this;return this._listeners.push(e),function(){t._listeners=t._listeners.filter(function(t){return e!==t})}},e.prototype.ngOnDestroy=function(){this._listeners=[]},e.decorators=[{type:n.Injectable}],e.ctorParameters=function(){return[]},e}(),u={provide:l,deps:[[new n.Optional,new n.SkipSelf,l]],useFactory:s};e.UniqueSelectionDispatcher=l,e.UNIQUE_SELECTION_DISPATCHER_PROVIDER=u,e.DataSource=o,e.SelectionModel=r,e.SelectionChange=c,e.getMultipleValuesInSingleSelectionError=i,e.ɵa=s,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-collections.umd.min.js.map
