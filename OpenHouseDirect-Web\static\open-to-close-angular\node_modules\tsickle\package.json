{"_from": "tsickle@^0.27.2", "_id": "tsickle@0.27.5", "_inBundle": false, "_integrity": "sha512-NP+CjM1EXza/M8mOXBLH3vkFEJiu1zfEAlC5WdJxHPn8l96QPz5eooP6uAgYtw1CcKfuSyIiheNUdKxtDWCNeg==", "_location": "/tsickle", "_phantomChildren": {"buffer-from": "1.1.2"}, "_requested": {"type": "range", "registry": true, "raw": "tsickle@^0.27.2", "name": "tsickle", "escapedName": "tsickle", "rawSpec": "^0.27.2", "saveSpec": null, "fetchSpec": "^0.27.2"}, "_requiredBy": ["/@angular/compiler-cli"], "_resolved": "https://registry.npmjs.org/tsickle/-/tsickle-0.27.5.tgz", "_shasum": "41e1a41a5acf971cbb2b0558a9590779234d591f", "_spec": "tsickle@^0.27.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\@angular\\compiler-cli", "bin": {"tsickle": "src/main.js"}, "bugs": {"url": "https://github.com/angular/tsickle/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://angular.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://angular.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://angular.io/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://angular.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"minimist": "^1.2.0", "mkdirp": "^0.5.1", "source-map": "^0.6.0", "source-map-support": "^0.5.0"}, "deprecated": false, "description": "Transpile TypeScript code to JavaScript with Closure annotations.", "devDependencies": {"@types/chai": "4.1.2", "@types/diff": "3.2.2", "@types/glob": "5.0.35", "@types/google-closure-compiler": "0.0.18", "@types/jasmine": "2.8.6", "@types/minimatch": "3.0.3", "@types/minimist": "1.2.0", "@types/mkdirp": "0.5.2", "@types/node": "6.0.102", "@types/source-map-support": "0.4.0", "chai": "4.1.2", "chai-diff": "1.0.1", "clang-format": "1.2.2", "diff": "3.5.0", "glob": "7.1.2", "google-closure-compiler": "20161024.3.0", "gulp": "3.9.1", "gulp-clang-format": "1.0.25", "gulp-tslint": "8.1.3", "gulp-typescript": "4.0.1", "jasmine": "3.1.0", "merge2": "1.2.1", "temp": "0.8.3", "tslint": "5.9.1", "typescript": "2.7.2"}, "directories": {"test": "test"}, "files": ["src/*"], "homepage": "https://github.com/angular/tsickle", "keywords": ["typescript", "closure"], "license": "MIT", "main": "src/tsickle.js", "name": "tsickle", "peerDependencies": {"typescript": ">=2.4.2 <2.8"}, "repository": {"type": "git", "url": "git+https://github.com/angular/tsickle.git"}, "scripts": {"build": "bazel build //:npm_package", "test": "gulp test.check-format && gulp test.check-lint && bazel test ..."}, "typings": "src/tsickle.d.ts", "version": "0.27.5"}