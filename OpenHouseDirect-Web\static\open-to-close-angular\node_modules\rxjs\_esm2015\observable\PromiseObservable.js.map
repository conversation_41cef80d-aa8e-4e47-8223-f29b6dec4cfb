{"version": 3, "file": "PromiseObservable.js", "sourceRoot": "", "sources": ["../../src/observable/PromiseObservable.ts"], "names": [], "mappings": "OAAO,EAAE,IAAI,EAAE,MAAM,cAAc;OAE5B,EAAE,UAAU,EAAE,MAAM,eAAe;AAI1C;;;;GAIG;AACH,uCAA0C,UAAU;IAkClD,YAAoB,OAAuB,EAAU,SAAsB;QACzE,OAAO,CAAC;QADU,YAAO,GAAP,OAAO,CAAgB;QAAU,cAAS,GAAT,SAAS,CAAa;IAE3E,CAAC;IAhCD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,OAAO,MAAM,CAAI,OAAuB,EAAE,SAAsB;QAC9D,MAAM,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAMD,oCAAoC,CAAC,UAAU,CAAC,UAAyB;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;YACtB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACnB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACvB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC5B,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,CAAC;YACH,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,OAAO,CAAC,IAAI,CACV,CAAC,KAAK;oBACJ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;oBACtB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACvB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACxB,CAAC;gBACH,CAAC,EACD,CAAC,GAAG;oBACF,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC,CACF;qBACA,IAAI,CAAC,IAAI,EAAE,GAAG;oBACb,kDAAkD;oBAClD,IAAI,CAAC,UAAU,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACnB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACvB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,OAAO,CAAC,IAAI,CACV,CAAC,KAAK;oBACJ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;oBACtB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;oBAC7E,CAAC;gBACH,CAAC,EACD,CAAC,GAAG;oBACF,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;oBAC5E,CAAC;gBACH,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG;oBACd,kDAAkD;oBAClD,IAAI,CAAC,UAAU,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAMD,sBAAyB,GAAuB;IAC9C,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;IAClC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QACvB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,UAAU,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;AACH,CAAC;AAMD,uBAA0B,GAAwB;IAChD,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;IAChC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QACvB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;AACH,CAAC"}