{"version": 3, "file": "zipAll.js", "sourceRoot": "", "sources": ["../../../src/add/operator/zipAll.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,uBAAuB,uBAAuB,CAAC,CAAA;AAE/C,uBAAU,CAAC,SAAS,CAAC,MAAM,GAAG,eAAM,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { zipAll } from '../../operator/zipAll';\n\nObservable.prototype.zipAll = zipAll;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    zipAll: typeof zipAll;\n  }\n}"]}