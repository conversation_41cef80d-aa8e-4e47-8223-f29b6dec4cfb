{"_from": "source-map@^0.7.3", "_id": "source-map@0.7.6", "_inBundle": false, "_integrity": "sha512-i5uvt8C3ikiWeNZSVZNWcfZPItFQOsYTUAOkcUPGd8DqDy1uOUikjt5dG+uRlwyvR108Fb9DOd4GvXfT0N2/uQ==", "_location": "/stylus/source-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "source-map@^0.7.3", "name": "source-map", "escapedName": "source-map", "rawSpec": "^0.7.3", "saveSpec": null, "fetchSpec": "^0.7.3"}, "_requiredBy": ["/stylus"], "_resolved": "https://registry.npmjs.org/source-map/-/source-map-0.7.6.tgz", "_shasum": "a3658ab87e5b6429c8a1f3ba0083d4c61ca3ef02", "_spec": "source-map@^0.7.3", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\stylus", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"./lib/read-wasm.js": "./lib/read-wasm-browser.js"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "deprecated": false, "description": "Generates and consumes source maps", "devDependencies": {"c8": "^7.12.0", "doctoc": "^2.2.1", "eslint": "^8.24.0", "eslint-config-prettier": "^8.5.0", "prettier": "^2.7.1"}, "engines": {"node": ">= 12"}, "files": ["source-map.js", "source-map.d.ts", "lib/"], "homepage": "https://github.com/mozilla/source-map", "license": "BSD-3-<PERSON><PERSON>", "main": "./source-map.js", "name": "source-map", "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "scripts": {"clean": "rm -rf coverage", "coverage": "c8 --reporter=text --reporter=html npm test", "lint": "eslint --fix *.js lib/ test/ --ignore-pattern 'test/source-map-tests/**'", "prettier": "prettier --write .", "test": "git submodule update --init --recursive; node test/run-tests.js", "toc": "doctoc --github --notitle README.md CONTRIBUTING.md"}, "types": "./source-map.d.ts", "version": "0.7.6", "warnings": [{"code": "ENOTSUP", "required": {"node": ">= 12"}, "pkgid": "source-map@0.7.6"}]}