{"version": 3, "file": "pt-GW.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/pt-GW.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;QAClE,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC;KAC/E;IACD;QACE,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;QAClE,AADmE;KAEpE;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['meia-noite', 'meio-dia', 'manhã', 'tarde', 'noite', 'madrugada'],\n    ['meia-noite', 'meio-dia', 'da manhã', 'da tarde', 'da noite', 'da madrugada'],\n  ],\n  [\n    ['meia-noite', 'meio-dia', 'manhã', 'tarde', 'noite', 'madrugada'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '19:00'], ['19:00', '24:00'],\n    ['00:00', '06:00']\n  ]\n];\n"]}