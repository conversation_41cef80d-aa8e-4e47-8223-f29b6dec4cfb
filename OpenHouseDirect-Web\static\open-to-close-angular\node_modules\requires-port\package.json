{"_from": "requires-port@^1.0.0", "_id": "requires-port@1.0.0", "_inBundle": false, "_integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==", "_location": "/requires-port", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "requires-port@^1.0.0", "name": "requires-port", "escapedName": "requires-port", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/amqplib/url-parse", "/http-proxy", "/url-parse"], "_resolved": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "_shasum": "925d2601d39ac485e091cf0da5c6e694dc3dcaff", "_spec": "requires-port@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\http-proxy", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/unshiftio/requires-port/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a protocol requires a certain port number to be added to an URL.", "devDependencies": {"assume": "1.3.x", "istanbul": "0.4.x", "mocha": "2.3.x", "pre-commit": "1.1.x"}, "homepage": "https://github.com/unshiftio/requires-port", "keywords": ["port", "require", "http", "https", "ws", "wss", "gopher", "file", "ftp", "requires", "requried", "portnumber", "url", "parsing", "validation", "cows"], "license": "MIT", "main": "index.js", "name": "requires-port", "repository": {"type": "git", "url": "git+https://github.com/unshiftio/requires-port.git"}, "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "coverage": "istanbul cover _mocha -- test.js", "test": "mocha test.js", "test-travis": "istanbul cover _mocha --report lcovonly -- test.js", "watch": "mocha --watch test.js"}, "version": "1.0.0"}