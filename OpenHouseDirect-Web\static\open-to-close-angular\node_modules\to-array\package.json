{"_from": "to-array@0.1.4", "_id": "to-array@0.1.4", "_inBundle": false, "_integrity": "sha512-LhVdShQD/4Mk4zXNroIQZJC+Ap3zgLcDuwEdcmLv9CCO73NWockQDwyUnW/m8VX/EElfL6FcYx7EeutN4HJA6A==", "_location": "/to-array", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "to-array@0.1.4", "name": "to-array", "escapedName": "to-array", "rawSpec": "0.1.4", "saveSpec": null, "fetchSpec": "0.1.4"}, "_requiredBy": ["/socket.io-client"], "_resolved": "https://registry.npmjs.org/to-array/-/to-array-0.1.4.tgz", "_shasum": "17e6c11f73dd4f3d74cda7a4ff3238e9ad9bf890", "_spec": "to-array@0.1.4", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\socket.io-client", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/to-array/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "component": {"scripts": {"to-array/index.js": "index.js"}}, "contributors": [{"name": "<PERSON>"}], "dependencies": {}, "deprecated": false, "description": "Turn an array like into an array", "devDependencies": {"tap": "~0.3.1"}, "homepage": "https://github.com/Raynos/to-array", "keywords": [], "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/to-array/raw/master/LICENSE"}], "main": "index", "name": "to-array", "repository": {"type": "git", "url": "git://github.com/Raynos/to-array.git"}, "scripts": {"test": "tap --stderr --tap ./test"}, "version": "0.1.4"}