{"_from": "object.omit@^2.0.0", "_id": "object.omit@2.0.1", "_inBundle": false, "_integrity": "sha512-UiAM5mhmIuKLsOvrL+B0U2d1hXHF3bFYWIuH1LMpuV2EJEHG1Ntz06PgLEHjm6VFd87NpH8rastvPoyv6UW2fA==", "_location": "/object.omit", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object.omit@^2.0.0", "name": "object.omit", "escapedName": "object.omit", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/micromatch"], "_resolved": "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz", "_shasum": "1a9c744829f39dbb858c76ca3579ae2a54ebd1fa", "_spec": "object.omit@^2.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/object.omit/issues"}, "bundleDependencies": false, "dependencies": {"for-own": "^0.1.4", "is-extendable": "^0.1.1"}, "deprecated": false, "description": "Return a copy of an object excluding the given key, or array of keys. Also accepts an optional filter function as the last argument.", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2", "should": "^11.1.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/object.omit", "keywords": ["clear", "delete", "key", "object", "omit", "property", "remove", "value"], "license": "MIT", "main": "index.js", "name": "object.omit", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/object.omit.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["object.defaults", "object.filter", "object.pick", "object.pluck", "object.reduce"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb", "verb-generate-readme"]}, "version": "2.0.1"}