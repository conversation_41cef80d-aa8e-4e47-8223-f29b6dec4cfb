{"version": 3, "file": "sl.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/sl.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;QAC9C,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;QAChE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;KAC/E;IACD;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC3C,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;QAC1D,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC;KACtE;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['24.00', '12.00', 'zj', 'd', 'p', 'zv', 'po'],\n    ['opoln.', 'opold.', 'zjut.', 'dop.', 'pop.', 'zveč.', 'ponoči'],\n    ['opolnoči', 'opoldne', 'zjutraj', 'dopoldan', 'popoldan', 'zvečer', 'ponoči']\n  ],\n  [\n    ['24.00', '12.00', 'j', 'd', 'p', 'v', 'n'],\n    ['poln.', 'pold.', 'jut.', 'dop.', 'pop.', 'zve<PERSON>.', 'no<PERSON>'],\n    ['pol<PERSON><PERSON>', 'poldne', 'jutro', 'dopoldne', 'popoldne', 'večer', 'no<PERSON>']\n  ],\n  [\n    '00:00', '12:00', ['06:00', '10:00'], ['10:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '22:00'], ['22:00', '06:00']\n  ]\n];\n"]}