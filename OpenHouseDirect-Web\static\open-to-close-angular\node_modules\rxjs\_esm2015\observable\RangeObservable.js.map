{"version": 3, "file": "RangeObservable.js", "sourceRoot": "", "sources": ["../../src/observable/RangeObservable.ts"], "names": [], "mappings": "OACO,EAAE,UAAU,EAAE,MAAM,eAAe;AAI1C;;;;GAIG;AACH,qCAAqC,UAAU;IA+D7C,YAAY,KAAa,EACb,KAAa,EACb,SAAsB;QAChC,OAAO,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IApED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACH,OAAO,MAAM,CAAC,KAAK,GAAW,CAAC,EACjB,KAAK,GAAW,CAAC,EACjB,SAAsB;QAClC,MAAM,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACtD,CAAC;IAED,OAAO,QAAQ,CAAC,KAAU;QAExB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;QAElD,EAAE,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;YACnB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QACxB,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QAEjB,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAeD,oCAAoC,CAAC,UAAU,CAAC,UAA8B;QAC5E,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACrD,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;aAChC,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC;gBACF,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC;oBACrB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACtB,KAAK,CAAC;gBACR,CAAC;gBACD,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;gBACzB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACtB,KAAK,CAAC;gBACR,CAAC;YACH,CAAC,QAAQ,IAAI,EAAE;QACjB,CAAC;IACH,CAAC;AACH,CAAC;AAAA"}