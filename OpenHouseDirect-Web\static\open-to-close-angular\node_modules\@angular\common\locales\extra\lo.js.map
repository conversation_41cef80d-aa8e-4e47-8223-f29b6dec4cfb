{"version": 3, "file": "lo.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/lo.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ;YAC1C,QAAQ;SACT;QACD;YACE,SAAS,EAAE,SAAS,EAAE,UAAU;YAChC,SAAS,EAAE,QAAQ,EAAE,QAAQ;SAC9B;QACD;YACE,SAAS,EAAE,SAAS,EAAE,UAAU;YAChC,SAAS,EAAE,QAAQ,EAAE,WAAW;SACjC;KACF;IACD;QACE,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;QACtC;YACE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK;YAC1C,UAAU;SACX;QACD;YACE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK;YACrC,KAAK,EAAE,UAAU;SAClB;KACF;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'ທຄ', 'ທ', 'ຕອນເຊົ້າ', 'ຕອນທ່ຽງ', 'ຕອນແລງ',\n      'ກາງຄືນ'\n    ],\n    [\n      'ທ່ຽງຄືນ', 'ຕອນທ່ຽງ', 'ຕອນເຊົ້າ',\n      'ຕອນບ່າຍ', 'ຕອນແລງ', 'ກາງຄືນ'\n    ],\n    [\n      'ທ່ຽງຄືນ', 'ຕອນທ່ຽງ', 'ຕອນເຊົ້າ',\n      'ຕອນບ່າຍ', 'ຕອນແລງ', 'ຕອນກາງຄືນ'\n    ]\n  ],\n  [\n    ['ທຄ', 'ຕອນທ່ຽງ', 'ຊ', 'ສ', 'ລ', 'ກຄ'],\n    [\n      'ທ່ຽງ​ຄືນ', 'ທ່ຽງ', '​ເຊົ້າ', 'ສວຍ', 'ແລງ',\n      '​ກາງ​ຄືນ'\n    ],\n    [\n      'ທ່ຽງຄືນ', 'ຕອນທ່ຽງ', '​ເຊົ້າ', 'ສວຍ',\n      'ແລງ', '​ກາງ​ຄືນ'\n    ]\n  ],\n  [\n    '00:00', '12:00', ['05:00', '12:00'], ['12:00', '16:00'], ['16:00', '20:00'],\n    ['20:00', '05:00']\n  ]\n];\n"]}