{"version": 3, "file": "cdk-scrolling.umd.min.js", "sources": ["../../src/cdk/scrolling/scroll-dispatcher.ts", "../../src/cdk/scrolling/viewport-ruler.ts", "../../src/cdk/scrolling/scrollable.ts", "../../src/cdk/scrolling/scrolling-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementRef, Injectable, NgZone, Optional, SkipSelf, OnDestroy} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Observable} from 'rxjs/Observable';\nimport {of as observableOf} from 'rxjs/observable/of';\nimport {fromEvent} from 'rxjs/observable/fromEvent';\nimport {auditTime} from 'rxjs/operators/auditTime';\nimport {filter} from 'rxjs/operators/filter';\nimport {CdkScrollable} from './scrollable';\n\n\n/** Time in ms to throttle the scrolling events by default. */\nexport const DEFAULT_SCROLL_TIME = 20;\n\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\n@Injectable()\nexport class ScrollDispatcher implements OnDestroy {\n  constructor(private _ngZone: NgZone, private _platform: Platform) { }\n\n  /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n  private _scrolled = new Subject<CdkScrollable|void>();\n\n  /** Keeps track of the global `scroll` and `resize` subscriptions. */\n  _globalSubscription: Subscription | null = null;\n\n  /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n  private _scrolledCount = 0;\n\n  /**\n   * Map of all the scrollable references that are registered with the service and their\n   * scroll event subscriptions.\n   */\n  scrollContainers: Map<CdkScrollable, Subscription> = new Map();\n\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n  register(scrollable: CdkScrollable): void {\n    const scrollSubscription = scrollable.elementScrolled()\n        .subscribe(() => this._scrolled.next(scrollable));\n\n    this.scrollContainers.set(scrollable, scrollSubscription);\n  }\n\n  /**\n   * Deregisters a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n  deregister(scrollable: CdkScrollable): void {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n  scrolled(auditTimeInMs: number = DEFAULT_SCROLL_TIME): Observable<CdkScrollable|void> {\n    return this._platform.isBrowser ? Observable.create(observer => {\n      if (!this._globalSubscription) {\n        this._addGlobalListener();\n      }\n\n      // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n      const subscription = auditTimeInMs > 0 ?\n        this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) :\n        this._scrolled.subscribe(observer);\n\n      this._scrolledCount++;\n\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n\n        if (!this._scrolledCount) {\n          this._removeGlobalListener();\n        }\n      };\n    }) : observableOf<void>();\n  }\n\n  ngOnDestroy() {\n    this._removeGlobalListener();\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n  }\n\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n  ancestorScrolled(elementRef: ElementRef, auditTimeInMs?: number): Observable<CdkScrollable|void> {\n    const ancestors = this.getAncestorScrollContainers(elementRef);\n\n    return this.scrolled(auditTimeInMs).pipe(filter(target => {\n      return !target || ancestors.indexOf(target) > -1;\n    }));\n  }\n\n  /** Returns all registered Scrollables that contain the provided element. */\n  getAncestorScrollContainers(elementRef: ElementRef): CdkScrollable[] {\n    const scrollingContainers: CdkScrollable[] = [];\n\n    this.scrollContainers.forEach((_subscription: Subscription, scrollable: CdkScrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n\n    return scrollingContainers;\n  }\n\n  /** Returns true if the element is contained within the provided Scrollable. */\n  private _scrollableContainsElement(scrollable: CdkScrollable, elementRef: ElementRef): boolean {\n    let element = elementRef.nativeElement;\n    let scrollableElement = scrollable.getElementRef().nativeElement;\n\n    // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n    do {\n      if (element == scrollableElement) { return true; }\n    } while (element = element.parentElement);\n\n    return false;\n  }\n\n  /** Sets up the global scroll listeners. */\n  private _addGlobalListener() {\n    this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n      return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n    });\n  }\n\n  /** Cleans up the global scroll listener. */\n  private _removeGlobalListener() {\n    if (this._globalSubscription) {\n      this._globalSubscription.unsubscribe();\n      this._globalSubscription = null;\n    }\n  }\n}\n\n/** @docs-private */\nexport function SCROLL_DISPATCHER_PROVIDER_FACTORY(\n    parentDispatcher: ScrollDispatcher, ngZone: NgZone, platform: Platform) {\n  return parentDispatcher || new ScrollDispatcher(ngZone, platform);\n}\n\n/** @docs-private */\nexport const SCROLL_DISPATCHER_PROVIDER = {\n  // If there is already a ScrollDispatcher available, use that. Otherwise, provide a new one.\n  provide: ScrollDispatcher,\n  deps: [[new Optional(), new SkipSelf(), ScrollDispatcher], NgZone, Platform],\n  useFactory: SCROLL_DISPATCHER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Optional, SkipSelf, NgZone, OnDestroy} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\nimport {Observable} from 'rxjs/Observable';\nimport {fromEvent} from 'rxjs/observable/fromEvent';\nimport {merge} from 'rxjs/observable/merge';\nimport {auditTime} from 'rxjs/operators/auditTime';\nimport {Subscription} from 'rxjs/Subscription';\nimport {of as observableOf} from 'rxjs/observable/of';\n\n/** Time in ms to throttle the resize events by default. */\nexport const DEFAULT_RESIZE_TIME = 20;\n\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\n@Injectable()\nexport class ViewportRuler implements OnDestroy {\n  /** Cached viewport dimensions. */\n  private _viewportSize: {width: number; height: number};\n\n  /** Stream of viewport change events. */\n  private _change: Observable<Event>;\n\n  /** Subscription to streams that invalidate the cached viewport dimensions. */\n  private _invalidateCache: Subscription;\n\n  constructor(platform: Platform, ngZone: NgZone) {\n    this._change = platform.isBrowser ? ngZone.runOutsideAngular(() => {\n      return merge<Event>(fromEvent(window, 'resize'), fromEvent(window, 'orientationchange'));\n    }) : observableOf();\n\n    this._invalidateCache = this.change().subscribe(() => this._updateViewportSize());\n  }\n\n  ngOnDestroy() {\n    this._invalidateCache.unsubscribe();\n  }\n\n  /** Returns the viewport's width and height. */\n  getViewportSize(): Readonly<{width: number, height: number}> {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n\n    return {width: this._viewportSize.width, height: this._viewportSize.height};\n  }\n\n  /** Gets a ClientRect for the viewport's bounds. */\n  getViewportRect(): ClientRect {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {width, height} = this.getViewportSize();\n\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width,\n    };\n  }\n\n  /** Gets the (top, left) scroll position of the viewport. */\n  getViewportScrollPosition() {\n    // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n    const documentRect = document.documentElement.getBoundingClientRect();\n\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY ||\n                 document.documentElement.scrollTop || 0;\n\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX ||\n                  document.documentElement.scrollLeft || 0;\n\n    return {top, left};\n  }\n\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * @param throttle Time in milliseconds to throttle the stream.\n   */\n  change(throttleTime: number = DEFAULT_RESIZE_TIME): Observable<Event> {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n\n  /** Updates the cached viewport size. */\n  private _updateViewportSize() {\n    this._viewportSize = {width: window.innerWidth, height: window.innerHeight};\n  }\n}\n\n/** @docs-private */\nexport function VIEWPORT_RULER_PROVIDER_FACTORY(parentRuler: ViewportRuler,\n                                                platform: Platform,\n                                                ngZone: NgZone) {\n  return parentRuler || new ViewportRuler(platform, ngZone);\n}\n\n/** @docs-private */\nexport const VIEWPORT_RULER_PROVIDER = {\n  // If there is already a ViewportRuler available, use that. Otherwise, provide a new one.\n  provide: ViewportRuler,\n  deps: [[new Optional(), new SkipSelf(), ViewportRuler], Platform, NgZone],\n  useFactory: VIEWPORT_RULER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, OnInit, OnDestroy, NgZone} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Subject} from 'rxjs/Subject';\nimport {ScrollDispatcher} from './scroll-dispatcher';\n\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\n@Directive({\n  selector: '[cdk-scrollable], [cdkScrollable]'\n})\nexport class CdkScrollable implements OnInit, OnDestroy {\n  private _elementScrolled: Subject<Event> = new Subject();\n  private _scrollListener = (event: Event) => this._elementScrolled.next(event);\n\n  constructor(private _elementRef: ElementRef,\n              private _scroll: ScrollDispatcher,\n              private _ngZone: NgZone) {}\n\n  ngOnInit() {\n    this._ngZone.runOutsideAngular(() => {\n      this.getElementRef().nativeElement.addEventListener('scroll', this._scrollListener);\n    });\n\n    this._scroll.register(this);\n  }\n\n  ngOnDestroy() {\n    this._scroll.deregister(this);\n\n    if (this._scrollListener) {\n      this.getElementRef().nativeElement.removeEventListener('scroll', this._scrollListener);\n    }\n  }\n\n  /**\n   * Returns observable that emits when a scroll event is fired on the host element.\n   */\n  elementScrolled(): Observable<any> {\n    return this._elementScrolled.asObservable();\n  }\n\n  getElementRef(): ElementRef {\n    return this._elementRef;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {SCROLL_DISPATCHER_PROVIDER} from './scroll-dispatcher';\nimport {CdkScrollable} from  './scrollable';\nimport {PlatformModule} from '@angular/cdk/platform';\n\n@NgModule({\n  imports: [PlatformModule],\n  exports: [CdkScrollable],\n  declarations: [CdkScrollable],\n  providers: [SCROLL_DISPATCHER_PROVIDER],\n})\nexport class ScrollDispatchModule {}\n"], "names": ["SCROLL_DISPATCHER_PROVIDER_FACTORY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngZone", "platform", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VIEWPORT_RULER_PROVIDER_FACTORY", "parentRuler", "ViewportRuler", "_ngZone", "_platform", "this", "_scrolled", "Subject", "_globalSubscription", "_scrolledCount", "scrollContainers", "Map", "prototype", "register", "scrollable", "_this", "scrollSubscription", "elementScrolled", "subscribe", "next", "set", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "Observable", "create", "observer", "_addGlobalListener", "subscription", "pipe", "auditTime", "_removeGlobalListener", "observableOf", "ngOnDestroy", "for<PERSON>ach", "_", "container", "ancestorScrolled", "elementRef", "ancestors", "getAncestorScrollContainers", "filter", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "element", "nativeElement", "scrollableElement", "getElementRef", "parentElement", "runOutsideAngular", "fromEvent", "window", "document", "type", "Injectable", "NgZone", "Platform", "SCROLL_DISPATCHER_PROVIDER", "provide", "deps", "Optional", "SkipSelf", "useFactory", "CdkScrollable", "_elementRef", "_scroll", "_elementScrolled", "_scrollListener", "event", "ngOnInit", "addEventListener", "removeEventListener", "asObservable", "Directive", "args", "selector", "ElementRef", "_change", "merge", "_invalidateCache", "change", "_updateViewportSize", "getViewportSize", "_viewportSize", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "_a", "top", "left", "bottom", "right", "documentRect", "documentElement", "getBoundingClientRect", "body", "scrollTop", "scrollY", "scrollLeft", "scrollX", "throttleTime", "innerWidth", "innerHeight", "VIEWPORT_RULER_PROVIDER", "ScrollDispatchModule", "NgModule", "imports", "PlatformModule", "exports", "declarations", "providers"], "mappings": ";;;;;;;61BAyKA,SAAAA,GACIC,EAAoCC,EAAgBC,GACtD,MAAOF,IAAoB,GAAIG,GAAiBF,EAAQC,GCzD1D,QAAAE,GAAgDC,EACAH,EACAD,GAC9C,MAAOI,IAAe,GAAIC,GAAcJ,EAAUD,GDhGpD,iBAQE,QAAFE,GAAsBI,EAAyBC,GAAzBC,KAAtBF,QAAsBA,EAAyBE,KAA/CD,UAA+CA,EAG/CC,KAAAC,UAAsB,GAAIC,GAAAA,QAG1BF,KAAAG,oBAA6C,KAG7CH,KAAAI,eAA2B,EAM3BJ,KAAAK,iBAAuD,GAAIC,KA5C3D,MAmDEZ,GAAFa,UAAAC,SAAE,SAASC,GAAT,GAAFC,GAAAV,KACUW,EAAqBF,EAAWG,kBACjCC,UAAU,WAAM,MAAAH,GAAKT,UAAUa,KAAKL,IAEzCT,MAAKK,iBAAiBU,IAAIN,EAAYE,IAOxCjB,EAAFa,UAAAS,WAAE,SAAWP,GACT,GAAMQ,GAAsBjB,KAAKK,iBAAiBa,IAAIT,EAElDQ,KACFA,EAAoBE,cACpBnB,KAAKK,iBAAiBe,OAAOX,KAcjCf,EAAFa,UAAAc,SAAE,SAASC,GAAT,GAAFZ,GAAAV,IACI,YADJ,KAAAsB,IAAWA,EA5DwB,IA6DxBtB,KAAKD,UAAUwB,UAAYC,EAAAA,WAAWC,OAAO,SAAAC,GAC7ChB,EAAKP,qBACRO,EAAKiB,oBAKP,IAAMC,GAAeN,EAAgB,EACnCZ,EAAKT,UAAU4B,KAAKC,EAAAA,UAAUR,IAAgBT,UAAUa,GACxDhB,EAAKT,UAAUY,UAAUa,EAI3B,OAFAhB,GAAKN,iBAEE,WACLwB,EAAaT,gBACbT,EAAKN,gBAGHM,EAAKqB,2BAGNC,EAAAA,MAGPtC,EAAFa,UAAA0B,YAAE,WAAA,GAAFvB,GAAAV,IACIA,MAAK+B,wBACL/B,KAAKK,iBAAiB6B,QAAQ,SAACC,EAAGC,GAAc,MAAA1B,GAAKM,WAAWoB,MASlE1C,EAAFa,UAAA8B,iBAAE,SAAiBC,EAAwBhB,GACvC,GAAMiB,GAAYvC,KAAKwC,4BAA4BF,EAEnD,OAAOtC,MAAKqB,SAASC,GAAeO,KAAKY,EAAAA,OAAO,SAAAC,GAC9C,OAAQA,GAAUH,EAAUI,QAAQD,IAAW,MAKnDhD,EAAFa,UAAAiC,4BAAE,SAA4BF,GAA5B,GAAF5B,GAAAV,KACU4C,IAQN,OANA5C,MAAKK,iBAAiB6B,QAAQ,SAACW,EAA6BpC,GACtDC,EAAKoC,2BAA2BrC,EAAY6B,IAC9CM,EAAoBG,KAAKtC,KAItBmC,GAIDlD,EAAVa,UAAAuC,2BAAA,SAAqCrC,EAA2B6B,GAC5D,GAAIU,GAAUV,EAAWW,cACrBC,EAAoBzC,EAAW0C,gBAAgBF,aAInD,IACE,GAAID,GAAWE,EAAqB,OAAO,QACpCF,EAAUA,EAAQI,cAE3B,QAAO,GAID1D,EAAVa,UAAAoB,wCACI3B,MAAKG,oBAAsBH,KAAKF,QAAQuD,kBAAkB,WACxD,MAAOC,GAAAA,UAAUC,OAAOC,SAAU,UAAU3C,UAAU,WAAM,MAAAH,GAAKT,UAAUa,YAKvEpB,EAAVa,UAAAwB,iCACQ/B,KAAKG,sBACPH,KAAKG,oBAAoBgB,cACzBnB,KAAKG,oBAAsB,sBAxIjCsD,KAACC,EAAAA,iDAnBDD,KAAgCE,EAAAA,SAChCF,KAAQG,EAAAA,YATRlE,KA+KamE,GAEXC,QAASpE,EACTqE,OAAQ,GAAIC,GAAAA,SAAY,GAAIC,GAAAA,SAAYvE,GAAmBiE,EAAAA,OAAQC,EAAAA,UACnEM,WAAY5E,gBEzJZ,QAAF6E,GAAsBC,EACAC,EACAvE,GAFpB,GAAFY,GAAAV,IAAsBA,MAAtBoE,YAAsBA,EACApE,KAAtBqE,QAAsBA,EACArE,KAAtBF,QAAsBA,EALtBE,KAAAsE,iBAA6C,GAAIpE,GAAAA,QACjDF,KAAAuE,gBAA4B,SAACC,GAAiB,MAAA9D,GAAK4D,iBAAiBxD,KAAK0D,IAxBzE,MA8BEL,GAAF5D,UAAAkE,SAAE,WAAA,GAAF/D,GAAAV,IACIA,MAAKF,QAAQuD,kBAAkB,WAC7B3C,EAAKyC,gBAAgBF,cAAcyB,iBAAiB,SAAUhE,EAAK6D,mBAGrEvE,KAAKqE,QAAQ7D,SAASR,OAGxBmE,EAAF5D,UAAA0B,YAAE,WACEjC,KAAKqE,QAAQrD,WAAWhB,MAEpBA,KAAKuE,iBACPvE,KAAKmD,gBAAgBF,cAAc0B,oBAAoB,SAAU3E,KAAKuE,kBAO1EJ,EAAF5D,UAAAK,gBAAE,WACE,MAAOZ,MAAKsE,iBAAiBM,gBAG/BT,EAAF5D,UAAA4C,cAAE,WACE,MAAOnD,MAAKoE,4BAnChBX,KAACoB,EAAAA,UAADC,OACEC,SAAU,4EAZZtB,KAAmBuB,EAAAA,aAGnBvB,KAAQ/D,IAHR+D,KAAkDE,EAAAA,UARlDQ,kBDmCE,QAAFtE,GAAcJ,EAAoBD,GAAhC,GAAFkB,GAAAV,IACIA,MAAKiF,QAAUxF,EAAS8B,UAAY/B,EAAO6D,kBAAkB,WAC3D,MAAO6B,GAAAA,MAAa5B,EAAAA,UAAUC,OAAQ,UAAWD,EAAAA,UAAUC,OAAQ,wBAChEvB,EAAAA,KAELhC,KAAKmF,iBAAmBnF,KAAKoF,SAASvE,UAAU,WAAM,MAAAH,GAAK2E,wBAxC/D,MA2CExF,GAAFU,UAAA0B,YAAE,WACEjC,KAAKmF,iBAAiBhE,eAIxBtB,EAAFU,UAAA+E,gBAAE,WAKE,MAJKtF,MAAKuF,eACRvF,KAAKqF,uBAGCG,MAAOxF,KAAKuF,cAAcC,MAAOC,OAAQzF,KAAKuF,cAAcE,SAItE5F,EAAFU,UAAAmF,gBAAE,WAUE,GAAMC,GAAiB3F,KAAK4F,4BAChCC,EAAA7F,KAAAsF,kBAAWE,EAAXK,EAAAL,MAAkBC,EAAlBI,EAAAJ,MAEI,QACEK,IAAKH,EAAeG,IACpBC,KAAMJ,EAAeI,KACrBC,OAAQL,EAAeG,IAAML,EAC7BQ,MAAON,EAAeI,KAAOP,EAC7BC,OAANA,EACMD,MAANA,IAKE3F,EAAFU,UAAAqF,0BAAE,WAOE,GAAMM,GAAe1C,SAAS2C,gBAAgBC,uBAQ9C,QAAQN,KANKI,EAAaJ,KAAOtC,SAAS6C,KAAKC,WAAa/C,OAAOgD,SACtD/C,SAAS2C,gBAAgBG,WAAa,EAKtCP,MAHCG,EAAaH,MAAQvC,SAAS6C,KAAKG,YAAcjD,OAAOkD,SACxDjD,SAAS2C,gBAAgBK,YAAc,IASvD3G,EAAFU,UAAA6E,OAAE,SAAOsB,GACL,WADJ,KAAAA,IAASA,EArF0B,IAsFxBA,EAAe,EAAI1G,KAAKiF,QAAQpD,KAAKC,EAAAA,UAAU4E,IAAiB1G,KAAKiF,SAItEpF,EAAVU,UAAA8E,+BACIrF,KAAKuF,eAAiBC,MAAOjC,OAAOoD,WAAYlB,OAAQlC,OAAOqD,6BArFnEnD,KAACC,EAAAA,iDAfDD,KAAQG,EAAAA,WADRH,KAAwCE,EAAAA,UARxC9D,KAyHagH,GAEX/C,QAASjE,EACTkE,OAAQ,GAAIC,GAAAA,SAAY,GAAIC,GAAAA,SAAYpE,GAAgB+D,EAAAA,SAAUD,EAAAA,QAClEO,WAAYvE,GErHdmH,EAAA,yBARA,sBAaArD,KAACsD,EAAAA,SAADjC,OACEkC,SAAUC,EAAAA,gBACVC,SAAU/C,GACVgD,cAAehD,GACfiD,WAAYvD,6CAjBdiD,2BHqBmC,sICHA"}