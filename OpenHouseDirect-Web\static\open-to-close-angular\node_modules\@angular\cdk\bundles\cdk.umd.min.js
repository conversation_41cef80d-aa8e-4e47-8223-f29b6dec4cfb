/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("@angular/core")):"function"==typeof define&&define.amd?define(["exports","@angular/core"],n):n((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{}),e.ng.core)}(this,function(e,n){"use strict";var o=new n.Version("5.2.1");e.VERSION=o,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk.umd.min.js.map
