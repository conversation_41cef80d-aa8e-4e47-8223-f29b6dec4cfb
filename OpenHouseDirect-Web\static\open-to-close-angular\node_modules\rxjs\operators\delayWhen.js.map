{"version": 3, "file": "delayWhen.js", "sourceRoot": "", "sources": ["../../src/operators/delayWhen.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAC3C,2BAA2B,eAAe,CAAC,CAAA;AAE3C,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAG9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,mBAA6B,qBAAoD,EACpD,iBAAmC;IAC9D,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACtB,MAAM,CAAC,UAAC,MAAqB;YAC3B,OAAA,IAAI,2BAA2B,CAAC,MAAM,EAAE,iBAAiB,CAAC;iBACvD,IAAI,CAAC,IAAI,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;QADrD,CACqD,CAAC;IAC1D,CAAC;IACD,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,EAAzD,CAAyD,CAAC;AAC9F,CAAC;AARe,iBAAS,YAQxB,CAAA;AAED;IACE,2BAAoB,qBAAoD;QAApD,0BAAqB,GAArB,qBAAqB,CAA+B;IACxE,CAAC;IAED,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;IAC3F,CAAC;IACH,wBAAC;AAAD,CAAC,AAPD,IAOC;AAED;;;;GAIG;AACH;IAAwC,uCAAqB;IAK3D,6BAAY,WAA0B,EAClB,qBAAoD;QACtE,kBAAM,WAAW,CAAC,CAAC;QADD,0BAAqB,GAArB,qBAAqB,CAA+B;QALhE,cAAS,GAAY,KAAK,CAAC;QAC3B,+BAA0B,GAAwB,EAAE,CAAC;QACrD,WAAM,GAAa,EAAE,CAAC;IAK9B,CAAC;IAED,wCAAU,GAAV,UAAW,UAAa,EAAE,UAAe,EAC9B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACxC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,yCAAW,GAAX,UAAY,KAAU,EAAE,QAA+B;QACrD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,4CAAc,GAAd,UAAe,QAA+B;QAC5C,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAChD,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACV,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,mCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC;YACH,IAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YACxD,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;gBAClB,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACtC,CAAC;QACH,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAES,uCAAS,GAAnB;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,gDAAkB,GAA1B,UAA2B,YAAmC;QAC5D,YAAY,CAAC,WAAW,EAAE,CAAC;QAE3B,IAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC9E,IAAI,KAAK,GAAM,IAAI,CAAC;QAEpB,EAAE,CAAC,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACrC,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,CAAC,KAAK,CAAC;IACf,CAAC;IAEO,sCAAQ,GAAhB,UAAiB,aAA8B,EAAE,KAAQ;QACvD,IAAM,oBAAoB,GAAG,qCAAiB,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;QAE3E,EAAE,CAAC,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAC/B,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAEO,yCAAW,GAAnB;QACE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,0BAA0B,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACnE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AA7ED,CAAwC,iCAAe,GA6EtD;AAED;;;;GAIG;AACH;IAA6C,+CAAa;IACxD,qCAAY,oCAAoC,CAAQ,MAAqB,EAAU,iBAAkC;QACvH,iBAAO,CAAC;QAD8C,WAAM,GAAN,MAAM,CAAe;QAAU,sBAAiB,GAAjB,iBAAiB,CAAiB;IAEzH,CAAC;IAED,oCAAoC,CAAC,gDAAU,GAAV,UAAW,UAAyB;QACvE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7F,CAAC;IACH,kCAAC;AAAD,CAAC,AARD,CAA6C,uBAAU,GAQtD;AAED;;;;GAIG;AACH;IAA6C,+CAAa;IAGxD,qCAAoB,MAAqB,EAAU,MAAqB;QACtE,iBAAO,CAAC;QADU,WAAM,GAAN,MAAM,CAAe;QAAU,WAAM,GAAN,MAAM,CAAe;QAFhE,qBAAgB,GAAY,KAAK,CAAC;IAI1C,CAAC;IAES,2CAAK,GAAf,UAAgB,MAAW;QACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAES,4CAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAES,+CAAS,GAAnB;QACE,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,uDAAiB,GAAzB;QACE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IACH,kCAAC;AAAD,CAAC,AA3BD,CAA6C,uBAAU,GA2BtD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { Subscription, TeardownLogic } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { MonoTypeOperatorFunction } from '../interfaces';\n\n/**\n * Delays the emission of items from the source Observable by a given time span\n * determined by the emissions of another Observable.\n *\n * <span class=\"informal\">It's like {@link delay}, but the time span of the\n * delay duration is determined by a second Observable.</span>\n *\n * <img src=\"./img/delayWhen.png\" width=\"100%\">\n *\n * `delayWhen` time shifts each emitted value from the source Observable by a\n * time span determined by another Observable. When the source emits a value,\n * the `delayDurationSelector` function is called with the source value as\n * argument, and should return an Observable, called the \"duration\" Observable.\n * The source value is emitted on the output Observable only when the duration\n * Observable emits a value or completes.\n *\n * Optionally, `delayWhen` takes a second argument, `subscriptionDelay`, which\n * is an Observable. When `subscriptionDelay` emits its first value or\n * completes, the source Observable is subscribed to and starts behaving like\n * described in the previous paragraph. If `subscriptionDelay` is not provided,\n * `delayWhen` will subscribe to the source Observable as soon as the output\n * Observable is subscribed.\n *\n * @example <caption>Delay each click by a random amount of time, between 0 and 5 seconds</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var delayedClicks = clicks.delayWhen(event =>\n *   Rx.Observable.interval(Math.random() * 5000)\n * );\n * delayedClicks.subscribe(x => console.log(x));\n *\n * @see {@link debounce}\n * @see {@link delay}\n *\n * @param {function(value: T): Observable} delayDurationSelector A function that\n * returns an Observable for each value emitted by the source Observable, which\n * is then used to delay the emission of that item on the output Observable\n * until the Observable returned from this function emits a value.\n * @param {Observable} subscriptionDelay An Observable that triggers the\n * subscription to the source Observable once it emits any value.\n * @return {Observable} An Observable that delays the emissions of the source\n * Observable by an amount of time specified by the Observable returned by\n * `delayDurationSelector`.\n * @method delayWhen\n * @owner Observable\n */\nexport function delayWhen<T>(delayDurationSelector: (value: T) => Observable<any>,\n                             subscriptionDelay?: Observable<any>): MonoTypeOperatorFunction<T> {\n  if (subscriptionDelay) {\n    return (source: Observable<T>) =>\n      new SubscriptionDelayObservable(source, subscriptionDelay)\n        .lift(new DelayWhenOperator(delayDurationSelector));\n  }\n  return (source: Observable<T>) => source.lift(new DelayWhenOperator(delayDurationSelector));\n}\n\nclass DelayWhenOperator<T> implements Operator<T, T> {\n  constructor(private delayDurationSelector: (value: T) => Observable<any>) {\n  }\n\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n    return source.subscribe(new DelayWhenSubscriber(subscriber, this.delayDurationSelector));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass DelayWhenSubscriber<T, R> extends OuterSubscriber<T, R> {\n  private completed: boolean = false;\n  private delayNotifierSubscriptions: Array<Subscription> = [];\n  private values: Array<T> = [];\n\n  constructor(destination: Subscriber<T>,\n              private delayDurationSelector: (value: T) => Observable<any>) {\n    super(destination);\n  }\n\n  notifyNext(outerValue: T, innerValue: any,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, R>): void {\n    this.destination.next(outerValue);\n    this.removeSubscription(innerSub);\n    this.tryComplete();\n  }\n\n  notifyError(error: any, innerSub: InnerSubscriber<T, R>): void {\n    this._error(error);\n  }\n\n  notifyComplete(innerSub: InnerSubscriber<T, R>): void {\n    const value = this.removeSubscription(innerSub);\n    if (value) {\n      this.destination.next(value);\n    }\n    this.tryComplete();\n  }\n\n  protected _next(value: T): void {\n    try {\n      const delayNotifier = this.delayDurationSelector(value);\n      if (delayNotifier) {\n        this.tryDelay(delayNotifier, value);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n  protected _complete(): void {\n    this.completed = true;\n    this.tryComplete();\n  }\n\n  private removeSubscription(subscription: InnerSubscriber<T, R>): T {\n    subscription.unsubscribe();\n\n    const subscriptionIdx = this.delayNotifierSubscriptions.indexOf(subscription);\n    let value: T = null;\n\n    if (subscriptionIdx !== -1) {\n      value = this.values[subscriptionIdx];\n      this.delayNotifierSubscriptions.splice(subscriptionIdx, 1);\n      this.values.splice(subscriptionIdx, 1);\n    }\n\n    return value;\n  }\n\n  private tryDelay(delayNotifier: Observable<any>, value: T): void {\n    const notifierSubscription = subscribeToResult(this, delayNotifier, value);\n\n    if (notifierSubscription && !notifierSubscription.closed) {\n      this.add(notifierSubscription);\n      this.delayNotifierSubscriptions.push(notifierSubscription);\n    }\n\n    this.values.push(value);\n  }\n\n  private tryComplete(): void {\n    if (this.completed && this.delayNotifierSubscriptions.length === 0) {\n      this.destination.complete();\n    }\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass SubscriptionDelayObservable<T> extends Observable<T> {\n  constructor(/** @deprecated internal use only */ public source: Observable<T>, private subscriptionDelay: Observable<any>) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>) {\n    this.subscriptionDelay.subscribe(new SubscriptionDelaySubscriber(subscriber, this.source));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass SubscriptionDelaySubscriber<T> extends Subscriber<T> {\n  private sourceSubscribed: boolean = false;\n\n  constructor(private parent: Subscriber<T>, private source: Observable<T>) {\n    super();\n  }\n\n  protected _next(unused: any) {\n    this.subscribeToSource();\n  }\n\n  protected _error(err: any) {\n    this.unsubscribe();\n    this.parent.error(err);\n  }\n\n  protected _complete() {\n    this.subscribeToSource();\n  }\n\n  private subscribeToSource(): void {\n    if (!this.sourceSubscribed) {\n      this.sourceSubscribed = true;\n      this.unsubscribe();\n      this.source.subscribe(this.parent);\n    }\n  }\n}\n"]}