{"version": 3, "file": "throttleTime.js", "sourceRoot": "", "sources": ["../../../src/add/operator/throttleTime.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,6BAA6B,6BAA6B,CAAC,CAAA;AAE3D,uBAAU,CAAC,SAAS,CAAC,YAAY,GAAG,2BAAY,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { throttleTime } from '../../operator/throttleTime';\n\nObservable.prototype.throttleTime = throttleTime;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    throttleTime: typeof throttleTime;\n  }\n}"]}