{"version": 3, "file": "cdk-keycodes.umd.js", "sources": ["../../src/cdk/keycodes/keycodes.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport const UP_ARROW = 38;\nexport const DOWN_ARROW = 40;\nexport const RIGHT_ARROW = 39;\nexport const LEFT_ARROW = 37;\nexport const PAGE_UP = 33;\nexport const PAGE_DOWN = 34;\nexport const HOME = 36;\nexport const END = 35;\nexport const ENTER = 13;\nexport const SPACE = 32;\nexport const TAB = 9;\nexport const ESCAPE = 27;\nexport const BACKSPACE = 8;\nexport const DELETE = 46;\nexport const A = 65;\nexport const Z = 90;\nexport const ZERO = 48;\nexport const NINE = 57;\nexport const COMMA = 188;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAQA,IAAa,QAAQ,GAAG,EAAE,CAAC;AAC3B,IAAa,UAAU,GAAG,EAAE,CAAC;AAC7B,IAAa,WAAW,GAAG,EAAE,CAAC;AAC9B,IAAa,UAAU,GAAG,EAAE,CAAC;AAC7B,IAAa,OAAO,GAAG,EAAE,CAAC;AAC1B,IAAa,SAAS,GAAG,EAAE,CAAC;AAC5B,IAAa,IAAI,GAAG,EAAE,CAAC;AACvB,IAAa,GAAG,GAAG,EAAE,CAAC;AACtB,IAAa,KAAK,GAAG,EAAE,CAAC;AACxB,IAAa,KAAK,GAAG,EAAE,CAAC;AACxB,IAAa,GAAG,GAAG,CAAC,CAAC;AACrB,IAAa,MAAM,GAAG,EAAE,CAAC;AACzB,IAAa,SAAS,GAAG,CAAC,CAAC;AAC3B,IAAa,MAAM,GAAG,EAAE,CAAC;AACzB,IAAa,CAAC,GAAG,EAAE,CAAC;AACpB,IAAa,CAAC,GAAG,EAAE,CAAC;AACpB,IAAa,IAAI,GAAG,EAAE,CAAC;AACvB,IAAa,IAAI,GAAG,EAAE,CAAC;AACvB,IAAa,KAAK,GAAG,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;"}