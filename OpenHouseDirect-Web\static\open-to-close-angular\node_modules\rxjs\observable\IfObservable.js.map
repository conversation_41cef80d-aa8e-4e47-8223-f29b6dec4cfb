{"version": 3, "file": "IfObservable.js", "sourceRoot": "", "sources": ["../../src/observable/IfObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAAkD,eAAe,CAAC,CAAA;AAIlE,kCAAkC,2BAA2B,CAAC,CAAA;AAC9D,gCAAgC,oBAAoB,CAAC,CAAA;AACrD;;;;GAIG;AACH;IAAwC,gCAAa;IAQnD,sBAAoB,SAA+B,EAC/B,UAA4C,EAC5C,UAA4C;QAC9D,iBAAO,CAAC;QAHU,cAAS,GAAT,SAAS,CAAsB;QAC/B,eAAU,GAAV,UAAU,CAAkC;QAC5C,eAAU,GAAV,UAAU,CAAkC;IAEhE,CAAC;IAVM,mBAAM,GAAb,UAAoB,SAA+B,EAC/B,UAA4C,EAC5C,UAA4C;QAC9D,MAAM,CAAC,IAAI,YAAY,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IAQD,oCAAoC,CAAC,iCAAU,GAAV,UAAW,UAA2B;QACzE,IAAA,SAAkD,EAA1C,wBAAS,EAAE,0BAAU,EAAE,0BAAU,CAAU;QAEnD,MAAM,CAAC,IAAI,YAAY,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACzE,CAAC;IACH,mBAAC;AAAD,CAAC,AAnBD,CAAwC,uBAAU,GAmBjD;AAnBY,oBAAY,eAmBxB,CAAA;AAED;IAAiC,gCAAqB;IACpD,sBAAY,WAA0B,EAClB,SAA+B,EAC/B,UAA4C,EAC5C,UAA4C;QAC9D,kBAAM,WAAW,CAAC,CAAC;QAHD,cAAS,GAAT,SAAS,CAAsB;QAC/B,eAAU,GAAV,UAAU,CAAkC;QAC5C,eAAU,GAAV,UAAU,CAAkC;QAE9D,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEO,4BAAK,GAAb;QACE,IAAA,SAAkD,EAA1C,wBAAS,EAAE,0BAAU,EAAE,0BAAU,CAAU;QAEnD,IAAI,MAAe,CAAC;QACpB,IAAI,CAAC;YACH,MAAM,GAAY,SAAS,EAAE,CAAC;YAC9B,IAAM,MAAM,GAAG,MAAM,GAAG,UAAU,GAAG,UAAU,CAAC;YAEhD,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACX,IAAI,CAAC,GAAG,CAAC,qCAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;YAC5C,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;QACH,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IACH,mBAAC;AAAD,CAAC,AA1BD,CAAiC,iCAAe,GA0B/C", "sourcesContent": ["import { Observable, SubscribableOrPromise } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { TeardownLogic } from '../Subscription';\n\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class IfObservable<T, R> extends Observable<T> {\n\n  static create<T, R>(condition: () => boolean | void,\n                      thenSource?: SubscribableOrPromise<T> | void,\n                      elseSource?: SubscribableOrPromise<R> | void): Observable<T|R> {\n    return new IfObservable(condition, thenSource, elseSource);\n  }\n\n  constructor(private condition: () => boolean | void,\n              private thenSource?: SubscribableOrPromise<T> | void,\n              private elseSource?: SubscribableOrPromise<R> | void) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T|R>): TeardownLogic {\n    const { condition, thenSource, elseSource } = this;\n\n    return new IfSubscriber(subscriber, condition, thenSource, elseSource);\n  }\n}\n\nclass IfSubscriber<T, R> extends OuterSubscriber<T, T> {\n  constructor(destination: Subscriber<T>,\n              private condition: () => boolean | void,\n              private thenSource?: SubscribableOrPromise<T> | void,\n              private elseSource?: SubscribableOrPromise<R> | void) {\n    super(destination);\n    this.tryIf();\n  }\n\n  private tryIf(): void {\n    const { condition, thenSource, elseSource } = this;\n\n    let result: boolean;\n    try {\n      result = <boolean>condition();\n      const source = result ? thenSource : elseSource;\n\n      if (source) {\n        this.add(subscribeToResult(this, source));\n      } else {\n        this._complete();\n      }\n    } catch (err) {\n      this._error(err);\n    }\n  }\n}\n"]}