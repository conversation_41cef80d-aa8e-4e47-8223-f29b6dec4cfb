{"version": 3, "file": "a11y.js", "sources": ["../../../src/cdk/a11y/index.ts", "../../../src/cdk/a11y/public-api.ts", "../../../src/cdk/a11y/a11y-module.ts", "../../../src/cdk/a11y/fake-mousedown.ts", "../../../src/cdk/a11y/focus-monitor/focus-monitor.ts", "../../../src/cdk/a11y/live-announcer/live-announcer.ts", "../../../src/cdk/a11y/key-manager/focus-key-manager.ts", "../../../src/cdk/a11y/key-manager/activedescendant-key-manager.ts", "../../../src/cdk/a11y/key-manager/list-key-manager.ts", "../../../src/cdk/a11y/aria-describer/aria-describer.ts", "../../../src/cdk/a11y/aria-describer/aria-reference.ts", "../../../src/cdk/a11y/focus-trap/focus-trap.ts", "../../../src/cdk/a11y/interactivity-checker/interactivity-checker.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CdkTrapFocus} from './focus-trap/focus-trap';\n\n\nexport * from './aria-describer/aria-describer';\nexport * from './key-manager/activedescendant-key-manager';\nexport * from './key-manager/focus-key-manager';\nexport * from './key-manager/list-key-manager';\nexport * from './focus-trap/focus-trap';\nexport * from './interactivity-checker/interactivity-checker';\nexport * from './live-announcer/live-announcer';\nexport * from './focus-monitor/focus-monitor';\nexport * from './fake-mousedown';\nexport * from './a11y-module';\n\n/**\n * @deprecated Renamed to CdkTrapFocus.\n * @deletion-target 6.0.0\n */\nexport {CdkTrapFocus as FocusTrapDirective};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PlatformModule} from '@angular/cdk/platform';\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {ARIA_DESCRIBER_PROVIDER, AriaDescriber} from './aria-describer/aria-describer';\nimport {CdkMonitorFocus, FOCUS_MONITOR_PROVIDER} from './focus-monitor/focus-monitor';\nimport {\n  CdkTrapFocus,\n  FocusTrapDeprecatedDirective,\n  FocusTrapFactory,\n} from './focus-trap/focus-trap';\nimport {Interact<PERSON><PERSON>he<PERSON>} from './interactivity-checker/interactivity-checker';\nimport {LIVE_ANNOUNCER_PROVIDER} from './live-announcer/live-announcer';\n\n@NgModule({\n  imports: [CommonModule, PlatformModule],\n  declarations: [CdkTrapFocus, FocusTrapDeprecatedDirective, CdkMonitorFocus],\n  exports: [CdkTrapFocus, FocusTrapDeprecatedDirective, CdkMonitorFocus],\n  providers: [\n    InteractivityChecker,\n    FocusTrapFactory,\n    AriaDescriber,\n    LIVE_ANNOUNCER_PROVIDER,\n    ARIA_DESCRIBER_PROVIDER,\n    FOCUS_MONITOR_PROVIDER,\n  ]\n})\nexport class A11yModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Screenreaders will often fire fake mousedown events when a focusable element\n * is activated using the keyboard. We can typically distinguish between these faked\n * mousedown events and real mousedown events using the \"buttons\" property. While\n * real mousedowns will indicate the mouse button that was pressed (e.g. \"1\" for\n * the left mouse button), faked mousedowns will usually set the property value to 0.\n */\nexport function isFakeMousedownFromScreenReader(event: MouseEvent): boolean {\n  return event.buttons === 0;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Platform, supportsPassiveEventListeners} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injectable,\n  NgZone,\n  OnDestroy,\n  Optional,\n  Output,\n  Renderer2,\n  SkipSelf,\n} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {of as observableOf} from 'rxjs/observable/of';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\n\n\n// This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n// that a value of around 650ms seems appropriate.\nexport const TOUCH_BUFFER_MS = 650;\n\n\nexport type FocusOrigin = 'touch' | 'mouse' | 'keyboard' | 'program' | null;\n\n\ntype MonitoredElementInfo = {\n  unlisten: Function,\n  checkChildren: boolean,\n  subject: Subject<FocusOrigin>\n};\n\n\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\n@Injectable()\nexport class FocusMonitor implements OnDestroy {\n  /** The focus origin that the next focus event is a result of. */\n  private _origin: FocusOrigin = null;\n\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  private _lastFocusOrigin: FocusOrigin;\n\n  /** Whether the window has just been focused. */\n  private _windowFocused = false;\n\n  /** The target of the last touch event. */\n  private _lastTouchTarget: EventTarget | null;\n\n  /** The timeout id of the touch timeout, used to cancel timeout later. */\n  private _touchTimeoutId: number;\n\n  /** The timeout id of the window focus timeout. */\n  private _windowFocusTimeoutId: number;\n\n  /** The timeout id of the origin clearing timeout. */\n  private _originTimeoutId: number;\n\n  /** Map of elements being monitored to their info. */\n  private _elementInfo = new Map<HTMLElement, MonitoredElementInfo>();\n\n  /** A map of global objects to lists of current listeners. */\n  private _unregisterGlobalListeners = () => {};\n\n  /** The number of elements currently being monitored. */\n  private _monitoredElementCount = 0;\n\n  constructor(private _ngZone: NgZone, private _platform: Platform) {}\n\n  /**\n   * @docs-private\n   * @deprecated renderer param no longer needed.\n   * @deletion-target 6.0.0\n   */\n  monitor(element: HTMLElement, renderer: Renderer2, checkChildren: boolean):\n      Observable<FocusOrigin>;\n  /**\n   * Monitors focus on an element and applies appropriate CSS classes.\n   * @param element The element to monitor\n   * @param checkChildren Whether to count the element as focused when its children are focused.\n   * @returns An observable that emits when the focus state of the element changes.\n   *     When the element is blurred, null will be emitted.\n   */\n  monitor(element: HTMLElement, checkChildren?: boolean): Observable<FocusOrigin>;\n  monitor(\n      element: HTMLElement,\n      renderer?: Renderer2 | boolean,\n      checkChildren?: boolean): Observable<FocusOrigin> {\n    // TODO(mmalerba): clean up after deprecated signature is removed.\n    if (!(renderer instanceof Renderer2)) {\n      checkChildren = renderer;\n    }\n    checkChildren = !!checkChildren;\n\n    // Do nothing if we're not on the browser platform.\n    if (!this._platform.isBrowser) {\n      return observableOf(null);\n    }\n    // Check if we're already monitoring this element.\n    if (this._elementInfo.has(element)) {\n      let cachedInfo = this._elementInfo.get(element);\n      cachedInfo!.checkChildren = checkChildren;\n      return cachedInfo!.subject.asObservable();\n    }\n\n    // Create monitored element info.\n    let info: MonitoredElementInfo = {\n      unlisten: () => {},\n      checkChildren: checkChildren,\n      subject: new Subject<FocusOrigin>()\n    };\n    this._elementInfo.set(element, info);\n    this._incrementMonitoredElementCount();\n\n    // Start listening. We need to listen in capture phase since focus events don't bubble.\n    let focusListener = (event: FocusEvent) => this._onFocus(event, element);\n    let blurListener = (event: FocusEvent) => this._onBlur(event, element);\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('focus', focusListener, true);\n      element.addEventListener('blur', blurListener, true);\n    });\n\n    // Create an unlisten function for later.\n    info.unlisten = () => {\n      element.removeEventListener('focus', focusListener, true);\n      element.removeEventListener('blur', blurListener, true);\n    };\n\n    return info.subject.asObservable();\n  }\n\n  /**\n   * Stops monitoring an element and removes all focus classes.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: HTMLElement): void {\n    const elementInfo = this._elementInfo.get(element);\n\n    if (elementInfo) {\n      elementInfo.unlisten();\n      elementInfo.subject.complete();\n\n      this._setClasses(element);\n      this._elementInfo.delete(element);\n      this._decrementMonitoredElementCount();\n    }\n  }\n\n  /**\n   * Focuses the element via the specified focus origin.\n   * @param element The element to focus.\n   * @param origin The focus origin.\n   */\n  focusVia(element: HTMLElement, origin: FocusOrigin): void {\n    this._setOriginForCurrentEventQueue(origin);\n    element.focus();\n  }\n\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n\n  /** Register necessary event listeners on the document and window. */\n  private _registerGlobalListeners() {\n    // Do nothing if we're not on the browser platform.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    // On keydown record the origin and clear any touch event that may be in progress.\n    let documentKeydownListener = () => {\n      this._lastTouchTarget = null;\n      this._setOriginForCurrentEventQueue('keyboard');\n    };\n\n    // On mousedown record the origin only if there is not touch target, since a mousedown can\n    // happen as a result of a touch event.\n    let documentMousedownListener = () => {\n      if (!this._lastTouchTarget) {\n        this._setOriginForCurrentEventQueue('mouse');\n      }\n    };\n\n    // When the touchstart event fires the focus event is not yet in the event queue. This means\n    // we can't rely on the trick used above (setting timeout of 0ms). Instead we wait 650ms to\n    // see if a focus happens.\n    let documentTouchstartListener = (event: TouchEvent) => {\n      if (this._touchTimeoutId != null) {\n        clearTimeout(this._touchTimeoutId);\n      }\n      this._lastTouchTarget = event.target;\n      this._touchTimeoutId = setTimeout(() => this._lastTouchTarget = null, TOUCH_BUFFER_MS);\n    };\n\n    // Make a note of when the window regains focus, so we can restore the origin info for the\n    // focused element.\n    let windowFocusListener = () => {\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false, 0);\n    };\n\n    // Note: we listen to events in the capture phase so we can detect them even if the user stops\n    // propagation.\n    this._ngZone.runOutsideAngular(() => {\n      document.addEventListener('keydown', documentKeydownListener, true);\n      document.addEventListener('mousedown', documentMousedownListener, true);\n      document.addEventListener('touchstart', documentTouchstartListener,\n          supportsPassiveEventListeners() ? ({passive: true, capture: true} as any) : true);\n      window.addEventListener('focus', windowFocusListener);\n    });\n\n    this._unregisterGlobalListeners = () => {\n      document.removeEventListener('keydown', documentKeydownListener, true);\n      document.removeEventListener('mousedown', documentMousedownListener, true);\n      document.removeEventListener('touchstart', documentTouchstartListener,\n          supportsPassiveEventListeners() ? ({passive: true, capture: true} as any) : true);\n      window.removeEventListener('focus', windowFocusListener);\n\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._touchTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    };\n  }\n\n  private _toggleClass(element: Element, className: string, shouldSet: boolean) {\n    if (shouldSet) {\n      element.classList.add(className);\n    } else {\n      element.classList.remove(className);\n    }\n  }\n\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  private _setClasses(element: HTMLElement, origin?: FocusOrigin): void {\n    const elementInfo = this._elementInfo.get(element);\n\n    if (elementInfo) {\n      this._toggleClass(element, 'cdk-focused', !!origin);\n      this._toggleClass(element, 'cdk-touch-focused', origin === 'touch');\n      this._toggleClass(element, 'cdk-keyboard-focused', origin === 'keyboard');\n      this._toggleClass(element, 'cdk-mouse-focused', origin === 'mouse');\n      this._toggleClass(element, 'cdk-program-focused', origin === 'program');\n    }\n  }\n\n  /**\n   * Sets the origin and schedules an async function to clear it at the end of the event queue.\n   * @param origin The origin to set.\n   */\n  private _setOriginForCurrentEventQueue(origin: FocusOrigin): void {\n    this._origin = origin;\n    this._originTimeoutId = setTimeout(() => this._origin = null, 0);\n  }\n\n  /**\n   * Checks whether the given focus event was caused by a touchstart event.\n   * @param event The focus event to check.\n   * @returns Whether the event was caused by a touch.\n   */\n  private _wasCausedByTouch(event: FocusEvent): boolean {\n    // Note(mmalerba): This implementation is not quite perfect, there is a small edge case.\n    // Consider the following dom structure:\n    //\n    // <div #parent tabindex=\"0\" cdkFocusClasses>\n    //   <div #child (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // If the user touches the #child element and the #parent is programmatically focused as a\n    // result, this code will still consider it to have been caused by the touch event and will\n    // apply the cdk-touch-focused class rather than the cdk-program-focused class. This is a\n    // relatively small edge-case that can be worked around by using\n    // focusVia(parentEl, 'program') to focus the parent element.\n    //\n    // If we decide that we absolutely must handle this case correctly, we can do so by listening\n    // for the first focus event after the touchstart, and then the first blur event after that\n    // focus event. When that blur event fires we know that whatever follows is not a result of the\n    // touchstart.\n    let focusTarget = event.target;\n    return this._lastTouchTarget instanceof Node && focusTarget instanceof Node &&\n        (focusTarget === this._lastTouchTarget || focusTarget.contains(this._lastTouchTarget));\n  }\n\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  private _onFocus(event: FocusEvent, element: HTMLElement) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || (!elementInfo.checkChildren && element !== event.target)) {\n      return;\n    }\n\n    // If we couldn't detect a cause for the focus event, it's due to one of three reasons:\n    // 1) The window has just regained focus, in which case we want to restore the focused state of\n    //    the element from before the window blurred.\n    // 2) It was caused by a touch event, in which case we mark the origin as 'touch'.\n    // 3) The element was programmatically focused, in which case we should mark the origin as\n    //    'program'.\n    if (!this._origin) {\n      if (this._windowFocused && this._lastFocusOrigin) {\n        this._origin = this._lastFocusOrigin;\n      } else if (this._wasCausedByTouch(event)) {\n        this._origin = 'touch';\n      } else {\n        this._origin = 'program';\n      }\n    }\n\n    this._setClasses(element, this._origin);\n    elementInfo.subject.next(this._origin);\n    this._lastFocusOrigin = this._origin;\n    this._origin = null;\n  }\n\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event: FocusEvent, element: HTMLElement) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n\n    if (!elementInfo || (elementInfo.checkChildren && event.relatedTarget instanceof Node &&\n        element.contains(event.relatedTarget))) {\n      return;\n    }\n\n    this._setClasses(element);\n    elementInfo.subject.next(null);\n  }\n\n  private _incrementMonitoredElementCount() {\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount == 1) {\n      this._registerGlobalListeners();\n    }\n  }\n\n  private _decrementMonitoredElementCount() {\n    // Unregister global listeners when last element is unmonitored.\n    if (!--this._monitoredElementCount) {\n      this._unregisterGlobalListeners();\n      this._unregisterGlobalListeners = () => {};\n    }\n  }\n\n}\n\n\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\n@Directive({\n  selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n})\nexport class CdkMonitorFocus implements OnDestroy {\n  private _monitorSubscription: Subscription;\n  @Output() cdkFocusChange = new EventEmitter<FocusOrigin>();\n\n  constructor(private _elementRef: ElementRef, private _focusMonitor: FocusMonitor) {\n    this._monitorSubscription = this._focusMonitor.monitor(\n        this._elementRef.nativeElement,\n        this._elementRef.nativeElement.hasAttribute('cdkMonitorSubtreeFocus'))\n        .subscribe(origin => this.cdkFocusChange.emit(origin));\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef.nativeElement);\n    this._monitorSubscription.unsubscribe();\n  }\n}\n\n/** @docs-private */\nexport function FOCUS_MONITOR_PROVIDER_FACTORY(\n    parentDispatcher: FocusMonitor, ngZone: NgZone, platform: Platform) {\n  return parentDispatcher || new FocusMonitor(ngZone, platform);\n}\n\n/** @docs-private */\nexport const FOCUS_MONITOR_PROVIDER = {\n  // If there is already a FocusMonitor available, use that. Otherwise, provide a new one.\n  provide: FocusMonitor,\n  deps: [[new Optional(), new SkipSelf(), FocusMonitor], NgZone, Platform],\n  useFactory: FOCUS_MONITOR_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Injectable,\n  InjectionToken,\n  Optional,\n  Inject,\n  SkipSelf,\n  OnDestroy,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\n\n\nexport const LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken<HTMLElement>('liveAnnouncerElement');\n\n/** Possible politeness levels. */\nexport type AriaLivePoliteness = 'off' | 'polite' | 'assertive';\n\n@Injectable()\nexport class LiveAnnouncer implements OnD<PERSON>roy {\n  private _liveElement: Element;\n\n  constructor(\n      @Optional() @Inject(LIVE_ANNOUNCER_ELEMENT_TOKEN) elementToken: any,\n      @Inject(DOCUMENT) private _document: any) {\n\n    // We inject the live element as `any` because the constructor signature cannot reference\n    // browser globals (HTMLElement) on non-browser environments, since having a class decorator\n    // causes TypeScript to preserve the constructor signature types.\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n\n  /**\n   * Announces a message to screenreaders.\n   * @param message Message to be announced to the screenreader\n   * @param politeness The politeness of the announcer element\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, politeness: AriaLivePoliteness = 'polite'): Promise<void> {\n    this._liveElement.textContent = '';\n\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return new Promise(resolve => {\n      setTimeout(() => {\n        this._liveElement.textContent = message;\n        resolve();\n      }, 100);\n    });\n  }\n\n  ngOnDestroy() {\n    if (this._liveElement && this._liveElement.parentNode) {\n      this._liveElement.parentNode.removeChild(this._liveElement);\n    }\n  }\n\n  private _createLiveElement(): Element {\n    let liveEl = this._document.createElement('div');\n\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n\n    this._document.body.appendChild(liveEl);\n\n    return liveEl;\n  }\n\n}\n\n/** @docs-private */\nexport function LIVE_ANNOUNCER_PROVIDER_FACTORY(\n    parentDispatcher: LiveAnnouncer, liveElement: any, _document: any) {\n  return parentDispatcher || new LiveAnnouncer(liveElement, _document);\n}\n\n/** @docs-private */\nexport const LIVE_ANNOUNCER_PROVIDER = {\n  // If there is already a LiveAnnouncer available, use that. Otherwise, provide a new one.\n  provide: LiveAnnouncer,\n  deps: [\n    [new Optional(), new SkipSelf(), LiveAnnouncer],\n    [new Optional(), new Inject(LIVE_ANNOUNCER_ELEMENT_TOKEN)],\n    DOCUMENT,\n  ],\n  useFactory: LIVE_ANNOUNCER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\nimport {FocusOrigin} from '../focus-monitor/focus-monitor';\n\n/**\n * This is the interface for focusable items (used by the FocusKeyManager).\n * Each item must know how to focus itself, whether or not it is currently disabled\n * and be able to supply it's label.\n */\nexport interface FocusableOption extends ListKeyManagerOption {\n  /** Focuses the `FocusableOption`. */\n  focus(origin?: FocusOrigin): void;\n}\n\nexport class FocusKeyManager<T> extends ListKeyManager<FocusableOption & T> {\n  private _origin: FocusOrigin = 'program';\n\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin: FocusOrigin): this {\n    this._origin = origin;\n    return this;\n  }\n\n  /**\n   * This method sets the active item to the item at the specified index.\n   * It also adds focuses the newly active item.\n   */\n  setActiveItem(index: number): void {\n    super.setActiveItem(index);\n\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\n\n/**\n * This is the interface for highlightable items (used by the ActiveDescendantKeyManager).\n * Each item must know how to style itself as active or inactive and whether or not it is\n * currently disabled.\n */\nexport interface Highlightable extends ListKeyManagerOption {\n  /** Applies the styles for an active item to this item. */\n  setActiveStyles(): void;\n\n  /** Applies the styles for an inactive item to this item. */\n  setInactiveStyles(): void;\n}\n\nexport class ActiveDescendantKeyManager<T> extends ListKeyManager<Highlightable & T> {\n\n  /**\n   * This method sets the active item to the item at the specified index.\n   * It also adds active styles to the newly active item and removes active\n   * styles from the previously active item.\n   */\n  setActiveItem(index: number): void {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {QueryList} from '@angular/core';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {\n  UP_ARROW,\n  DOWN_ARROW,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  TAB,\n  A,\n  Z,\n  ZERO,\n  NINE,\n} from '@angular/cdk/keycodes';\nimport {debounceTime} from 'rxjs/operators/debounceTime';\nimport {filter} from 'rxjs/operators/filter';\nimport {map} from 'rxjs/operators/map';\nimport {tap} from 'rxjs/operators/tap';\n\n/** This interface is for items that can be passed to a ListKeyManager. */\nexport interface ListKeyManagerOption {\n  /** Whether the option is disabled. */\n  disabled?: boolean;\n\n  /** Gets the label for this option. */\n  getLabel?(): string;\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nexport class ListKeyManager<T extends ListKeyManagerOption> {\n  private _activeItemIndex = -1;\n  private _activeItem: T;\n  private _wrap = false;\n  private _letterKeyStream = new Subject<string>();\n  private _typeaheadSubscription = Subscription.EMPTY;\n  private _vertical = true;\n  private _horizontal: 'ltr' | 'rtl' | null;\n\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  private _skipPredicateFn = (item: T) => item.disabled;\n\n  // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n  private _pressedLetters: string[] = [];\n\n  constructor(private _items: QueryList<T>) {\n    _items.changes.subscribe((newItems: QueryList<T>) => {\n      if (this._activeItem) {\n        const itemArray = newItems.toArray();\n        const newIndex = itemArray.indexOf(this._activeItem);\n\n        if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n          this._activeItemIndex = newIndex;\n        }\n      }\n    });\n  }\n\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  tabOut: Subject<void> = new Subject<void>();\n\n  /** Stream that emits whenever the active item of the list manager changes. */\n  change = new Subject<number>();\n\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate: (item: T) => boolean): this {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n\n  /**\n   * Turns on wrapping mode, which ensures that the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   */\n  withWrap(): this {\n    this._wrap = true;\n    return this;\n  }\n\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled: boolean = true): this {\n    this._vertical = enabled;\n    return this;\n  }\n\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction: 'ltr' | 'rtl' | null): this {\n    this._horizontal = direction;\n    return this;\n  }\n\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval: number = 200): this {\n    if (this._items.length && this._items.some(item => typeof item.getLabel !== 'function')) {\n      throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n\n    this._typeaheadSubscription.unsubscribe();\n\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._typeaheadSubscription = this._letterKeyStream.pipe(\n      tap(keyCode => this._pressedLetters.push(keyCode)),\n      debounceTime(debounceInterval),\n      filter(() => this._pressedLetters.length > 0),\n      map(() => this._pressedLetters.join(''))\n    ).subscribe(inputString => {\n      const items = this._items.toArray();\n\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < items.length + 1; i++) {\n        const index = (this._activeItemIndex + i) % items.length;\n        const item = items[index];\n\n        if (!this._skipPredicateFn(item) &&\n            item.getLabel!().toUpperCase().trim().indexOf(inputString) === 0) {\n\n          this.setActiveItem(index);\n          break;\n        }\n      }\n\n      this._pressedLetters = [];\n    });\n\n    return this;\n  }\n\n  /**\n   * Sets the active item to the item at the index specified.\n   * @param index The index of the item to be set as active.\n   */\n  setActiveItem(index: number): void {\n    const previousIndex = this._activeItemIndex;\n\n    this._activeItemIndex = index;\n    this._activeItem = this._items.toArray()[index];\n\n    if (this._activeItemIndex !== previousIndex) {\n      this.change.next(index);\n    }\n  }\n\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n\n      case DOWN_ARROW:\n        if (this._vertical) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case UP_ARROW:\n        if (this._vertical) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case RIGHT_ARROW:\n        if (this._horizontal === 'ltr') {\n          this.setNextItemActive();\n          break;\n        } else if (this._horizontal === 'rtl') {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case LEFT_ARROW:\n        if (this._horizontal === 'ltr') {\n          this.setPreviousItemActive();\n          break;\n        } else if (this._horizontal === 'rtl') {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      default:\n        // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n        // otherwise fall back to resolving alphanumeric characters via the keyCode.\n        if (event.key && event.key.length === 1) {\n          this._letterKeyStream.next(event.key.toLocaleUpperCase());\n        } else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n          this._letterKeyStream.next(String.fromCharCode(keyCode));\n        }\n\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n\n    this._pressedLetters = [];\n    event.preventDefault();\n  }\n\n  /** Index of the currently active item. */\n  get activeItemIndex(): number | null {\n    return this._activeItemIndex;\n  }\n\n  /** The active item. */\n  get activeItem(): T | null {\n    return this._activeItem;\n  }\n\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive(): void {\n    this._setActiveItemByIndex(0, 1);\n  }\n\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive(): void {\n    this._setActiveItemByIndex(this._items.length - 1, -1);\n  }\n\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive(): void {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive(): void {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive()\n                                            : this._setActiveItemByDelta(-1);\n  }\n\n  /**\n   * Allows setting of the activeItemIndex without any other effects.\n   * @param index The new activeItemIndex.\n   */\n  updateActiveItemIndex(index: number) {\n    this._activeItemIndex = index;\n  }\n\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  private _setActiveItemByDelta(delta: -1 | 1, items = this._items.toArray()): void {\n    this._wrap ? this._setActiveInWrapMode(delta, items)\n               : this._setActiveInDefaultMode(delta, items);\n  }\n\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  private _setActiveInWrapMode(delta: -1 | 1, items: T[]): void {\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + (delta * i) + items.length) % items.length;\n      const item = items[index];\n\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  private _setActiveInDefaultMode(delta: -1 | 1, items: T[]): void {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta, items);\n  }\n\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  private _setActiveItemByIndex(index: number, fallbackDelta: -1 | 1,\n                                items = this._items.toArray()): void {\n    if (!items[index]) {\n      return;\n    }\n\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n\n      if (!items[index]) {\n        return;\n      }\n    }\n\n    this.setActiveItem(index);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Inject, InjectionToken, Optional, SkipSelf} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId} from './aria-reference';\n\n/**\n * Interface used to register message elements and keep a count of how many registrations have\n * the same message and the reference to the message element used for the `aria-describedby`.\n */\nexport interface RegisteredMessage {\n  /** The element containing the message. */\n  messageElement: Element;\n\n  /** The number of elements that reference this message element via `aria-describedby`. */\n  referenceCount: number;\n}\n\n/** ID used for the body container where all messages are appended. */\nexport const MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n\n/** ID prefix used for each created message element. */\nexport const CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n\n/** Attribute given to each host element that is described by a message element. */\nexport const CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n\n/** Global map of all registered message elements that have been placed into the document. */\nconst messageRegistry = new Map<string, RegisteredMessage>();\n\n/** Container for all registered messages. */\nlet messagesContainer: HTMLElement | null = null;\n\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n * @docs-private\n */\n@Injectable()\nexport class AriaDescriber {\n  private _document: Document;\n\n  constructor(@Inject(DOCUMENT) _document: any) {\n    this._document = _document;\n  }\n\n  /**\n   * Adds to the host element an aria-describedby reference to a hidden element that contains\n   * the message. If the same message has already been registered, then it will reuse the created\n   * message element.\n   */\n  describe(hostElement: Element, message: string) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    if (!messageRegistry.has(message)) {\n      this._createMessageElement(message);\n    }\n\n    if (!this._isElementDescribedByMessage(hostElement, message)) {\n      this._addMessageReference(hostElement, message);\n    }\n  }\n\n  /** Removes the host element's aria-describedby reference to the message element. */\n  removeDescription(hostElement: Element, message: string) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    if (this._isElementDescribedByMessage(hostElement, message)) {\n      this._removeMessageReference(hostElement, message);\n    }\n\n    const registeredMessage = messageRegistry.get(message);\n    if (registeredMessage && registeredMessage.referenceCount === 0) {\n      this._deleteMessageElement(message);\n    }\n\n    if (messagesContainer && messagesContainer.childNodes.length === 0) {\n      this._deleteMessagesContainer();\n    }\n  }\n\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements =\n        this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}]`);\n\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n\n    if (messagesContainer) {\n      this._deleteMessagesContainer();\n    }\n\n    messageRegistry.clear();\n  }\n\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  private _createMessageElement(message: string) {\n    const messageElement = this._document.createElement('div');\n    messageElement.setAttribute('id', `${CDK_DESCRIBEDBY_ID_PREFIX}-${nextId++}`);\n    messageElement.appendChild(this._document.createTextNode(message)!);\n\n    if (!messagesContainer) { this._createMessagesContainer(); }\n    messagesContainer!.appendChild(messageElement);\n\n    messageRegistry.set(message, {messageElement, referenceCount: 0});\n  }\n\n  /** Deletes the message element from the global messages container. */\n  private _deleteMessageElement(message: string) {\n    const registeredMessage = messageRegistry.get(message);\n    const messageElement = registeredMessage && registeredMessage.messageElement;\n    if (messagesContainer && messageElement) {\n      messagesContainer.removeChild(messageElement);\n    }\n    messageRegistry.delete(message);\n  }\n\n  /** Creates the global container for all aria-describedby messages. */\n  private _createMessagesContainer() {\n    messagesContainer = this._document.createElement('div');\n    messagesContainer.setAttribute('id', MESSAGES_CONTAINER_ID);\n    messagesContainer.setAttribute('aria-hidden', 'true');\n    messagesContainer.style.display = 'none';\n    this._document.body.appendChild(messagesContainer);\n  }\n\n  /** Deletes the global messages container. */\n  private _deleteMessagesContainer() {\n    if (messagesContainer && messagesContainer.parentNode) {\n      messagesContainer.parentNode.removeChild(messagesContainer);\n      messagesContainer = null;\n    }\n  }\n\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  private _removeCdkDescribedByReferenceIds(element: Element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby')\n        .filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  private _addMessageReference(element: Element, message: string) {\n    const registeredMessage = messageRegistry.get(message)!;\n\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, '');\n\n    registeredMessage.referenceCount++;\n  }\n\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  private _removeMessageReference(element: Element, message: string) {\n    const registeredMessage = messageRegistry.get(message)!;\n    registeredMessage.referenceCount--;\n\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n\n  /** Returns true if the element has been described by the provided message ID. */\n  private _isElementDescribedByMessage(element: Element, message: string): boolean {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = messageRegistry.get(message);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n\n  /** Determines whether a message can be described on a particular element. */\n  private _canBeDescribed(element: Element, message: string): boolean {\n    return element.nodeType === this._document.ELEMENT_NODE && message != null &&\n        !!`${message}`.trim();\n  }\n\n}\n\n/** @docs-private */\nexport function ARIA_DESCRIBER_PROVIDER_FACTORY(parentDispatcher: AriaDescriber, _document: any) {\n  return parentDispatcher || new AriaDescriber(_document);\n}\n\n/** @docs-private */\nexport const ARIA_DESCRIBER_PROVIDER = {\n  // If there is already an AriaDescriber available, use that. Otherwise, provide a new one.\n  provide: AriaDescriber,\n  deps: [\n    [new Optional(), new SkipSelf(), AriaDescriber],\n    DOCUMENT as InjectionToken<any>\n  ],\n  useFactory: ARIA_DESCRIBER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** IDs are deliminated by an empty space, as per the spec. */\nconst ID_DELIMINATOR = ' ';\n\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function addAriaReferencedId(el: Element, attr: string, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  if (ids.some(existingId => existingId.trim() == id.trim())) { return; }\n  ids.push(id.trim());\n\n  el.setAttribute(attr, ids.join(ID_DELIMINATOR));\n}\n\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function removeAriaReferencedId(el: Element, attr: string, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  const filteredIds = ids.filter(val => val != id.trim());\n\n  el.setAttribute(attr, filteredIds.join(ID_DELIMINATOR));\n}\n\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function getAriaReferenceIds(el: Element, attr: string): string[] {\n  // Get string array of all individual ids (whitespace deliminated) in the attribute value\n  return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  Input,\n  NgZone,\n  OnDestroy,\n  AfterContentInit,\n  Injectable,\n  Inject,\n} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {take} from 'rxjs/operators/take';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {DOCUMENT} from '@angular/common';\n\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause to two to misalign.\n */\nexport class FocusTrap {\n  private _startAnchor: HTMLElement | null;\n  private _endAnchor: HTMLElement | null;\n\n  /** Whether the focus trap is active. */\n  get enabled(): boolean { return this._enabled; }\n  set enabled(val: boolean) {\n    this._enabled = val;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._startAnchor.tabIndex = this._endAnchor.tabIndex = this._enabled ? 0 : -1;\n    }\n  }\n  private _enabled: boolean = true;\n\n  constructor(\n    private _element: HTMLElement,\n    private _checker: InteractivityChecker,\n    private _ngZone: NgZone,\n    private _document: Document,\n    deferAnchors = false) {\n\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    if (this._startAnchor && this._startAnchor.parentNode) {\n      this._startAnchor.parentNode.removeChild(this._startAnchor);\n    }\n\n    if (this._endAnchor && this._endAnchor.parentNode) {\n      this._endAnchor.parentNode.removeChild(this._endAnchor);\n    }\n\n    this._startAnchor = this._endAnchor = null;\n  }\n\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   */\n  attachAnchors(): void {\n    if (!this._startAnchor) {\n      this._startAnchor = this._createAnchor();\n    }\n\n    if (!this._endAnchor) {\n      this._endAnchor = this._createAnchor();\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      this._startAnchor!.addEventListener('focus', () => {\n        this.focusLastTabbableElement();\n      });\n\n      this._endAnchor!.addEventListener('focus', () => {\n        this.focusFirstTabbableElement();\n      });\n\n      if (this._element.parentNode) {\n        this._element.parentNode.insertBefore(this._startAnchor!, this._element);\n        this._element.parentNode.insertBefore(this._endAnchor!, this._element.nextSibling);\n      }\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then either focuses the first element that the\n   * user specified, or the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusInitialElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement()));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusFirstTabbableElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement()));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusLastTabbableElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement()));\n    });\n  }\n\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  private _getRegionBoundary(bound: 'start' | 'end'): HTMLElement | null {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    let markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` +\n                                                 `[cdkFocusRegion${bound}], ` +\n                                                 `[cdk-focus-${bound}]`) as NodeListOf<HTMLElement>;\n\n    for (let i = 0; i < markers.length; i++) {\n      if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}',` +\n                     ` use 'cdkFocusRegion${bound}' instead.`, markers[i]);\n      } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}',` +\n                     ` use 'cdkFocusRegion${bound}' instead.`, markers[i]);\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ?\n        markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusInitialElement(): boolean {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` +\n                                                          `[cdkFocusInitial]`) as HTMLElement;\n\n    if (this._element.hasAttribute(`cdk-focus-initial`)) {\n      console.warn(`Found use of deprecated attribute 'cdk-focus-initial',` +\n                    ` use 'cdkFocusInitial' instead.`, this._element);\n    }\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n      return true;\n    }\n\n    return this.focusFirstTabbableElement();\n  }\n\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusFirstTabbableElement(): boolean {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusLastTabbableElement(): boolean {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  private _getFirstTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in DOM order. Note that IE doesn't have `children` for SVG so we fall\n    // back to `childNodes` which includes text nodes, comments etc.\n    let children = root.children || root.childNodes;\n\n    for (let i = 0; i < children.length; i++) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n        this._getFirstTabbableElement(children[i] as HTMLElement) :\n        null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  private _getLastTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in reverse DOM order.\n    let children = root.children || root.childNodes;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n        this._getLastTabbableElement(children[i] as HTMLElement) :\n        null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Creates an anchor element. */\n  private _createAnchor(): HTMLElement {\n    const anchor = this._document.createElement('div');\n    anchor.tabIndex = this._enabled ? 0 : -1;\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    return anchor;\n  }\n\n  /** Executes a function when the zone is stable. */\n  private _executeOnStable(fn: () => any): void {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.asObservable().pipe(take(1)).subscribe(fn);\n    }\n  }\n}\n\n\n/** Factory that allows easy instantiation of focus traps. */\n@Injectable()\nexport class FocusTrapFactory {\n  private _document: Document;\n\n  constructor(\n      private _checker: InteractivityChecker,\n      private _ngZone: NgZone,\n      @Inject(DOCUMENT) _document: any) {\n\n    this._document = _document;\n  }\n\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element: HTMLElement, deferCaptureElements: boolean = false): FocusTrap {\n    return new FocusTrap(\n        element, this._checker, this._ngZone, this._document, deferCaptureElements);\n  }\n}\n\n\n/**\n * Directive for trapping focus within a region.\n * @docs-private\n * @deprecated\n * @deletion-target 6.0.0\n */\n@Directive({\n  selector: 'cdk-focus-trap',\n})\nexport class FocusTrapDeprecatedDirective implements OnDestroy, AfterContentInit {\n  focusTrap: FocusTrap;\n\n  /** Whether the focus trap is active. */\n  @Input()\n  get disabled(): boolean { return !this.focusTrap.enabled; }\n  set disabled(val: boolean) {\n    this.focusTrap.enabled = !coerceBooleanProperty(val);\n  }\n\n  constructor(private _elementRef: ElementRef, private _focusTrapFactory: FocusTrapFactory) {\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy();\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n  }\n}\n\n\n/** Directive for trapping focus within a region. */\n@Directive({\n  selector: '[cdkTrapFocus]',\n  exportAs: 'cdkTrapFocus',\n})\nexport class CdkTrapFocus implements OnDestroy, AfterContentInit {\n  private _document: Document;\n\n  /** Underlying FocusTrap instance. */\n  focusTrap: FocusTrap;\n\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  private _previouslyFocusedElement: HTMLElement | null = null;\n\n  /** Whether the focus trap is active. */\n  @Input('cdkTrapFocus')\n  get enabled(): boolean { return this.focusTrap.enabled; }\n  set enabled(value: boolean) { this.focusTrap.enabled = coerceBooleanProperty(value); }\n\n  /**\n   * Whether the directive should automatially move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  @Input('cdkTrapFocusAutoCapture')\n  get autoCapture(): boolean { return this._autoCapture; }\n  set autoCapture(value: boolean) { this._autoCapture = coerceBooleanProperty(value); }\n  private _autoCapture: boolean;\n\n  constructor(\n      private _elementRef: ElementRef,\n      private _focusTrapFactory: FocusTrapFactory,\n      @Inject(DOCUMENT) _document: any) {\n\n    this._document = _document;\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy();\n\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n\n    if (this.autoCapture) {\n      this._previouslyFocusedElement = this._document.activeElement as HTMLElement;\n      this.focusTrap.focusInitialElementWhenReady();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\n\n\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\n@Injectable()\nexport class InteractivityChecker {\n\n  constructor(private _platform: Platform) {}\n\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element: HTMLElement): boolean {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element: HTMLElement): boolean {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element: HTMLElement): boolean {\n    // Nothing is tabbable on the the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n\n    const frameElement = getFrameElement(getWindow(element));\n\n    if (frameElement) {\n      const frameType = frameElement && frameElement.nodeName.toLowerCase();\n\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n\n      // Webkit and Blink consider anything inside of an <object> element as non-tabbable.\n      if ((this._platform.BLINK || this._platform.WEBKIT) && frameType === 'object') {\n        return false;\n      }\n\n      // Webkit and Blink disable tabbing to an element inside of an invisible frame.\n      if ((this._platform.BLINK || this._platform.WEBKIT) && !this.isVisible(frameElement)) {\n        return false;\n      }\n\n    }\n\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'iframe') {\n      // The frames may be tabbable depending on content, but it's not possibly to reliably\n      // investigate the content of the frames.\n      return false;\n    }\n\n    if (nodeName === 'audio') {\n      if (!element.hasAttribute('controls')) {\n        // By default an <audio> element without the controls enabled is not tabbable.\n        return false;\n      } else if (this._platform.BLINK) {\n        // In Blink <audio controls> elements are always tabbable.\n        return true;\n      }\n    }\n\n    if (nodeName === 'video') {\n      if (!element.hasAttribute('controls') && this._platform.TRIDENT) {\n        // In Trident a <video> element without the controls enabled is not tabbable.\n        return false;\n      } else if (this._platform.BLINK || this._platform.FIREFOX) {\n        // In Chrome and Firefox <video controls> elements are always tabbable.\n        return true;\n      }\n    }\n\n    if (nodeName === 'object' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      // In all Blink and WebKit based browsers <object> elements are never tabbable.\n      return false;\n    }\n\n    // In iOS the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n\n    return element.tabIndex >= 0;\n  }\n\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element: HTMLElement): boolean {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && this.isVisible(element);\n  }\n\n}\n\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window: Window) {\n  try {\n    return window.frameElement as HTMLElement;\n  } catch (e) {\n    return null;\n  }\n}\n\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element: HTMLElement): boolean {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight ||\n      (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n\n/** Gets whether an element's  */\nfunction isNativeFormElement(element: Node) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' ||\n      nodeName === 'select' ||\n      nodeName === 'button' ||\n      nodeName === 'textarea';\n}\n\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element: HTMLElement): boolean {\n  return isInputElement(element) && element.type == 'hidden';\n}\n\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element: HTMLElement): boolean {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n\n/** Gets whether an element is an input element. */\nfunction isInputElement(element: HTMLElement): element is HTMLInputElement {\n  return element.nodeName.toLowerCase() == 'input';\n}\n\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element: HTMLElement): element is HTMLAnchorElement {\n  return element.nodeName.toLowerCase() == 'a';\n}\n\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element: HTMLElement): boolean {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex');\n\n  // IE11 parses tabindex=\"\" as the value \"-32768\"\n  if (tabIndex == '-32768') {\n    return false;\n  }\n\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element: HTMLElement): number | null {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element: HTMLElement): boolean {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && (element as HTMLInputElement).type;\n\n  return inputType === 'text'\n      || inputType === 'password'\n      || nodeName === 'select'\n      || nodeName === 'textarea';\n}\n\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element: HTMLElement): boolean {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return isNativeFormElement(element) ||\n      isAnchorWithHref(element) ||\n      element.hasAttribute('contenteditable') ||\n      hasValidTabIndex(element);\n}\n\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node: HTMLElement): Window {\n  return node.ownerDocument.defaultView || window;\n}\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AYQA,AACA;;;;AAYA,AAAA,MAAA,oBAAA,CAAA;;;;IAEE,WAAF,CAAsB,SAAmB,EAAzC;QAAsB,IAAtB,CAAA,SAA+B,GAAT,SAAS,CAAU;KAAI;;;;;;;IAQ3C,UAAU,CAAC,OAAoB,EAAjC;;;QAGI,OAAO,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;KACzC;;;;;;;;;;IAUD,SAAS,CAAC,OAAoB,EAAhC;QACI,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC;KACnF;;;;;;;;IASD,UAAU,CAAC,OAAoB,EAAjC;;QAEI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,uBAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAEzD,IAAI,YAAY,EAAE;YAChB,uBAAM,SAAS,GAAG,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;;YAGtE,IAAI,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzC,OAAO,KAAK,CAAC;aACd;;YAGD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,KAAK,QAAQ,EAAE;gBAC7E,OAAO,KAAK,CAAC;aACd;;YAGD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;gBACpF,OAAO,KAAK,CAAC;aACd;SAEF;QAED,qBAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC9C,qBAAI,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;YAC3C,OAAO,aAAa,KAAK,CAAC,CAAC,CAAC;SAC7B;QAED,IAAI,QAAQ,KAAK,QAAQ,EAAE;;;YAGzB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,QAAQ,KAAK,OAAO,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;;gBAErC,OAAO,KAAK,CAAC;aACd;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;gBAE/B,OAAO,IAAI,CAAC;aACb;SACF;QAED,IAAI,QAAQ,KAAK,OAAO,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;;gBAE/D,OAAO,KAAK,CAAC;aACd;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;;gBAEzD,OAAO,IAAI,CAAC;aACb;SACF;QAED,IAAI,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;;YAE5E,OAAO,KAAK,CAAC;SACd;;QAGD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE;YACrF,OAAO,KAAK,CAAC;SACd;QAED,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC9B;;;;;;;IAQD,WAAW,CAAC,OAAoB,EAAlC;;;QAGI,OAAO,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;KAChG;;;IAxHH,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IAXA,EAAA,IAAA,EAAQ,QAAQ,GAAhB;;;;;;;;;AA4IA,SAAA,eAAA,CAAyB,MAAc,EAAvC;IACE,IAAI;QACF,yBAAO,MAAM,CAAC,YAA2B,EAAC;KAC3C;IAAC,wBAAO,CAAC,EAAE;QACV,OAAO,IAAI,CAAC;KACb;CACF;;;;;;AAGD,SAAA,WAAA,CAAqB,OAAoB,EAAzC;;;IAGE,OAAO,CAAC,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,YAAY;SAChD,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;CACxF;;;;;;AAGD,SAAA,mBAAA,CAA6B,OAAa,EAA1C;IACE,qBAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC9C,OAAO,QAAQ,KAAK,OAAO;QACvB,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,UAAU,CAAC;CAC7B;;;;;;AAGD,SAAA,aAAA,CAAuB,OAAoB,EAA3C;IACE,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC;CAC5D;;;;;;AAGD,SAAA,gBAAA,CAA0B,OAAoB,EAA9C;IACE,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;CACjE;;;;;;AAGD,SAAA,cAAA,CAAwB,OAAoB,EAA5C;IACE,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,OAAO,CAAC;CAClD;;;;;;AAGD,SAAA,eAAA,CAAyB,OAAoB,EAA7C;IACE,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC;CAC9C;;;;;;AAGD,SAAA,gBAAA,CAA0B,OAAoB,EAA9C;IACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;QACvE,OAAO,KAAK,CAAC;KACd;IAED,qBAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;;IAGhD,IAAI,QAAQ,IAAI,QAAQ,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;CACvD;;;;;;;AAMD,SAAA,gBAAA,CAA0B,OAAoB,EAA9C;IACE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE;QAC9B,OAAO,IAAI,CAAC;KACb;;IAGD,uBAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;IAEtE,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;CACxC;;;;;;AAGD,SAAA,wBAAA,CAAkC,OAAoB,EAAtD;IACE,qBAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC9C,qBAAI,SAAS,GAAG,QAAQ,KAAK,OAAO,IAAI,mBAAC,OAA2B,GAAE,IAAI,CAAC;IAE3E,OAAO,SAAS,KAAK,MAAM;WACpB,SAAS,KAAK,UAAU;WACxB,QAAQ,KAAK,QAAQ;WACrB,QAAQ,KAAK,UAAU,CAAC;CAChC;;;;;;;AAMD,SAAA,sBAAA,CAAgC,OAAoB,EAApD;;IAEE,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,KAAK,CAAC;KACd;IAED,OAAO,mBAAmB,CAAC,OAAO,CAAC;QAC/B,gBAAgB,CAAC,OAAO,CAAC;QACzB,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC;QACvC,gBAAgB,CAAC,OAAO,CAAC,CAAC;CAC/B;;;;;;AAGD,SAAA,SAAA,CAAmB,IAAiB,EAApC;IACE,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,MAAM,CAAC;CACjD;;;;;;;ADvPD,AAUA,AACA,AACA,AACA;;;;;;;AAUA,AAAA,MAAA,SAAA,CAAA;;;;;;;;IAeE,WAAF,CACY,QADZ,EAEY,QAFZ,EAGY,OAHZ,EAIY,SAJZ,EAKI,YAAY,GAAG,KAAK,EALxB;QACY,IAAZ,CAAA,QAAoB,GAAR,QAAQ,CAApB;QACY,IAAZ,CAAA,QAAoB,GAAR,QAAQ,CAApB;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QACY,IAAZ,CAAA,SAAqB,GAAT,SAAS,CAArB;QANA,IAAA,CAAA,QAAA,GAA8B,IAAI,CAAlC;QASI,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;;;;;IApBD,IAAI,OAAO,GAAb,EAA2B,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;;;;;IAChD,IAAI,OAAO,CAAC,GAAY,EAA1B;QACI,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEpB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;YACxC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAChF;KACF;;;;;IAgBD,OAAO,GAAT;QACI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7D;QAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;YACjD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC5C;;;;;;IAMD,aAAa,GAAf;QACI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;SAC1C;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;SACxC;QAED,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAnC;6BACA,EAAM,IAAI,CAAC,YAAY,GAAE,gBAAgB,CAAC,OAAO,EAAE,MAAnD;gBACQ,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACjC,CAAP,CAAA;YAEA,EAAM,IAAI,CAAC,UAAU,GAAE,gBAAgB,CAAC,OAAO,EAAE,MAAjD;gBACQ,IAAI,CAAC,yBAAyB,EAAE,CAAC;aAClC,CAAP,CAAA;YAEM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,oBAAC,IAAI,CAAC,YAAY,IAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,oBAAC,IAAI,CAAC,UAAU,IAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;aACpF;SACF,CAAC,CAAC;KACJ;;;;;;;IAQD,4BAA4B,GAA9B;QACI,OAAO,IAAI,OAAO,CAAU,OAAO,IAAvC;YACM,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;SAClE,CAAC,CAAC;KACJ;;;;;;;IAQD,kCAAkC,GAApC;QACI,OAAO,IAAI,OAAO,CAAU,OAAO,IAAvC;YACM,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;SACxE,CAAC,CAAC;KACJ;;;;;;;IAQD,iCAAiC,GAAnC;QACI,OAAO,IAAI,OAAO,CAAU,OAAO,IAAvC;YACM,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;SACvE,CAAC,CAAC;KACJ;;;;;;IAOO,kBAAkB,CAAC,KAAsB,EAAnD;;QAEI,qBAAI,OAAO,qBAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAjD,kBAAA,EAAsE,KAAK,CAA3E,GAAA,CAAgF;YAC/B,CAAjD,eAAA,EAAmE,KAAK,CAAxE,GAAA,CAA6E;YAC5B,CAAjD,WAAA,EAA+D,KAAK,CAApE,CAAA,CAAuE,CAA4B,CAAA,CAAC;QAEhG,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAlC,UAAA,EAA+C,KAAK,CAApD,CAAsD,CAAC,EAAE;gBACjD,OAAO,CAAC,IAAI,CAAC,CAArB,6CAAA,EAAqE,KAAK,CAA1E,EAAA,CAA8E;oBACzD,CAArB,oBAAA,EAA4C,KAAK,CAAjD,UAAA,CAA6D,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACpE;iBAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAzC,iBAAA,EAA6D,KAAK,CAAlE,CAAoE,CAAC,EAAE;gBAC/D,OAAO,CAAC,IAAI,CAAC,CAArB,oDAAA,EAA4E,KAAK,CAAjF,EAAA,CAAqF;oBAChE,CAArB,oBAAA,EAA4C,KAAK,CAAjD,UAAA,CAA6D,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACpE;SACF;QAED,IAAI,KAAK,IAAI,OAAO,EAAE;YACpB,OAAO,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnF;QACD,OAAO,OAAO,CAAC,MAAM;YACjB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;;;;;IAOhF,mBAAmB,GAArB;;QAEI,uBAAM,iBAAiB,qBAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAA1D,qBAAA,CAAiF;YACvB,CAA1D,iBAAA,CAA6E,CAAgB,CAAA,CAAC;QAE1F,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAnC,iBAAA,CAAsD,CAAC,EAAE;YACnD,OAAO,CAAC,IAAI,CAAC,CAAnB,sDAAA,CAA2E;gBACvD,CAApB,+BAAA,CAAqD,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjE;QAED,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC;KACzC;;;;;IAMD,yBAAyB,GAA3B;QACI,uBAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;SAC3B;QAED,OAAO,CAAC,CAAC,iBAAiB,CAAC;KAC5B;;;;;IAMD,wBAAwB,GAA1B;QACI,uBAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAEzD,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;SAC3B;QAED,OAAO,CAAC,CAAC,iBAAiB,CAAC;KAC5B;;;;;;IAGO,wBAAwB,CAAC,IAAiB,EAApD;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC;SACb;;;QAID,qBAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC;QAEhD,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,qBAAI,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY;gBACtE,IAAI,CAAC,wBAAwB,mBAAC,QAAQ,CAAC,CAAC,CAAgB,EAAC;gBACzD,IAAI,CAAC;YAEP,IAAI,aAAa,EAAE;gBACjB,OAAO,aAAa,CAAC;aACtB;SACF;QAED,OAAO,IAAI,CAAC;;;;;;;IAIN,uBAAuB,CAAC,IAAiB,EAAnD;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC;SACb;;QAGD,qBAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC;QAEhD,KAAK,qBAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7C,qBAAI,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY;gBACtE,IAAI,CAAC,uBAAuB,mBAAC,QAAQ,CAAC,CAAC,CAAgB,EAAC;gBACxD,IAAI,CAAC;YAEP,IAAI,aAAa,EAAE;gBACjB,OAAO,aAAa,CAAC;aACtB;SACF;QAED,OAAO,IAAI,CAAC;;;;;;IAIN,aAAa,GAAvB;QACI,uBAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC5C,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAC9C,OAAO,MAAM,CAAC;;;;;;;IAIR,gBAAgB,CAAC,EAAa,EAAxC;QACI,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,EAAE,EAAE,CAAC;SACN;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;SAClE;;CAEJ;;;;AAKD,AAAA,MAAA,gBAAA,CAAA;;;;;;IAGE,WAAF,CACc,QADd,EAEc,OAFd,EAGwB,SAHxB,EAAA;QACc,IAAd,CAAA,QAAsB,GAAR,QAAQ,CAAtB;QACc,IAAd,CAAA,OAAqB,GAAP,OAAO,CAArB;QAGI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;;;;;;;;IASD,MAAM,CAAC,OAAoB,EAAE,oBAA/B,GAA+D,KAAK,EAApE;QACI,OAAO,IAAI,SAAS,CAChB,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;KACjF;;;IAtBH,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IApQA,EAAA,IAAA,EAAQ,oBAAoB,GAA5B;IARA,EAAA,IAAA,EAAE,MAAM,GAAR;IAmRA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAO,MAAM,EAAb,IAAA,EAAA,CAAc,QAAQ,EAAtB,EAAA,EAAA,EAAA;;;;;;;;AA4BA,AAAA,MAAA,4BAAA,CAAA;;;;;IAUE,WAAF,CAAsB,WAAuB,EAAU,iBAAmC,EAA1F;QAAsB,IAAtB,CAAA,WAAiC,GAAX,WAAW,CAAY;QAAU,IAAvD,CAAA,iBAAwE,GAAjB,iBAAiB,CAAkB;QACtF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;KACtF;;;;;IAPH,IAAM,QAAQ,GAAd,EAA4B,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAA3D;;;;;IACE,IAAI,QAAQ,CAAC,GAAY,EAA3B;QACI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;KACtD;;;;IAMD,WAAW,GAAb;QACI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;KAC1B;;;;IAED,kBAAkB,GAApB;QACI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;KAChC;;;IAvBH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;gBACT,QAAQ,EAAE,gBAAgB;aAC3B,EAAD,EAAA;;;;IAhTA,EAAA,IAAA,EAAE,UAAU,GAAZ;IA+QA,EAAA,IAAA,EAAa,gBAAgB,GAA7B;;;IAsCA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,EAAA;;;;;AAyBA,AAAA,MAAA,YAAA,CAAA;;;;;;IAuBE,WAAF,CACc,WADd,EAEc,iBAFd,EAGwB,SAHxB,EAAA;QACc,IAAd,CAAA,WAAyB,GAAX,WAAW,CAAzB;QACc,IAAd,CAAA,iBAA+B,GAAjB,iBAAiB,CAA/B;;;;QAlBA,IAAA,CAAA,yBAAA,GAA0D,IAAI,CAA9D;QAqBI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;KACtF;;;;;IAnBH,IAAM,OAAO,GAAb,EAA2B,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAzD;;;;;IACE,IAAI,OAAO,CAAC,KAAc,EAA5B,EAAgC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;;;;;;IAOxF,IAAM,WAAW,GAAjB,EAA+B,OAAO,IAAI,CAAC,YAAY,CAAC,EAAxD;;;;;IACE,IAAI,WAAW,CAAC,KAAc,EAAhC,EAAoC,IAAI,CAAC,YAAY,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;;;;IAYrF,WAAW,GAAb;QACI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;;;QAIzB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACvC;KACF;;;;IAED,kBAAkB,GAApB;QACI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;QAE/B,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,yBAAyB,qBAAG,IAAI,CAAC,SAAS,CAAC,aAA4B,CAAA,CAAC;YAC7E,IAAI,CAAC,SAAS,CAAC,4BAA4B,EAAE,CAAC;SAC/C;KACF;;;IAtDH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;gBACT,QAAQ,EAAE,gBAAgB;gBAC1B,QAAQ,EAAE,cAAc;aACzB,EAAD,EAAA;;;;IA7UA,EAAA,IAAA,EAAE,UAAU,GAAZ;IA+QA,EAAA,IAAA,EAAa,gBAAgB,GAA7B;IAyFA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAO,MAAM,EAAb,IAAA,EAAA,CAAc,QAAQ,EAAtB,EAAA,EAAA,EAAA;;;IAhBA,SAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,IAAA,EAAA,CAAS,cAAc,EAAvB,EAAA,EAAA;IAQA,aAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,IAAA,EAAA,CAAS,yBAAyB,EAAlC,EAAA,EAAA;;;;;;;;;;;ADjWA,MAAM,cAAc,GAAG,GAAG,CAAC;;;;;;;;;AAM3B,AAAA,SAAA,mBAAA,CAAoC,EAAW,EAAE,IAAY,EAAE,EAAU,EAAzE;IACE,uBAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC1C,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE;QAAE,OAAO;KAAE;IACvE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAEpB,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;CACjD;;;;;;;;;AAMD,AAAA,SAAA,sBAAA,CAAuC,EAAW,EAAE,IAAY,EAAE,EAAU,EAA5E;IACE,uBAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC1C,uBAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAExD,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;CACzD;;;;;;;;AAMD,AAAA,SAAA,mBAAA,CAAoC,EAAW,EAAE,IAAY,EAA7D;;IAEE,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;CAC1D;;;;;;;ADjCD,AACA,AACA;;;;;;;;;AAeA,AAAO,MAAM,qBAAqB,GAAG,mCAAmC,CAAC;;;;AAGzE,AAAO,MAAM,yBAAyB,GAAG,yBAAyB,CAAC;;;;AAGnE,AAAO,MAAM,8BAA8B,GAAG,sBAAsB,CAAC;;;;AAGrE,IAAI,MAAM,GAAG,CAAC,CAAC;;;;AAGf,MAAM,eAAe,GAAG,IAAI,GAAG,EAA6B,CAAC;;;;AAG7D,IAAI,iBAAiB,GAAuB,IAAI,CAAC;;;;;;;AASjD,AAAA,MAAA,aAAA,CAAA;;;;IAGE,WAAF,CAAgC,SAAhC,EAAA;QACI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;;;;;;;;;IAOD,QAAQ,CAAC,WAAoB,EAAE,OAAe,EAAhD;QACI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC/C,OAAO;SACR;QAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACjC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC5D,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACjD;KACF;;;;;;;IAGD,iBAAiB,CAAC,WAAoB,EAAE,OAAe,EAAzD;QACI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC/C,OAAO;SACR;QAED,IAAI,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC3D,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACpD;QAED,uBAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,KAAK,CAAC,EAAE;YAC/D,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;SACrC;QAED,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAClE,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACjC;KACF;;;;;IAGD,WAAW,GAAb;QACI,uBAAM,iBAAiB,GACnB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CADxC,CAAA,EAC4C,8BAA8B,CAD1E,CAAA,CAC6E,CAAC,CAAC;QAE3E,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,IAAI,CAAC,iCAAiC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,8BAA8B,CAAC,CAAC;SACtE;QAED,IAAI,iBAAiB,EAAE;YACrB,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACjC;QAED,eAAe,CAAC,KAAK,EAAE,CAAC;KACzB;;;;;;;IAMO,qBAAqB,CAAC,OAAe,EAA/C;QACI,uBAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3D,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,CAAtC,EAAyC,yBAAyB,CAAlE,CAAA,EAAsE,MAAM,EAAE,CAA9E,CAAgF,CAAC,CAAC;QAC9E,cAAc,CAAC,WAAW,oBAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,GAAE,CAAC;QAEpE,IAAI,CAAC,iBAAiB,EAAE;YAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC;SAAE;QAChE,EAAI,iBAAiB,GAAE,WAAW,CAAC,cAAc,CAAjD,CAAA;QAEI,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,EAAC,cAAc,EAAE,cAAc,EAAE,CAAC,EAAC,CAAC,CAAC;;;;;;;IAI5D,qBAAqB,CAAC,OAAe,EAA/C;QACI,uBAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,uBAAM,cAAc,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC;QAC7E,IAAI,iBAAiB,IAAI,cAAc,EAAE;YACvC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;SAC/C;QACD,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;;;;;;IAI1B,wBAAwB,GAAlC;QACI,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACxD,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;QAC5D,iBAAiB,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACtD,iBAAiB,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;;;;;;IAI7C,wBAAwB,GAAlC;QACI,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,UAAU,EAAE;YACrD,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;YAC5D,iBAAiB,GAAG,IAAI,CAAC;SAC1B;;;;;;;IAIK,iCAAiC,CAAC,OAAgB,EAA5D;;QAEI,uBAAM,oBAAoB,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC;aACxE,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;;;;;;;IAOnE,oBAAoB,CAAC,OAAgB,EAAE,OAAe,EAAhE;QACI,uBAAM,iBAAiB,sBAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAC,CAAC;;;QAIxD,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACtF,OAAO,CAAC,YAAY,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAAC;QAEzD,iBAAiB,CAAC,cAAc,EAAE,CAAC;;;;;;;;;IAO7B,uBAAuB,CAAC,OAAgB,EAAE,OAAe,EAAnE;QACI,uBAAM,iBAAiB,sBAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAC,CAAC;QACxD,iBAAiB,CAAC,cAAc,EAAE,CAAC;QAEnC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACzF,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC,CAAC;;;;;;;;IAIlD,4BAA4B,CAAC,OAAgB,EAAE,OAAe,EAAxE;QACI,uBAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACtE,uBAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,uBAAM,SAAS,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;QAE3E,OAAO,CAAC,CAAC,SAAS,IAAI,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;;;;;;;;IAItD,eAAe,CAAC,OAAgB,EAAE,OAAe,EAA3D;QACI,OAAO,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,OAAO,IAAI,IAAI;YACtE,CAAC,CAAC,CAAV,EAAa,OAAO,CAApB,CAAsB,CAAC,IAAI,EAAE,CAAC;;;;IAzJ9B,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IAIA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAe,MAAM,EAArB,IAAA,EAAA,CAAsB,QAAQ,EAA9B,EAAA,EAAA,EAAA;;;;;;;;AA2JA,AAAA,SAAA,+BAAA,CAAgD,gBAA+B,EAAE,SAAc,EAA/F;IACE,OAAO,gBAAgB,IAAI,IAAI,aAAa,CAAC,SAAS,CAAC,CAAC;CACzD;;;;AAGD,AAAO,MAAM,uBAAuB,GAAG;;IAErC,OAAO,EAAE,aAAa;IACtB,IAAI,EAAE;QACJ,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,aAAa,CAAC;0BAC/C,QAA+B;KAChC;IACD,UAAU,EAAE,+BAA+B;CAC5C,CAAC;;;;;;;ADnNF,AACA,AACA,AAWA,AACA,AACA,AACA;;;;;;;;;AAeA,AAAA,MAAA,cAAA,CAAA;;;;IAkBE,WAAF,CAAsB,MAAoB,EAA1C;QAAsB,IAAtB,CAAA,MAA4B,GAAN,MAAM,CAAc;QAjB1C,IAAA,CAAA,gBAAA,GAA6B,CAAC,CAAC,CAA/B;QAEA,IAAA,CAAA,KAAA,GAAkB,KAAK,CAAvB;QACA,IAAA,CAAA,gBAAA,GAA6B,IAAI,OAAO,EAAU,CAAlD;QACA,IAAA,CAAA,sBAAA,GAAmC,YAAY,CAAC,KAAK,CAArD;QACA,IAAA,CAAA,SAAA,GAAsB,IAAI,CAA1B;;;;;QAOA,IAAA,CAAA,gBAAA,GAA6B,CAAC,IAAO,KAAK,IAAI,CAAC,QAAQ,CAAvD;QAGA,IAAA,CAAA,eAAA,GAAsC,EAAE,CAAxC;;;;;QAmBA,IAAA,CAAA,MAAA,GAA0B,IAAI,OAAO,EAAQ,CAA7C;;;;QAGA,IAAA,CAAA,MAAA,GAAW,IAAI,OAAO,EAAU,CAAhC;QAnBI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAsB,KAApD;YACM,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,uBAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrC,uBAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAErD,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE;oBACvD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;iBAClC;aACF;SACF,CAAC,CAAC;KACJ;;;;;;;IAgBD,aAAa,CAAC,SAA+B,EAA/C;QACI,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,OAAO,IAAI,CAAC;KACb;;;;;;IAMD,QAAQ,GAAV;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC;KACb;;;;;;IAMD,uBAAuB,CAAC,OAA1B,GAA6C,IAAI,EAAjD;QACI,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QACzB,OAAO,IAAI,CAAC;KACb;;;;;;;IAOD,yBAAyB,CAAC,SAA+B,EAA3D;QACI,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC7B,OAAO,IAAI,CAAC;KACb;;;;;;IAMD,aAAa,CAAC,gBAAhB,GAA2C,GAAG,EAA9C;QACI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE;YACvF,MAAM,KAAK,CAAC,8EAA8E,CAAC,CAAC;SAC7F;QAED,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;;;;QAK1C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CACtD,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAClD,YAAY,CAAC,gBAAgB,CAAC,EAC9B,MAAM,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,EAC7C,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CACzC,CAAC,SAAS,CAAC,WAAW,IAL3B;YAMM,uBAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;;;YAIpC,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzC,uBAAM,KAAK,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;gBACzD,uBAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;gBAE1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAxC,EACY,IAAI,CAAC,QAAQ,IADzB,CAC6B,WAAW,EADxC,CAC2C,IAAI,EAD/C,CACkD,OAAO,CAAC,WAAW,CADrE,KAC2E,CAAC,EAAE;oBAEpE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAC1B,MAAM;iBACP;aACF;YAED,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;SAC3B,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;KACb;;;;;;IAMD,aAAa,CAAC,KAAa,EAA7B;QACI,uBAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,gBAAgB,KAAK,aAAa,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;KACF;;;;;;IAMD,SAAS,CAAC,KAAoB,EAAhC;QACI,uBAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAE9B,QAAQ,OAAO;YACb,KAAK,GAAG;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACnB,OAAO;YAET,KAAK,UAAU;gBACb,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH,KAAK,WAAW;gBACd,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBACrC,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH,KAAK,UAAU;gBACb,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBAC9B,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;iBACP;qBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH;;;gBAGE,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;iBAC3D;qBAAM,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,EAAE;oBACjF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC1D;;;gBAID,OAAO;SACV;QAED,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;KACxB;;;;;IAGD,IAAI,eAAe,GAArB;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;;;;;IAGD,IAAI,UAAU,GAAhB;QACI,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;;;;;IAGD,kBAAkB,GAApB;QACI,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC;;;;;IAGD,iBAAiB,GAAnB;QACI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACxD;;;;;IAGD,iBAAiB,GAAnB;QACI,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;KACvF;;;;;IAGD,qBAAqB,GAAvB;QACI,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE;cACxB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1E;;;;;;IAMD,qBAAqB,CAAC,KAAa,EAArC;QACI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC/B;;;;;;;;;IAOO,qBAAqB,CAAC,KAAa,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAA5E;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC;cACvC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;;;;;;;;;IAQlD,oBAAoB,CAAC,KAAa,EAAE,KAAU,EAAxD;QACI,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,uBAAM,KAAK,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;YAClF,uBAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,OAAO;aACR;SACF;;;;;;;;;;IAQK,uBAAuB,CAAC,KAAa,EAAE,KAAU,EAA3D;QACI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;;;;;;;;;;;IAQlE,qBAAqB,CAAC,KAAa,EAAE,aAAqB,EACpC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAD7D;QAEI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACjB,OAAO;SACR;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1C,KAAK,IAAI,aAAa,CAAC;YAEvB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACjB,OAAO;aACR;SACF;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;CAE7B;;;;;;;AD3UD;;;;;;;AAeA,AAAA,MAAA,0BAA2C,SAAQ,cAAiC,CAApF;;;;;;;;IAOE,aAAa,CAAC,KAAa,EAA7B;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;SACrC;QACD,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;SACnC;KACF;CAEF;;;;;;;ADhCD;;;;;;;AAaA,AAAA,MAAA,eAAgC,SAAQ,cAAmC,CAA3E;;;QACA,IAAA,CAAA,OAAA,GAAiC,SAAS,CAA1C;;;;;;;IAME,cAAc,CAAC,MAAmB,EAApC;QACI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,OAAO,IAAI,CAAC;KACb;;;;;;;IAMD,aAAa,CAAC,KAAa,EAA7B;QACI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE3B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrC;KACF;CACF;;;;;;;ADpCD,AAQA,AAGA,AAAO,MAAM,4BAA4B,GAAG,IAAI,cAAc,CAAc,sBAAsB,CAAC,CAAC;AAMpG,AAAA,MAAA,aAAA,CAAA;;;;;IAGE,WAAF,CACwD,YADxD,EAEgC,SAFhC,EAAA;QAEgC,IAAhC,CAAA,SAAyC,GAAT,SAAS,CAAzC;;;;QAKI,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC/D;;;;;;;IAQD,QAAQ,CAAC,OAAe,EAAE,UAA5B,GAA6D,QAAQ,EAArE;QACI,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,EAAE,CAAC;;QAGnC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;;;;;;QAOxD,OAAO,IAAI,OAAO,CAAC,OAAO,IAA9B;YACM,UAAU,CAAC,MAAjB;gBACQ,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;gBACxC,OAAO,EAAE,CAAC;aACX,EAAE,GAAG,CAAC,CAAC;SACT,CAAC,CAAC;KACJ;;;;IAED,WAAW,GAAb;QACI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7D;KACF;;;;IAEO,kBAAkB,GAA5B;QACI,qBAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEjD,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC5C,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAC3C,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAExC,OAAO,MAAM,CAAC;;;;IAtDlB,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IAKA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAO,QAAQ,EAAf,EAAA,EAAA,IAAA,EAAmB,MAAM,EAAzB,IAAA,EAAA,CAA0B,4BAA4B,EAAtD,EAAA,EAAA,EAAA;IACA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAO,MAAM,EAAb,IAAA,EAAA,CAAc,QAAQ,EAAtB,EAAA,EAAA,EAAA;;;;;;;;;AAsDA,AAAA,SAAA,+BAAA,CACI,gBAA+B,EAAE,WAAgB,EAAE,SAAc,EADrE;IAEE,OAAO,gBAAgB,IAAI,IAAI,aAAa,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;CACtE;;;;AAGD,AAAO,MAAM,uBAAuB,GAAG;;IAErC,OAAO,EAAE,aAAa;IACtB,IAAI,EAAE;QACJ,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,aAAa,CAAC;QAC/C,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,MAAM,CAAC,4BAA4B,CAAC,CAAC;QAC1D,QAAQ;KACT;IACD,UAAU,EAAE,+BAA+B;CAC5C,CAAC;;;;;;AD5FF,AACA,AAaA,AACA;;AAMA,AAAO,MAAM,eAAe,GAAG,GAAG,CAAC;;;;AAenC,AAAA,MAAA,YAAA,CAAA;;;;;IA+BE,WAAF,CAAsB,OAAe,EAAU,SAAmB,EAAlE;QAAsB,IAAtB,CAAA,OAA6B,GAAP,OAAO,CAAQ;QAAU,IAA/C,CAAA,SAAwD,GAAT,SAAS,CAAU;;;;QA7BlE,IAAA,CAAA,OAAA,GAAiC,IAAI,CAArC;;;;QAMA,IAAA,CAAA,cAAA,GAA2B,KAAK,CAAhC;;;;QAeA,IAAA,CAAA,YAAA,GAAyB,IAAI,GAAG,EAAqC,CAArE;;;;QAGA,IAAA,CAAA,0BAAA,GAAuC,MAAvC,GAA+C,CAA/C;;;;QAGA,IAAA,CAAA,sBAAA,GAAmC,CAAC,CAApC;KAEsE;;;;;;;IAiBpE,OAAO,CACH,OAAoB,EACpB,QAA8B,EAC9B,aAAuB,EAH7B;;QAKI,IAAI,EAAE,QAAQ,YAAY,SAAS,CAAC,EAAE;YACpC,aAAa,GAAG,QAAQ,CAAC;SAC1B;QACD,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC;;QAGhC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAOA,EAAY,CAAC,IAAI,CAAC,CAAC;SAC3B;;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAClC,qBAAI,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtD,EAAM,UAAU,GAAE,aAAa,GAAG,aAAa,CAA/C;YACM,wBAAN,EAAa,UAAU,GAAE,OAAO,CAAC,YAAY,EAA7C,CAAgD;SAC3C;;QAGD,qBAAI,IAAI,GAAyB;YAC/B,QAAQ,EAAE,MAAhB,GAAwB;YAClB,aAAa,EAAE,aAAa;YAC5B,OAAO,EAAE,IAAI,OAAO,EAAe;SACpC,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,+BAA+B,EAAE,CAAC;;QAGvC,qBAAI,aAAa,GAAG,CAAC,KAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACzE,qBAAI,YAAY,GAAG,CAAC,KAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAnC;YACM,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YACvD,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;SACtD,CAAC,CAAC;;QAGH,IAAI,CAAC,QAAQ,GAAG,MAApB;YACM,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YAC1D,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;SACzD,CAAC;QAEF,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;KACpC;;;;;;IAMD,cAAc,CAAC,OAAoB,EAArC;QACI,uBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAE/B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,+BAA+B,EAAE,CAAC;SACxC;KACF;;;;;;;IAOD,QAAQ,CAAC,OAAoB,EAAE,MAAmB,EAApD;QACI,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,CAAC,KAAK,EAAE,CAAC;KACjB;;;;IAED,WAAW,GAAb;QACI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;KAC7E;;;;;IAGO,wBAAwB,GAAlC;;QAEI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO;SACR;;QAGD,qBAAI,uBAAuB,GAAG,MAAlC;YACM,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,8BAA8B,CAAC,UAAU,CAAC,CAAC;SACjD,CAAC;;;QAIF,qBAAI,yBAAyB,GAAG,MAApC;YACM,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;aAC9C;SACF,CAAC;;;;QAKF,qBAAI,0BAA0B,GAAG,CAAC,KAAiB,KAAvD;YACM,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,EAAE;gBAChC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACpC;YACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAC;YACrC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,gBAAgB,GAAG,IAAI,EAAE,eAAe,CAAC,CAAC;SACxF,CAAC;;;QAIF,qBAAI,mBAAmB,GAAG,MAA9B;YACM,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,cAAc,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/E,CAAC;;;QAIF,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAnC;YACM,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;YACpE,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,yBAAyB,EAAE,IAAI,CAAC,CAAC;YACxE,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,0BAA0B,EAC9D,6BAA6B,EAAE,sBAAI,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAQ,KAAI,IAAI,CAAC,CAAC;YACtF,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;SACvD,CAAC,CAAC;QAEH,IAAI,CAAC,0BAA0B,GAAG,MAAtC;YACM,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;YACvE,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAC3E,QAAQ,CAAC,mBAAmB,CAAC,YAAY,EAAE,0BAA0B,EACjE,6BAA6B,EAAE,sBAAI,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAQ,KAAI,IAAI,CAAC,CAAC;YACtF,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;;YAGzD,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACnC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACrC,CAAC;;;;;;;;IAGI,YAAY,CAAC,OAAgB,EAAE,SAAiB,EAAE,SAAkB,EAA9E;QACI,IAAI,SAAS,EAAE;YACb,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SAClC;aAAM;YACL,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACrC;;;;;;;;IAQK,WAAW,CAAC,OAAoB,EAAE,MAAoB,EAAhE;QACI,uBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sBAAsB,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC;YAC1E,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,qBAAqB,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC;SACzE;;;;;;;IAOK,8BAA8B,CAAC,MAAmB,EAA5D;QACI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;;;;;;;IAQ3D,iBAAiB,CAAC,KAAiB,EAA7C;;;;;;;;;;;;;;;;;;QAkBI,qBAAI,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO,IAAI,CAAC,gBAAgB,YAAY,IAAI,IAAI,WAAW,YAAY,IAAI;aACtE,WAAW,KAAK,IAAI,CAAC,gBAAgB,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;;;;;;;IAQrF,QAAQ,CAAC,KAAiB,EAAE,OAAoB,EAA1D;;;;;;;QAQI,uBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,WAAW,KAAK,CAAC,WAAW,CAAC,aAAa,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE;YAC5E,OAAO;SACR;;;;;;;QAQD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAChD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;aACtC;iBAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;gBACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;aACxB;iBAAM;gBACL,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;aAC1B;SACF;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;;;;;;;IAQtB,OAAO,CAAC,KAAiB,EAAE,OAAoB,EAAjD;;;QAGI,uBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,YAAY,IAAI;YACjF,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE;YAC1C,OAAO;SACR;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC1B,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAChC;;;;IAEO,+BAA+B,GAAzC;;QAEI,IAAI,EAAE,IAAI,CAAC,sBAAsB,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACjC;;;;;IAGK,+BAA+B,GAAzC;;QAEI,IAAI,CAAC,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,0BAA0B,GAAG,MAAxC,GAAgD,CAAC;SAC5C;;;;IAnUL,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IA7BA,EAAA,IAAA,EAAE,MAAM,GAAR;IANA,EAAA,IAAA,EAAQ,QAAQ,GAAhB;;;;;;;;;;;AAwXA,AAAA,MAAA,eAAA,CAAA;;;;;IAIE,WAAF,CAAsB,WAAuB,EAAU,aAA2B,EAAlF;QAAsB,IAAtB,CAAA,WAAiC,GAAX,WAAW,CAAY;QAAU,IAAvD,CAAA,aAAoE,GAAb,aAAa,CAAc;QAFlF,IAAA,CAAA,cAAA,GAA6B,IAAI,YAAY,EAAe,CAA5D;QAGI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAClD,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;aACrE,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;KAC5D;;;;IAED,WAAW,GAAb;QACI,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;KACzC;;;IAjBH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;gBACT,QAAQ,EAAE,oDAAoD;aAC/D,EAAD,EAAA;;;;IApXA,EAAA,IAAA,EAAE,UAAU,GAAZ;IAiCA,EAAA,IAAA,EAAa,YAAY,GAAzB;;;IAsVA,gBAAA,EAAA,CAAA,EAAA,IAAA,EAAG,MAAM,EAAT,EAAA;;;;;;;;;AAgBA,AAAA,SAAA,8BAAA,CACI,gBAA8B,EAAE,MAAc,EAAE,QAAkB,EADtE;IAEE,OAAO,gBAAgB,IAAI,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;CAC/D;;;;AAGD,AAAO,MAAM,sBAAsB,GAAG;;IAEpC,OAAO,EAAE,YAAY;IACrB,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,YAAY,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC;IACxE,UAAU,EAAE,8BAA8B;CAC3C,CAAC;;;;;;;;;;;;;;;;AD7YF,AAAA,SAAA,+BAAA,CAAgD,KAAiB,EAAjE;IACE,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,CAAC;CAC5B;;;;;;;ADTD,AACA,AACA,AACA,AACA,AACA,AAKA,AACA,AAeA,AAAA,MAAA,UAAA,CAAA;;;IAbA,EAAA,IAAA,EAAC,QAAQ,EAAT,IAAA,EAAA,CAAU;gBACR,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC;gBACvC,YAAY,EAAE,CAAC,YAAY,EAAE,4BAA4B,EAAE,eAAe,CAAC;gBAC3E,OAAO,EAAE,CAAC,YAAY,EAAE,4BAA4B,EAAE,eAAe,CAAC;gBACtE,SAAS,EAAE;oBACT,oBAAoB;oBACpB,gBAAgB;oBAChB,aAAa;oBACb,uBAAuB;oBACvB,uBAAuB;oBACvB,sBAAsB;iBACvB;aACF,EAAD,EAAA;;;;;;;;GD1BA,AAGA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAMA,AAA4C;;;;;;;;GDrB5C,AAA6B;;"}