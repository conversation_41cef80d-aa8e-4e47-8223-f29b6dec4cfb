{"_from": "tree-kill@^1.0.0", "_id": "tree-kill@1.2.2", "_inBundle": false, "_integrity": "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==", "_location": "/tree-kill", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tree-kill@^1.0.0", "name": "tree-kill", "escapedName": "tree-kill", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/@ngtools/webpack"], "_resolved": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz", "_shasum": "4ca09a9092c88b73a7cdc5e8a01b507b0790a0cc", "_spec": "tree-kill@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\@ngtools\\webpack", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "bin": {"tree-kill": "cli.js"}, "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "deprecated": false, "description": "kill trees of processes", "devDependencies": {"mocha": "^2.2.5"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "license": "MIT", "main": "index.js", "name": "tree-kill", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "scripts": {"test": "mocha"}, "types": "index.d.ts", "version": "1.2.2"}