{"_from": "define-property@^0.2.5", "_id": "define-property@0.2.5", "_inBundle": false, "_integrity": "sha512-Rr7<PERSON>jQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==", "_location": "/object-copy/define-property", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "define-property@^0.2.5", "name": "define-property", "escapedName": "define-property", "rawSpec": "^0.2.5", "saveSpec": null, "fetchSpec": "^0.2.5"}, "_requiredBy": ["/object-copy"], "_resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "_shasum": "c35b1ef918ec3c990f9a5bc57be04aacec5c8116", "_spec": "define-property@^0.2.5", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\object-copy", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/define-property/issues"}, "bundleDependencies": false, "dependencies": {"is-descriptor": "^0.1.0"}, "deprecated": false, "description": "Define a non-enumerable property on an object.", "devDependencies": {"mocha": "*", "should": "^7.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/define-property", "keywords": ["define", "define-property", "enumerable", "key", "non", "non-enumerable", "object", "prop", "property", "value"], "license": "MIT", "main": "index.js", "name": "define-property", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/define-property.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["mixin-deep", "mixin-object", "delegate-object", "forward-object"]}}, "version": "0.2.5"}