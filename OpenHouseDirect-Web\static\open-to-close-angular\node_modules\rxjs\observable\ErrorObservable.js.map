{"version": 3, "file": "ErrorObservable.js", "sourceRoot": "", "sources": ["../../src/observable/ErrorObservable.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAS3C;;;;GAIG;AACH;IAAqC,mCAAe;IAmDlD,yBAAmB,KAAU,EAAU,SAAsB;QAC3D,iBAAO,CAAC;QADS,UAAK,GAAL,KAAK,CAAK;QAAU,cAAS,GAAT,SAAS,CAAa;IAE7D,CAAC;IAnDD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACI,sBAAM,GAAb,UAAc,KAAU,EAAE,SAAsB;QAC9C,MAAM,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC/C,CAAC;IAEM,wBAAQ,GAAf,UAAgB,GAAgB;QACtB,qBAAK,EAAE,2BAAU,CAAS;QAClC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAMD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAA2B;QACzE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,UAAU,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAErC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACrD,YAAK,EAAE,sBAAU;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AArED,CAAqC,uBAAU,GAqE9C;AArEY,uBAAe,kBAqE3B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { TeardownLogic } from '../Subscription';\nimport { Subscriber } from '../Subscriber';\n\nexport interface DispatchArg {\n  error: any;\n  subscriber: any;\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class ErrorObservable extends Observable<any> {\n\n  /**\n   * Creates an Observable that emits no items to the Observer and immediately\n   * emits an error notification.\n   *\n   * <span class=\"informal\">Just emits 'error', and nothing else.\n   * </span>\n   *\n   * <img src=\"./img/throw.png\" width=\"100%\">\n   *\n   * This static operator is useful for creating a simple Observable that only\n   * emits the error notification. It can be used for composing with other\n   * Observables, such as in a {@link mergeMap}.\n   *\n   * @example <caption>Emit the number 7, then emit an error.</caption>\n   * var result = Rx.Observable.throw(new Error('oops!')).startWith(7);\n   * result.subscribe(x => console.log(x), e => console.error(e));\n   *\n   * @example <caption>Map and flatten numbers to the sequence 'a', 'b', 'c', but throw an error for 13</caption>\n   * var interval = Rx.Observable.interval(1000);\n   * var result = interval.mergeMap(x =>\n   *   x === 13 ?\n   *     Rx.Observable.throw('Thirteens are bad') :\n   *     Rx.Observable.of('a', 'b', 'c')\n   * );\n   * result.subscribe(x => console.log(x), e => console.error(e));\n   *\n   * @see {@link create}\n   * @see {@link empty}\n   * @see {@link never}\n   * @see {@link of}\n   *\n   * @param {any} error The particular Error to pass to the error notification.\n   * @param {Scheduler} [scheduler] A {@link IScheduler} to use for scheduling\n   * the emission of the error notification.\n   * @return {Observable} An error Observable: emits only the error notification\n   * using the given error argument.\n   * @static true\n   * @name throw\n   * @owner Observable\n   */\n  static create(error: any, scheduler?: IScheduler): ErrorObservable {\n    return new ErrorObservable(error, scheduler);\n  }\n\n  static dispatch(arg: DispatchArg) {\n    const { error, subscriber } = arg;\n    subscriber.error(error);\n  }\n\n  constructor(public error: any, private scheduler?: IScheduler) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<any>): TeardownLogic {\n    const error = this.error;\n    const scheduler = this.scheduler;\n\n    subscriber.syncErrorThrowable = true;\n\n    if (scheduler) {\n      return scheduler.schedule(ErrorObservable.dispatch, 0, {\n        error, subscriber\n      });\n    } else {\n      subscriber.error(error);\n    }\n  }\n}\n"]}