/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export default [
    [
        ['gece', 'ö', 'sabah', 'öğleden önce', 'öğleden sonra', 'ak<PERSON><PERSON><PERSON><PERSON><PERSON>', 'akşam', 'gece'],
        [
            'gece yarısı', 'öğle', 'sabah', 'öğleden önce', 'öğleden sonra', 'ak<PERSON><PERSON><PERSON>st<PERSON>',
            'akşam', 'gece'
        ],
    ],
    [
        [
            'gece yarısı', 'öğle', 'sabah', 'öğleden önce', 'öğleden sonra', 'ak<PERSON><PERSON><PERSON>st<PERSON>',
            'akşam', 'gece'
        ],
        ,
    ],
    [
        '00:00', '12:00', ['06:00', '11:00'], ['11:00', '12:00'], ['12:00', '18:00'],
        ['18:00', '19:00'], ['19:00', '21:00'], ['21:00', '06:00']
    ]
];
//# sourceMappingURL=tr-CY.js.map