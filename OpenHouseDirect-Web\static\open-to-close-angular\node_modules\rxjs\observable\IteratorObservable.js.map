{"version": 3, "file": "IteratorObservable.js", "sourceRoot": "", "sources": ["../../src/observable/IteratorObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,qBAAqB,cAAc,CAAC,CAAA;AAEpC,2BAA2B,eAAe,CAAC,CAAA;AAC3C,yBAA4C,oBAAoB,CAAC,CAAA;AAIjE;;;;GAIG;AACH;IAA2C,sCAAa;IAmCtD,4BAAY,QAAa,EAAU,SAAsB;QACvD,iBAAO,CAAC;QADyB,cAAS,GAAT,SAAS,CAAa;QAGvD,EAAE,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAxCM,yBAAM,GAAb,UAAiB,QAAa,EAAE,SAAsB;QACpD,MAAM,CAAC,IAAI,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAEM,2BAAQ,GAAf,UAAgB,KAAU;QAEhB,uBAAK,EAAE,yBAAQ,EAAE,yBAAQ,EAAE,6BAAU,CAAW;QAExD,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACb,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,CAAC;QACT,CAAC;QAED,IAAI,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC7B,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YAChB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QAExB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC;gBAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC;YACD,MAAM,CAAC;QACT,CAAC;QAEM,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAYD,oCAAoC,CAAC,uCAAU,GAAV,UAAW,UAAyB;QAEvE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAA,SAAoC,EAA5B,sBAAQ,EAAE,wBAAS,CAAU;QAErC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACxD,YAAK,EAAE,kBAAQ,EAAE,sBAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC;gBACF,IAAI,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC7B,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACtB,KAAK,CAAC;gBACR,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChC,CAAC;gBACD,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACtB,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC;wBAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACpB,CAAC;oBACD,KAAK,CAAC;gBACR,CAAC;YACH,CAAC,QAAQ,IAAI,EAAE;QACjB,CAAC;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AAxED,CAA2C,uBAAU,GAwEpD;AAxEY,0BAAkB,qBAwE9B,CAAA;AAED;IACE,wBAAoB,GAAW,EACX,GAAe,EACf,GAAwB;QADhC,mBAAuB,GAAvB,OAAuB;QACvB,mBAAgC,GAAhC,MAAsB,GAAG,CAAC,MAAM;QAFxB,QAAG,GAAH,GAAG,CAAQ;QACX,QAAG,GAAH,GAAG,CAAY;QACf,QAAG,GAAH,GAAG,CAAqB;IAC5C,CAAC;IACD,yBAAC,mBAAe,CAAC,GAAjB,cAAsB,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtC,6BAAI,GAAJ;QACE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG;YACzB,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SACrC,GAAG;YACA,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;IACH,qBAAC;AAAD,CAAC,AAfD,IAeC;AAED;IACE,uBAAoB,GAAe,EACf,GAAe,EACf,GAA2B;QADnC,mBAAuB,GAAvB,OAAuB;QACvB,mBAAmC,GAAnC,MAAsB,QAAQ,CAAC,GAAG,CAAC;QAF3B,QAAG,GAAH,GAAG,CAAY;QACf,QAAG,GAAH,GAAG,CAAY;QACf,QAAG,GAAH,GAAG,CAAwB;IAC/C,CAAC;IACD,wBAAC,mBAAe,CAAC,GAAjB,cAAsB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACpC,4BAAI,GAAJ;QACE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG;YACzB,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SAC9B,GAAG;YACA,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AAfD,IAeC;AAED,qBAAqB,GAAQ;IAC3B,IAAM,CAAC,GAAG,GAAG,CAAC,mBAAe,CAAC,CAAC;IAC/B,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC;QAClC,MAAM,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC;IAChD,CAAC;IACD,MAAM,CAAC,GAAG,CAAC,mBAAe,CAAC,EAAE,CAAC;AAChC,CAAC;AAED,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAE3C,kBAAkB,CAAM;IACtB,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IACpB,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACb,MAAM,CAAC,CAAC,CAAC;IACb,CAAC;IACD,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,CAAC,GAAG,CAAC;IACf,CAAC;IACD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACX,MAAM,CAAC,CAAC,CAAC;IACb,CAAC;IACD,EAAE,CAAC,CAAC,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC;QACvB,MAAM,CAAC,cAAc,CAAC;IAC1B,CAAC;IACD,MAAM,CAAC,GAAG,CAAC;AACb,CAAC;AAED,wBAAwB,KAAU;IAChC,MAAM,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,WAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3D,CAAC;AAED,cAAc,KAAU;IACtB,IAAI,aAAa,GAAG,CAAC,KAAK,CAAC;IAC3B,EAAE,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,aAAa,CAAC;IACvB,CAAC;IACD,EAAE,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC", "sourcesContent": ["import { root } from '../util/root';\nimport { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { TeardownLogic } from '../Subscription';\nimport { Subscriber } from '../Subscriber';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class IteratorObservable<T> extends Observable<T> {\n  private iterator: any;\n\n  static create<T>(iterator: any, scheduler?: IScheduler): IteratorObservable<T> {\n    return new IteratorObservable(iterator, scheduler);\n  }\n\n  static dispatch(state: any) {\n\n    const { index, hasError, iterator, subscriber } = state;\n\n    if (hasError) {\n      subscriber.error(state.error);\n      return;\n    }\n\n    let result = iterator.next();\n    if (result.done) {\n      subscriber.complete();\n      return;\n    }\n\n    subscriber.next(result.value);\n    state.index = index + 1;\n\n    if (subscriber.closed) {\n      if (typeof iterator.return === 'function') {\n        iterator.return();\n      }\n      return;\n    }\n\n    (<any> this).schedule(state);\n  }\n\n  constructor(iterator: any, private scheduler?: IScheduler) {\n    super();\n\n    if (iterator == null) {\n      throw new Error('iterator cannot be null.');\n    }\n\n    this.iterator = getIterator(iterator);\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n\n    let index = 0;\n    const { iterator, scheduler } = this;\n\n    if (scheduler) {\n      return scheduler.schedule(IteratorObservable.dispatch, 0, {\n        index, iterator, subscriber\n      });\n    } else {\n      do {\n        let result = iterator.next();\n        if (result.done) {\n          subscriber.complete();\n          break;\n        } else {\n          subscriber.next(result.value);\n        }\n        if (subscriber.closed) {\n          if (typeof iterator.return === 'function') {\n            iterator.return();\n          }\n          break;\n        }\n      } while (true);\n    }\n  }\n}\n\nclass StringIterator {\n  constructor(private str: string,\n              private idx: number = 0,\n              private len: number = str.length) {\n  }\n  [Symbol_iterator]() { return (this); }\n  next() {\n    return this.idx < this.len ? {\n        done: false,\n        value: this.str.charAt(this.idx++)\n    } : {\n        done: true,\n        value: undefined\n    };\n  }\n}\n\nclass ArrayIterator {\n  constructor(private arr: Array<any>,\n              private idx: number = 0,\n              private len: number = toLength(arr)) {\n  }\n  [Symbol_iterator]() { return this; }\n  next() {\n    return this.idx < this.len ? {\n        done: false,\n        value: this.arr[this.idx++]\n    } : {\n        done: true,\n        value: undefined\n    };\n  }\n}\n\nfunction getIterator(obj: any) {\n  const i = obj[Symbol_iterator];\n  if (!i && typeof obj === 'string') {\n    return new StringIterator(obj);\n  }\n  if (!i && obj.length !== undefined) {\n    return new ArrayIterator(obj);\n  }\n  if (!i) {\n    throw new TypeError('object is not iterable');\n  }\n  return obj[Symbol_iterator]();\n}\n\nconst maxSafeInteger = Math.pow(2, 53) - 1;\n\nfunction toLength(o: any) {\n  let len = +o.length;\n  if (isNaN(len)) {\n      return 0;\n  }\n  if (len === 0 || !numberIsFinite(len)) {\n      return len;\n  }\n  len = sign(len) * Math.floor(Math.abs(len));\n  if (len <= 0) {\n      return 0;\n  }\n  if (len > maxSafeInteger) {\n      return maxSafeInteger;\n  }\n  return len;\n}\n\nfunction numberIsFinite(value: any) {\n  return typeof value === 'number' && root.isFinite(value);\n}\n\nfunction sign(value: any) {\n  let valueAsNumber = +value;\n  if (valueAsNumber === 0) {\n    return valueAsNumber;\n  }\n  if (isNaN(valueAsNumber)) {\n    return valueAsNumber;\n  }\n  return valueAsNumber < 0 ? -1 : 1;\n}\n"]}