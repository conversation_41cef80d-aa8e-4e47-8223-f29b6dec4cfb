{"version": 3, "file": "testing.js", "sources": ["../../../packages/common/esm5/testing/src/location_mock.js", "../../../packages/common/esm5/testing/src/mock_location_strategy.js", "../../../packages/common/esm5/testing/src/testing.js", "../../../packages/common/esm5/testing/public_api.js", "../../../packages/common/esm5/testing/testing.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { EventEmitter, Injectable } from '@angular/core';\n/**\n * A spy for {\\@link Location} that allows tests to fire simulated location events.\n *\n * \\@experimental\n */\nvar SpyLocation = /** @class */ (function () {\n    function SpyLocation() {\n        this.urlChanges = [];\n        this._history = [new LocationState('', '')];\n        this._historyIndex = 0;\n        /**\n         * \\@internal\n         */\n        this._subject = new EventEmitter();\n        /**\n         * \\@internal\n         */\n        this._baseHref = '';\n        /**\n         * \\@internal\n         */\n        this._platformStrategy = /** @type {?} */ ((null));\n    }\n    /**\n     * @param {?} url\n     * @return {?}\n     */\n    SpyLocation.prototype.setInitialPath = /**\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) { this._history[this._historyIndex].path = url; };\n    /**\n     * @param {?} url\n     * @return {?}\n     */\n    SpyLocation.prototype.setBaseHref = /**\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) { this._baseHref = url; };\n    /**\n     * @return {?}\n     */\n    SpyLocation.prototype.path = /**\n     * @return {?}\n     */\n    function () { return this._history[this._historyIndex].path; };\n    /**\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    SpyLocation.prototype.isCurrentPathEqualTo = /**\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    function (path, query) {\n        if (query === void 0) { query = ''; }\n        var /** @type {?} */ givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n        var /** @type {?} */ currPath = this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n        return currPath == givenPath + (query.length > 0 ? ('?' + query) : '');\n    };\n    /**\n     * @param {?} pathname\n     * @return {?}\n     */\n    SpyLocation.prototype.simulateUrlPop = /**\n     * @param {?} pathname\n     * @return {?}\n     */\n    function (pathname) {\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'popstate' });\n    };\n    /**\n     * @param {?} pathname\n     * @return {?}\n     */\n    SpyLocation.prototype.simulateHashChange = /**\n     * @param {?} pathname\n     * @return {?}\n     */\n    function (pathname) {\n        // Because we don't prevent the native event, the browser will independently update the path\n        this.setInitialPath(pathname);\n        this.urlChanges.push('hash: ' + pathname);\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'hashchange' });\n    };\n    /**\n     * @param {?} url\n     * @return {?}\n     */\n    SpyLocation.prototype.prepareExternalUrl = /**\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) {\n        if (url.length > 0 && !url.startsWith('/')) {\n            url = '/' + url;\n        }\n        return this._baseHref + url;\n    };\n    /**\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    SpyLocation.prototype.go = /**\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    function (path, query) {\n        if (query === void 0) { query = ''; }\n        path = this.prepareExternalUrl(path);\n        if (this._historyIndex > 0) {\n            this._history.splice(this._historyIndex + 1);\n        }\n        this._history.push(new LocationState(path, query));\n        this._historyIndex = this._history.length - 1;\n        var /** @type {?} */ locationState = this._history[this._historyIndex - 1];\n        if (locationState.path == path && locationState.query == query) {\n            return;\n        }\n        var /** @type {?} */ url = path + (query.length > 0 ? ('?' + query) : '');\n        this.urlChanges.push(url);\n        this._subject.emit({ 'url': url, 'pop': false });\n    };\n    /**\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    SpyLocation.prototype.replaceState = /**\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    function (path, query) {\n        if (query === void 0) { query = ''; }\n        path = this.prepareExternalUrl(path);\n        var /** @type {?} */ history = this._history[this._historyIndex];\n        if (history.path == path && history.query == query) {\n            return;\n        }\n        history.path = path;\n        history.query = query;\n        var /** @type {?} */ url = path + (query.length > 0 ? ('?' + query) : '');\n        this.urlChanges.push('replace: ' + url);\n    };\n    /**\n     * @return {?}\n     */\n    SpyLocation.prototype.forward = /**\n     * @return {?}\n     */\n    function () {\n        if (this._historyIndex < (this._history.length - 1)) {\n            this._historyIndex++;\n            this._subject.emit({ 'url': this.path(), 'pop': true });\n        }\n    };\n    /**\n     * @return {?}\n     */\n    SpyLocation.prototype.back = /**\n     * @return {?}\n     */\n    function () {\n        if (this._historyIndex > 0) {\n            this._historyIndex--;\n            this._subject.emit({ 'url': this.path(), 'pop': true });\n        }\n    };\n    /**\n     * @param {?} onNext\n     * @param {?=} onThrow\n     * @param {?=} onReturn\n     * @return {?}\n     */\n    SpyLocation.prototype.subscribe = /**\n     * @param {?} onNext\n     * @param {?=} onThrow\n     * @param {?=} onReturn\n     * @return {?}\n     */\n    function (onNext, onThrow, onReturn) {\n        return this._subject.subscribe({ next: onNext, error: onThrow, complete: onReturn });\n    };\n    /**\n     * @param {?} url\n     * @return {?}\n     */\n    SpyLocation.prototype.normalize = /**\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) { return /** @type {?} */ ((null)); };\n    SpyLocation.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    SpyLocation.ctorParameters = function () { return []; };\n    return SpyLocation;\n}());\nexport { SpyLocation };\nfunction SpyLocation_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    SpyLocation.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    SpyLocation.ctorParameters;\n    /** @type {?} */\n    SpyLocation.prototype.urlChanges;\n    /** @type {?} */\n    SpyLocation.prototype._history;\n    /** @type {?} */\n    SpyLocation.prototype._historyIndex;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    SpyLocation.prototype._subject;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    SpyLocation.prototype._baseHref;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    SpyLocation.prototype._platformStrategy;\n}\nvar LocationState = /** @class */ (function () {\n    function LocationState(path, query) {\n        this.path = path;\n        this.query = query;\n    }\n    return LocationState;\n}());\nfunction LocationState_tsickle_Closure_declarations() {\n    /** @type {?} */\n    LocationState.prototype.path;\n    /** @type {?} */\n    LocationState.prototype.query;\n}\n//# sourceMappingURL=location_mock.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport * as tslib_1 from \"tslib\";\nimport { LocationStrategy } from '@angular/common';\nimport { EventEmitter, Injectable } from '@angular/core';\n/**\n * A mock implementation of {\\@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * \\@stable\n */\nvar MockLocationStrategy = /** @class */ (function (_super) {\n    tslib_1.__extends(MockLocationStrategy, _super);\n    function MockLocationStrategy() {\n        var _this = _super.call(this) || this;\n        _this.internalBaseHref = '/';\n        _this.internalPath = '/';\n        _this.internalTitle = '';\n        _this.urlChanges = [];\n        /**\n         * \\@internal\n         */\n        _this._subject = new EventEmitter();\n        return _this;\n    }\n    /**\n     * @param {?} url\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.simulatePopState = /**\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) {\n        this.internalPath = url;\n        this._subject.emit(new _MockPopStateEvent(this.path()));\n    };\n    /**\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.path = /**\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    function (includeHash) {\n        if (includeHash === void 0) { includeHash = false; }\n        return this.internalPath;\n    };\n    /**\n     * @param {?} internal\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.prepareExternalUrl = /**\n     * @param {?} internal\n     * @return {?}\n     */\n    function (internal) {\n        if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n            return this.internalBaseHref + internal.substring(1);\n        }\n        return this.internalBaseHref + internal;\n    };\n    /**\n     * @param {?} ctx\n     * @param {?} title\n     * @param {?} path\n     * @param {?} query\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.pushState = /**\n     * @param {?} ctx\n     * @param {?} title\n     * @param {?} path\n     * @param {?} query\n     * @return {?}\n     */\n    function (ctx, title, path, query) {\n        this.internalTitle = title;\n        var /** @type {?} */ url = path + (query.length > 0 ? ('?' + query) : '');\n        this.internalPath = url;\n        var /** @type {?} */ externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push(externalUrl);\n    };\n    /**\n     * @param {?} ctx\n     * @param {?} title\n     * @param {?} path\n     * @param {?} query\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.replaceState = /**\n     * @param {?} ctx\n     * @param {?} title\n     * @param {?} path\n     * @param {?} query\n     * @return {?}\n     */\n    function (ctx, title, path, query) {\n        this.internalTitle = title;\n        var /** @type {?} */ url = path + (query.length > 0 ? ('?' + query) : '');\n        this.internalPath = url;\n        var /** @type {?} */ externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push('replace: ' + externalUrl);\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.onPopState = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._subject.subscribe({ next: fn }); };\n    /**\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.getBaseHref = /**\n     * @return {?}\n     */\n    function () { return this.internalBaseHref; };\n    /**\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.back = /**\n     * @return {?}\n     */\n    function () {\n        if (this.urlChanges.length > 0) {\n            this.urlChanges.pop();\n            var /** @type {?} */ nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n            this.simulatePopState(nextUrl);\n        }\n    };\n    /**\n     * @return {?}\n     */\n    MockLocationStrategy.prototype.forward = /**\n     * @return {?}\n     */\n    function () { throw 'not implemented'; };\n    MockLocationStrategy.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    MockLocationStrategy.ctorParameters = function () { return []; };\n    return MockLocationStrategy;\n}(LocationStrategy));\nexport { MockLocationStrategy };\nfunction MockLocationStrategy_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    MockLocationStrategy.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    MockLocationStrategy.ctorParameters;\n    /** @type {?} */\n    MockLocationStrategy.prototype.internalBaseHref;\n    /** @type {?} */\n    MockLocationStrategy.prototype.internalPath;\n    /** @type {?} */\n    MockLocationStrategy.prototype.internalTitle;\n    /** @type {?} */\n    MockLocationStrategy.prototype.urlChanges;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    MockLocationStrategy.prototype._subject;\n}\nvar _MockPopStateEvent = /** @class */ (function () {\n    function _MockPopStateEvent(newUrl) {\n        this.newUrl = newUrl;\n        this.pop = true;\n        this.type = 'popstate';\n    }\n    return _MockPopStateEvent;\n}());\nfunction _MockPopStateEvent_tsickle_Closure_declarations() {\n    /** @type {?} */\n    _MockPopStateEvent.prototype.pop;\n    /** @type {?} */\n    _MockPopStateEvent.prototype.type;\n    /** @type {?} */\n    _MockPopStateEvent.prototype.newUrl;\n}\n//# sourceMappingURL=mock_location_strategy.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport { SpyLocation } from './location_mock';\nexport { MockLocationStrategy } from './mock_location_strategy';\n//# sourceMappingURL=testing.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport { SpyLocation, MockLocationStrategy } from './src/testing';\n// This file only reexports content of the `src` folder. Keep it that way.\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { SpyLocation, MockLocationStrategy } from './public_api';\n//# sourceMappingURL=testing.js.map"], "names": ["tslib_1.__extends"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;AAWA,AACA;;;;;AAKA,IAAI,WAAW,kBAAkB,YAAY;IACzC,SAAS,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;;;;QAIvB,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;;;;QAInC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;;;;QAIpB,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,EAAE,CAAC;KACtD;;;;;IAKD,WAAW,CAAC,SAAS,CAAC,cAAc;;;;IAIpC,UAAU,GAAG,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;;;;;IAKjE,WAAW,CAAC,SAAS,CAAC,WAAW;;;;IAIjC,UAAU,GAAG,EAAE,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;;;;IAIzC,WAAW,CAAC,SAAS,CAAC,IAAI;;;IAG1B,YAAY,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;;;IAM/D,WAAW,CAAC,SAAS,CAAC,oBAAoB;;;;;IAK1C,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;QACrC,qBAAqB,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAChG,qBAAqB,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC3H,OAAO,QAAQ,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;KAC1E,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,cAAc;;;;IAIpC,UAAU,QAAQ,EAAE;QAChB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;KAC5E,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,kBAAkB;;;;IAIxC,UAAU,QAAQ,EAAE;;QAEhB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC;KAC9E,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,kBAAkB;;;;IAIxC,UAAU,GAAG,EAAE;QACX,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;SACnB;QACD,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KAC/B,CAAC;;;;;;IAMF,WAAW,CAAC,SAAS,CAAC,EAAE;;;;;IAKxB,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;QACrC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9C,qBAAqB,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QAC3E,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa,CAAC,KAAK,IAAI,KAAK,EAAE;YAC5D,OAAO;SACV;QACD,qBAAqB,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;KACpD,CAAC;;;;;;IAMF,WAAW,CAAC,SAAS,CAAC,YAAY;;;;;IAKlC,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;QACrC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACrC,qBAAqB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjE,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE;YAChD,OAAO;SACV;QACD,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,qBAAqB,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;KAC3C,CAAC;;;;IAIF,WAAW,CAAC,SAAS,CAAC,OAAO;;;IAG7B,YAAY;QACR,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACjD,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;SAC3D;KACJ,CAAC;;;;IAIF,WAAW,CAAC,SAAS,CAAC,IAAI;;;IAG1B,YAAY;QACR,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;SAC3D;KACJ,CAAC;;;;;;;IAOF,WAAW,CAAC,SAAS,CAAC,SAAS;;;;;;IAM/B,UAAU,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;KACxF,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,SAAS;;;;IAI/B,UAAU,GAAG,EAAE,EAAE,0BAA0B,IAAI,GAAG,EAAE,CAAC;IACrD,WAAW,CAAC,UAAU,GAAG;QACrB,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,WAAW,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACxD,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC,CAAC;AACL,AA+BA,IAAI,aAAa,kBAAkB,YAAY;IAC3C,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;IACD,OAAO,aAAa,CAAC;CACxB,EAAE,CAAC;;AC/PJ;;;;;;;;;;;AAWA,AAGA;;;;;;AAMA,IAAI,oBAAoB,kBAAkB,UAAU,MAAM,EAAE;IACxDA,SAAiB,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;IAChD,SAAS,oBAAoB,GAAG;QAC5B,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QACtC,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC7B,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC;QACzB,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;QACzB,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;;;;QAItB,KAAK,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC;KAChB;;;;;IAKD,oBAAoB,CAAC,SAAS,CAAC,gBAAgB;;;;IAI/C,UAAU,GAAG,EAAE;QACX,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC3D,CAAC;;;;;IAKF,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;;IAInC,UAAU,WAAW,EAAE;QACnB,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE;QACpD,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B,CAAC;;;;;IAKF,oBAAoB,CAAC,SAAS,CAAC,kBAAkB;;;;IAIjD,UAAU,QAAQ,EAAE;QAChB,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjE,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACxD;QACD,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;KAC3C,CAAC;;;;;;;;IAQF,oBAAoB,CAAC,SAAS,CAAC,SAAS;;;;;;;IAOxC,UAAU,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,qBAAqB,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,qBAAqB,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACrC,CAAC;;;;;;;;IAQF,oBAAoB,CAAC,SAAS,CAAC,YAAY;;;;;;;IAO3C,UAAU,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,qBAAqB,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,qBAAqB,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;KACnD,CAAC;;;;;IAKF,oBAAoB,CAAC,SAAS,CAAC,UAAU;;;;IAIzC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;;;;IAIzD,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;IAG1C,YAAY,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;;;;IAI9C,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;IAGnC,YAAY;QACR,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACtB,qBAAqB,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC7G,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAClC;KACJ,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,OAAO;;;IAGtC,YAAY,EAAE,MAAM,iBAAiB,CAAC,EAAE,CAAC;IACzC,oBAAoB,CAAC,UAAU,GAAG;QAC9B,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,oBAAoB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACjE,OAAO,oBAAoB,CAAC;CAC/B,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACrB,AAuBA,IAAI,kBAAkB,kBAAkB,YAAY;IAChD,SAAS,kBAAkB,CAAC,MAAM,EAAE;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;KAC1B;IACD,OAAO,kBAAkB,CAAC;CAC7B,EAAE,CAAC;;AC3LJ;;;;;;;;;;GAUG;;ACVH;;;;;;;;;;;;;;;;AAgBA,AAAkE;0EACQ;;ACjB1E;;;;;;GAMG;;;;"}