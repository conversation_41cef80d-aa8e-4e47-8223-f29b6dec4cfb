{"version": 3, "file": "hu.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/hu.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,AAAD;QACpE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;KAC7E;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC3D;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['éjfél', 'dél', 'reggel', 'de.', 'du.', 'este', 'éjjel', 'hajnal'], ,\n    ['éjfél', 'dél', 'reggel', 'd<PERSON><PERSON><PERSON><PERSON><PERSON>', 'délut<PERSON>', 'este', 'éjjel', 'hajnal']\n  ],\n  ,\n  [\n    '00:00', '12:00', ['06:00', '09:00'], ['09:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '21:00'], ['21:00', '04:00'], ['04:00', '06:00']\n  ]\n];\n"]}