{"version": 3, "file": "switchMapTo.js", "sourceRoot": "", "sources": ["../../src/operators/switchMapTo.ts"], "names": [], "mappings": ";;;;;;AAIA,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAM9D,mCAAmC;AAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCG;AACH,qBAAqC,eAA8B,EAC9B,cAG0C;IAC7E,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAArE,CAAqE,CAAC;AAC1G,CAAC;AANe,mBAAW,cAM1B,CAAA;AAED;IACE,6BAAoB,UAAyB,EACzB,cAA4F;QAD5F,eAAU,GAAV,UAAU,CAAe;QACzB,mBAAc,GAAd,cAAc,CAA8E;IAChH,CAAC;IAED,kCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IACvG,CAAC;IACH,0BAAC;AAAD,CAAC,AARD,IAQC;AAED;;;;GAIG;AACH;IAA6C,yCAAqB;IAIhE,+BAAY,WAA0B,EAClB,KAAoB,EACpB,cAA4F;QAC9G,kBAAM,WAAW,CAAC,CAAC;QAFD,UAAK,GAAL,KAAK,CAAe;QACpB,mBAAc,GAAd,cAAc,CAA8E;QALxG,UAAK,GAAW,CAAC,CAAC;IAO1B,CAAC;IAES,qCAAK,GAAf,UAAgB,KAAU;QACxB,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACjD,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACtB,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,GAAG,qCAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC9F,CAAC;IAES,yCAAS,GAAnB;QACS,8CAAiB,CAAS;QACjC,EAAE,CAAC,CAAC,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;YACnD,gBAAK,CAAC,SAAS,WAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,oCAAoC,CAAC,4CAAY,GAAZ;QACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,8CAAc,GAAd,UAAe,QAAsB;QACnC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACnB,gBAAK,CAAC,SAAS,WAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,0CAAU,GAAV,UAAW,UAAa,EAAE,UAAa,EAC5B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACxC,IAAA,SAA4C,EAApC,kCAAc,EAAE,4BAAW,CAAU;QAC7C,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACzE,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,iDAAiB,GAAzB,UAA0B,UAAa,EAAE,UAAa,EAC5B,UAAkB,EAAE,UAAkB;QAC9D,IAAA,SAA4C,EAApC,kCAAc,EAAE,4BAAW,CAAU;QAC7C,IAAI,MAAS,CAAC;QACd,IAAI,CAAC;YACH,MAAM,GAAG,cAAc,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC1E,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAED,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IACH,4BAAC;AAAD,CAAC,AA7DD,CAA6C,iCAAe,GA6D3D", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Observable, ObservableInput } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OperatorFunction } from '../interfaces';\n\n/* tslint:disable:max-line-length */\nexport function switchMapTo<T, R>(observable: ObservableInput<R>): OperatorFunction<T, R>;\nexport function switchMapTo<T, I, R>(observable: ObservableInput<I>, resultSelector: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R): OperatorFunction<T, R>;\n/* tslint:enable:max-line-length */\n\n/**\n * Projects each source value to the same Observable which is flattened multiple\n * times with {@link switch} in the output Observable.\n *\n * <span class=\"informal\">It's like {@link switchMap}, but maps each value\n * always to the same inner Observable.</span>\n *\n * <img src=\"./img/switchMapTo.png\" width=\"100%\">\n *\n * Maps each source value to the given Observable `innerObservable` regardless\n * of the source value, and then flattens those resulting Observables into one\n * single Observable, which is the output Observable. The output Observables\n * emits values only from the most recently emitted instance of\n * `innerObservable`.\n *\n * @example <caption>Rerun an interval Observable on every click event</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var result = clicks.switchMapTo(Rx.Observable.interval(1000));\n * result.subscribe(x => console.log(x));\n *\n * @see {@link concatMapTo}\n * @see {@link switch}\n * @see {@link switchMap}\n * @see {@link mergeMapTo}\n *\n * @param {ObservableInput} innerObservable An Observable to replace each value from\n * the source Observable.\n * @param {function(outerValue: T, innerValue: I, outerIndex: number, innerIndex: number): any} [resultSelector]\n * A function to produce the value on the output Observable based on the values\n * and the indices of the source (outer) emission and the inner Observable\n * emission. The arguments passed to this function are:\n * - `outerValue`: the value that came from the source\n * - `innerValue`: the value that came from the projected Observable\n * - `outerIndex`: the \"index\" of the value that came from the source\n * - `innerIndex`: the \"index\" of the value from the projected Observable\n * @return {Observable} An Observable that emits items from the given\n * `innerObservable` (and optionally transformed through `resultSelector`) every\n * time a value is emitted on the source Observable, and taking only the values\n * from the most recently projected inner Observable.\n * @method switchMapTo\n * @owner Observable\n */\nexport function switchMapTo<T, I, R>(innerObservable: Observable<I>,\n                                     resultSelector?: (outerValue: T,\n                                                       innerValue: I,\n                                                       outerIndex: number,\n                                                       innerIndex: number) => R): OperatorFunction<T, I | R> {\n  return (source: Observable<T>) => source.lift(new SwitchMapToOperator(innerObservable, resultSelector));\n}\n\nclass SwitchMapToOperator<T, I, R> implements Operator<T, I> {\n  constructor(private observable: Observable<I>,\n              private resultSelector?: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R) {\n  }\n\n  call(subscriber: Subscriber<I>, source: any): any {\n    return source.subscribe(new SwitchMapToSubscriber(subscriber, this.observable, this.resultSelector));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass SwitchMapToSubscriber<T, I, R> extends OuterSubscriber<T, I> {\n  private index: number = 0;\n  private innerSubscription: Subscription;\n\n  constructor(destination: Subscriber<I>,\n              private inner: Observable<I>,\n              private resultSelector?: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R) {\n    super(destination);\n  }\n\n  protected _next(value: any) {\n    const innerSubscription = this.innerSubscription;\n    if (innerSubscription) {\n      innerSubscription.unsubscribe();\n    }\n    this.add(this.innerSubscription = subscribeToResult(this, this.inner, value, this.index++));\n  }\n\n  protected _complete() {\n    const {innerSubscription} = this;\n    if (!innerSubscription || innerSubscription.closed) {\n      super._complete();\n    }\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    this.innerSubscription = null;\n  }\n\n  notifyComplete(innerSub: Subscription) {\n    this.remove(innerSub);\n    this.innerSubscription = null;\n    if (this.isStopped) {\n      super._complete();\n    }\n  }\n\n  notifyNext(outerValue: T, innerValue: I,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, I>): void {\n    const { resultSelector, destination } = this;\n    if (resultSelector) {\n      this.tryResultSelector(outerValue, innerValue, outerIndex, innerIndex);\n    } else {\n      destination.next(innerValue);\n    }\n  }\n\n  private tryResultSelector(outerValue: T, innerValue: I,\n                            outerIndex: number, innerIndex: number): void {\n    const { resultSelector, destination } = this;\n    let result: R;\n    try {\n      result = resultSelector(outerValue, innerValue, outerIndex, innerIndex);\n    } catch (err) {\n      destination.error(err);\n      return;\n    }\n\n    destination.next(result);\n  }\n}\n"]}