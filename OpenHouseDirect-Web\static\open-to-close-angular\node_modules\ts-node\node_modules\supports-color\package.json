{"_from": "supports-color@^5.3.0", "_id": "supports-color@5.5.0", "_inBundle": false, "_integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "_location": "/ts-node/supports-color", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "supports-color@^5.3.0", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "^5.3.0", "saveSpec": null, "fetchSpec": "^5.3.0"}, "_requiredBy": ["/ts-node/chalk"], "_resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "_shasum": "e2e69a44ac8772f78a1ec0b35b689df6530efc8f", "_spec": "supports-color@^5.3.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\ts-node\\node_modules\\chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "bundleDependencies": false, "dependencies": {"has-flag": "^3.0.0"}, "deprecated": false, "description": "Detect whether a terminal supports color", "devDependencies": {"ava": "^0.25.0", "import-fresh": "^2.0.0", "xo": "^0.20.0"}, "engines": {"node": ">=4"}, "files": ["index.js", "browser.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "license": "MIT", "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "xo && ava"}, "version": "5.5.0"}