{"_from": "normalize-path@^2.0.0", "_id": "normalize-path@2.1.1", "_inBundle": false, "_integrity": "sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w==", "_location": "/normalize-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize-path@^2.0.0", "name": "normalize-path", "escapedName": "normalize-path", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/anymatch", "/micromatch"], "_resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "_shasum": "1ab28b556e198363a8c1a6f7e6fa20137fe6aed9", "_spec": "normalize-path@^2.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\anymatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/Blaine<PERSON><PERSON>litz"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"remove-trailing-separator": "^1.0.1"}, "deprecated": false, "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes unless disabled.", "devDependencies": {"benchmarked": "^0.1.1", "gulp-format-md": "^0.1.11", "minimist": "^1.2.0", "mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/normalize-path", "keywords": ["backslash", "file", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "license": "MIT", "main": "index.js", "name": "normalize-path", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["contains-path", "ends-with", "is-absolute", "is-relative", "parse-filepath", "path-ends-with", "path-segments", "rewrite-ext", "unixify"], "description": "Other useful libraries for working with paths in node.js:"}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "2.1.1"}