/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core")):"function"==typeof define&&define.amd?define(["exports","@angular/core"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.platform=e.ng.cdk.platform||{}),e.ng.core)}(this,function(e,t){"use strict";function r(){if(null==n&&"undefined"!=typeof window)try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){return n=!0}}))}finally{n=n||!1}return n}function i(){if(s)return s;if("object"!=typeof document||!document)return s=new Set(a);var e=document.createElement("input");return s=new Set(a.filter(function(t){return e.setAttribute("type",t),e.type===t}))}var n,s,o="undefined"!=typeof Intl&&Intl.v8BreakIterator,u=function(){function e(){this.isBrowser="object"==typeof document&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!(!window.chrome&&!o)&&!!CSS&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}return e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[]},e}(),a=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"],d=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{providers:[u]}]}],e.ctorParameters=function(){return[]},e}();e.Platform=u,e.supportsPassiveEventListeners=r,e.getSupportedInputTypes=i,e.PlatformModule=d,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-platform.umd.min.js.map
