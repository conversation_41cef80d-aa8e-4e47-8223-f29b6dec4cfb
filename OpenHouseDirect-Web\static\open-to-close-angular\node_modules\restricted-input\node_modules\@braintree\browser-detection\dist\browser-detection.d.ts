import isAndroid = require("./is-android");
import isChromeOS = require("./is-chrome-os");
import isChrome = require("./is-chrome");
import isDuckDuckGo = require("./is-duckduckgo");
import isIe = require("./is-ie");
import isIe9 = require("./is-ie9");
import isIe10 = require("./is-ie10");
import isIe11 = require("./is-ie11");
import isEdge = require("./is-edge");
import isFirefox = require("./is-firefox");
import isSafari = require("./is-safari");
import isIos = require("./is-ios");
import isIosFirefox = require("./is-ios-firefox");
import isIosGoogleSearchApp = require("./is-ios-google-search-app");
import isIosSafari = require("./is-ios-safari");
import isIosUIWebview = require("./is-ios-uiwebview");
import isIosWebview = require("./is-ios-webview");
import isIosWKWebview = require("./is-ios-wkwebview");
import isIpadOS = require("./is-ipados");
import isMobileFirefox = require("./is-mobile-firefox");
import isOpera = require("./is-opera");
import isSamsungBrowser = require("./is-samsung");
import isSilk = require("./is-silk");
import hasSoftwareKeyboard = require("./has-software-keyboard");
import supportsPopups = require("./supports-popups");
import supportsPaymentRequestApi = require("./supports-payment-request-api");
export { isAndroid, isChromeOS, isChrome, isDuckDuckGo, isIe, isIe9, isIe10, isIe11, isEdge, isFirefox, isIos, isIosFirefox, isIosGoogleSearchApp, isSafari, isIosSafari, isIosUIWebview, isIosWebview, isIosWKWebview, isIpadOS, isMobileFirefox, isOpera, isSamsungBrowser, isSilk, hasSoftwareKeyboard, supportsPopups, supportsPaymentRequestApi, };
