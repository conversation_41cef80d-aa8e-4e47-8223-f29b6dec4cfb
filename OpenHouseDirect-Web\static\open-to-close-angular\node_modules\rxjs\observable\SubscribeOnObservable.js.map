{"version": 3, "file": "SubscribeOnObservable.js", "sourceRoot": "", "sources": ["../../src/observable/SubscribeOnObservable.ts"], "names": [], "mappings": ";;;;;;AAIA,2BAA2B,eAAe,CAAC,CAAA;AAC3C,qBAAqB,mBAAmB,CAAC,CAAA;AACzC,0BAA0B,mBAAmB,CAAC,CAAA;AAO9C;;;;GAIG;AACH;IAA8C,yCAAa;IAUzD,+BAAmB,MAAqB,EACpB,SAAqB,EACrB,SAA4B;QADpC,yBAA6B,GAA7B,aAA6B;QAC7B,yBAAoC,GAApC,uBAAoC;QAC9C,iBAAO,CAAC;QAHS,WAAM,GAAN,MAAM,CAAe;QACpB,cAAS,GAAT,SAAS,CAAY;QACrB,cAAS,GAAT,SAAS,CAAmB;QAE9C,EAAE,CAAC,CAAC,CAAC,qBAAS,CAAC,SAAS,CAAC,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACrB,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,SAAS,GAAG,WAAI,CAAC;QACxB,CAAC;IACH,CAAC;IAnBM,4BAAM,GAAb,UAAiB,MAAqB,EAAE,KAAiB,EAAE,SAA4B;QAA/C,qBAAiB,GAAjB,SAAiB;QAAE,yBAA4B,GAA5B,uBAA4B;QACrF,MAAM,CAAC,IAAI,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAC7D,CAAC;IAEM,8BAAQ,GAAf,UAAoC,GAAmB;QAC7C,uBAAM,EAAE,2BAAU,CAAS;QACnC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;IAChD,CAAC;IAcD,oCAAoC,CAAC,0CAAU,GAAV,UAAW,UAAyB;QACvE,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;QAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,EAAE;YAC/D,cAAM,EAAE,sBAAU;SACnB,CAAC,CAAC;IACL,CAAC;IACH,4BAAC;AAAD,CAAC,AA/BD,CAA8C,uBAAU,GA+BvD;AA/BY,6BAAqB,wBA+BjC,CAAA", "sourcesContent": ["import { Action } from '../scheduler/Action';\nimport { IScheduler } from '../Scheduler';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { Observable } from '../Observable';\nimport { asap } from '../scheduler/asap';\nimport { isNumeric } from '../util/isNumeric';\n\nexport interface DispatchArg<T> {\n  source: Observable<T>;\n  subscriber: Subscriber<T>;\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class SubscribeOnObservable<T> extends Observable<T> {\n  static create<T>(source: Observable<T>, delay: number = 0, scheduler: IScheduler = asap): Observable<T> {\n    return new SubscribeOnObservable(source, delay, scheduler);\n  }\n\n  static dispatch<T>(this: Action<T>, arg: DispatchArg<T>): Subscription {\n    const { source, subscriber } = arg;\n    return this.add(source.subscribe(subscriber));\n  }\n\n  constructor(public source: Observable<T>,\n              private delayTime: number = 0,\n              private scheduler: IScheduler = asap) {\n    super();\n    if (!isNumeric(delayTime) || delayTime < 0) {\n      this.delayTime = 0;\n    }\n    if (!scheduler || typeof scheduler.schedule !== 'function') {\n      this.scheduler = asap;\n    }\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>) {\n    const delay = this.delayTime;\n    const source = this.source;\n    const scheduler = this.scheduler;\n\n    return scheduler.schedule(SubscribeOnObservable.dispatch, delay, {\n      source, subscriber\n    });\n  }\n}\n"]}