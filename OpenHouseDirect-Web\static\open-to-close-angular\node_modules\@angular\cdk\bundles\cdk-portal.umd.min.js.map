{"version": 3, "file": "cdk-portal.umd.min.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../src/cdk/portal/portal-errors.ts", "../../src/cdk/portal/portal.ts", "../../src/cdk/portal/dom-portal-outlet.ts", "../../src/cdk/portal/portal-directives.ts", "../../src/cdk/portal/portal-injector.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nexport function throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nexport function throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nexport function throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nexport function throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n              'a ComponentPortal or a TemplatePortal.');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nexport function throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nexport function throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n    TemplateRef,\n    ViewContainerRef,\n    ElementRef,\n    ComponentRef,\n    EmbeddedViewRef,\n    Injector\n} from '@angular/core';\nimport {\n    throwNullPortalOutletError,\n    throwPortalAlreadyAttachedError,\n    throwNoPortalAttachedError,\n    throwNullPortalError,\n    throwPortalOutletAlreadyDisposedError,\n    throwUnknownPortalTypeError\n} from './portal-errors';\n\n/** Interface that can be used to generically type a class. */\nexport interface ComponentType<T> {\n  new (...args: any[]): T;\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nexport abstract class Portal<T> {\n  private _attachedHost: PortalOutlet | null;\n\n  /** Attach this portal to a host. */\n  attach(host: PortalOutlet): T {\n    if (host == null) {\n      throwNullPortalOutletError();\n    }\n\n    if (host.hasAttached()) {\n      throwPortalAlreadyAttachedError();\n    }\n\n    this._attachedHost = host;\n    return <T> host.attach(this);\n  }\n\n  /** Detach this portal from its host */\n  detach(): void {\n    let host = this._attachedHost;\n\n    if (host == null) {\n      throwNoPortalAttachedError();\n    } else {\n      this._attachedHost = null;\n      host.detach();\n    }\n  }\n\n  /** Whether this portal is attached to a host. */\n  get isAttached(): boolean {\n    return this._attachedHost != null;\n  }\n\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host: PortalOutlet | null) {\n    this._attachedHost = host;\n  }\n}\n\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nexport class ComponentPortal<T> extends Portal<ComponentRef<T>> {\n  /** The type of the component that will be instantiated for attachment. */\n  component: ComponentType<T>;\n\n  /**\n   * [Optional] Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef?: ViewContainerRef | null;\n\n  /** [Optional] Injector used for the instantiation of the component. */\n  injector?: Injector | null;\n\n  constructor(\n      component: ComponentType<T>,\n      viewContainerRef?: ViewContainerRef | null,\n      injector?: Injector | null) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n  }\n}\n\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nexport class TemplatePortal<C = any> extends Portal<C> {\n  /** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef: TemplateRef<C>;\n\n  /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef: ViewContainerRef;\n\n  /** Contextual data to be passed in to the embedded view. */\n  context: C | undefined;\n\n  constructor(template: TemplateRef<C>, viewContainerRef: ViewContainerRef, context?: C) {\n    super();\n    this.templateRef = template;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n  }\n\n  get origin(): ElementRef {\n    return this.templateRef.elementRef;\n  }\n\n  /**\n   * Attach the the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host: PortalOutlet, context: C | undefined = this.context): C {\n    this.context = context;\n    return super.attach(host);\n  }\n\n  detach(): void {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n\n\n/** A `PortalOutlet` is an space that can contain a single `Portal`. */\nexport interface PortalOutlet {\n  /** Attaches a portal to this outlet. */\n  attach(portal: Portal<any>): any;\n\n  /** Detaches the currently attached portal from this outlet. */\n  detach(): any;\n\n  /** Performs cleanup before the outlet is destroyed. */\n  dispose(): void;\n\n  /** Whether there is currently a portal attached to this outlet. */\n  hasAttached(): boolean;\n}\n\n\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nexport abstract class BasePortalOutlet implements PortalOutlet {\n  /** The portal currently attached to the host. */\n  protected _attachedPortal: Portal<any> | null;\n\n  /** A function that will permanently dispose this host. */\n  private _disposeFn: (() => void) | null;\n\n  /** Whether this host has already been permanently disposed. */\n  private _isDisposed: boolean = false;\n\n  /** Whether this host has an attached portal. */\n  hasAttached(): boolean {\n    return !!this._attachedPortal;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /** Attaches a portal. */\n  attach(portal: Portal<any>): any {\n    if (!portal) {\n      throwNullPortalError();\n    }\n\n    if (this.hasAttached()) {\n      throwPortalAlreadyAttachedError();\n    }\n\n    if (this._isDisposed) {\n      throwPortalOutletAlreadyDisposedError();\n    }\n\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n    }\n\n    throwUnknownPortalTypeError();\n  }\n\n  abstract attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n\n  abstract attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C>;\n\n  /** Detaches a previously attached portal. */\n  detach(): void {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n\n    this._invokeDisposeFn();\n  }\n\n  /** Permanently dispose of this portal host. */\n  dispose(): void {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n\n  /** @docs-private */\n  setDisposeFn(fn: () => void) {\n    this._disposeFn = fn;\n  }\n\n  private _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentFactoryResolver,\n  ComponentRef,\n  EmbeddedViewRef,\n  ApplicationRef,\n  Injector,\n} from '@angular/core';\nimport {BasePortalOutlet, ComponentPortal, TemplatePortal} from './portal';\n\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nexport class DomPortalOutlet extends BasePortalOutlet {\n  constructor(\n      /** Element into which the content is projected. */\n      public outletElement: Element,\n      private _componentFactoryResolver: ComponentFactoryResolver,\n      private _appRef: ApplicationRef,\n      private _defaultInjector: Injector) {\n    super();\n  }\n\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    let componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n    let componentRef: ComponentRef<T>;\n\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(\n          componentFactory,\n          portal.viewContainerRef.length,\n          portal.injector || portal.viewContainerRef.parentInjector);\n\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      componentRef = componentFactory.create(portal.injector || this._defaultInjector);\n      this._appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        this._appRef.detachView(componentRef.hostView);\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n\n    return componentRef;\n  }\n\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context);\n    viewRef.detectChanges();\n\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n\n    this.setDisposeFn((() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    }));\n\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose(): void {\n    super.dispose();\n    if (this.outletElement.parentNode != null) {\n      this.outletElement.parentNode.removeChild(this.outletElement);\n    }\n  }\n\n  /** Gets the root HTMLElement for an instantiated component. */\n  private _getComponentRootNode(componentRef: ComponentRef<any>): HTMLElement {\n    return (componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  NgModule,\n  ComponentRef,\n  Directive,\n  EmbeddedViewRef,\n  TemplateRef,\n  ComponentFactoryResolver,\n  ViewContainerRef,\n  OnDestroy,\n  OnInit,\n  Input,\n  EventEmitter,\n  Output,\n} from '@angular/core';\nimport {Portal, TemplatePortal, ComponentPortal, BasePortalOutlet} from './portal';\n\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\n@Directive({\n  selector: '[cdk-portal], [cdkPortal], [portal]',\n  exportAs: 'cdkPortal',\n})\nexport class CdkPortal extends TemplatePortal {\n  constructor(templateRef: TemplateRef<any>, viewContainerRef: ViewContainerRef) {\n    super(templateRef, viewContainerRef);\n  }\n}\n\n/**\n * Possible attached references to the CdkPortalOutlet.\n */\nexport type CdkPortalOutletAttachedRef = ComponentRef<any> | EmbeddedViewRef<any> | null;\n\n\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\n@Directive({\n  selector: '[cdkPortalOutlet], [cdkPortalHost], [portalHost]',\n  exportAs: 'cdkPortalOutlet, cdkPortalHost',\n  inputs: ['portal: cdkPortalOutlet']\n})\nexport class CdkPortalOutlet extends BasePortalOutlet implements OnInit, OnDestroy {\n  /** Whether the portal component is initialized. */\n  private _isInitialized = false;\n\n  /** Reference to the currently-attached component/view ref. */\n  private _attachedRef: CdkPortalOutletAttachedRef;\n\n  constructor(\n      private _componentFactoryResolver: ComponentFactoryResolver,\n      private _viewContainerRef: ViewContainerRef) {\n    super();\n  }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('portalHost')\n  get _deprecatedPortal() { return this.portal; }\n  set _deprecatedPortal(v) { this.portal = v; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('cdkPortalHost')\n  get _deprecatedPortalHost() { return this.portal; }\n  set _deprecatedPortalHost(v) { this.portal = v; }\n\n  /** Portal associated with the Portal outlet. */\n  get portal(): Portal<any> | null {\n    return this._attachedPortal;\n  }\n\n  set portal(portal: Portal<any> | null) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n\n    if (this.hasAttached()) {\n      super.detach();\n    }\n\n    if (portal) {\n      super.attach(portal);\n    }\n\n    this._attachedPortal = portal;\n  }\n\n  @Output('attached') attached: EventEmitter<CdkPortalOutletAttachedRef> =\n      new EventEmitter<CdkPortalOutletAttachedRef>();\n\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef(): CdkPortalOutletAttachedRef {\n    return this._attachedRef;\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedPortal = null;\n    this._attachedRef = null;\n  }\n\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    portal.setAttachedHost(this);\n\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ?\n        portal.viewContainerRef :\n        this._viewContainerRef;\n\n    const componentFactory =\n        this._componentFactoryResolver.resolveComponentFactory(portal.component);\n    const ref = viewContainerRef.createComponent(\n        componentFactory, viewContainerRef.length,\n        portal.injector || viewContainerRef.parentInjector);\n\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n\n    return ref;\n  }\n\n  /**\n   * Attach the given TemplatePortal to this PortlHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context);\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n\n    return viewRef;\n  }\n}\n\n\n@NgModule({\n  exports: [CdkPortal, CdkPortalOutlet],\n  declarations: [CdkPortal, CdkPortalOutlet],\n})\nexport class PortalModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injector} from '@angular/core';\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n */\nexport class PortalInjector implements Injector {\n  constructor(\n    private _parentInjector: Injector,\n    private _customTokens: WeakMap<any, any>) { }\n\n  get(token: any, notFoundValue?: any): any {\n    const value = this._customTokens.get(token);\n\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n\n    return this._parentInjector.get<any>(token, notFoundValue);\n  }\n}\n"], "names": ["__extends", "d", "b", "__", "this", "constructor", "extendStatics", "prototype", "Object", "create", "throwNullPortalError", "Error", "throwPortalAlreadyAttachedError", "throwPortalOutletAlreadyDisposedError", "throwUnknownPortalTypeError", "throwNullPortalOutletError", "throwNoPortalAttachedError", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "Portal", "attach", "host", "has<PERSON>tta<PERSON>", "_attachedHost", "detach", "defineProperty", "setAttachedHost", "ComponentPortal", "_super", "component", "viewContainerRef", "injector", "_this", "call", "tslib_1.__extends", "TemplatePortal", "template", "context", "templateRef", "elementRef", "undefined", "BasePortalOutlet", "_isDisposed", "_attachedPortal", "portal", "attachComponentPortal", "attachTemplatePortal", "_invokeDisposeFn", "dispose", "setDisposeFn", "fn", "_disposeFn", "DomPortalOutlet", "outletElement", "_componentFactoryResolver", "_appRef", "_defaultInjector", "componentRef", "componentFactory", "resolveComponentFactory", "createComponent", "length", "parentInjector", "destroy", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "detach<PERSON>iew", "append<PERSON><PERSON><PERSON>", "_getComponentRootNode", "viewContainer", "viewRef", "createEmbeddedView", "detectChanges", "rootNodes", "for<PERSON>ach", "rootNode", "index", "indexOf", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "CdkPortal", "type", "Directive", "args", "selector", "exportAs", "TemplateRef", "ViewContainerRef", "CdkPortalOutlet", "_viewContainerRef", "_isInitialized", "attached", "EventEmitter", "v", "_attachedRef", "ngOnInit", "ngOnDestroy", "ref", "emit", "clear", "inputs", "ComponentFactoryResolver", "_deprecatedPortal", "Input", "_deprecatedPortalHost", "Output", "PortalModule", "NgModule", "exports", "declarations", "PortalInjector", "_parentInjector", "_customTokens", "get", "token", "notFoundValue", "value"], "mappings": ";;;;;;;2SAoBA,SAAgBA,GAAUC,EAAGC,GAEzB,QAASC,KAAOC,KAAKC,YAAcJ,EADnCK,EAAcL,EAAGC,GAEjBD,EAAEM,UAAkB,OAANL,EAAaM,OAAOC,OAAOP,IAAMC,EAAGI,UAAYL,EAAEK,UAAW,GAAIJ,ICXnF,QAAAO,KACE,KAAMC,OAAM,mCAOd,QAAAC,KACE,KAAMD,OAAM,sCAOd,QAAAE,KACE,KAAMF,OAAM,+CAOd,QAAAG,KACE,KAAMH,OAAM,uHAQd,QAAAI,KACE,KAAMJ,OAAM,wDAOd,QAAAK,KACE,KAAML,OAAM,gEDtCd,GAAIL,GAAgBE,OAAOS,iBACpBC,uBAA2BC,QAAS,SAAUlB,EAAGC,GAAKD,EAAEiB,UAAYhB,IACvE,SAAUD,EAAGC,GAAK,IAAK,GAAIkB,KAAKlB,GAAOA,EAAEmB,eAAeD,KAAInB,EAAEmB,GAAKlB,EAAEkB,KEgBzEE,EAAA,yBAlCA,MAsCEA,GAAFf,UAAAgB,OAAE,SAAOC,GAUL,MATY,OAARA,GACFT,IAGES,EAAKC,eACPb,IAGFR,KAAKsB,cAAgBF,EACVA,EAAKD,OAAOnB,OAIzBkB,EAAFf,UAAAoB,OAAE,WACE,GAAIH,GAAOpB,KAAKsB,aAEJ,OAARF,EACFR,KAEAZ,KAAKsB,cAAgB,KACrBF,EAAKG,WAKTnB,OAAFoB,eAAMN,EAANf,UAAA,kBAAE,WACE,MAA6B,OAAtBH,KAAKsB,+CAOdJ,EAAFf,UAAAsB,gBAAE,SAAgBL,GACdpB,KAAKsB,cAAgBF,GAzEzBF,KAiFAQ,EAAA,SAAAC,GAcE,QAAFD,GACME,EACAC,EACAC,GAHJ,GAAFC,GAIIJ,EAJJK,KAAAhC,OAAAA,WAKI+B,GAAKH,UAAYA,EACjBG,EAAKF,iBAAmBA,EACxBE,EAAKD,SAAWA,IAtGpB,MAiFwCG,GAAxCP,EAAAC,GAjFAD,GAiFwCR,GA4BxCgB,EAAA,SAAAP,GAUE,QAAFO,GAAcC,EAA0BN,EAAoCO,GAA1E,GAAFL,GACIJ,EADJK,KAAAhC,OAAAA,WAEI+B,GAAKM,YAAcF,EACnBJ,EAAKF,iBAAmBA,EACxBE,EAAKK,QAAUA,IA3HnB,MA6G6CH,GAA7CC,EAAAP,GAiBEvB,OAAFoB,eAAMU,EAAN/B,UAAA,cAAE,WACE,MAAOH,MAAKqC,YAAYC,4CAQ1BJ,EAAF/B,UAAAgB,OAAE,SAAOC,EAAoBgB,GAEzB,WAFJ,KAAAA,IAA6BA,EAAyBpC,KAAKoC,SACvDpC,KAAKoC,QAAUA,EACRT,EAAXxB,UAAiBgB,OAAjBa,KAAAhC,KAAwBoB,IAGtBc,EAAF/B,UAAAoB,OAAE,WAEE,MADAvB,MAAKoC,YAAUG,GACRZ,EAAXxB,UAAiBoB,OAAjBS,KAAAhC,OA9IAkC,GA6G6ChB,GA0D7CsB,EAAA,wBAQAxC,KAAAyC,aAAiC,EA/KjC,MAkLED,GAAFrC,UAAAkB,YAAE,WACE,QAASrB,KAAK0C,iBAQhBF,EAAFrC,UAAAgB,OAAE,SAAOwB,GAaL,MAZKA,IACHrC,IAGEN,KAAKqB,eACPb,IAGER,KAAKyC,aACPhC,IAGEkC,YAAkBjB,IACpB1B,KAAK0C,gBAAkBC,EAChB3C,KAAK4C,sBAAsBD,IACzBA,YAAkBT,IAC3BlC,KAAK0C,gBAAkBC,EAChB3C,KAAK6C,qBAAqBF,QAGnCjC,MAQF8B,EAAFrC,UAAAoB,OAAE,WACMvB,KAAK0C,kBACP1C,KAAK0C,gBAAgBjB,gBAAgB,MACrCzB,KAAK0C,gBAAkB,MAGzB1C,KAAK8C,oBAIPN,EAAFrC,UAAA4C,QAAE,WACM/C,KAAKqB,eACPrB,KAAKuB,SAGPvB,KAAK8C,mBACL9C,KAAKyC,aAAc,GAIrBD,EAAFrC,UAAA6C,aAAE,SAAaC,GACXjD,KAAKkD,WAAaD,GAGZT,EAAVrC,UAAA2C,4BACQ9C,KAAKkD,aACPlD,KAAKkD,aACLlD,KAAKkD,WAAa,OAnPxBV,KCsBAW,EAAA,SAAAxB,GACE,QAAFwB,GAEaC,EACCC,EACAC,EACAC,GALZ,GAAFxB,GAMIJ,EANJK,KAAAhC,OAAAA,WAEa+B,GAAbqB,cAAaA,EACCrB,EAAdsB,0BAAcA,EACAtB,EAAduB,QAAcA,EACAvB,EAAdwB,iBAAcA,IA5Bd,MAsBqCtB,GAArCkB,EAAAxB,GAeEwB,EAAFhD,UAAAyC,sBAAE,SAAyBD,GAAzB,GAEMa,GAFRzB,EAAA/B,KACQyD,EAAmBzD,KAAKqD,0BAA0BK,wBAAwBf,EAAOf,UA0BrF,OAnBIe,GAAOd,kBACT2B,EAAeb,EAAOd,iBAAiB8B,gBACnCF,EACAd,EAAOd,iBAAiB+B,OACxBjB,EAAOb,UAAYa,EAAOd,iBAAiBgC,gBAE/C7D,KAAKgD,aAAa,WAAM,MAAAQ,GAAaM,cAErCN,EAAeC,EAAiBpD,OAAOsC,EAAOb,UAAY9B,KAAKuD,kBAC/DvD,KAAKsD,QAAQS,WAAWP,EAAaQ,UACrChE,KAAKgD,aAAa,WAChBjB,EAAKuB,QAAQW,WAAWT,EAAaQ,UACrCR,EAAaM,aAKjB9D,KAAKoD,cAAcc,YAAYlE,KAAKmE,sBAAsBX,IAEnDA,GAQTL,EAAFhD,UAAA0C,qBAAE,SAAwBF,GAAxB,GAAFZ,GAAA/B,KACQoE,EAAgBzB,EAAOd,iBACvBwC,EAAUD,EAAcE,mBAAmB3B,EAAON,YAAaM,EAAOP,QAiB1E,OAhBAiC,GAAQE,gBAMRF,EAAQG,UAAUC,QAAQ,SAAAC,GAAY,MAAA3C,GAAKqB,cAAcc,YAAYQ,KAErE1E,KAAKgD,aAAY,WACf,GAAI2B,GAAQP,EAAcQ,QAAQP,IACnB,IAAXM,GACFP,EAAcS,OAAOF,KAKlBN,GAMTlB,EAAFhD,UAAA4C,QAAE,WACEpB,EAAJxB,UAAU4C,QAAVf,KAAAhC,MACyC,MAAjCA,KAAKoD,cAAc0B,YACrB9E,KAAKoD,cAAc0B,WAAWC,YAAY/E,KAAKoD,gBAK3CD,EAAVhD,UAAAgE,sBAAA,SAAgCX,GAC5B,MAAQA,GAA6C,SAAEgB,UAAU,IA1GrErB,GAsBqCX,iBCYnC,QAAFwC,GAAc3C,EAA+BR,GAC7C,MAAIF,GAAJK,KAAAhC,KAAUqC,EAAaR,IAAvB7B,KAnCA,MAiC+BiC,GAA/B+C,EAAArD,kBAJAsD,KAACC,EAAAA,UAADC,OACEC,SAAU,sCACVC,SAAU,oDAlBZJ,KAAEK,EAAAA,cAEFL,KAAEM,EAAAA,oBAfFP,GAiC+B9C,iBA+B7B,QAAFsD,GACcnC,EACAoC,GAFZ,GAAF1D,GAGIJ,EAHJK,KAAAhC,OAAAA,WACc+B,GAAdsB,0BAAcA,EACAtB,EAAd0D,kBAAcA,EAPd1D,EAAA2D,gBAA2B,EAqD3B3D,EAAA4D,SAAM,GAAIC,GAAAA,eAhHV,MAyDqC3D,GAArCuD,EAAA7D,GAkBAvB,OAAAoB,eAAMgE,EAANrF,UAAA,yBAAA,WAA4B,MAAOH,MAAK2C,YACtC,SAAsBkD,GAAK7F,KAAK2C,OAASkD,mCAO3CzF,OAAAoB,eAAMgE,EAANrF,UAAA,6BAAA,WAAgC,MAAOH,MAAK2C,YAC1C,SAA0BkD,GAAK7F,KAAK2C,OAASkD,mCAG7CzF,OAAFoB,eAAMgE,EAANrF,UAAA,cAAE,WACE,MAAOH,MAAK0C,qBAGd,SAAWC,KAKL3C,KAAKqB,eAAkBsB,GAAW3C,KAAK0F,kBAIvC1F,KAAKqB,eACPM,EAANxB,UAAYoB,OAAZS,KAAAhC,MAGQ2C,GACFhB,EAANxB,UAAYgB,OAAZa,KAAAhC,KAAmB2C,GAGf3C,KAAK0C,gBAAkBC,oCAOzBvC,OAAFoB,eAAMgE,EAANrF,UAAA,mBAAE,WACE,MAAOH,MAAK8F,8CAGdN,EAAFrF,UAAA4F,SAAE,WACE/F,KAAK0F,gBAAiB,GAGxBF,EAAFrF,UAAA6F,YAAE,WACErE,EAAJxB,UAAU4C,QAAVf,KAAAhC,MACIA,KAAK0C,gBAAkB,KACvB1C,KAAK8F,aAAe,MAStBN,EAAFrF,UAAAyC,sBAAE,SAAyBD,GACvBA,EAAOlB,gBAAgBzB,KAIvB,IAAM6B,GAA8C,MAA3Bc,EAAOd,iBAC5Bc,EAAOd,iBACP7B,KAAKyF,kBAEHhC,EACFzD,KAAKqD,0BAA0BK,wBAAwBf,EAAOf,WAC5DqE,EAAMpE,EAAiB8B,gBACzBF,EAAkB5B,EAAiB+B,OACnCjB,EAAOb,UAAYD,EAAiBgC,eAOxC,OALAlC,GAAJxB,UAAU6C,aAAVhB,KAAAhC,KAAuB,WAAM,MAAAiG,GAAInC,YAC7B9D,KAAK0C,gBAAkBC,EACvB3C,KAAK8F,aAAeG,EACpBjG,KAAK2F,SAASO,KAAKD,GAEZA,GAQTT,EAAFrF,UAAA0C,qBAAE,SAAwBF,GAAxB,GAAFZ,GAAA/B,IACI2C,GAAOlB,gBAAgBzB,KACvB,IAAMqE,GAAUrE,KAAKyF,kBAAkBnB,mBAAmB3B,EAAON,YAAaM,EAAOP,QAOrF,OANAT,GAAJxB,UAAU6C,aAAVhB,KAAAhC,KAAuB,WAAM,MAAA+B,GAAK0D,kBAAkBU,UAEhDnG,KAAK0C,gBAAkBC,EACvB3C,KAAK8F,aAAezB,EACpBrE,KAAK2F,SAASO,KAAK7B,GAEZA,kBAxHXY,KAACC,EAAAA,UAADC,OACEC,SAAU,mDACVC,SAAU,iCACVe,QAAS,mEAzCXnB,KAAEoB,EAAAA,2BACFpB,KAAEM,EAAAA,sCA2DFe,oBAAArB,KAAGsB,EAAAA,MAAHpB,MAAS,gBAQTqB,wBAAAvB,KAAGsB,EAAAA,MAAHpB,MAAS,mBA6BTQ,WAAAV,KAAGwB,EAAAA,OAAHtB,MAAU,eA/GVK,GAyDqChD,GAArCkE,EAAA,yBAzDA,sBAiLAzB,KAAC0B,EAAAA,SAADxB,OACEyB,SAAU5B,EAAWQ,GACrBqB,cAAe7B,EAAWQ,6CAnL5BkB,KCeAI,EAAA,WACE,QAAFA,GACYC,EACAC,GADAhH,KAAZ+G,gBAAYA,EACA/G,KAAZgH,cAAYA,EAlBZ,MAoBEF,GAAF3G,UAAA8G,IAAE,SAAIC,EAAYC,GACd,GAAMC,GAAQpH,KAAKgH,cAAcC,IAAIC,EAErC,YAAqB,KAAVE,EACFA,EAGFpH,KAAK+G,gBAAgBE,IAASC,EAAOC,IA3BhDL"}