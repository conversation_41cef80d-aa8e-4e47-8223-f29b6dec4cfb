
2.1.2 / 2014-06-02
==================

 * fix: make this an optimizable function with crankshaft

2.1.1 / 2014-04-17
==================

 * remove memoization

2.1.0 / 2014-04-17
==================

 * add catching of thunk errors

2.0.0 / 2014-04-17
==================

 * change to behave like a future (subsequent calls yield the same value)
 * remove support for eager execution (breaking change)

1.0.0 / 2014-04-11
==================

 * add assert(fn)
