{"version": 3, "file": "cdk-stepper.umd.min.js", "sources": ["../../src/cdk/stepper/step-label.ts", "../../src/cdk/stepper/stepper.ts", "../../src/cdk/stepper/stepper-button.ts", "../../src/cdk/stepper/stepper-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, TemplateRef} from '@angular/core';\n\n@Directive({\n  selector: '[cdkStepLabel]',\n})\nexport class CdkStepLabel {\n  constructor(/** @docs-private */ public template: TemplateRef<any>) { }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ContentChildren,\n  EventEmitter,\n  Input,\n  Output,\n  QueryList,\n  Directive,\n  ElementRef,\n  Component,\n  ContentChild,\n  ViewChild,\n  TemplateRef,\n  ViewEncapsulation,\n  Optional,\n  Inject,\n  forwardRef,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  OnChanges,\n  OnDestroy\n} from '@angular/core';\nimport {\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  DOWN_ARROW,\n  UP_ARROW,\n  ENTER,\n  SPACE,\n  HOME,\n  END,\n} from '@angular/cdk/keycodes';\nimport {CdkStepLabel} from './step-label';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {AbstractControl} from '@angular/forms';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {Subject} from 'rxjs/Subject';\n\n/** Used to generate unique ID for each stepper component. */\nlet nextId = 0;\n\n/**\n * Position state of the content of each step in stepper that is used for transitioning\n * the content into correct position upon step selection change.\n */\nexport type StepContentPositionState = 'previous' | 'current' | 'next';\n\n/** Possible orientation of a stepper. */\nexport type StepperOrientation = 'horizontal' | 'vertical';\n\n/** Change event emitted on selection changes. */\nexport class StepperSelectionEvent {\n  /** Index of the step now selected. */\n  selectedIndex: number;\n\n  /** Index of the step previously selected. */\n  previouslySelectedIndex: number;\n\n  /** The step instance now selected. */\n  selectedStep: CdkStep;\n\n  /** The step instance previously selected. */\n  previouslySelectedStep: CdkStep;\n}\n\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-step',\n  exportAs: 'cdkStep',\n  templateUrl: 'step.html',\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class CdkStep implements OnChanges {\n  /** Template for step label if it exists. */\n  @ContentChild(CdkStepLabel) stepLabel: CdkStepLabel;\n\n  /** Template for step content. */\n  @ViewChild(TemplateRef) content: TemplateRef<any>;\n\n  /** The top level abstract control of the step. */\n  @Input() stepControl: AbstractControl;\n\n  /** Whether user has seen the expanded step content or not. */\n  interacted = false;\n\n  /** Label of the step. */\n  @Input() label: string;\n\n  /** Whether the user can return to this step once it has been marked as complted. */\n  @Input()\n  get editable(): boolean { return this._editable; }\n  set editable(value: boolean) {\n    this._editable = coerceBooleanProperty(value);\n  }\n  private _editable = true;\n\n  /** Whether the completion of step is optional. */\n  @Input()\n  get optional(): boolean { return this._optional; }\n  set optional(value: boolean) {\n    this._optional = coerceBooleanProperty(value);\n  }\n  private _optional = false;\n\n  /** Whether step is marked as completed. */\n  @Input()\n  get completed(): boolean {\n    return this._customCompleted == null ? this._defaultCompleted : this._customCompleted;\n  }\n  set completed(value: boolean) {\n    this._customCompleted = coerceBooleanProperty(value);\n  }\n  private _customCompleted: boolean | null = null;\n\n  private get _defaultCompleted() {\n    return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n  }\n\n  constructor(@Inject(forwardRef(() => CdkStepper)) private _stepper: CdkStepper) { }\n\n  /** Selects this step component. */\n  select(): void {\n    this._stepper.selected = this;\n  }\n\n  /** Resets the step to its initial state. Note that this includes resetting form data. */\n  reset(): void {\n    this.interacted = false;\n\n    if (this._customCompleted != null) {\n      this._customCompleted = false;\n    }\n\n    if (this.stepControl) {\n      this.stepControl.reset();\n    }\n  }\n\n  ngOnChanges() {\n    // Since basically all inputs of the MatStep get proxied through the view down to the\n    // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n    this._stepper._stateChanged();\n  }\n}\n\n@Directive({\n  selector: '[cdkStepper]',\n  exportAs: 'cdkStepper',\n})\nexport class CdkStepper implements OnDestroy {\n  /** Emits when the component is destroyed. */\n  protected _destroyed = new Subject<void>();\n\n  /** The list of step components that the stepper is holding. */\n  @ContentChildren(CdkStep) _steps: QueryList<CdkStep>;\n\n  /** The list of step headers of the steps in the stepper. */\n  _stepHeader: QueryList<ElementRef>;\n\n  /** Whether the validity of previous steps should be checked or not. */\n  @Input()\n  get linear(): boolean { return this._linear; }\n  set linear(value: boolean) { this._linear = coerceBooleanProperty(value); }\n  private _linear = false;\n\n  /** The index of the selected step. */\n  @Input()\n  get selectedIndex() { return this._selectedIndex; }\n  set selectedIndex(index: number) {\n    if (this._steps) {\n      // Ensure that the index can't be out of bounds.\n      if (index < 0 || index > this._steps.length - 1) {\n        throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n      }\n\n      if (this._anyControlsInvalidOrPending(index) || index < this._selectedIndex &&\n          !this._steps.toArray()[index].editable) {\n        // remove focus from clicked step header if the step is not able to be selected\n        this._stepHeader.toArray()[index].nativeElement.blur();\n      } else if (this._selectedIndex != index) {\n        this._emitStepperSelectionEvent(index);\n        this._focusIndex = this._selectedIndex;\n      }\n    } else {\n      this._selectedIndex = this._focusIndex = index;\n    }\n  }\n  private _selectedIndex = 0;\n\n  /** The step that is selected. */\n  @Input()\n  get selected(): CdkStep { return this._steps.toArray()[this.selectedIndex]; }\n  set selected(step: CdkStep) {\n    this.selectedIndex = this._steps.toArray().indexOf(step);\n  }\n\n  /** Event emitted when the selected step has changed. */\n  @Output() selectionChange: EventEmitter<StepperSelectionEvent>\n      = new EventEmitter<StepperSelectionEvent>();\n\n  /** The index of the step that the focus can be set. */\n  _focusIndex: number = 0;\n\n  /** Used to track unique ID for each stepper component. */\n  _groupId: number;\n\n  protected _orientation: StepperOrientation = 'horizontal';\n\n  constructor(\n    @Optional() private _dir: Directionality,\n    private _changeDetectorRef: ChangeDetectorRef) {\n    this._groupId = nextId++;\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Selects and focuses the next step in list. */\n  next(): void {\n    this.selectedIndex = Math.min(this._selectedIndex + 1, this._steps.length - 1);\n  }\n\n  /** Selects and focuses the previous step in list. */\n  previous(): void {\n    this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n  }\n\n  /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n  reset(): void {\n    this.selectedIndex = 0;\n    this._steps.forEach(step => step.reset());\n    this._stateChanged();\n  }\n\n  /** Returns a unique id for each step label element. */\n  _getStepLabelId(i: number): string {\n    return `cdk-step-label-${this._groupId}-${i}`;\n  }\n\n  /** Returns unique id for each step content element. */\n  _getStepContentId(i: number): string {\n    return `cdk-step-content-${this._groupId}-${i}`;\n  }\n\n  /** Marks the component to be change detected. */\n  _stateChanged() {\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Returns position state of the step with the given index. */\n  _getAnimationDirection(index: number): StepContentPositionState {\n    const position = index - this._selectedIndex;\n    if (position < 0) {\n      return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n    } else if (position > 0) {\n      return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n    }\n    return 'current';\n  }\n\n  /** Returns the type of icon to be displayed. */\n  _getIndicatorType(index: number): 'number' | 'edit' | 'done' {\n    const step = this._steps.toArray()[index];\n    if (!step.completed || this._selectedIndex == index) {\n      return 'number';\n    } else {\n      return step.editable ? 'edit' : 'done';\n    }\n  }\n\n  private _emitStepperSelectionEvent(newIndex: number): void {\n    const stepsArray = this._steps.toArray();\n    this.selectionChange.emit({\n      selectedIndex: newIndex,\n      previouslySelectedIndex: this._selectedIndex,\n      selectedStep: stepsArray[newIndex],\n      previouslySelectedStep: stepsArray[this._selectedIndex],\n    });\n    this._selectedIndex = newIndex;\n    this._stateChanged();\n  }\n\n  _onKeydown(event: KeyboardEvent) {\n    const keyCode = event.keyCode;\n\n    // Note that the left/right arrows work both in vertical and horizontal mode.\n    if (keyCode === RIGHT_ARROW) {\n      this._layoutDirection() === 'rtl' ? this._focusPreviousStep() : this._focusNextStep();\n      event.preventDefault();\n    }\n\n    if (keyCode === LEFT_ARROW) {\n      this._layoutDirection() === 'rtl' ? this._focusNextStep() : this._focusPreviousStep();\n      event.preventDefault();\n    }\n\n    // Note that the up/down arrows only work in vertical mode.\n    // See: https://www.w3.org/TR/wai-aria-practices-1.1/#tabpanel\n    if (this._orientation === 'vertical' && (keyCode === UP_ARROW || keyCode === DOWN_ARROW)) {\n      keyCode === UP_ARROW ? this._focusPreviousStep() : this._focusNextStep();\n      event.preventDefault();\n    }\n\n    if (keyCode === SPACE || keyCode === ENTER) {\n      this.selectedIndex = this._focusIndex;\n      event.preventDefault();\n    }\n\n    if (keyCode === HOME) {\n      this._focusStep(0);\n      event.preventDefault();\n    }\n\n    if (keyCode === END) {\n      this._focusStep(this._steps.length - 1);\n      event.preventDefault();\n    }\n  }\n\n  private _focusNextStep() {\n    this._focusStep((this._focusIndex + 1) % this._steps.length);\n  }\n\n  private _focusPreviousStep() {\n    this._focusStep((this._focusIndex + this._steps.length - 1) % this._steps.length);\n  }\n\n  private _focusStep(index: number) {\n    this._focusIndex = index;\n    this._stepHeader.toArray()[this._focusIndex].nativeElement.focus();\n  }\n\n  private _anyControlsInvalidOrPending(index: number): boolean {\n    const steps = this._steps.toArray();\n\n    steps[this._selectedIndex].interacted = true;\n\n    if (this._linear && index >= 0) {\n      return steps.slice(0, index).some(step => {\n        const control = step.stepControl;\n        const isIncomplete = control ?\n            (control.invalid || control.pending || !step.interacted) :\n            !step.completed;\n        return isIncomplete && !step.optional;\n      });\n    }\n\n    return false;\n  }\n\n  private _layoutDirection(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input} from '@angular/core';\nimport {CdkStepper} from './stepper';\n\n/** But<PERSON> that moves to the next step in a stepper workflow. */\n@Directive({\n  selector: 'button[cdkStepperNext]',\n  host: {\n    '(click)': '_stepper.next()',\n    '[type]': 'type',\n  }\n})\nexport class CdkStepperNext {\n  /** Type of the next button. Defaults to \"submit\" if not specified. */\n  @Input() type: string = 'submit';\n\n  constructor(public _stepper: CdkStepper) {}\n}\n\n/** But<PERSON> that moves to the previous step in a stepper workflow. */\n@Directive({\n  selector: 'button[cdkStepperPrevious]',\n  host: {\n    '(click)': '_stepper.previous()',\n    '[type]': 'type',\n  }\n})\nexport class CdkStepperPrevious {\n  /** Type of the previous button. Defaults to \"button\" if not specified. */\n  @Input() type: string = 'button';\n\n  constructor(public _stepper: CdkStepper) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkStepper, CdkStep} from './stepper';\nimport {CommonModule} from '@angular/common';\nimport {CdkStepLabel} from './step-label';\nimport {CdkStepperNext, CdkStepperPrevious} from './stepper-button';\nimport {BidiModule} from '@angular/cdk/bidi';\n\n@NgModule({\n  imports: [BidiModule, CommonModule],\n  exports: [CdkStep, CdkStepper, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n  declarations: [CdkStep, CdkStepper, CdkStepLabel, CdkStepperNext, CdkStepperPrevious]\n})\nexport class CdkStepperModule {}\n"], "names": ["CdkStepLabel", "template", "this", "type", "Directive", "args", "selector", "TemplateRef", "nextId", "StepperSelectionEvent", "CdkStep", "_stepper", "interacted", "_editable", "_optional", "_customCompleted", "Object", "defineProperty", "prototype", "value", "coerceBooleanProperty", "_defaultCompleted", "stepControl", "valid", "select", "selected", "reset", "ngOnChanges", "_stateChanged", "Component", "exportAs", "encapsulation", "ViewEncapsulation", "None", "preserveWhitespaces", "changeDetection", "ChangeDetectionStrategy", "OnPush", "propDecorators", "Input", "label", "editable", "optional", "completed", "CdkStepper", "_changeDetectorRef", "_selectedIndex", "_groupId", "get", "configurable", "index", "_steps", "length", "_anyControlsInvalidOrPending", "toArray", "_step<PERSON><PERSON>er", "nativeElement", "blur", "_emitStepperSelectionEvent", "_focusIndex", "enumerable", "_destroyed", "complete", "for<PERSON>ach", "step", "position", "_layoutDirection", "selectionChange", "emit", "selectedIndex", "newIndex", "previouslySelectedIndex", "selectedStep", "stepsArray", "previouslySelectedStep", "keyCode", "RIGHT_ARROW", "event", "preventDefault", "LEFT_ARROW", "_focusNextStep", "_focusPreviousStep", "SPACE", "ENTER", "HOME", "_focusStep", "END", "focus", "steps", "_linear", "slice", "some", "control", "invalid", "pending", "decorators", "Output", "CdkStepperNext", "host", "(click)", "[type]", "CdkStepperPrevious", "CdkStepperModule", "NgModule", "imports", "BidiModule", "CommonModule", "exports", "declarations"], "mappings": ";;;;;;;yqBAQA,IAAAA,GAAA,WAME,QAAFA,GAA0CC,GAAAC,KAA1CD,SAA0CA,EAd1C,sBAUAE,KAACC,EAAAA,UAADC,OACEC,SAAU,yDAHZH,KAAmBI,EAAAA,eARnBP,KC8CIQ,EAAS,EAYbC,EAAA,yBA1DA,MAAAA,mBA+HE,QAAFC,GAA4DC,GAAAT,KAA5DS,SAA4DA,EAnC5DT,KAAAU,YAAe,EAWfV,KAAAW,WAAsB,EAQtBX,KAAAY,WAAsB,EAUtBZ,KAAAa,iBAA6C,KAP7C,MAfAC,QAAAC,eAAMP,EAANQ,UAAA,gBAAA,WAA4B,MAAOhB,MAAKW,eACtC,SAAaM,GACXjB,KAAKW,UAAYO,EAAAA,sBAAsBD,oCAM3CH,OAAAC,eAAMP,EAANQ,UAAA,gBAAA,WAA4B,MAAOhB,MAAKY,eACtC,SAAaK,GACXjB,KAAKY,UAAYM,EAAAA,sBAAsBD,oCAM3CH,OAAAC,eAAMP,EAANQ,UAAA,4BACI,MAAgC,OAAzBhB,KAAKa,iBAA2Bb,KAAKmB,kBAAoBnB,KAAKa,sBAEvE,SAAcI,GACZjB,KAAKa,iBAAmBK,EAAAA,sBAAsBD,oCAIlDH,OAAAC,eAAcP,EAAdQ,UAAA,oCACI,MAAOhB,MAAKoB,YAAcpB,KAAKoB,YAAYC,OAASrB,KAAKU,WAAaV,KAAKU,4CAM7EF,EAAFQ,UAAAM,OAAE,WACEtB,KAAKS,SAASc,SAAWvB,MAI3BQ,EAAFQ,UAAAQ,MAAE,WACExB,KAAKU,YAAa,EAEW,MAAzBV,KAAKa,mBACPb,KAAKa,kBAAmB,GAGtBb,KAAKoB,aACPpB,KAAKoB,YAAYI,SAIrBhB,EAAFQ,UAAAS,YAAE,WAGEzB,KAAKS,SAASiB,gCA9ElBzB,KAAC0B,EAAAA,UAADxB,OAAAC,SAAA,WACEwB,SAAU,UACV7B,SAAU,uDACV8B,cAAFC,EAAAA,kBAAAC,KACEC,qBAAF,EACEC,gBAAFC,EAAAA,wBAAAC,gIAiFA3B,EAAA4B,6GA3EAhB,cAAAnB,KAAAoC,EAAAA,QAGAC,QAAArC,KAAAoC,EAAAA,QAGAE,WAAAtC,KAAAoC,EAAAA,QAMAG,WAAAvC,KAAAoC,EAAAA,QAGAI,YAAAxC,KAAAoC,EAAAA,SAgBA7B,KAlHAkC,EAAA,uCAyNA1C,KAAA2C,mBAAAA,gDAzDA3C,KAAA4C,eAA6B,4FAkD7B5C,KAAA6C,SAAAvC,IAJA,MAcAQ,QAAAC,eAAA2B,EAAA1B,UAAA,UACA8B,2GAlDEC,cAAF,kIAOQ,GAAIC,EAAQ,GAApBA,EAAAhD,KAAAiD,OAAAC,OAAA,kFAGQlD,MAARmD,6BAAAH,IAAAA,EAAAhD,KAAA4C,iBACA5C,KAAAiD,OAAAG,UAAAJ,GAAAT,SAGUvC,KAAKqD,YAAfD,UAAAJ,GAAAM,cAAAC,OAEavD,KAAb4C,gBAAAI,IACAhD,KAAAwD,2BAAAR,GAAYhD,KAAKyD,YAAjBzD,KAAoC4C,oBAIpC5C,MAAA4C,eAAA5C,KAAAyD,YAAAT,GAEAU,YAAA,EACAX,cAAA,wLAOAW,YAAA,EACAX,cAAA,8DAoBA/C,KAAA2D,WAAAC,yPAgBA5D,KAAAiD,OAAAY,QAAA,SAAAC,GAAA,MAAAA,GAAAtC,UACIxB,KAAK0B,sVAqBT,OAAAqC,GAAA,EACA,QAAA/D,KAAAgE,mBAAA,OAAA,WAEAD,EAAA,EACA,QAAA/D,KAAAgE,mBAAA,WAAA,OACA,mFAMA,OAAAF,GAAArB,WAAAzC,KAAA4C,gBAAAI,EAIAc,EAAAvB,SAAA,OAAA,OAHA,wFAQAvC,MAAAiE,gBAAAC,MACAC,cAAAC,EACQC,wBAARrE,KAAA4C,eACM0B,aAANC,EAAAH,GACMI,uBAAND,EAAAvE,KAAA4C,kBAEA5C,KAAA4C,eAAAwB,EACApE,KAAA0B,mEAMA+C,KAAAC,EAAAA,8FAGQC,EAARC,kBAEMH,IAANI,EAAAA,aACA,QAAA7E,KAAAgE,mBAAAhE,KAAA8E,iBAAA9E,KAAA+E,qBAEQJ,EAARC,oJAOQD,EAARC,kBAEMH,IAANO,EAAAA,OAAAP,IAAAQ,EAAAA,QACAjF,KAAAmE,cAAAnE,KAAAyD,YAEQkB,EAARC,kBAEMH,IAANS,EAAAA,OACAlF,KAAAmF,WAAA,GAEQR,EAARC,kBAEMH,IAANW,EAAAA,MACApF,KAAAmF,WAAAnF,KAAAiD,OAAAC,OAAA,GAEQyB,EAARC,gSAcA5E,KAAAqD,YAAAD,UAAApD,KAAAyD,aAAAH,cAAA+B,yFAMI,OADJC,GAAuCtF,KAAa4C,gBAApDlC,YAAA,KACAV,KAAAuF,SAAAvC,GAAkB,IAEHsC,EAAfE,MAAA,EAA8BxC,GAA9ByC,KAAA,SAAA3B,GAEY,GAAqB4B,GAAjC5B,EAAA1C,WAIA,QAHAsE,EACAA,EAAAC,SAAAD,EAAAE,UAAA9B,EAAApD,YACAoD,EAAArB,aACgCqB,EAAhCtB,2GAUAE,EAAWmD,6DAhNXjE,SAAA,wIAjIAc,EAAAN,mFA0IA+B,gBAAAlE,KAAAoC,EAAAA,QAMAd,WAAAtB,KAAAoC,EAAAA,QAMA4B,kBAAAhE,KAAA6F,EAAAA,UA+BApD,kBCvLE,QAAFqD,GAAqBtF,GAAAT,KAArBS,SAAqBA,EAFrBT,KAAAC,KAA0B,SArB1B,sBAYAA,KAACC,EAAAA,UAADC,OACEC,SAAU,yBACV4F,MACEC,UAAW,kBACXC,SAAU,gDAPdjG,KAAQyC,uBAYRzC,OAAAA,KAAGoC,EAAAA,SArBH0D,kBAsCE,QAAFI,GAAqB1F,GAAAT,KAArBS,SAAqBA,EAFrBT,KAAAC,KAA0B,SApC1B,sBA2BAA,KAACC,EAAAA,UAADC,OACEC,SAAU,6BACV4F,MACEC,UAAW,sBACXC,SAAU,gDAtBdjG,KAAQyC,uBA2BRzC,OAAAA,KAAGoC,EAAAA,SApCH8D,KCQAC,EAAA,yBARA,sBAeAnG,KAACoG,EAAAA,SAADlG,OACEmG,SAAUC,EAAAA,WAAYC,EAAAA,cACtBC,SAAUjG,EAASkC,EAAY5C,EAAciG,EAAgBI,GAC7DO,cAAelG,EAASkC,EAAY5C,EAAciG,EAAgBI,6CAlBpEC"}