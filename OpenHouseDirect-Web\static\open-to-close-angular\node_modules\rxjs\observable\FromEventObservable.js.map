{"version": 3, "file": "FromEventObservable.js", "sourceRoot": "", "sources": ["../../src/observable/FromEventObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAA2B,eAAe,CAAC,CAAA;AAC3C,yBAAyB,kBAAkB,CAAC,CAAA;AAC5C,2BAA2B,oBAAoB,CAAC,CAAA;AAChD,4BAA4B,qBAAqB,CAAC,CAAA;AAClD,6BAA6B,iBAAiB,CAAC,CAAA;AAG/C,IAAM,QAAQ,GAAa,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AASrD,iCAAiC,SAAc;IAC7C,MAAM,CAAC,CAAC,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,cAAc,KAAK,UAAU,CAAC;AACtH,CAAC;AAMD,mCAAmC,SAAc;IAC/C,MAAM,CAAC,CAAC,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,EAAE,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,GAAG,KAAK,UAAU,CAAC;AAClG,CAAC;AAED,oBAAoB,SAAc;IAChC,MAAM,CAAC,CAAC,CAAC,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,mBAAmB,CAAC;AACzE,CAAC;AAED,0BAA0B,SAAc;IACtC,MAAM,CAAC,CAAC,CAAC,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,yBAAyB,CAAC;AAC/E,CAAC;AAED,uBAAuB,SAAc;IACnC,MAAM,CAAC,CAAC,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,gBAAgB,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,mBAAmB,KAAK,UAAU,CAAC;AAChI,CAAC;AAYD;;;;GAIG;AACH;IAA4C,uCAAa;IAwIvD,6BAAoB,SAA0B,EAC1B,SAAiB,EACjB,QAAqC,EACrC,OAA8B;QAChD,iBAAO,CAAC;QAJU,cAAS,GAAT,SAAS,CAAiB;QAC1B,cAAS,GAAT,SAAS,CAAQ;QACjB,aAAQ,GAAR,QAAQ,CAA6B;QACrC,YAAO,GAAP,OAAO,CAAuB;IAElD,CAAC;IAtID,mCAAmC;IAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmHG;IACI,0BAAM,GAAb,UAAiB,MAAuB,EACvB,SAAiB,EACjB,OAA2D,EAC3D,QAAqC;QACpD,EAAE,CAAC,CAAC,uBAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxB,QAAQ,GAAQ,OAAO,CAAC;YACxB,OAAO,GAAG,SAAS,CAAC;QACtB,CAAC;QACD,MAAM,CAAC,IAAI,mBAAmB,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAA2C,CAAC,CAAC;IAC3G,CAAC;IASc,qCAAiB,GAAhC,UAAoC,SAA0B,EAC1B,SAAiB,EACjB,OAAiB,EACjB,UAAyB,EACzB,OAA8B;QAChE,IAAI,WAAuB,CAAC;QAC5B,EAAE,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACzD,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrD,mBAAmB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAC/F,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACpC,IAAM,QAAM,GAAG,SAAS,CAAC;YACzB,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAiB,OAAO,EAAW,OAAO,CAAC,CAAC;YAChF,WAAW,GAAG,cAAM,OAAA,QAAM,CAAC,mBAAmB,CAAC,SAAS,EAAiB,OAAO,EAAW,OAAO,CAAC,EAA/E,CAA+E,CAAC;QACtG,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAChD,IAAM,QAAM,GAAG,SAAS,CAAC;YACzB,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACjC,WAAW,GAAG,cAAM,OAAA,QAAM,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,EAA9B,CAA8B,CAAC;QACrD,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAM,QAAM,GAAG,SAAS,CAAC;YACzB,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,OAA2B,CAAC,CAAC;YAC9D,WAAW,GAAG,cAAM,OAAA,QAAM,CAAC,cAAc,CAAC,SAAS,EAAE,OAA2B,CAAC,EAA7D,CAA6D,CAAC;QACpF,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;QAC9C,CAAC;QAED,UAAU,CAAC,GAAG,CAAC,IAAI,2BAAY,CAAC,WAAW,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,oCAAoC,CAAC,wCAAU,GAAV,UAAW,UAAyB;QACvE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,OAAO,GAAG,QAAQ,GAAG;YAAC,cAAc;iBAAd,WAAc,CAAd,sBAAc,CAAd,IAAc;gBAAd,6BAAc;;YACtC,IAAI,MAAM,GAAG,mBAAQ,CAAC,QAAQ,CAAC,eAAI,IAAI,CAAC,CAAC;YACzC,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;gBAC3B,UAAU,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,GAAG,UAAC,CAAM,IAAK,OAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC;QAEnC,mBAAmB,CAAC,iBAAiB,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC5F,CAAC;IACH,0BAAC;AAAD,CAAC,AA5LD,CAA4C,uBAAU,GA4LrD;AA5LY,2BAAmB,sBA4L/B,CAAA", "sourcesContent": ["import { Observable } from '../Observable';\nimport { tryCatch } from '../util/tryCatch';\nimport { isFunction } from '../util/isFunction';\nimport { errorObject } from '../util/errorObject';\nimport { Subscription } from '../Subscription';\nimport { Subscriber } from '../Subscriber';\n\nconst toString: Function = Object.prototype.toString;\n\nexport type NodeStyleEventEmitter = {\n  addListener: (eventName: string, handler: NodeEventHandler) => void;\n  removeListener: (eventName: string, handler: NodeEventHandler) => void;\n};\n\nexport type NodeEventHandler = (...args: any[]) => void;\n\nfunction isNodeStyleEventEmitter(sourceObj: any): sourceObj is NodeStyleEventEmitter {\n  return !!sourceObj && typeof sourceObj.addListener === 'function' && typeof sourceObj.removeListener === 'function';\n}\n\nexport type JQueryStyleEventEmitter = {\n  on: (eventName: string, handler: Function) => void;\n  off: (eventName: string, handler: Function) => void;\n};\nfunction isJQueryStyleEventEmitter(sourceObj: any): sourceObj is JQueryStyleEventEmitter {\n  return !!sourceObj && typeof sourceObj.on === 'function' && typeof sourceObj.off === 'function';\n}\n\nfunction isNodeList(sourceObj: any): sourceObj is NodeList {\n  return !!sourceObj && toString.call(sourceObj) === '[object NodeList]';\n}\n\nfunction isHTMLCollection(sourceObj: any): sourceObj is HTMLCollection {\n  return !!sourceObj && toString.call(sourceObj) === '[object HTMLCollection]';\n}\n\nfunction isEventTarget(sourceObj: any): sourceObj is EventTarget {\n  return !!sourceObj && typeof sourceObj.addEventListener === 'function' && typeof sourceObj.removeEventListener === 'function';\n}\n\nexport type EventTargetLike = EventTarget | NodeStyleEventEmitter | JQueryStyleEventEmitter | NodeList | HTMLCollection;\n\nexport type EventListenerOptions = {\n  capture?: boolean;\n  passive?: boolean;\n  once?: boolean;\n} | boolean;\n\nexport type SelectorMethodSignature<T> = (...args: Array<any>) => T;\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class FromEventObservable<T> extends Observable<T> {\n\n  /* tslint:disable:max-line-length */\n  static create<T>(target: EventTargetLike, eventName: string): Observable<T>;\n  static create<T>(target: EventTargetLike, eventName: string, selector: SelectorMethodSignature<T>): Observable<T>;\n  static create<T>(target: EventTargetLike, eventName: string, options: EventListenerOptions): Observable<T>;\n  static create<T>(target: EventTargetLike, eventName: string, options: EventListenerOptions, selector: SelectorMethodSignature<T>): Observable<T>;\n  /* tslint:enable:max-line-length */\n\n  /**\n   * Creates an Observable that emits events of a specific type coming from the\n   * given event target.\n   *\n   * <span class=\"informal\">Creates an Observable from DOM events, or Node.js\n   * EventEmitter events or others.</span>\n   *\n   * <img src=\"./img/fromEvent.png\" width=\"100%\">\n   *\n   * `fromEvent` accepts as a first argument event target, which is an object with methods\n   * for registering event handler functions. As a second argument it takes string that indicates\n   * type of event we want to listen for. `fromEvent` supports selected types of event targets,\n   * which are described in detail below. If your event target does not match any of the ones listed,\n   * you should use {@link fromEventPattern}, which can be used on arbitrary APIs.\n   * When it comes to APIs supported by `fromEvent`, their methods for adding and removing event\n   * handler functions have different names, but they all accept a string describing event type\n   * and function itself, which will be called whenever said event happens.\n   *\n   * Every time resulting Observable is subscribed, event handler function will be registered\n   * to event target on given event type. When that event fires, value\n   * passed as a first argument to registered function will be emitted by output Observable.\n   * When Observable is unsubscribed, function will be unregistered from event target.\n   *\n   * Note that if event target calls registered function with more than one argument, second\n   * and following arguments will not appear in resulting stream. In order to get access to them,\n   * you can pass to `fromEvent` optional project function, which will be called with all arguments\n   * passed to event handler. Output Observable will then emit value returned by project function,\n   * instead of the usual value.\n   *\n   * Remember that event targets listed below are checked via duck typing. It means that\n   * no matter what kind of object you have and no matter what environment you work in,\n   * you can safely use `fromEvent` on that object if it exposes described methods (provided\n   * of course they behave as was described above). So for example if Node.js library exposes\n   * event target which has the same method names as DOM EventTarget, `fromEvent` is still\n   * a good choice.\n   *\n   * If the API you use is more callback then event handler oriented (subscribed\n   * callback function fires only once and thus there is no need to manually\n   * unregister it), you should use {@link bindCallback} or {@link bindNodeCallback}\n   * instead.\n   *\n   * `fromEvent` supports following types of event targets:\n   *\n   * **DOM EventTarget**\n   *\n   * This is an object with `addEventListener` and `removeEventListener` methods.\n   *\n   * In the browser, `addEventListener` accepts - apart from event type string and event\n   * handler function arguments - optional third parameter, which is either an object or boolean,\n   * both used for additional configuration how and when passed function will be called. When\n   * `fromEvent` is used with event target of that type, you can provide this values\n   * as third parameter as well.\n   *\n   * **Node.js EventEmitter**\n   *\n   * An object with `addListener` and `removeListener` methods.\n   *\n   * **JQuery-style event target**\n   *\n   * An object with `on` and `off` methods\n   *\n   * **DOM NodeList**\n   *\n   * List of DOM Nodes, returned for example by `document.querySelectorAll` or `Node.childNodes`.\n   *\n   * Although this collection is not event target in itself, `fromEvent` will iterate over all Nodes\n   * it contains and install event handler function in every of them. When returned Observable\n   * is unsubscribed, function will be removed from all Nodes.\n   *\n   * **DOM HtmlCollection**\n   *\n   * Just as in case of NodeList it is a collection of DOM nodes. Here as well event handler function is\n   * installed and removed in each of elements.\n   *\n   *\n   * @example <caption>Emits clicks happening on the DOM document</caption>\n   * var clicks = Rx.Observable.fromEvent(document, 'click');\n   * clicks.subscribe(x => console.log(x));\n   *\n   * // Results in:\n   * // MouseEvent object logged to console every time a click\n   * // occurs on the document.\n   *\n   *\n   * @example <caption>Use addEventListener with capture option</caption>\n   * var clicksInDocument = Rx.Observable.fromEvent(document, 'click', true); // note optional configuration parameter\n   *                                                                          // which will be passed to addEventListener\n   * var clicksInDiv = Rx.Observable.fromEvent(someDivInDocument, 'click');\n   *\n   * clicksInDocument.subscribe(() => console.log('document'));\n   * clicksInDiv.subscribe(() => console.log('div'));\n   *\n   * // By default events bubble UP in DOM tree, so normally\n   * // when we would click on div in document\n   * // \"div\" would be logged first and then \"document\".\n   * // Since we specified optional `capture` option, document\n   * // will catch event when it goes DOWN DOM tree, so console\n   * // will log \"document\" and then \"div\".\n   *\n   * @see {@link bindCallback}\n   * @see {@link bindNodeCallback}\n   * @see {@link fromEventPattern}\n   *\n   * @param {EventTargetLike} target The DOM EventTarget, Node.js\n   * EventEmitter, JQuery-like event target, NodeList or HTMLCollection to attach the event handler to.\n   * @param {string} eventName The event name of interest, being emitted by the\n   * `target`.\n   * @param {EventListenerOptions} [options] Options to pass through to addEventListener\n   * @param {SelectorMethodSignature<T>} [selector] An optional function to\n   * post-process results. It takes the arguments from the event handler and\n   * should return a single value.\n   * @return {Observable<T>}\n   * @static true\n   * @name fromEvent\n   * @owner Observable\n   */\n  static create<T>(target: EventTargetLike,\n                   eventName: string,\n                   options?: EventListenerOptions | SelectorMethodSignature<T>,\n                   selector?: SelectorMethodSignature<T>): Observable<T> {\n    if (isFunction(options)) {\n      selector = <any>options;\n      options = undefined;\n    }\n    return new FromEventObservable(target, eventName, selector, options as EventListenerOptions | undefined);\n  }\n\n  constructor(private sourceObj: EventTargetLike,\n              private eventName: string,\n              private selector?: SelectorMethodSignature<T>,\n              private options?: EventListenerOptions) {\n    super();\n  }\n\n  private static setupSubscription<T>(sourceObj: EventTargetLike,\n                                      eventName: string,\n                                      handler: Function,\n                                      subscriber: Subscriber<T>,\n                                      options?: EventListenerOptions) {\n    let unsubscribe: () => void;\n    if (isNodeList(sourceObj) || isHTMLCollection(sourceObj)) {\n      for (let i = 0, len = sourceObj.length; i < len; i++) {\n        FromEventObservable.setupSubscription(sourceObj[i], eventName, handler, subscriber, options);\n      }\n    } else if (isEventTarget(sourceObj)) {\n      const source = sourceObj;\n      sourceObj.addEventListener(eventName, <EventListener>handler, <boolean>options);\n      unsubscribe = () => source.removeEventListener(eventName, <EventListener>handler, <boolean>options);\n    } else if (isJQueryStyleEventEmitter(sourceObj)) {\n      const source = sourceObj;\n      sourceObj.on(eventName, handler);\n      unsubscribe = () => source.off(eventName, handler);\n    } else if (isNodeStyleEventEmitter(sourceObj)) {\n      const source = sourceObj;\n      sourceObj.addListener(eventName, handler as NodeEventHandler);\n      unsubscribe = () => source.removeListener(eventName, handler as NodeEventHandler);\n    } else {\n      throw new TypeError('Invalid event target');\n    }\n\n    subscriber.add(new Subscription(unsubscribe));\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>) {\n    const sourceObj = this.sourceObj;\n    const eventName = this.eventName;\n    const options = this.options;\n    const selector = this.selector;\n    let handler = selector ? (...args: any[]) => {\n      let result = tryCatch(selector)(...args);\n      if (result === errorObject) {\n        subscriber.error(errorObject.e);\n      } else {\n        subscriber.next(result);\n      }\n    } : (e: any) => subscriber.next(e);\n\n    FromEventObservable.setupSubscription(sourceObj, eventName, handler, subscriber, options);\n  }\n}\n"]}