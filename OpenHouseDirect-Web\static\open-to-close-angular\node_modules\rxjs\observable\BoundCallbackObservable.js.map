{"version": 3, "file": "BoundCallbackObservable.js", "sourceRoot": "", "sources": ["../../src/observable/BoundCallbackObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAA2B,eAAe,CAAC,CAAA;AAI3C,yBAAyB,kBAAkB,CAAC,CAAA;AAC5C,4BAA4B,qBAAqB,CAAC,CAAA;AAClD,6BAA6B,iBAAiB,CAAC,CAAA;AAE/C;;;;GAIG;AACH;IAAgD,2CAAa;IAsK3D,iCAAoB,YAAsB,EACtB,QAAkB,EAClB,IAAW,EACX,OAAY,EACZ,SAAqB;QACvC,iBAAO,CAAC;QALU,iBAAY,GAAZ,YAAY,CAAU;QACtB,aAAQ,GAAR,QAAQ,CAAU;QAClB,SAAI,GAAJ,IAAI,CAAO;QACX,YAAO,GAAP,OAAO,CAAK;QACZ,cAAS,GAAT,SAAS,CAAY;IAEzC,CAAC;IAvJD,mCAAmC;IAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsIG;IACI,8BAAM,GAAb,UAAiB,IAAc,EACd,QAAqC,EACrC,SAAsB;QADtB,wBAAqC,GAArC,oBAAqC;QAEpD,MAAM,CAAC;YAAoB,cAAc;iBAAd,WAAc,CAAd,sBAAc,CAAd,IAAc;gBAAd,6BAAc;;YACvC,MAAM,CAAC,IAAI,uBAAuB,CAAI,IAAI,EAAO,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QACpF,CAAC,CAAC;IACJ,CAAC;IAUD,oCAAoC,CAAC,4CAAU,GAAV,UAAW,UAA+B;QAC7E,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE3B,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACf,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBACb,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,2BAAY,EAAK,CAAC;gBAC/C,IAAM,OAAO,GAAG;oBAA8B,mBAAmB;yBAAnB,WAAmB,CAAnB,sBAAmB,CAAnB,IAAmB;wBAAnB,kCAAmB;;oBAC/D,IAAM,MAAM,GAAS,SAAU,CAAC,MAAM,CAAC;oBAC/B,8BAAQ,EAAE,wBAAO,CAAY;oBACrC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACb,IAAM,QAAM,GAAG,mBAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;wBACzD,EAAE,CAAC,CAAC,QAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;4BAC3B,OAAO,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;wBACjC,CAAC;wBAAC,IAAI,CAAC,CAAC;4BACJ,OAAO,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;4BACrB,OAAO,CAAC,QAAQ,EAAE,CAAC;wBACrB,CAAC;oBACH,CAAC;oBAAC,IAAI,CAAC,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;wBAC/D,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACrB,CAAC;gBACH,CAAC,CAAC;gBACF,gDAAgD;gBAC1C,OAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;gBAE7B,IAAM,MAAM,GAAG,mBAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBAChF,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;oBAC3B,OAAO,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YACD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,sBAAU,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACtH,CAAC;IACH,CAAC;IAEM,gCAAQ,GAAf,UAAmB,KAAsF;QACvG,IAAM,IAAI,GAAuB,IAAK,CAAC;QAC/B,yBAAM,EAAE,6BAAU,EAAE,uBAAO,CAAW;QACtC,sCAAY,EAAE,kBAAI,EAAE,4BAAS,CAAY;QACjD,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE7B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACb,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,2BAAY,EAAK,CAAC;YAEjD,IAAM,OAAO,GAAG;gBAA8B,mBAAmB;qBAAnB,WAAmB,CAAnB,sBAAmB,CAAnB,IAAmB;oBAAnB,kCAAmB;;gBAC/D,IAAM,MAAM,GAAS,SAAU,CAAC,MAAM,CAAC;gBAC/B,8BAAQ,EAAE,wBAAO,CAAY;gBACrC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACb,IAAM,QAAM,GAAG,mBAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;oBACzD,EAAE,CAAC,CAAC,QAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;wBAC3B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,yBAAW,CAAC,CAAC,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;oBAClF,CAAC;oBAAC,IAAI,CAAC,CAAC;wBACN,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,QAAM,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;oBAC5E,CAAC;gBACH,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,IAAM,KAAK,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;oBAC/D,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,YAAK,EAAE,gBAAO,EAAE,CAAC,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC,CAAC;YACF,uDAAuD;YACjD,OAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;YAE/B,IAAM,MAAM,GAAG,mBAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3E,EAAE,CAAC,CAAC,MAAM,KAAK,yBAAW,CAAC,CAAC,CAAC;gBAC3B,OAAO,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1C,CAAC;IACH,8BAAC;AAAD,CAAC,AAxPD,CAAgD,uBAAU,GAwPzD;AAxPY,+BAAuB,0BAwPnC,CAAA;AAMD,sBAAyB,GAAuB;IACtC,qBAAK,EAAE,qBAAO,CAAS;IAC/B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC;AAMD,uBAA0B,GAAwB;IACxC,iBAAG,EAAE,qBAAO,CAAS;IAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC", "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { IScheduler } from '../Scheduler';\nimport { tryCatch } from '../util/tryCatch';\nimport { errorObject } from '../util/errorObject';\nimport { AsyncSubject } from '../AsyncSubject';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class BoundCallbackObservable<T> extends Observable<T> {\n  subject: AsyncSubject<T>;\n\n  /* tslint:disable:max-line-length */\n  static create(callbackFunc: (callback: () => any) => any, selector?: void, scheduler?: IScheduler): () => Observable<void>;\n  static create<R>(callbackFunc: (callback: (result: R) => any) => any, selector?: void, scheduler?: IScheduler): () => Observable<R>;\n  static create<T, R>(callbackFunc: (v1: T, callback: (result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T) => Observable<R>;\n  static create<T, T2, R>(callbackFunc: (v1: T, v2: T2, callback: (result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2) => Observable<R>;\n  static create<T, T2, T3, R>(callbackFunc: (v1: T, v2: T2, v3: T3, callback: (result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3) => Observable<R>;\n  static create<T, T2, T3, T4, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, callback: (result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4) => Observable<R>;\n  static create<T, T2, T3, T4, T5, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, callback: (result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4, v5: T5) => Observable<R>;\n  static create<T, T2, T3, T4, T5, T6, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6, callback: (result: R) => any) => any, selector?: void, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6) => Observable<R>;\n  static create<R>(callbackFunc: (callback: (...args: any[]) => any) => any, selector: (...args: any[]) => R, scheduler?: IScheduler): () => Observable<R>;\n  static create<T, R>(callbackFunc: (v1: T, callback: (...args: any[]) => any) => any, selector: (...args: any[]) => R, scheduler?: IScheduler): (v1: T) => Observable<R>;\n  static create<T, T2, R>(callbackFunc: (v1: T, v2: T2, callback: (...args: any[]) => any) => any, selector: (...args: any[]) => R, scheduler?: IScheduler): (v1: T, v2: T2) => Observable<R>;\n  static create<T, T2, T3, R>(callbackFunc: (v1: T, v2: T2, v3: T3, callback: (...args: any[]) => any) => any, selector: (...args: any[]) => R, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3) => Observable<R>;\n  static create<T, T2, T3, T4, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, callback: (...args: any[]) => any) => any, selector: (...args: any[]) => R, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4) => Observable<R>;\n  static create<T, T2, T3, T4, T5, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, callback: (...args: any[]) => any) => any, selector: (...args: any[]) => R, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4, v5: T5) => Observable<R>;\n  static create<T, T2, T3, T4, T5, T6, R>(callbackFunc: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6, callback: (...args: any[]) => any) => any, selector: (...args: any[]) => R, scheduler?: IScheduler): (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6) => Observable<R>;\n  static create<T>(callbackFunc: Function, selector?: void, scheduler?: IScheduler): (...args: any[]) => Observable<T>;\n  static create<T>(callbackFunc: Function, selector?: (...args: any[]) => T, scheduler?: IScheduler): (...args: any[]) => Observable<T>;\n  /* tslint:enable:max-line-length */\n\n  /**\n   * Converts a callback API to a function that returns an Observable.\n   *\n   * <span class=\"informal\">Give it a function `f` of type `f(x, callback)` and\n   * it will return a function `g` that when called as `g(x)` will output an\n   * Observable.</span>\n   *\n   * `bindCallback` is not an operator because its input and output are not\n   * Observables. The input is a function `func` with some parameters, the\n   * last parameter must be a callback function that `func` calls when it is\n   * done.\n   *\n   * The output of `bindCallback` is a function that takes the same parameters\n   * as `func`, except the last one (the callback). When the output function\n   * is called with arguments it will return an Observable. If function `func`\n   * calls its callback with one argument the Observable will emit that value.\n   * If on the other hand the callback is called with multiple values the resulting\n   * Observable will emit an array with said values as arguments.\n   *\n   * It is very important to remember that input function `func` is not called\n   * when the output function is, but rather when the Observable returned by the output\n   * function is subscribed. This means if `func` makes an AJAX request, that request\n   * will be made every time someone subscribes to the resulting Observable, but not before.\n   *\n   * Optionally, a selector function can be passed to `bindObservable`. The selector function\n   * takes the same arguments as the callback and returns the value that will be emitted by the Observable.\n   * Even though by default multiple arguments passed to callback appear in the stream as an array\n   * the selector function will be called with arguments directly, just as the callback would.\n   * This means you can imagine the default selector (when one is not provided explicitly)\n   * as a function that aggregates all its arguments into an array, or simply returns first argument\n   * if there is only one.\n   *\n   * The last optional parameter - {@link Scheduler} - can be used to control when the call\n   * to `func` happens after someone subscribes to Observable, as well as when results\n   * passed to callback will be emitted. By default, the subscription to  an Observable calls `func`\n   * synchronously, but using `Scheduler.async` as the last parameter will defer the call to `func`,\n   * just like wrapping the call in `setTimeout` with a timeout of `0` would. If you use the async Scheduler\n   * and call `subscribe` on the output Observable all function calls that are currently executing\n   * will end before `func` is invoked.\n   *\n   * By default results passed to the callback are emitted immediately after `func` invokes the callback.\n   * In particular, if the callback is called synchronously the subscription of the resulting Observable\n   * will call the `next` function synchronously as well.  If you want to defer that call,\n   * you may use `Scheduler.async` just as before.  This means that by using `Scheduler.async` you can\n   * ensure that `func` always calls its callback asynchronously, thus avoiding terrifying Zalgo.\n   *\n   * Note that the Observable created by the output function will always emit a single value\n   * and then complete immediately. If `func` calls the callback multiple times, values from subsequent\n   * calls will not appear in the stream. If you need to listen for multiple calls,\n   *  you probably want to use {@link fromEvent} or {@link fromEventPattern} instead.\n   *\n   * If `func` depends on some context (`this` property) and is not already bound the context of `func`\n   * will be the context that the output function has at call time. In particular, if `func`\n   * is called as a method of some objec and if `func` is not already bound, in order to preserve the context\n   * it is recommended that the context of the output function is set to that object as well.\n   *\n   * If the input function calls its callback in the \"node style\" (i.e. first argument to callback is\n   * optional error parameter signaling whether the call failed or not), {@link bindNodeCallback}\n   * provides convenient error handling and probably is a better choice.\n   * `bindCallback` will treat such functions the same as any other and error parameters\n   * (whether passed or not) will always be interpreted as regular callback argument.\n   *\n   *\n   * @example <caption>Convert jQuery's getJSON to an Observable API</caption>\n   * // Suppose we have jQuery.getJSON('/my/url', callback)\n   * var getJSONAsObservable = Rx.Observable.bindCallback(jQuery.getJSON);\n   * var result = getJSONAsObservable('/my/url');\n   * result.subscribe(x => console.log(x), e => console.error(e));\n   *\n   *\n   * @example <caption>Receive an array of arguments passed to a callback</caption>\n   * someFunction((a, b, c) => {\n   *   console.log(a); // 5\n   *   console.log(b); // 'some string'\n   *   console.log(c); // {someProperty: 'someValue'}\n   * });\n   *\n   * const boundSomeFunction = Rx.Observable.bindCallback(someFunction);\n   * boundSomeFunction().subscribe(values => {\n   *   console.log(values) // [5, 'some string', {someProperty: 'someValue'}]\n   * });\n   *\n   *\n   * @example <caption>Use bindCallback with a selector function</caption>\n   * someFunction((a, b, c) => {\n   *   console.log(a); // 'a'\n   *   console.log(b); // 'b'\n   *   console.log(c); // 'c'\n   * });\n   *\n   * const boundSomeFunction = Rx.Observable.bindCallback(someFunction, (a, b, c) => a + b + c);\n   * boundSomeFunction().subscribe(value => {\n   *   console.log(value) // 'abc'\n   * });\n   *\n   *\n   * @example <caption>Compare behaviour with and without async Scheduler</caption>\n   * function iCallMyCallbackSynchronously(cb) {\n   *   cb();\n   * }\n   *\n   * const boundSyncFn = Rx.Observable.bindCallback(iCallMyCallbackSynchronously);\n   * const boundAsyncFn = Rx.Observable.bindCallback(iCallMyCallbackSynchronously, null, Rx.Scheduler.async);\n   *\n   * boundSyncFn().subscribe(() => console.log('I was sync!'));\n   * boundAsyncFn().subscribe(() => console.log('I was async!'));\n   * console.log('This happened...');\n   *\n   * // Logs:\n   * // I was sync!\n   * // This happened...\n   * // I was async!\n   *\n   *\n   * @example <caption>Use bindCallback on an object method</caption>\n   * const boundMethod = Rx.Observable.bindCallback(someObject.methodWithCallback);\n   * boundMethod.call(someObject) // make sure methodWithCallback has access to someObject\n   * .subscribe(subscriber);\n   *\n   *\n   * @see {@link bindNodeCallback}\n   * @see {@link from}\n   * @see {@link fromPromise}\n   *\n   * @param {function} func A function with a callback as the last parameter.\n   * @param {function} [selector] A function which takes the arguments from the\n   * callback and maps them to a value that is emitted on the output Observable.\n   * @param {Scheduler} [scheduler] The scheduler on which to schedule the\n   * callbacks.\n   * @return {function(...params: *): Observable} A function which returns the\n   * Observable that delivers the same values the callback would deliver.\n   * @static true\n   * @name bindCallback\n   * @owner Observable\n   */\n  static create<T>(func: Function,\n                   selector: Function | void = undefined,\n                   scheduler?: IScheduler): (...args: any[]) => Observable<T> {\n    return function(this: any, ...args: any[]): Observable<T> {\n      return new BoundCallbackObservable<T>(func, <any>selector, args, this, scheduler);\n    };\n  }\n\n  constructor(private callbackFunc: Function,\n              private selector: Function,\n              private args: any[],\n              private context: any,\n              private scheduler: IScheduler) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T | T[]>): Subscription {\n    const callbackFunc = this.callbackFunc;\n    const args = this.args;\n    const scheduler = this.scheduler;\n    let subject = this.subject;\n\n    if (!scheduler) {\n      if (!subject) {\n        subject = this.subject = new AsyncSubject<T>();\n        const handler = function handlerFn(this: any, ...innerArgs: any[]) {\n          const source = (<any>handlerFn).source;\n          const { selector, subject } = source;\n          if (selector) {\n            const result = tryCatch(selector).apply(this, innerArgs);\n            if (result === errorObject) {\n              subject.error(errorObject.e);\n          } else {\n              subject.next(result);\n              subject.complete();\n            }\n          } else {\n            subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n            subject.complete();\n          }\n        };\n        // use named function instance to avoid closure.\n        (<any>handler).source = this;\n\n        const result = tryCatch(callbackFunc).apply(this.context, args.concat(handler));\n        if (result === errorObject) {\n          subject.error(errorObject.e);\n        }\n      }\n      return subject.subscribe(subscriber);\n    } else {\n      return scheduler.schedule(BoundCallbackObservable.dispatch, 0, { source: this, subscriber, context: this.context });\n    }\n  }\n\n  static dispatch<T>(state: { source: BoundCallbackObservable<T>, subscriber: Subscriber<T>, context: any }) {\n    const self = (<Subscription><any>this);\n    const { source, subscriber, context } = state;\n    const { callbackFunc, args, scheduler } = source;\n    let subject = source.subject;\n\n    if (!subject) {\n      subject = source.subject = new AsyncSubject<T>();\n\n      const handler = function handlerFn(this: any, ...innerArgs: any[]) {\n        const source = (<any>handlerFn).source;\n        const { selector, subject } = source;\n        if (selector) {\n          const result = tryCatch(selector).apply(this, innerArgs);\n          if (result === errorObject) {\n            self.add(scheduler.schedule(dispatchError, 0, { err: errorObject.e, subject }));\n          } else {\n            self.add(scheduler.schedule(dispatchNext, 0, { value: result, subject }));\n          }\n        } else {\n          const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n          self.add(scheduler.schedule(dispatchNext, 0, { value, subject }));\n        }\n      };\n      // use named function to pass values in without closure\n      (<any>handler).source = source;\n\n      const result = tryCatch(callbackFunc).apply(context, args.concat(handler));\n      if (result === errorObject) {\n        subject.error(errorObject.e);\n      }\n    }\n\n    self.add(subject.subscribe(subscriber));\n  }\n}\n\ninterface DispatchNextArg<T> {\n  subject: AsyncSubject<T>;\n  value: T;\n}\nfunction dispatchNext<T>(arg: DispatchNextArg<T>) {\n  const { value, subject } = arg;\n  subject.next(value);\n  subject.complete();\n}\n\ninterface DispatchErrorArg<T> {\n  subject: AsyncSubject<T>;\n  err: any;\n}\nfunction dispatchError<T>(arg: DispatchErrorArg<T>) {\n  const { err, subject } = arg;\n  subject.error(err);\n}\n"]}