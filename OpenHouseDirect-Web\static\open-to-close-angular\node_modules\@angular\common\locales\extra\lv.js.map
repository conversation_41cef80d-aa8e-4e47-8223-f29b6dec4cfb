{"version": 3, "file": "lv.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/lv.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,AAAD;QAChE,CAAC,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC;KAC1E;IACD;QACE,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;QAC5D,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC;QAC/D,CAAC,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC;KACvE;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['pusnaktī', 'pusd.', 'no rīta', 'pēcpusd.', 'vakarā', 'naktī'], ,\n    ['pusnaktī', 'pusdienlaikā', 'no rīta', 'pēcpusdienā', 'vakarā', 'naktī']\n  ],\n  [\n    ['pusnakts', 'pusd.', 'rīts', 'pēcpusd.', 'vakars', 'nakts'],\n    ['pusnakts', 'pusd.', 'rīts', 'pēcpusdiena', 'vakars', 'nakts'],\n    ['pusnakts', 'pusdienlaiks', 'rīts', 'pēcpusdiena', 'vakars', 'nakts']\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '23:00'],\n    ['23:00', '06:00']\n  ]\n];\n"]}