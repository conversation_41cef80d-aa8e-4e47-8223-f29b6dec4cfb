{"_from": "to-regex@^3.0.2", "_id": "to-regex@3.0.2", "_inBundle": false, "_integrity": "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==", "_location": "/to-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "to-regex@^3.0.2", "name": "to-regex", "escapedName": "to-regex", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/http-proxy-middleware/braces", "/http-proxy-middleware/expand-brackets", "/http-proxy-middleware/extglob", "/http-proxy-middleware/micromatch", "/karma/braces", "/karma/expand-brackets", "/karma/extglob", "/karma/micromatch", "/nanomatch", "/readdirp/braces", "/readdirp/expand-brackets", "/readdirp/extglob", "/readdirp/micromatch", "/watchpack-chokidar2/braces", "/watchpack-chokidar2/expand-brackets", "/watchpack-chokidar2/extglob", "/watchpack-chokidar2/micromatch", "/webpack-dev-server/braces", "/webpack-dev-server/expand-brackets", "/webpack-dev-server/extglob", "/webpack-dev-server/micromatch"], "_resolved": "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz", "_shasum": "13cfdd9b336552f30b51f33a8ae1b42a7a7599ce", "_spec": "to-regex@^3.0.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\readdirp\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex/issues"}, "bundleDependencies": false, "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "deprecated": false, "description": "Generate a regex from a string or array of strings.", "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/to-regex", "keywords": ["match", "regex", "regular expression", "test", "to"], "license": "MIT", "main": "index.js", "name": "to-regex", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": {"method": "preWrite"}, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-glob", "is-glob", "path-regex", "to-regex-range"]}, "lint": {"reflinks": true}}, "version": "3.0.2"}