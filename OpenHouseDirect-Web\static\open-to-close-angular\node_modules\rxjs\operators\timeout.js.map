{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../../src/operators/timeout.ts"], "names": [], "mappings": ";;;;;;AACA,sBAAsB,oBAAoB,CAAC,CAAA;AAC3C,uBAAuB,gBAAgB,CAAC,CAAA;AAExC,2BAA2B,eAAe,CAAC,CAAA;AAI3C,6BAA6B,sBAAsB,CAAC,CAAA;AAGpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgEG;AACH,iBAA2B,GAAkB,EAClB,SAA6B;IAA7B,yBAA6B,GAA7B,yBAA6B;IACtD,IAAM,eAAe,GAAG,eAAM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAM,OAAO,GAAG,eAAe,GAAG,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAS,GAAG,CAAC,CAAC;IACnF,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,2BAAY,EAAE,CAAC,CAAC,EAAzF,CAAyF,CAAC;AAC9H,CAAC;AALe,eAAO,UAKtB,CAAA;AAED;IACE,yBAAoB,OAAe,EACf,eAAwB,EACxB,SAAqB,EACrB,aAA2B;QAH3B,YAAO,GAAP,OAAO,CAAQ;QACf,oBAAe,GAAf,eAAe,CAAS;QACxB,cAAS,GAAT,SAAS,CAAY;QACrB,kBAAa,GAAb,aAAa,CAAc;IAC/C,CAAC;IAED,8BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,iBAAiB,CAC3C,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CACnF,CAAC,CAAC;IACL,CAAC;IACH,sBAAC;AAAD,CAAC,AAZD,IAYC;AAED;;;;GAIG;AACH;IAAmC,qCAAa;IAI9C,2BAAY,WAA0B,EAClB,eAAwB,EACxB,OAAe,EACf,SAAqB,EACrB,aAA2B;QAC7C,kBAAM,WAAW,CAAC,CAAC;QAJD,oBAAe,GAAf,eAAe,CAAS;QACxB,YAAO,GAAP,OAAO,CAAQ;QACf,cAAS,GAAT,SAAS,CAAY;QACrB,kBAAa,GAAb,aAAa,CAAc;QANvC,WAAM,GAAiC,IAAI,CAAC;QAQlD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEc,iCAAe,GAA9B,UAAkC,UAAgC;QAChE,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAEO,2CAAe,GAAvB;QACU,wBAAM,CAAU;QACxB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,wEAAwE;YACxE,0EAA0E;YAC1E,2EAA2E;YAC3E,8EAA8E;YAC9E,oDAAoD;YACpD,IAAI,CAAC,MAAM,GAAmC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAE,CAAC;QACrF,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAmC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAC5E,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CACrD,CAAC,CAAC;QACN,CAAC;IACH,CAAC;IAES,iCAAK,GAAf,UAAgB,KAAQ;QACtB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QACD,gBAAK,CAAC,KAAK,YAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,oCAAoC,CAAC,wCAAY,GAAZ;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC5B,CAAC;IACH,wBAAC;AAAD,CAAC,AA7CD,CAAmC,uBAAU,GA6C5C", "sourcesContent": ["import { Action } from '../scheduler/Action';\nimport { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { TeardownLogic } from '../Subscription';\nimport { TimeoutError } from '../util/TimeoutError';\nimport { MonoTypeOperatorFunction } from '../interfaces';\n\n/**\n *\n * Errors if Observable does not emit a value in given time span.\n *\n * <span class=\"informal\">Timeouts on Observable that doesn't emit values fast enough.</span>\n *\n * <img src=\"./img/timeout.png\" width=\"100%\">\n *\n * `timeout` operator accepts as an argument either a number or a Date.\n *\n * If number was provided, it returns an Observable that behaves like a source\n * Observable, unless there is a period of time where there is no value emitted.\n * So if you provide `100` as argument and first value comes after 50ms from\n * the moment of subscription, this value will be simply re-emitted by the resulting\n * Observable. If however after that 100ms passes without a second value being emitted,\n * stream will end with an error and source Observable will be unsubscribed.\n * These checks are performed throughout whole lifecycle of Observable - from the moment\n * it was subscribed to, until it completes or errors itself. Thus every value must be\n * emitted within specified period since previous value.\n *\n * If provided argument was Date, returned Observable behaves differently. It throws\n * if Observable did not complete before provided Date. This means that periods between\n * emission of particular values do not matter in this case. If Observable did not complete\n * before provided Date, source Observable will be unsubscribed. Other than that, resulting\n * stream behaves just as source Observable.\n *\n * `timeout` accepts also a Scheduler as a second parameter. It is used to schedule moment (or moments)\n * when returned Observable will check if source stream emitted value or completed.\n *\n * @example <caption>Check if ticks are emitted within certain timespan</caption>\n * const seconds = Rx.Observable.interval(1000);\n *\n * seconds.timeout(1100) // Let's use bigger timespan to be safe,\n *                       // since `interval` might fire a bit later then scheduled.\n * .subscribe(\n *     value => console.log(value), // Will emit numbers just as regular `interval` would.\n *     err => console.log(err) // Will never be called.\n * );\n *\n * seconds.timeout(900).subscribe(\n *     value => console.log(value), // Will never be called.\n *     err => console.log(err) // Will emit error before even first value is emitted,\n *                             // since it did not arrive within 900ms period.\n * );\n *\n * @example <caption>Use Date to check if Observable completed</caption>\n * const seconds = Rx.Observable.interval(1000);\n *\n * seconds.timeout(new Date(\"December 17, 2020 03:24:00\"))\n * .subscribe(\n *     value => console.log(value), // Will emit values as regular `interval` would\n *                                  // until December 17, 2020 at 03:24:00.\n *     err => console.log(err) // On December 17, 2020 at 03:24:00 it will emit an error,\n *                             // since Observable did not complete by then.\n * );\n *\n * @see {@link timeoutWith}\n *\n * @param {number|Date} due Number specifying period within which Observable must emit values\n *                          or Date specifying before when Observable should complete\n * @param {Scheduler} [scheduler] Scheduler controlling when timeout checks occur.\n * @return {Observable<T>} Observable that mirrors behaviour of source, unless timeout checks fail.\n * @method timeout\n * @owner Observable\n */\nexport function timeout<T>(due: number | Date,\n                           scheduler: IScheduler = async): MonoTypeOperatorFunction<T> {\n  const absoluteTimeout = isDate(due);\n  const waitFor = absoluteTimeout ? (+due - scheduler.now()) : Math.abs(<number>due);\n  return (source: Observable<T>) => source.lift(new TimeoutOperator(waitFor, absoluteTimeout, scheduler, new TimeoutError()));\n}\n\nclass TimeoutOperator<T> implements Operator<T, T> {\n  constructor(private waitFor: number,\n              private absoluteTimeout: boolean,\n              private scheduler: IScheduler,\n              private errorInstance: TimeoutError) {\n  }\n\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n    return source.subscribe(new TimeoutSubscriber<T>(\n      subscriber, this.absoluteTimeout, this.waitFor, this.scheduler, this.errorInstance\n    ));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass TimeoutSubscriber<T> extends Subscriber<T> {\n\n  private action: Action<TimeoutSubscriber<T>> = null;\n\n  constructor(destination: Subscriber<T>,\n              private absoluteTimeout: boolean,\n              private waitFor: number,\n              private scheduler: IScheduler,\n              private errorInstance: TimeoutError) {\n    super(destination);\n    this.scheduleTimeout();\n  }\n\n  private static dispatchTimeout<T>(subscriber: TimeoutSubscriber<T>): void {\n    subscriber.error(subscriber.errorInstance);\n  }\n\n  private scheduleTimeout(): void {\n    const { action } = this;\n    if (action) {\n      // Recycle the action if we've already scheduled one. All the production\n      // Scheduler Actions mutate their state/delay time and return themeselves.\n      // VirtualActions are immutable, so they create and return a clone. In this\n      // case, we need to set the action reference to the most recent VirtualAction,\n      // to ensure that's the one we clone from next time.\n      this.action = (<Action<TimeoutSubscriber<T>>> action.schedule(this, this.waitFor));\n    } else {\n      this.add(this.action = (<Action<TimeoutSubscriber<T>>> this.scheduler.schedule(\n        TimeoutSubscriber.dispatchTimeout, this.waitFor, this\n      )));\n    }\n  }\n\n  protected _next(value: T): void {\n    if (!this.absoluteTimeout) {\n      this.scheduleTimeout();\n    }\n    super._next(value);\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    this.action = null;\n    this.scheduler = null;\n    this.errorInstance = null;\n  }\n}\n"]}