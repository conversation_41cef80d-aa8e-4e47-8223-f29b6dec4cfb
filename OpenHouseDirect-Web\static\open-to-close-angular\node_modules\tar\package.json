{"_from": "tar@^2.0.0", "_id": "tar@2.2.2", "_inBundle": false, "_integrity": "sha512-FCEhQ/4rE1zYv9rYXJw/msRqsnmlje5jHP6huWeBZ704jUTy02c5AZyWujpMR1ax6mVw9NyJMfuK2CMDWVIfgA==", "_location": "/tar", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tar@^2.0.0", "name": "tar", "escapedName": "tar", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/node-gyp"], "_resolved": "https://registry.npmjs.org/tar/-/tar-2.2.2.tgz", "_shasum": "0ca8848562c7299b8b446ff6a4d60cdbb23edc40", "_spec": "tar@^2.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\node-gyp", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "bundleDependencies": false, "dependencies": {"block-stream": "*", "fstream": "^1.0.12", "inherits": "2"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "description": "tar for node", "devDependencies": {"graceful-fs": "^4.1.2", "mkdirp": "^0.5.0", "rimraf": "1.x", "tap": "0.x"}, "homepage": "https://github.com/isaacs/node-tar#readme", "license": "ISC", "main": "tar.js", "name": "tar", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "scripts": {"test": "tap test/*.js"}, "version": "2.2.2"}