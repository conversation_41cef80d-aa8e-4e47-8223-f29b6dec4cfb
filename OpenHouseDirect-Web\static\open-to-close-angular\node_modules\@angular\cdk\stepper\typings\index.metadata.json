{"__symbolic": "module", "version": 4, "metadata": {"StepContentPositionState": {"__symbolic": "interface"}, "StepperOrientation": {"__symbolic": "interface"}, "StepperSelectionEvent": {"__symbolic": "class", "members": {}}, "CdkStep": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Component", "line": 72, "character": 1}, "arguments": [{"moduleId": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "module"}, "member": "id"}, "selector": "cdk-step", "exportAs": "cdkStep", "encapsulation": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewEncapsulation", "line": 77, "character": 17}, "member": "None"}, "preserveWhitespaces": false, "changeDetection": {"__symbolic": "select", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectionStrategy", "line": 79, "character": 19}, "member": "OnPush"}, "template": "<ng-template><ng-content></ng-content></ng-template>"}]}], "members": {"stepLabel": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChild", "line": 83, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "CdkStepLabel"}]}]}], "content": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ViewChild", "line": 86, "character": 3}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "TemplateRef", "line": 86, "character": 13}]}]}], "stepControl": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 89, "character": 3}}]}], "label": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 95, "character": 3}}]}], "editable": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 98, "character": 3}}]}], "optional": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 106, "character": 3}}]}], "completed": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 114, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 127, "character": 15}, "arguments": [{"__symbolic": "reference", "name": "CdkStepper"}]}]], "parameters": [{"__symbolic": "reference", "name": "CdkStepper"}]}], "select": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "ngOnChanges": [{"__symbolic": "method"}]}}, "CdkStepper": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 154, "character": 1}, "arguments": [{"selector": "[cdkStepper]", "exportAs": "cdkStepper"}]}], "members": {"_steps": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "ContentChildren", "line": 163, "character": 3}, "arguments": [{"__symbolic": "reference", "name": "CdkStep"}]}]}], "linear": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 169, "character": 3}}]}], "selectedIndex": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 175, "character": 3}}]}], "selected": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 199, "character": 3}}]}], "selectionChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 206, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 218, "character": 5}}], null], "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/bidi", "name": "Directionality", "line": 218, "character": 30}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 219, "character": 32}]}], "ngOnDestroy": [{"__symbolic": "method"}], "next": [{"__symbolic": "method"}], "previous": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "_getStepLabelId": [{"__symbolic": "method"}], "_getStepContentId": [{"__symbolic": "method"}], "_stateChanged": [{"__symbolic": "method"}], "_getAnimationDirection": [{"__symbolic": "method"}], "_getIndicatorType": [{"__symbolic": "method"}], "_emitStepperSelectionEvent": [{"__symbolic": "method"}], "_onKeydown": [{"__symbolic": "method"}], "_focusNextStep": [{"__symbolic": "method"}], "_focusPreviousStep": [{"__symbolic": "method"}], "_focusStep": [{"__symbolic": "method"}], "_anyControlsInvalidOrPending": [{"__symbolic": "method"}], "_layoutDirection": [{"__symbolic": "method"}]}}, "CdkStepLabel": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 10, "character": 1}, "arguments": [{"selector": "[cdkStepLabel]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}]}}, "CdkStepperNext": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 12, "character": 1}, "arguments": [{"selector": "button[cdkStepperNext]", "host": {"(click)": "_stepper.next()", "[type]": "type"}}]}], "members": {"type": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 21, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CdkStepper"}]}]}}, "CdkStepperPrevious": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 27, "character": 1}, "arguments": [{"selector": "button[cdkStepperPrevious]", "host": {"(click)": "_stepper.previous()", "[type]": "type"}}]}], "members": {"type": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 36, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "CdkStepper"}]}]}}, "CdkStepperModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 15, "character": 1}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/cdk/bidi", "name": "BidiModule", "line": 16, "character": 12}, {"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule", "line": 16, "character": 24}], "exports": [{"__symbolic": "reference", "name": "CdkStep"}, {"__symbolic": "reference", "name": "CdkStepper"}, {"__symbolic": "reference", "name": "CdkStepLabel"}, {"__symbolic": "reference", "name": "CdkStepperNext"}, {"__symbolic": "reference", "name": "CdkStepperPrevious"}], "declarations": [{"__symbolic": "reference", "name": "CdkStep"}, {"__symbolic": "reference", "name": "CdkStepper"}, {"__symbolic": "reference", "name": "CdkStepLabel"}, {"__symbolic": "reference", "name": "CdkStepperNext"}, {"__symbolic": "reference", "name": "CdkStepperPrevious"}]}]}], "members": {}}}, "origins": {"StepContentPositionState": "./stepper", "StepperOrientation": "./stepper", "StepperSelectionEvent": "./stepper", "CdkStep": "./stepper", "CdkStepper": "./stepper", "CdkStepLabel": "./step-label", "CdkStepperNext": "./stepper-button", "CdkStepperPrevious": "./stepper-button", "CdkStepperModule": "./stepper-module"}, "importAs": "@angular/cdk/stepper"}