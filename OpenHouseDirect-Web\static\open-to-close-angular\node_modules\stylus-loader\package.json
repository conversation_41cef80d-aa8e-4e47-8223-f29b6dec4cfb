{"_from": "stylus-loader@^3.0.1", "_id": "stylus-loader@3.0.2", "_inBundle": false, "_integrity": "sha512-+VomPdZ6a0razP+zinir61yZgpw2NfljeSsdUF5kJuEzlo3khXhY19Fn6l8QQz1GRJGtMCo8nG5C04ePyV7SUA==", "_location": "/stylus-loader", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "stylus-loader@^3.0.1", "name": "stylus-loader", "escapedName": "stylus-loader", "rawSpec": "^3.0.1", "saveSpec": null, "fetchSpec": "^3.0.1"}, "_requiredBy": ["/@angular/cli"], "_resolved": "https://registry.npmjs.org/stylus-loader/-/stylus-loader-3.0.2.tgz", "_shasum": "27a706420b05a38e038e7cacb153578d450513c6", "_spec": "stylus-loader@^3.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\@angular\\cli", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dontkry.com"}, "bugs": {"url": "https://github.com/shama/stylus-loader/issues"}, "bundleDependencies": false, "dependencies": {"loader-utils": "^1.0.2", "lodash.clonedeep": "^4.5.0", "when": "~3.6.x"}, "deprecated": false, "description": "Stylus loader for webpack", "devDependencies": {"benchmark": "^1.0.0", "css-loader": "^0.14.0", "mocha": "~2.1.0", "mocha-loader": "^1.0.0", "nib": "^1.0.4", "node-libs-browser": "^0.5.2", "raw-loader": "~0.5.1", "should": "~4.6.1", "style-loader": "^0.12.2", "stylus": ">=0.52.4", "testem": "^0.8.3", "webpack": "^2.2.0", "webpack-dev-server": "^2.0.0"}, "files": ["index.js", "lib/"], "homepage": "https://github.com/shama/stylus-loader#readme", "keywords": ["webpack", "loader", "stylus"], "license": "MIT", "main": "index.js", "name": "stylus-loader", "peerDependencies": {"stylus": ">=0.52.4"}, "repository": {"type": "git", "url": "git+ssh://**************/shama/stylus-loader.git"}, "scripts": {"test": "testem ci", "test-build": "webpack --config test/webpack.config.js --output-path=test/tmp --output-filename=bundle.js", "test-dev": "testem -l firefox", "test-one": "testem ci -l firefox"}, "version": "3.0.2"}