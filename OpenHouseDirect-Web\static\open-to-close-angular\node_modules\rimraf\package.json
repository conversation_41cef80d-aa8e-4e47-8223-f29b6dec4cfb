{"_from": "rimraf@^2.6.2", "_id": "rim<PERSON>f@2.7.1", "_inBundle": false, "_integrity": "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==", "_location": "/rimraf", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "rimraf@^2.6.2", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "^2.6.2", "saveSpec": null, "fetchSpec": "^2.6.2"}, "_requiredBy": ["/cacache", "/copy-concurrently", "/del", "/fstream", "/istanbul-lib-source-maps", "/karma", "/move-concurrently", "/node-gyp", "/protractor/del", "/protractor/webdriver-manager", "/selenium-webdriver", "/webdriver-js-extender/selenium-webdriver"], "_resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz", "_shasum": "35797f13a7fdadc566142c29d4f07ccad483e3ec", "_spec": "rimraf@^2.6.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\cacache", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"rimraf": "./bin.js"}, "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bundleDependencies": false, "dependencies": {"glob": "^7.1.3"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "description": "A deep deletion module for node (like `rm -rf`)", "devDependencies": {"mkdirp": "^0.5.1", "tap": "^12.1.1"}, "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "homepage": "https://github.com/isaacs/rimraf#readme", "license": "ISC", "main": "rimraf.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/isaacs/rimraf.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js"}, "version": "2.7.1"}