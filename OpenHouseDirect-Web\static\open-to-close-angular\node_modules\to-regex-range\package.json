{"_from": "to-regex-range@^2.1.0", "_id": "to-regex-range@2.1.1", "_inBundle": false, "_integrity": "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg==", "_location": "/to-regex-range", "_phantomChildren": {"kind-of": "3.2.2"}, "_requested": {"type": "range", "registry": true, "raw": "to-regex-range@^2.1.0", "name": "to-regex-range", "escapedName": "to-regex-range", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/http-proxy-middleware/fill-range", "/karma/fill-range", "/readdirp/fill-range", "/watchpack-chokidar2/fill-range", "/webpack-dev-server/fill-range"], "_resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz", "_shasum": "7c80c17b9dfebe599e27367e0d4dd5590141db38", "_spec": "to-regex-range@^2.1.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\readdirp\\node_modules\\fill-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "bundleDependencies": false, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "deprecated": false, "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/micromatch/to-regex-range", "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "license": "MIT", "main": "index.js", "name": "to-regex-range", "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["0-5", "0-9", "1-5", "1-9"]}, "version": "2.1.1"}