{"_from": "vlq@^0.2.2", "_id": "vlq@0.2.3", "_inBundle": false, "_integrity": "sha512-DRibZL6DsNhIgYQ+wNdWDL2SL3bKPlVrRiBqV5yuMm++op8W4kGFtaQfCs4KEJn0wBZcHVHJ3eoywX8983k1ow==", "_location": "/vlq", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vlq@^0.2.2", "name": "vlq", "escapedName": "vlq", "rawSpec": "^0.2.2", "saveSpec": null, "fetchSpec": "^0.2.2"}, "_requiredBy": ["/magic-string"], "_resolved": "https://registry.npmjs.org/vlq/-/vlq-0.2.3.tgz", "_shasum": "8f3e4328cf63b1540c0d67e1b2778386f8975b26", "_spec": "vlq@^0.2.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\magic-string", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/vlq/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Generate, and decode, base64 VLQ mappings for source maps and other uses", "devDependencies": {"eslint": "^3.19.0", "rollup": "^0.41.6"}, "files": ["README.md", "LICENSE", "src/vlq.js", "dist/vlq.js"], "homepage": "https://github.com/<PERSON>-<PERSON>/vlq#readme", "license": "MIT", "main": "dist/vlq.js", "module": "src/vlq.js", "name": "vlq", "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-Harris/vlq.git"}, "scripts": {"build": "rollup src/vlq.js -n vlq -f umd > dist/vlq.js", "lint": "eslint src", "prepublish": "npm test", "pretest": "npm run build", "test": "node test"}, "version": "0.2.3"}