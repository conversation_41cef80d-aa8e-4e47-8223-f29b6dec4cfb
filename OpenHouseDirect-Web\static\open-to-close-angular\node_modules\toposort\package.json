{"_from": "toposort@^1.0.0", "_id": "toposort@1.0.7", "_inBundle": false, "_integrity": "sha512-FclLrw8b9bMWf4QlCJuHBEVhSRsqDj6u3nIjAzPeJvgl//1hBlffdlk0MALceL14+koWEdU4ofRAXofbODxQzg==", "_location": "/toposort", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "toposort@^1.0.0", "name": "toposort", "escapedName": "toposort", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/html-webpack-plugin"], "_resolved": "https://registry.npmjs.org/toposort/-/toposort-1.0.7.tgz", "_shasum": "2e68442d9f64ec720b8cc89e6443ac6caa950029", "_spec": "toposort@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\html-webpack-plugin", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/marcelklehr/toposort/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Topological sort of directed ascyclic graphs (like dependecy lists)", "devDependencies": {"vows": "0.7.x"}, "homepage": "https://github.com/marcelklehr/toposort#readme", "keywords": ["topological", "sort", "sorting", "graphs", "graph", "dependency", "list", "dependencies", "acyclic"], "license": "MIT", "main": "index.js", "name": "toposort", "repository": {"type": "git", "url": "git+https://github.com/marcelklehr/toposort.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.7"}