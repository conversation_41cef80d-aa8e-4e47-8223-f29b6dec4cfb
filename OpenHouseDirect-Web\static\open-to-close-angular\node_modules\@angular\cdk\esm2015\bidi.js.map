{"version": 3, "file": "bidi.js", "sources": ["../../../src/cdk/bidi/index.ts", "../../../src/cdk/bidi/public-api.ts", "../../../src/cdk/bidi/bidi-module.ts", "../../../src/cdk/bidi/dir.ts", "../../../src/cdk/bidi/directionality.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport {Directionality, DIR_DOCUMENT, Direction} from './directionality';\nexport {Dir} from './dir';\nexport * from './bidi-module';\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {Dir} from './dir';\nimport {DIR_DOCUMENT, Directionality} from './directionality';\n\n\n@NgModule({\n  exports: [Dir],\n  declarations: [Dir],\n  providers: [\n    {provide: DIR_DOCUMENT, useExisting: DOCUMENT},\n    Directionality,\n  ]\n})\nexport class BidiModule { }\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  Output,\n  Input,\n  EventEmitter,\n  AfterContentInit,\n  OnDestroy,\n} from '@angular/core';\n\nimport {Direction, Directionality} from './directionality';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n@Directive({\n  selector: '[dir]',\n  providers: [{provide: Directionality, useExisting: Dir}],\n  host: {'[dir]': 'dir'},\n  exportAs: 'dir',\n})\nexport class Dir implements Directionality, AfterContentInit, OnDestroy {\n  _dir: Direction = 'ltr';\n\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Event emitted when the direction changes. */\n  @Output('dirChange') change = new EventEmitter<Direction>();\n\n  /** @docs-private */\n  @Input()\n  get dir(): Direction { return this._dir; }\n  set dir(v: Direction) {\n    const old = this._dir;\n    this._dir = v;\n    if (old !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n\n  /** Current layout direction of the element. */\n  get value(): Direction { return this.dir; }\n\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  EventEmitter,\n  Injectable,\n  Optional,\n  Inject,\n  InjectionToken,\n} from '@angular/core';\n\n\nexport type Direction = 'ltr' | 'rtl';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-brower because the unit tests\n * themselves use things like `querySelector` in test code.\n */\nexport const DIR_DOCUMENT = new InjectionToken<Document>('cdk-dir-doc');\n\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\n@Injectable()\nexport class Directionality {\n  /** The current 'ltr' or 'rtl' value. */\n  readonly value: Direction = 'ltr';\n\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  readonly change = new EventEmitter<Direction>();\n\n  constructor(@Optional() @Inject(DIR_DOCUMENT) _document?: any) {\n    if (_document) {\n      // TODO: handle 'auto' value -\n      // We still need to account for dir=\"auto\".\n      // It looks like HTMLElemenet.dir is also \"auto\" when that's set to the attribute,\n      // but getComputedStyle return either \"ltr\" or \"rtl\". avoiding getComputedStyle for now\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.value = (bodyDir || htmlDir || 'ltr') as Direction;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AIQA;;;;;;;;;;AAqBA,AAAO,MAAM,YAAY,GAAG,IAAI,cAAc,CAAW,aAAa,CAAC,CAAC;;;;;AAOxE,AAAA,MAAA,cAAA,CAAA;;;;IAOE,WAAF,CAAgD,SAAhD,EAAA;;;;QALA,IAAA,CAAA,KAAA,GAA8B,KAAK,CAAnC;;;;QAGA,IAAA,CAAA,MAAA,GAAoB,IAAI,YAAY,EAAa,CAAjD;QAGI,IAAI,SAAS,EAAE;;;;;YAKb,uBAAM,OAAO,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;YAC3D,uBAAM,OAAO,GAAG,SAAS,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,GAAG,IAAI,CAAC;YACjF,IAAI,CAAC,KAAK,sBAAI,OAAO,IAAI,OAAO,IAAI,KAAK,EAAc,CAAC;SACzD;KACF;;;IAlBH,EAAA,IAAA,EAAC,UAAU,EAAX;;;;IAQA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAe,QAAQ,EAAvB,EAAA,EAAA,IAAA,EAA2B,MAAM,EAAjC,IAAA,EAAA,CAAkC,YAAY,EAA9C,EAAA,EAAA,EAAA;;;;;;;;ADnCA,AASA;;;;;;AAcA,AAAA,MAAA,GAAA,CAAA;;QACA,IAAA,CAAA,IAAA,GAAoB,KAAK,CAAzB;;;;QAGA,IAAA,CAAA,cAAA,GAAoC,KAAK,CAAzC;;;;QAGA,IAAA,CAAA,MAAA,GAAgC,IAAI,YAAY,EAAa,CAA7D;;;;;;IAIA,IAAM,GAAG,GAAT,EAAyB,OAAO,IAAI,CAAC,IAAI,CAAC,EAA1C;;;;;IACE,IAAI,GAAG,CAAC,CAAY,EAAtB;QACI,uBAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;KACF;;;;;IAGD,IAAI,KAAK,GAAX,EAA2B,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE;;;;;IAG3C,kBAAkB,GAApB;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC5B;;;;IAED,WAAW,GAAb;QACI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB;;;IApCH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;gBACT,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,EAAC,CAAC;gBACxD,IAAI,EAAE,EAAC,OAAO,EAAE,KAAK,EAAC;gBACtB,QAAQ,EAAE,KAAK;aAChB,EAAD,EAAA;;;;;IAQA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAG,MAAM,EAAT,IAAA,EAAA,CAAU,WAAW,EAArB,EAAA,EAAA;IAGA,KAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,EAAA;;;;;;;;ADjCA,AACA,AACA,AACA,AAWA,AAAA,MAAA,UAAA,CAAA;;;IARA,EAAA,IAAA,EAAC,QAAQ,EAAT,IAAA,EAAA,CAAU;gBACR,OAAO,EAAE,CAAC,GAAG,CAAC;gBACd,YAAY,EAAE,CAAC,GAAG,CAAC;gBACnB,SAAS,EAAE;oBACT,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAC;oBAC9C,cAAc;iBACf;aACF,EAAD,EAAA;;;;;;;;GDbA,AACA,AACA,AAA8B;;;;;;;;GDN9B,AAA6B;;"}