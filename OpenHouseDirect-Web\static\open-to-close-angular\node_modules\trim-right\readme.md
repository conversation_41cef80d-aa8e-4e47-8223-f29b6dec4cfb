# trim-right [![Build Status](https://travis-ci.org/sindresorhus/trim-right.svg?branch=master)](https://travis-ci.org/sindresorhus/trim-right)

> Similar to [`String#trim()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/Trim) but removes only whitespace on the right


## Install

```
$ npm install --save trim-right
```


## Usage

```js
var trimRight = require('trim-right');

trimRight('  unicorn  ');
//=> '  unicorn'
```


## Related

- [`trim-left`](https://github.com/sindresorhus/trim-left) - Similar to `String#trim()` but removes only whitespace on the left


## License

MIT © [Sindre Sorhus](http://sindresorhus.com)
