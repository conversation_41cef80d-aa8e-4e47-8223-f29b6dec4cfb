/**
 * @license Angular v5.2.5
 * (c) 2010-2018 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/animations")):"function"==typeof define&&define.amd?define("@angular/animations/browser/testing",["exports","@angular/animations"],factory):factory((global.ng=global.ng||{},global.ng.animations=global.ng.animations||{},global.ng.animations.browser=global.ng.animations.browser||{},global.ng.animations.browser.testing={}),global.ng.animations)}(this,function(exports,_angular_animations){"use strict";function __extends(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}function containsVendorPrefix(prop){return"ebkit"==prop.substring(1,6)}function validateStyleProperty(prop){_CACHED_BODY||(_CACHED_BODY=getBodyNode()||{},_IS_WEBKIT=!!_CACHED_BODY.style&&"WebkitAppearance"in _CACHED_BODY.style);var result=!0;if(_CACHED_BODY.style&&!containsVendorPrefix(prop)&&!(result=prop in _CACHED_BODY.style)&&_IS_WEBKIT){result="Webkit"+prop.charAt(0).toUpperCase()+prop.substr(1)in _CACHED_BODY.style}return result}function getBodyNode(){return"undefined"!=typeof document?document.body:null}function allowPreviousPlayerStylesMerge(duration,delay){return 0===duration||0===delay}var extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])},_contains=function(elm1,elm2){return!1},_matches=function(element,selector){return!1},_query=function(element,selector,multi){return[]};if("undefined"!=typeof Element){if(_contains=function(elm1,elm2){return elm1.contains(elm2)},Element.prototype.matches)_matches=function(element,selector){return element.matches(selector)};else{var proto=Element.prototype,fn_1=proto.matchesSelector||proto.mozMatchesSelector||proto.msMatchesSelector||proto.oMatchesSelector||proto.webkitMatchesSelector;fn_1&&(_matches=function(element,selector){return fn_1.apply(element,[selector])})}_query=function(element,selector,multi){var results=[];if(multi)results.push.apply(results,element.querySelectorAll(selector));else{var elm=element.querySelector(selector);elm&&results.push(elm)}return results}}var _CACHED_BODY=null,_IS_WEBKIT=!1,matchesElement=_matches,containsElement=_contains,invokeQuery=_query,MockAnimationDriver=function(){function MockAnimationDriver(){}return MockAnimationDriver.prototype.validateStyleProperty=function(prop){return validateStyleProperty(prop)},MockAnimationDriver.prototype.matchesElement=function(element,selector){return matchesElement(element,selector)},MockAnimationDriver.prototype.containsElement=function(elm1,elm2){return containsElement(elm1,elm2)},MockAnimationDriver.prototype.query=function(element,selector,multi){return invokeQuery(element,selector,multi)},MockAnimationDriver.prototype.computeStyle=function(element,prop,defaultValue){return defaultValue||""},MockAnimationDriver.prototype.animate=function(element,keyframes,duration,delay,easing,previousPlayers){void 0===previousPlayers&&(previousPlayers=[]);var player=new MockAnimationPlayer(element,keyframes,duration,delay,easing,previousPlayers);return MockAnimationDriver.log.push(player),player},MockAnimationDriver.log=[],MockAnimationDriver}(),MockAnimationPlayer=function(_super){function MockAnimationPlayer(element,keyframes,duration,delay,easing,previousPlayers){var _this=_super.call(this)||this;return _this.element=element,_this.keyframes=keyframes,_this.duration=duration,_this.delay=delay,_this.easing=easing,_this.previousPlayers=previousPlayers,_this.__finished=!1,_this.__started=!1,_this.previousStyles={},_this._onInitFns=[],_this.currentSnapshot={},allowPreviousPlayerStylesMerge(duration,delay)&&previousPlayers.forEach(function(player){if(player instanceof MockAnimationPlayer){var styles_1=player.currentSnapshot;Object.keys(styles_1).forEach(function(prop){return _this.previousStyles[prop]=styles_1[prop]})}}),_this.totalTime=delay+duration,_this}return __extends(MockAnimationPlayer,_super),MockAnimationPlayer.prototype.onInit=function(fn){this._onInitFns.push(fn)},MockAnimationPlayer.prototype.init=function(){_super.prototype.init.call(this),this._onInitFns.forEach(function(fn){return fn()}),this._onInitFns=[]},MockAnimationPlayer.prototype.finish=function(){_super.prototype.finish.call(this),this.__finished=!0},MockAnimationPlayer.prototype.destroy=function(){_super.prototype.destroy.call(this),this.__finished=!0},MockAnimationPlayer.prototype.triggerMicrotask=function(){},MockAnimationPlayer.prototype.play=function(){_super.prototype.play.call(this),this.__started=!0},MockAnimationPlayer.prototype.hasStarted=function(){return this.__started},MockAnimationPlayer.prototype.beforeDestroy=function(){var _this=this,captures={};Object.keys(this.previousStyles).forEach(function(prop){captures[prop]=_this.previousStyles[prop]}),this.hasStarted()&&this.keyframes.forEach(function(kf){Object.keys(kf).forEach(function(prop){"offset"!=prop&&(captures[prop]=_this.__finished?kf[prop]:_angular_animations.AUTO_STYLE)})}),this.currentSnapshot=captures},MockAnimationPlayer}(_angular_animations.NoopAnimationPlayer);exports.MockAnimationDriver=MockAnimationDriver,exports.MockAnimationPlayer=MockAnimationPlayer,Object.defineProperty(exports,"__esModule",{value:!0})});
//# sourceMappingURL=animations-browser-testing.umd.min.js.map