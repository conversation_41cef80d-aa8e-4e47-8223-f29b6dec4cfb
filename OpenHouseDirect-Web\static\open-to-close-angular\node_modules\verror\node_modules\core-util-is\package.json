{"_from": "core-util-is@1.0.2", "_id": "core-util-is@1.0.2", "_inBundle": false, "_integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "_location": "/verror/core-util-is", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "core-util-is@1.0.2", "name": "core-util-is", "escapedName": "core-util-is", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/verror"], "_resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "_shasum": "b5fd54220aa2bc5ab57aab7140c940754503c1a7", "_spec": "core-util-is@1.0.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\verror", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The `util.is*` functions introduced in Node v0.12.", "devDependencies": {"tap": "^2.3.0"}, "homepage": "https://github.com/isaacs/core-util-is#readme", "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "license": "MIT", "main": "lib/util.js", "name": "core-util-is", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is.git"}, "scripts": {"test": "tap test.js"}, "version": "1.0.2"}