{"_from": "v8flags@^3.0.0", "_id": "v8flags@3.2.0", "_inBundle": false, "_integrity": "sha512-mH8etigqMfiGWdeXpaaqGfs6BndypxusHHcv2qSHyZkGEznCd/qAXCWWRzeowtL54147cktFOC4P5y+kl8d8Jg==", "_location": "/v8flags", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "v8flags@^3.0.0", "name": "v8flags", "escapedName": "v8flags", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/ts-node"], "_resolved": "https://registry.npmjs.org/v8flags/-/v8flags-3.2.0.tgz", "_shasum": "b243e3b4dfd731fa774e7492128109a0fe66d656", "_spec": "v8flags@^3.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\ts-node", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/v8flags/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"homedir-polyfill": "^1.0.1"}, "deprecated": false, "description": "Get available v8 and Node.js flags.", "devDependencies": {"async": "^2.5.0", "eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3", "proxyquire": "^1.8.0"}, "engines": {"node": ">= 0.10"}, "files": ["index.js", "config-path.js", "LICENSE"], "homepage": "https://github.com/gulpjs/v8flags#readme", "keywords": ["v8 flags", "harmony flags"], "license": "MIT", "main": "index.js", "name": "v8flags", "repository": {"type": "git", "url": "git+https://github.com/gulpjs/v8flags.git"}, "scripts": {"cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls", "lint": "eslint .", "pretest": "npm run lint", "test": "mocha --async-only"}, "version": "3.2.0"}