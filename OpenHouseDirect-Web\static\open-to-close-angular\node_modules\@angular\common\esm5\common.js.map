{"version": 3, "file": "common.js", "sources": ["../../../packages/common/esm5/src/location/platform_location.js", "../../../packages/common/esm5/src/location/location_strategy.js", "../../../packages/common/esm5/src/location/location.js", "../../../packages/common/esm5/src/location/hash_location_strategy.js", "../../../packages/common/esm5/src/location/path_location_strategy.js", "../../../packages/common/esm5/src/location/index.js", "../../../packages/common/esm5/src/i18n/currencies.js", "../../../packages/common/esm5/src/i18n/locale_en.js", "../../../packages/common/esm5/src/i18n/locale_data.js", "../../../packages/common/esm5/src/i18n/locale_data_api.js", "../../../packages/common/esm5/src/i18n/localization.js", "../../../packages/common/esm5/src/cookie.js", "../../../packages/common/esm5/src/directives/ng_class.js", "../../../packages/common/esm5/src/directives/ng_component_outlet.js", "../../../packages/common/esm5/src/directives/ng_for_of.js", "../../../packages/common/esm5/src/directives/ng_if.js", "../../../packages/common/esm5/src/directives/ng_switch.js", "../../../packages/common/esm5/src/directives/ng_plural.js", "../../../packages/common/esm5/src/directives/ng_style.js", "../../../packages/common/esm5/src/directives/ng_template_outlet.js", "../../../packages/common/esm5/src/directives/index.js", "../../../packages/common/esm5/src/i18n/format_date.js", "../../../packages/common/esm5/src/pipes/invalid_pipe_argument_error.js", "../../../packages/common/esm5/src/pipes/date_pipe.js", "../../../packages/common/esm5/src/pipes/deprecated/intl.js", "../../../packages/common/esm5/src/pipes/deprecated/date_pipe.js", "../../../packages/common/esm5/src/i18n/format_number.js", "../../../packages/common/esm5/src/pipes/deprecated/number_pipe.js", "../../../packages/common/esm5/src/pipes/deprecated/index.js", "../../../packages/common/esm5/src/pipes/async_pipe.js", "../../../packages/common/esm5/src/pipes/case_conversion_pipes.js", "../../../packages/common/esm5/src/pipes/i18n_plural_pipe.js", "../../../packages/common/esm5/src/pipes/i18n_select_pipe.js", "../../../packages/common/esm5/src/pipes/json_pipe.js", "../../../packages/common/esm5/src/pipes/number_pipe.js", "../../../packages/common/esm5/src/pipes/slice_pipe.js", "../../../packages/common/esm5/src/pipes/index.js", "../../../packages/common/esm5/src/common_module.js", "../../../packages/common/esm5/src/dom_tokens.js", "../../../packages/common/esm5/src/platform_id.js", "../../../packages/common/esm5/src/version.js", "../../../packages/common/esm5/src/common.js", "../../../packages/common/esm5/public_api.js", "../../../packages/common/esm5/common.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { InjectionToken } from '@angular/core';\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM apis, which allows the Router to be platform\n * agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that angular supports. For example, `\\@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `\\@angular/platform-webworker` provides\n * one suitable for use with web workers.\n *\n * The `PlatformLocation` class is used directly by all implementations of {\\@link LocationStrategy}\n * when they need to interact with the DOM apis like pushState, popState, etc...\n *\n * {\\@link LocationStrategy} in turn is used by the {\\@link Location} service which is used directly\n * by the {\\@link Router} in order to navigate between routes. Since all interactions between {\\@link\n * Router} /\n * {\\@link Location} / {\\@link LocationStrategy} and DOM apis flow through the `PlatformLocation`\n * class they are all platform independent.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM apis, which allows the Router to be platform\n * agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that angular supports. For example, `\\@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `\\@angular/platform-webworker` provides\n * one suitable for use with web workers.\n *\n * The `PlatformLocation` class is used directly by all implementations of {\\@link LocationStrategy}\n * when they need to interact with the DOM apis like pushState, popState, etc...\n *\n * {\\@link LocationStrategy} in turn is used by the {\\@link Location} service which is used directly\n * by the {\\@link Router} in order to navigate between routes. Since all interactions between {\\@link\n * Router} /\n * {\\@link Location} / {\\@link LocationStrategy} and DOM apis flow through the `PlatformLocation`\n * class they are all platform independent.\n *\n * \\@stable\n * @abstract\n */\nPlatformLocation = /** @class */ (function () {\n    function PlatformLocation() {\n    }\n    return PlatformLocation;\n}());\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {\\@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM apis, which allows the Router to be platform\n * agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that angular supports. For example, `\\@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `\\@angular/platform-webworker` provides\n * one suitable for use with web workers.\n *\n * The `PlatformLocation` class is used directly by all implementations of {\\@link LocationStrategy}\n * when they need to interact with the DOM apis like pushState, popState, etc...\n *\n * {\\@link LocationStrategy} in turn is used by the {\\@link Location} service which is used directly\n * by the {\\@link Router} in order to navigate between routes. Since all interactions between {\\@link\n * Router} /\n * {\\@link Location} / {\\@link LocationStrategy} and DOM apis flow through the `PlatformLocation`\n * class they are all platform independent.\n *\n * \\@stable\n * @abstract\n */\nexport { PlatformLocation };\nfunction PlatformLocation_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @return {?}\n     */\n    PlatformLocation.prototype.getBaseHrefFromDOM = function () { };\n    /**\n     * @abstract\n     * @param {?} fn\n     * @return {?}\n     */\n    PlatformLocation.prototype.onPopState = function (fn) { };\n    /**\n     * @abstract\n     * @param {?} fn\n     * @return {?}\n     */\n    PlatformLocation.prototype.onHashChange = function (fn) { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    PlatformLocation.prototype.pathname = function () { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    PlatformLocation.prototype.search = function () { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    PlatformLocation.prototype.hash = function () { };\n    /**\n     * @abstract\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @return {?}\n     */\n    PlatformLocation.prototype.replaceState = function (state, title, url) { };\n    /**\n     * @abstract\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @return {?}\n     */\n    PlatformLocation.prototype.pushState = function (state, title, url) { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    PlatformLocation.prototype.forward = function () { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    PlatformLocation.prototype.back = function () { };\n}\n/**\n * \\@whatItDoes indicates when a location is initialized\n * \\@experimental\n */\nexport var /** @type {?} */ LOCATION_INITIALIZED = new InjectionToken('Location Initialized');\n/**\n * A serializable version of the event from onPopState or onHashChange\n *\n * \\@experimental\n * @record\n */\nexport function LocationChangeEvent() { }\nfunction LocationChangeEvent_tsickle_Closure_declarations() {\n    /** @type {?} */\n    LocationChangeEvent.prototype.type;\n}\n/**\n * \\@experimental\n * @record\n */\nexport function LocationChangeListener() { }\nfunction LocationChangeListener_tsickle_Closure_declarations() {\n    /* TODO: handle strange member:\n    (e: LocationChangeEvent): any;\n    */\n}\n//# sourceMappingURL=platform_location.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { InjectionToken } from '@angular/core';\n/**\n * `LocationStrategy` is responsible for representing and reading route state\n * from the browser's URL. Angular provides two strategies:\n * {\\@link HashLocationStrategy} and {\\@link PathLocationStrategy}.\n *\n * This is used under the hood of the {\\@link Location} service.\n *\n * Applications should use the {\\@link Router} or {\\@link Location} services to\n * interact with application route state.\n *\n * For instance, {\\@link HashLocationStrategy} produces URLs like\n * `http://example.com#/foo`, and {\\@link PathLocationStrategy} produces\n * `http://example.com/foo` as an equivalent URL.\n *\n * See these two classes for more.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * `LocationStrategy` is responsible for representing and reading route state\n * from the browser's URL. <PERSON>ular provides two strategies:\n * {\\@link HashLocationStrategy} and {\\@link PathLocationStrategy}.\n *\n * This is used under the hood of the {\\@link Location} service.\n *\n * Applications should use the {\\@link Router} or {\\@link Location} services to\n * interact with application route state.\n *\n * For instance, {\\@link HashLocationStrategy} produces URLs like\n * `http://example.com#/foo`, and {\\@link PathLocationStrategy} produces\n * `http://example.com/foo` as an equivalent URL.\n *\n * See these two classes for more.\n *\n * \\@stable\n * @abstract\n */\nLocationStrategy = /** @class */ (function () {\n    function LocationStrategy() {\n    }\n    return LocationStrategy;\n}());\n/**\n * `LocationStrategy` is responsible for representing and reading route state\n * from the browser's URL. Angular provides two strategies:\n * {\\@link HashLocationStrategy} and {\\@link PathLocationStrategy}.\n *\n * This is used under the hood of the {\\@link Location} service.\n *\n * Applications should use the {\\@link Router} or {\\@link Location} services to\n * interact with application route state.\n *\n * For instance, {\\@link HashLocationStrategy} produces URLs like\n * `http://example.com#/foo`, and {\\@link PathLocationStrategy} produces\n * `http://example.com/foo` as an equivalent URL.\n *\n * See these two classes for more.\n *\n * \\@stable\n * @abstract\n */\nexport { LocationStrategy };\nfunction LocationStrategy_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    LocationStrategy.prototype.path = function (includeHash) { };\n    /**\n     * @abstract\n     * @param {?} internal\n     * @return {?}\n     */\n    LocationStrategy.prototype.prepareExternalUrl = function (internal) { };\n    /**\n     * @abstract\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @param {?} queryParams\n     * @return {?}\n     */\n    LocationStrategy.prototype.pushState = function (state, title, url, queryParams) { };\n    /**\n     * @abstract\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @param {?} queryParams\n     * @return {?}\n     */\n    LocationStrategy.prototype.replaceState = function (state, title, url, queryParams) { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    LocationStrategy.prototype.forward = function () { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    LocationStrategy.prototype.back = function () { };\n    /**\n     * @abstract\n     * @param {?} fn\n     * @return {?}\n     */\n    LocationStrategy.prototype.onPopState = function (fn) { };\n    /**\n     * @abstract\n     * @return {?}\n     */\n    LocationStrategy.prototype.getBaseHref = function () { };\n}\n/**\n * The `APP_BASE_HREF` token represents the base href to be used with the\n * {\\@link PathLocationStrategy}.\n *\n * If you're using {\\@link PathLocationStrategy}, you must provide a provider to a string\n * representing the URL prefix that should be preserved when generating and recognizing\n * URLs.\n *\n * ### Example\n *\n * ```typescript\n * import {Component, NgModule} from '\\@angular/core';\n * import {APP_BASE_HREF} from '\\@angular/common';\n *\n * \\@NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * \\@stable\n */\nexport var /** @type {?} */ APP_BASE_HREF = new InjectionToken('appBaseHref');\n//# sourceMappingURL=location_strategy.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { EventEmitter, Injectable } from '@angular/core';\nimport { LocationStrategy } from './location_strategy';\n/**\n * \\@experimental\n * @record\n */\nexport function PopStateEvent() { }\nfunction PopStateEvent_tsickle_Closure_declarations() {\n    /** @type {?|undefined} */\n    PopStateEvent.prototype.pop;\n    /** @type {?|undefined} */\n    PopStateEvent.prototype.type;\n    /** @type {?|undefined} */\n    PopStateEvent.prototype.url;\n}\n/**\n * \\@whatItDoes `Location` is a service that applications can use to interact with a browser's URL.\n * \\@description\n * Depending on which {\\@link LocationStrategy} is used, `Location` will either persist\n * to the URL's path or the URL's hash segment.\n *\n * Note: it's better to use {\\@link Router#navigate} service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n * \\@stable\n */\nvar Location = /** @class */ (function () {\n    function Location(platformStrategy) {\n        var _this = this;\n        /**\n         * \\@internal\n         */\n        this._subject = new EventEmitter();\n        this._platformStrategy = platformStrategy;\n        var /** @type {?} */ browserBaseHref = this._platformStrategy.getBaseHref();\n        this._baseHref = Location.stripTrailingSlash(_stripIndexHtml(browserBaseHref));\n        this._platformStrategy.onPopState(function (ev) {\n            _this._subject.emit({\n                'url': _this.path(true),\n                'pop': true,\n                'type': ev.type,\n            });\n        });\n    }\n    /**\n     * Returns the normalized URL path.\n     */\n    // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n    // removed.\n    /**\n     * Returns the normalized URL path.\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    Location.prototype.path = /**\n     * Returns the normalized URL path.\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    function (includeHash) {\n        if (includeHash === void 0) { includeHash = false; }\n        return this.normalize(this._platformStrategy.path(includeHash));\n    };\n    /**\n     * Normalizes the given path and compares to the current normalized path.\n     */\n    /**\n     * Normalizes the given path and compares to the current normalized path.\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    Location.prototype.isCurrentPathEqualTo = /**\n     * Normalizes the given path and compares to the current normalized path.\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    function (path, query) {\n        if (query === void 0) { query = ''; }\n        return this.path() == this.normalize(path + Location.normalizeQueryParams(query));\n    };\n    /**\n     * Given a string representing a URL, returns the normalized URL path without leading or\n     * trailing slashes.\n     */\n    /**\n     * Given a string representing a URL, returns the normalized URL path without leading or\n     * trailing slashes.\n     * @param {?} url\n     * @return {?}\n     */\n    Location.prototype.normalize = /**\n     * Given a string representing a URL, returns the normalized URL path without leading or\n     * trailing slashes.\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) {\n        return Location.stripTrailingSlash(_stripBaseHref(this._baseHref, _stripIndexHtml(url)));\n    };\n    /**\n     * Given a string representing a URL, returns the platform-specific external URL path.\n     * If the given URL doesn't begin with a leading slash (`'/'`), this method adds one\n     * before normalizing. This method will also add a hash if `HashLocationStrategy` is\n     * used, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n     */\n    /**\n     * Given a string representing a URL, returns the platform-specific external URL path.\n     * If the given URL doesn't begin with a leading slash (`'/'`), this method adds one\n     * before normalizing. This method will also add a hash if `HashLocationStrategy` is\n     * used, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n     * @param {?} url\n     * @return {?}\n     */\n    Location.prototype.prepareExternalUrl = /**\n     * Given a string representing a URL, returns the platform-specific external URL path.\n     * If the given URL doesn't begin with a leading slash (`'/'`), this method adds one\n     * before normalizing. This method will also add a hash if `HashLocationStrategy` is\n     * used, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) {\n        if (url && url[0] !== '/') {\n            url = '/' + url;\n        }\n        return this._platformStrategy.prepareExternalUrl(url);\n    };\n    // TODO: rename this method to pushState\n    /**\n     * Changes the browsers URL to the normalized version of the given URL, and pushes a\n     * new item onto the platform's history.\n     */\n    /**\n     * Changes the browsers URL to the normalized version of the given URL, and pushes a\n     * new item onto the platform's history.\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    Location.prototype.go = /**\n     * Changes the browsers URL to the normalized version of the given URL, and pushes a\n     * new item onto the platform's history.\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    function (path, query) {\n        if (query === void 0) { query = ''; }\n        this._platformStrategy.pushState(null, '', path, query);\n    };\n    /**\n     * Changes the browsers URL to the normalized version of the given URL, and replaces\n     * the top item on the platform's history stack.\n     */\n    /**\n     * Changes the browsers URL to the normalized version of the given URL, and replaces\n     * the top item on the platform's history stack.\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    Location.prototype.replaceState = /**\n     * Changes the browsers URL to the normalized version of the given URL, and replaces\n     * the top item on the platform's history stack.\n     * @param {?} path\n     * @param {?=} query\n     * @return {?}\n     */\n    function (path, query) {\n        if (query === void 0) { query = ''; }\n        this._platformStrategy.replaceState(null, '', path, query);\n    };\n    /**\n     * Navigates forward in the platform's history.\n     */\n    /**\n     * Navigates forward in the platform's history.\n     * @return {?}\n     */\n    Location.prototype.forward = /**\n     * Navigates forward in the platform's history.\n     * @return {?}\n     */\n    function () { this._platformStrategy.forward(); };\n    /**\n     * Navigates back in the platform's history.\n     */\n    /**\n     * Navigates back in the platform's history.\n     * @return {?}\n     */\n    Location.prototype.back = /**\n     * Navigates back in the platform's history.\n     * @return {?}\n     */\n    function () { this._platformStrategy.back(); };\n    /**\n     * Subscribe to the platform's `popState` events.\n     */\n    /**\n     * Subscribe to the platform's `popState` events.\n     * @param {?} onNext\n     * @param {?=} onThrow\n     * @param {?=} onReturn\n     * @return {?}\n     */\n    Location.prototype.subscribe = /**\n     * Subscribe to the platform's `popState` events.\n     * @param {?} onNext\n     * @param {?=} onThrow\n     * @param {?=} onReturn\n     * @return {?}\n     */\n    function (onNext, onThrow, onReturn) {\n        return this._subject.subscribe({ next: onNext, error: onThrow, complete: onReturn });\n    };\n    /**\n     * Given a string of url parameters, prepend with '?' if needed, otherwise return parameters as\n     * is.\n     * @param {?} params\n     * @return {?}\n     */\n    Location.normalizeQueryParams = /**\n     * Given a string of url parameters, prepend with '?' if needed, otherwise return parameters as\n     * is.\n     * @param {?} params\n     * @return {?}\n     */\n    function (params) {\n        return params && params[0] !== '?' ? '?' + params : params;\n    };\n    /**\n     * Given 2 parts of a url, join them with a slash if needed.\n     * @param {?} start\n     * @param {?} end\n     * @return {?}\n     */\n    Location.joinWithSlash = /**\n     * Given 2 parts of a url, join them with a slash if needed.\n     * @param {?} start\n     * @param {?} end\n     * @return {?}\n     */\n    function (start, end) {\n        if (start.length == 0) {\n            return end;\n        }\n        if (end.length == 0) {\n            return start;\n        }\n        var /** @type {?} */ slashes = 0;\n        if (start.endsWith('/')) {\n            slashes++;\n        }\n        if (end.startsWith('/')) {\n            slashes++;\n        }\n        if (slashes == 2) {\n            return start + end.substring(1);\n        }\n        if (slashes == 1) {\n            return start + end;\n        }\n        return start + '/' + end;\n    };\n    /**\n     * If url has a trailing slash, remove it, otherwise return url as is. This\n     * method looks for the first occurence of either #, ?, or the end of the\n     * line as `/` characters after any of these should not be replaced.\n     * @param {?} url\n     * @return {?}\n     */\n    Location.stripTrailingSlash = /**\n     * If url has a trailing slash, remove it, otherwise return url as is. This\n     * method looks for the first occurence of either #, ?, or the end of the\n     * line as `/` characters after any of these should not be replaced.\n     * @param {?} url\n     * @return {?}\n     */\n    function (url) {\n        var /** @type {?} */ match = url.match(/#|\\?|$/);\n        var /** @type {?} */ pathEndIdx = match && match.index || url.length;\n        var /** @type {?} */ droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n        return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n    };\n    Location.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    Location.ctorParameters = function () { return [\n        { type: LocationStrategy, },\n    ]; };\n    return Location;\n}());\nexport { Location };\nfunction Location_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    Location.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    Location.ctorParameters;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    Location.prototype._subject;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    Location.prototype._baseHref;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    Location.prototype._platformStrategy;\n}\n/**\n * @param {?} baseHref\n * @param {?} url\n * @return {?}\n */\nfunction _stripBaseHref(baseHref, url) {\n    return baseHref && url.startsWith(baseHref) ? url.substring(baseHref.length) : url;\n}\n/**\n * @param {?} url\n * @return {?}\n */\nfunction _stripIndexHtml(url) {\n    return url.replace(/\\/index.html$/, '');\n}\n//# sourceMappingURL=location.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport * as tslib_1 from \"tslib\";\nimport { Inject, Injectable, Optional } from '@angular/core';\nimport { Location } from './location';\nimport { APP_BASE_HREF, LocationStrategy } from './location_strategy';\nimport { PlatformLocation } from './platform_location';\n/**\n * \\@whatItDoes Use URL hash for storing application location data.\n * \\@description\n * `HashLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n *\n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n *\n * ### Example\n *\n * {\\@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n *\n * \\@stable\n */\nvar HashLocationStrategy = /** @class */ (function (_super) {\n    tslib_1.__extends(HashLocationStrategy, _super);\n    function HashLocationStrategy(_platformLocation, _baseHref) {\n        var _this = _super.call(this) || this;\n        _this._platformLocation = _platformLocation;\n        _this._baseHref = '';\n        if (_baseHref != null) {\n            _this._baseHref = _baseHref;\n        }\n        return _this;\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.onPopState = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        this._platformLocation.onPopState(fn);\n        this._platformLocation.onHashChange(fn);\n    };\n    /**\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.getBaseHref = /**\n     * @return {?}\n     */\n    function () { return this._baseHref; };\n    /**\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.path = /**\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    function (includeHash) {\n        if (includeHash === void 0) { includeHash = false; }\n        // the hash value is always prefixed with a `#`\n        // and if it is empty then it will stay empty\n        var /** @type {?} */ path = this._platformLocation.hash;\n        if (path == null)\n            path = '#';\n        return path.length > 0 ? path.substring(1) : path;\n    };\n    /**\n     * @param {?} internal\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.prepareExternalUrl = /**\n     * @param {?} internal\n     * @return {?}\n     */\n    function (internal) {\n        var /** @type {?} */ url = Location.joinWithSlash(this._baseHref, internal);\n        return url.length > 0 ? ('#' + url) : url;\n    };\n    /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} path\n     * @param {?} queryParams\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.pushState = /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} path\n     * @param {?} queryParams\n     * @return {?}\n     */\n    function (state, title, path, queryParams) {\n        var /** @type {?} */ url = this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n        if (url.length == 0) {\n            url = this._platformLocation.pathname;\n        }\n        this._platformLocation.pushState(state, title, url);\n    };\n    /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} path\n     * @param {?} queryParams\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.replaceState = /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} path\n     * @param {?} queryParams\n     * @return {?}\n     */\n    function (state, title, path, queryParams) {\n        var /** @type {?} */ url = this.prepareExternalUrl(path + Location.normalizeQueryParams(queryParams));\n        if (url.length == 0) {\n            url = this._platformLocation.pathname;\n        }\n        this._platformLocation.replaceState(state, title, url);\n    };\n    /**\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.forward = /**\n     * @return {?}\n     */\n    function () { this._platformLocation.forward(); };\n    /**\n     * @return {?}\n     */\n    HashLocationStrategy.prototype.back = /**\n     * @return {?}\n     */\n    function () { this._platformLocation.back(); };\n    HashLocationStrategy.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    HashLocationStrategy.ctorParameters = function () { return [\n        { type: PlatformLocation, },\n        { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF,] },] },\n    ]; };\n    return HashLocationStrategy;\n}(LocationStrategy));\nexport { HashLocationStrategy };\nfunction HashLocationStrategy_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HashLocationStrategy.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HashLocationStrategy.ctorParameters;\n    /** @type {?} */\n    HashLocationStrategy.prototype._baseHref;\n    /** @type {?} */\n    HashLocationStrategy.prototype._platformLocation;\n}\n//# sourceMappingURL=hash_location_strategy.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport * as tslib_1 from \"tslib\";\nimport { Inject, Injectable, Optional } from '@angular/core';\nimport { Location } from './location';\nimport { APP_BASE_HREF, LocationStrategy } from './location_strategy';\nimport { PlatformLocation } from './platform_location';\n/**\n * \\@whatItDoes Use URL for storing application location data.\n * \\@description\n * `PathLocationStrategy` is a {\\@link LocationStrategy} used to configure the\n * {\\@link Location} service to represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you must provide a {\\@link APP_BASE_HREF}\n * or add a base element to the document. This URL prefix that will be preserved\n * when generating and recognizing URLs.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Similarly, if you add `<base href='/my/app'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * ### Example\n *\n * {\\@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * \\@stable\n */\nvar PathLocationStrategy = /** @class */ (function (_super) {\n    tslib_1.__extends(PathLocationStrategy, _super);\n    function PathLocationStrategy(_platformLocation, href) {\n        var _this = _super.call(this) || this;\n        _this._platformLocation = _platformLocation;\n        if (href == null) {\n            href = _this._platformLocation.getBaseHrefFromDOM();\n        }\n        if (href == null) {\n            throw new Error(\"No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.\");\n        }\n        _this._baseHref = href;\n        return _this;\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.onPopState = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        this._platformLocation.onPopState(fn);\n        this._platformLocation.onHashChange(fn);\n    };\n    /**\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.getBaseHref = /**\n     * @return {?}\n     */\n    function () { return this._baseHref; };\n    /**\n     * @param {?} internal\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.prepareExternalUrl = /**\n     * @param {?} internal\n     * @return {?}\n     */\n    function (internal) {\n        return Location.joinWithSlash(this._baseHref, internal);\n    };\n    /**\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.path = /**\n     * @param {?=} includeHash\n     * @return {?}\n     */\n    function (includeHash) {\n        if (includeHash === void 0) { includeHash = false; }\n        var /** @type {?} */ pathname = this._platformLocation.pathname +\n            Location.normalizeQueryParams(this._platformLocation.search);\n        var /** @type {?} */ hash = this._platformLocation.hash;\n        return hash && includeHash ? \"\" + pathname + hash : pathname;\n    };\n    /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @param {?} queryParams\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.pushState = /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @param {?} queryParams\n     * @return {?}\n     */\n    function (state, title, url, queryParams) {\n        var /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n        this._platformLocation.pushState(state, title, externalUrl);\n    };\n    /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @param {?} queryParams\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.replaceState = /**\n     * @param {?} state\n     * @param {?} title\n     * @param {?} url\n     * @param {?} queryParams\n     * @return {?}\n     */\n    function (state, title, url, queryParams) {\n        var /** @type {?} */ externalUrl = this.prepareExternalUrl(url + Location.normalizeQueryParams(queryParams));\n        this._platformLocation.replaceState(state, title, externalUrl);\n    };\n    /**\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.forward = /**\n     * @return {?}\n     */\n    function () { this._platformLocation.forward(); };\n    /**\n     * @return {?}\n     */\n    PathLocationStrategy.prototype.back = /**\n     * @return {?}\n     */\n    function () { this._platformLocation.back(); };\n    PathLocationStrategy.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    PathLocationStrategy.ctorParameters = function () { return [\n        { type: PlatformLocation, },\n        { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [APP_BASE_HREF,] },] },\n    ]; };\n    return PathLocationStrategy;\n}(LocationStrategy));\nexport { PathLocationStrategy };\nfunction PathLocationStrategy_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    PathLocationStrategy.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    PathLocationStrategy.ctorParameters;\n    /** @type {?} */\n    PathLocationStrategy.prototype._baseHref;\n    /** @type {?} */\n    PathLocationStrategy.prototype._platformLocation;\n}\n//# sourceMappingURL=path_location_strategy.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport { PlatformLocation, LOCATION_INITIALIZED } from './platform_location';\nexport { LocationStrategy, APP_BASE_HREF } from './location_strategy';\nexport { HashLocationStrategy } from './hash_location_strategy';\nexport { PathLocationStrategy } from './path_location_strategy';\nexport { Location } from './location';\n//# sourceMappingURL=index.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n/**\n * \\@internal\n */\nexport var /** @type {?} */ CURRENCIES = {\n    'AOA': [, 'Kz'],\n    'ARS': [, '$'],\n    'AUD': ['A$', '$'],\n    'BAM': [, 'KM'],\n    'BBD': [, '$'],\n    'BDT': [, '৳'],\n    'BMD': [, '$'],\n    'BND': [, '$'],\n    'BOB': [, 'Bs'],\n    'BRL': ['R$'],\n    'BSD': [, '$'],\n    'BWP': [, 'P'],\n    'BYN': [, 'р.'],\n    'BZD': [, '$'],\n    'CAD': ['CA$', '$'],\n    'CLP': [, '$'],\n    'CNY': ['CN¥', '¥'],\n    'COP': [, '$'],\n    'CRC': [, '₡'],\n    'CUC': [, '$'],\n    'CUP': [, '$'],\n    'CZK': [, 'Kč'],\n    'DKK': [, 'kr'],\n    'DOP': [, '$'],\n    'EGP': [, 'E£'],\n    'ESP': [, '₧'],\n    'EUR': ['€'],\n    'FJD': [, '$'],\n    'FKP': [, '£'],\n    'GBP': ['£'],\n    'GEL': [, '₾'],\n    'GIP': [, '£'],\n    'GNF': [, 'FG'],\n    'GTQ': [, 'Q'],\n    'GYD': [, '$'],\n    'HKD': ['HK$', '$'],\n    'HNL': [, 'L'],\n    'HRK': [, 'kn'],\n    'HUF': [, 'Ft'],\n    'IDR': [, 'Rp'],\n    'ILS': ['₪'],\n    'INR': ['₹'],\n    'ISK': [, 'kr'],\n    'JMD': [, '$'],\n    'JPY': ['¥'],\n    'KHR': [, '៛'],\n    'KMF': [, 'CF'],\n    'KPW': [, '₩'],\n    'KRW': ['₩'],\n    'KYD': [, '$'],\n    'KZT': [, '₸'],\n    'LAK': [, '₭'],\n    'LBP': [, 'L£'],\n    'LKR': [, 'Rs'],\n    'LRD': [, '$'],\n    'LTL': [, 'Lt'],\n    'LVL': [, 'Ls'],\n    'MGA': [, 'Ar'],\n    'MMK': [, 'K'],\n    'MNT': [, '₮'],\n    'MUR': [, 'Rs'],\n    'MXN': ['MX$', '$'],\n    'MYR': [, 'RM'],\n    'NAD': [, '$'],\n    'NGN': [, '₦'],\n    'NIO': [, 'C$'],\n    'NOK': [, 'kr'],\n    'NPR': [, 'Rs'],\n    'NZD': ['NZ$', '$'],\n    'PHP': [, '₱'],\n    'PKR': [, 'Rs'],\n    'PLN': [, 'zł'],\n    'PYG': [, '₲'],\n    'RON': [, 'lei'],\n    'RUB': [, '₽'],\n    'RUR': [, 'р.'],\n    'RWF': [, 'RF'],\n    'SBD': [, '$'],\n    'SEK': [, 'kr'],\n    'SGD': [, '$'],\n    'SHP': [, '£'],\n    'SRD': [, '$'],\n    'SSP': [, '£'],\n    'STD': [, 'Db'],\n    'SYP': [, '£'],\n    'THB': [, '฿'],\n    'TOP': [, 'T$'],\n    'TRY': [, '₺'],\n    'TTD': [, '$'],\n    'TWD': ['NT$', '$'],\n    'UAH': [, '₴'],\n    'USD': ['$'],\n    'UYU': [, '$'],\n    'VEF': [, 'Bs'],\n    'VND': ['₫'],\n    'XAF': ['FCFA'],\n    'XCD': ['EC$', '$'],\n    'XOF': ['CFA'],\n    'XPF': ['CFPF'],\n    'ZAR': [, 'R'],\n    'ZMW': [, 'ZK'],\n};\n//# sourceMappingURL=currencies.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n/**\n * @param {?} n\n * @return {?}\n */\nfunction plural(n) {\n    var /** @type {?} */ i = Math.floor(Math.abs(n)), /** @type {?} */ v = n.toString().replace(/^[^.]*\\.?/, '').length;\n    if (i === 1 && v === 0)\n        return 1;\n    return 5;\n}\nexport default [\n    'en',\n    [\n        ['a', 'p'],\n        ['AM', 'PM'],\n    ],\n    [\n        ['AM', 'PM'],\n        ,\n    ],\n    [\n        ['S', 'M', 'T', 'W', 'T', 'F', 'S'], ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n        ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n        ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa']\n    ],\n    ,\n    [\n        ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n        ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n        [\n            'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September',\n            'October', 'November', 'December'\n        ]\n    ],\n    ,\n    [['B', 'A'], ['BC', 'AD'], ['Before Christ', 'Anno Domini']], 0, [6, 0],\n    ['M/d/yy', 'MMM d, y', 'MMMM d, y', 'EEEE, MMMM d, y'],\n    ['h:mm a', 'h:mm:ss a', 'h:mm:ss a z', 'h:mm:ss a zzzz'],\n    [\n        '{1}, {0}',\n        ,\n        '{1} \\'at\\' {0}',\n    ],\n    ['.', ',', ';', '%', '+', '-', 'E', '×', '‰', '∞', 'NaN', ':'],\n    ['#,##0.###', '#,##0%', '¤#,##0.00', '#E0'], '$', 'US Dollar', plural\n];\n//# sourceMappingURL=locale_en.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * \\@experimental i18n support is experimental.\n */\nexport var /** @type {?} */ LOCALE_DATA = {};\n/**\n * Register global data to be used internally by Angular. See the\n * {\\@linkDocs guide/i18n#i18n-pipes \"I18n guide\"} to know how to import additional locale data.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} data\n * @param {?=} localeId\n * @param {?=} extraData\n * @return {?}\n */\nexport function registerLocaleData(data, localeId, extraData) {\n    if (typeof localeId !== 'string') {\n        extraData = localeId;\n        localeId = data[0 /* LocaleId */];\n    }\n    localeId = localeId.toLowerCase().replace(/_/g, '-');\n    LOCALE_DATA[localeId] = data;\n    if (extraData) {\n        LOCALE_DATA[localeId][18 /* ExtraData */] = extraData;\n    }\n}\n/** @enum {number} */\nvar LocaleDataIndex = {\n    LocaleId: 0,\n    DayPeriodsFormat: 1,\n    DayPeriodsStandalone: 2,\n    DaysFormat: 3,\n    DaysStandalone: 4,\n    MonthsFormat: 5,\n    MonthsStandalone: 6,\n    Eras: 7,\n    FirstDayOfWeek: 8,\n    WeekendRange: 9,\n    DateFormat: 10,\n    TimeFormat: 11,\n    DateTimeFormat: 12,\n    NumberSymbols: 13,\n    NumberFormats: 14,\n    CurrencySymbol: 15,\n    CurrencyName: 16,\n    PluralCase: 17,\n    ExtraData: 18,\n};\nexport { LocaleDataIndex };\n/** @enum {number} */\nvar ExtraLocaleDataIndex = {\n    ExtraDayPeriodFormats: 0,\n    ExtraDayPeriodStandalone: 1,\n    ExtraDayPeriodsRules: 2,\n};\nexport { ExtraLocaleDataIndex };\n//# sourceMappingURL=locale_data.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { CURRENCIES } from './currencies';\nimport localeEn from './locale_en';\nimport { LOCALE_DATA } from './locale_data';\n/** @enum {number} */\nvar NumberFormatStyle = {\n    Decimal: 0,\n    Percent: 1,\n    Currency: 2,\n    Scientific: 3,\n};\nexport { NumberFormatStyle };\nNumberFormatStyle[NumberFormatStyle.Decimal] = \"Decimal\";\nNumberFormatStyle[NumberFormatStyle.Percent] = \"Percent\";\nNumberFormatStyle[NumberFormatStyle.Currency] = \"Currency\";\nNumberFormatStyle[NumberFormatStyle.Scientific] = \"Scientific\";\n/** @enum {number} */\nvar Plural = {\n    Zero: 0,\n    One: 1,\n    Two: 2,\n    Few: 3,\n    Many: 4,\n    Other: 5,\n};\nexport { Plural };\nPlural[Plural.Zero] = \"Zero\";\nPlural[Plural.One] = \"One\";\nPlural[Plural.Two] = \"Two\";\nPlural[Plural.Few] = \"Few\";\nPlural[Plural.Many] = \"Many\";\nPlural[Plural.Other] = \"Other\";\n/** @enum {number} */\nvar FormStyle = {\n    Format: 0,\n    Standalone: 1,\n};\nexport { FormStyle };\nFormStyle[FormStyle.Format] = \"Format\";\nFormStyle[FormStyle.Standalone] = \"Standalone\";\n/** @enum {number} */\nvar TranslationWidth = {\n    Narrow: 0,\n    Abbreviated: 1,\n    Wide: 2,\n    Short: 3,\n};\nexport { TranslationWidth };\nTranslationWidth[TranslationWidth.Narrow] = \"Narrow\";\nTranslationWidth[TranslationWidth.Abbreviated] = \"Abbreviated\";\nTranslationWidth[TranslationWidth.Wide] = \"Wide\";\nTranslationWidth[TranslationWidth.Short] = \"Short\";\n/** @enum {number} */\nvar FormatWidth = {\n    Short: 0,\n    Medium: 1,\n    Long: 2,\n    Full: 3,\n};\nexport { FormatWidth };\nFormatWidth[FormatWidth.Short] = \"Short\";\nFormatWidth[FormatWidth.Medium] = \"Medium\";\nFormatWidth[FormatWidth.Long] = \"Long\";\nFormatWidth[FormatWidth.Full] = \"Full\";\n/** @enum {number} */\nvar NumberSymbol = {\n    Decimal: 0,\n    Group: 1,\n    List: 2,\n    PercentSign: 3,\n    PlusSign: 4,\n    MinusSign: 5,\n    Exponential: 6,\n    SuperscriptingExponent: 7,\n    PerMille: 8,\n    Infinity: 9,\n    NaN: 10,\n    TimeSeparator: 11,\n    CurrencyDecimal: 12,\n    CurrencyGroup: 13,\n};\nexport { NumberSymbol };\nNumberSymbol[NumberSymbol.Decimal] = \"Decimal\";\nNumberSymbol[NumberSymbol.Group] = \"Group\";\nNumberSymbol[NumberSymbol.List] = \"List\";\nNumberSymbol[NumberSymbol.PercentSign] = \"PercentSign\";\nNumberSymbol[NumberSymbol.PlusSign] = \"PlusSign\";\nNumberSymbol[NumberSymbol.MinusSign] = \"MinusSign\";\nNumberSymbol[NumberSymbol.Exponential] = \"Exponential\";\nNumberSymbol[NumberSymbol.SuperscriptingExponent] = \"SuperscriptingExponent\";\nNumberSymbol[NumberSymbol.PerMille] = \"PerMille\";\nNumberSymbol[NumberSymbol.Infinity] = \"Infinity\";\nNumberSymbol[NumberSymbol.NaN] = \"NaN\";\nNumberSymbol[NumberSymbol.TimeSeparator] = \"TimeSeparator\";\nNumberSymbol[NumberSymbol.CurrencyDecimal] = \"CurrencyDecimal\";\nNumberSymbol[NumberSymbol.CurrencyGroup] = \"CurrencyGroup\";\n/** @enum {number} */\nvar WeekDay = {\n    Sunday: 0,\n    Monday: 1,\n    Tuesday: 2,\n    Wednesday: 3,\n    Thursday: 4,\n    Friday: 5,\n    Saturday: 6,\n};\nexport { WeekDay };\nWeekDay[WeekDay.Sunday] = \"Sunday\";\nWeekDay[WeekDay.Monday] = \"Monday\";\nWeekDay[WeekDay.Tuesday] = \"Tuesday\";\nWeekDay[WeekDay.Wednesday] = \"Wednesday\";\nWeekDay[WeekDay.Thursday] = \"Thursday\";\nWeekDay[WeekDay.Friday] = \"Friday\";\nWeekDay[WeekDay.Saturday] = \"Saturday\";\n/**\n * The locale id for the chosen locale (e.g `en-GB`).\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function getLocaleId(locale) {\n    return findLocaleData(locale)[0 /* LocaleId */];\n}\n/**\n * Periods of the day (e.g. `[AM, PM]` for en-US).\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} formStyle\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleDayPeriods(locale, formStyle, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    var /** @type {?} */ amPmData = /** @type {?} */ ([data[1 /* DayPeriodsFormat */], data[2 /* DayPeriodsStandalone */]]);\n    var /** @type {?} */ amPm = getLastDefinedValue(amPmData, formStyle);\n    return getLastDefinedValue(amPm, width);\n}\n/**\n * Days of the week for the Gregorian calendar (e.g. `[Sunday, Monday, ... Saturday]` for en-US).\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} formStyle\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleDayNames(locale, formStyle, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    var /** @type {?} */ daysData = /** @type {?} */ ([data[3 /* DaysFormat */], data[4 /* DaysStandalone */]]);\n    var /** @type {?} */ days = getLastDefinedValue(daysData, formStyle);\n    return getLastDefinedValue(days, width);\n}\n/**\n * Months of the year for the Gregorian calendar (e.g. `[January, February, ...]` for en-US).\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} formStyle\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleMonthNames(locale, formStyle, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    var /** @type {?} */ monthsData = /** @type {?} */ ([data[5 /* MonthsFormat */], data[6 /* MonthsStandalone */]]);\n    var /** @type {?} */ months = getLastDefinedValue(monthsData, formStyle);\n    return getLastDefinedValue(months, width);\n}\n/**\n * Eras for the Gregorian calendar (e.g. AD/BC).\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleEraNames(locale, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    var /** @type {?} */ erasData = /** @type {?} */ (data[7 /* Eras */]);\n    return getLastDefinedValue(erasData, width);\n}\n/**\n * First day of the week for this locale, based on english days (Sunday = 0, Monday = 1, ...).\n * For example in french the value would be 1 because the first day of the week is Monday.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function getLocaleFirstDayOfWeek(locale) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return data[8 /* FirstDayOfWeek */];\n}\n/**\n * Range of days in the week that represent the week-end for this locale, based on english days\n * (Sunday = 0, Monday = 1, ...).\n * For example in english the value would be [6,0] for Saturday to Sunday.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function getLocaleWeekEndRange(locale) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return data[9 /* WeekendRange */];\n}\n/**\n * Date format that depends on the locale.\n *\n * There are four basic date formats:\n * - `full` should contain long-weekday (EEEE), year (y), long-month (MMMM), day (d).\n *\n *  For example, English uses `EEEE, MMMM d, y`, corresponding to a date like\n *  \"Tuesday, September 14, 1999\".\n *\n * - `long` should contain year, long-month, day.\n *\n *  For example, `MMMM d, y`, corresponding to a date like \"September 14, 1999\".\n *\n * - `medium` should contain year, abbreviated-month (MMM), day.\n *\n *  For example, `MMM d, y`, corresponding to a date like \"Sep 14, 1999\".\n *  For languages that do not use abbreviated months, use the numeric month (MM/M). For example,\n *  `y/MM/dd`, corresponding to a date like \"1999/09/14\".\n *\n * - `short` should contain year, numeric-month (MM/M), and day.\n *\n *  For example, `M/d/yy`, corresponding to a date like \"9/14/99\".\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleDateFormat(locale, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return getLastDefinedValue(data[10 /* DateFormat */], width);\n}\n/**\n * Time format that depends on the locale.\n *\n * The standard formats include four basic time formats:\n * - `full` should contain hour (h/H), minute (mm), second (ss), and zone (zzzz).\n * - `long` should contain hour, minute, second, and zone (z)\n * - `medium` should contain hour, minute, second.\n * - `short` should contain hour, minute.\n *\n * Note: The patterns depend on whether the main country using your language uses 12-hour time or\n * not:\n * - For 12-hour time, use a pattern like `hh:mm a` using h to mean a 12-hour clock cycle running\n * 1 through 12 (midnight plus 1 minute is 12:01), or using K to mean a 12-hour clock cycle\n * running 0 through 11 (midnight plus 1 minute is 0:01).\n * - For 24-hour time, use a pattern like `HH:mm` using H to mean a 24-hour clock cycle running 0\n * through 23 (midnight plus 1 minute is 0:01), or using k to mean a 24-hour clock cycle running\n * 1 through 24 (midnight plus 1 minute is 24:01).\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleTimeFormat(locale, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return getLastDefinedValue(data[11 /* TimeFormat */], width);\n}\n/**\n * Date-time format that depends on the locale.\n *\n * The date-time pattern shows how to combine separate patterns for date (represented by {1})\n * and time (represented by {0}) into a single pattern. It usually doesn't need to be changed.\n * What you want to pay attention to are:\n * - possibly removing a space for languages that don't use it, such as many East Asian languages\n * - possibly adding a comma, other punctuation, or a combining word\n *\n * For example:\n * - English uses `{1} 'at' {0}` or `{1}, {0}` (depending on date style), while Japanese uses\n *  `{1}{0}`.\n * - An English formatted date-time using the combining pattern `{1}, {0}` could be\n *  `Dec 10, 2010, 3:59:49 PM`. Notice the comma and space between the date portion and the time\n *  portion.\n *\n * There are four formats (`full`, `long`, `medium`, `short`); the determination of which to use\n * is normally based on the date style. For example, if the date has a full month and weekday\n * name, the full combining pattern will be used to combine that with a time. If the date has\n * numeric month, the short version of the combining pattern will be used to combine that with a\n * time. English uses `{1} 'at' {0}` for full and long styles, and `{1}, {0}` for medium and short\n * styles.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleDateTimeFormat(locale, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    var /** @type {?} */ dateTimeFormatData = /** @type {?} */ (data[12 /* DateTimeFormat */]);\n    return getLastDefinedValue(dateTimeFormatData, width);\n}\n/**\n * Number symbol that can be used to replace placeholders in number formats.\n * See {\\@link NumberSymbol} for more information.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} symbol\n * @return {?}\n */\nexport function getLocaleNumberSymbol(locale, symbol) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    var /** @type {?} */ res = data[13 /* NumberSymbols */][symbol];\n    if (typeof res === 'undefined') {\n        if (symbol === NumberSymbol.CurrencyDecimal) {\n            return data[13 /* NumberSymbols */][NumberSymbol.Decimal];\n        }\n        else if (symbol === NumberSymbol.CurrencyGroup) {\n            return data[13 /* NumberSymbols */][NumberSymbol.Group];\n        }\n    }\n    return res;\n}\n/**\n * Number format that depends on the locale.\n *\n * Numbers are formatted using patterns, like `#,###.00`. For example, the pattern `#,###.00`\n * when used to format the number 12345.678 could result in \"12'345,67\". That would happen if the\n * grouping separator for your language is an apostrophe, and the decimal separator is a comma.\n *\n * <b>Important:</b> The characters `.` `,` `0` `#` (and others below) are special placeholders;\n * they stand for the decimal separator, and so on, and are NOT real characters.\n * You must NOT \"translate\" the placeholders; for example, don't change `.` to `,` even though in\n * your language the decimal point is written with a comma. The symbols should be replaced by the\n * local equivalents, using the Number Symbols for your language.\n *\n * Here are the special characters used in number patterns:\n *\n * | Symbol | Meaning |\n * |--------|---------|\n * | . | Replaced automatically by the character used for the decimal point. |\n * | , | Replaced by the \"grouping\" (thousands) separator. |\n * | 0 | Replaced by a digit (or zero if there aren't enough digits). |\n * | # | Replaced by a digit (or nothing if there aren't enough). |\n * | ¤ | This will be replaced by a currency symbol, such as $ or USD. |\n * | % | This marks a percent format. The % symbol may change position, but must be retained. |\n * | E | This marks a scientific format. The E symbol may change position, but must be retained. |\n * | ' | Special characters used as literal characters are quoted with ASCII single quotes. |\n *\n * You can find more information\n * [on the CLDR website](http://cldr.unicode.org/translation/number-patterns)\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} type\n * @return {?}\n */\nexport function getLocaleNumberFormat(locale, type) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return data[14 /* NumberFormats */][type];\n}\n/**\n * The symbol used to represent the currency for the main country using this locale (e.g. $ for\n * the locale en-US).\n * The symbol will be `null` if the main country cannot be determined.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function getLocaleCurrencySymbol(locale) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return data[15 /* CurrencySymbol */] || null;\n}\n/**\n * The name of the currency for the main country using this locale (e.g. USD for the locale\n * en-US).\n * The name will be `null` if the main country cannot be determined.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function getLocaleCurrencyName(locale) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return data[16 /* CurrencyName */] || null;\n}\n/**\n * The locale plural function used by ICU expressions to determine the plural case to use.\n * See {\\@link NgPlural} for more information.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function getLocalePluralCase(locale) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    return data[17 /* PluralCase */];\n}\n/**\n * @param {?} data\n * @return {?}\n */\nfunction checkFullData(data) {\n    if (!data[18 /* ExtraData */]) {\n        throw new Error(\"Missing extra locale data for the locale \\\"\" + data[0 /* LocaleId */] + \"\\\". Use \\\"registerLocaleData\\\" to load new data. See the \\\"I18n guide\\\" on angular.io to know more.\");\n    }\n}\n/**\n * Rules used to determine which day period to use (See `dayPeriods` below).\n * The rules can either be an array or a single value. If it's an array, consider it as \"from\"\n * and \"to\". If it's a single value then it means that the period is only valid at this exact\n * value.\n * There is always the same number of rules as the number of day periods, which means that the\n * first rule is applied to the first day period and so on.\n * You should fallback to AM/PM when there are no rules available.\n *\n * Note: this is only available if you load the full locale data.\n * See the {\\@linkDocs guide/i18n#i18n-pipes \"I18n guide\"} to know how to import additional locale\n * data.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function getLocaleExtraDayPeriodRules(locale) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    checkFullData(data);\n    var /** @type {?} */ rules = data[18 /* ExtraData */][2 /* ExtraDayPeriodsRules */] || [];\n    return rules.map(function (rule) {\n        if (typeof rule === 'string') {\n            return extractTime(rule);\n        }\n        return [extractTime(rule[0]), extractTime(rule[1])];\n    });\n}\n/**\n * Day Periods indicate roughly how the day is broken up in different languages (e.g. morning,\n * noon, afternoon, midnight, ...).\n * You should use the function {\\@link getLocaleExtraDayPeriodRules} to determine which period to\n * use.\n * You should fallback to AM/PM when there are no day periods available.\n *\n * Note: this is only available if you load the full locale data.\n * See the {\\@linkDocs guide/i18n#i18n-pipes \"I18n guide\"} to know how to import additional locale\n * data.\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @param {?} formStyle\n * @param {?} width\n * @return {?}\n */\nexport function getLocaleExtraDayPeriods(locale, formStyle, width) {\n    var /** @type {?} */ data = findLocaleData(locale);\n    checkFullData(data);\n    var /** @type {?} */ dayPeriodsData = /** @type {?} */ ([\n        data[18 /* ExtraData */][0 /* ExtraDayPeriodFormats */],\n        data[18 /* ExtraData */][1 /* ExtraDayPeriodStandalone */]\n    ]);\n    var /** @type {?} */ dayPeriods = getLastDefinedValue(dayPeriodsData, formStyle) || [];\n    return getLastDefinedValue(dayPeriods, width) || [];\n}\n/**\n * Returns the first value that is defined in an array, going backwards.\n *\n * To avoid repeating the same data (e.g. when \"format\" and \"standalone\" are the same) we only\n * add the first one to the locale data arrays, the other ones are only defined when different.\n * We use this function to retrieve the first defined value.\n *\n * \\@experimental i18n support is experimental.\n * @template T\n * @param {?} data\n * @param {?} index\n * @return {?}\n */\nfunction getLastDefinedValue(data, index) {\n    for (var /** @type {?} */ i = index; i > -1; i--) {\n        if (typeof data[i] !== 'undefined') {\n            return data[i];\n        }\n    }\n    throw new Error('Locale data API: locale data undefined');\n}\n/**\n * Extract the hours and minutes from a string like \"15:45\"\n * @param {?} time\n * @return {?}\n */\nfunction extractTime(time) {\n    var _a = time.split(':'), h = _a[0], m = _a[1];\n    return { hours: +h, minutes: +m };\n}\n/**\n * Finds the locale data for a locale id\n *\n * \\@experimental i18n support is experimental.\n * @param {?} locale\n * @return {?}\n */\nexport function findLocaleData(locale) {\n    var /** @type {?} */ normalizedLocale = locale.toLowerCase().replace(/_/g, '-');\n    var /** @type {?} */ match = LOCALE_DATA[normalizedLocale];\n    if (match) {\n        return match;\n    }\n    // let's try to find a parent locale\n    var /** @type {?} */ parentLocale = normalizedLocale.split('-')[0];\n    match = LOCALE_DATA[parentLocale];\n    if (match) {\n        return match;\n    }\n    if (parentLocale === 'en') {\n        return localeEn;\n    }\n    throw new Error(\"Missing locale data for the locale \\\"\" + locale + \"\\\".\");\n}\n/**\n * Return the currency symbol for a given currency code, or the code if no symbol available\n * (e.g.: format narrow = $, format wide = US$, code = USD)\n *\n * \\@experimental i18n support is experimental.\n * @param {?} code\n * @param {?} format\n * @return {?}\n */\nexport function getCurrencySymbol(code, format) {\n    var /** @type {?} */ currency = CURRENCIES[code] || [];\n    var /** @type {?} */ symbolNarrow = currency[1];\n    if (format === 'narrow' && typeof symbolNarrow === 'string') {\n        return symbolNarrow;\n    }\n    return currency[0] || code;\n}\n//# sourceMappingURL=locale_data_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport * as tslib_1 from \"tslib\";\nimport { Inject, Injectable, InjectionToken, LOCALE_ID, Optional } from '@angular/core';\nimport { Plural, getLocalePluralCase } from './locale_data_api';\n/**\n * @deprecated from v5\n */\nexport var /** @type {?} */ DEPRECATED_PLURAL_FN = new InjectionToken('UseV4Plurals');\n/**\n * \\@experimental\n * @abstract\n */\nvar /**\n * \\@experimental\n * @abstract\n */\nNgLocalization = /** @class */ (function () {\n    function NgLocalization() {\n    }\n    return NgLocalization;\n}());\n/**\n * \\@experimental\n * @abstract\n */\nexport { NgLocalization };\nfunction NgLocalization_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?} value\n     * @param {?=} locale\n     * @return {?}\n     */\n    NgLocalization.prototype.getPluralCategory = function (value, locale) { };\n}\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n * @param {?} value\n * @param {?} cases\n * @param {?} ngLocalization\n * @param {?=} locale\n * @return {?}\n */\nexport function getPluralCategory(value, cases, ngLocalization, locale) {\n    var /** @type {?} */ key = \"=\" + value;\n    if (cases.indexOf(key) > -1) {\n        return key;\n    }\n    key = ngLocalization.getPluralCategory(value, locale);\n    if (cases.indexOf(key) > -1) {\n        return key;\n    }\n    if (cases.indexOf('other') > -1) {\n        return 'other';\n    }\n    throw new Error(\"No plural message found for value \\\"\" + value + \"\\\"\");\n}\n/**\n * Returns the plural case based on the locale\n *\n * \\@experimental\n */\nvar NgLocaleLocalization = /** @class */ (function (_super) {\n    tslib_1.__extends(NgLocaleLocalization, _super);\n    function NgLocaleLocalization(locale, /** @deprecated from v5 */\n        deprecatedPluralFn) {\n        var _this = _super.call(this) || this;\n        _this.locale = locale;\n        _this.deprecatedPluralFn = deprecatedPluralFn;\n        return _this;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} locale\n     * @return {?}\n     */\n    NgLocaleLocalization.prototype.getPluralCategory = /**\n     * @param {?} value\n     * @param {?=} locale\n     * @return {?}\n     */\n    function (value, locale) {\n        var /** @type {?} */ plural = this.deprecatedPluralFn ? this.deprecatedPluralFn(locale || this.locale, value) :\n            getLocalePluralCase(locale || this.locale)(value);\n        switch (plural) {\n            case Plural.Zero:\n                return 'zero';\n            case Plural.One:\n                return 'one';\n            case Plural.Two:\n                return 'two';\n            case Plural.Few:\n                return 'few';\n            case Plural.Many:\n                return 'many';\n            default:\n                return 'other';\n        }\n    };\n    NgLocaleLocalization.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    NgLocaleLocalization.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n        { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [DEPRECATED_PLURAL_FN,] },] },\n    ]; };\n    return NgLocaleLocalization;\n}(NgLocalization));\nexport { NgLocaleLocalization };\nfunction NgLocaleLocalization_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgLocaleLocalization.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgLocaleLocalization.ctorParameters;\n    /** @type {?} */\n    NgLocaleLocalization.prototype.locale;\n    /**\n     * @deprecated from v5\n     * @type {?}\n     */\n    NgLocaleLocalization.prototype.deprecatedPluralFn;\n}\n/**\n * Returns the plural case based on the locale\n *\n * @deprecated from v5 the plural case function is in locale data files common/locales/*.ts\n * \\@experimental\n * @param {?} locale\n * @param {?} nLike\n * @return {?}\n */\nexport function getPluralCase(locale, nLike) {\n    // TODO(vicb): lazy compute\n    if (typeof nLike === 'string') {\n        nLike = parseInt(/** @type {?} */ (nLike), 10);\n    }\n    var /** @type {?} */ n = /** @type {?} */ (nLike);\n    var /** @type {?} */ nDecimal = n.toString().replace(/^[^.]*\\.?/, '');\n    var /** @type {?} */ i = Math.floor(Math.abs(n));\n    var /** @type {?} */ v = nDecimal.length;\n    var /** @type {?} */ f = parseInt(nDecimal, 10);\n    var /** @type {?} */ t = parseInt(n.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n    var /** @type {?} */ lang = locale.split('-')[0].toLowerCase();\n    switch (lang) {\n        case 'af':\n        case 'asa':\n        case 'az':\n        case 'bem':\n        case 'bez':\n        case 'bg':\n        case 'brx':\n        case 'ce':\n        case 'cgg':\n        case 'chr':\n        case 'ckb':\n        case 'ee':\n        case 'el':\n        case 'eo':\n        case 'es':\n        case 'eu':\n        case 'fo':\n        case 'fur':\n        case 'gsw':\n        case 'ha':\n        case 'haw':\n        case 'hu':\n        case 'jgo':\n        case 'jmc':\n        case 'ka':\n        case 'kk':\n        case 'kkj':\n        case 'kl':\n        case 'ks':\n        case 'ksb':\n        case 'ky':\n        case 'lb':\n        case 'lg':\n        case 'mas':\n        case 'mgo':\n        case 'ml':\n        case 'mn':\n        case 'nb':\n        case 'nd':\n        case 'ne':\n        case 'nn':\n        case 'nnh':\n        case 'nyn':\n        case 'om':\n        case 'or':\n        case 'os':\n        case 'ps':\n        case 'rm':\n        case 'rof':\n        case 'rwk':\n        case 'saq':\n        case 'seh':\n        case 'sn':\n        case 'so':\n        case 'sq':\n        case 'ta':\n        case 'te':\n        case 'teo':\n        case 'tk':\n        case 'tr':\n        case 'ug':\n        case 'uz':\n        case 'vo':\n        case 'vun':\n        case 'wae':\n        case 'xog':\n            if (n === 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'ak':\n        case 'ln':\n        case 'mg':\n        case 'pa':\n        case 'ti':\n            if (n === Math.floor(n) && n >= 0 && n <= 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'am':\n        case 'as':\n        case 'bn':\n        case 'fa':\n        case 'gu':\n        case 'hi':\n        case 'kn':\n        case 'mr':\n        case 'zu':\n            if (i === 0 || n === 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'ar':\n            if (n === 0)\n                return Plural.Zero;\n            if (n === 1)\n                return Plural.One;\n            if (n === 2)\n                return Plural.Two;\n            if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10)\n                return Plural.Few;\n            if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99)\n                return Plural.Many;\n            return Plural.Other;\n        case 'ast':\n        case 'ca':\n        case 'de':\n        case 'en':\n        case 'et':\n        case 'fi':\n        case 'fy':\n        case 'gl':\n        case 'it':\n        case 'nl':\n        case 'sv':\n        case 'sw':\n        case 'ur':\n        case 'yi':\n            if (i === 1 && v === 0)\n                return Plural.One;\n            return Plural.Other;\n        case 'be':\n            if (n % 10 === 1 && !(n % 100 === 11))\n                return Plural.One;\n            if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 4 &&\n                !(n % 100 >= 12 && n % 100 <= 14))\n                return Plural.Few;\n            if (n % 10 === 0 || n % 10 === Math.floor(n % 10) && n % 10 >= 5 && n % 10 <= 9 ||\n                n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 14)\n                return Plural.Many;\n            return Plural.Other;\n        case 'br':\n            if (n % 10 === 1 && !(n % 100 === 11 || n % 100 === 71 || n % 100 === 91))\n                return Plural.One;\n            if (n % 10 === 2 && !(n % 100 === 12 || n % 100 === 72 || n % 100 === 92))\n                return Plural.Two;\n            if (n % 10 === Math.floor(n % 10) && (n % 10 >= 3 && n % 10 <= 4 || n % 10 === 9) &&\n                !(n % 100 >= 10 && n % 100 <= 19 || n % 100 >= 70 && n % 100 <= 79 ||\n                    n % 100 >= 90 && n % 100 <= 99))\n                return Plural.Few;\n            if (!(n === 0) && n % 1e6 === 0)\n                return Plural.Many;\n            return Plural.Other;\n        case 'bs':\n        case 'hr':\n        case 'sr':\n            if (v === 0 && i % 10 === 1 && !(i % 100 === 11) || f % 10 === 1 && !(f % 100 === 11))\n                return Plural.One;\n            if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n                !(i % 100 >= 12 && i % 100 <= 14) ||\n                f % 10 === Math.floor(f % 10) && f % 10 >= 2 && f % 10 <= 4 &&\n                    !(f % 100 >= 12 && f % 100 <= 14))\n                return Plural.Few;\n            return Plural.Other;\n        case 'cs':\n        case 'sk':\n            if (i === 1 && v === 0)\n                return Plural.One;\n            if (i === Math.floor(i) && i >= 2 && i <= 4 && v === 0)\n                return Plural.Few;\n            if (!(v === 0))\n                return Plural.Many;\n            return Plural.Other;\n        case 'cy':\n            if (n === 0)\n                return Plural.Zero;\n            if (n === 1)\n                return Plural.One;\n            if (n === 2)\n                return Plural.Two;\n            if (n === 3)\n                return Plural.Few;\n            if (n === 6)\n                return Plural.Many;\n            return Plural.Other;\n        case 'da':\n            if (n === 1 || !(t === 0) && (i === 0 || i === 1))\n                return Plural.One;\n            return Plural.Other;\n        case 'dsb':\n        case 'hsb':\n            if (v === 0 && i % 100 === 1 || f % 100 === 1)\n                return Plural.One;\n            if (v === 0 && i % 100 === 2 || f % 100 === 2)\n                return Plural.Two;\n            if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 ||\n                f % 100 === Math.floor(f % 100) && f % 100 >= 3 && f % 100 <= 4)\n                return Plural.Few;\n            return Plural.Other;\n        case 'ff':\n        case 'fr':\n        case 'hy':\n        case 'kab':\n            if (i === 0 || i === 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'fil':\n            if (v === 0 && (i === 1 || i === 2 || i === 3) ||\n                v === 0 && !(i % 10 === 4 || i % 10 === 6 || i % 10 === 9) ||\n                !(v === 0) && !(f % 10 === 4 || f % 10 === 6 || f % 10 === 9))\n                return Plural.One;\n            return Plural.Other;\n        case 'ga':\n            if (n === 1)\n                return Plural.One;\n            if (n === 2)\n                return Plural.Two;\n            if (n === Math.floor(n) && n >= 3 && n <= 6)\n                return Plural.Few;\n            if (n === Math.floor(n) && n >= 7 && n <= 10)\n                return Plural.Many;\n            return Plural.Other;\n        case 'gd':\n            if (n === 1 || n === 11)\n                return Plural.One;\n            if (n === 2 || n === 12)\n                return Plural.Two;\n            if (n === Math.floor(n) && (n >= 3 && n <= 10 || n >= 13 && n <= 19))\n                return Plural.Few;\n            return Plural.Other;\n        case 'gv':\n            if (v === 0 && i % 10 === 1)\n                return Plural.One;\n            if (v === 0 && i % 10 === 2)\n                return Plural.Two;\n            if (v === 0 &&\n                (i % 100 === 0 || i % 100 === 20 || i % 100 === 40 || i % 100 === 60 || i % 100 === 80))\n                return Plural.Few;\n            if (!(v === 0))\n                return Plural.Many;\n            return Plural.Other;\n        case 'he':\n            if (i === 1 && v === 0)\n                return Plural.One;\n            if (i === 2 && v === 0)\n                return Plural.Two;\n            if (v === 0 && !(n >= 0 && n <= 10) && n % 10 === 0)\n                return Plural.Many;\n            return Plural.Other;\n        case 'is':\n            if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0))\n                return Plural.One;\n            return Plural.Other;\n        case 'ksh':\n            if (n === 0)\n                return Plural.Zero;\n            if (n === 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'kw':\n        case 'naq':\n        case 'se':\n        case 'smn':\n            if (n === 1)\n                return Plural.One;\n            if (n === 2)\n                return Plural.Two;\n            return Plural.Other;\n        case 'lag':\n            if (n === 0)\n                return Plural.Zero;\n            if ((i === 0 || i === 1) && !(n === 0))\n                return Plural.One;\n            return Plural.Other;\n        case 'lt':\n            if (n % 10 === 1 && !(n % 100 >= 11 && n % 100 <= 19))\n                return Plural.One;\n            if (n % 10 === Math.floor(n % 10) && n % 10 >= 2 && n % 10 <= 9 &&\n                !(n % 100 >= 11 && n % 100 <= 19))\n                return Plural.Few;\n            if (!(f === 0))\n                return Plural.Many;\n            return Plural.Other;\n        case 'lv':\n        case 'prg':\n            if (n % 10 === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19 ||\n                v === 2 && f % 100 === Math.floor(f % 100) && f % 100 >= 11 && f % 100 <= 19)\n                return Plural.Zero;\n            if (n % 10 === 1 && !(n % 100 === 11) || v === 2 && f % 10 === 1 && !(f % 100 === 11) ||\n                !(v === 2) && f % 10 === 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'mk':\n            if (v === 0 && i % 10 === 1 || f % 10 === 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'mt':\n            if (n === 1)\n                return Plural.One;\n            if (n === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 2 && n % 100 <= 10)\n                return Plural.Few;\n            if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 19)\n                return Plural.Many;\n            return Plural.Other;\n        case 'pl':\n            if (i === 1 && v === 0)\n                return Plural.One;\n            if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n                !(i % 100 >= 12 && i % 100 <= 14))\n                return Plural.Few;\n            if (v === 0 && !(i === 1) && i % 10 === Math.floor(i % 10) && i % 10 >= 0 && i % 10 <= 1 ||\n                v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n                v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 12 && i % 100 <= 14)\n                return Plural.Many;\n            return Plural.Other;\n        case 'pt':\n            if (n === Math.floor(n) && n >= 0 && n <= 2 && !(n === 2))\n                return Plural.One;\n            return Plural.Other;\n        case 'ro':\n            if (i === 1 && v === 0)\n                return Plural.One;\n            if (!(v === 0) || n === 0 ||\n                !(n === 1) && n % 100 === Math.floor(n % 100) && n % 100 >= 1 && n % 100 <= 19)\n                return Plural.Few;\n            return Plural.Other;\n        case 'ru':\n        case 'uk':\n            if (v === 0 && i % 10 === 1 && !(i % 100 === 11))\n                return Plural.One;\n            if (v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 2 && i % 10 <= 4 &&\n                !(i % 100 >= 12 && i % 100 <= 14))\n                return Plural.Few;\n            if (v === 0 && i % 10 === 0 ||\n                v === 0 && i % 10 === Math.floor(i % 10) && i % 10 >= 5 && i % 10 <= 9 ||\n                v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 11 && i % 100 <= 14)\n                return Plural.Many;\n            return Plural.Other;\n        case 'shi':\n            if (i === 0 || n === 1)\n                return Plural.One;\n            if (n === Math.floor(n) && n >= 2 && n <= 10)\n                return Plural.Few;\n            return Plural.Other;\n        case 'si':\n            if (n === 0 || n === 1 || i === 0 && f === 1)\n                return Plural.One;\n            return Plural.Other;\n        case 'sl':\n            if (v === 0 && i % 100 === 1)\n                return Plural.One;\n            if (v === 0 && i % 100 === 2)\n                return Plural.Two;\n            if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 || !(v === 0))\n                return Plural.Few;\n            return Plural.Other;\n        case 'tzm':\n            if (n === Math.floor(n) && n >= 0 && n <= 1 || n === Math.floor(n) && n >= 11 && n <= 99)\n                return Plural.One;\n            return Plural.Other;\n        // When there is no specification, the default is always \"other\"\n        // Spec: http://cldr.unicode.org/index/cldr-spec/plural-rules\n        // > other (required—general plural form — also used if the language only has a single form)\n        default:\n            return Plural.Other;\n    }\n}\n//# sourceMappingURL=localization.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @param {?} cookieStr\n * @param {?} name\n * @return {?}\n */\nexport function parseCookieValue(cookieStr, name) {\n    name = encodeURIComponent(name);\n    for (var _i = 0, _a = cookieStr.split(';'); _i < _a.length; _i++) {\n        var cookie = _a[_i];\n        var /** @type {?} */ eqIndex = cookie.indexOf('=');\n        var _b = eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)], cookieName = _b[0], cookieValue = _b[1];\n        if (cookieName.trim() === name) {\n            return decodeURIComponent(cookieValue);\n        }\n    }\n    return null;\n}\n//# sourceMappingURL=cookie.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, ElementRef, Input, IterableDiffers, KeyValueDiffers, Renderer2, ɵisListLikeIterable as isListLikeIterable, ɵstringify as stringify } from '@angular/core';\n/**\n * \\@ngModule CommonModule\n *\n * \\@whatItDoes Adds and removes CSS classes on an HTML element.\n *\n * \\@howToUse\n * ```\n *     <some-element [ngClass]=\"'first second'\">...</some-element>\n *\n *     <some-element [ngClass]=\"['first', 'second']\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n *\n *     <some-element [ngClass]=\"stringExp|arrayExp|objExp\">...</some-element>\n *\n *     <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n *\n * \\@description\n *\n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n *\n * \\@stable\n */\nvar NgClass = /** @class */ (function () {\n    function NgClass(_iterableDiffers, _keyValueDiffers, _ngEl, _renderer) {\n        this._iterableDiffers = _iterableDiffers;\n        this._keyValueDiffers = _keyValueDiffers;\n        this._ngEl = _ngEl;\n        this._renderer = _renderer;\n        this._initialClasses = [];\n    }\n    Object.defineProperty(NgClass.prototype, \"klass\", {\n        set: /**\n         * @param {?} v\n         * @return {?}\n         */\n        function (v) {\n            this._removeClasses(this._initialClasses);\n            this._initialClasses = typeof v === 'string' ? v.split(/\\s+/) : [];\n            this._applyClasses(this._initialClasses);\n            this._applyClasses(this._rawClass);\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(NgClass.prototype, \"ngClass\", {\n        set: /**\n         * @param {?} v\n         * @return {?}\n         */\n        function (v) {\n            this._removeClasses(this._rawClass);\n            this._applyClasses(this._initialClasses);\n            this._iterableDiffer = null;\n            this._keyValueDiffer = null;\n            this._rawClass = typeof v === 'string' ? v.split(/\\s+/) : v;\n            if (this._rawClass) {\n                if (isListLikeIterable(this._rawClass)) {\n                    this._iterableDiffer = this._iterableDiffers.find(this._rawClass).create();\n                }\n                else {\n                    this._keyValueDiffer = this._keyValueDiffers.find(this._rawClass).create();\n                }\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @return {?}\n     */\n    NgClass.prototype.ngDoCheck = /**\n     * @return {?}\n     */\n    function () {\n        if (this._iterableDiffer) {\n            var /** @type {?} */ iterableChanges = this._iterableDiffer.diff(/** @type {?} */ (this._rawClass));\n            if (iterableChanges) {\n                this._applyIterableChanges(iterableChanges);\n            }\n        }\n        else if (this._keyValueDiffer) {\n            var /** @type {?} */ keyValueChanges = this._keyValueDiffer.diff(/** @type {?} */ (this._rawClass));\n            if (keyValueChanges) {\n                this._applyKeyValueChanges(keyValueChanges);\n            }\n        }\n    };\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n    NgClass.prototype._applyKeyValueChanges = /**\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        var _this = this;\n        changes.forEachAddedItem(function (record) { return _this._toggleClass(record.key, record.currentValue); });\n        changes.forEachChangedItem(function (record) { return _this._toggleClass(record.key, record.currentValue); });\n        changes.forEachRemovedItem(function (record) {\n            if (record.previousValue) {\n                _this._toggleClass(record.key, false);\n            }\n        });\n    };\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n    NgClass.prototype._applyIterableChanges = /**\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        var _this = this;\n        changes.forEachAddedItem(function (record) {\n            if (typeof record.item === 'string') {\n                _this._toggleClass(record.item, true);\n            }\n            else {\n                throw new Error(\"NgClass can only toggle CSS classes expressed as strings, got \" + stringify(record.item));\n            }\n        });\n        changes.forEachRemovedItem(function (record) { return _this._toggleClass(record.item, false); });\n    };\n    /**\n     * Applies a collection of CSS classes to the DOM element.\n     *\n     * For argument of type Set and Array CSS class names contained in those collections are always\n     * added.\n     * For argument of type Map CSS class name in the map's key is toggled based on the value (added\n     * for truthy and removed for falsy).\n     * @param {?} rawClassVal\n     * @return {?}\n     */\n    NgClass.prototype._applyClasses = /**\n     * Applies a collection of CSS classes to the DOM element.\n     *\n     * For argument of type Set and Array CSS class names contained in those collections are always\n     * added.\n     * For argument of type Map CSS class name in the map's key is toggled based on the value (added\n     * for truthy and removed for falsy).\n     * @param {?} rawClassVal\n     * @return {?}\n     */\n    function (rawClassVal) {\n        var _this = this;\n        if (rawClassVal) {\n            if (Array.isArray(rawClassVal) || rawClassVal instanceof Set) {\n                (/** @type {?} */ (rawClassVal)).forEach(function (klass) { return _this._toggleClass(klass, true); });\n            }\n            else {\n                Object.keys(rawClassVal).forEach(function (klass) { return _this._toggleClass(klass, !!rawClassVal[klass]); });\n            }\n        }\n    };\n    /**\n     * Removes a collection of CSS classes from the DOM element. This is mostly useful for cleanup\n     * purposes.\n     * @param {?} rawClassVal\n     * @return {?}\n     */\n    NgClass.prototype._removeClasses = /**\n     * Removes a collection of CSS classes from the DOM element. This is mostly useful for cleanup\n     * purposes.\n     * @param {?} rawClassVal\n     * @return {?}\n     */\n    function (rawClassVal) {\n        var _this = this;\n        if (rawClassVal) {\n            if (Array.isArray(rawClassVal) || rawClassVal instanceof Set) {\n                (/** @type {?} */ (rawClassVal)).forEach(function (klass) { return _this._toggleClass(klass, false); });\n            }\n            else {\n                Object.keys(rawClassVal).forEach(function (klass) { return _this._toggleClass(klass, false); });\n            }\n        }\n    };\n    /**\n     * @param {?} klass\n     * @param {?} enabled\n     * @return {?}\n     */\n    NgClass.prototype._toggleClass = /**\n     * @param {?} klass\n     * @param {?} enabled\n     * @return {?}\n     */\n    function (klass, enabled) {\n        var _this = this;\n        klass = klass.trim();\n        if (klass) {\n            klass.split(/\\s+/g).forEach(function (klass) {\n                if (enabled) {\n                    _this._renderer.addClass(_this._ngEl.nativeElement, klass);\n                }\n                else {\n                    _this._renderer.removeClass(_this._ngEl.nativeElement, klass);\n                }\n            });\n        }\n    };\n    NgClass.decorators = [\n        { type: Directive, args: [{ selector: '[ngClass]' },] },\n    ];\n    /** @nocollapse */\n    NgClass.ctorParameters = function () { return [\n        { type: IterableDiffers, },\n        { type: KeyValueDiffers, },\n        { type: ElementRef, },\n        { type: Renderer2, },\n    ]; };\n    NgClass.propDecorators = {\n        \"klass\": [{ type: Input, args: ['class',] },],\n        \"ngClass\": [{ type: Input },],\n    };\n    return NgClass;\n}());\nexport { NgClass };\nfunction NgClass_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgClass.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgClass.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgClass.propDecorators;\n    /** @type {?} */\n    NgClass.prototype._iterableDiffer;\n    /** @type {?} */\n    NgClass.prototype._keyValueDiffer;\n    /** @type {?} */\n    NgClass.prototype._initialClasses;\n    /** @type {?} */\n    NgClass.prototype._rawClass;\n    /** @type {?} */\n    NgClass.prototype._iterableDiffers;\n    /** @type {?} */\n    NgClass.prototype._keyValueDiffers;\n    /** @type {?} */\n    NgClass.prototype._ngEl;\n    /** @type {?} */\n    NgClass.prototype._renderer;\n}\n//# sourceMappingURL=ng_class.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { ComponentFactoryResolver, Directive, Injector, Input, NgModuleFactory, NgModuleRef, Type, ViewContainerRef } from '@angular/core';\n/**\n * Instantiates a single {\\@link Component} type and inserts its Host View into current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n *\n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will get destroyed.\n *\n * ### Fine tune control\n *\n * You can control the component creation process by using the following optional attributes:\n *\n * * `ngComponentOutletInjector`: Optional custom {\\@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n *\n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if exists.\n *\n * * `ngComponentOutletNgModuleFactory`: Optional module factory to allow dynamically loading other\n * module, then load a component from that module.\n *\n * ### Syntax\n *\n * Simple\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n *\n * Customized injector/content\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n *\n * Customized ngModuleFactory\n * ```\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModuleFactory: moduleFactory;\">\n * </ng-container>\n * ```\n * ## Example\n *\n * {\\@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n *\n * A more complete example with additional options:\n *\n * {\\@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n * A more complete example with ngModuleFactory:\n *\n * {\\@example common/ngComponentOutlet/ts/module.ts region='NgModuleFactoryExample'}\n *\n * \\@experimental\n */\nvar NgComponentOutlet = /** @class */ (function () {\n    function NgComponentOutlet(_viewContainerRef) {\n        this._viewContainerRef = _viewContainerRef;\n        this._componentRef = null;\n        this._moduleRef = null;\n    }\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n    NgComponentOutlet.prototype.ngOnChanges = /**\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        this._viewContainerRef.clear();\n        this._componentRef = null;\n        if (this.ngComponentOutlet) {\n            var /** @type {?} */ elInjector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n            if (changes['ngComponentOutletNgModuleFactory']) {\n                if (this._moduleRef)\n                    this._moduleRef.destroy();\n                if (this.ngComponentOutletNgModuleFactory) {\n                    var /** @type {?} */ parentModule = elInjector.get(NgModuleRef);\n                    this._moduleRef = this.ngComponentOutletNgModuleFactory.create(parentModule.injector);\n                }\n                else {\n                    this._moduleRef = null;\n                }\n            }\n            var /** @type {?} */ componentFactoryResolver = this._moduleRef ? this._moduleRef.componentFactoryResolver :\n                elInjector.get(ComponentFactoryResolver);\n            var /** @type {?} */ componentFactory = componentFactoryResolver.resolveComponentFactory(this.ngComponentOutlet);\n            this._componentRef = this._viewContainerRef.createComponent(componentFactory, this._viewContainerRef.length, elInjector, this.ngComponentOutletContent);\n        }\n    };\n    /**\n     * @return {?}\n     */\n    NgComponentOutlet.prototype.ngOnDestroy = /**\n     * @return {?}\n     */\n    function () {\n        if (this._moduleRef)\n            this._moduleRef.destroy();\n    };\n    NgComponentOutlet.decorators = [\n        { type: Directive, args: [{ selector: '[ngComponentOutlet]' },] },\n    ];\n    /** @nocollapse */\n    NgComponentOutlet.ctorParameters = function () { return [\n        { type: ViewContainerRef, },\n    ]; };\n    NgComponentOutlet.propDecorators = {\n        \"ngComponentOutlet\": [{ type: Input },],\n        \"ngComponentOutletInjector\": [{ type: Input },],\n        \"ngComponentOutletContent\": [{ type: Input },],\n        \"ngComponentOutletNgModuleFactory\": [{ type: Input },],\n    };\n    return NgComponentOutlet;\n}());\nexport { NgComponentOutlet };\nfunction NgComponentOutlet_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgComponentOutlet.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgComponentOutlet.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgComponentOutlet.propDecorators;\n    /** @type {?} */\n    NgComponentOutlet.prototype.ngComponentOutlet;\n    /** @type {?} */\n    NgComponentOutlet.prototype.ngComponentOutletInjector;\n    /** @type {?} */\n    NgComponentOutlet.prototype.ngComponentOutletContent;\n    /** @type {?} */\n    NgComponentOutlet.prototype.ngComponentOutletNgModuleFactory;\n    /** @type {?} */\n    NgComponentOutlet.prototype._componentRef;\n    /** @type {?} */\n    NgComponentOutlet.prototype._moduleRef;\n    /** @type {?} */\n    NgComponentOutlet.prototype._viewContainerRef;\n}\n//# sourceMappingURL=ng_component_outlet.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, Input, IterableDiffers, TemplateRef, ViewContainerRef, isDevMode } from '@angular/core';\n/**\n * \\@stable\n * @template T\n */\nvar /**\n * \\@stable\n * @template T\n */\nNgForOfContext = /** @class */ (function () {\n    function NgForOfContext($implicit, ngForOf, index, count) {\n        this.$implicit = $implicit;\n        this.ngForOf = ngForOf;\n        this.index = index;\n        this.count = count;\n    }\n    Object.defineProperty(NgForOfContext.prototype, \"first\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.index === 0; },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(NgForOfContext.prototype, \"last\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.index === this.count - 1; },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(NgForOfContext.prototype, \"even\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.index % 2 === 0; },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(NgForOfContext.prototype, \"odd\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return !this.even; },\n        enumerable: true,\n        configurable: true\n    });\n    return NgForOfContext;\n}());\n/**\n * \\@stable\n * @template T\n */\nexport { NgForOfContext };\nfunction NgForOfContext_tsickle_Closure_declarations() {\n    /** @type {?} */\n    NgForOfContext.prototype.$implicit;\n    /** @type {?} */\n    NgForOfContext.prototype.ngForOf;\n    /** @type {?} */\n    NgForOfContext.prototype.index;\n    /** @type {?} */\n    NgForOfContext.prototype.count;\n}\n/**\n * The `NgForOf` directive instantiates a template once per item from an iterable. The context\n * for each instantiated template inherits from the outer context with the given loop variable\n * set to the current item from the iterable.\n *\n * ### Local Variables\n *\n * `NgForOf` provides several exported values that can be aliased to local variables:\n *\n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n *\n * ```\n * <li *ngFor=\"let user of userObservable | async as users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n *\n * ### Change Propagation\n *\n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n *\n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n * * Otherwise, the DOM element for that item will remain the same.\n *\n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls (such as `<input>` elements which accept user input) that are present. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n *\n * It is possible for the identities of elements in the iterator to change while the data does not.\n * This can happen, for example, if the iterator produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response will produce objects with\n * different identities, and Angular will tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted). This is an expensive operation and should\n * be avoided if possible.\n *\n * To customize the default tracking algorithm, `NgForOf` supports `trackBy` option.\n * `trackBy` takes a function which has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n *\n * ### Syntax\n *\n * - `<li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>`\n *\n * With `<ng-template>` element:\n *\n * ```\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n *\n * ### Example\n *\n * See a [live demo](http://plnkr.co/edit/KVuXxDp0qinGDyo307QW?p=preview) for a more detailed\n * example.\n *\n * \\@stable\n * @template T\n */\nvar NgForOf = /** @class */ (function () {\n    function NgForOf(_viewContainer, _template, _differs) {\n        this._viewContainer = _viewContainer;\n        this._template = _template;\n        this._differs = _differs;\n        this._differ = null;\n    }\n    Object.defineProperty(NgForOf.prototype, \"ngForTrackBy\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this._trackByFn; },\n        set: /**\n         * @param {?} fn\n         * @return {?}\n         */\n        function (fn) {\n            if (isDevMode() && fn != null && typeof fn !== 'function') {\n                // TODO(vicb): use a log service once there is a public one available\n                if (/** @type {?} */ (console) && /** @type {?} */ (console.warn)) {\n                    console.warn(\"trackBy must be a function, but received \" + JSON.stringify(fn) + \". \" +\n                        \"See https://angular.io/docs/ts/latest/api/common/index/NgFor-directive.html#!#change-propagation for more information.\");\n                }\n            }\n            this._trackByFn = fn;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(NgForOf.prototype, \"ngForTemplate\", {\n        set: /**\n         * @param {?} value\n         * @return {?}\n         */\n        function (value) {\n            // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n            // The current type is too restrictive; a template that just uses index, for example,\n            // should be acceptable.\n            if (value) {\n                this._template = value;\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n    NgForOf.prototype.ngOnChanges = /**\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        if ('ngForOf' in changes) {\n            // React on ngForOf changes only once all inputs have been initialized\n            var /** @type {?} */ value = changes['ngForOf'].currentValue;\n            if (!this._differ && value) {\n                try {\n                    this._differ = this._differs.find(value).create(this.ngForTrackBy);\n                }\n                catch (/** @type {?} */ e) {\n                    throw new Error(\"Cannot find a differ supporting object '\" + value + \"' of type '\" + getTypeNameForDebugging(value) + \"'. NgFor only supports binding to Iterables such as Arrays.\");\n                }\n            }\n        }\n    };\n    /**\n     * @return {?}\n     */\n    NgForOf.prototype.ngDoCheck = /**\n     * @return {?}\n     */\n    function () {\n        if (this._differ) {\n            var /** @type {?} */ changes = this._differ.diff(this.ngForOf);\n            if (changes)\n                this._applyChanges(changes);\n        }\n    };\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n    NgForOf.prototype._applyChanges = /**\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        var _this = this;\n        var /** @type {?} */ insertTuples = [];\n        changes.forEachOperation(function (item, adjustedPreviousIndex, currentIndex) {\n            if (item.previousIndex == null) {\n                var /** @type {?} */ view = _this._viewContainer.createEmbeddedView(_this._template, new NgForOfContext(/** @type {?} */ ((null)), _this.ngForOf, -1, -1), currentIndex);\n                var /** @type {?} */ tuple = new RecordViewTuple(item, view);\n                insertTuples.push(tuple);\n            }\n            else if (currentIndex == null) {\n                _this._viewContainer.remove(adjustedPreviousIndex);\n            }\n            else {\n                var /** @type {?} */ view = /** @type {?} */ ((_this._viewContainer.get(adjustedPreviousIndex)));\n                _this._viewContainer.move(view, currentIndex);\n                var /** @type {?} */ tuple = new RecordViewTuple(item, /** @type {?} */ (view));\n                insertTuples.push(tuple);\n            }\n        });\n        for (var /** @type {?} */ i = 0; i < insertTuples.length; i++) {\n            this._perViewChange(insertTuples[i].view, insertTuples[i].record);\n        }\n        for (var /** @type {?} */ i = 0, /** @type {?} */ ilen = this._viewContainer.length; i < ilen; i++) {\n            var /** @type {?} */ viewRef = /** @type {?} */ (this._viewContainer.get(i));\n            viewRef.context.index = i;\n            viewRef.context.count = ilen;\n        }\n        changes.forEachIdentityChange(function (record) {\n            var /** @type {?} */ viewRef = /** @type {?} */ (_this._viewContainer.get(record.currentIndex));\n            viewRef.context.$implicit = record.item;\n        });\n    };\n    /**\n     * @param {?} view\n     * @param {?} record\n     * @return {?}\n     */\n    NgForOf.prototype._perViewChange = /**\n     * @param {?} view\n     * @param {?} record\n     * @return {?}\n     */\n    function (view, record) {\n        view.context.$implicit = record.item;\n    };\n    NgForOf.decorators = [\n        { type: Directive, args: [{ selector: '[ngFor][ngForOf]' },] },\n    ];\n    /** @nocollapse */\n    NgForOf.ctorParameters = function () { return [\n        { type: ViewContainerRef, },\n        { type: TemplateRef, },\n        { type: IterableDiffers, },\n    ]; };\n    NgForOf.propDecorators = {\n        \"ngForOf\": [{ type: Input },],\n        \"ngForTrackBy\": [{ type: Input },],\n        \"ngForTemplate\": [{ type: Input },],\n    };\n    return NgForOf;\n}());\nexport { NgForOf };\nfunction NgForOf_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgForOf.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgForOf.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgForOf.propDecorators;\n    /** @type {?} */\n    NgForOf.prototype.ngForOf;\n    /** @type {?} */\n    NgForOf.prototype._differ;\n    /** @type {?} */\n    NgForOf.prototype._trackByFn;\n    /** @type {?} */\n    NgForOf.prototype._viewContainer;\n    /** @type {?} */\n    NgForOf.prototype._template;\n    /** @type {?} */\n    NgForOf.prototype._differs;\n}\n/**\n * @template T\n */\nvar /**\n * @template T\n */\nRecordViewTuple = /** @class */ (function () {\n    function RecordViewTuple(record, view) {\n        this.record = record;\n        this.view = view;\n    }\n    return RecordViewTuple;\n}());\nfunction RecordViewTuple_tsickle_Closure_declarations() {\n    /** @type {?} */\n    RecordViewTuple.prototype.record;\n    /** @type {?} */\n    RecordViewTuple.prototype.view;\n}\n/**\n * @param {?} type\n * @return {?}\n */\nexport function getTypeNameForDebugging(type) {\n    return type['name'] || typeof type;\n}\n//# sourceMappingURL=ng_for_of.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';\n/**\n * Conditionally includes a template based on the value of an `expression`.\n *\n * `ngIf` evaluates the `expression` and then renders the `then` or `else` template in its place\n * when expression is truthy or falsy respectively. Typically the:\n *  - `then` template is the inline template of `ngIf` unless bound to a different value.\n *  - `else` template is blank unless it is bound.\n *\n * ## Most common usage\n *\n * The most common usage of the `ngIf` directive is to conditionally show the inline template as\n * seen in this example:\n * {\\@example common/ngIf/ts/module.ts region='NgIfSimple'}\n *\n * ## Showing an alternative template using `else`\n *\n * If it is necessary to display a template when the `expression` is falsy use the `else` template\n * binding as shown. Note that the `else` binding points to a `<ng-template>` labeled `#elseBlock`.\n * The template can be defined anywhere in the component view but is typically placed right after\n * `ngIf` for readability.\n *\n * {\\@example common/ngIf/ts/module.ts region='NgIfElse'}\n *\n * ## Using non-inlined `then` template\n *\n * Usually the `then` template is the inlined template of the `ngIf`, but it can be changed using\n * a binding (just like `else`). Because `then` and `else` are bindings, the template references can\n * change at runtime as shown in this example.\n *\n * {\\@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n *\n * ## Storing conditional result in a variable\n *\n * A common pattern is that we need to show a set of properties from the same object. If the\n * object is undefined, then we have to use the safe-traversal-operator `?.` to guard against\n * dereferencing a `null` value. This is especially the case when waiting on async data such as\n * when using the `async` pipe as shown in following example:\n *\n * ```\n * Hello {{ (userStream|async)?.last }}, {{ (userStream|async)?.first }}!\n * ```\n *\n * There are several inefficiencies in the above example:\n *  - We create multiple subscriptions on `userStream`. One for each `async` pipe, or two in the\n *    example above.\n *  - We cannot display an alternative screen while waiting for the data to arrive asynchronously.\n *  - We have to use the safe-traversal-operator `?.` to access properties, which is cumbersome.\n *  - We have to place the `async` pipe in parenthesis.\n *\n * A better way to do this is to use `ngIf` and store the result of the condition in a local\n * variable as shown in the the example below:\n *\n * {\\@example common/ngIf/ts/module.ts region='NgIfAs'}\n *\n * Notice that:\n *  - We use only one `async` pipe and hence only one subscription gets created.\n *  - `ngIf` stores the result of the `userStream|async` in the local variable `user`.\n *  - The local `user` can then be bound repeatedly in a more efficient way.\n *  - No need to use the safe-traversal-operator `?.` to access properties as `ngIf` will only\n *    display the data if `userStream` returns a value.\n *  - We can display an alternative template while waiting for the data.\n *\n * ### Syntax\n *\n * Simple form:\n * - `<div *ngIf=\"condition\">...</div>`\n * - `<ng-template [ngIf]=\"condition\"><div>...</div></ng-template>`\n *\n * Form with an else block:\n * ```\n * <div *ngIf=\"condition; else elseBlock\">...</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n *\n * Form with a `then` and `else` block:\n * ```\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>...</ng-template>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n *\n * Form with storing the value locally:\n * ```\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>...</ng-template>\n * ```\n *\n * \\@stable\n */\nvar NgIf = /** @class */ (function () {\n    function NgIf(_viewContainer, templateRef) {\n        this._viewContainer = _viewContainer;\n        this._context = new NgIfContext();\n        this._thenTemplateRef = null;\n        this._elseTemplateRef = null;\n        this._thenViewRef = null;\n        this._elseViewRef = null;\n        this._thenTemplateRef = templateRef;\n    }\n    Object.defineProperty(NgIf.prototype, \"ngIf\", {\n        set: /**\n         * @param {?} condition\n         * @return {?}\n         */\n        function (condition) {\n            this._context.$implicit = this._context.ngIf = condition;\n            this._updateView();\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(NgIf.prototype, \"ngIfThen\", {\n        set: /**\n         * @param {?} templateRef\n         * @return {?}\n         */\n        function (templateRef) {\n            this._thenTemplateRef = templateRef;\n            this._thenViewRef = null; // clear previous view if any.\n            this._updateView();\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(NgIf.prototype, \"ngIfElse\", {\n        set: /**\n         * @param {?} templateRef\n         * @return {?}\n         */\n        function (templateRef) {\n            this._elseTemplateRef = templateRef;\n            this._elseViewRef = null; // clear previous view if any.\n            this._updateView();\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @return {?}\n     */\n    NgIf.prototype._updateView = /**\n     * @return {?}\n     */\n    function () {\n        if (this._context.$implicit) {\n            if (!this._thenViewRef) {\n                this._viewContainer.clear();\n                this._elseViewRef = null;\n                if (this._thenTemplateRef) {\n                    this._thenViewRef =\n                        this._viewContainer.createEmbeddedView(this._thenTemplateRef, this._context);\n                }\n            }\n        }\n        else {\n            if (!this._elseViewRef) {\n                this._viewContainer.clear();\n                this._thenViewRef = null;\n                if (this._elseTemplateRef) {\n                    this._elseViewRef =\n                        this._viewContainer.createEmbeddedView(this._elseTemplateRef, this._context);\n                }\n            }\n        }\n    };\n    NgIf.decorators = [\n        { type: Directive, args: [{ selector: '[ngIf]' },] },\n    ];\n    /** @nocollapse */\n    NgIf.ctorParameters = function () { return [\n        { type: ViewContainerRef, },\n        { type: TemplateRef, },\n    ]; };\n    NgIf.propDecorators = {\n        \"ngIf\": [{ type: Input },],\n        \"ngIfThen\": [{ type: Input },],\n        \"ngIfElse\": [{ type: Input },],\n    };\n    return NgIf;\n}());\nexport { NgIf };\nfunction NgIf_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgIf.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgIf.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgIf.propDecorators;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    NgIf.ngIfUseIfTypeGuard;\n    /** @type {?} */\n    NgIf.prototype._context;\n    /** @type {?} */\n    NgIf.prototype._thenTemplateRef;\n    /** @type {?} */\n    NgIf.prototype._elseTemplateRef;\n    /** @type {?} */\n    NgIf.prototype._thenViewRef;\n    /** @type {?} */\n    NgIf.prototype._elseViewRef;\n    /** @type {?} */\n    NgIf.prototype._viewContainer;\n}\n/**\n * \\@stable\n */\nvar /**\n * \\@stable\n */\nNgIfContext = /** @class */ (function () {\n    function NgIfContext() {\n        this.$implicit = null;\n        this.ngIf = null;\n    }\n    return NgIfContext;\n}());\n/**\n * \\@stable\n */\nexport { NgIfContext };\nfunction NgIfContext_tsickle_Closure_declarations() {\n    /** @type {?} */\n    NgIfContext.prototype.$implicit;\n    /** @type {?} */\n    NgIfContext.prototype.ngIf;\n}\n//# sourceMappingURL=ng_if.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, Host, Input, TemplateRef, ViewContainerRef } from '@angular/core';\nvar SwitchView = /** @class */ (function () {\n    function SwitchView(_viewContainerRef, _templateRef) {\n        this._viewContainerRef = _viewContainerRef;\n        this._templateRef = _templateRef;\n        this._created = false;\n    }\n    /**\n     * @return {?}\n     */\n    SwitchView.prototype.create = /**\n     * @return {?}\n     */\n    function () {\n        this._created = true;\n        this._viewContainerRef.createEmbeddedView(this._templateRef);\n    };\n    /**\n     * @return {?}\n     */\n    SwitchView.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        this._created = false;\n        this._viewContainerRef.clear();\n    };\n    /**\n     * @param {?} created\n     * @return {?}\n     */\n    SwitchView.prototype.enforceState = /**\n     * @param {?} created\n     * @return {?}\n     */\n    function (created) {\n        if (created && !this._created) {\n            this.create();\n        }\n        else if (!created && this._created) {\n            this.destroy();\n        }\n    };\n    return SwitchView;\n}());\nexport { SwitchView };\nfunction SwitchView_tsickle_Closure_declarations() {\n    /** @type {?} */\n    SwitchView.prototype._created;\n    /** @type {?} */\n    SwitchView.prototype._viewContainerRef;\n    /** @type {?} */\n    SwitchView.prototype._templateRef;\n}\n/**\n * \\@ngModule CommonModule\n *\n * \\@whatItDoes Adds / removes DOM sub-trees when the nest match expressions matches the switch\n *             expression.\n *\n * \\@howToUse\n * ```\n *     <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n * \\@description\n *\n * `NgSwitch` stamps out nested views when their match expression value matches the value of the\n * switch expression.\n *\n * In other words:\n * - you define a container element (where you place the directive with a switch expression on the\n * `[ngSwitch]=\"...\"` attribute)\n * - you define inner views inside the `NgSwitch` and place a `*ngSwitchCase` attribute on the view\n * root elements.\n *\n * Elements within `NgSwitch` but outside of a `NgSwitchCase` or `NgSwitchDefault` directives will\n * be preserved at the location.\n *\n * The `ngSwitchCase` directive informs the parent `NgSwitch` of which view to display when the\n * expression is evaluated.\n * When no matching expression is found on a `ngSwitchCase` view, the `ngSwitchDefault` view is\n * stamped out.\n *\n * \\@stable\n */\nvar NgSwitch = /** @class */ (function () {\n    function NgSwitch() {\n        this._defaultUsed = false;\n        this._caseCount = 0;\n        this._lastCaseCheckIndex = 0;\n        this._lastCasesMatched = false;\n    }\n    Object.defineProperty(NgSwitch.prototype, \"ngSwitch\", {\n        set: /**\n         * @param {?} newValue\n         * @return {?}\n         */\n        function (newValue) {\n            this._ngSwitch = newValue;\n            if (this._caseCount === 0) {\n                this._updateDefaultCases(true);\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /** @internal */\n    /**\n     * \\@internal\n     * @return {?}\n     */\n    NgSwitch.prototype._addCase = /**\n     * \\@internal\n     * @return {?}\n     */\n    function () { return this._caseCount++; };\n    /** @internal */\n    /**\n     * \\@internal\n     * @param {?} view\n     * @return {?}\n     */\n    NgSwitch.prototype._addDefault = /**\n     * \\@internal\n     * @param {?} view\n     * @return {?}\n     */\n    function (view) {\n        if (!this._defaultViews) {\n            this._defaultViews = [];\n        }\n        this._defaultViews.push(view);\n    };\n    /** @internal */\n    /**\n     * \\@internal\n     * @param {?} value\n     * @return {?}\n     */\n    NgSwitch.prototype._matchCase = /**\n     * \\@internal\n     * @param {?} value\n     * @return {?}\n     */\n    function (value) {\n        var /** @type {?} */ matched = value == this._ngSwitch;\n        this._lastCasesMatched = this._lastCasesMatched || matched;\n        this._lastCaseCheckIndex++;\n        if (this._lastCaseCheckIndex === this._caseCount) {\n            this._updateDefaultCases(!this._lastCasesMatched);\n            this._lastCaseCheckIndex = 0;\n            this._lastCasesMatched = false;\n        }\n        return matched;\n    };\n    /**\n     * @param {?} useDefault\n     * @return {?}\n     */\n    NgSwitch.prototype._updateDefaultCases = /**\n     * @param {?} useDefault\n     * @return {?}\n     */\n    function (useDefault) {\n        if (this._defaultViews && useDefault !== this._defaultUsed) {\n            this._defaultUsed = useDefault;\n            for (var /** @type {?} */ i = 0; i < this._defaultViews.length; i++) {\n                var /** @type {?} */ defaultView = this._defaultViews[i];\n                defaultView.enforceState(useDefault);\n            }\n        }\n    };\n    NgSwitch.decorators = [\n        { type: Directive, args: [{ selector: '[ngSwitch]' },] },\n    ];\n    /** @nocollapse */\n    NgSwitch.ctorParameters = function () { return []; };\n    NgSwitch.propDecorators = {\n        \"ngSwitch\": [{ type: Input },],\n    };\n    return NgSwitch;\n}());\nexport { NgSwitch };\nfunction NgSwitch_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgSwitch.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgSwitch.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgSwitch.propDecorators;\n    /** @type {?} */\n    NgSwitch.prototype._defaultViews;\n    /** @type {?} */\n    NgSwitch.prototype._defaultUsed;\n    /** @type {?} */\n    NgSwitch.prototype._caseCount;\n    /** @type {?} */\n    NgSwitch.prototype._lastCaseCheckIndex;\n    /** @type {?} */\n    NgSwitch.prototype._lastCasesMatched;\n    /** @type {?} */\n    NgSwitch.prototype._ngSwitch;\n}\n/**\n * \\@ngModule CommonModule\n *\n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgSwitch} when the\n *             given expression evaluate to respectively the same/different value as the switch\n *             expression.\n *\n * \\@howToUse\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * </container-element>\n * ```\n * \\@description\n *\n * Insert the sub-tree when the expression evaluates to the same value as the enclosing switch\n * expression.\n *\n * If multiple match expressions match the switch expression value, all of them are displayed.\n *\n * See {\\@link NgSwitch} for more details and example.\n *\n * \\@stable\n */\nvar NgSwitchCase = /** @class */ (function () {\n    function NgSwitchCase(viewContainer, templateRef, ngSwitch) {\n        this.ngSwitch = ngSwitch;\n        ngSwitch._addCase();\n        this._view = new SwitchView(viewContainer, templateRef);\n    }\n    /**\n     * @return {?}\n     */\n    NgSwitchCase.prototype.ngDoCheck = /**\n     * @return {?}\n     */\n    function () { this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase)); };\n    NgSwitchCase.decorators = [\n        { type: Directive, args: [{ selector: '[ngSwitchCase]' },] },\n    ];\n    /** @nocollapse */\n    NgSwitchCase.ctorParameters = function () { return [\n        { type: ViewContainerRef, },\n        { type: TemplateRef, },\n        { type: NgSwitch, decorators: [{ type: Host },] },\n    ]; };\n    NgSwitchCase.propDecorators = {\n        \"ngSwitchCase\": [{ type: Input },],\n    };\n    return NgSwitchCase;\n}());\nexport { NgSwitchCase };\nfunction NgSwitchCase_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgSwitchCase.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgSwitchCase.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgSwitchCase.propDecorators;\n    /** @type {?} */\n    NgSwitchCase.prototype._view;\n    /** @type {?} */\n    NgSwitchCase.prototype.ngSwitchCase;\n    /** @type {?} */\n    NgSwitchCase.prototype.ngSwitch;\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a view that is added to the parent {\\@link NgSwitch} when no case expressions\n * match the\n *             switch expression.\n *\n * \\@howToUse\n * ```\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-other-element *ngSwitchDefault>...</some-other-element>\n * </container-element>\n * ```\n *\n * \\@description\n *\n * Insert the sub-tree when no case expressions evaluate to the same value as the enclosing switch\n * expression.\n *\n * See {\\@link NgSwitch} for more details and example.\n *\n * \\@stable\n */\nvar NgSwitchDefault = /** @class */ (function () {\n    function NgSwitchDefault(viewContainer, templateRef, ngSwitch) {\n        ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n    }\n    NgSwitchDefault.decorators = [\n        { type: Directive, args: [{ selector: '[ngSwitchDefault]' },] },\n    ];\n    /** @nocollapse */\n    NgSwitchDefault.ctorParameters = function () { return [\n        { type: ViewContainerRef, },\n        { type: TemplateRef, },\n        { type: NgSwitch, decorators: [{ type: Host },] },\n    ]; };\n    return NgSwitchDefault;\n}());\nexport { NgSwitchDefault };\nfunction NgSwitchDefault_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgSwitchDefault.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgSwitchDefault.ctorParameters;\n}\n//# sourceMappingURL=ng_switch.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Attribute, Directive, Host, Input, TemplateRef, ViewContainerRef } from '@angular/core';\nimport { NgLocalization, getPluralCategory } from '../i18n/localization';\nimport { SwitchView } from './ng_switch';\n/**\n * \\@ngModule CommonModule\n *\n * \\@whatItDoes Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n *\n * \\@howToUse\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n *\n * \\@description\n *\n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n *\n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n *\n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n *\n * \\@experimental\n */\nvar NgPlural = /** @class */ (function () {\n    function NgPlural(_localization) {\n        this._localization = _localization;\n        this._caseViews = {};\n    }\n    Object.defineProperty(NgPlural.prototype, \"ngPlural\", {\n        set: /**\n         * @param {?} value\n         * @return {?}\n         */\n        function (value) {\n            this._switchValue = value;\n            this._updateView();\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} value\n     * @param {?} switchView\n     * @return {?}\n     */\n    NgPlural.prototype.addCase = /**\n     * @param {?} value\n     * @param {?} switchView\n     * @return {?}\n     */\n    function (value, switchView) { this._caseViews[value] = switchView; };\n    /**\n     * @return {?}\n     */\n    NgPlural.prototype._updateView = /**\n     * @return {?}\n     */\n    function () {\n        this._clearViews();\n        var /** @type {?} */ cases = Object.keys(this._caseViews);\n        var /** @type {?} */ key = getPluralCategory(this._switchValue, cases, this._localization);\n        this._activateView(this._caseViews[key]);\n    };\n    /**\n     * @return {?}\n     */\n    NgPlural.prototype._clearViews = /**\n     * @return {?}\n     */\n    function () {\n        if (this._activeView)\n            this._activeView.destroy();\n    };\n    /**\n     * @param {?} view\n     * @return {?}\n     */\n    NgPlural.prototype._activateView = /**\n     * @param {?} view\n     * @return {?}\n     */\n    function (view) {\n        if (view) {\n            this._activeView = view;\n            this._activeView.create();\n        }\n    };\n    NgPlural.decorators = [\n        { type: Directive, args: [{ selector: '[ngPlural]' },] },\n    ];\n    /** @nocollapse */\n    NgPlural.ctorParameters = function () { return [\n        { type: NgLocalization, },\n    ]; };\n    NgPlural.propDecorators = {\n        \"ngPlural\": [{ type: Input },],\n    };\n    return NgPlural;\n}());\nexport { NgPlural };\nfunction NgPlural_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgPlural.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgPlural.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgPlural.propDecorators;\n    /** @type {?} */\n    NgPlural.prototype._switchValue;\n    /** @type {?} */\n    NgPlural.prototype._activeView;\n    /** @type {?} */\n    NgPlural.prototype._caseViews;\n    /** @type {?} */\n    NgPlural.prototype._localization;\n}\n/**\n * \\@ngModule CommonModule\n *\n * \\@whatItDoes Creates a view that will be added/removed from the parent {\\@link NgPlural} when the\n *             given expression matches the plural expression according to CLDR rules.\n *\n * \\@howToUse\n * ```\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n * ```\n *\n * See {\\@link NgPlural} for more details and example.\n *\n * \\@experimental\n */\nvar NgPluralCase = /** @class */ (function () {\n    function NgPluralCase(value, template, viewContainer, ngPlural) {\n        this.value = value;\n        var /** @type {?} */ isANumber = !isNaN(Number(value));\n        ngPlural.addCase(isANumber ? \"=\" + value : value, new SwitchView(viewContainer, template));\n    }\n    NgPluralCase.decorators = [\n        { type: Directive, args: [{ selector: '[ngPluralCase]' },] },\n    ];\n    /** @nocollapse */\n    NgPluralCase.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Attribute, args: ['ngPluralCase',] },] },\n        { type: TemplateRef, },\n        { type: ViewContainerRef, },\n        { type: NgPlural, decorators: [{ type: Host },] },\n    ]; };\n    return NgPluralCase;\n}());\nexport { NgPluralCase };\nfunction NgPluralCase_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgPluralCase.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgPluralCase.ctorParameters;\n    /** @type {?} */\n    NgPluralCase.prototype.value;\n}\n//# sourceMappingURL=ng_plural.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, ElementRef, Input, KeyValueDiffers, Renderer2 } from '@angular/core';\n/**\n * \\@ngModule CommonModule\n *\n * \\@whatItDoes Update an HTML element styles.\n *\n * \\@howToUse\n * ```\n * <some-element [ngStyle]=\"{'font-style': styleExp}\">...</some-element>\n *\n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n *\n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n *\n * \\@description\n *\n * The styles are updated according to the value of the expression evaluation:\n * - keys are style names with an optional `.<unit>` suffix (ie 'top.px', 'font-style.em'),\n * - values are the values assigned to those properties (expressed in the given unit).\n *\n * \\@stable\n */\nvar NgStyle = /** @class */ (function () {\n    function NgStyle(_differs, _ngEl, _renderer) {\n        this._differs = _differs;\n        this._ngEl = _ngEl;\n        this._renderer = _renderer;\n    }\n    Object.defineProperty(NgStyle.prototype, \"ngStyle\", {\n        set: /**\n         * @param {?} v\n         * @return {?}\n         */\n        function (v) {\n            this._ngStyle = v;\n            if (!this._differ && v) {\n                this._differ = this._differs.find(v).create();\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @return {?}\n     */\n    NgStyle.prototype.ngDoCheck = /**\n     * @return {?}\n     */\n    function () {\n        if (this._differ) {\n            var /** @type {?} */ changes = this._differ.diff(this._ngStyle);\n            if (changes) {\n                this._applyChanges(changes);\n            }\n        }\n    };\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n    NgStyle.prototype._applyChanges = /**\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        var _this = this;\n        changes.forEachRemovedItem(function (record) { return _this._setStyle(record.key, null); });\n        changes.forEachAddedItem(function (record) { return _this._setStyle(record.key, record.currentValue); });\n        changes.forEachChangedItem(function (record) { return _this._setStyle(record.key, record.currentValue); });\n    };\n    /**\n     * @param {?} nameAndUnit\n     * @param {?} value\n     * @return {?}\n     */\n    NgStyle.prototype._setStyle = /**\n     * @param {?} nameAndUnit\n     * @param {?} value\n     * @return {?}\n     */\n    function (nameAndUnit, value) {\n        var _a = nameAndUnit.split('.'), name = _a[0], unit = _a[1];\n        value = value != null && unit ? \"\" + value + unit : value;\n        if (value != null) {\n            this._renderer.setStyle(this._ngEl.nativeElement, name, /** @type {?} */ (value));\n        }\n        else {\n            this._renderer.removeStyle(this._ngEl.nativeElement, name);\n        }\n    };\n    NgStyle.decorators = [\n        { type: Directive, args: [{ selector: '[ngStyle]' },] },\n    ];\n    /** @nocollapse */\n    NgStyle.ctorParameters = function () { return [\n        { type: KeyValueDiffers, },\n        { type: ElementRef, },\n        { type: Renderer2, },\n    ]; };\n    NgStyle.propDecorators = {\n        \"ngStyle\": [{ type: Input },],\n    };\n    return NgStyle;\n}());\nexport { NgStyle };\nfunction NgStyle_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgStyle.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgStyle.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgStyle.propDecorators;\n    /** @type {?} */\n    NgStyle.prototype._ngStyle;\n    /** @type {?} */\n    NgStyle.prototype._differ;\n    /** @type {?} */\n    NgStyle.prototype._differs;\n    /** @type {?} */\n    NgStyle.prototype._ngEl;\n    /** @type {?} */\n    NgStyle.prototype._renderer;\n}\n//# sourceMappingURL=ng_style.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';\n/**\n * \\@ngModule CommonModule\n *\n * \\@whatItDoes Inserts an embedded view from a prepared `TemplateRef`\n *\n * \\@howToUse\n * ```\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n *\n * \\@description\n *\n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n *\n * Note: using the key `$implicit` in the context object will set its value as default.\n *\n * ## Example\n *\n * {\\@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n *\n * \\@stable\n */\nvar NgTemplateOutlet = /** @class */ (function () {\n    function NgTemplateOutlet(_viewContainerRef) {\n        this._viewContainerRef = _viewContainerRef;\n    }\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n    NgTemplateOutlet.prototype.ngOnChanges = /**\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        var /** @type {?} */ recreateView = this._shouldRecreateView(changes);\n        if (recreateView) {\n            if (this._viewRef) {\n                this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._viewRef));\n            }\n            if (this.ngTemplateOutlet) {\n                this._viewRef = this._viewContainerRef.createEmbeddedView(this.ngTemplateOutlet, this.ngTemplateOutletContext);\n            }\n        }\n        else {\n            if (this._viewRef && this.ngTemplateOutletContext) {\n                this._updateExistingContext(this.ngTemplateOutletContext);\n            }\n        }\n    };\n    /**\n     * We need to re-create existing embedded view if:\n     * - templateRef has changed\n     * - context has changes\n     *\n     * We mark context object as changed when the corresponding object\n     * shape changes (new properties are added or existing properties are removed).\n     * In other words we consider context with the same properties as \"the same\" even\n     * if object reference changes (see https://github.com/angular/angular/issues/13407).\n     * @param {?} changes\n     * @return {?}\n     */\n    NgTemplateOutlet.prototype._shouldRecreateView = /**\n     * We need to re-create existing embedded view if:\n     * - templateRef has changed\n     * - context has changes\n     *\n     * We mark context object as changed when the corresponding object\n     * shape changes (new properties are added or existing properties are removed).\n     * In other words we consider context with the same properties as \"the same\" even\n     * if object reference changes (see https://github.com/angular/angular/issues/13407).\n     * @param {?} changes\n     * @return {?}\n     */\n    function (changes) {\n        var /** @type {?} */ ctxChange = changes['ngTemplateOutletContext'];\n        return !!changes['ngTemplateOutlet'] || (ctxChange && this._hasContextShapeChanged(ctxChange));\n    };\n    /**\n     * @param {?} ctxChange\n     * @return {?}\n     */\n    NgTemplateOutlet.prototype._hasContextShapeChanged = /**\n     * @param {?} ctxChange\n     * @return {?}\n     */\n    function (ctxChange) {\n        var /** @type {?} */ prevCtxKeys = Object.keys(ctxChange.previousValue || {});\n        var /** @type {?} */ currCtxKeys = Object.keys(ctxChange.currentValue || {});\n        if (prevCtxKeys.length === currCtxKeys.length) {\n            for (var _i = 0, currCtxKeys_1 = currCtxKeys; _i < currCtxKeys_1.length; _i++) {\n                var propName = currCtxKeys_1[_i];\n                if (prevCtxKeys.indexOf(propName) === -1) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        else {\n            return true;\n        }\n    };\n    /**\n     * @param {?} ctx\n     * @return {?}\n     */\n    NgTemplateOutlet.prototype._updateExistingContext = /**\n     * @param {?} ctx\n     * @return {?}\n     */\n    function (ctx) {\n        for (var _i = 0, _a = Object.keys(ctx); _i < _a.length; _i++) {\n            var propName = _a[_i];\n            (/** @type {?} */ (this._viewRef.context))[propName] = (/** @type {?} */ (this.ngTemplateOutletContext))[propName];\n        }\n    };\n    NgTemplateOutlet.decorators = [\n        { type: Directive, args: [{ selector: '[ngTemplateOutlet]' },] },\n    ];\n    /** @nocollapse */\n    NgTemplateOutlet.ctorParameters = function () { return [\n        { type: ViewContainerRef, },\n    ]; };\n    NgTemplateOutlet.propDecorators = {\n        \"ngTemplateOutletContext\": [{ type: Input },],\n        \"ngTemplateOutlet\": [{ type: Input },],\n    };\n    return NgTemplateOutlet;\n}());\nexport { NgTemplateOutlet };\nfunction NgTemplateOutlet_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NgTemplateOutlet.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NgTemplateOutlet.ctorParameters;\n    /** @type {!Object<string,!Array<{type: !Function, args: (undefined|!Array<?>)}>>} */\n    NgTemplateOutlet.propDecorators;\n    /** @type {?} */\n    NgTemplateOutlet.prototype._viewRef;\n    /** @type {?} */\n    NgTemplateOutlet.prototype.ngTemplateOutletContext;\n    /** @type {?} */\n    NgTemplateOutlet.prototype.ngTemplateOutlet;\n    /** @type {?} */\n    NgTemplateOutlet.prototype._viewContainerRef;\n}\n//# sourceMappingURL=ng_template_outlet.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { NgClass } from './ng_class';\nimport { NgComponentOutlet } from './ng_component_outlet';\nimport { NgForOf, NgForOfContext } from './ng_for_of';\nimport { NgIf, NgIfContext } from './ng_if';\nimport { NgPlural, NgPluralCase } from './ng_plural';\nimport { NgStyle } from './ng_style';\nimport { Ng<PERSON><PERSON>, NgSwitchCase, NgSwitchDefault } from './ng_switch';\nimport { NgTemplateOutlet } from './ng_template_outlet';\nexport { NgClass, NgComponentOutlet, NgForOf, NgForOfContext, NgIf, NgIf<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ase, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet };\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\nexport var /** @type {?} */ COMMON_DIRECTIVES = [\n    NgClass,\n    NgComponentOutlet,\n    NgForOf,\n    NgIf,\n    NgTemplateOutlet,\n    NgStyle,\n    NgSwitch,\n    NgSwitchCase,\n    NgSwitchDefault,\n    NgPlural,\n    NgPluralCase,\n];\n//# sourceMappingURL=index.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { FormStyle, FormatWidth, NumberSymbol, TranslationWidth, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleDayNames, getLocaleDayPeriods, getLocaleEraNames, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocaleId, getLocaleMonthNames, getLocaleNumberSymbol, getLocaleTimeFormat } from './locale_data_api';\nvar /** @type {?} */ NAMED_FORMATS = {};\nvar /** @type {?} */ DATE_FORMATS_SPLIT = /((?:[^GyMLwWdEabBhHmsSzZO']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\\s\\S]*)/;\n/** @enum {number} */\nvar ZoneWidth = {\n    Short: 0,\n    ShortGMT: 1,\n    Long: 2,\n    Extended: 3,\n};\nZoneWidth[ZoneWidth.Short] = \"Short\";\nZoneWidth[ZoneWidth.ShortGMT] = \"ShortGMT\";\nZoneWidth[ZoneWidth.Long] = \"Long\";\nZoneWidth[ZoneWidth.Extended] = \"Extended\";\n/** @enum {number} */\nvar DateType = {\n    FullYear: 0,\n    Month: 1,\n    Date: 2,\n    Hours: 3,\n    Minutes: 4,\n    Seconds: 5,\n    Milliseconds: 6,\n    Day: 7,\n};\nDateType[DateType.FullYear] = \"FullYear\";\nDateType[DateType.Month] = \"Month\";\nDateType[DateType.Date] = \"Date\";\nDateType[DateType.Hours] = \"Hours\";\nDateType[DateType.Minutes] = \"Minutes\";\nDateType[DateType.Seconds] = \"Seconds\";\nDateType[DateType.Milliseconds] = \"Milliseconds\";\nDateType[DateType.Day] = \"Day\";\n/** @enum {number} */\nvar TranslationType = {\n    DayPeriods: 0,\n    Days: 1,\n    Months: 2,\n    Eras: 3,\n};\nTranslationType[TranslationType.DayPeriods] = \"DayPeriods\";\nTranslationType[TranslationType.Days] = \"Days\";\nTranslationType[TranslationType.Months] = \"Months\";\nTranslationType[TranslationType.Eras] = \"Eras\";\n/**\n * Transforms a date to a locale string based on a pattern and a timezone\n *\n * \\@internal\n * @param {?} date\n * @param {?} format\n * @param {?} locale\n * @param {?=} timezone\n * @return {?}\n */\nexport function formatDate(date, format, locale, timezone) {\n    var /** @type {?} */ namedFormat = getNamedFormat(locale, format);\n    format = namedFormat || format;\n    var /** @type {?} */ parts = [];\n    var /** @type {?} */ match;\n    while (format) {\n        match = DATE_FORMATS_SPLIT.exec(format);\n        if (match) {\n            parts = parts.concat(match.slice(1));\n            var /** @type {?} */ part = parts.pop();\n            if (!part) {\n                break;\n            }\n            format = part;\n        }\n        else {\n            parts.push(format);\n            break;\n        }\n    }\n    var /** @type {?} */ dateTimezoneOffset = date.getTimezoneOffset();\n    if (timezone) {\n        dateTimezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n        date = convertTimezoneToLocal(date, timezone, true);\n    }\n    var /** @type {?} */ text = '';\n    parts.forEach(function (value) {\n        var /** @type {?} */ dateFormatter = getDateFormatter(value);\n        text += dateFormatter ?\n            dateFormatter(date, locale, dateTimezoneOffset) :\n            value === '\\'\\'' ? '\\'' : value.replace(/(^'|'$)/g, '').replace(/''/g, '\\'');\n    });\n    return text;\n}\n/**\n * @param {?} locale\n * @param {?} format\n * @return {?}\n */\nfunction getNamedFormat(locale, format) {\n    var /** @type {?} */ localeId = getLocaleId(locale);\n    NAMED_FORMATS[localeId] = NAMED_FORMATS[localeId] || {};\n    if (NAMED_FORMATS[localeId][format]) {\n        return NAMED_FORMATS[localeId][format];\n    }\n    var /** @type {?} */ formatValue = '';\n    switch (format) {\n        case 'shortDate':\n            formatValue = getLocaleDateFormat(locale, FormatWidth.Short);\n            break;\n        case 'mediumDate':\n            formatValue = getLocaleDateFormat(locale, FormatWidth.Medium);\n            break;\n        case 'longDate':\n            formatValue = getLocaleDateFormat(locale, FormatWidth.Long);\n            break;\n        case 'fullDate':\n            formatValue = getLocaleDateFormat(locale, FormatWidth.Full);\n            break;\n        case 'shortTime':\n            formatValue = getLocaleTimeFormat(locale, FormatWidth.Short);\n            break;\n        case 'mediumTime':\n            formatValue = getLocaleTimeFormat(locale, FormatWidth.Medium);\n            break;\n        case 'longTime':\n            formatValue = getLocaleTimeFormat(locale, FormatWidth.Long);\n            break;\n        case 'fullTime':\n            formatValue = getLocaleTimeFormat(locale, FormatWidth.Full);\n            break;\n        case 'short':\n            var /** @type {?} */ shortTime = getNamedFormat(locale, 'shortTime');\n            var /** @type {?} */ shortDate = getNamedFormat(locale, 'shortDate');\n            formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Short), [shortTime, shortDate]);\n            break;\n        case 'medium':\n            var /** @type {?} */ mediumTime = getNamedFormat(locale, 'mediumTime');\n            var /** @type {?} */ mediumDate = getNamedFormat(locale, 'mediumDate');\n            formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Medium), [mediumTime, mediumDate]);\n            break;\n        case 'long':\n            var /** @type {?} */ longTime = getNamedFormat(locale, 'longTime');\n            var /** @type {?} */ longDate = getNamedFormat(locale, 'longDate');\n            formatValue =\n                formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Long), [longTime, longDate]);\n            break;\n        case 'full':\n            var /** @type {?} */ fullTime = getNamedFormat(locale, 'fullTime');\n            var /** @type {?} */ fullDate = getNamedFormat(locale, 'fullDate');\n            formatValue =\n                formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Full), [fullTime, fullDate]);\n            break;\n    }\n    if (formatValue) {\n        NAMED_FORMATS[localeId][format] = formatValue;\n    }\n    return formatValue;\n}\n/**\n * @param {?} str\n * @param {?} opt_values\n * @return {?}\n */\nfunction formatDateTime(str, opt_values) {\n    if (opt_values) {\n        str = str.replace(/\\{([^}]+)}/g, function (match, key) {\n            return (opt_values != null && key in opt_values) ? opt_values[key] : match;\n        });\n    }\n    return str;\n}\n/**\n * @param {?} num\n * @param {?} digits\n * @param {?=} minusSign\n * @param {?=} trim\n * @param {?=} negWrap\n * @return {?}\n */\nfunction padNumber(num, digits, minusSign, trim, negWrap) {\n    if (minusSign === void 0) { minusSign = '-'; }\n    var /** @type {?} */ neg = '';\n    if (num < 0 || (negWrap && num <= 0)) {\n        if (negWrap) {\n            num = -num + 1;\n        }\n        else {\n            num = -num;\n            neg = minusSign;\n        }\n    }\n    var /** @type {?} */ strNum = '' + num;\n    while (strNum.length < digits)\n        strNum = '0' + strNum;\n    if (trim) {\n        strNum = strNum.substr(strNum.length - digits);\n    }\n    return neg + strNum;\n}\n/**\n * Returns a date formatter that transforms a date into its locale digit representation\n * @param {?} name\n * @param {?} size\n * @param {?=} offset\n * @param {?=} trim\n * @param {?=} negWrap\n * @return {?}\n */\nfunction dateGetter(name, size, offset, trim, negWrap) {\n    if (offset === void 0) { offset = 0; }\n    if (trim === void 0) { trim = false; }\n    if (negWrap === void 0) { negWrap = false; }\n    return function (date, locale) {\n        var /** @type {?} */ part = getDatePart(name, date, size);\n        if (offset > 0 || part > -offset) {\n            part += offset;\n        }\n        if (name === DateType.Hours && part === 0 && offset === -12) {\n            part = 12;\n        }\n        return padNumber(part, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign), trim, negWrap);\n    };\n}\n/**\n * @param {?} name\n * @param {?} date\n * @param {?} size\n * @return {?}\n */\nfunction getDatePart(name, date, size) {\n    switch (name) {\n        case DateType.FullYear:\n            return date.getFullYear();\n        case DateType.Month:\n            return date.getMonth();\n        case DateType.Date:\n            return date.getDate();\n        case DateType.Hours:\n            return date.getHours();\n        case DateType.Minutes:\n            return date.getMinutes();\n        case DateType.Seconds:\n            return date.getSeconds();\n        case DateType.Milliseconds:\n            var /** @type {?} */ div = size === 1 ? 100 : (size === 2 ? 10 : 1);\n            return Math.round(date.getMilliseconds() / div);\n        case DateType.Day:\n            return date.getDay();\n        default:\n            throw new Error(\"Unknown DateType value \\\"\" + name + \"\\\".\");\n    }\n}\n/**\n * Returns a date formatter that transforms a date into its locale string representation\n * @param {?} name\n * @param {?} width\n * @param {?=} form\n * @param {?=} extended\n * @return {?}\n */\nfunction dateStrGetter(name, width, form, extended) {\n    if (form === void 0) { form = FormStyle.Format; }\n    if (extended === void 0) { extended = false; }\n    return function (date, locale) {\n        return getDateTranslation(date, locale, name, width, form, extended);\n    };\n}\n/**\n * Returns the locale translation of a date for a given form, type and width\n * @param {?} date\n * @param {?} locale\n * @param {?} name\n * @param {?} width\n * @param {?} form\n * @param {?} extended\n * @return {?}\n */\nfunction getDateTranslation(date, locale, name, width, form, extended) {\n    switch (name) {\n        case TranslationType.Months:\n            return getLocaleMonthNames(locale, form, width)[date.getMonth()];\n        case TranslationType.Days:\n            return getLocaleDayNames(locale, form, width)[date.getDay()];\n        case TranslationType.DayPeriods:\n            var /** @type {?} */ currentHours_1 = date.getHours();\n            var /** @type {?} */ currentMinutes_1 = date.getMinutes();\n            if (extended) {\n                var /** @type {?} */ rules = getLocaleExtraDayPeriodRules(locale);\n                var /** @type {?} */ dayPeriods_1 = getLocaleExtraDayPeriods(locale, form, width);\n                var /** @type {?} */ result_1;\n                rules.forEach(function (rule, index) {\n                    if (Array.isArray(rule)) {\n                        // morning, afternoon, evening, night\n                        var _a = rule[0], hoursFrom = _a.hours, minutesFrom = _a.minutes;\n                        var _b = rule[1], hoursTo = _b.hours, minutesTo = _b.minutes;\n                        if (currentHours_1 >= hoursFrom && currentMinutes_1 >= minutesFrom &&\n                            (currentHours_1 < hoursTo ||\n                                (currentHours_1 === hoursTo && currentMinutes_1 < minutesTo))) {\n                            result_1 = dayPeriods_1[index];\n                        }\n                    }\n                    else {\n                        // noon or midnight\n                        var hours = rule.hours, minutes = rule.minutes;\n                        if (hours === currentHours_1 && minutes === currentMinutes_1) {\n                            result_1 = dayPeriods_1[index];\n                        }\n                    }\n                });\n                if (result_1) {\n                    return result_1;\n                }\n            }\n            // if no rules for the day periods, we use am/pm by default\n            return getLocaleDayPeriods(locale, form, /** @type {?} */ (width))[currentHours_1 < 12 ? 0 : 1];\n        case TranslationType.Eras:\n            return getLocaleEraNames(locale, /** @type {?} */ (width))[date.getFullYear() <= 0 ? 0 : 1];\n        default:\n            // This default case is not needed by TypeScript compiler, as the switch is exhaustive.\n            // However Closure Compiler does not understand that and reports an error in typed mode.\n            // The `throw new Error` below works around the problem, and the unexpected: never variable\n            // makes sure tsc still checks this code is unreachable.\n            var /** @type {?} */ unexpected = name;\n            throw new Error(\"unexpected translation type \" + unexpected);\n    }\n}\n/**\n * Returns a date formatter that transforms a date and an offset into a timezone with ISO8601 or\n * GMT format depending on the width (eg: short = +0430, short:GMT = GMT+4, long = GMT+04:30,\n * extended = +04:30)\n * @param {?} width\n * @return {?}\n */\nfunction timeZoneGetter(width) {\n    return function (date, locale, offset) {\n        var /** @type {?} */ zone = -1 * offset;\n        var /** @type {?} */ minusSign = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n        var /** @type {?} */ hours = zone > 0 ? Math.floor(zone / 60) : Math.ceil(zone / 60);\n        switch (width) {\n            case ZoneWidth.Short:\n                return ((zone >= 0) ? '+' : '') + padNumber(hours, 2, minusSign) +\n                    padNumber(Math.abs(zone % 60), 2, minusSign);\n            case ZoneWidth.ShortGMT:\n                return 'GMT' + ((zone >= 0) ? '+' : '') + padNumber(hours, 1, minusSign);\n            case ZoneWidth.Long:\n                return 'GMT' + ((zone >= 0) ? '+' : '') + padNumber(hours, 2, minusSign) + ':' +\n                    padNumber(Math.abs(zone % 60), 2, minusSign);\n            case ZoneWidth.Extended:\n                if (offset === 0) {\n                    return 'Z';\n                }\n                else {\n                    return ((zone >= 0) ? '+' : '') + padNumber(hours, 2, minusSign) + ':' +\n                        padNumber(Math.abs(zone % 60), 2, minusSign);\n                }\n            default:\n                throw new Error(\"Unknown zone width \\\"\" + width + \"\\\"\");\n        }\n    };\n}\nvar /** @type {?} */ JANUARY = 0;\nvar /** @type {?} */ THURSDAY = 4;\n/**\n * @param {?} year\n * @return {?}\n */\nfunction getFirstThursdayOfYear(year) {\n    var /** @type {?} */ firstDayOfYear = (new Date(year, JANUARY, 1)).getDay();\n    return new Date(year, 0, 1 + ((firstDayOfYear <= THURSDAY) ? THURSDAY : THURSDAY + 7) - firstDayOfYear);\n}\n/**\n * @param {?} datetime\n * @return {?}\n */\nfunction getThursdayThisWeek(datetime) {\n    return new Date(datetime.getFullYear(), datetime.getMonth(), datetime.getDate() + (THURSDAY - datetime.getDay()));\n}\n/**\n * @param {?} size\n * @param {?=} monthBased\n * @return {?}\n */\nfunction weekGetter(size, monthBased) {\n    if (monthBased === void 0) { monthBased = false; }\n    return function (date, locale) {\n        var /** @type {?} */ result;\n        if (monthBased) {\n            var /** @type {?} */ nbDaysBefore1stDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1).getDay() - 1;\n            var /** @type {?} */ today = date.getDate();\n            result = 1 + Math.floor((today + nbDaysBefore1stDayOfMonth) / 7);\n        }\n        else {\n            var /** @type {?} */ firstThurs = getFirstThursdayOfYear(date.getFullYear());\n            var /** @type {?} */ thisThurs = getThursdayThisWeek(date);\n            var /** @type {?} */ diff = thisThurs.getTime() - firstThurs.getTime();\n            result = 1 + Math.round(diff / 6.048e8); // 6.048e8 ms per week\n        }\n        return padNumber(result, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n    };\n}\nvar /** @type {?} */ DATE_FORMATS = {};\n/**\n * @param {?} format\n * @return {?}\n */\nfunction getDateFormatter(format) {\n    if (DATE_FORMATS[format]) {\n        return DATE_FORMATS[format];\n    }\n    var /** @type {?} */ formatter;\n    switch (format) {\n        // Era name (AD/BC)\n        case 'G':\n        case 'GG':\n        case 'GGG':\n            formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Abbreviated);\n            break;\n        case 'GGGG':\n            formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Wide);\n            break;\n        case 'GGGGG':\n            formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Narrow);\n            break;\n        // 1 digit representation of the year, e.g. (AD 1 => 1, AD 199 => 199)\n        case 'y':\n            formatter = dateGetter(DateType.FullYear, 1, 0, false, true);\n            break;\n        // 2 digit representation of the year, padded (00-99). (e.g. AD 2001 => 01, AD 2010 => 10)\n        case 'yy':\n            formatter = dateGetter(DateType.FullYear, 2, 0, true, true);\n            break;\n        // 3 digit representation of the year, padded (000-999). (e.g. AD 2001 => 01, AD 2010 => 10)\n        case 'yyy':\n            formatter = dateGetter(DateType.FullYear, 3, 0, false, true);\n            break;\n        // 4 digit representation of the year (e.g. AD 1 => 0001, AD 2010 => 2010)\n        case 'yyyy':\n            formatter = dateGetter(DateType.FullYear, 4, 0, false, true);\n            break;\n        // Month of the year (1-12), numeric\n        case 'M':\n        case 'L':\n            formatter = dateGetter(DateType.Month, 1, 1);\n            break;\n        case 'MM':\n        case 'LL':\n            formatter = dateGetter(DateType.Month, 2, 1);\n            break;\n        // Month of the year (January, ...), string, format\n        case 'MMM':\n            formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated);\n            break;\n        case 'MMMM':\n            formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Wide);\n            break;\n        case 'MMMMM':\n            formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Narrow);\n            break;\n        // Month of the year (January, ...), string, standalone\n        case 'LLL':\n            formatter =\n                dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated, FormStyle.Standalone);\n            break;\n        case 'LLLL':\n            formatter =\n                dateStrGetter(TranslationType.Months, TranslationWidth.Wide, FormStyle.Standalone);\n            break;\n        case 'LLLLL':\n            formatter =\n                dateStrGetter(TranslationType.Months, TranslationWidth.Narrow, FormStyle.Standalone);\n            break;\n        // Week of the year (1, ... 52)\n        case 'w':\n            formatter = weekGetter(1);\n            break;\n        case 'ww':\n            formatter = weekGetter(2);\n            break;\n        // Week of the month (1, ...)\n        case 'W':\n            formatter = weekGetter(1, true);\n            break;\n        // Day of the month (1-31)\n        case 'd':\n            formatter = dateGetter(DateType.Date, 1);\n            break;\n        case 'dd':\n            formatter = dateGetter(DateType.Date, 2);\n            break;\n        // Day of the Week\n        case 'E':\n        case 'EE':\n        case 'EEE':\n            formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Abbreviated);\n            break;\n        case 'EEEE':\n            formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide);\n            break;\n        case 'EEEEE':\n            formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Narrow);\n            break;\n        case 'EEEEEE':\n            formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short);\n            break;\n        // Generic period of the day (am-pm)\n        case 'a':\n        case 'aa':\n        case 'aaa':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated);\n            break;\n        case 'aaaa':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide);\n            break;\n        case 'aaaaa':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow);\n            break;\n        // Extended period of the day (midnight, at night, ...), standalone\n        case 'b':\n        case 'bb':\n        case 'bbb':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated, FormStyle.Standalone, true);\n            break;\n        case 'bbbb':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide, FormStyle.Standalone, true);\n            break;\n        case 'bbbbb':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow, FormStyle.Standalone, true);\n            break;\n        // Extended period of the day (midnight, night, ...), standalone\n        case 'B':\n        case 'BB':\n        case 'BBB':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated, FormStyle.Format, true);\n            break;\n        case 'BBBB':\n            formatter =\n                dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide, FormStyle.Format, true);\n            break;\n        case 'BBBBB':\n            formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow, FormStyle.Format, true);\n            break;\n        // Hour in AM/PM, (1-12)\n        case 'h':\n            formatter = dateGetter(DateType.Hours, 1, -12);\n            break;\n        case 'hh':\n            formatter = dateGetter(DateType.Hours, 2, -12);\n            break;\n        // Hour of the day (0-23)\n        case 'H':\n            formatter = dateGetter(DateType.Hours, 1);\n            break;\n        // Hour in day, padded (00-23)\n        case 'HH':\n            formatter = dateGetter(DateType.Hours, 2);\n            break;\n        // Minute of the hour (0-59)\n        case 'm':\n            formatter = dateGetter(DateType.Minutes, 1);\n            break;\n        case 'mm':\n            formatter = dateGetter(DateType.Minutes, 2);\n            break;\n        // Second of the minute (0-59)\n        case 's':\n            formatter = dateGetter(DateType.Seconds, 1);\n            break;\n        case 'ss':\n            formatter = dateGetter(DateType.Seconds, 2);\n            break;\n        // Fractional second padded (0-9)\n        case 'S':\n            formatter = dateGetter(DateType.Milliseconds, 1);\n            break;\n        case 'SS':\n            formatter = dateGetter(DateType.Milliseconds, 2);\n            break;\n        // = millisecond\n        case 'SSS':\n            formatter = dateGetter(DateType.Milliseconds, 3);\n            break;\n        // Timezone ISO8601 short format (-0430)\n        case 'Z':\n        case 'ZZ':\n        case 'ZZZ':\n            formatter = timeZoneGetter(ZoneWidth.Short);\n            break;\n        // Timezone ISO8601 extended format (-04:30)\n        case 'ZZZZZ':\n            formatter = timeZoneGetter(ZoneWidth.Extended);\n            break;\n        // Timezone GMT short format (GMT+4)\n        case 'O':\n        case 'OO':\n        case 'OOO':\n        // Should be location, but fallback to format O instead because we don't have the data yet\n        case 'z':\n        case 'zz':\n        case 'zzz':\n            formatter = timeZoneGetter(ZoneWidth.ShortGMT);\n            break;\n        // Timezone GMT long format (GMT+0430)\n        case 'OOOO':\n        case 'ZZZZ':\n        // Should be location, but fallback to format O instead because we don't have the data yet\n        case 'zzzz':\n            formatter = timeZoneGetter(ZoneWidth.Long);\n            break;\n        default:\n            return null;\n    }\n    DATE_FORMATS[format] = formatter;\n    return formatter;\n}\n/**\n * @param {?} timezone\n * @param {?} fallback\n * @return {?}\n */\nfunction timezoneToOffset(timezone, fallback) {\n    // Support: IE 9-11 only, Edge 13-15+\n    // IE/Edge do not \"understand\" colon (`:`) in timezone\n    timezone = timezone.replace(/:/g, '');\n    var /** @type {?} */ requestedTimezoneOffset = Date.parse('Jan 01, 1970 00:00:00 ' + timezone) / 60000;\n    return isNaN(requestedTimezoneOffset) ? fallback : requestedTimezoneOffset;\n}\n/**\n * @param {?} date\n * @param {?} minutes\n * @return {?}\n */\nfunction addDateMinutes(date, minutes) {\n    date = new Date(date.getTime());\n    date.setMinutes(date.getMinutes() + minutes);\n    return date;\n}\n/**\n * @param {?} date\n * @param {?} timezone\n * @param {?} reverse\n * @return {?}\n */\nfunction convertTimezoneToLocal(date, timezone, reverse) {\n    var /** @type {?} */ reverseValue = reverse ? -1 : 1;\n    var /** @type {?} */ dateTimezoneOffset = date.getTimezoneOffset();\n    var /** @type {?} */ timezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n    return addDateMinutes(date, reverseValue * (timezoneOffset - dateTimezoneOffset));\n}\n//# sourceMappingURL=format_date.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { ɵstringify as stringify } from '@angular/core';\n/**\n * @param {?} type\n * @param {?} value\n * @return {?}\n */\nexport function invalidPipeArgumentError(type, value) {\n    return Error(\"InvalidPipeArgument: '\" + value + \"' for pipe '\" + stringify(type) + \"'\");\n}\n//# sourceMappingURL=invalid_pipe_argument_error.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Inject, LOCALE_ID, Pipe } from '@angular/core';\nimport { formatDate } from '../i18n/format_date';\nimport { invalidPipeArgumentError } from './invalid_pipe_argument_error';\nexport var /** @type {?} */ ISO8601_DATE_REGEX = /^(\\d{4})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a date according to locale rules.\n * \\@howToUse `date_expression | date[:format[:timezone[:locale]]]`\n * \\@description\n *\n * Where:\n * - `expression` is a date object or a number (milliseconds since UTC epoch) or an ISO string\n * (https://www.w3.org/TR/NOTE-datetime).\n * - `format` indicates which date/time components to include. The format can be predefined as\n *   shown below (all examples are given for `en-US`) or custom as shown in the table.\n *   - `'short'`: equivalent to `'M/d/yy, h:mm a'` (e.g. `6/15/15, 9:03 AM`)\n *   - `'medium'`: equivalent to `'MMM d, y, h:mm:ss a'` (e.g. `Jun 15, 2015, 9:03:01 AM`)\n *   - `'long'`: equivalent to `'MMMM d, y, h:mm:ss a z'` (e.g. `June 15, 2015 at 9:03:01 AM GMT+1`)\n *   - `'full'`: equivalent to `'EEEE, MMMM d, y, h:mm:ss a zzzz'` (e.g. `Monday, June 15, 2015 at\n * 9:03:01 AM GMT+01:00`)\n *   - `'shortDate'`: equivalent to `'M/d/yy'` (e.g. `6/15/15`)\n *   - `'mediumDate'`: equivalent to `'MMM d, y'` (e.g. `Jun 15, 2015`)\n *   - `'longDate'`: equivalent to `'MMMM d, y'` (e.g. `June 15, 2015`)\n *   - `'fullDate'`: equivalent to `'EEEE, MMMM d, y'` (e.g. `Monday, June 15, 2015`)\n *   - `'shortTime'`: equivalent to `'h:mm a'` (e.g. `9:03 AM`)\n *   - `'mediumTime'`: equivalent to `'h:mm:ss a'` (e.g. `9:03:01 AM`)\n *   - `'longTime'`: equivalent to `'h:mm:ss a z'` (e.g. `9:03:01 AM GMT+1`)\n *   - `'fullTime'`: equivalent to `'h:mm:ss a zzzz'` (e.g. `9:03:01 AM GMT+01:00`)\n *  - `timezone` to be used for formatting. It understands UTC/GMT and the continental US time zone\n *  abbreviations, but for general use, use a time zone offset, for example,\n *  `'+0430'` (4 hours, 30 minutes east of the Greenwich meridian)\n *  If not specified, the local system timezone of the end-user's browser will be used.\n *  - `locale` is a `string` defining the locale to use (uses the current {\\@link LOCALE_ID} by\n * default)\n *\n *\n *  | Field Type         | Format      | Description                                                   | Example Value                                              |\n *  |--------------------|-------------|---------------------------------------------------------------|------------------------------------------------------------|\n *  | Era                | G, GG & GGG | Abbreviated                                                   | AD                                                         |\n *  |                    | GGGG        | Wide                                                          | Anno Domini                                                |\n *  |                    | GGGGG       | Narrow                                                        | A                                                          |\n *  | Year               | y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                    | yy          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                    | yyy         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                    | yyyy        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | Month              | M           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                    | MM          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                    | MMM         | Abbreviated                                                   | Sep                                                        |\n *  |                    | MMMM        | Wide                                                          | September                                                  |\n *  |                    | MMMMM       | Narrow                                                        | S                                                          |\n *  | Month standalone   | L           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                    | LL          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                    | LLL         | Abbreviated                                                   | Sep                                                        |\n *  |                    | LLLL        | Wide                                                          | September                                                  |\n *  |                    | LLLLL       | Narrow                                                        | S                                                          |\n *  | Week of year       | w           | Numeric: minimum digits                                       | 1... 53                                                    |\n *  |                    | ww          | Numeric: 2 digits + zero padded                               | 01... 53                                                   |\n *  | Week of month      | W           | Numeric: 1 digit                                              | 1... 5                                                     |\n *  | Day of month       | d           | Numeric: minimum digits                                       | 1                                                          |\n *  |                    | dd          | Numeric: 2 digits + zero padded                               | 01                                                          |\n *  | Week day           | E, EE & EEE | Abbreviated                                                   | Tue                                                        |\n *  |                    | EEEE        | Wide                                                          | Tuesday                                                    |\n *  |                    | EEEEE       | Narrow                                                        | T                                                          |\n *  |                    | EEEEEE      | Short                                                         | Tu                                                         |\n *  | Period             | a, aa & aaa | Abbreviated                                                   | am/pm or AM/PM                                             |\n *  |                    | aaaa        | Wide (fallback to `a` when missing)                           | ante meridiem/post meridiem                                |\n *  |                    | aaaaa       | Narrow                                                        | a/p                                                        |\n *  | Period*            | B, BB & BBB | Abbreviated                                                   | mid.                                                       |\n *  |                    | BBBB        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                    | BBBBB       | Narrow                                                        | md                                                         |\n *  | Period standalone* | b, bb & bbb | Abbreviated                                                   | mid.                                                       |\n *  |                    | bbbb        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                    | bbbbb       | Narrow                                                        | md                                                         |\n *  | Hour 1-12          | h           | Numeric: minimum digits                                       | 1, 12                                                      |\n *  |                    | hh          | Numeric: 2 digits + zero padded                               | 01, 12                                                     |\n *  | Hour 0-23          | H           | Numeric: minimum digits                                       | 0, 23                                                      |\n *  |                    | HH          | Numeric: 2 digits + zero padded                               | 00, 23                                                     |\n *  | Minute             | m           | Numeric: minimum digits                                       | 8, 59                                                      |\n *  |                    | mm          | Numeric: 2 digits + zero padded                               | 08, 59                                                     |\n *  | Second             | s           | Numeric: minimum digits                                       | 0... 59                                                    |\n *  |                    | ss          | Numeric: 2 digits + zero padded                               | 00... 59                                                   |\n *  | Fractional seconds | S           | Numeric: 1 digit                                              | 0... 9                                                     |\n *  |                    | SS          | Numeric: 2 digits + zero padded                               | 00... 99                                                   |\n *  |                    | SSS         | Numeric: 3 digits + zero padded (= milliseconds)              | 000... 999                                                 |\n *  | Zone               | z, zz & zzz | Short specific non location format (fallback to O)            | GMT-8                                                      |\n *  |                    | zzzz        | Long specific non location format (fallback to OOOO)          | GMT-08:00                                                  |\n *  |                    | Z, ZZ & ZZZ | ISO8601 basic format                                          | -0800                                                      |\n *  |                    | ZZZZ        | Long localized GMT format                                     | GMT-8:00                                                   |\n *  |                    | ZZZZZ       | ISO8601 extended format + Z indicator for offset 0 (= XXXXX)  | -08:00                                                     |\n *  |                    | O, OO & OOO | Short localized GMT format                                    | GMT-8                                                      |\n *  |                    | OOOO        | Long localized GMT format                                     | GMT-08:00                                                  |\n *\n *\n * When the expression is a ISO string without time (e.g. 2016-09-19) the time zone offset is not\n * applied and the formatted text will have the same day, month and year of the expression.\n *\n * WARNINGS:\n * - this pipe has only access to en-US locale data by default. If you want to localize the dates\n *   in another language, you will have to import data for other locales.\n *   See the {\\@linkDocs guide/i18n#i18n-pipes \"I18n guide\"} to know how to import additional locale\n *   data.\n * - Fields suffixed with * are only available in the extra dataset.\n *   See the {\\@linkDocs guide/i18n#i18n-pipes \"I18n guide\"} to know how to import extra locale\n *   data.\n * - this pipe is marked as pure hence it will not be re-evaluated when the input is mutated.\n *   Instead users should treat the date as an immutable object and change the reference when the\n *   pipe needs to re-run (this is to avoid reformatting the date on every change detection run\n *   which would be an expensive operation).\n *\n * ### Examples\n *\n * Assuming `dateObj` is (year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11)\n * in the _local_ time and locale is 'en-US':\n *\n * {\\@example common/pipes/ts/date_pipe.ts region='DatePipe'}\n *\n * \\@stable\n */\nvar DatePipe = /** @class */ (function () {\n    function DatePipe(locale) {\n        this.locale = locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} format\n     * @param {?=} timezone\n     * @param {?=} locale\n     * @return {?}\n     */\n    DatePipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} format\n     * @param {?=} timezone\n     * @param {?=} locale\n     * @return {?}\n     */\n    function (value, format, timezone, locale) {\n        if (format === void 0) { format = 'mediumDate'; }\n        if (value == null || value === '' || value !== value)\n            return null;\n        if (typeof value === 'string') {\n            value = value.trim();\n        }\n        var /** @type {?} */ date;\n        var /** @type {?} */ match;\n        if (isDate(value)) {\n            date = value;\n        }\n        else if (!isNaN(value - parseFloat(value))) {\n            date = new Date(parseFloat(value));\n        }\n        else if (typeof value === 'string' && /^(\\d{4}-\\d{1,2}-\\d{1,2})$/.test(value)) {\n            /**\n             * For ISO Strings without time the day, month and year must be extracted from the ISO String\n             * before Date creation to avoid time offset and errors in the new Date.\n             * If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n             * date, some browsers (e.g. IE 9) will throw an invalid Date error\n             * If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n             * is applied\n             * Note: ISO months are 0 for January, 1 for February, ...\n             */\n            var _a = value.split('-').map(function (val) { return +val; }), y = _a[0], m = _a[1], d = _a[2];\n            date = new Date(y, m - 1, d);\n        }\n        else if ((typeof value === 'string') && (match = value.match(ISO8601_DATE_REGEX))) {\n            date = isoStringToDate(match);\n        }\n        else {\n            date = new Date(value);\n        }\n        if (!isDate(date)) {\n            throw invalidPipeArgumentError(DatePipe, value);\n        }\n        return formatDate(date, format, locale || this.locale, timezone);\n    };\n    DatePipe.decorators = [\n        { type: Pipe, args: [{ name: 'date', pure: true },] },\n    ];\n    /** @nocollapse */\n    DatePipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return DatePipe;\n}());\nexport { DatePipe };\nfunction DatePipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    DatePipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    DatePipe.ctorParameters;\n    /** @type {?} */\n    DatePipe.prototype.locale;\n}\n/**\n * \\@internal\n * @param {?} match\n * @return {?}\n */\nexport function isoStringToDate(match) {\n    var /** @type {?} */ date = new Date(0);\n    var /** @type {?} */ tzHour = 0;\n    var /** @type {?} */ tzMin = 0;\n    // match[8] means that the string contains \"Z\" (UTC) or a timezone like \"+01:00\" or \"+0100\"\n    var /** @type {?} */ dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n    var /** @type {?} */ timeSetter = match[8] ? date.setUTCHours : date.setHours;\n    // if there is a timezone defined like \"+01:00\" or \"+0100\"\n    if (match[9]) {\n        tzHour = +(match[9] + match[10]);\n        tzMin = +(match[9] + match[11]);\n    }\n    dateSetter.call(date, +(match[1]), +(match[2]) - 1, +(match[3]));\n    var /** @type {?} */ h = +(match[4] || '0') - tzHour;\n    var /** @type {?} */ m = +(match[5] || '0') - tzMin;\n    var /** @type {?} */ s = +(match[6] || '0');\n    var /** @type {?} */ ms = Math.round(parseFloat('0.' + (match[7] || 0)) * 1000);\n    timeSetter.call(date, h, m, s, ms);\n    return date;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isDate(value) {\n    return value instanceof Date && !isNaN(value.valueOf());\n}\n//# sourceMappingURL=date_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport * as tslib_1 from \"tslib\";\nimport { NumberFormatStyle } from '../../i18n/locale_data_api';\nvar NumberFormatter = /** @class */ (function () {\n    function NumberFormatter() {\n    }\n    /**\n     * @param {?} num\n     * @param {?} locale\n     * @param {?} style\n     * @param {?=} opts\n     * @return {?}\n     */\n    NumberFormatter.format = /**\n     * @param {?} num\n     * @param {?} locale\n     * @param {?} style\n     * @param {?=} opts\n     * @return {?}\n     */\n    function (num, locale, style, opts) {\n        if (opts === void 0) { opts = {}; }\n        var minimumIntegerDigits = opts.minimumIntegerDigits, minimumFractionDigits = opts.minimumFractionDigits, maximumFractionDigits = opts.maximumFractionDigits, currency = opts.currency, _a = opts.currencyAsSymbol, currencyAsSymbol = _a === void 0 ? false : _a;\n        var /** @type {?} */ options = {\n            minimumIntegerDigits: minimumIntegerDigits,\n            minimumFractionDigits: minimumFractionDigits,\n            maximumFractionDigits: maximumFractionDigits,\n            style: NumberFormatStyle[style].toLowerCase()\n        };\n        if (style == NumberFormatStyle.Currency) {\n            options.currency = typeof currency == 'string' ? currency : undefined;\n            options.currencyDisplay = currencyAsSymbol ? 'symbol' : 'code';\n        }\n        return new Intl.NumberFormat(locale, options).format(num);\n    };\n    return NumberFormatter;\n}());\nexport { NumberFormatter };\nvar /** @type {?} */ DATE_FORMATS_SPLIT = /((?:[^yMLdHhmsazZEwGjJ']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|J+|j+|m+|s+|a|z|Z|G+|w+))(.*)/;\nvar /** @type {?} */ PATTERN_ALIASES = {\n    // Keys are quoted so they do not get renamed during closure compilation.\n    'yMMMdjms': datePartGetterFactory(combine([\n        digitCondition('year', 1),\n        nameCondition('month', 3),\n        digitCondition('day', 1),\n        digitCondition('hour', 1),\n        digitCondition('minute', 1),\n        digitCondition('second', 1),\n    ])),\n    'yMdjm': datePartGetterFactory(combine([\n        digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1),\n        digitCondition('hour', 1), digitCondition('minute', 1)\n    ])),\n    'yMMMMEEEEd': datePartGetterFactory(combine([\n        digitCondition('year', 1), nameCondition('month', 4), nameCondition('weekday', 4),\n        digitCondition('day', 1)\n    ])),\n    'yMMMMd': datePartGetterFactory(combine([digitCondition('year', 1), nameCondition('month', 4), digitCondition('day', 1)])),\n    'yMMMd': datePartGetterFactory(combine([digitCondition('year', 1), nameCondition('month', 3), digitCondition('day', 1)])),\n    'yMd': datePartGetterFactory(combine([digitCondition('year', 1), digitCondition('month', 1), digitCondition('day', 1)])),\n    'jms': datePartGetterFactory(combine([digitCondition('hour', 1), digitCondition('second', 1), digitCondition('minute', 1)])),\n    'jm': datePartGetterFactory(combine([digitCondition('hour', 1), digitCondition('minute', 1)]))\n};\nvar /** @type {?} */ DATE_FORMATS = {\n    // Keys are quoted so they do not get renamed.\n    'yyyy': datePartGetterFactory(digitCondition('year', 4)),\n    'yy': datePartGetterFactory(digitCondition('year', 2)),\n    'y': datePartGetterFactory(digitCondition('year', 1)),\n    'MMMM': datePartGetterFactory(nameCondition('month', 4)),\n    'MMM': datePartGetterFactory(nameCondition('month', 3)),\n    'MM': datePartGetterFactory(digitCondition('month', 2)),\n    'M': datePartGetterFactory(digitCondition('month', 1)),\n    'LLLL': datePartGetterFactory(nameCondition('month', 4)),\n    'L': datePartGetterFactory(nameCondition('month', 1)),\n    'dd': datePartGetterFactory(digitCondition('day', 2)),\n    'd': datePartGetterFactory(digitCondition('day', 1)),\n    'HH': digitModifier(hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), false)))),\n    'H': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), false))),\n    'hh': digitModifier(hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 2), true)))),\n    'h': hourExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n    'jj': datePartGetterFactory(digitCondition('hour', 2)),\n    'j': datePartGetterFactory(digitCondition('hour', 1)),\n    'mm': digitModifier(datePartGetterFactory(digitCondition('minute', 2))),\n    'm': datePartGetterFactory(digitCondition('minute', 1)),\n    'ss': digitModifier(datePartGetterFactory(digitCondition('second', 2))),\n    's': datePartGetterFactory(digitCondition('second', 1)),\n    // while ISO 8601 requires fractions to be prefixed with `.` or `,`\n    // we can be just safely rely on using `sss` since we currently don't support single or two digit\n    // fractions\n    'sss': datePartGetterFactory(digitCondition('second', 3)),\n    'EEEE': datePartGetterFactory(nameCondition('weekday', 4)),\n    'EEE': datePartGetterFactory(nameCondition('weekday', 3)),\n    'EE': datePartGetterFactory(nameCondition('weekday', 2)),\n    'E': datePartGetterFactory(nameCondition('weekday', 1)),\n    'a': hourClockExtractor(datePartGetterFactory(hour12Modify(digitCondition('hour', 1), true))),\n    'Z': timeZoneGetter('short'),\n    'z': timeZoneGetter('long'),\n    'ww': datePartGetterFactory({}),\n    // Week of year, padded (00-53). Week 01 is the week with the\n    // first Thursday of the year. not support ?\n    'w': datePartGetterFactory({}),\n    // Week of year (0-53). Week 1 is the week with the first Thursday\n    // of the year not support ?\n    'G': datePartGetterFactory(nameCondition('era', 1)),\n    'GG': datePartGetterFactory(nameCondition('era', 2)),\n    'GGG': datePartGetterFactory(nameCondition('era', 3)),\n    'GGGG': datePartGetterFactory(nameCondition('era', 4))\n};\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction digitModifier(inner) {\n    return function (date, locale) {\n        var /** @type {?} */ result = inner(date, locale);\n        return result.length == 1 ? '0' + result : result;\n    };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourClockExtractor(inner) {\n    return function (date, locale) { return inner(date, locale).split(' ')[1]; };\n}\n/**\n * @param {?} inner\n * @return {?}\n */\nfunction hourExtractor(inner) {\n    return function (date, locale) { return inner(date, locale).split(' ')[0]; };\n}\n/**\n * @param {?} date\n * @param {?} locale\n * @param {?} options\n * @return {?}\n */\nfunction intlDateFormat(date, locale, options) {\n    return new Intl.DateTimeFormat(locale, options).format(date).replace(/[\\u200e\\u200f]/g, '');\n}\n/**\n * @param {?} timezone\n * @return {?}\n */\nfunction timeZoneGetter(timezone) {\n    // To workaround `Intl` API restriction for single timezone let format with 24 hours\n    var /** @type {?} */ options = { hour: '2-digit', hour12: false, timeZoneName: timezone };\n    return function (date, locale) {\n        var /** @type {?} */ result = intlDateFormat(date, locale, options);\n        // Then extract first 3 letters that related to hours\n        return result ? result.substring(3) : '';\n    };\n}\n/**\n * @param {?} options\n * @param {?} value\n * @return {?}\n */\nfunction hour12Modify(options, value) {\n    options.hour12 = value;\n    return options;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction digitCondition(prop, len) {\n    var /** @type {?} */ result = {};\n    result[prop] = len === 2 ? '2-digit' : 'numeric';\n    return result;\n}\n/**\n * @param {?} prop\n * @param {?} len\n * @return {?}\n */\nfunction nameCondition(prop, len) {\n    var /** @type {?} */ result = {};\n    if (len < 4) {\n        result[prop] = len > 1 ? 'short' : 'narrow';\n    }\n    else {\n        result[prop] = 'long';\n    }\n    return result;\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction combine(options) {\n    return options.reduce(function (merged, opt) { return (tslib_1.__assign({}, merged, opt)); }, {});\n}\n/**\n * @param {?} ret\n * @return {?}\n */\nfunction datePartGetterFactory(ret) {\n    return function (date, locale) { return intlDateFormat(date, locale, ret); };\n}\nvar /** @type {?} */ DATE_FORMATTER_CACHE = new Map();\n/**\n * @param {?} format\n * @param {?} date\n * @param {?} locale\n * @return {?}\n */\nfunction dateFormatter(format, date, locale) {\n    var /** @type {?} */ fn = PATTERN_ALIASES[format];\n    if (fn)\n        return fn(date, locale);\n    var /** @type {?} */ cacheKey = format;\n    var /** @type {?} */ parts = DATE_FORMATTER_CACHE.get(cacheKey);\n    if (!parts) {\n        parts = [];\n        var /** @type {?} */ match = void 0;\n        DATE_FORMATS_SPLIT.exec(format);\n        var /** @type {?} */ _format = format;\n        while (_format) {\n            match = DATE_FORMATS_SPLIT.exec(_format);\n            if (match) {\n                parts = parts.concat(match.slice(1));\n                _format = /** @type {?} */ ((parts.pop()));\n            }\n            else {\n                parts.push(_format);\n                _format = null;\n            }\n        }\n        DATE_FORMATTER_CACHE.set(cacheKey, parts);\n    }\n    return parts.reduce(function (text, part) {\n        var /** @type {?} */ fn = DATE_FORMATS[part];\n        return text + (fn ? fn(date, locale) : partToTime(part));\n    }, '');\n}\n/**\n * @param {?} part\n * @return {?}\n */\nfunction partToTime(part) {\n    return part === '\\'\\'' ? '\\'' : part.replace(/(^'|'$)/g, '').replace(/''/g, '\\'');\n}\nvar DateFormatter = /** @class */ (function () {\n    function DateFormatter() {\n    }\n    /**\n     * @param {?} date\n     * @param {?} locale\n     * @param {?} pattern\n     * @return {?}\n     */\n    DateFormatter.format = /**\n     * @param {?} date\n     * @param {?} locale\n     * @param {?} pattern\n     * @return {?}\n     */\n    function (date, locale, pattern) {\n        return dateFormatter(pattern, date, locale);\n    };\n    return DateFormatter;\n}());\nexport { DateFormatter };\n//# sourceMappingURL=intl.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n* @license\n* Copyright Google Inc. All Rights Reserved.\n*\n* Use of this source code is governed by an MIT-style license that can be\n* found in the LICENSE file at https://angular.io/license\n  */\nimport { Inject, LOCALE_ID, Pipe } from '@angular/core';\nimport { ISO8601_DATE_REGEX, isoStringToDate } from '../date_pipe';\nimport { invalidPipeArgumentError } from '../invalid_pipe_argument_error';\nimport { DateFormatter } from './intl';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a date according to locale rules.\n * \\@howToUse `date_expression | date[:format]`\n * \\@description\n *\n * Where:\n * - `expression` is a date object or a number (milliseconds since UTC epoch) or an ISO string\n * (https://www.w3.org/TR/NOTE-datetime).\n * - `format` indicates which date/time components to include. The format can be predefined as\n *   shown below or custom as shown in the table.\n *   - `'medium'`: equivalent to `'yMMMdjms'` (e.g. `Sep 3, 2010, 12:05:08 PM` for `en-US`)\n *   - `'short'`: equivalent to `'yMdjm'` (e.g. `9/3/2010, 12:05 PM` for `en-US`)\n *   - `'fullDate'`: equivalent to `'yMMMMEEEEd'` (e.g. `Friday, September 3, 2010` for `en-US`)\n *   - `'longDate'`: equivalent to `'yMMMMd'` (e.g. `September 3, 2010` for `en-US`)\n *   - `'mediumDate'`: equivalent to `'yMMMd'` (e.g. `Sep 3, 2010` for `en-US`)\n *   - `'shortDate'`: equivalent to `'yMd'` (e.g. `9/3/2010` for `en-US`)\n *   - `'mediumTime'`: equivalent to `'jms'` (e.g. `12:05:08 PM` for `en-US`)\n *   - `'shortTime'`: equivalent to `'jm'` (e.g. `12:05 PM` for `en-US`)\n *\n *\n *  | Component | Symbol | Narrow | Short Form   | Long Form         | Numeric   | 2-digit   |\n *  |-----------|:------:|--------|--------------|-------------------|-----------|-----------|\n *  | era       |   G    | G (A)  | GGG (AD)     | GGGG (Anno Domini)| -         | -         |\n *  | year      |   y    | -      | -            | -                 | y (2015)  | yy (15)   |\n *  | month     |   M    | L (S)  | MMM (Sep)    | MMMM (September)  | M (9)     | MM (09)   |\n *  | day       |   d    | -      | -            | -                 | d (3)     | dd (03)   |\n *  | weekday   |   E    | E (S)  | EEE (Sun)    | EEEE (Sunday)     | -         | -         |\n *  | hour      |   j    | -      | -            | -                 | j (13)    | jj (13)   |\n *  | hour12    |   h    | -      | -            | -                 | h (1 PM)  | hh (01 PM)|\n *  | hour24    |   H    | -      | -            | -                 | H (13)    | HH (13)   |\n *  | minute    |   m    | -      | -            | -                 | m (5)     | mm (05)   |\n *  | second    |   s    | -      | -            | -                 | s (9)     | ss (09)   |\n *  | timezone  |   z    | -      | -            | z (Pacific Standard Time)| -  | -         |\n *  | timezone  |   Z    | -      | Z (GMT-8:00) | -                 | -         | -         |\n *  | timezone  |   a    | -      | a (PM)       | -                 | -         | -         |\n *\n * In javascript, only the components specified will be respected (not the ordering,\n * punctuations, ...) and details of the formatting will be dependent on the locale.\n *\n * Timezone of the formatted text will be the local system timezone of the end-user's machine.\n *\n * When the expression is a ISO string without time (e.g. 2016-09-19) the time zone offset is not\n * applied and the formatted text will have the same day, month and year of the expression.\n *\n * WARNINGS:\n * - this pipe is marked as pure hence it will not be re-evaluated when the input is mutated.\n *   Instead users should treat the date as an immutable object and change the reference when the\n *   pipe needs to re-run (this is to avoid reformatting the date on every change detection run\n *   which would be an expensive operation).\n * - this pipe uses the Internationalization API. Therefore it is only reliable in Chrome and Opera\n *   browsers.\n *\n * ### Examples\n *\n * Assuming `dateObj` is (year: 2010, month: 9, day: 3, hour: 12 PM, minute: 05, second: 08)\n * in the _local_ time and locale is 'en-US':\n *\n * {\\@example common/pipes/ts/date_pipe.ts region='DeprecatedDatePipe'}\n *\n * \\@stable\n */\nvar DeprecatedDatePipe = /** @class */ (function () {\n    function DeprecatedDatePipe(_locale) {\n        this._locale = _locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} pattern\n     * @return {?}\n     */\n    DeprecatedDatePipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} pattern\n     * @return {?}\n     */\n    function (value, pattern) {\n        if (pattern === void 0) { pattern = 'mediumDate'; }\n        if (value == null || value === '' || value !== value)\n            return null;\n        var /** @type {?} */ date;\n        if (typeof value === 'string') {\n            value = value.trim();\n        }\n        if (isDate(value)) {\n            date = value;\n        }\n        else if (!isNaN(value - parseFloat(value))) {\n            date = new Date(parseFloat(value));\n        }\n        else if (typeof value === 'string' && /^(\\d{4}-\\d{1,2}-\\d{1,2})$/.test(value)) {\n            /**\n             * For ISO Strings without time the day, month and year must be extracted from the ISO String\n             * before Date creation to avoid time offset and errors in the new Date.\n             * If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n             * date, some browsers (e.g. IE 9) will throw an invalid Date error\n             * If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the\n             * timeoffset\n             * is applied\n             * Note: ISO months are 0 for January, 1 for February, ...\n             */\n            var _a = value.split('-').map(function (val) { return parseInt(val, 10); }), y = _a[0], m = _a[1], d = _a[2];\n            date = new Date(y, m - 1, d);\n        }\n        else {\n            date = new Date(value);\n        }\n        if (!isDate(date)) {\n            var /** @type {?} */ match = void 0;\n            if ((typeof value === 'string') && (match = value.match(ISO8601_DATE_REGEX))) {\n                date = isoStringToDate(match);\n            }\n            else {\n                throw invalidPipeArgumentError(DeprecatedDatePipe, value);\n            }\n        }\n        return DateFormatter.format(date, this._locale, DeprecatedDatePipe._ALIASES[pattern] || pattern);\n    };\n    /**\n     * \\@internal\n     */\n    DeprecatedDatePipe._ALIASES = {\n        'medium': 'yMMMdjms',\n        'short': 'yMdjm',\n        'fullDate': 'yMMMMEEEEd',\n        'longDate': 'yMMMMd',\n        'mediumDate': 'yMMMd',\n        'shortDate': 'yMd',\n        'mediumTime': 'jms',\n        'shortTime': 'jm'\n    };\n    DeprecatedDatePipe.decorators = [\n        { type: Pipe, args: [{ name: 'date', pure: true },] },\n    ];\n    /** @nocollapse */\n    DeprecatedDatePipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return DeprecatedDatePipe;\n}());\nexport { DeprecatedDatePipe };\nfunction DeprecatedDatePipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    DeprecatedDatePipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    DeprecatedDatePipe.ctorParameters;\n    /**\n     * \\@internal\n     * @type {?}\n     */\n    DeprecatedDatePipe._ALIASES;\n    /** @type {?} */\n    DeprecatedDatePipe.prototype._locale;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isDate(value) {\n    return value instanceof Date && !isNaN(value.valueOf());\n}\n//# sourceMappingURL=date_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { NumberFormatStyle, NumberSymbol, getLocaleNumberFormat, getLocaleNumberSymbol } from './locale_data_api';\nexport var /** @type {?} */ NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\nvar /** @type {?} */ MAX_DIGITS = 22;\nvar /** @type {?} */ DECIMAL_SEP = '.';\nvar /** @type {?} */ ZERO_CHAR = '0';\nvar /** @type {?} */ PATTERN_SEP = ';';\nvar /** @type {?} */ GROUP_SEP = ',';\nvar /** @type {?} */ DIGIT_CHAR = '#';\nvar /** @type {?} */ CURRENCY_CHAR = '¤';\nvar /** @type {?} */ PERCENT_CHAR = '%';\n/**\n * Transform a number to a locale string based on a style and a format\n *\n * \\@internal\n * @param {?} value\n * @param {?} locale\n * @param {?} style\n * @param {?=} digitsInfo\n * @param {?=} currency\n * @return {?}\n */\nexport function formatNumber(value, locale, style, digitsInfo, currency) {\n    if (currency === void 0) { currency = null; }\n    var /** @type {?} */ res = { str: null };\n    var /** @type {?} */ format = getLocaleNumberFormat(locale, style);\n    var /** @type {?} */ num;\n    // Convert strings to numbers\n    if (typeof value === 'string' && !isNaN(+value - parseFloat(value))) {\n        num = +value;\n    }\n    else if (typeof value !== 'number') {\n        res.error = value + \" is not a number\";\n        return res;\n    }\n    else {\n        num = value;\n    }\n    var /** @type {?} */ pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n    var /** @type {?} */ formattedText = '';\n    var /** @type {?} */ isZero = false;\n    if (!isFinite(num)) {\n        formattedText = getLocaleNumberSymbol(locale, NumberSymbol.Infinity);\n    }\n    else {\n        var /** @type {?} */ parsedNumber = parseNumber(num);\n        if (style === NumberFormatStyle.Percent) {\n            parsedNumber = toPercent(parsedNumber);\n        }\n        var /** @type {?} */ minInt = pattern.minInt;\n        var /** @type {?} */ minFraction = pattern.minFrac;\n        var /** @type {?} */ maxFraction = pattern.maxFrac;\n        if (digitsInfo) {\n            var /** @type {?} */ parts = digitsInfo.match(NUMBER_FORMAT_REGEXP);\n            if (parts === null) {\n                res.error = digitsInfo + \" is not a valid digit info\";\n                return res;\n            }\n            var /** @type {?} */ minIntPart = parts[1];\n            var /** @type {?} */ minFractionPart = parts[3];\n            var /** @type {?} */ maxFractionPart = parts[5];\n            if (minIntPart != null) {\n                minInt = parseIntAutoRadix(minIntPart);\n            }\n            if (minFractionPart != null) {\n                minFraction = parseIntAutoRadix(minFractionPart);\n            }\n            if (maxFractionPart != null) {\n                maxFraction = parseIntAutoRadix(maxFractionPart);\n            }\n            else if (minFractionPart != null && minFraction > maxFraction) {\n                maxFraction = minFraction;\n            }\n        }\n        roundNumber(parsedNumber, minFraction, maxFraction);\n        var /** @type {?} */ digits = parsedNumber.digits;\n        var /** @type {?} */ integerLen = parsedNumber.integerLen;\n        var /** @type {?} */ exponent = parsedNumber.exponent;\n        var /** @type {?} */ decimals = [];\n        isZero = digits.every(function (d) { return !d; });\n        // pad zeros for small numbers\n        for (; integerLen < minInt; integerLen++) {\n            digits.unshift(0);\n        }\n        // pad zeros for small numbers\n        for (; integerLen < 0; integerLen++) {\n            digits.unshift(0);\n        }\n        // extract decimals digits\n        if (integerLen > 0) {\n            decimals = digits.splice(integerLen, digits.length);\n        }\n        else {\n            decimals = digits;\n            digits = [0];\n        }\n        // format the integer digits with grouping separators\n        var /** @type {?} */ groups = [];\n        if (digits.length >= pattern.lgSize) {\n            groups.unshift(digits.splice(-pattern.lgSize, digits.length).join(''));\n        }\n        while (digits.length > pattern.gSize) {\n            groups.unshift(digits.splice(-pattern.gSize, digits.length).join(''));\n        }\n        if (digits.length) {\n            groups.unshift(digits.join(''));\n        }\n        var /** @type {?} */ groupSymbol = currency ? NumberSymbol.CurrencyGroup : NumberSymbol.Group;\n        formattedText = groups.join(getLocaleNumberSymbol(locale, groupSymbol));\n        // append the decimal digits\n        if (decimals.length) {\n            var /** @type {?} */ decimalSymbol = currency ? NumberSymbol.CurrencyDecimal : NumberSymbol.Decimal;\n            formattedText += getLocaleNumberSymbol(locale, decimalSymbol) + decimals.join('');\n        }\n        if (exponent) {\n            formattedText += getLocaleNumberSymbol(locale, NumberSymbol.Exponential) + '+' + exponent;\n        }\n    }\n    if (num < 0 && !isZero) {\n        formattedText = pattern.negPre + formattedText + pattern.negSuf;\n    }\n    else {\n        formattedText = pattern.posPre + formattedText + pattern.posSuf;\n    }\n    if (style === NumberFormatStyle.Currency && currency !== null) {\n        res.str = formattedText\n            .replace(CURRENCY_CHAR, currency)\n            .replace(CURRENCY_CHAR, '');\n        return res;\n    }\n    if (style === NumberFormatStyle.Percent) {\n        res.str = formattedText.replace(new RegExp(PERCENT_CHAR, 'g'), getLocaleNumberSymbol(locale, NumberSymbol.PercentSign));\n        return res;\n    }\n    res.str = formattedText;\n    return res;\n}\n/**\n * @record\n */\nfunction ParsedNumberFormat() { }\nfunction ParsedNumberFormat_tsickle_Closure_declarations() {\n    /** @type {?} */\n    ParsedNumberFormat.prototype.minInt;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.minFrac;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.maxFrac;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.posPre;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.posSuf;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.negPre;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.negSuf;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.gSize;\n    /** @type {?} */\n    ParsedNumberFormat.prototype.lgSize;\n}\n/**\n * @param {?} format\n * @param {?=} minusSign\n * @return {?}\n */\nfunction parseNumberFormat(format, minusSign) {\n    if (minusSign === void 0) { minusSign = '-'; }\n    var /** @type {?} */ p = {\n        minInt: 1,\n        minFrac: 0,\n        maxFrac: 0,\n        posPre: '',\n        posSuf: '',\n        negPre: '',\n        negSuf: '',\n        gSize: 0,\n        lgSize: 0\n    };\n    var /** @type {?} */ patternParts = format.split(PATTERN_SEP);\n    var /** @type {?} */ positive = patternParts[0];\n    var /** @type {?} */ negative = patternParts[1];\n    var /** @type {?} */ positiveParts = positive.indexOf(DECIMAL_SEP) !== -1 ?\n        positive.split(DECIMAL_SEP) :\n        [\n            positive.substring(0, positive.lastIndexOf(ZERO_CHAR) + 1),\n            positive.substring(positive.lastIndexOf(ZERO_CHAR) + 1)\n        ], /** @type {?} */\n    integer = positiveParts[0], /** @type {?} */ fraction = positiveParts[1] || '';\n    p.posPre = integer.substr(0, integer.indexOf(DIGIT_CHAR));\n    for (var /** @type {?} */ i = 0; i < fraction.length; i++) {\n        var /** @type {?} */ ch = fraction.charAt(i);\n        if (ch === ZERO_CHAR) {\n            p.minFrac = p.maxFrac = i + 1;\n        }\n        else if (ch === DIGIT_CHAR) {\n            p.maxFrac = i + 1;\n        }\n        else {\n            p.posSuf += ch;\n        }\n    }\n    var /** @type {?} */ groups = integer.split(GROUP_SEP);\n    p.gSize = groups[1] ? groups[1].length : 0;\n    p.lgSize = (groups[2] || groups[1]) ? (groups[2] || groups[1]).length : 0;\n    if (negative) {\n        var /** @type {?} */ trunkLen = positive.length - p.posPre.length - p.posSuf.length, /** @type {?} */\n        pos = negative.indexOf(DIGIT_CHAR);\n        p.negPre = negative.substr(0, pos).replace(/'/g, '');\n        p.negSuf = negative.substr(pos + trunkLen).replace(/'/g, '');\n    }\n    else {\n        p.negPre = minusSign + p.posPre;\n        p.negSuf = p.posSuf;\n    }\n    return p;\n}\n/**\n * @record\n */\nfunction ParsedNumber() { }\nfunction ParsedNumber_tsickle_Closure_declarations() {\n    /** @type {?} */\n    ParsedNumber.prototype.digits;\n    /** @type {?} */\n    ParsedNumber.prototype.exponent;\n    /** @type {?} */\n    ParsedNumber.prototype.integerLen;\n}\n/**\n * @param {?} parsedNumber\n * @return {?}\n */\nfunction toPercent(parsedNumber) {\n    // if the number is 0, don't do anything\n    if (parsedNumber.digits[0] === 0) {\n        return parsedNumber;\n    }\n    // Getting the current number of decimals\n    var /** @type {?} */ fractionLen = parsedNumber.digits.length - parsedNumber.integerLen;\n    if (parsedNumber.exponent) {\n        parsedNumber.exponent += 2;\n    }\n    else {\n        if (fractionLen === 0) {\n            parsedNumber.digits.push(0, 0);\n        }\n        else if (fractionLen === 1) {\n            parsedNumber.digits.push(0);\n        }\n        parsedNumber.integerLen += 2;\n    }\n    return parsedNumber;\n}\n/**\n * Parses a number.\n * Significant bits of this parse algorithm came from https://github.com/MikeMcl/big.js/\n * @param {?} num\n * @return {?}\n */\nfunction parseNumber(num) {\n    var /** @type {?} */ numStr = Math.abs(num) + '';\n    var /** @type {?} */ exponent = 0, /** @type {?} */ digits, /** @type {?} */ integerLen;\n    var /** @type {?} */ i, /** @type {?} */ j, /** @type {?} */ zeros;\n    // Decimal point?\n    if ((integerLen = numStr.indexOf(DECIMAL_SEP)) > -1) {\n        numStr = numStr.replace(DECIMAL_SEP, '');\n    }\n    // Exponential form?\n    if ((i = numStr.search(/e/i)) > 0) {\n        // Work out the exponent.\n        if (integerLen < 0)\n            integerLen = i;\n        integerLen += +numStr.slice(i + 1);\n        numStr = numStr.substring(0, i);\n    }\n    else if (integerLen < 0) {\n        // There was no decimal point or exponent so it is an integer.\n        integerLen = numStr.length;\n    }\n    // Count the number of leading zeros.\n    for (i = 0; numStr.charAt(i) === ZERO_CHAR; i++) {\n        /* empty */\n    }\n    if (i === (zeros = numStr.length)) {\n        // The digits are all zero.\n        digits = [0];\n        integerLen = 1;\n    }\n    else {\n        // Count the number of trailing zeros\n        zeros--;\n        while (numStr.charAt(zeros) === ZERO_CHAR)\n            zeros--;\n        // Trailing zeros are insignificant so ignore them\n        integerLen -= i;\n        digits = [];\n        // Convert string to array of digits without leading/trailing zeros.\n        for (j = 0; i <= zeros; i++, j++) {\n            digits[j] = +numStr.charAt(i);\n        }\n    }\n    // If the number overflows the maximum allowed digits then use an exponent.\n    if (integerLen > MAX_DIGITS) {\n        digits = digits.splice(0, MAX_DIGITS - 1);\n        exponent = integerLen - 1;\n        integerLen = 1;\n    }\n    return { digits: digits, exponent: exponent, integerLen: integerLen };\n}\n/**\n * Round the parsed number to the specified number of decimal places\n * This function changes the parsedNumber in-place\n * @param {?} parsedNumber\n * @param {?} minFrac\n * @param {?} maxFrac\n * @return {?}\n */\nfunction roundNumber(parsedNumber, minFrac, maxFrac) {\n    if (minFrac > maxFrac) {\n        throw new Error(\"The minimum number of digits after fraction (\" + minFrac + \") is higher than the maximum (\" + maxFrac + \").\");\n    }\n    var /** @type {?} */ digits = parsedNumber.digits;\n    var /** @type {?} */ fractionLen = digits.length - parsedNumber.integerLen;\n    var /** @type {?} */ fractionSize = Math.min(Math.max(minFrac, fractionLen), maxFrac);\n    // The index of the digit to where rounding is to occur\n    var /** @type {?} */ roundAt = fractionSize + parsedNumber.integerLen;\n    var /** @type {?} */ digit = digits[roundAt];\n    if (roundAt > 0) {\n        // Drop fractional digits beyond `roundAt`\n        digits.splice(Math.max(parsedNumber.integerLen, roundAt));\n        // Set non-fractional digits beyond `roundAt` to 0\n        for (var /** @type {?} */ j = roundAt; j < digits.length; j++) {\n            digits[j] = 0;\n        }\n    }\n    else {\n        // We rounded to zero so reset the parsedNumber\n        fractionLen = Math.max(0, fractionLen);\n        parsedNumber.integerLen = 1;\n        digits.length = Math.max(1, roundAt = fractionSize + 1);\n        digits[0] = 0;\n        for (var /** @type {?} */ i = 1; i < roundAt; i++)\n            digits[i] = 0;\n    }\n    if (digit >= 5) {\n        if (roundAt - 1 < 0) {\n            for (var /** @type {?} */ k = 0; k > roundAt; k--) {\n                digits.unshift(0);\n                parsedNumber.integerLen++;\n            }\n            digits.unshift(1);\n            parsedNumber.integerLen++;\n        }\n        else {\n            digits[roundAt - 1]++;\n        }\n    }\n    // Pad out with zeros to get the required fraction length\n    for (; fractionLen < Math.max(0, fractionSize); fractionLen++)\n        digits.push(0);\n    var /** @type {?} */ dropTrailingZeros = fractionSize !== 0;\n    // Minimal length = nb of decimals required + current nb of integers\n    // Any number besides that is optional and can be removed if it's a trailing 0\n    var /** @type {?} */ minLen = minFrac + parsedNumber.integerLen;\n    // Do any carrying, e.g. a digit was rounded up to 10\n    var /** @type {?} */ carry = digits.reduceRight(function (carry, d, i, digits) {\n        d = d + carry;\n        digits[i] = d < 10 ? d : d - 10; // d % 10\n        if (dropTrailingZeros) {\n            // Do not keep meaningless fractional trailing zeros (e.g. 15.52000 --> 15.52)\n            if (digits[i] === 0 && i >= minLen) {\n                digits.pop();\n            }\n            else {\n                dropTrailingZeros = false;\n            }\n        }\n        return d >= 10 ? 1 : 0; // Math.floor(d / 10);\n    }, 0);\n    if (carry) {\n        digits.unshift(carry);\n        parsedNumber.integerLen++;\n    }\n}\n/**\n * \\@internal\n * @param {?} text\n * @return {?}\n */\nexport function parseIntAutoRadix(text) {\n    var /** @type {?} */ result = parseInt(text);\n    if (isNaN(result)) {\n        throw new Error('Invalid integer literal when parsing ' + text);\n    }\n    return result;\n}\n//# sourceMappingURL=format_number.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Inject, LOCALE_ID, Pipe } from '@angular/core';\nimport { NUMBER_FORMAT_REGEXP, parseIntAutoRadix } from '../../i18n/format_number';\nimport { NumberFormatStyle } from '../../i18n/locale_data_api';\nimport { invalidPipeArgumentError } from '../invalid_pipe_argument_error';\nimport { NumberFormatter } from './intl';\n/**\n * @param {?} pipe\n * @param {?} locale\n * @param {?} value\n * @param {?} style\n * @param {?=} digits\n * @param {?=} currency\n * @param {?=} currencyAsSymbol\n * @return {?}\n */\nfunction formatNumber(pipe, locale, value, style, digits, currency, currencyAsSymbol) {\n    if (currency === void 0) { currency = null; }\n    if (currencyAsSymbol === void 0) { currencyAsSymbol = false; }\n    if (value == null)\n        return null;\n    // Convert strings to numbers\n    value = typeof value === 'string' && !isNaN(+value - parseFloat(value)) ? +value : value;\n    if (typeof value !== 'number') {\n        throw invalidPipeArgumentError(pipe, value);\n    }\n    var /** @type {?} */ minInt;\n    var /** @type {?} */ minFraction;\n    var /** @type {?} */ maxFraction;\n    if (style !== NumberFormatStyle.Currency) {\n        // rely on Intl default for currency\n        minInt = 1;\n        minFraction = 0;\n        maxFraction = 3;\n    }\n    if (digits) {\n        var /** @type {?} */ parts = digits.match(NUMBER_FORMAT_REGEXP);\n        if (parts === null) {\n            throw new Error(digits + \" is not a valid digit info for number pipes\");\n        }\n        if (parts[1] != null) {\n            // min integer digits\n            minInt = parseIntAutoRadix(parts[1]);\n        }\n        if (parts[3] != null) {\n            // min fraction digits\n            minFraction = parseIntAutoRadix(parts[3]);\n        }\n        if (parts[5] != null) {\n            // max fraction digits\n            maxFraction = parseIntAutoRadix(parts[5]);\n        }\n    }\n    return NumberFormatter.format(/** @type {?} */ (value), locale, style, {\n        minimumIntegerDigits: minInt,\n        minimumFractionDigits: minFraction,\n        maximumFractionDigits: maxFraction,\n        currency: currency,\n        currencyAsSymbol: currencyAsSymbol,\n    });\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number according to locale rules.\n * \\@howToUse `number_expression | number[:digitInfo]`\n *\n * Formats a number as text. Group sizing and separator and other locale-specific\n * configurations are based on the active locale.\n *\n * where `expression` is a number:\n *  - `digitInfo` is a `string` which has a following format: <br>\n *     <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>\n *   - `minIntegerDigits` is the minimum number of integer digits to use. Defaults to `1`.\n *   - `minFractionDigits` is the minimum number of digits after fraction. Defaults to `0`.\n *   - `maxFractionDigits` is the maximum number of digits after fraction. Defaults to `3`.\n *\n * For more information on the acceptable range for each of these numbers and other\n * details see your native internationalization library.\n *\n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n *\n * ### Example\n *\n * {\\@example common/pipes/ts/number_pipe.ts region='DeprecatedNumberPipe'}\n *\n * \\@stable\n */\nvar DeprecatedDecimalPipe = /** @class */ (function () {\n    function DeprecatedDecimalPipe(_locale) {\n        this._locale = _locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} digits\n     * @return {?}\n     */\n    DeprecatedDecimalPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} digits\n     * @return {?}\n     */\n    function (value, digits) {\n        return formatNumber(DeprecatedDecimalPipe, this._locale, value, NumberFormatStyle.Decimal, digits);\n    };\n    DeprecatedDecimalPipe.decorators = [\n        { type: Pipe, args: [{ name: 'number' },] },\n    ];\n    /** @nocollapse */\n    DeprecatedDecimalPipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return DeprecatedDecimalPipe;\n}());\nexport { DeprecatedDecimalPipe };\nfunction DeprecatedDecimalPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    DeprecatedDecimalPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    DeprecatedDecimalPipe.ctorParameters;\n    /** @type {?} */\n    DeprecatedDecimalPipe.prototype._locale;\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as a percentage according to locale rules.\n * \\@howToUse `number_expression | percent[:digitInfo]`\n *\n * \\@description\n *\n * Formats a number as percentage.\n *\n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n *\n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n *\n * ### Example\n *\n * {\\@example common/pipes/ts/percent_pipe.ts region='DeprecatedPercentPipe'}\n *\n * \\@stable\n */\nvar DeprecatedPercentPipe = /** @class */ (function () {\n    function DeprecatedPercentPipe(_locale) {\n        this._locale = _locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} digits\n     * @return {?}\n     */\n    DeprecatedPercentPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} digits\n     * @return {?}\n     */\n    function (value, digits) {\n        return formatNumber(DeprecatedPercentPipe, this._locale, value, NumberFormatStyle.Percent, digits);\n    };\n    DeprecatedPercentPipe.decorators = [\n        { type: Pipe, args: [{ name: 'percent' },] },\n    ];\n    /** @nocollapse */\n    DeprecatedPercentPipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return DeprecatedPercentPipe;\n}());\nexport { DeprecatedPercentPipe };\nfunction DeprecatedPercentPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    DeprecatedPercentPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    DeprecatedPercentPipe.ctorParameters;\n    /** @type {?} */\n    DeprecatedPercentPipe.prototype._locale;\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as currency using locale rules.\n * \\@howToUse `number_expression | currency[:currencyCode[:symbolDisplay[:digitInfo]]]`\n * \\@description\n *\n * Use `currency` to format a number as currency.\n *\n * - `currencyCode` is the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code, such\n *    as `USD` for the US dollar and `EUR` for the euro.\n * - `symbolDisplay` is a boolean indicating whether to use the currency symbol or code.\n *   - `true`: use symbol (e.g. `$`).\n *   - `false`(default): use code (e.g. `USD`).\n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n *\n * WARNING: this pipe uses the Internationalization API which is not yet available in all browsers\n * and may require a polyfill. See [Browser Support](guide/browser-support) for details.\n *\n * ### Example\n *\n * {\\@example common/pipes/ts/currency_pipe.ts region='DeprecatedCurrencyPipe'}\n *\n * \\@stable\n */\nvar DeprecatedCurrencyPipe = /** @class */ (function () {\n    function DeprecatedCurrencyPipe(_locale) {\n        this._locale = _locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} currencyCode\n     * @param {?=} symbolDisplay\n     * @param {?=} digits\n     * @return {?}\n     */\n    DeprecatedCurrencyPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} currencyCode\n     * @param {?=} symbolDisplay\n     * @param {?=} digits\n     * @return {?}\n     */\n    function (value, currencyCode, symbolDisplay, digits) {\n        if (currencyCode === void 0) { currencyCode = 'USD'; }\n        if (symbolDisplay === void 0) { symbolDisplay = false; }\n        return formatNumber(DeprecatedCurrencyPipe, this._locale, value, NumberFormatStyle.Currency, digits, currencyCode, symbolDisplay);\n    };\n    DeprecatedCurrencyPipe.decorators = [\n        { type: Pipe, args: [{ name: 'currency' },] },\n    ];\n    /** @nocollapse */\n    DeprecatedCurrencyPipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return DeprecatedCurrencyPipe;\n}());\nexport { DeprecatedCurrencyPipe };\nfunction DeprecatedCurrencyPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    DeprecatedCurrencyPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    DeprecatedCurrencyPipe.ctorParameters;\n    /** @type {?} */\n    DeprecatedCurrencyPipe.prototype._locale;\n}\n//# sourceMappingURL=number_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { DeprecatedDatePipe } from './date_pipe';\nimport { DeprecatedCurrencyPipe, DeprecatedDecimalPipe, DeprecatedPercentPipe } from './number_pipe';\nexport { DeprecatedCurrencyPipe, DeprecatedDatePipe, DeprecatedDecimalPipe, DeprecatedPercentPipe, };\n/**\n * A collection of deprecated i18n pipes that require intl api\n *\n * @deprecated from v5\n */\nexport var /** @type {?} */ COMMON_DEPRECATED_I18N_PIPES = [DeprecatedDecimalPipe, DeprecatedPercentPipe, DeprecatedCurrencyPipe, DeprecatedDatePipe];\n//# sourceMappingURL=index.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { ChangeDetectorRef, Pipe, WrappedValue, ɵisObservable, ɵisPromise } from '@angular/core';\nimport { invalidPipeArgumentError } from './invalid_pipe_argument_error';\n/**\n * @record\n */\nfunction SubscriptionStrategy() { }\nfunction SubscriptionStrategy_tsickle_Closure_declarations() {\n    /** @type {?} */\n    SubscriptionStrategy.prototype.createSubscription;\n    /** @type {?} */\n    SubscriptionStrategy.prototype.dispose;\n    /** @type {?} */\n    SubscriptionStrategy.prototype.onDestroy;\n}\nvar ObservableStrategy = /** @class */ (function () {\n    function ObservableStrategy() {\n    }\n    /**\n     * @param {?} async\n     * @param {?} updateLatestValue\n     * @return {?}\n     */\n    ObservableStrategy.prototype.createSubscription = /**\n     * @param {?} async\n     * @param {?} updateLatestValue\n     * @return {?}\n     */\n    function (async, updateLatestValue) {\n        return async.subscribe({ next: updateLatestValue, error: function (e) { throw e; } });\n    };\n    /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    ObservableStrategy.prototype.dispose = /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    function (subscription) { subscription.unsubscribe(); };\n    /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    ObservableStrategy.prototype.onDestroy = /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    function (subscription) { subscription.unsubscribe(); };\n    return ObservableStrategy;\n}());\nvar PromiseStrategy = /** @class */ (function () {\n    function PromiseStrategy() {\n    }\n    /**\n     * @param {?} async\n     * @param {?} updateLatestValue\n     * @return {?}\n     */\n    PromiseStrategy.prototype.createSubscription = /**\n     * @param {?} async\n     * @param {?} updateLatestValue\n     * @return {?}\n     */\n    function (async, updateLatestValue) {\n        return async.then(updateLatestValue, function (e) { throw e; });\n    };\n    /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    PromiseStrategy.prototype.dispose = /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    function (subscription) { };\n    /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    PromiseStrategy.prototype.onDestroy = /**\n     * @param {?} subscription\n     * @return {?}\n     */\n    function (subscription) { };\n    return PromiseStrategy;\n}());\nvar /** @type {?} */ _promiseStrategy = new PromiseStrategy();\nvar /** @type {?} */ _observableStrategy = new ObservableStrategy();\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Unwraps a value from an asynchronous primitive.\n * \\@howToUse `observable_or_promise_expression | async`\n * \\@description\n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks.\n *\n *\n * ## Examples\n *\n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n *\n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n *\n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n *\n * {\\@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n *\n * \\@stable\n */\nvar AsyncPipe = /** @class */ (function () {\n    function AsyncPipe(_ref) {\n        this._ref = _ref;\n        this._latestValue = null;\n        this._latestReturnedValue = null;\n        this._subscription = null;\n        this._obj = null;\n        this._strategy = /** @type {?} */ ((null));\n    }\n    /**\n     * @return {?}\n     */\n    AsyncPipe.prototype.ngOnDestroy = /**\n     * @return {?}\n     */\n    function () {\n        if (this._subscription) {\n            this._dispose();\n        }\n    };\n    /**\n     * @param {?} obj\n     * @return {?}\n     */\n    AsyncPipe.prototype.transform = /**\n     * @param {?} obj\n     * @return {?}\n     */\n    function (obj) {\n        if (!this._obj) {\n            if (obj) {\n                this._subscribe(obj);\n            }\n            this._latestReturnedValue = this._latestValue;\n            return this._latestValue;\n        }\n        if (obj !== this._obj) {\n            this._dispose();\n            return this.transform(/** @type {?} */ (obj));\n        }\n        if (this._latestValue === this._latestReturnedValue) {\n            return this._latestReturnedValue;\n        }\n        this._latestReturnedValue = this._latestValue;\n        return WrappedValue.wrap(this._latestValue);\n    };\n    /**\n     * @param {?} obj\n     * @return {?}\n     */\n    AsyncPipe.prototype._subscribe = /**\n     * @param {?} obj\n     * @return {?}\n     */\n    function (obj) {\n        var _this = this;\n        this._obj = obj;\n        this._strategy = this._selectStrategy(obj);\n        this._subscription = this._strategy.createSubscription(obj, function (value) { return _this._updateLatestValue(obj, value); });\n    };\n    /**\n     * @param {?} obj\n     * @return {?}\n     */\n    AsyncPipe.prototype._selectStrategy = /**\n     * @param {?} obj\n     * @return {?}\n     */\n    function (obj) {\n        if (ɵisPromise(obj)) {\n            return _promiseStrategy;\n        }\n        if (ɵisObservable(obj)) {\n            return _observableStrategy;\n        }\n        throw invalidPipeArgumentError(AsyncPipe, obj);\n    };\n    /**\n     * @return {?}\n     */\n    AsyncPipe.prototype._dispose = /**\n     * @return {?}\n     */\n    function () {\n        this._strategy.dispose(/** @type {?} */ ((this._subscription)));\n        this._latestValue = null;\n        this._latestReturnedValue = null;\n        this._subscription = null;\n        this._obj = null;\n    };\n    /**\n     * @param {?} async\n     * @param {?} value\n     * @return {?}\n     */\n    AsyncPipe.prototype._updateLatestValue = /**\n     * @param {?} async\n     * @param {?} value\n     * @return {?}\n     */\n    function (async, value) {\n        if (async === this._obj) {\n            this._latestValue = value;\n            this._ref.markForCheck();\n        }\n    };\n    AsyncPipe.decorators = [\n        { type: Pipe, args: [{ name: 'async', pure: false },] },\n    ];\n    /** @nocollapse */\n    AsyncPipe.ctorParameters = function () { return [\n        { type: ChangeDetectorRef, },\n    ]; };\n    return AsyncPipe;\n}());\nexport { AsyncPipe };\nfunction AsyncPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    AsyncPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    AsyncPipe.ctorParameters;\n    /** @type {?} */\n    AsyncPipe.prototype._latestValue;\n    /** @type {?} */\n    AsyncPipe.prototype._latestReturnedValue;\n    /** @type {?} */\n    AsyncPipe.prototype._subscription;\n    /** @type {?} */\n    AsyncPipe.prototype._obj;\n    /** @type {?} */\n    AsyncPipe.prototype._strategy;\n    /** @type {?} */\n    AsyncPipe.prototype._ref;\n}\n//# sourceMappingURL=async_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Pipe } from '@angular/core';\nimport { invalidPipeArgumentError } from './invalid_pipe_argument_error';\n/**\n * Transforms text to lowercase.\n *\n * {\\@example  common/pipes/ts/lowerupper_pipe.ts region='LowerUpperPipe' }\n *\n * \\@stable\n */\nvar LowerCasePipe = /** @class */ (function () {\n    function LowerCasePipe() {\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    LowerCasePipe.prototype.transform = /**\n     * @param {?} value\n     * @return {?}\n     */\n    function (value) {\n        if (!value)\n            return value;\n        if (typeof value !== 'string') {\n            throw invalidPipeArgumentError(LowerCasePipe, value);\n        }\n        return value.toLowerCase();\n    };\n    LowerCasePipe.decorators = [\n        { type: Pipe, args: [{ name: 'lowercase' },] },\n    ];\n    /** @nocollapse */\n    LowerCasePipe.ctorParameters = function () { return []; };\n    return LowerCasePipe;\n}());\nexport { LowerCasePipe };\nfunction LowerCasePipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    LowerCasePipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    LowerCasePipe.ctorParameters;\n}\n/**\n * Helper method to transform a single word to titlecase.\n *\n * \\@stable\n * @param {?} word\n * @return {?}\n */\nfunction titleCaseWord(word) {\n    if (!word)\n        return word;\n    return word[0].toUpperCase() + word.substr(1).toLowerCase();\n}\n/**\n * Transforms text to titlecase.\n *\n * \\@stable\n */\nvar TitleCasePipe = /** @class */ (function () {\n    function TitleCasePipe() {\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    TitleCasePipe.prototype.transform = /**\n     * @param {?} value\n     * @return {?}\n     */\n    function (value) {\n        if (!value)\n            return value;\n        if (typeof value !== 'string') {\n            throw invalidPipeArgumentError(TitleCasePipe, value);\n        }\n        return value.split(/\\b/g).map(function (word) { return titleCaseWord(word); }).join('');\n    };\n    TitleCasePipe.decorators = [\n        { type: Pipe, args: [{ name: 'titlecase' },] },\n    ];\n    /** @nocollapse */\n    TitleCasePipe.ctorParameters = function () { return []; };\n    return TitleCasePipe;\n}());\nexport { TitleCasePipe };\nfunction TitleCasePipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    TitleCasePipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    TitleCasePipe.ctorParameters;\n}\n/**\n * Transforms text to uppercase.\n *\n * \\@stable\n */\nvar UpperCasePipe = /** @class */ (function () {\n    function UpperCasePipe() {\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    UpperCasePipe.prototype.transform = /**\n     * @param {?} value\n     * @return {?}\n     */\n    function (value) {\n        if (!value)\n            return value;\n        if (typeof value !== 'string') {\n            throw invalidPipeArgumentError(UpperCasePipe, value);\n        }\n        return value.toUpperCase();\n    };\n    UpperCasePipe.decorators = [\n        { type: Pipe, args: [{ name: 'uppercase' },] },\n    ];\n    /** @nocollapse */\n    UpperCasePipe.ctorParameters = function () { return []; };\n    return UpperCasePipe;\n}());\nexport { UpperCasePipe };\nfunction UpperCasePipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    UpperCasePipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    UpperCasePipe.ctorParameters;\n}\n//# sourceMappingURL=case_conversion_pipes.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Pi<PERSON> } from '@angular/core';\nimport { NgLocalization, getPluralCategory } from '../i18n/localization';\nimport { invalidPipeArgumentError } from './invalid_pipe_argument_error';\nvar /** @type {?} */ _INTERPOLATION_REGEXP = /#/g;\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Maps a value to a string that pluralizes the value according to locale rules.\n * \\@howToUse `expression | i18nPlural:mapping[:locale]`\n * \\@description\n *\n *  Where:\n *  - `expression` is a number.\n *  - `mapping` is an object that mimics the ICU format, see\n *    http://userguide.icu-project.org/formatparse/messages\n *  - `locale` is a `string` defining the locale to use (uses the current {\\@link LOCALE_ID} by\n * default)\n *\n *  ## Example\n *\n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n *\n * \\@experimental\n */\nvar I18nPluralPipe = /** @class */ (function () {\n    function I18nPluralPipe(_localization) {\n        this._localization = _localization;\n    }\n    /**\n     * @param {?} value\n     * @param {?} pluralMap\n     * @param {?=} locale\n     * @return {?}\n     */\n    I18nPluralPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?} pluralMap\n     * @param {?=} locale\n     * @return {?}\n     */\n    function (value, pluralMap, locale) {\n        if (value == null)\n            return '';\n        if (typeof pluralMap !== 'object' || pluralMap === null) {\n            throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n        }\n        var /** @type {?} */ key = getPluralCategory(value, Object.keys(pluralMap), this._localization, locale);\n        return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n    };\n    I18nPluralPipe.decorators = [\n        { type: Pipe, args: [{ name: 'i18nPlural', pure: true },] },\n    ];\n    /** @nocollapse */\n    I18nPluralPipe.ctorParameters = function () { return [\n        { type: NgLocalization, },\n    ]; };\n    return I18nPluralPipe;\n}());\nexport { I18nPluralPipe };\nfunction I18nPluralPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    I18nPluralPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    I18nPluralPipe.ctorParameters;\n    /** @type {?} */\n    I18nPluralPipe.prototype._localization;\n}\n//# sourceMappingURL=i18n_plural_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Pipe } from '@angular/core';\nimport { invalidPipeArgumentError } from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Generic selector that displays the string that matches the current value.\n * \\@howToUse `expression | i18nSelect:mapping`\n * \\@description\n *\n *  Where `mapping` is an object that indicates the text that should be displayed\n *  for different values of the provided `expression`.\n *  If none of the keys of the mapping match the value of the `expression`, then the content\n *  of the `other` key is returned when present, otherwise an empty string is returned.\n *\n *  ## Example\n *\n * {\\@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n *\n *  \\@experimental\n */\nvar I18nSelectPipe = /** @class */ (function () {\n    function I18nSelectPipe() {\n    }\n    /**\n     * @param {?} value\n     * @param {?} mapping\n     * @return {?}\n     */\n    I18nSelectPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?} mapping\n     * @return {?}\n     */\n    function (value, mapping) {\n        if (value == null)\n            return '';\n        if (typeof mapping !== 'object' || typeof value !== 'string') {\n            throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n        }\n        if (mapping.hasOwnProperty(value)) {\n            return mapping[value];\n        }\n        if (mapping.hasOwnProperty('other')) {\n            return mapping['other'];\n        }\n        return '';\n    };\n    I18nSelectPipe.decorators = [\n        { type: Pipe, args: [{ name: 'i18nSelect', pure: true },] },\n    ];\n    /** @nocollapse */\n    I18nSelectPipe.ctorParameters = function () { return []; };\n    return I18nSelectPipe;\n}());\nexport { I18nSelectPipe };\nfunction I18nSelectPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    I18nSelectPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    I18nSelectPipe.ctorParameters;\n}\n//# sourceMappingURL=i18n_select_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Pipe } from '@angular/core';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Converts value into JSON string.\n * \\@howToUse `expression | json`\n * \\@description\n *\n * Converts value into string using `JSON.stringify`. Useful for debugging.\n *\n * ### Example\n * {\\@example common/pipes/ts/json_pipe.ts region='JsonPipe'}\n *\n * \\@stable\n */\nvar JsonPipe = /** @class */ (function () {\n    function JsonPipe() {\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    JsonPipe.prototype.transform = /**\n     * @param {?} value\n     * @return {?}\n     */\n    function (value) { return JSON.stringify(value, null, 2); };\n    JsonPipe.decorators = [\n        { type: Pipe, args: [{ name: 'json', pure: false },] },\n    ];\n    /** @nocollapse */\n    JsonPipe.ctorParameters = function () { return []; };\n    return JsonPipe;\n}());\nexport { JsonPipe };\nfunction JsonPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    JsonPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    JsonPipe.ctorParameters;\n}\n//# sourceMappingURL=json_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Inject, LOCALE_ID, Pipe } from '@angular/core';\nimport { formatNumber } from '../i18n/format_number';\nimport { NumberFormatStyle, getCurrencySymbol } from '../i18n/locale_data_api';\nimport { invalidPipeArgumentError } from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number according to locale rules.\n * \\@howToUse `number_expression | number[:digitInfo[:locale]]`\n *\n * Formats a number as text. Group sizing and separator and other locale-specific\n * configurations are based on the active locale.\n *\n * where `expression` is a number:\n *  - `digitInfo` is a `string` which has a following format: <br>\n *     <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>\n *   - `minIntegerDigits` is the minimum number of integer digits to use. Defaults to `1`.\n *   - `minFractionDigits` is the minimum number of digits after fraction. Defaults to `0`.\n *   - `maxFractionDigits` is the maximum number of digits after fraction. Defaults to `3`.\n *  - `locale` is a `string` defining the locale to use (uses the current {\\@link LOCALE_ID} by\n * default)\n *\n * For more information on the acceptable range for each of these numbers and other\n * details see your native internationalization library.\n *\n * ### Example\n *\n * {\\@example common/pipes/ts/number_pipe.ts region='NumberPipe'}\n *\n * \\@stable\n */\nvar DecimalPipe = /** @class */ (function () {\n    function DecimalPipe(_locale) {\n        this._locale = _locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} digits\n     * @param {?=} locale\n     * @return {?}\n     */\n    DecimalPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} digits\n     * @param {?=} locale\n     * @return {?}\n     */\n    function (value, digits, locale) {\n        if (isEmpty(value))\n            return null;\n        locale = locale || this._locale;\n        var _a = formatNumber(value, locale, NumberFormatStyle.Decimal, digits), str = _a.str, error = _a.error;\n        if (error) {\n            throw invalidPipeArgumentError(DecimalPipe, error);\n        }\n        return str;\n    };\n    DecimalPipe.decorators = [\n        { type: Pipe, args: [{ name: 'number' },] },\n    ];\n    /** @nocollapse */\n    DecimalPipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return DecimalPipe;\n}());\nexport { DecimalPipe };\nfunction DecimalPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    DecimalPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    DecimalPipe.ctorParameters;\n    /** @type {?} */\n    DecimalPipe.prototype._locale;\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as a percentage according to locale rules.\n * \\@howToUse `number_expression | percent[:digitInfo[:locale]]`\n *\n * \\@description\n *\n * Formats a number as percentage.\n *\n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n *  - `locale` is a `string` defining the locale to use (uses the current {\\@link LOCALE_ID} by\n * default)\n *\n * ### Example\n *\n * {\\@example common/pipes/ts/percent_pipe.ts region='PercentPipe'}\n *\n * \\@stable\n */\nvar PercentPipe = /** @class */ (function () {\n    function PercentPipe(_locale) {\n        this._locale = _locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} digits\n     * @param {?=} locale\n     * @return {?}\n     */\n    PercentPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} digits\n     * @param {?=} locale\n     * @return {?}\n     */\n    function (value, digits, locale) {\n        if (isEmpty(value))\n            return null;\n        locale = locale || this._locale;\n        var _a = formatNumber(value, locale, NumberFormatStyle.Percent, digits), str = _a.str, error = _a.error;\n        if (error) {\n            throw invalidPipeArgumentError(PercentPipe, error);\n        }\n        return str;\n    };\n    PercentPipe.decorators = [\n        { type: Pipe, args: [{ name: 'percent' },] },\n    ];\n    /** @nocollapse */\n    PercentPipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return PercentPipe;\n}());\nexport { PercentPipe };\nfunction PercentPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    PercentPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    PercentPipe.ctorParameters;\n    /** @type {?} */\n    PercentPipe.prototype._locale;\n}\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Formats a number as currency using locale rules.\n * \\@howToUse `number_expression | currency[:currencyCode[:display[:digitInfo[:locale]]]]`\n * \\@description\n *\n * Use `currency` to format a number as currency.\n *\n * - `currencyCode` is the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code, such\n *    as `USD` for the US dollar and `EUR` for the euro.\n * - `display` indicates whether to show the currency symbol or the code.\n *   - `code`: use code (e.g. `USD`).\n *   - `symbol`(default): use symbol (e.g. `$`).\n *   - `symbol-narrow`: some countries have two symbols for their currency, one regular and one\n *   narrow (e.g. the canadian dollar CAD has the symbol `CA$` and the symbol-narrow `$`).\n *   - boolean (deprecated from v5): `true` for symbol and false for `code`\n *   If there is no narrow symbol for the chosen currency, the regular symbol will be used.\n * - `digitInfo` See {\\@link DecimalPipe} for detailed description.\n *  - `locale` is a `string` defining the locale to use (uses the current {\\@link LOCALE_ID} by\n * default)\n *\n * ### Example\n *\n * {\\@example common/pipes/ts/currency_pipe.ts region='CurrencyPipe'}\n *\n * \\@stable\n */\nvar CurrencyPipe = /** @class */ (function () {\n    function CurrencyPipe(_locale) {\n        this._locale = _locale;\n    }\n    /**\n     * @param {?} value\n     * @param {?=} currencyCode\n     * @param {?=} display\n     * @param {?=} digits\n     * @param {?=} locale\n     * @return {?}\n     */\n    CurrencyPipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?=} currencyCode\n     * @param {?=} display\n     * @param {?=} digits\n     * @param {?=} locale\n     * @return {?}\n     */\n    function (value, currencyCode, display, digits, locale) {\n        if (display === void 0) { display = 'symbol'; }\n        if (isEmpty(value))\n            return null;\n        locale = locale || this._locale;\n        if (typeof display === 'boolean') {\n            if (/** @type {?} */ (console) && /** @type {?} */ (console.warn)) {\n                console.warn(\"Warning: the currency pipe has been changed in Angular v5. The symbolDisplay option (third parameter) is now a string instead of a boolean. The accepted values are \\\"code\\\", \\\"symbol\\\" or \\\"symbol-narrow\\\".\");\n            }\n            display = display ? 'symbol' : 'code';\n        }\n        var /** @type {?} */ currency = currencyCode || 'USD';\n        if (display !== 'code') {\n            currency = getCurrencySymbol(currency, display === 'symbol' ? 'wide' : 'narrow');\n        }\n        var _a = formatNumber(value, locale, NumberFormatStyle.Currency, digits, currency), str = _a.str, error = _a.error;\n        if (error) {\n            throw invalidPipeArgumentError(CurrencyPipe, error);\n        }\n        return str;\n    };\n    CurrencyPipe.decorators = [\n        { type: Pipe, args: [{ name: 'currency' },] },\n    ];\n    /** @nocollapse */\n    CurrencyPipe.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [LOCALE_ID,] },] },\n    ]; };\n    return CurrencyPipe;\n}());\nexport { CurrencyPipe };\nfunction CurrencyPipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    CurrencyPipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    CurrencyPipe.ctorParameters;\n    /** @type {?} */\n    CurrencyPipe.prototype._locale;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isEmpty(value) {\n    return value == null || value === '' || value !== value;\n}\n//# sourceMappingURL=number_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Pipe } from '@angular/core';\nimport { invalidPipeArgumentError } from './invalid_pipe_argument_error';\n/**\n * \\@ngModule CommonModule\n * \\@whatItDoes Creates a new List or String containing a subset (slice) of the elements.\n * \\@howToUse `array_or_string_expression | slice:start[:end]`\n * \\@description\n *\n * Where the input expression is a `List` or `String`, and:\n * - `start`: The starting index of the subset to return.\n *   - **a positive integer**: return the item at `start` index and all items after\n *     in the list or string expression.\n *   - **a negative integer**: return the item at `start` index from the end and all items after\n *     in the list or string expression.\n *   - **if positive and greater than the size of the expression**: return an empty list or string.\n *   - **if negative and greater than the size of the expression**: return entire list or string.\n * - `end`: The ending index of the subset to return.\n *   - **omitted**: return all items until the end.\n *   - **if positive**: return all items before `end` index of the list or string.\n *   - **if negative**: return all items before `end` index from the end of the list or string.\n *\n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n *\n * When operating on a [List], the returned list is always a copy even when all\n * the elements are being returned.\n *\n * When operating on a blank value, the pipe returns the blank value.\n *\n * ## List Example\n *\n * This `ngFor` example:\n *\n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n *\n * produces the following:\n *\n *     <li>b</li>\n *     <li>c</li>\n *\n * ## String Examples\n *\n * {\\@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n *\n * \\@stable\n */\nvar SlicePipe = /** @class */ (function () {\n    function SlicePipe() {\n    }\n    /**\n     * @param {?} value\n     * @param {?} start\n     * @param {?=} end\n     * @return {?}\n     */\n    SlicePipe.prototype.transform = /**\n     * @param {?} value\n     * @param {?} start\n     * @param {?=} end\n     * @return {?}\n     */\n    function (value, start, end) {\n        if (value == null)\n            return value;\n        if (!this.supports(value)) {\n            throw invalidPipeArgumentError(SlicePipe, value);\n        }\n        return value.slice(start, end);\n    };\n    /**\n     * @param {?} obj\n     * @return {?}\n     */\n    SlicePipe.prototype.supports = /**\n     * @param {?} obj\n     * @return {?}\n     */\n    function (obj) { return typeof obj === 'string' || Array.isArray(obj); };\n    SlicePipe.decorators = [\n        { type: Pipe, args: [{ name: 'slice', pure: false },] },\n    ];\n    /** @nocollapse */\n    SlicePipe.ctorParameters = function () { return []; };\n    return SlicePipe;\n}());\nexport { SlicePipe };\nfunction SlicePipe_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    SlicePipe.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    SlicePipe.ctorParameters;\n}\n//# sourceMappingURL=slice_pipe.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { AsyncPipe } from './async_pipe';\nimport { LowerCasePipe, TitleCasePipe, UpperCasePipe } from './case_conversion_pipes';\nimport { DatePipe } from './date_pipe';\nimport { I18nPluralPipe } from './i18n_plural_pipe';\nimport { I18nSelectPipe } from './i18n_select_pipe';\nimport { JsonPipe } from './json_pipe';\nimport { CurrencyPipe, DecimalPipe, PercentPipe } from './number_pipe';\nimport { SlicePipe } from './slice_pipe';\nexport { AsyncPipe, CurrencyPipe, DatePipe, DecimalPipe, I18nPluralPipe, I18nSelectPipe, JsonPipe, LowerCasePipe, PercentPipe, SlicePipe, TitleCasePipe, UpperCasePipe };\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\nexport var /** @type {?} */ COMMON_PIPES = [\n    AsyncPipe,\n    UpperCasePipe,\n    LowerCasePipe,\n    JsonPipe,\n    SlicePipe,\n    DecimalPipe,\n    PercentPipe,\n    TitleCasePipe,\n    CurrencyPipe,\n    DatePipe,\n    I18nPluralPipe,\n    I18nSelectPipe,\n];\n//# sourceMappingURL=index.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { NgModule } from '@angular/core';\nimport { COMMON_DIRECTIVES } from './directives/index';\nimport { DEPRECATED_PLURAL_FN, NgLocaleLocalization, NgLocalization, getPluralCase } from './i18n/localization';\nimport { COMMON_DEPRECATED_I18N_PIPES } from './pipes/deprecated/index';\nimport { COMMON_PIPES } from './pipes/index';\n/**\n * The module that includes all the basic Angular directives like {\\@link NgIf}, {\\@link NgForOf}, ...\n *\n * \\@stable\n */\nvar CommonModule = /** @class */ (function () {\n    function CommonModule() {\n    }\n    CommonModule.decorators = [\n        { type: NgModule, args: [{\n                    declarations: [COMMON_DIRECTIVES, COMMON_PIPES],\n                    exports: [COMMON_DIRECTIVES, COMMON_PIPES],\n                    providers: [\n                        { provide: NgLocalization, useClass: NgLocaleLocalization },\n                    ],\n                },] },\n    ];\n    /** @nocollapse */\n    CommonModule.ctorParameters = function () { return []; };\n    return CommonModule;\n}());\nexport { CommonModule };\nfunction CommonModule_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    CommonModule.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    CommonModule.ctorParameters;\n}\nvar ɵ0 = getPluralCase;\n/**\n * A module that contains the deprecated i18n pipes.\n *\n * @deprecated from v5\n */\nvar DeprecatedI18NPipesModule = /** @class */ (function () {\n    function DeprecatedI18NPipesModule() {\n    }\n    DeprecatedI18NPipesModule.decorators = [\n        { type: NgModule, args: [{\n                    declarations: [COMMON_DEPRECATED_I18N_PIPES],\n                    exports: [COMMON_DEPRECATED_I18N_PIPES],\n                    providers: [{ provide: DEPRECATED_PLURAL_FN, useValue: ɵ0 }],\n                },] },\n    ];\n    /** @nocollapse */\n    DeprecatedI18NPipesModule.ctorParameters = function () { return []; };\n    return DeprecatedI18NPipesModule;\n}());\nexport { DeprecatedI18NPipesModule };\nfunction DeprecatedI18NPipesModule_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    DeprecatedI18NPipesModule.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    DeprecatedI18NPipesModule.ctorParameters;\n}\nexport { ɵ0 };\n//# sourceMappingURL=common_module.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { InjectionToken } from '@angular/core';\n/**\n * A DI Token representing the main rendering context. In a browser this is the DOM Document.\n *\n * Note: Document might not be available in the Application Context when Application and Rendering\n * Contexts are not the same (e.g. when running the application into a Web Worker).\n *\n * \\@stable\n */\nexport var /** @type {?} */ DOCUMENT = new InjectionToken('DocumentToken');\n//# sourceMappingURL=dom_tokens.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport var /** @type {?} */ PLATFORM_BROWSER_ID = 'browser';\nexport var /** @type {?} */ PLATFORM_SERVER_ID = 'server';\nexport var /** @type {?} */ PLATFORM_WORKER_APP_ID = 'browserWorkerApp';\nexport var /** @type {?} */ PLATFORM_WORKER_UI_ID = 'browserWorkerUi';\n/**\n * Returns whether a platform id represents a browser platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformBrowser(platformId) {\n    return platformId === PLATFORM_BROWSER_ID;\n}\n/**\n * Returns whether a platform id represents a server platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformServer(platformId) {\n    return platformId === PLATFORM_SERVER_ID;\n}\n/**\n * Returns whether a platform id represents a web worker app platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformWorkerApp(platformId) {\n    return platformId === PLATFORM_WORKER_APP_ID;\n}\n/**\n * Returns whether a platform id represents a web worker UI platform.\n * \\@experimental\n * @param {?} platformId\n * @return {?}\n */\nexport function isPlatformWorkerUi(platformId) {\n    return platformId === PLATFORM_WORKER_UI_ID;\n}\n//# sourceMappingURL=platform_id.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Version } from '@angular/core';\n/**\n * \\@stable\n */\nexport var /** @type {?} */ VERSION = new Version('5.2.11');\n//# sourceMappingURL=version.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\nexport { PlatformLocation, LOCATION_INITIALIZED, LocationStrategy, APP_BASE_HREF, HashLocationStrategy, PathLocationStrategy, Location } from './location/index';\nexport { NgLocaleLocalization, NgLocalization } from './i18n/localization';\nexport { registerLocaleData } from './i18n/locale_data';\nexport { Plural, NumberFormatStyle, FormStyle, TranslationWidth, FormatWidth, NumberSymbol, WeekDay, getCurrencySymbol, getLocaleDayPeriods, getLocaleDayNames, getLocaleMonthNames, getLocaleId, getLocaleEraNames, getLocaleWeekEndRange, getLocaleFirstDayOfWeek, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocalePluralCase, getLocaleTimeFormat, getLocaleNumberSymbol, getLocaleNumberFormat, getLocaleCurrencyName, getLocaleCurrencySymbol } from './i18n/locale_data_api';\nexport { parseCookieValue as ɵparseCookieValue } from './cookie';\nexport { CommonModule, DeprecatedI18NPipesModule } from './common_module';\nexport { NgClass, NgForOf, NgForOfContext, NgIf, NgIfContext, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NgComponentOutlet } from './directives/index';\nexport { DOCUMENT } from './dom_tokens';\nexport { AsyncPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, JsonPipe, LowerCasePipe, CurrencyPipe, DecimalPipe, PercentPipe, SlicePipe, UpperCasePipe, TitleCasePipe } from './pipes/index';\nexport { DeprecatedDatePipe, DeprecatedCurrencyPipe, DeprecatedDecimalPipe, DeprecatedPercentPipe } from './pipes/deprecated/index';\nexport { PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID, PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID, PLATFORM_WORKER_APP_ID as ɵPLATFORM_WORKER_APP_ID, PLATFORM_WORKER_UI_ID as ɵPLATFORM_WORKER_UI_ID, isPlatformBrowser, isPlatformServer, isPlatformWorkerApp, isPlatformWorkerUi } from './platform_id';\nexport { VERSION } from './version';\n//# sourceMappingURL=common.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport { NgLocaleLocalization, NgLocalization, registerLocaleData, Plural, NumberFormatStyle, FormStyle, TranslationWidth, FormatWidth, NumberSymbol, WeekDay, getCurrencySymbol, getLocaleDayPeriods, getLocaleDayNames, getLocaleMonthNames, getLocaleId, getLocaleEraNames, getLocaleWeekEndRange, getLocaleFirstDayOfWeek, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocalePluralCase, getLocaleTimeFormat, getLocaleNumberSymbol, getLocaleNumberFormat, getLocaleCurrencyName, getLocaleCurrencySymbol, ɵparseCookieValue, CommonModule, DeprecatedI18NPipesModule, NgClass, NgForOf, NgForOfContext, NgIf, NgIfContext, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NgComponentOutlet, DOCUMENT, AsyncPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, JsonPipe, LowerCasePipe, CurrencyPipe, DecimalPipe, PercentPipe, SlicePipe, UpperCasePipe, TitleCasePipe, DeprecatedDatePipe, DeprecatedCurrencyPipe, DeprecatedDecimalPipe, DeprecatedPercentPipe, ɵPLATFORM_BROWSER_ID, ɵPLATFORM_SERVER_ID, ɵPLATFORM_WORKER_APP_ID, ɵPLATFORM_WORKER_UI_ID, isPlatformBrowser, isPlatformServer, isPlatformWorkerApp, isPlatformWorkerUi, VERSION, PlatformLocation, LOCATION_INITIALIZED, LocationStrategy, APP_BASE_HREF, HashLocationStrategy, PathLocationStrategy, Location } from './src/common';\nexport { registerLocaleData as ɵregisterLocaleData } from './src/i18n/locale_data';\n// This file only reexports content of the `src` folder. Keep it that way.\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { ɵregisterLocaleData, NgLocaleLocalization, NgLocalization, registerLocaleData, Plural, NumberFormatStyle, FormStyle, TranslationWidth, FormatWidth, NumberSymbol, WeekDay, getCurrencySymbol, getLocaleDayPeriods, getLocaleDayNames, getLocaleMonthNames, getLocaleId, getLocaleEraNames, getLocaleWeekEndRange, getLocaleFirstDayOfWeek, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocalePluralCase, getLocaleTimeFormat, getLocaleNumberSymbol, getLocaleNumberFormat, getLocaleCurrencyName, getLocaleCurrencySymbol, ɵparseCookieValue, CommonModule, DeprecatedI18NPipesModule, NgClass, NgForO<PERSON>, NgForOfContext, NgIf, NgIfContext, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NgComponentOutlet, DOCUMENT, AsyncPipe, DatePipe, I18nPluralPipe, I18nSelectPipe, JsonPipe, LowerCasePipe, CurrencyPipe, DecimalPipe, PercentPipe, SlicePipe, UpperCasePipe, TitleCasePipe, DeprecatedDatePipe, DeprecatedCurrencyPipe, DeprecatedDecimalPipe, DeprecatedPercentPipe, ɵPLATFORM_BROWSER_ID, ɵPLATFORM_SERVER_ID, ɵPLATFORM_WORKER_APP_ID, ɵPLATFORM_WORKER_UI_ID, isPlatformBrowser, isPlatformServer, isPlatformWorkerApp, isPlatformWorkerUi, VERSION, PlatformLocation, LOCATION_INITIALIZED, LocationStrategy, APP_BASE_HREF, HashLocationStrategy, PathLocationStrategy, Location } from './public_api';\nexport { COMMON_DIRECTIVES as ɵe } from './src/directives/index';\nexport { findLocaleData as ɵd } from './src/i18n/locale_data_api';\nexport { DEPRECATED_PLURAL_FN as ɵa, getPluralCase as ɵb } from './src/i18n/localization';\nexport { COMMON_DEPRECATED_I18N_PIPES as ɵg } from './src/pipes/deprecated/index';\nexport { COMMON_PIPES as ɵf } from './src/pipes/index';\n//# sourceMappingURL=common.js.map"], "names": ["tslib_1.__extends", "isListLikeIterable", "stringify", "isDate", "DATE_FORMATS_SPLIT", "DATE_FORMATS", "timeZoneGetter", "tslib_1.__assign", "formatNumber"], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAuBA,gBAAgB,kBAAkB,YAAY;IAC1C,SAAS,gBAAgB,GAAG;KAC3B;IACD,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC,CAAC;AACL,AAoFA;;;;AAIA,AAAO,IAAqB,oBAAoB,GAAG,IAAI,cAAc,CAAC,sBAAsB,CAAC,CAAC;;;;;;;AAO9F,AAAyC;AACzC,AAIA;;;GAGG;;ACtKH;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;;;;;;;AAmBA,IAmBA,gBAAgB,kBAAkB,YAAY;IAC1C,SAAS,gBAAgB,GAAG;KAC3B;IACD,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC,CAAC;AACL,AAyEA;;;;;;;;;;;;;;;;;;;;;;AAsBA,AAAO,IAAqB,aAAa,GAAG,IAAI,cAAc,CAAC,aAAa,CAAC;;ACtJ7E;;;;;;;;;;;AAWA,AAEA;;;;AAIA,AAAmC;AACnC,AAQA;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,QAAQ,kBAAkB,YAAY;IACtC,SAAS,QAAQ,CAAC,gBAAgB,EAAE;QAChC,IAAI,KAAK,GAAG,IAAI,CAAC;;;;QAIjB,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,qBAAqB,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,kBAAkB,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE;YAC5C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAChB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;gBACvB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,EAAE,CAAC,IAAI;aAClB,CAAC,CAAC;SACN,CAAC,CAAC;KACN;;;;;;;;;;;IAWD,QAAQ,CAAC,SAAS,CAAC,IAAI;;;;;IAKvB,UAAU,WAAW,EAAE;QACnB,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;KACnE,CAAC;;;;;;;;;;IAUF,QAAQ,CAAC,SAAS,CAAC,oBAAoB;;;;;;IAMvC,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;QACrC,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;KACrF,CAAC;;;;;;;;;;;IAWF,QAAQ,CAAC,SAAS,CAAC,SAAS;;;;;;IAM5B,UAAU,GAAG,EAAE;QACX,OAAO,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC5F,CAAC;;;;;;;;;;;;;;;IAeF,QAAQ,CAAC,SAAS,CAAC,kBAAkB;;;;;;;;IAQrC,UAAU,GAAG,EAAE;QACX,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACvB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;SACnB;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;KACzD,CAAC;;;;;;;;;;;;;IAaF,QAAQ,CAAC,SAAS,CAAC,EAAE;;;;;;;IAOrB,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;QACrC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC3D,CAAC;;;;;;;;;;;;IAYF,QAAQ,CAAC,SAAS,CAAC,YAAY;;;;;;;IAO/B,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;QACrC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC9D,CAAC;;;;;;;;IAQF,QAAQ,CAAC,SAAS,CAAC,OAAO;;;;IAI1B,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;;;;;;;;IAQlD,QAAQ,CAAC,SAAS,CAAC,IAAI;;;;IAIvB,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;;;;;;;;;;;IAW/C,QAAQ,CAAC,SAAS,CAAC,SAAS;;;;;;;IAO5B,UAAU,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;KACxF,CAAC;;;;;;;IAOF,QAAQ,CAAC,oBAAoB;;;;;;IAM7B,UAAU,MAAM,EAAE;QACd,OAAO,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;KAC9D,CAAC;;;;;;;IAOF,QAAQ,CAAC,aAAa;;;;;;IAMtB,UAAU,KAAK,EAAE,GAAG,EAAE;QAClB,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACnB,OAAO,GAAG,CAAC;SACd;QACD,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,qBAAqB,OAAO,GAAG,CAAC,CAAC;QACjC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,EAAE,CAAC;SACb;QACD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,EAAE,CAAC;SACb;QACD,IAAI,OAAO,IAAI,CAAC,EAAE;YACd,OAAO,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACnC;QACD,IAAI,OAAO,IAAI,CAAC,EAAE;YACd,OAAO,KAAK,GAAG,GAAG,CAAC;SACtB;QACD,OAAO,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;KAC5B,CAAC;;;;;;;;IAQF,QAAQ,CAAC,kBAAkB;;;;;;;IAO3B,UAAU,GAAG,EAAE;QACX,qBAAqB,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACjD,qBAAqB,UAAU,GAAG,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC;QACrE,qBAAqB,eAAe,GAAG,UAAU,IAAI,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1F,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;KAChE,CAAC;IACF,QAAQ,CAAC,UAAU,GAAG;QAClB,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,QAAQ,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC3C,EAAE,IAAI,EAAE,gBAAgB,GAAG;KAC9B,CAAC,EAAE,CAAC;IACL,OAAO,QAAQ,CAAC;CACnB,EAAE,CAAC,CAAC;AACL,AAyBA;;;;;AAKA,SAAS,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE;IACnC,OAAO,QAAQ,IAAI,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;CACtF;;;;;AAKD,SAAS,eAAe,CAAC,GAAG,EAAE;IAC1B,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;CAC3C;;ACpWD;;;;;;;;;;;AAWA,AAKA;;;;;;;;;;;;;;;;;AAiBA,IAAI,oBAAoB,kBAAkB,UAAU,MAAM,EAAE;IACxDA,SAAiB,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;IAChD,SAAS,oBAAoB,CAAC,iBAAiB,EAAE,SAAS,EAAE;QACxD,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QACtC,KAAK,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC5C,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;QACrB,IAAI,SAAS,IAAI,IAAI,EAAE;YACnB,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;SAC/B;QACD,OAAO,KAAK,CAAC;KAChB;;;;;IAKD,oBAAoB,CAAC,SAAS,CAAC,UAAU;;;;IAIzC,UAAU,EAAE,EAAE;QACV,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;KAC3C,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;IAG1C,YAAY,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;;;;;IAKvC,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;;IAInC,UAAU,WAAW,EAAE;QACnB,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE;;;QAGpD,qBAAqB,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;QACxD,IAAI,IAAI,IAAI,IAAI;YACZ,IAAI,GAAG,GAAG,CAAC;QACf,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACrD,CAAC;;;;;IAKF,oBAAoB,CAAC,SAAS,CAAC,kBAAkB;;;;IAIjD,UAAU,QAAQ,EAAE;QAChB,qBAAqB,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5E,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC;KAC7C,CAAC;;;;;;;;IAQF,oBAAoB,CAAC,SAAS,CAAC,SAAS;;;;;;;IAOxC,UAAU,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE;QACvC,qBAAqB,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,QAAQ,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;QACtG,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;YACjB,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SACzC;QACD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KACvD,CAAC;;;;;;;;IAQF,oBAAoB,CAAC,SAAS,CAAC,YAAY;;;;;;;IAO3C,UAAU,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE;QACvC,qBAAqB,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,QAAQ,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;QACtG,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;YACjB,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SACzC;QACD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KAC1D,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,OAAO;;;IAGtC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;;;;IAIlD,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;IAGnC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;IAC/C,oBAAoB,CAAC,UAAU,GAAG;QAC9B,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,oBAAoB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACvD,EAAE,IAAI,EAAE,gBAAgB,GAAG;QAC3B,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAE,EAAE,EAAE,EAAE;KACnG,CAAC,EAAE,CAAC;IACL,OAAO,oBAAoB,CAAC;CAC/B,CAAC,gBAAgB,CAAC,CAAC;;AC7JpB;;;;;;;;;;;AAWA,AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAI,oBAAoB,kBAAkB,UAAU,MAAM,EAAE;IACxDA,SAAiB,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;IAChD,SAAS,oBAAoB,CAAC,iBAAiB,EAAE,IAAI,EAAE;QACnD,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QACtC,KAAK,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,IAAI,GAAG,KAAK,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;SACvD;QACD,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,6GAA6G,CAAC,CAAC;SAClI;QACD,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,OAAO,KAAK,CAAC;KAChB;;;;;IAKD,oBAAoB,CAAC,SAAS,CAAC,UAAU;;;;IAIzC,UAAU,EAAE,EAAE;QACV,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;KAC3C,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;IAG1C,YAAY,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;;;;;IAKvC,oBAAoB,CAAC,SAAS,CAAC,kBAAkB;;;;IAIjD,UAAU,QAAQ,EAAE;QAChB,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC3D,CAAC;;;;;IAKF,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;;IAInC,UAAU,WAAW,EAAE;QACnB,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE;QACpD,qBAAqB,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC3D,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACjE,qBAAqB,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;QACxD,OAAO,IAAI,IAAI,WAAW,GAAG,EAAE,GAAG,QAAQ,GAAG,IAAI,GAAG,QAAQ,CAAC;KAChE,CAAC;;;;;;;;IAQF,oBAAoB,CAAC,SAAS,CAAC,SAAS;;;;;;;IAOxC,UAAU,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE;QACtC,qBAAqB,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,QAAQ,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;QAC7G,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;KAC/D,CAAC;;;;;;;;IAQF,oBAAoB,CAAC,SAAS,CAAC,YAAY;;;;;;;IAO3C,UAAU,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE;QACtC,qBAAqB,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,QAAQ,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;QAC7G,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;KAClE,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,OAAO;;;IAGtC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;;;;IAIlD,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;IAGnC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;IAC/C,oBAAoB,CAAC,UAAU,GAAG;QAC9B,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,oBAAoB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACvD,EAAE,IAAI,EAAE,gBAAgB,GAAG;QAC3B,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,aAAa,EAAE,EAAE,EAAE,EAAE;KACnG,CAAC,EAAE,CAAC;IACL,OAAO,oBAAoB,CAAC;CAC/B,CAAC,gBAAgB,CAAC,CAAC;;AChKpB;;;;;;;;;;GAUG;;ACVH;;;;;;;;;;;;;;;;AAgBA,AAAO,IAAqB,UAAU,GAAG;IACrC,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;IAClB,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,CAAC,IAAI,CAAC;IACb,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,KAAK,CAAC;IAChB,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,CAAC,MAAM,CAAC;IACf,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;IACnB,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,MAAM,CAAC;IACf,KAAK,EAAE,GAAG,GAAG,CAAC;IACd,KAAK,EAAE,GAAG,IAAI,CAAC;CAClB;;ACtHD;;;;;;;;;;;;;;;;;AAiBA,SAAS,MAAM,CAAC,CAAC,EAAE;IACf,qBAAqB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IACpH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;CACZ;AACD,eAAe;IACX,IAAI;IACJ;QACI,CAAC,GAAG,EAAE,GAAG,CAAC;QACV,CAAC,IAAI,EAAE,IAAI,CAAC;KACf;IACD;QACI,CAAC,IAAI,EAAE,IAAI,CAAC;;KAEf;IACD;QACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QACtF,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;QAC9E,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;KAC7C;;IAED;QACI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC5D,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QACpF;YACI,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW;YACrF,SAAS,EAAE,UAAU,EAAE,UAAU;SACpC;KACJ;;IAED,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACvE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC;IACtD,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC;IACxD;QACI,UAAU;;QAEV,gBAAgB;KACnB;IACD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC;IAC9D,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM;CACxE,CAAC;;AC1DF;;;;;;;;;;;;;;AAcA,AAAO,IAAqB,WAAW,GAAG,EAAE,CAAC;;;;;;;;;;;AAW7C,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE;IAC1D,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAC9B,SAAS,GAAG,QAAQ,CAAC;QACrB,QAAQ,GAAG,IAAI,CAAC,CAAC,gBAAgB,CAAC;KACrC;IACD,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACrD,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;IAC7B,IAAI,SAAS,EAAE;QACX,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,iBAAiB,GAAG,SAAS,CAAC;KACzD;CACJ;;ACnCD;;;;;;;;;;;AAWA,AAGA;AACA,IAAI,iBAAiB,GAAG;IACpB,OAAO,EAAE,CAAC;IACV,OAAO,EAAE,CAAC;IACV,QAAQ,EAAE,CAAC;IACX,UAAU,EAAE,CAAC;CAChB,CAAC;AACF,AACA,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACzD,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACzD,iBAAiB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AAC3D,iBAAiB,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;;AAE/D,IAAI,MAAM,GAAG;IACT,IAAI,EAAE,CAAC;IACP,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACX,CAAC;AACF,AACA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC7B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC3B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC3B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC3B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;;AAE/B,IAAI,SAAS,GAAG;IACZ,MAAM,EAAE,CAAC;IACT,UAAU,EAAE,CAAC;CAChB,CAAC;AACF,AACA,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACvC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;;AAE/C,IAAI,gBAAgB,GAAG;IACnB,MAAM,EAAE,CAAC;IACT,WAAW,EAAE,CAAC;IACd,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACX,CAAC;AACF,AACA,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACrD,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;AAC/D,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACjD,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;;AAEnD,IAAI,WAAW,GAAG;IACd,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,CAAC;IACT,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;CACV,CAAC;AACF,AACA,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AACzC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AAC3C,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACvC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;;AAEvC,IAAI,YAAY,GAAG;IACf,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,WAAW,EAAE,CAAC;IACd,QAAQ,EAAE,CAAC;IACX,SAAS,EAAE,CAAC;IACZ,WAAW,EAAE,CAAC;IACd,sBAAsB,EAAE,CAAC;IACzB,QAAQ,EAAE,CAAC;IACX,QAAQ,EAAE,CAAC;IACX,GAAG,EAAE,EAAE;IACP,aAAa,EAAE,EAAE;IACjB,eAAe,EAAE,EAAE;IACnB,aAAa,EAAE,EAAE;CACpB,CAAC;AACF,AACA,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AAC/C,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAC3C,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACzC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;AACvD,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AACjD,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;AACnD,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;AACvD,YAAY,CAAC,YAAY,CAAC,sBAAsB,CAAC,GAAG,wBAAwB,CAAC;AAC7E,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AACjD,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AACjD,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACvC,YAAY,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;AAC3D,YAAY,CAAC,YAAY,CAAC,eAAe,CAAC,GAAG,iBAAiB,CAAC;AAC/D,YAAY,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;;AAE3D,IAAI,OAAO,GAAG;IACV,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,OAAO,EAAE,CAAC;IACV,SAAS,EAAE,CAAC;IACZ,QAAQ,EAAE,CAAC;IACX,MAAM,EAAE,CAAC;IACT,QAAQ,EAAE,CAAC;CACd,CAAC;AACF,AACA,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACnC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACnC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACrC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;AACzC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AACvC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACnC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;;;;;;;;AAQvC,AAAO,SAAS,WAAW,CAAC,MAAM,EAAE;IAChC,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC;CACnD;;;;;;;;;;AAUD,AAAO,SAAS,mBAAmB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;IAC1D,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,qBAAqB,QAAQ,qBAAqB,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC;IACxH,qBAAqB,IAAI,GAAG,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACrE,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;CAC3C;;;;;;;;;;AAUD,AAAO,SAAS,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;IACxD,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,qBAAqB,QAAQ,qBAAqB,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC;IAC5G,qBAAqB,IAAI,GAAG,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACrE,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;CAC3C;;;;;;;;;;AAUD,AAAO,SAAS,mBAAmB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;IAC1D,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,qBAAqB,UAAU,qBAAqB,CAAC,IAAI,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC;IAClH,qBAAqB,MAAM,GAAG,mBAAmB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACzE,OAAO,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;CAC7C;;;;;;;;;AASD,AAAO,SAAS,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE;IAC7C,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,qBAAqB,QAAQ,qBAAqB,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC;IACtE,OAAO,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;CAC/C;;;;;;;;;AASD,AAAO,SAAS,uBAAuB,CAAC,MAAM,EAAE;IAC5C,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC,CAAC,sBAAsB,CAAC;CACvC;;;;;;;;;;AAUD,AAAO,SAAS,qBAAqB,CAAC,MAAM,EAAE;IAC1C,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC,CAAC,oBAAoB,CAAC;CACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BD,AAAO,SAAS,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE;IAC/C,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,mBAAmB,CAAC,IAAI,CAAC,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;CAChE;;;;;;;;;;;;;;;;;;;;;;;;AAwBD,AAAO,SAAS,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE;IAC/C,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,mBAAmB,CAAC,IAAI,CAAC,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;CAChE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BD,AAAO,SAAS,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE;IACnD,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,qBAAqB,kBAAkB,qBAAqB,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAAC;IAC3F,OAAO,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;CACzD;;;;;;;;;;AAUD,AAAO,SAAS,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE;IAClD,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,qBAAqB,GAAG,GAAG,IAAI,CAAC,EAAE,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAChE,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;QAC5B,IAAI,MAAM,KAAK,YAAY,CAAC,eAAe,EAAE;YACzC,OAAO,IAAI,CAAC,EAAE,qBAAqB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC7D;aACI,IAAI,MAAM,KAAK,YAAY,CAAC,aAAa,EAAE;YAC5C,OAAO,IAAI,CAAC,EAAE,qBAAqB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SAC3D;KACJ;IACD,OAAO,GAAG,CAAC;CACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCD,AAAO,SAAS,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE;IAChD,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;CAC7C;;;;;;;;;;AAUD,AAAO,SAAS,uBAAuB,CAAC,MAAM,EAAE;IAC5C,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC,EAAE,sBAAsB,IAAI,IAAI,CAAC;CAChD;;;;;;;;;;AAUD,AAAO,SAAS,qBAAqB,CAAC,MAAM,EAAE;IAC1C,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC,EAAE,oBAAoB,IAAI,IAAI,CAAC;CAC9C;;;;;;;;;AASD,AAAO,SAAS,mBAAmB,CAAC,MAAM,EAAE;IACxC,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC,EAAE,kBAAkB,CAAC;CACpC;;;;;AAKD,SAAS,aAAa,CAAC,IAAI,EAAE;IACzB,IAAI,CAAC,IAAI,CAAC,EAAE,iBAAiB,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,6CAA6C,GAAG,IAAI,CAAC,CAAC,gBAAgB,GAAG,qGAAqG,CAAC,CAAC;KACnM;CACJ;;;;;;;;;;;;;;;;;;AAkBD,AAAO,SAAS,4BAA4B,CAAC,MAAM,EAAE;IACjD,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,aAAa,CAAC,IAAI,CAAC,CAAC;IACpB,qBAAqB,KAAK,GAAG,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAAC,4BAA4B,IAAI,EAAE,CAAC;IAC1F,OAAO,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;QAC7B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC1B,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC;SAC5B;QACD,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvD,CAAC,CAAC;CACN;;;;;;;;;;;;;;;;;;AAkBD,AAAO,SAAS,wBAAwB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;IAC/D,qBAAqB,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IACnD,aAAa,CAAC,IAAI,CAAC,CAAC;IACpB,qBAAqB,cAAc,qBAAqB;QACpD,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAAC,6BAA6B;QACvD,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAAC,gCAAgC;KAC7D,CAAC,CAAC;IACH,qBAAqB,UAAU,GAAG,mBAAmB,CAAC,cAAc,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;IACvF,OAAO,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;CACvD;;;;;;;;;;;;;;AAcD,SAAS,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE;IACtC,KAAK,qBAAqB,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC9C,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;YAChC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;SAClB;KACJ;IACD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;CAC7D;;;;;;AAMD,SAAS,WAAW,CAAC,IAAI,EAAE;IACvB,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC;CACrC;;;;;;;;AAQD,AAAO,SAAS,cAAc,CAAC,MAAM,EAAE;IACnC,qBAAqB,gBAAgB,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAChF,qBAAqB,KAAK,GAAG,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAC3D,IAAI,KAAK,EAAE;QACP,OAAO,KAAK,CAAC;KAChB;;IAED,qBAAqB,YAAY,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,KAAK,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;IAClC,IAAI,KAAK,EAAE;QACP,OAAO,KAAK,CAAC;KAChB;IACD,IAAI,YAAY,KAAK,IAAI,EAAE;QACvB,OAAO,QAAQ,CAAC;KACnB;IACD,MAAM,IAAI,KAAK,CAAC,uCAAuC,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC;CAC7E;;;;;;;;;;AAUD,AAAO,SAAS,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE;IAC5C,qBAAqB,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACvD,qBAAqB,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,MAAM,KAAK,QAAQ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;QACzD,OAAO,YAAY,CAAC;KACvB;IACD,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;CAC9B;;AC7hBD;;;;;;;;;;;AAWA,AAGA;;;AAGA,AAAO,IAAqB,oBAAoB,GAAG,IAAI,cAAc,CAAC,cAAc,CAAC,CAAC;;;;;AAKtF,IAIA,cAAc,kBAAkB,YAAY;IACxC,SAAS,cAAc,GAAG;KACzB;IACD,OAAO,cAAc,CAAC;CACzB,EAAE,CAAC,CAAC;AACL,AAcA;;;;;;;;;;AAUA,AAAO,SAAS,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE;IACpE,qBAAqB,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC;IACvC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACzB,OAAO,GAAG,CAAC;KACd;IACD,GAAG,GAAG,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACtD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACzB,OAAO,GAAG,CAAC;KACd;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;QAC7B,OAAO,OAAO,CAAC;KAClB;IACD,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;CAC1E;;;;;;AAMD,IAAI,oBAAoB,kBAAkB,UAAU,MAAM,EAAE;IACxDA,SAAiB,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;IAChD,SAAS,oBAAoB,CAAC,MAAM;QAChC,kBAAkB,EAAE;QACpB,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QACtC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACtB,KAAK,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC9C,OAAO,KAAK,CAAC;KAChB;;;;;;IAMD,oBAAoB,CAAC,SAAS,CAAC,iBAAiB;;;;;IAKhD,UAAU,KAAK,EAAE,MAAM,EAAE;QACrB,qBAAqB,MAAM,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;YACzG,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;QACtD,QAAQ,MAAM;YACV,KAAK,MAAM,CAAC,IAAI;gBACZ,OAAO,MAAM,CAAC;YAClB,KAAK,MAAM,CAAC,GAAG;gBACX,OAAO,KAAK,CAAC;YACjB,KAAK,MAAM,CAAC,GAAG;gBACX,OAAO,KAAK,CAAC;YACjB,KAAK,MAAM,CAAC,GAAG;gBACX,OAAO,KAAK,CAAC;YACjB,KAAK,MAAM,CAAC,IAAI;gBACZ,OAAO,MAAM,CAAC;YAClB;gBACI,OAAO,OAAO,CAAC;SACtB;KACJ,CAAC;IACF,oBAAoB,CAAC,UAAU,GAAG;QAC9B,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,oBAAoB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACvD,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;QACxE,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,oBAAoB,EAAE,EAAE,EAAE,EAAE;KAC1G,CAAC,EAAE,CAAC;IACL,OAAO,oBAAoB,CAAC;CAC/B,CAAC,cAAc,CAAC,CAAC,CAAC;AACnB,AAiBA;;;;;;;;;AASA,AAAO,SAAS,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE;;IAEzC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,KAAK,GAAG,QAAQ,mBAAmB,KAAK,GAAG,EAAE,CAAC,CAAC;KAClD;IACD,qBAAqB,CAAC,qBAAqB,KAAK,CAAC,CAAC;IAClD,qBAAqB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IACtE,qBAAqB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,qBAAqB,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;IACzC,qBAAqB,CAAC,GAAG,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAChD,qBAAqB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IACvF,qBAAqB,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAC/D,QAAQ,IAAI;QACR,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACvC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBAChE,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBACjE,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBAC3D,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBAC3E,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBACjE,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;gBACrE,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;gBACrE,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7E,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;oBAC9D,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACnC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;gBAC3B,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;gBACjF,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBACtE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACjC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;oBACvD,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACrC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClD,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;gBACV,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7C,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,KAAK,CAAC;QACX,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;gBACzC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;gBACzC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;gBAC1E,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;gBAC/D,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1C,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC1D,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7D,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACvC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACxC,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;gBACnB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;gBACnB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;gBAChE,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;gBACvB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;gBACvB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC;iBACN,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;gBACvF,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;gBACV,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;gBAC/C,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC1D,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;gBAClC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACjD,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBAC3D,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;gBACV,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBACjF,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBAC5E,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;gBACjF,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;gBAC1B,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;gBACvC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBAC3E,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBACjE,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBACtE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBACpF,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBACtE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBAC5E,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;gBACrD,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;gBACrB,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBAC9E,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;gBAC5C,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBACtE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;gBACvB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;gBACtE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;gBAC5E,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBAClB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACxC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACxC,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;gBACxB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;gBACxB,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;gBACxF,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,KAAK,KAAK;YACN,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;gBACpF,OAAO,MAAM,CAAC,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,KAAK,CAAC;;;;QAIxB;YACI,OAAO,MAAM,CAAC,KAAK,CAAC;KAC3B;CACJ;;ACjgBD;;;;;;;;;;;;;;;;AAgBA,AAAO,SAAS,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE;IAC9C,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAChC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;QAC9D,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QACpB,qBAAqB,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnD,IAAI,EAAE,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACvI,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;YAC5B,OAAO,kBAAkB,CAAC,WAAW,CAAC,CAAC;SAC1C;KACJ;IACD,OAAO,IAAI,CAAC;CACf;;AC3BD;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,IAAI,OAAO,kBAAkB,YAAY;IACrC,SAAS,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE;QACnE,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;KAC7B;IACD,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;QAC9C,GAAG;;;;QAIH,UAAU,CAAC,EAAE;YACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1C,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YACnE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACtC;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE;QAChD,GAAG;;;;QAIH,UAAU,CAAC,EAAE;YACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5D,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAIC,mBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;iBAC9E;qBACI;oBACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;iBAC9E;aACJ;SACJ;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;IAIH,OAAO,CAAC,SAAS,CAAC,SAAS;;;IAG3B,YAAY;QACR,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,qBAAqB,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,mBAAmB,IAAI,CAAC,SAAS,EAAE,CAAC;YACpG,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;aAC/C;SACJ;aACI,IAAI,IAAI,CAAC,eAAe,EAAE;YAC3B,qBAAqB,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,mBAAmB,IAAI,CAAC,SAAS,EAAE,CAAC;YACpG,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;aAC/C;SACJ;KACJ,CAAC;;;;;IAKF,OAAO,CAAC,SAAS,CAAC,qBAAqB;;;;IAIvC,UAAU,OAAO,EAAE;QACf,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,CAAC,gBAAgB,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5G,OAAO,CAAC,kBAAkB,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9G,OAAO,CAAC,kBAAkB,CAAC,UAAU,MAAM,EAAE;YACzC,IAAI,MAAM,CAAC,aAAa,EAAE;gBACtB,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aACzC;SACJ,CAAC,CAAC;KACN,CAAC;;;;;IAKF,OAAO,CAAC,SAAS,CAAC,qBAAqB;;;;IAIvC,UAAU,OAAO,EAAE;QACf,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,CAAC,gBAAgB,CAAC,UAAU,MAAM,EAAE;YACvC,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACzC;iBACI;gBACD,MAAM,IAAI,KAAK,CAAC,gEAAgE,GAAGC,UAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;aAC9G;SACJ,CAAC,CAAC;QACH,OAAO,CAAC,kBAAkB,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;KACpG,CAAC;;;;;;;;;;;IAWF,OAAO,CAAC,SAAS,CAAC,aAAa;;;;;;;;;;IAU/B,UAAU,WAAW,EAAE;QACnB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,WAAW,EAAE;YACb,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,WAAW,YAAY,GAAG,EAAE;gBAC1D,mBAAmB,WAAW,GAAG,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;aAC1G;iBACI;gBACD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aAClH;SACJ;KACJ,CAAC;;;;;;;IAOF,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;IAMhC,UAAU,WAAW,EAAE;QACnB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,WAAW,EAAE;YACb,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,WAAW,YAAY,GAAG,EAAE;gBAC1D,mBAAmB,WAAW,GAAG,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aAC3G;iBACI;gBACD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aACnG;SACJ;KACJ,CAAC;;;;;;IAMF,OAAO,CAAC,SAAS,CAAC,YAAY;;;;;IAK9B,UAAU,KAAK,EAAE,OAAO,EAAE;QACtB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;gBACzC,IAAI,OAAO,EAAE;oBACT,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;iBAC9D;qBACI;oBACD,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;iBACjE;aACJ,CAAC,CAAC;SACN;KACJ,CAAC;IACF,OAAO,CAAC,UAAU,GAAG;QACjB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,EAAE;KAC1D,CAAC;;IAEF,OAAO,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC1C,EAAE,IAAI,EAAE,eAAe,GAAG;QAC1B,EAAE,IAAI,EAAE,eAAe,GAAG;QAC1B,EAAE,IAAI,EAAE,UAAU,GAAG;QACrB,EAAE,IAAI,EAAE,SAAS,GAAG;KACvB,CAAC,EAAE,CAAC;IACL,OAAO,CAAC,cAAc,GAAG;QACrB,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,EAAE;QAC7C,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KAChC,CAAC;IACF,OAAO,OAAO,CAAC;CAClB,EAAE,CAAC;;AC5OJ;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,IAAI,iBAAiB,kBAAkB,YAAY;IAC/C,SAAS,iBAAiB,CAAC,iBAAiB,EAAE;QAC1C,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B;;;;;IAKD,iBAAiB,CAAC,SAAS,CAAC,WAAW;;;;IAIvC,UAAU,OAAO,EAAE;QACf,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,qBAAqB,UAAU,GAAG,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAC1G,IAAI,OAAO,CAAC,kCAAkC,CAAC,EAAE;gBAC7C,IAAI,IAAI,CAAC,UAAU;oBACf,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC9B,IAAI,IAAI,CAAC,gCAAgC,EAAE;oBACvC,qBAAqB,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBAChE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;iBACzF;qBACI;oBACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;iBAC1B;aACJ;YACD,qBAAqB,wBAAwB,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,wBAAwB;gBACtG,UAAU,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAC7C,qBAAqB,gBAAgB,GAAG,wBAAwB,CAAC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACjH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC3J;KACJ,CAAC;;;;IAIF,iBAAiB,CAAC,SAAS,CAAC,WAAW;;;IAGvC,YAAY;QACR,IAAI,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;KACjC,CAAC;IACF,iBAAiB,CAAC,UAAU,GAAG;QAC3B,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,qBAAqB,EAAE,EAAE,EAAE;KACpE,CAAC;;IAEF,iBAAiB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACpD,EAAE,IAAI,EAAE,gBAAgB,GAAG;KAC9B,CAAC,EAAE,CAAC;IACL,iBAAiB,CAAC,cAAc,GAAG;QAC/B,mBAAmB,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QACvC,2BAA2B,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC/C,0BAA0B,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9C,kCAAkC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KACzD,CAAC;IACF,OAAO,iBAAiB,CAAC;CAC5B,EAAE,CAAC;;AC9HJ;;;;;;;;;;;AAWA,AACA;;;;AAIA,IAIA,cAAc,kBAAkB,YAAY;IACxC,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE;QACtD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;IACD,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;QACrD,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;QACxC,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE;QACpD,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;QACrD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE;QACpD,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAC5C,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,KAAK,EAAE;QACnD,GAAG;;;QAGH,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAClC,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,OAAO,cAAc,CAAC;CACzB,EAAE,CAAC,CAAC;AACL,AAeA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,IAAI,OAAO,kBAAkB,YAAY;IACrC,SAAS,OAAO,CAAC,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE;QAClD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;IACD,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE;QACrD,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE;QACvC,GAAG;;;;QAIH,UAAU,EAAE,EAAE;YACV,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;;gBAEvD,qBAAqB,CAAC,OAAO,uBAAuB,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC/D,OAAO,CAAC,IAAI,CAAC,2CAA2C,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI;wBAChF,wHAAwH,CAAC,CAAC;iBACjI;aACJ;YACD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,eAAe,EAAE;QACtD,GAAG;;;;QAIH,UAAU,KAAK,EAAE;;;;YAIb,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;aAC1B;SACJ;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;IAKH,OAAO,CAAC,SAAS,CAAC,WAAW;;;;IAI7B,UAAU,OAAO,EAAE;QACf,IAAI,SAAS,IAAI,OAAO,EAAE;;YAEtB,qBAAqB,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;gBACxB,IAAI;oBACA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBACtE;gBACD,wBAAwB,CAAC,EAAE;oBACvB,MAAM,IAAI,KAAK,CAAC,0CAA0C,GAAG,KAAK,GAAG,aAAa,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,6DAA6D,CAAC,CAAC;iBACxL;aACJ;SACJ;KACJ,CAAC;;;;IAIF,OAAO,CAAC,SAAS,CAAC,SAAS;;;IAG3B,YAAY;QACR,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,qBAAqB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/D,IAAI,OAAO;gBACP,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SACnC;KACJ,CAAC;;;;;IAKF,OAAO,CAAC,SAAS,CAAC,aAAa;;;;IAI/B,UAAU,OAAO,EAAE;QACf,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,YAAY,GAAG,EAAE,CAAC;QACvC,OAAO,CAAC,gBAAgB,CAAC,UAAU,IAAI,EAAE,qBAAqB,EAAE,YAAY,EAAE;YAC1E,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;gBAC5B,qBAAqB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,kBAAkB,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,cAAc,oBAAoB,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;gBACzK,qBAAqB,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC7D,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC5B;iBACI,IAAI,YAAY,IAAI,IAAI,EAAE;gBAC3B,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;aACtD;iBACI;gBACD,qBAAqB,IAAI,sBAAsB,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBACjG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;gBAC9C,qBAAqB,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,oBAAoB,IAAI,EAAE,CAAC;gBAChF,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC5B;SACJ,CAAC,CAAC;QACH,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3D,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;SACrE;QACD,KAAK,qBAAqB,CAAC,GAAG,CAAC,mBAAmB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAChG,qBAAqB,OAAO,qBAAqB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7E,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;YAC1B,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;SAChC;QACD,OAAO,CAAC,qBAAqB,CAAC,UAAU,MAAM,EAAE;YAC5C,qBAAqB,OAAO,qBAAqB,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;YAChG,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC;SAC3C,CAAC,CAAC;KACN,CAAC;;;;;;IAMF,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;IAKhC,UAAU,IAAI,EAAE,MAAM,EAAE;QACpB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC;KACxC,CAAC;IACF,OAAO,CAAC,UAAU,GAAG;QACjB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,kBAAkB,EAAE,EAAE,EAAE;KACjE,CAAC;;IAEF,OAAO,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC1C,EAAE,IAAI,EAAE,gBAAgB,GAAG;QAC3B,EAAE,IAAI,EAAE,WAAW,GAAG;QACtB,EAAE,IAAI,EAAE,eAAe,GAAG;KAC7B,CAAC,EAAE,CAAC;IACL,OAAO,CAAC,cAAc,GAAG;QACrB,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7B,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAClC,eAAe,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KACtC,CAAC;IACF,OAAO,OAAO,CAAC;CAClB,EAAE,CAAC,CAAC;AACL,AAwBA;;;AAGA,IAGA,eAAe,kBAAkB,YAAY;IACzC,SAAS,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;IACD,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC,CAAC;AACL,AAMA;;;;AAIA,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE;IAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,CAAC;CACtC;;ACzVD;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA,IAAI,IAAI,kBAAkB,YAAY;IAClC,SAAS,IAAI,CAAC,cAAc,EAAE,WAAW,EAAE;QACvC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;KACvC;IACD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE;QAC1C,GAAG;;;;QAIH,UAAU,SAAS,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC;YACzD,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE;QAC9C,GAAG;;;;QAIH,UAAU,WAAW,EAAE;YACnB,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE;QAC9C,GAAG;;;;QAIH,UAAU,WAAW,EAAE;YACnB,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;IAIH,IAAI,CAAC,SAAS,CAAC,WAAW;;;IAG1B,YAAY;QACR,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,YAAY;wBACb,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACpF;aACJ;SACJ;aACI;YACD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,YAAY;wBACb,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACpF;aACJ;SACJ;KACJ,CAAC;IACF,IAAI,CAAC,UAAU,GAAG;QACd,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE;KACvD,CAAC;;IAEF,IAAI,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACvC,EAAE,IAAI,EAAE,gBAAgB,GAAG;QAC3B,EAAE,IAAI,EAAE,WAAW,GAAG;KACzB,CAAC,EAAE,CAAC;IACL,IAAI,CAAC,cAAc,GAAG;QAClB,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC1B,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9B,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KACjC,CAAC;IACF,OAAO,IAAI,CAAC;CACf,EAAE,CAAC,CAAC;AACL,AA6BA;;;AAGA,IAGA,WAAW,kBAAkB,YAAY;IACrC,SAAS,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;IACD,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC;;ACzOJ;;;;;;;;;;;AAWA,AACA,IAAI,UAAU,kBAAkB,YAAY;IACxC,SAAS,UAAU,CAAC,iBAAiB,EAAE,YAAY,EAAE;QACjD,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;KACzB;;;;IAID,UAAU,CAAC,SAAS,CAAC,MAAM;;;IAG3B,YAAY;QACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAChE,CAAC;;;;IAIF,UAAU,CAAC,SAAS,CAAC,OAAO;;;IAG5B,YAAY;QACR,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;KAClC,CAAC;;;;;IAKF,UAAU,CAAC,SAAS,CAAC,YAAY;;;;IAIjC,UAAU,OAAO,EAAE;QACf,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;aACI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;KACJ,CAAC;IACF,OAAO,UAAU,CAAC;CACrB,EAAE,CAAC,CAAC;AACL,AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,IAAI,QAAQ,kBAAkB,YAAY;IACtC,SAAS,QAAQ,GAAG;QAChB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;KAClC;IACD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,EAAE;QAClD,GAAG;;;;QAIH,UAAU,QAAQ,EAAE;YAChB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;gBACvB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;aAClC;SACJ;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;;IAMH,QAAQ,CAAC,SAAS,CAAC,QAAQ;;;;IAI3B,YAAY,EAAE,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;;;;;;;IAO1C,QAAQ,CAAC,SAAS,CAAC,WAAW;;;;;IAK9B,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACjC,CAAC;;;;;;;IAOF,QAAQ,CAAC,SAAS,CAAC,UAAU;;;;;IAK7B,UAAU,KAAK,EAAE;QACb,qBAAqB,OAAO,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC;QACvD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC;QAC3D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,UAAU,EAAE;YAC9C,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClD,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;SAClC;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;;;;;IAKF,QAAQ,CAAC,SAAS,CAAC,mBAAmB;;;;IAItC,UAAU,UAAU,EAAE;QAClB,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,KAAK,IAAI,CAAC,YAAY,EAAE;YACxD,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;YAC/B,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjE,qBAAqB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACzD,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACxC;SACJ;KACJ,CAAC;IACF,QAAQ,CAAC,UAAU,GAAG;QAClB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,EAAE;KAC3D,CAAC;;IAEF,QAAQ,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACrD,QAAQ,CAAC,cAAc,GAAG;QACtB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KACjC,CAAC;IACF,OAAO,QAAQ,CAAC;CACnB,EAAE,CAAC,CAAC;AACL,AAwBA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,YAAY,kBAAkB,YAAY;IAC1C,SAAS,YAAY,CAAC,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE;QACxD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;KAC3D;;;;IAID,YAAY,CAAC,SAAS,CAAC,SAAS;;;IAGhC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;IACtF,YAAY,CAAC,UAAU,GAAG;QACtB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,EAAE,EAAE;KAC/D,CAAC;;IAEF,YAAY,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC/C,EAAE,IAAI,EAAE,gBAAgB,GAAG;QAC3B,EAAE,IAAI,EAAE,WAAW,GAAG;QACtB,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;KACpD,CAAC,EAAE,CAAC;IACL,YAAY,CAAC,cAAc,GAAG;QAC1B,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KACrC,CAAC;IACF,OAAO,YAAY,CAAC;CACvB,EAAE,CAAC,CAAC;AACL,AAkBA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAI,eAAe,kBAAkB,YAAY;IAC7C,SAAS,eAAe,CAAC,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE;QAC3D,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;KACpE;IACD,eAAe,CAAC,UAAU,GAAG;QACzB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,mBAAmB,EAAE,EAAE,EAAE;KAClE,CAAC;;IAEF,eAAe,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAClD,EAAE,IAAI,EAAE,gBAAgB,GAAG;QAC3B,EAAE,IAAI,EAAE,WAAW,GAAG;QACtB,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;KACpD,CAAC,EAAE,CAAC;IACL,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC;;AC7UJ;;;;;;;;;;;AAWA,AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,IAAI,QAAQ,kBAAkB,YAAY;IACtC,SAAS,QAAQ,CAAC,aAAa,EAAE;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACxB;IACD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,EAAE;QAClD,GAAG;;;;QAIH,UAAU,KAAK,EAAE;YACb,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;;IAMH,QAAQ,CAAC,SAAS,CAAC,OAAO;;;;;IAK1B,UAAU,KAAK,EAAE,UAAU,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC;;;;IAItE,QAAQ,CAAC,SAAS,CAAC,WAAW;;;IAG9B,YAAY;QACR,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,qBAAqB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,qBAAqB,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3F,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5C,CAAC;;;;IAIF,QAAQ,CAAC,SAAS,CAAC,WAAW;;;IAG9B,YAAY;QACR,IAAI,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;KAClC,CAAC;;;;;IAKF,QAAQ,CAAC,SAAS,CAAC,aAAa;;;;IAIhC,UAAU,IAAI,EAAE;QACZ,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;SAC7B;KACJ,CAAC;IACF,QAAQ,CAAC,UAAU,GAAG;QAClB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,EAAE;KAC3D,CAAC;;IAEF,QAAQ,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC3C,EAAE,IAAI,EAAE,cAAc,GAAG;KAC5B,CAAC,EAAE,CAAC;IACL,QAAQ,CAAC,cAAc,GAAG;QACtB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KACjC,CAAC;IACF,OAAO,QAAQ,CAAC;CACnB,EAAE,CAAC,CAAC;AACL,AAoBA;;;;;;;;;;;;;;;;;;AAkBA,IAAI,YAAY,kBAAkB,YAAY;IAC1C,SAAS,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE;QAC5D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,qBAAqB,SAAS,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACvD,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC9F;IACD,YAAY,CAAC,UAAU,GAAG;QACtB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,EAAE,EAAE;KAC/D,CAAC;;IAEF,YAAY,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC/C,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE,EAAE,EAAE;QAChF,EAAE,IAAI,EAAE,WAAW,GAAG;QACtB,EAAE,IAAI,EAAE,gBAAgB,GAAG;QAC3B,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;KACpD,CAAC,EAAE,CAAC;IACL,OAAO,YAAY,CAAC;CACvB,EAAE,CAAC;;AChLJ;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAI,OAAO,kBAAkB,YAAY;IACrC,SAAS,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC9B;IACD,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE;QAChD,GAAG;;;;QAIH,UAAU,CAAC,EAAE;YACT,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE;gBACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;aACjD;SACJ;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;IAIH,OAAO,CAAC,SAAS,CAAC,SAAS;;;IAG3B,YAAY;QACR,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,qBAAqB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChE,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;aAC/B;SACJ;KACJ,CAAC;;;;;IAKF,OAAO,CAAC,SAAS,CAAC,aAAa;;;;IAI/B,UAAU,OAAO,EAAE;QACf,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,CAAC,kBAAkB,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5F,OAAO,CAAC,gBAAgB,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;QACzG,OAAO,CAAC,kBAAkB,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;KAC9G,CAAC;;;;;;IAMF,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;IAK3B,UAAU,WAAW,EAAE,KAAK,EAAE;QAC1B,IAAI,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5D,KAAK,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;QAC1D,IAAI,KAAK,IAAI,IAAI,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,oBAAoB,KAAK,EAAE,CAAC;SACrF;aACI;YACD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SAC9D;KACJ,CAAC;IACF,OAAO,CAAC,UAAU,GAAG;QACjB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,EAAE;KAC1D,CAAC;;IAEF,OAAO,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC1C,EAAE,IAAI,EAAE,eAAe,GAAG;QAC1B,EAAE,IAAI,EAAE,UAAU,GAAG;QACrB,EAAE,IAAI,EAAE,SAAS,GAAG;KACvB,CAAC,EAAE,CAAC;IACL,OAAO,CAAC,cAAc,GAAG;QACrB,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KAChC,CAAC;IACF,OAAO,OAAO,CAAC;CAClB,EAAE,CAAC;;ACnHJ;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,gBAAgB,kBAAkB,YAAY;IAC9C,SAAS,gBAAgB,CAAC,iBAAiB,EAAE;QACzC,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;;;;;IAKD,gBAAgB,CAAC,SAAS,CAAC,WAAW;;;;IAItC,UAAU,OAAO,EAAE;QACf,qBAAqB,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACtE,IAAI,YAAY,EAAE;YACd,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aAChF;YACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;aAClH;SACJ;aACI;YACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC/C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;aAC7D;SACJ;KACJ,CAAC;;;;;;;;;;;;;IAaF,gBAAgB,CAAC,SAAS,CAAC,mBAAmB;;;;;;;;;;;;IAY9C,UAAU,OAAO,EAAE;QACf,qBAAqB,SAAS,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACpE,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;KAClG,CAAC;;;;;IAKF,gBAAgB,CAAC,SAAS,CAAC,uBAAuB;;;;IAIlD,UAAU,SAAS,EAAE;QACjB,qBAAqB,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;QAC9E,qBAAqB,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;QAC7E,IAAI,WAAW,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;YAC3C,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,aAAa,GAAG,WAAW,EAAE,EAAE,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;gBAC3E,IAAI,QAAQ,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;gBACjC,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;oBACtC,OAAO,IAAI,CAAC;iBACf;aACJ;YACD,OAAO,KAAK,CAAC;SAChB;aACI;YACD,OAAO,IAAI,CAAC;SACf;KACJ,CAAC;;;;;IAKF,gBAAgB,CAAC,SAAS,CAAC,sBAAsB;;;;IAIjD,UAAU,GAAG,EAAE;QACX,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YAC1D,IAAI,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;YACtB,mBAAmB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,mBAAmB,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC,CAAC;SACtH;KACJ,CAAC;IACF,gBAAgB,CAAC,UAAU,GAAG;QAC1B,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,EAAE,EAAE;KACnE,CAAC;;IAEF,gBAAgB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACnD,EAAE,IAAI,EAAE,gBAAgB,GAAG;KAC9B,CAAC,EAAE,CAAC;IACL,gBAAgB,CAAC,cAAc,GAAG;QAC9B,yBAAyB,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7C,kBAAkB,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;KACzC,CAAC;IACF,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC;;AC9IJ;;;;;;;;;;;AAWA,AASA;;;;AAIA,AAAO,IAAqB,iBAAiB,GAAG;IAC5C,OAAO;IACP,iBAAiB;IACjB,OAAO;IACP,IAAI;IACJ,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,eAAe;IACf,QAAQ;IACR,YAAY;CACf;;ACpCD;;;;;;;;;;;AAWA,AACA,IAAqB,aAAa,GAAG,EAAE,CAAC;AACxC,IAAqB,kBAAkB,GAAG,mMAAmM,CAAC;;AAE9O,IAAI,SAAS,GAAG;IACZ,KAAK,EAAE,CAAC;IACR,QAAQ,EAAE,CAAC;IACX,IAAI,EAAE,CAAC;IACP,QAAQ,EAAE,CAAC;CACd,CAAC;AACF,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AACrC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AAC3C,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACnC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;;AAE3C,IAAI,QAAQ,GAAG;IACX,QAAQ,EAAE,CAAC;IACX,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;IACV,OAAO,EAAE,CAAC;IACV,YAAY,EAAE,CAAC;IACf,GAAG,EAAE,CAAC;CACT,CAAC;AACF,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AACzC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AACnC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACjC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AACnC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACvC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACvC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;AACjD,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;;AAE/B,IAAI,eAAe,GAAG;IAClB,UAAU,EAAE,CAAC;IACb,IAAI,EAAE,CAAC;IACP,MAAM,EAAE,CAAC;IACT,IAAI,EAAE,CAAC;CACV,CAAC;AACF,eAAe,CAAC,eAAe,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;AAC3D,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC/C,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACnD,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;;;;;;;;;;;AAW/C,AAAO,SAAS,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;IACvD,qBAAqB,WAAW,GAAG,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClE,MAAM,GAAG,WAAW,IAAI,MAAM,CAAC;IAC/B,qBAAqB,KAAK,GAAG,EAAE,CAAC;IAChC,qBAAqB,KAAK,CAAC;IAC3B,OAAO,MAAM,EAAE;QACX,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,KAAK,EAAE;YACP,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,qBAAqB,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YACxC,IAAI,CAAC,IAAI,EAAE;gBACP,MAAM;aACT;YACD,MAAM,GAAG,IAAI,CAAC;SACjB;aACI;YACD,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnB,MAAM;SACT;KACJ;IACD,qBAAqB,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACnE,IAAI,QAAQ,EAAE;QACV,kBAAkB,GAAG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;QACpE,IAAI,GAAG,sBAAsB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;KACvD;IACD,qBAAqB,IAAI,GAAG,EAAE,CAAC;IAC/B,KAAK,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;QAC3B,qBAAqB,aAAa,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,IAAI,aAAa;YACjB,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB,CAAC;YAC/C,KAAK,KAAK,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KACpF,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;CACf;;;;;;AAMD,SAAS,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE;IACpC,qBAAqB,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IACpD,aAAa,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACxD,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;KAC1C;IACD,qBAAqB,WAAW,GAAG,EAAE,CAAC;IACtC,QAAQ,MAAM;QACV,KAAK,WAAW;YACZ,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM;QACV,KAAK,YAAY;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM;QACV,KAAK,UAAU;YACX,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;QACV,KAAK,UAAU;YACX,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;QACV,KAAK,WAAW;YACZ,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM;QACV,KAAK,YAAY;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM;QACV,KAAK,UAAU;YACX,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;QACV,KAAK,UAAU;YACX,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM;QACV,KAAK,OAAO;YACR,qBAAqB,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACrE,qBAAqB,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACrE,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;YACzG,MAAM;QACV,KAAK,QAAQ;YACT,qBAAqB,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YACvE,qBAAqB,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YACvE,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;YAC5G,MAAM;QACV,KAAK,MAAM;YACP,qBAAqB,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACnE,qBAAqB,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACnE,WAAW;gBACP,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC5F,MAAM;QACV,KAAK,MAAM;YACP,qBAAqB,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACnE,qBAAqB,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACnE,WAAW;gBACP,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC5F,MAAM;KACb;IACD,IAAI,WAAW,EAAE;QACb,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC;KACjD;IACD,OAAO,WAAW,CAAC;CACtB;;;;;;AAMD,SAAS,cAAc,CAAC,GAAG,EAAE,UAAU,EAAE;IACrC,IAAI,UAAU,EAAE;QACZ,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,KAAK,EAAE,GAAG,EAAE;YACnD,OAAO,CAAC,UAAU,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAC9E,CAAC,CAAC;KACN;IACD,OAAO,GAAG,CAAC;CACd;;;;;;;;;AASD,SAAS,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE;IACtD,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE;IAC9C,qBAAqB,GAAG,GAAG,EAAE,CAAC;IAC9B,IAAI,GAAG,GAAG,CAAC,KAAK,OAAO,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE;QAClC,IAAI,OAAO,EAAE;YACT,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;SAClB;aACI;YACD,GAAG,GAAG,CAAC,GAAG,CAAC;YACX,GAAG,GAAG,SAAS,CAAC;SACnB;KACJ;IACD,qBAAqB,MAAM,GAAG,EAAE,GAAG,GAAG,CAAC;IACvC,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM;QACzB,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;IAC1B,IAAI,IAAI,EAAE;QACN,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;KAClD;IACD,OAAO,GAAG,GAAG,MAAM,CAAC;CACvB;;;;;;;;;;AAUD,SAAS,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;IACnD,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE;IACtC,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,KAAK,CAAC,EAAE;IAC5C,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE;QAC3B,qBAAqB,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;YAC9B,IAAI,IAAI,MAAM,CAAC;SAClB;QACD,IAAI,IAAI,KAAK,QAAQ,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE;YACzD,IAAI,GAAG,EAAE,CAAC;SACb;QACD,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KACtG,CAAC;CACL;;;;;;;AAOD,SAAS,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;IACnC,QAAQ,IAAI;QACR,KAAK,QAAQ,CAAC,QAAQ;YAClB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;QAC9B,KAAK,QAAQ,CAAC,KAAK;YACf,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,KAAK,QAAQ,CAAC,IAAI;YACd,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,KAAK,QAAQ,CAAC,KAAK;YACf,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,KAAK,QAAQ,CAAC,OAAO;YACjB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;QAC7B,KAAK,QAAQ,CAAC,OAAO;YACjB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;QAC7B,KAAK,QAAQ,CAAC,YAAY;YACtB,qBAAqB,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,GAAG,CAAC,CAAC;QACpD,KAAK,QAAQ,CAAC,GAAG;YACb,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACzB;YACI,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;KACnE;CACJ;;;;;;;;;AASD,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;IAChD,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,EAAE;IACjD,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG,KAAK,CAAC,EAAE;IAC9C,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE;QAC3B,OAAO,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;KACxE,CAAC;CACL;;;;;;;;;;;AAWD,SAAS,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;IACnE,QAAQ,IAAI;QACR,KAAK,eAAe,CAAC,MAAM;YACvB,OAAO,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrE,KAAK,eAAe,CAAC,IAAI;YACrB,OAAO,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACjE,KAAK,eAAe,CAAC,UAAU;YAC3B,qBAAqB,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtD,qBAAqB,gBAAgB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1D,IAAI,QAAQ,EAAE;gBACV,qBAAqB,KAAK,GAAG,4BAA4B,CAAC,MAAM,CAAC,CAAC;gBAClE,qBAAqB,YAAY,GAAG,wBAAwB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBAClF,qBAAqB,QAAQ,CAAC;gBAC9B,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,KAAK,EAAE;oBACjC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;wBAErB,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,EAAE,CAAC,KAAK,EAAE,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC;wBACjE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;wBAC7D,IAAI,cAAc,IAAI,SAAS,IAAI,gBAAgB,IAAI,WAAW;6BAC7D,cAAc,GAAG,OAAO;iCACpB,cAAc,KAAK,OAAO,IAAI,gBAAgB,GAAG,SAAS,CAAC,CAAC,EAAE;4BACnE,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;yBAClC;qBACJ;yBACI;;wBAED,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC/C,IAAI,KAAK,KAAK,cAAc,IAAI,OAAO,KAAK,gBAAgB,EAAE;4BAC1D,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;yBAClC;qBACJ;iBACJ,CAAC,CAAC;gBACH,IAAI,QAAQ,EAAE;oBACV,OAAO,QAAQ,CAAC;iBACnB;aACJ;;YAED,OAAO,mBAAmB,CAAC,MAAM,EAAE,IAAI,oBAAoB,KAAK,EAAE,CAAC,cAAc,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACpG,KAAK,eAAe,CAAC,IAAI;YACrB,OAAO,iBAAiB,CAAC,MAAM,oBAAoB,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAChG;;;;;YAKI,qBAAqB,UAAU,GAAG,IAAI,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,8BAA8B,GAAG,UAAU,CAAC,CAAC;KACpE;CACJ;;;;;;;;AAQD,SAAS,cAAc,CAAC,KAAK,EAAE;IAC3B,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE;QACnC,qBAAqB,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QACxC,qBAAqB,SAAS,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;QACvF,qBAAqB,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACrF,QAAQ,KAAK;YACT,KAAK,SAAS,CAAC,KAAK;gBAChB,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;oBAC5D,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;YACrD,KAAK,SAAS,CAAC,QAAQ;gBACnB,OAAO,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;YAC7E,KAAK,SAAS,CAAC,IAAI;gBACf,OAAO,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG;oBAC1E,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;YACrD,KAAK,SAAS,CAAC,QAAQ;gBACnB,IAAI,MAAM,KAAK,CAAC,EAAE;oBACd,OAAO,GAAG,CAAC;iBACd;qBACI;oBACD,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG;wBAClE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;iBACpD;YACL;gBACI,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;SAC/D;KACJ,CAAC;CACL;AACD,IAAqB,OAAO,GAAG,CAAC,CAAC;AACjC,IAAqB,QAAQ,GAAG,CAAC,CAAC;;;;;AAKlC,SAAS,sBAAsB,CAAC,IAAI,EAAE;IAClC,qBAAqB,cAAc,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC;IAC5E,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,QAAQ,IAAI,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC;CAC3G;;;;;AAKD,SAAS,mBAAmB,CAAC,QAAQ,EAAE;IACnC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,OAAO,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;CACrH;;;;;;AAMD,SAAS,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE;IAClC,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE;IAClD,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE;QAC3B,qBAAqB,MAAM,CAAC;QAC5B,IAAI,UAAU,EAAE;YACZ,qBAAqB,yBAAyB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAC/G,qBAAqB,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5C,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,yBAAyB,IAAI,CAAC,CAAC,CAAC;SACpE;aACI;YACD,qBAAqB,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAC7E,qBAAqB,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC3D,qBAAqB,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;YACvE,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;SAC3C;QACD,OAAO,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;KACzF,CAAC;CACL;AACD,IAAqB,YAAY,GAAG,EAAE,CAAC;;;;;AAKvC,SAAS,gBAAgB,CAAC,MAAM,EAAE;IAC9B,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;QACtB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;KAC/B;IACD,qBAAqB,SAAS,CAAC;IAC/B,QAAQ,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM;QACV,KAAK,MAAM;YACP,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACvE,MAAM;QACV,KAAK,OAAO;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7D,MAAM;;QAEV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5D,MAAM;;QAEV,KAAK,KAAK;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7D,MAAM;;QAEV,KAAK,MAAM;YACP,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7D,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7C,MAAM;QACV,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7C,MAAM;;QAEV,KAAK,KAAK;YACN,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAChF,MAAM;QACV,KAAK,MAAM;YACP,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzE,MAAM;QACV,KAAK,OAAO;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC3E,MAAM;;QAEV,KAAK,KAAK;YACN,SAAS;gBACL,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;YAC9F,MAAM;QACV,KAAK,MAAM;YACP,SAAS;gBACL,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;YACvF,MAAM;QACV,KAAK,OAAO;YACR,SAAS;gBACL,aAAa,CAAC,eAAe,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;YACzF,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM;QACV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAChC,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,MAAM;QACV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM;QACV,KAAK,MAAM;YACP,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACvE,MAAM;QACV,KAAK,OAAO;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzE,MAAM;QACV,KAAK,QAAQ;YACT,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxE,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACpF,MAAM;QACV,KAAK,MAAM;YACP,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM;QACV,KAAK,OAAO;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC/E,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAChH,MAAM;QACV,KAAK,MAAM;YACP,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACzG,MAAM;QACV,KAAK,OAAO;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAC3G,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC5G,MAAM;QACV,KAAK,MAAM;YACP,SAAS;gBACL,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC7F,MAAM;QACV,KAAK,OAAO;YACR,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,UAAU,EAAE,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvG,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM;QACV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC1C,MAAM;;QAEV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC1C,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;QACV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;QACV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM;;QAEV,KAAK,GAAG;YACJ,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YACjD,MAAM;QACV,KAAK,IAAI;YACL,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YACjD,MAAM;;QAEV,KAAK,KAAK;YACN,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YACjD,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM;;QAEV,KAAK,OAAO;YACR,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM;;QAEV,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;;QAEX,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,KAAK;YACN,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM;;QAEV,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM,CAAC;;QAEZ,KAAK,MAAM;YACP,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM;QACV;YACI,OAAO,IAAI,CAAC;KACnB;IACD,YAAY,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;IACjC,OAAO,SAAS,CAAC;CACpB;;;;;;AAMD,SAAS,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE;;;IAG1C,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACtC,qBAAqB,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC;IACvG,OAAO,KAAK,CAAC,uBAAuB,CAAC,GAAG,QAAQ,GAAG,uBAAuB,CAAC;CAC9E;;;;;;AAMD,SAAS,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;IACnC,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,CAAC;IAC7C,OAAO,IAAI,CAAC;CACf;;;;;;;AAOD,SAAS,sBAAsB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE;IACrD,qBAAqB,YAAY,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACrD,qBAAqB,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACnE,qBAAqB,cAAc,GAAG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;IACrF,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY,IAAI,cAAc,GAAG,kBAAkB,CAAC,CAAC,CAAC;CACrF;;AC7oBD;;;;;;;;;;;AAWA,AACA;;;;;AAKA,AAAO,SAAS,wBAAwB,CAAC,IAAI,EAAE,KAAK,EAAE;IAClD,OAAO,KAAK,CAAC,wBAAwB,GAAG,KAAK,GAAG,cAAc,GAAGA,UAAS,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;CAC3F;;ACnBD;;;;;;;;;;;AAWA,AAGO,IAAqB,kBAAkB,GAAG,sGAAsG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHxJ,IAAI,QAAQ,kBAAkB,YAAY;IACtC,SAAS,QAAQ,CAAC,MAAM,EAAE;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;;;;;;;;IAQD,QAAQ,CAAC,SAAS,CAAC,SAAS;;;;;;;IAO5B,UAAU,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE;QACvC,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,YAAY,CAAC,EAAE;QACjD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;YAChD,OAAO,IAAI,CAAC;QAChB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;SACxB;QACD,qBAAqB,IAAI,CAAC;QAC1B,qBAAqB,KAAK,CAAC;QAC3B,IAAIC,QAAM,CAAC,KAAK,CAAC,EAAE;YACf,IAAI,GAAG,KAAK,CAAC;SAChB;aACI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;YACxC,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SACtC;aACI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;YAU3E,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAChG,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;SAChC;aACI,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAE;YAC/E,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;SACjC;aACI;YACD,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;QACD,IAAI,CAACA,QAAM,CAAC,IAAI,CAAC,EAAE;YACf,MAAM,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SACnD;QACD,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACpE,CAAC;IACF,QAAQ,CAAC,UAAU,GAAG;QAClB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;KACxD,CAAC;;IAEF,QAAQ,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC3C,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,QAAQ,CAAC;CACnB,EAAE,CAAC,CAAC;AACL,AAYA;;;;;AAKA,AAAO,SAAS,eAAe,CAAC,KAAK,EAAE;IACnC,qBAAqB,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;IACxC,qBAAqB,MAAM,GAAG,CAAC,CAAC;IAChC,qBAAqB,KAAK,GAAG,CAAC,CAAC;;IAE/B,qBAAqB,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;IACpF,qBAAqB,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;;IAE9E,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;QACV,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;KACnC;IACD,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC;IACrD,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;IACpD,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;IAC5C,qBAAqB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAChF,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACnC,OAAO,IAAI,CAAC;CACf;;;;;AAKD,SAASA,QAAM,CAAC,KAAK,EAAE;IACnB,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;CAC3D;;AC9OD;;;;AAIA,AAEA,IAAI,eAAe,kBAAkB,YAAY;IAC7C,SAAS,eAAe,GAAG;KAC1B;;;;;;;;IAQD,eAAe,CAAC,MAAM;;;;;;;IAOtB,UAAU,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;QAChC,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE;QACnC,IAAI,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,EAAE,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,EAAE,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;QAClQ,qBAAqB,OAAO,GAAG;YAC3B,oBAAoB,EAAE,oBAAoB;YAC1C,qBAAqB,EAAE,qBAAqB;YAC5C,qBAAqB,EAAE,qBAAqB;YAC5C,KAAK,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE;SAChD,CAAC;QACF,IAAI,KAAK,IAAI,iBAAiB,CAAC,QAAQ,EAAE;YACrC,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ,IAAI,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;YACtE,OAAO,CAAC,eAAe,GAAG,gBAAgB,GAAG,QAAQ,GAAG,MAAM,CAAC;SAClE;QACD,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC7D,CAAC;IACF,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC,CAAC;AACL,AACA,IAAqBC,oBAAkB,GAAG,qGAAqG,CAAC;AAChJ,IAAqB,eAAe,GAAG;;IAEnC,UAAU,EAAE,qBAAqB,CAAC,OAAO,CAAC;QACtC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QACzB,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;QACzB,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;QACxB,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QACzB,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3B,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC9B,CAAC,CAAC;IACH,OAAO,EAAE,qBAAqB,CAAC,OAAO,CAAC;QACnC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;QAC/E,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;KACzD,CAAC,CAAC;IACH,YAAY,EAAE,qBAAqB,CAAC,OAAO,CAAC;QACxC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC;QACjF,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;KAC3B,CAAC,CAAC;IACH,QAAQ,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1H,OAAO,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACzH,KAAK,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxH,KAAK,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5H,IAAI,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CACjG,CAAC;AACF,IAAqBC,cAAY,GAAG;;IAEhC,MAAM,EAAE,qBAAqB,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACxD,IAAI,EAAE,qBAAqB,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACtD,GAAG,EAAE,qBAAqB,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACrD,MAAM,EAAE,qBAAqB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACxD,KAAK,EAAE,qBAAqB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvD,IAAI,EAAE,qBAAqB,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvD,GAAG,EAAE,qBAAqB,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACtD,MAAM,EAAE,qBAAqB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACxD,GAAG,EAAE,qBAAqB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACrD,IAAI,EAAE,qBAAqB,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACrD,GAAG,EAAE,qBAAqB,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpD,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC,qBAAqB,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACzG,GAAG,EAAE,aAAa,CAAC,qBAAqB,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACzF,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC,qBAAqB,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACxG,GAAG,EAAE,aAAa,CAAC,qBAAqB,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACxF,IAAI,EAAE,qBAAqB,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACtD,GAAG,EAAE,qBAAqB,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACrD,IAAI,EAAE,aAAa,CAAC,qBAAqB,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;IACvE,GAAG,EAAE,qBAAqB,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACvD,IAAI,EAAE,aAAa,CAAC,qBAAqB,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;IACvE,GAAG,EAAE,qBAAqB,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;;;IAIvD,KAAK,EAAE,qBAAqB,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzD,MAAM,EAAE,qBAAqB,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IAC1D,KAAK,EAAE,qBAAqB,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACzD,IAAI,EAAE,qBAAqB,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACxD,GAAG,EAAE,qBAAqB,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACvD,GAAG,EAAE,kBAAkB,CAAC,qBAAqB,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7F,GAAG,EAAEC,gBAAc,CAAC,OAAO,CAAC;IAC5B,GAAG,EAAEA,gBAAc,CAAC,MAAM,CAAC;IAC3B,IAAI,EAAE,qBAAqB,CAAC,EAAE,CAAC;;;IAG/B,GAAG,EAAE,qBAAqB,CAAC,EAAE,CAAC;;;IAG9B,GAAG,EAAE,qBAAqB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACnD,IAAI,EAAE,qBAAqB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpD,KAAK,EAAE,qBAAqB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACrD,MAAM,EAAE,qBAAqB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CACzD,CAAC;;;;;AAKF,SAAS,aAAa,CAAC,KAAK,EAAE;IAC1B,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE;QAC3B,qBAAqB,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;KACrD,CAAC;CACL;;;;;AAKD,SAAS,kBAAkB,CAAC,KAAK,EAAE;IAC/B,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;CAChF;;;;;AAKD,SAAS,aAAa,CAAC,KAAK,EAAE;IAC1B,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;CAChF;;;;;;;AAOD,SAAS,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;IAC3C,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;CAC/F;;;;;AAKD,SAASA,gBAAc,CAAC,QAAQ,EAAE;;IAE9B,qBAAqB,OAAO,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;IAC1F,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE;QAC3B,qBAAqB,MAAM,GAAG,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;;QAEpE,OAAO,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;KAC5C,CAAC;CACL;;;;;;AAMD,SAAS,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE;IAClC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;IACvB,OAAO,OAAO,CAAC;CAClB;;;;;;AAMD,SAAS,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE;IAC/B,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;IACjD,OAAO,MAAM,CAAC;CACjB;;;;;;AAMD,SAAS,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE;IAC9B,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,IAAI,GAAG,GAAG,CAAC,EAAE;QACT,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC;KAC/C;SACI;QACD,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;KACzB;IACD,OAAO,MAAM,CAAC;CACjB;;;;;AAKD,SAAS,OAAO,CAAC,OAAO,EAAE;IACtB,OAAO,OAAO,CAAC,MAAM,CAAC,UAAU,MAAM,EAAE,GAAG,EAAE,EAAE,QAAQC,QAAgB,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;CACrG;;;;;AAKD,SAAS,qBAAqB,CAAC,GAAG,EAAE;IAChC,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;CAChF;AACD,IAAqB,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;;;;;;;AAOtD,SAAS,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;IACzC,qBAAqB,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IAClD,IAAI,EAAE;QACF,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC5B,qBAAqB,QAAQ,GAAG,MAAM,CAAC;IACvC,qBAAqB,KAAK,GAAG,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAChE,IAAI,CAAC,KAAK,EAAE;QACR,KAAK,GAAG,EAAE,CAAC;QACX,qBAAqB,KAAK,GAAG,KAAK,CAAC,CAAC;QACpCH,oBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,qBAAqB,OAAO,GAAG,MAAM,CAAC;QACtC,OAAO,OAAO,EAAE;YACZ,KAAK,GAAGA,oBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzC,IAAI,KAAK,EAAE;gBACP,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,OAAO,sBAAsB,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC;aAC9C;iBACI;gBACD,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC;aAClB;SACJ;QACD,oBAAoB,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC7C;IACD,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,IAAI,EAAE;QACtC,qBAAqB,EAAE,GAAGC,cAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,OAAO,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;KAC5D,EAAE,EAAE,CAAC,CAAC;CACV;;;;;AAKD,SAAS,UAAU,CAAC,IAAI,EAAE;IACtB,OAAO,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;CACrF;AACD,IAAI,aAAa,kBAAkB,YAAY;IAC3C,SAAS,aAAa,GAAG;KACxB;;;;;;;IAOD,aAAa,CAAC,MAAM;;;;;;IAMpB,UAAU,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;QAC7B,OAAO,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;KAC/C,CAAC;IACF,OAAO,aAAa,CAAC;CACxB,EAAE,CAAC;;AC3QJ;;;;;;;;;;;AAWA,AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,IAAI,kBAAkB,kBAAkB,YAAY;IAChD,SAAS,kBAAkB,CAAC,OAAO,EAAE;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;IAMD,kBAAkB,CAAC,SAAS,CAAC,SAAS;;;;;IAKtC,UAAU,KAAK,EAAE,OAAO,EAAE;QACtB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,YAAY,CAAC,EAAE;QACnD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;YAChD,OAAO,IAAI,CAAC;QAChB,qBAAqB,IAAI,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;SACxB;QACD,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;YACf,IAAI,GAAG,KAAK,CAAC;SAChB;aACI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;YACxC,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SACtC;aACI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;YAW3E,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7G,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;SAChC;aACI;YACD,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACf,qBAAqB,KAAK,GAAG,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAE;gBAC1E,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;aACjC;iBACI;gBACD,MAAM,wBAAwB,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;aAC7D;SACJ;QACD,OAAO,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;KACpG,CAAC;;;;IAIF,kBAAkB,CAAC,QAAQ,GAAG;QAC1B,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,YAAY;QACxB,UAAU,EAAE,QAAQ;QACpB,YAAY,EAAE,OAAO;QACrB,WAAW,EAAE,KAAK;QAClB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,IAAI;KACpB,CAAC;IACF,kBAAkB,CAAC,UAAU,GAAG;QAC5B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;KACxD,CAAC;;IAEF,kBAAkB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACrD,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,kBAAkB,CAAC;CAC7B,EAAE,CAAC,CAAC;AACL,AAiBA;;;;AAIA,SAAS,MAAM,CAAC,KAAK,EAAE;IACnB,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;CAC3D;;AClLD;;;;;;;;;;;AAWA,AACO,IAAqB,oBAAoB,GAAG,6BAA6B,CAAC;AACjF,IAAqB,UAAU,GAAG,EAAE,CAAC;AACrC,IAAqB,WAAW,GAAG,GAAG,CAAC;AACvC,IAAqB,SAAS,GAAG,GAAG,CAAC;AACrC,IAAqB,WAAW,GAAG,GAAG,CAAC;AACvC,IAAqB,SAAS,GAAG,GAAG,CAAC;AACrC,IAAqB,UAAU,GAAG,GAAG,CAAC;AACtC,IAAqB,aAAa,GAAG,GAAG,CAAC;AACzC,IAAqB,YAAY,GAAG,GAAG,CAAC;;;;;;;;;;;;AAYxC,AAAO,SAASG,cAAY,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE;IACrE,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI,CAAC,EAAE;IAC7C,qBAAqB,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IACzC,qBAAqB,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACnE,qBAAqB,GAAG,CAAC;;IAEzB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;QACjE,GAAG,GAAG,CAAC,KAAK,CAAC;KAChB;SACI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAChC,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,kBAAkB,CAAC;QACvC,OAAO,GAAG,CAAC;KACd;SACI;QACD,GAAG,GAAG,KAAK,CAAC;KACf;IACD,qBAAqB,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;IAChH,qBAAqB,aAAa,GAAG,EAAE,CAAC;IACxC,qBAAqB,MAAM,GAAG,KAAK,CAAC;IACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAChB,aAAa,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;KACxE;SACI;QACD,qBAAqB,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QACrD,IAAI,KAAK,KAAK,iBAAiB,CAAC,OAAO,EAAE;YACrC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;SAC1C;QACD,qBAAqB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7C,qBAAqB,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;QACnD,qBAAqB,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;QACnD,IAAI,UAAU,EAAE;YACZ,qBAAqB,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACpE,IAAI,KAAK,KAAK,IAAI,EAAE;gBAChB,GAAG,CAAC,KAAK,GAAG,UAAU,GAAG,4BAA4B,CAAC;gBACtD,OAAO,GAAG,CAAC;aACd;YACD,qBAAqB,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3C,qBAAqB,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAChD,qBAAqB,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,UAAU,IAAI,IAAI,EAAE;gBACpB,MAAM,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;aAC1C;YACD,IAAI,eAAe,IAAI,IAAI,EAAE;gBACzB,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC,CAAC;aACpD;YACD,IAAI,eAAe,IAAI,IAAI,EAAE;gBACzB,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC,CAAC;aACpD;iBACI,IAAI,eAAe,IAAI,IAAI,IAAI,WAAW,GAAG,WAAW,EAAE;gBAC3D,WAAW,GAAG,WAAW,CAAC;aAC7B;SACJ;QACD,WAAW,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACpD,qBAAqB,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QAClD,qBAAqB,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAC1D,qBAAqB,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QACtD,qBAAqB,QAAQ,GAAG,EAAE,CAAC;QACnC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;QAEnD,OAAO,UAAU,GAAG,MAAM,EAAE,UAAU,EAAE,EAAE;YACtC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACrB;;QAED,OAAO,UAAU,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE;YACjC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACrB;;QAED,IAAI,UAAU,GAAG,CAAC,EAAE;YAChB,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACvD;aACI;YACD,QAAQ,GAAG,MAAM,CAAC;YAClB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAChB;;QAED,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,IAAI,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACjC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1E;QACD,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE;YAClC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACzE;QACD,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACnC;QACD,qBAAqB,WAAW,GAAG,QAAQ,GAAG,YAAY,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC;QAC9F,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;;QAExE,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,qBAAqB,aAAa,GAAG,QAAQ,GAAG,YAAY,CAAC,eAAe,GAAG,YAAY,CAAC,OAAO,CAAC;YACpG,aAAa,IAAI,qBAAqB,CAAC,MAAM,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACrF;QACD,IAAI,QAAQ,EAAE;YACV,aAAa,IAAI,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC;SAC7F;KACJ;IACD,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;QACpB,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;KACnE;SACI;QACD,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;KACnE;IACD,IAAI,KAAK,KAAK,iBAAiB,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE;QAC3D,GAAG,CAAC,GAAG,GAAG,aAAa;aAClB,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC;aAChC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;QAChC,OAAO,GAAG,CAAC;KACd;IACD,IAAI,KAAK,KAAK,iBAAiB,CAAC,OAAO,EAAE;QACrC,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;QACxH,OAAO,GAAG,CAAC;KACd;IACD,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC;IACxB,OAAO,GAAG,CAAC;CACd;AACD,AAwBA;;;;;AAKA,SAAS,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE;IAC1C,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE;IAC9C,qBAAqB,CAAC,GAAG;QACrB,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;QACV,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;KACZ,CAAC;IACF,qBAAqB,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC9D,qBAAqB,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAChD,qBAAqB,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAChD,qBAAqB,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACrE,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;QAC3B;YACI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC1D,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC1D;IACL,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,mBAAmB,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/E,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1D,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvD,qBAAqB,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,EAAE,KAAK,SAAS,EAAE;YAClB,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SACjC;aACI,IAAI,EAAE,KAAK,UAAU,EAAE;YACxB,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SACrB;aACI;YACD,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC;SAClB;KACJ;IACD,qBAAqB,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACvD,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;IAC1E,IAAI,QAAQ,EAAE;QACV,qBAAqB,QAAQ,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM;QACnF,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KAChE;SACI;QACD,CAAC,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC;QAChC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;KACvB;IACD,OAAO,CAAC,CAAC;CACZ;AACD,AAYA;;;;AAIA,SAAS,SAAS,CAAC,YAAY,EAAE;;IAE7B,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAC9B,OAAO,YAAY,CAAC;KACvB;;IAED,qBAAqB,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;IACxF,IAAI,YAAY,CAAC,QAAQ,EAAE;QACvB,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC9B;SACI;QACD,IAAI,WAAW,KAAK,CAAC,EAAE;YACnB,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAClC;aACI,IAAI,WAAW,KAAK,CAAC,EAAE;YACxB,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC/B;QACD,YAAY,CAAC,UAAU,IAAI,CAAC,CAAC;KAChC;IACD,OAAO,YAAY,CAAC;CACvB;;;;;;;AAOD,SAAS,WAAW,CAAC,GAAG,EAAE;IACtB,qBAAqB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACjD,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB,MAAM,mBAAmB,UAAU,CAAC;IACxF,qBAAqB,CAAC,mBAAmB,CAAC,mBAAmB,KAAK,CAAC;;IAEnE,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE;QACjD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;KAC5C;;IAED,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;;QAE/B,IAAI,UAAU,GAAG,CAAC;YACd,UAAU,GAAG,CAAC,CAAC;QACnB,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACnC;SACI,IAAI,UAAU,GAAG,CAAC,EAAE;;QAErB,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;KAC9B;;IAED,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC,EAAE,EAAE;;KAEhD;IACD,IAAI,CAAC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;;QAE/B,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACb,UAAU,GAAG,CAAC,CAAC;KAClB;SACI;;QAED,KAAK,EAAE,CAAC;QACR,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS;YACrC,KAAK,EAAE,CAAC;;QAEZ,UAAU,IAAI,CAAC,CAAC;QAChB,MAAM,GAAG,EAAE,CAAC;;QAEZ,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;YAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACjC;KACJ;;IAED,IAAI,UAAU,GAAG,UAAU,EAAE;QACzB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;QAC1C,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC;QAC1B,UAAU,GAAG,CAAC,CAAC;KAClB;IACD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;CACzE;;;;;;;;;AASD,SAAS,WAAW,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE;IACjD,IAAI,OAAO,GAAG,OAAO,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,+CAA+C,GAAG,OAAO,GAAG,gCAAgC,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC;KAClI;IACD,qBAAqB,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IAClD,qBAAqB,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;IAC3E,qBAAqB,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;;IAEtF,qBAAqB,OAAO,GAAG,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC;IACtE,qBAAqB,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;IAC7C,IAAI,OAAO,GAAG,CAAC,EAAE;;QAEb,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;;QAE1D,KAAK,qBAAqB,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3D,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACjB;KACJ;SACI;;QAED,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QACvC,YAAY,CAAC,UAAU,GAAG,CAAC,CAAC;QAC5B,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE;YAC7C,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACrB;IACD,IAAI,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;YACjB,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;gBAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClB,YAAY,CAAC,UAAU,EAAE,CAAC;aAC7B;YACD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClB,YAAY,CAAC,UAAU,EAAE,CAAC;SAC7B;aACI;YACD,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC;SACzB;KACJ;;IAED,OAAO,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,WAAW,EAAE;QACzD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnB,qBAAqB,iBAAiB,GAAG,YAAY,KAAK,CAAC,CAAC;;;IAG5D,qBAAqB,MAAM,GAAG,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC;;IAEhE,qBAAqB,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE;QAC3E,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACd,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,iBAAiB,EAAE;;YAEnB,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE;gBAChC,MAAM,CAAC,GAAG,EAAE,CAAC;aAChB;iBACI;gBACD,iBAAiB,GAAG,KAAK,CAAC;aAC7B;SACJ;QACD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KAC1B,EAAE,CAAC,CAAC,CAAC;IACN,IAAI,KAAK,EAAE;QACP,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtB,YAAY,CAAC,UAAU,EAAE,CAAC;KAC7B;CACJ;;;;;;AAMD,AAAO,SAAS,iBAAiB,CAAC,IAAI,EAAE;IACpC,qBAAqB,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,uCAAuC,GAAG,IAAI,CAAC,CAAC;KACnE;IACD,OAAO,MAAM,CAAC;CACjB;;ACtZD;;;;;;;;;;;AAWA,AAKA;;;;;;;;;;AAUA,SAAS,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE;IAClF,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI,CAAC,EAAE;IAC7C,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,KAAK,CAAC,EAAE;IAC9D,IAAI,KAAK,IAAI,IAAI;QACb,OAAO,IAAI,CAAC;;IAEhB,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;IACzF,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,MAAM,wBAAwB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC/C;IACD,qBAAqB,MAAM,CAAC;IAC5B,qBAAqB,WAAW,CAAC;IACjC,qBAAqB,WAAW,CAAC;IACjC,IAAI,KAAK,KAAK,iBAAiB,CAAC,QAAQ,EAAE;;QAEtC,MAAM,GAAG,CAAC,CAAC;QACX,WAAW,GAAG,CAAC,CAAC;QAChB,WAAW,GAAG,CAAC,CAAC;KACnB;IACD,IAAI,MAAM,EAAE;QACR,qBAAqB,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAChE,IAAI,KAAK,KAAK,IAAI,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,6CAA6C,CAAC,CAAC;SAC3E;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;;YAElB,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACxC;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;;YAElB,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7C;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;;YAElB,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7C;KACJ;IACD,OAAO,eAAe,CAAC,MAAM,mBAAmB,KAAK,GAAG,MAAM,EAAE,KAAK,EAAE;QACnE,oBAAoB,EAAE,MAAM;QAC5B,qBAAqB,EAAE,WAAW;QAClC,qBAAqB,EAAE,WAAW;QAClC,QAAQ,EAAE,QAAQ;QAClB,gBAAgB,EAAE,gBAAgB;KACrC,CAAC,CAAC;CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BD,IAAI,qBAAqB,kBAAkB,YAAY;IACnD,SAAS,qBAAqB,CAAC,OAAO,EAAE;QACpC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;IAMD,qBAAqB,CAAC,SAAS,CAAC,SAAS;;;;;IAKzC,UAAU,KAAK,EAAE,MAAM,EAAE;QACrB,OAAO,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KACtG,CAAC;IACF,qBAAqB,CAAC,UAAU,GAAG;QAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;KAC9C,CAAC;;IAEF,qBAAqB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACxD,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,qBAAqB,CAAC;CAChC,EAAE,CAAC,CAAC;AACL,AAYA;;;;;;;;;;;;;;;;;;;;AAoBA,IAAI,qBAAqB,kBAAkB,YAAY;IACnD,SAAS,qBAAqB,CAAC,OAAO,EAAE;QACpC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;IAMD,qBAAqB,CAAC,SAAS,CAAC,SAAS;;;;;IAKzC,UAAU,KAAK,EAAE,MAAM,EAAE;QACrB,OAAO,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KACtG,CAAC;IACF,qBAAqB,CAAC,UAAU,GAAG;QAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;KAC/C,CAAC;;IAEF,qBAAqB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACxD,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,qBAAqB,CAAC;CAChC,EAAE,CAAC,CAAC;AACL,AAYA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,sBAAsB,kBAAkB,YAAY;IACpD,SAAS,sBAAsB,CAAC,OAAO,EAAE;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;;;IAQD,sBAAsB,CAAC,SAAS,CAAC,SAAS;;;;;;;IAO1C,UAAU,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE;QAClD,IAAI,YAAY,KAAK,KAAK,CAAC,EAAE,EAAE,YAAY,GAAG,KAAK,CAAC,EAAE;QACtD,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,GAAG,KAAK,CAAC,EAAE;QACxD,OAAO,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;KACrI,CAAC;IACF,sBAAsB,CAAC,UAAU,GAAG;QAChC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE;KAChD,CAAC;;IAEF,sBAAsB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACzD,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,sBAAsB,CAAC;CACjC,EAAE,CAAC;;ACzPJ;;;;;;;;;;;AAWA,AAGA;;;;;AAKA,AAAO,IAAqB,4BAA4B,GAAG,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,kBAAkB,CAAC;;ACnBrJ;;;;;;;;;;;AAWA,AAcA,IAAI,kBAAkB,kBAAkB,YAAY;IAChD,SAAS,kBAAkB,GAAG;KAC7B;;;;;;IAMD,kBAAkB,CAAC,SAAS,CAAC,kBAAkB;;;;;IAK/C,UAAU,KAAK,EAAE,iBAAiB,EAAE;QAChC,OAAO,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;KACzF,CAAC;;;;;IAKF,kBAAkB,CAAC,SAAS,CAAC,OAAO;;;;IAIpC,UAAU,YAAY,EAAE,EAAE,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;;;;;IAKxD,kBAAkB,CAAC,SAAS,CAAC,SAAS;;;;IAItC,UAAU,YAAY,EAAE,EAAE,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;IACxD,OAAO,kBAAkB,CAAC;CAC7B,EAAE,CAAC,CAAC;AACL,IAAI,eAAe,kBAAkB,YAAY;IAC7C,SAAS,eAAe,GAAG;KAC1B;;;;;;IAMD,eAAe,CAAC,SAAS,CAAC,kBAAkB;;;;;IAK5C,UAAU,KAAK,EAAE,iBAAiB,EAAE;QAChC,OAAO,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;KACnE,CAAC;;;;;IAKF,eAAe,CAAC,SAAS,CAAC,OAAO;;;;IAIjC,UAAU,YAAY,EAAE,GAAG,CAAC;;;;;IAK5B,eAAe,CAAC,SAAS,CAAC,SAAS;;;;IAInC,UAAU,YAAY,EAAE,GAAG,CAAC;IAC5B,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC,CAAC;AACL,IAAqB,gBAAgB,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9D,IAAqB,mBAAmB,GAAG,IAAI,kBAAkB,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BpE,IAAI,SAAS,kBAAkB,YAAY;IACvC,SAAS,SAAS,CAAC,IAAI,EAAE;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,sBAAsB,IAAI,EAAE,CAAC;KAC9C;;;;IAID,SAAS,CAAC,SAAS,CAAC,WAAW;;;IAG/B,YAAY;QACR,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;KACJ,CAAC;;;;;IAKF,SAAS,CAAC,SAAS,CAAC,SAAS;;;;IAI7B,UAAU,GAAG,EAAE;QACX,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,IAAI,GAAG,EAAE;gBACL,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACxB;YACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC;YAC9C,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QACD,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE;YACnB,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,SAAS,mBAAmB,GAAG,EAAE,CAAC;SACjD;QACD,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,oBAAoB,EAAE;YACjD,OAAO,IAAI,CAAC,oBAAoB,CAAC;SACpC;QACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC;QAC9C,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAC/C,CAAC;;;;;IAKF,SAAS,CAAC,SAAS,CAAC,UAAU;;;;IAI9B,UAAU,GAAG,EAAE;QACX,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,EAAE,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,kBAAkB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;KAClI,CAAC;;;;;IAKF,SAAS,CAAC,SAAS,CAAC,eAAe;;;;IAInC,UAAU,GAAG,EAAE;QACX,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;YACjB,OAAO,gBAAgB,CAAC;SAC3B;QACD,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;YACpB,OAAO,mBAAmB,CAAC;SAC9B;QACD,MAAM,wBAAwB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;KAClD,CAAC;;;;IAIF,SAAS,CAAC,SAAS,CAAC,QAAQ;;;IAG5B,YAAY;QACR,IAAI,CAAC,SAAS,CAAC,OAAO,oBAAoB,IAAI,CAAC,aAAa,GAAG,CAAC;QAChE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB,CAAC;;;;;;IAMF,SAAS,CAAC,SAAS,CAAC,kBAAkB;;;;;IAKtC,UAAU,KAAK,EAAE,KAAK,EAAE;QACpB,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;SAC5B;KACJ,CAAC;IACF,SAAS,CAAC,UAAU,GAAG;QACnB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;KAC1D,CAAC;;IAEF,SAAS,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC5C,EAAE,IAAI,EAAE,iBAAiB,GAAG;KAC/B,CAAC,EAAE,CAAC;IACL,OAAO,SAAS,CAAC;CACpB,EAAE,CAAC;;AC9OJ;;;;;;;;;;;AAWA,AAEA;;;;;;;AAOA,IAAI,aAAa,kBAAkB,YAAY;IAC3C,SAAS,aAAa,GAAG;KACxB;;;;;IAKD,aAAa,CAAC,SAAS,CAAC,SAAS;;;;IAIjC,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,KAAK;YACN,OAAO,KAAK,CAAC;QACjB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACxD;QACD,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;KAC9B,CAAC;IACF,aAAa,CAAC,UAAU,GAAG;QACvB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE;KACjD,CAAC;;IAEF,aAAa,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC1D,OAAO,aAAa,CAAC;CACxB,EAAE,CAAC,CAAC;AACL,AAUA;;;;;;;AAOA,SAAS,aAAa,CAAC,IAAI,EAAE;IACzB,IAAI,CAAC,IAAI;QACL,OAAO,IAAI,CAAC;IAChB,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;CAC/D;;;;;;AAMD,IAAI,aAAa,kBAAkB,YAAY;IAC3C,SAAS,aAAa,GAAG;KACxB;;;;;IAKD,aAAa,CAAC,SAAS,CAAC,SAAS;;;;IAIjC,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,KAAK;YACN,OAAO,KAAK,CAAC;QACjB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACxD;QACD,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC3F,CAAC;IACF,aAAa,CAAC,UAAU,GAAG;QACvB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE;KACjD,CAAC;;IAEF,aAAa,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC1D,OAAO,aAAa,CAAC;CACxB,EAAE,CAAC,CAAC;AACL,AAUA;;;;;AAKA,IAAI,aAAa,kBAAkB,YAAY;IAC3C,SAAS,aAAa,GAAG;KACxB;;;;;IAKD,aAAa,CAAC,SAAS,CAAC,SAAS;;;;IAIjC,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,KAAK;YACN,OAAO,KAAK,CAAC;QACjB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACxD;QACD,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;KAC9B,CAAC;IACF,aAAa,CAAC,UAAU,GAAG;QACvB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE;KACjD,CAAC;;IAEF,aAAa,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC1D,OAAO,aAAa,CAAC;CACxB,EAAE,CAAC;;AC3IJ;;;;;;;;;;;AAWA,AAGA,IAAqB,qBAAqB,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;AAoBlD,IAAI,cAAc,kBAAkB,YAAY;IAC5C,SAAS,cAAc,CAAC,aAAa,EAAE;QACnC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;;;;;;;IAOD,cAAc,CAAC,SAAS,CAAC,SAAS;;;;;;IAMlC,UAAU,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;QAChC,IAAI,KAAK,IAAI,IAAI;YACb,OAAO,EAAE,CAAC;QACd,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,EAAE;YACrD,MAAM,wBAAwB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;SAC7D;QACD,qBAAqB,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACxG,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC1E,CAAC;IACF,cAAc,CAAC,UAAU,GAAG;QACxB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;KAC9D,CAAC;;IAEF,cAAc,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACjD,EAAE,IAAI,EAAE,cAAc,GAAG;KAC5B,CAAC,EAAE,CAAC;IACL,OAAO,cAAc,CAAC;CACzB,EAAE,CAAC;;ACnEJ;;;;;;;;;;;AAWA,AAEA;;;;;;;;;;;;;;;;;AAiBA,IAAI,cAAc,kBAAkB,YAAY;IAC5C,SAAS,cAAc,GAAG;KACzB;;;;;;IAMD,cAAc,CAAC,SAAS,CAAC,SAAS;;;;;IAKlC,UAAU,KAAK,EAAE,OAAO,EAAE;QACtB,IAAI,KAAK,IAAI,IAAI;YACb,OAAO,EAAE,CAAC;QACd,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC1D,MAAM,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SAC3D;QACD,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YAC/B,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;SACzB;QACD,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YACjC,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3B;QACD,OAAO,EAAE,CAAC;KACb,CAAC;IACF,cAAc,CAAC,UAAU,GAAG;QACxB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;KAC9D,CAAC;;IAEF,cAAc,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC3D,OAAO,cAAc,CAAC;CACzB,EAAE,CAAC;;AC/DJ;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;;AAaA,IAAI,QAAQ,kBAAkB,YAAY;IACtC,SAAS,QAAQ,GAAG;KACnB;;;;;IAKD,QAAQ,CAAC,SAAS,CAAC,SAAS;;;;IAI5B,UAAU,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5D,QAAQ,CAAC,UAAU,GAAG;QAClB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;KACzD,CAAC;;IAEF,QAAQ,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACrD,OAAO,QAAQ,CAAC;CACnB,EAAE,CAAC;;AC3CJ;;;;;;;;;;;AAWA,AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAI,WAAW,kBAAkB,YAAY;IACzC,SAAS,WAAW,CAAC,OAAO,EAAE;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;;IAOD,WAAW,CAAC,SAAS,CAAC,SAAS;;;;;;IAM/B,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;QAC7B,IAAI,OAAO,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,CAAC;QAChB,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,EAAE,GAAGA,cAAY,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;QACxG,IAAI,KAAK,EAAE;YACP,MAAM,wBAAwB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SACtD;QACD,OAAO,GAAG,CAAC;KACd,CAAC;IACF,WAAW,CAAC,UAAU,GAAG;QACrB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;KAC9C,CAAC;;IAEF,WAAW,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC9C,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC,CAAC;AACL,AAYA;;;;;;;;;;;;;;;;;;;AAmBA,IAAI,WAAW,kBAAkB,YAAY;IACzC,SAAS,WAAW,CAAC,OAAO,EAAE;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;;IAOD,WAAW,CAAC,SAAS,CAAC,SAAS;;;;;;IAM/B,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;QAC7B,IAAI,OAAO,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,CAAC;QAChB,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,EAAE,GAAGA,cAAY,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;QACxG,IAAI,KAAK,EAAE;YACP,MAAM,wBAAwB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SACtD;QACD,OAAO,GAAG,CAAC;KACd,CAAC;IACF,WAAW,CAAC,UAAU,GAAG;QACrB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;KAC/C,CAAC;;IAEF,WAAW,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC9C,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC,CAAC;AACL,AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAI,YAAY,kBAAkB,YAAY;IAC1C,SAAS,YAAY,CAAC,OAAO,EAAE;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;;;;IASD,YAAY,CAAC,SAAS,CAAC,SAAS;;;;;;;;IAQhC,UAAU,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE;QACpD,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,QAAQ,CAAC,EAAE;QAC/C,IAAI,OAAO,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,CAAC;QAChB,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAC9B,qBAAqB,CAAC,OAAO,uBAAuB,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC/D,OAAO,CAAC,IAAI,CAAC,gNAAgN,CAAC,CAAC;aAClO;YACD,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC;SACzC;QACD,qBAAqB,QAAQ,GAAG,YAAY,IAAI,KAAK,CAAC;QACtD,IAAI,OAAO,KAAK,MAAM,EAAE;YACpB,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC;SACpF;QACD,IAAI,EAAE,GAAGA,cAAY,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;QACnH,IAAI,KAAK,EAAE;YACP,MAAM,wBAAwB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SACvD;QACD,OAAO,GAAG,CAAC;KACd,CAAC;IACF,YAAY,CAAC,UAAU,GAAG;QACtB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE;KAChD,CAAC;;IAEF,YAAY,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC/C,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;KAC3E,CAAC,EAAE,CAAC;IACL,OAAO,YAAY,CAAC;CACvB,EAAE,CAAC,CAAC;AACL,AAYA;;;;AAIA,SAAS,OAAO,CAAC,KAAK,EAAE;IACpB,OAAO,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,CAAC;CAC3D;;ACzPD;;;;;;;;;;;AAWA,AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,IAAI,SAAS,kBAAkB,YAAY;IACvC,SAAS,SAAS,GAAG;KACpB;;;;;;;IAOD,SAAS,CAAC,SAAS,CAAC,SAAS;;;;;;IAM7B,UAAU,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;QACzB,IAAI,KAAK,IAAI,IAAI;YACb,OAAO,KAAK,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACvB,MAAM,wBAAwB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SACpD;QACD,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAClC,CAAC;;;;;IAKF,SAAS,CAAC,SAAS,CAAC,QAAQ;;;;IAI5B,UAAU,GAAG,EAAE,EAAE,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;IACzE,SAAS,CAAC,UAAU,GAAG;QACnB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;KAC1D,CAAC;;IAEF,SAAS,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACtD,OAAO,SAAS,CAAC;CACpB,EAAE,CAAC;;AC/FJ;;;;;;;;;;;AAWA,AASA;;;AAGA,AAAO,IAAqB,YAAY,GAAG;IACvC,SAAS;IACT,aAAa;IACb,aAAa;IACb,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,aAAa;IACb,YAAY;IACZ,QAAQ;IACR,cAAc;IACd,cAAc;CACjB;;ACpCD;;;;;;;;;;;AAWA,AAKA;;;;;AAKA,IAAI,YAAY,kBAAkB,YAAY;IAC1C,SAAS,YAAY,GAAG;KACvB;IACD,YAAY,CAAC,UAAU,GAAG;QACtB,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBACb,YAAY,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;oBAC/C,OAAO,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;oBAC1C,SAAS,EAAE;wBACP,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,oBAAoB,EAAE;qBAC9D;iBACJ,EAAE,EAAE;KAChB,CAAC;;IAEF,YAAY,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACzD,OAAO,YAAY,CAAC;CACvB,EAAE,CAAC,CAAC;AACL,AAUA,IAAI,EAAE,GAAG,aAAa,CAAC;;;;;;AAMvB,IAAI,yBAAyB,kBAAkB,YAAY;IACvD,SAAS,yBAAyB,GAAG;KACpC;IACD,yBAAyB,CAAC,UAAU,GAAG;QACnC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBACb,YAAY,EAAE,CAAC,4BAA4B,CAAC;oBAC5C,OAAO,EAAE,CAAC,4BAA4B,CAAC;oBACvC,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;iBAC/D,EAAE,EAAE;KAChB,CAAC;;IAEF,yBAAyB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACtE,OAAO,yBAAyB,CAAC;CACpC,EAAE,CAAC;;AClEJ;;;;;;;;;;;AAWA,AACA;;;;;;;;AAQA,AAAO,IAAqB,QAAQ,GAAG,IAAI,cAAc,CAAC,eAAe,CAAC;;ACpB1E;;;;;;;;;;;AAWA,AAAO,IAAqB,mBAAmB,GAAG,SAAS,CAAC;AAC5D,AAAO,IAAqB,kBAAkB,GAAG,QAAQ,CAAC;AAC1D,AAAO,IAAqB,sBAAsB,GAAG,kBAAkB,CAAC;AACxE,AAAO,IAAqB,qBAAqB,GAAG,iBAAiB,CAAC;;;;;;;AAOtE,AAAO,SAAS,iBAAiB,CAAC,UAAU,EAAE;IAC1C,OAAO,UAAU,KAAK,mBAAmB,CAAC;CAC7C;;;;;;;AAOD,AAAO,SAAS,gBAAgB,CAAC,UAAU,EAAE;IACzC,OAAO,UAAU,KAAK,kBAAkB,CAAC;CAC5C;;;;;;;AAOD,AAAO,SAAS,mBAAmB,CAAC,UAAU,EAAE;IAC5C,OAAO,UAAU,KAAK,sBAAsB,CAAC;CAChD;;;;;;;AAOD,AAAO,SAAS,kBAAkB,CAAC,UAAU,EAAE;IAC3C,OAAO,UAAU,KAAK,qBAAqB,CAAC;CAC/C;;AClDD;;;;;;;;;;;AAWA,AACA;;;AAGA,AAAO,IAAqB,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB,CAAC;;ACftE;;;;;;;;;;;;;;;GAeG;;ACfH;;;;;;;;;;;;;;;;AAgBA,AACmF;0EACT;;AClB1E;;;;;;GAMG;;;;"}