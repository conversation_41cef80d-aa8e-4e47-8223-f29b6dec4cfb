{"version": 3, "file": "windowCount.js", "sourceRoot": "", "sources": ["../../../src/add/operator/windowCount.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,4BAA4B,4BAA4B,CAAC,CAAA;AAEzD,uBAAU,CAAC,SAAS,CAAC,WAAW,GAAG,yBAAW,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { windowCount } from '../../operator/windowCount';\n\nObservable.prototype.windowCount = windowCount;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    windowCount: typeof windowCount;\n  }\n}"]}