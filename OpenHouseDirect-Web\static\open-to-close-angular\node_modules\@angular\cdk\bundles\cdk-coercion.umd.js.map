{"version": 3, "file": "cdk-coercion.umd.js", "sources": ["../../src/cdk/coercion/array.ts", "../../src/cdk/coercion/number-property.ts", "../../src/cdk/coercion/boolean-property.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Wraps the provided value in an array, unless the provided value is an array. */\nexport function coerceArray<T>(value: T | T[]): T[] {\n  return Array.isArray(value) ? value : [value];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a data-bound value (typically a string) to a number. */\nexport function coerceNumberProperty(value: any): number;\nexport function coerceNumberProperty<D>(value: any, fallback: D): number | D;\nexport function coerceNumberProperty(value: any, fallbackValue = 0) {\n  return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nexport function _isNumberValue(value: any): boolean {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value as any)) && !isNaN(Number(value));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nexport function coerceBooleanProperty(value: any): boolean {\n  return value != null && `${value}` !== 'false';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AESA,SAAA,qBAAA,CAAsC,KAAU,EAAhD;IACE,OAAO,KAAK,IAAI,IAAI,IAAI,EAA1B,GAA6B,KAAO,KAAK,OAAO,CAAC;CAChD;;;;;;;;;;;;ADAD,SAAA,oBAAA,CAAqC,KAAU,EAAE,aAAiB,EAAlE;IAAiD,IAAjD,aAAA,KAAA,KAAA,CAAA,EAAiD,EAAA,aAAjD,GAAA,CAAkE,CAAlE,EAAA;IACE,OAAO,cAAc,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC;CAC9D;;;;;;;AAMD,SAAA,cAAA,CAA+B,KAAU,EAAzC;;;;IAIE,OAAO,CAAC,KAAK,CAAC,UAAU,mBAAC,KAAY,EAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;CAClE;;;;;;;;;;;;;ADfD,SAAA,WAAA,CAA+B,KAAc,EAA7C;IACE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;CAC/C;;;;;;;;;"}