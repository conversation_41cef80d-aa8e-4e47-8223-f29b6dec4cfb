{"version": 3, "file": "cdk-observers.umd.js", "sources": ["../../src/cdk/observers/observe-content.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  NgModule,\n  Output,\n  Input,\n  EventEmitter,\n  OnDestroy,\n  AfterContentInit,\n  Injectable,\n  NgZone,\n  OnChanges,\n  SimpleChanges,\n} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {Subject} from 'rxjs/Subject';\nimport {debounceTime} from 'rxjs/operators/debounceTime';\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\n@Injectable()\nexport class MutationObserverFactory {\n  create(callback: MutationCallback): MutationObserver | null {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n}\n\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\n@Directive({\n  selector: '[cdkObserveContent]',\n  exportAs: 'cdkObserveContent',\n})\nexport class CdkObserveContent implements AfterContentInit, OnChanges, OnDestroy {\n  private _observer: MutationObserver | null;\n  private _disabled = false;\n\n  /** Event emitted for each change in the element's content. */\n  @Output('cdkObserveContent') event = new EventEmitter<MutationRecord[]>();\n\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  @Input('cdkObserveContentDisabled')\n  get disabled() { return this._disabled; }\n  set disabled(value: any) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n\n  /** Used for debouncing the emitted values to the observeContent event. */\n  private _debouncer = new Subject<MutationRecord[]>();\n\n  /** Debounce interval for emitting the changes. */\n  @Input() debounce: number;\n\n  constructor(\n    private _mutationObserverFactory: MutationObserverFactory,\n    private _elementRef: ElementRef,\n    private _ngZone: NgZone) { }\n\n  ngAfterContentInit() {\n    if (this.debounce > 0) {\n      this._ngZone.runOutsideAngular(() => {\n        this._debouncer.pipe(debounceTime(this.debounce))\n            .subscribe((mutations: MutationRecord[]) => this.event.emit(mutations));\n      });\n    } else {\n      this._debouncer.subscribe(mutations => this.event.emit(mutations));\n    }\n\n    this._observer = this._ngZone.runOutsideAngular(() => {\n      return this._mutationObserverFactory.create((mutations: MutationRecord[]) => {\n        this._debouncer.next(mutations);\n      });\n    });\n\n    if (!this.disabled) {\n      this._enable();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['disabled']) {\n      changes['disabled'].currentValue ? this._disable() : this._enable();\n    }\n  }\n\n  ngOnDestroy() {\n    this._disable();\n    this._debouncer.complete();\n  }\n\n  private _disable() {\n    if (this._observer) {\n      this._observer.disconnect();\n    }\n  }\n\n  private _enable() {\n    if (this._observer) {\n      this._observer.observe(this._elementRef.nativeElement, {\n        characterData: true,\n        childList: true,\n        subtree: true\n      });\n    }\n  }\n}\n\n\n@NgModule({\n  exports: [CdkObserveContent],\n  declarations: [CdkObserveContent],\n  providers: [MutationObserverFactory]\n})\nexport class ObserversModule {}\n"], "names": ["NgModule", "Input", "Output", "NgZone", "ElementRef", "Directive", "debounceTime", "coerceBooleanProperty", "Subject", "EventEmitter", "Injectable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgCE,uBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,UAAO,QAA0B,EAAnC;QACI,OAAO,OAAO,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KACxF,CAAH;;QAJA,EAAA,IAAA,EAACU,wBAAU,EAAX;;;;IA9BA,OAAA,uBAAA,CAAA;;;;;;;IAoEE,SAAF,iBAAA,CACY,wBADZ,EAEY,WAFZ,EAGY,OAHZ,EAAA;QACY,IAAZ,CAAA,wBAAoC,GAAxB,wBAAwB,CAApC;QACY,IAAZ,CAAA,WAAuB,GAAX,WAAW,CAAvB;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QAxBA,IAAA,CAAA,SAAA,GAAsB,KAAK,CAA3B;;;;QAGA,IAAA,CAAA,KAAA,GAAuC,IAAID,0BAAY,EAAoB,CAA3E;;;;QAaA,IAAA,CAAA,UAAA,GAAuB,IAAID,oBAAO,EAAoB,CAAtD;KAQgC;IAdhC,MAAA,CAAA,cAAA,CAAM,iBAAN,CAAA,SAAA,EAAA,UAAc,EAAd;;;;;;QAAA,YAAA,EAAmB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAzC;;;;;QACE,UAAa,KAAU,EAAzB;YACI,IAAI,CAAC,SAAS,GAAGD,2CAAqB,CAAC,KAAK,CAAC,CAAC;SAC/C;;;;;;;IAaD,iBAAF,CAAA,SAAA,CAAA,kBAAoB;;;IAAlB,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAmBG;QAlBC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAArC;gBACQ,KAAI,CAAC,UAAU,CAAC,IAAI,CAACD,wCAAY,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;qBAC5C,SAAS,CAAC,UAAC,SAA2B,EAAnD,EAAwD,OAAA,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAlF,EAAkF,CAAC,CAAC;aAC7E,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAA,SAAS,EAAzC,EAA6C,OAAA,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAvE,EAAuE,CAAC,CAAC;SACpE;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAApD;YACM,OAAO,KAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,UAAC,SAA2B,EAA9E;gBACQ,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACjC,CAAC,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;KACF,CAAH;;;;;IAEE,iBAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,UAAY,OAAsB,EAApC;QACI,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;YACvB,OAAO,CAAC,UAAU,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;SACrE;KACF,CAAH;;;;IAEE,iBAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC5B,CAAH;;;;IAEU,iBAAV,CAAA,SAAA,CAAA,QAAkB;;;;QACd,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;SAC7B;;;;;IAGK,iBAAV,CAAA,SAAA,CAAA,OAAiB;;;;QACb,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;gBACrD,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;SACJ;;;QA7EL,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,qBAAqB;oBAC/B,QAAQ,EAAE,mBAAmB;iBAC9B,EAAD,EAAA;;;;QAbA,EAAA,IAAA,EAAa,uBAAuB,GAApC;QArBA,EAAA,IAAA,EAAED,wBAAU,GAAZ;QAQA,EAAA,IAAA,EAAED,oBAAM,GAAR;;;QAgCA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,oBAAM,EAAT,IAAA,EAAA,CAAU,mBAAmB,EAA7B,EAAA,EAAA;QAMA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,mBAAK,EAAR,IAAA,EAAA,CAAS,2BAA2B,EAApC,EAAA,EAAA;QAUA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,EAAA;;IAlEA,OAAA,iBAAA,CAAA;;AA6CA,IAAA,eAAA,kBAAA,YAAA;;;;QA8EA,EAAA,IAAA,EAACD,sBAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAAC,iBAAiB,CAAC;oBAC5B,YAAY,EAAE,CAAC,iBAAiB,CAAC;oBACjC,SAAS,EAAE,CAAC,uBAAuB,CAAC;iBACrC,EAAD,EAAA;;;;IA/HA,OAAA,eAAA,CAAA;CAgIA,EAAA,CAAA,CAAA;;;;;;;;;"}