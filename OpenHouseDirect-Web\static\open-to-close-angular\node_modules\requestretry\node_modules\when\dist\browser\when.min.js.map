{"version": 3, "sources": ["build/when.browserify.js", "callbacks.js", "cancelable.js", "delay.js", "function.js", "guard.js", "keys.js", "lib/Promise.js", "lib/Scheduler.js", "lib/TimeoutError.js", "lib/apply.js", "lib/decorators/array.js", "lib/decorators/flow.js", "lib/decorators/fold.js", "lib/decorators/inspect.js", "lib/decorators/iterate.js", "lib/decorators/progress.js", "lib/decorators/timed.js", "lib/decorators/unhandledRejection.js", "lib/decorators/with.js", "lib/env.js", "lib/format.js", "lib/liftAll.js", "lib/makePromise.js", "lib/state.js", "node.js", "parallel.js", "pipeline.js", "poll.js", "sequence.js", "timeout.js", "when.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/build/when.browserify.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/callbacks.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/cancelable.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/delay.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/function.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/guard.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/keys.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/Promise.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/Scheduler.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/TimeoutError.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/apply.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/array.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/flow.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/fold.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/inspect.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/iterate.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/progress.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/timed.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/unhandledRejection.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/decorators/with.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/env.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/format.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/liftAll.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/makePromise.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/lib/state.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/node.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/parallel.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/pipeline.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/poll.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/sequence.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/timeout.js", "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08/when.js"], "names": ["when", "module", "exports", "require", "callbacks", "cancelable", "delay", "fn", "guard", "keys", "nodefn", "node", "parallel", "pipeline", "poll", "sequence", "timeout", "define", "apply", "asyncFunction", "extraAsyncArgs", "_apply", "this", "dispatch", "f", "thisArg", "args", "h", "push", "alwaysUnary", "resolve", "reject", "tryCatchResolve", "resolver", "e", "call", "slice", "arguments", "lift", "length", "concat", "liftAll", "src", "combine", "dst", "_liftAll", "promisify", "positions", "Promise", "all", "then", "callback<PERSON><PERSON>", "errbackPos", "p", "_defer", "callback", "normalizePosition", "errback", "insertCallback", "_handler", "pos", "splice", "Array", "prototype", "makeApply", "amd", "factory", "deferred", "canceler", "cancel", "promise", "msec", "value", "compose", "funcs", "firstPromise", "attempt", "reduce", "arg", "func", "condition", "with<PERSON><PERSON>", "exit", "n", "allowed", "count", "Math", "max", "waiting", "shift", "object", "<PERSON><PERSON><PERSON>", "k", "x", "pending", "results", "Object", "i", "fold", "map", "mapWithKey", "to<PERSON>romise", "o", "settle", "promises", "states", "populateResults", "makePromise", "Scheduler", "async", "asap", "scheduler", "_async", "_running", "_queue", "_queueLen", "_afterQueue", "_afterQueueLen", "self", "drain", "_drain", "enqueue", "task", "run", "afterQueue", "TimeoutError", "message", "Error", "name", "captureStackTrace", "create", "constructor", "l", "params", "callAndResolve", "c", "handler", "callAndResolveNext", "state", "applier", "any", "handleFulfill", "errors", "handleReject", "resolved", "become", "_visitRemaining", "visit", "RangeError", "some", "fulfill", "nFulfill", "nReject", "min", "notify", "_traverse", "filter", "predicate", "a", "keep", "filterSync", "filtered", "j", "settleOne", "join", "fulfilled", "rejected", "_unreport", "inspect", "ar", "liftCombine", "reduceRight", "arr", "z", "applyFold", "spread", "onFulfilled", "array", "rejectInvalidPredicate", "TypeError", "evaluatePredicate", "isError", "maybeThenable", "identity", "createCatchFilter", "runSideEffect", "propagate", "result", "propagateValue", "origCatch", "done", "onResult", "onError", "receiver", "otherwise", "onRejected", "ensure", "orElse", "defaultValue", "tap", "onFulfilledSideEffect", "_beget", "to", "iterate", "unfold", "unspool", "next", "item", "newSeed", "seed", "progress", "onProgress", "setTimeout", "ms", "y", "env", "setTimer", "handleDelay", "resolve<PERSON>elay", "onTimeout", "reason", "t", "clearTimer", "throwit", "noop", "format", "report", "r", "handled", "reported", "logError", "id", "formatError", "unreport", "indexOf", "logInfo", "formatObject", "tasks", "running", "flush", "localConsole", "console", "error", "log", "info", "onPotentiallyUnhandledRejection", "rejection", "onPotentiallyUnhandledRejectionHandled", "onFatalRejection", "child", "chain", "isNode", "process", "toString", "hasMutationObserver", "MutationObserver", "WebKitMutationObserver", "initMutationObserver", "scheduled", "document", "createTextNode", "observe", "characterData", "data", "MutationObs", "capturedSetTimeout", "clearTimeout", "nextTick", "vertxRequire", "vertx", "cancelTimer", "runOnLoop", "runOnContext", "s", "stack", "String", "JSON", "tryStringify", "stringify", "defaultCombine", "defaultDst", "bind", "liftOne", "key", "environment", "Handler", "init", "promiseResolve", "promiseReject", "promiseNotify", "Pending", "isPromise", "Async", "<PERSON><PERSON><PERSON><PERSON>", "Rejected", "never", "foreverP<PERSON><PERSON><PERSON><PERSON>", "defer", "begetFrom", "parent", "context", "traverseWith", "snd", "traverse", "tryCatch2", "tryMap", "mapAt", "traverseAt", "settleAt", "Fulfilled", "getHandlerMaybeThenable", "visitRemaining", "start", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "race", "runRace", "getHandlerUntrusted", "untrustedThen", "Thenable", "FailIfRejected", "inheritedContext", "createContext", "consumers", "thenable", "AssimilateTask", "errorId", "_report", "ReportTask", "UnreportTask", "cycle", "ContinuationTask", "continuation", "ProgressTask", "_then", "tryAssimilate", "Fold", "failIfRejected", "runContinuation1", "enterContext", "tryCatchReject", "exitContext", "runContinuation3", "tryCatchReject3", "runNotify", "tryCatchReturn", "b", "inherit", "Parent", "Child", "objectCreate", "hasCustomEvent", "CustomEvent", "ev", "ignoredException", "hasInternetExplorerCustomEvent", "createEvent", "initCustomEvent", "initEmitRejection", "emit", "type", "detail", "bubbles", "dispatchEvent", "emitRejection", "proto", "fail", "_state", "q", "cont", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_resolve", "_reject", "_notify", "toPendingState", "toRejectedState", "toFulfilledState", "cb", "createCallback", "args1", "al", "err", "bind<PERSON>allback", "success", "wrapped", "liftCallback", "runTask", "interval", "verifier", "delayInitialTask", "certify", "schedule", "vote", "canceled", "verification", "addResult", "trigger", "Deferred", "isPromiseLike", "mapFunc", "timed", "flow", "generate", "unhandledRejection", "feature"], "mappings": "yqBgCAA,GAAAA,GAAAC,EAAAC,QAAAC,EAAA,UAEAH,GAAAI,UAAAD,EAAA,gBACAH,EAAAK,WAAAF,EAAA,iBACAH,EAAAM,MAAAH,EAAA,YACAH,EAAAO,GAAAJ,EAAA,eACAH,EAAAQ,MAAAL,EAAA,YACAH,EAAAS,KAAAN,EAAA,WACAH,EAAAU,OAAAV,EAAAW,KAAAR,EAAA,WACAH,EAAAY,SAAAT,EAAA,eACAH,EAAAa,SAAAV,EAAA,eACAH,EAAAc,KAAAX,EAAA,WACAH,EAAAe,SAAAZ,EAAA,eACAH,EAAAgB,QAAAb,EAAA,yOCHA,SAAAc,GACAA,EAAA,SAAAd,GAgDA,QAAAe,GAAAC,EAAAC,GACA,MAAAC,GAAAF,EAAAG,KAAAF,OAOA,QAAAG,GAAAC,EAAAC,EAAAC,EAAAC,GACAD,EAAAE,KAAAC,EAAAF,EAAAG,QAAAH,GAAAE,EAAAF,EAAAI,OAAAJ,IACAK,EAAAR,EAAAC,EAAAC,EAAAC,GAGA,QAAAK,GAAAR,EAAAC,EAAAC,EAAAO,GACA,IACAT,EAAAN,MAAAO,EAAAC,GACA,MAAAQ,GACAD,EAAAF,OAAAG,IAwBA,QAAAC,GAAAhB,GACA,MAAAE,GAAAF,EAAAG,KAAAc,EAAAD,KAAAE,UAAA,IAoCA,QAAAC,GAAAd,GACA,GAAAE,GAAAW,UAAAE,OAAA,EAAAH,EAAAD,KAAAE,UAAA,KACA,OAAA,YACA,MAAAhB,GAAAG,EAAAF,KAAAI,EAAAc,OAAAJ,EAAAD,KAAAE,cAeA,QAAAI,GAAAC,EAAAC,EAAAC,GACA,MAAAC,GAAAP,EAAAK,EAAAC,EAAAF,GAqDA,QAAAI,GAAA3B,EAAA4B,GAEA,MAAA,YACA,GAAAtB,GAAAH,IACA,OAAA0B,GAAAC,IAAAZ,WAAAa,KAAA,SAAAxB,GACA,GAEAyB,GAAAC,EAFAC,EAAAL,EAAAM,QAsBA,OAlBA,gBAAAP,GAAAQ,WACAJ,EAAAK,EAAA9B,EAAAqB,EAAAQ,WAGA,gBAAAR,GAAAU,UACAL,EAAAI,EAAA9B,EAAAqB,EAAAU,UAGAN,EAAAC,GACAM,EAAAhC,EAAA0B,EAAAC,EAAAM,SAAA5B,OAAAsB,EAAAM,UACAD,EAAAhC,EAAAyB,EAAAE,EAAAM,SAAA7B,QAAAuB,EAAAM,YAEAD,EAAAhC,EAAAyB,EAAAE,EAAAM,SAAA7B,QAAAuB,EAAAM,UACAD,EAAAhC,EAAA0B,EAAAC,EAAAM,SAAA5B,OAAAsB,EAAAM,WAGAxC,EAAAD,MAAAO,EAAAC,GAEA2B,KAKA,QAAAG,GAAA9B,EAAAkC,GACA,MAAA,GAAAA,EAAAlC,EAAAa,OAAAqB,EAAA,EAAAA,EAGA,QAAAF,GAAAhC,EAAAkC,EAAAL,EAAA9B,GACA,gBAAAmC,IACAlC,EAAAmC,OAAAD,EAAA,EAAA/B,EAAA0B,EAAA9B,IAIA,QAAAI,GAAAtB,EAAAkB,GACA,MAAA,YACAY,UAAAE,OAAA,EACAhC,EAAA4B,KAAAV,EAAAW,EAAAD,KAAAE,YAEA9B,EAAAW,MAAAO,EAAAY,YAnPA,GAAArC,GAAAG,EAAA,UACA6C,EAAAhD,EAAAgD,QACAH,EAAA1C,EAAA,iBACAiC,EAAA0B,MAAAC,UAAA3B,MAEA4B,EAAA7D,EAAA,eACAkB,EAAA2C,EAAAhB,EAAAzB,EAEA,QACAe,KAAAA,EACAG,QAAAA,EACAvB,MAAAA,EACAiB,KAAAA,EACAW,UAAAA,MA2OA,kBAAA7B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,6ECnPA,SAAAc,GACAA,EAAA,WAeA,MAAA,UAAAkD,EAAAC,GAaA,MAVAD,GAAAE,OAAA,WACA,IACAF,EAAApC,OAAAqC,EAAAD,IACA,MAAAjC,GACAiC,EAAApC,OAAAG,GAGA,MAAAiC,GAAAG,SAGAH,MAIA,kBAAAlD,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,+BCxCA,SAAAjD,GACAA,EAAA,SAAAd,GAEA,GAAAH,GAAAG,EAAA,SAKA,OAAA,UAAAoE,EAAAC,GACA,MAAAxE,GAAAwE,GAAAlE,MAAAiE,OAIA,kBAAAtD,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,yCCdA,SAAAc,GACAA,EAAA,SAAAd,GAwBA,QAAAe,GAAAM,EAAAE,GAEA,MAAAL,GAAAG,EAAAF,KAAA,MAAAI,KAAAU,EAAAD,KAAAT,IAeA,QAAAY,GAAAd,GACA,GAAAE,GAAAW,UAAAE,OAAA,EAAAH,EAAAD,KAAAE,UAAA,KACA,OAAA,YACA,MAAAhB,GAAAG,EAAAF,KAAAI,EAAAc,OAAAJ,EAAAD,KAAAE,cAeA,QAAAI,GAAAC,EAAAC,EAAAC,GACA,MAAAC,GAAAP,EAAAK,EAAAC,EAAAF,GAgBA,QAAA+B,GAAAjD,GACA,GAAAkD,GAAAtC,EAAAD,KAAAE,UAAA,EAEA,OAAA,YACA,GAAAZ,GAAAH,KACAI,EAAAU,EAAAD,KAAAE,WACAsC,EAAAC,EAAA1D,MAAAO,GAAAD,GAAAgB,OAAAd,GAEA,OAAA1B,GAAA6E,OAAAH,EAAA,SAAAI,EAAAC,GACA,MAAAA,GAAA5C,KAAAV,EAAAqD,IACAH,IApFA,GAAA3E,GAAAG,EAAA,UACAyE,EAAA5E,EAAA,OACA6C,EAAA1C,EAAA,iBACAkB,EAAAlB,EAAA,eAAAH,EAAAgD,SACAZ,EAAA0B,MAAAC,UAAA3B,KAEA,QACAE,KAAAA,EACAG,QAAAA,EACAN,KAAAyC,EACA1D,MAAAA,EACAuD,QAAAA,MA6EA,kBAAAxD,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,6EC3FA,SAAAc,GACAA,EAAA,SAAAd,GAiBA,QAAAK,GAAAwE,EAAAxD,GACA,MAAA,YACA,GAAAE,GAAAU,EAAAD,KAAAE,UAEA,OAAArC,GAAAgF,KAAAC,SAAA3D,MAAA4B,KAAA,SAAAgC,GACA,MAAAlF,GAAAwB,EAAAN,MAAAI,KAAAI,IAAA,WAAAwD,MAcA,QAAAC,GAAAC,GAeA,QAAAF,KACAG,EAAAC,KAAAC,IAAAF,EAAA,EAAA,GACAG,EAAAjD,OAAA,GACAiD,EAAAC,QAAAP,GAjBA,GAAAG,GAAA,EACAG,IAEA,OAAA,YACA,MAAAxF,GAAAsE,QAAA,SAAAxC,GACAsD,EAAAC,EACAvD,EAAAoD,GAEAM,EAAA5D,KAAAE,GAEAuD,GAAA,KA7CA,GAAArF,GAAAG,EAAA,UACAiC,EAAA0B,MAAAC,UAAA3B,KAIA,OAFA5B,GAAA2E,EAAAA,EAEA3E,KAqDA,kBAAAS,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,yCC9DA,SAAAc,GAAA,YACAA,GAAA,SAAAd,GAmBA,QAAA8C,GAAAyC,GAmBA,QAAAC,GAAAC,EAAAC,EAAA5D,GAEAX,KAAAsE,GAAAC,EACA,MAAAC,GACA7D,EAAAH,QAAAiE,GAfA,IAAA,GAAAH,GAPAvC,EAAAL,EAAAM,SACArB,EAAAe,EAAAW,SAAAN,GAEA0C,KACAtF,EAAAuF,OAAAvF,KAAAiF,GACAI,EAAArF,EAAA8B,OAEA0D,EAAA,EAAAA,EAAAxF,EAAA8B,SAAA0D,EACAL,EAAAnF,EAAAwF,GACAjD,EAAAW,SAAA+B,EAAAE,IAAAM,KAAAP,EAAAC,EAAAG,EAAA9D,EAOA,OAJA,KAAA6D,GACA7D,EAAAH,QAAAiE,GAGA1C,EAoBA,QAAA8C,GAAAT,EAAAlE,GAQA,QAAA4E,GAAAR,EAAAC,GACA,MAAArE,GAAAqE,EAAAD,GARA,MAAAS,GAAAX,GAAAxC,KAAA,SAAAwC,GACA,MAAAzC,GAAA+C,OAAAvF,KAAAiF,GAAAb,OAAA,SAAAyB,EAAAV,GAEA,MADAU,GAAAV,GAAAS,EAAAX,EAAAE,IAAAM,KAAAE,EAAAR,GACAU,UAgBA,QAAAC,GAAAb,GACA,GAAAjF,GAAAuF,OAAAvF,KAAAiF,GACAK,IAEA,IAAA,IAAAtF,EAAA8B,OACA,MAAA8D,GAAAN,EAGA,IAAA1C,GAAAL,EAAAM,SACArB,EAAAe,EAAAW,SAAAN,GACAmD,EAAA/F,EAAA0F,IAAA,SAAAP,GAAA,MAAAF,GAAAE,IAMA,OAJA5F,GAAAuG,OAAAC,GAAAtD,KAAA,SAAAuD,GACAC,EAAAjG,EAAAgG,EAAAV,EAAA9D,KAGAoB,EAGA,QAAAqD,GAAAjG,EAAAgG,EAAAV,EAAA9D,GACA,IAAA,GAAAgE,GAAA,EAAAA,EAAAxF,EAAA8B,OAAA0D,IACAF,EAAAtF,EAAAwF,IAAAQ,EAAAR,EAEAhE,GAAAH,QAAAiE,GAjGA,GAAA/F,GAAAG,EAAA,UACA6C,EAAAhD,EAAAgD,QACAqD,EAAArG,EAAA8B,OAEA,QACAmB,IAAAjD,EAAAsC,KAAAW,GACAkD,IAAAA,EACAI,OAAAA,MA8FA,kBAAAtF,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,yCC7GA,SAAAc,GAAA,YACAA,GAAA,SAAAd,GAEA,GAAAwG,GAAAxG,EAAA,iBACAyG,EAAAzG,EAAA,eACA0G,EAAA1G,EAAA,SAAA2G,IAEA,OAAAH,IACAI,UAAA,GAAAH,GAAAC,QAIA,kBAAA5F,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,2ECZA,SAAAc,GAAA,YACAA,GAAA,WAUA,QAAA2F,GAAAC,GACAvF,KAAA0F,OAAAH,EACAvF,KAAA2F,UAAA,EAEA3F,KAAA4F,OAAA5F,KACAA,KAAA6F,UAAA,EACA7F,KAAA8F,eACA9F,KAAA+F,eAAA,CAEA,IAAAC,GAAAhG,IACAA,MAAAiG,MAAA,WACAD,EAAAE,UAkDA,MA1CAZ,GAAA7C,UAAA0D,QAAA,SAAAC,GACApG,KAAA4F,OAAA5F,KAAA6F,aAAAO,EACApG,KAAAqG,OAOAf,EAAA7C,UAAA6D,WAAA,SAAAF,GACApG,KAAA8F,YAAA9F,KAAA+F,kBAAAK,EACApG,KAAAqG,OAGAf,EAAA7C,UAAA4D,IAAA,WACArG,KAAA2F,WACA3F,KAAA2F,UAAA,EACA3F,KAAA0F,OAAA1F,KAAAiG,SAOAX,EAAA7C,UAAAyD,OAAA,WAEA,IADA,GAAAvB,GAAA,EACAA,EAAA3E,KAAA6F,YAAAlB,EACA3E,KAAA4F,OAAAjB,GAAA0B,MACArG,KAAA4F,OAAAjB,GAAA,MAMA,KAHA3E,KAAA6F,UAAA,EACA7F,KAAA2F,UAAA,EAEAhB,EAAA,EAAAA,EAAA3E,KAAA+F,iBAAApB,EACA3E,KAAA8F,YAAAnB,GAAA0B,MACArG,KAAA8F,YAAAnB,GAAA,MAGA3E,MAAA+F,eAAA,GAGAT,KAGA,kBAAA3F,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCC3EA,SAAAjD,GAAA,YACAA,GAAA,WAOA,QAAA4G,GAAAC,GACAC,MAAA5F,KAAAb,MACAA,KAAAwG,QAAAA,EACAxG,KAAA0G,KAAAH,EAAAG,KACA,kBAAAD,OAAAE,mBACAF,MAAAE,kBAAA3G,KAAAuG,GAOA,MAHAA,GAAA9D,UAAAiC,OAAAkC,OAAAH,MAAAhE,WACA8D,EAAA9D,UAAAoE,YAAAN,EAEAA,KAEA,kBAAA5G,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCCtBA,SAAAjD,GAAA,YACAA,GAAA,WAMA,QAAA+C,GAAAhB,EAAAb,GAOA,QAAAjB,GAAAM,EAAAC,EAAAC,GACA,GAAA2B,GAAAL,EAAAM,SACA8E,EAAA1G,EAAAa,OACA8F,EAAA,GAAAvE,OAAAsE,EAGA,OAFAE,IAAA9G,EAAAA,EAAAC,QAAAA,EAAAC,KAAAA,EAAA2G,OAAAA,EAAApC,EAAAmC,EAAA,EAAAjG,KAAAA,GAAAkB,EAAAM,UAEAN,EAGA,QAAAiF,GAAAC,EAAA5G,GACA,GAAA4G,EAAAtC,EAAA,EACA,MAAA9D,GAAAoG,EAAA/G,EAAA+G,EAAA9G,QAAA8G,EAAAF,OAAA1G,EAGA,IAAA6G,GAAAxF,EAAAW,SAAA4E,EAAA7G,KAAA6G,EAAAtC,GACAuC,GAAAtC,KAAAuC,EAAAF,EAAA,OAAA5G,GAGA,QAAA8G,GAAAF,EAAA1C,EAAAlE,GACA4G,EAAAF,OAAAE,EAAAtC,GAAAJ,EACA0C,EAAAtC,GAAA,EACAqC,EAAAC,EAAA5G,GAvBA,MAJAU,WAAAE,OAAA,IACAJ,EAAAH,GAGAd,EA2BA,QAAAc,GAAAR,EAAAC,EAAAC,EAAAO,GACA,IACAA,EAAAH,QAAAN,EAAAN,MAAAO,EAAAC,IACA,MAAAQ,GACAD,EAAAF,OAAAG,IAtCA,MAFA8B,GAAAhC,gBAAAA,EAEAgC,KA2CA,kBAAA/C,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCChDA,SAAAjD,GAAA,YACAA,GAAA,SAAAd,GAEA,GAAAuI,GAAAvI,EAAA,YACAwI,EAAAxI,EAAA,WAEA,OAAA,UAAA6C,GA2CA,QAAA4F,GAAApC,GA+BA,QAAAqC,GAAAhD,GAEAiD,EAAA,KACAxH,KAAAQ,QAAA+D,GAGA,QAAAkD,GAAA7G,GAEAZ,KAAA0H,WAIAF,EAAAlH,KAAAM,GACA,MAAA4D,GACAxE,KAAAS,OAAA+G,IArCA,IAAA,GAAAnH,GAAAkE,EAPAxC,EAAAL,EAAAM,SACArB,EAAAoB,EAAAM,SACAyE,EAAA5B,EAAAjE,SAAA,EAEAuD,EAAAsC,EACAU,KAEA7C,EAAA,EAAAmC,EAAAnC,IAAAA,EAEA,GADAJ,EAAAW,EAAAP,GACA,SAAAJ,GAAAI,IAAAO,GAAA,CAMA,GADA7E,EAAAqB,EAAAW,SAAAkC,GACAlE,EAAA+G,QAAA,EAAA,CACAzG,EAAAgH,OAAAtH,GACAqB,EAAAkG,gBAAA1C,EAAAP,EAAAtE,EACA,OAEAA,EAAAwH,MAAAlH,EAAA4G,EAAAE,SAVAjD,CAkBA,OAJA,KAAAA,GACA7D,EAAAF,OAAA,GAAAqH,YAAA,mCAGA/F,EAiCA,QAAAgG,GAAA7C,EAAArB,GA8CA,QAAAmE,GAAAzD,GAEAvE,KAAA0H,WAIAjD,EAAAnE,KAAAiE,GACA,MAAA0D,IACAT,EAAA,KACAxH,KAAAQ,QAAAiE,KAIA,QAAAhE,GAAAG,GAEAZ,KAAA0H,WAIAF,EAAAlH,KAAAM,GACA,MAAAsH,IACAzD,EAAA,KACAzE,KAAAS,OAAA+G,KAlEA,GAQAU,GACA3D,EAAAI,EATA5C,EAAAL,EAAAM,SACArB,EAAAoB,EAAAM,SAEAoC,KACA+C,KAEAV,EAAA5B,EAAAjE,SAAA,EACAgH,EAAA,CAKA,KAAAtD,EAAA,EAAAmC,EAAAnC,IAAAA,EACAJ,EAAAW,EAAAP,IACA,SAAAJ,GAAAI,IAAAO,OAGA+C,CAgBA,KAZApE,EAAAG,KAAAC,IAAAJ,EAAA,GACAqE,EAAAD,EAAApE,EAAA,EACAoE,EAAAjE,KAAAmE,IAAAtE,EAAAoE,GAEApE,EAAAoE,EACAtH,EAAAF,OAAA,GAAAqH,YAAA,uCACAjE,EAAA,qBAAAoE,IACA,IAAAA,GACAtH,EAAAH,QAAAiE,GAIAE,EAAA,EAAAmC,EAAAnC,IAAAA,EACAJ,EAAAW,EAAAP,IACA,SAAAJ,GAAAI,IAAAO,KAIAxD,EAAAW,SAAAkC,GAAAsD,MAAAlH,EAAAqH,EAAAvH,EAAAE,EAAAyH,OAGA,OAAArG,GAoCA,QAAA8C,GAAAK,EAAAhF,GACA,MAAAwB,GAAA2G,UAAAnI,EAAAgF,GAYA,QAAAoD,GAAApD,EAAAqD,GACA,GAAAC,GAAA1H,EAAAD,KAAAqE,EACA,OAAAxD,GAAA2G,UAAAE,EAAAC,GAAA5G,KAAA,SAAA6G,GACA,MAAAC,GAAAF,EAAAC,KAIA,QAAAC,GAAAxD,EAAAuD,GAIA,IAAA,GAFA3B,GAAA2B,EAAAxH,OACA0H,EAAA,GAAAnG,OAAAsE,GACAnC,EAAA,EAAAiE,EAAA,EAAA9B,EAAAnC,IAAAA,EACA8D,EAAA9D,KACAgE,EAAAC,KAAAlH,EAAAW,SAAA6C,EAAAP,IAAAzB,MAIA,OADAyF,GAAA1H,OAAA2H,EACAD,EAWA,QAAA1D,GAAAC,GACA,MAAAvD,GAAAuD,EAAAL,IAAAgE,IAGA,QAAAA,GAAA9G,GAGA,GAAAmF,EAKA,OAJAnF,aAAAL,KAEAwF,EAAAnF,EAAAM,SAAAyG,QAEA5B,GAAA,IAAAA,EAAAE,UAAAF,EAEAnC,EAAAhD,GAAAH,KAAAwF,EAAA2B,UAAA3B,EAAA4B,WAMA9B,EAAA+B,YACA7B,EAAA8B,QAAAhC,IAaA,QAAA3D,GAAA2B,EAAAhF,GACA,MAAAa,WAAAE,OAAA,EAAAkI,EAAAtI,KAAAqE,EAAAkE,EAAAlJ,GAAAa,UAAA,IACAoI,EAAAtI,KAAAqE,EAAAkE,EAAAlJ,IAaA,QAAAmJ,GAAAnE,EAAAhF,GACA,MAAAa,WAAAE,OAAA,EAAAqI,EAAAzI,KAAAqE,EAAAkE,EAAAlJ,GAAAa,UAAA,IACAuI,EAAAzI,KAAAqE,EAAAkE,EAAAlJ,IAGA,QAAAkJ,GAAAlJ,GACA,MAAA,UAAAqJ,EAAAhF,EAAAI,GACA,MAAA6E,GAAAtJ,EAAA,QAAAqJ,EAAAhF,EAAAI,KAxRA,GAAA6E,GAAAnC,EAAA3F,GACAqD,EAAArD,EAAAlB,QACAmB,EAAAD,EAAAC,IAEAwH,EAAA3G,MAAAC,UAAAc,OACA+F,EAAA9G,MAAAC,UAAA4G,YACAvI,EAAA0B,MAAAC,UAAA3B,KAyBA,OArBAY,GAAA4F,IAAAA,EACA5F,EAAAqG,KAAAA,EACArG,EAAAuD,OAAAA,EAEAvD,EAAAmD,IAAAA,EACAnD,EAAA4G,OAAAA,EACA5G,EAAA6B,OAAAA,EACA7B,EAAA2H,YAAAA,EAQA3H,EAAAe,UAAAgH,OAAA,SAAAC,GACA,MAAA1J,MAAA4B,KAAAD,GAAAC,KAAA,SAAA+H,GACA,MAAAD,GAAA9J,MAAAI,KAAA2J,MAIAjI,MA+PA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,0DCtSA,SAAAc,GAAA,YACAA,GAAA,WAoIA,QAAAiK,KACA,KAAA,IAAAC,WAAA,sCAGA,QAAAC,GAAAlJ,EAAA2H,GACA,MAAAwB,GAAAxB,GAAA3H,YAAA2H,GAAAA,EAAA3H,GAGA,QAAAmJ,GAAAxB,GACA,MAAAA,KAAA9B,OACA,MAAA8B,GAAAA,EAAA9F,oBAAAgE,OAGA,QAAAuD,GAAAzF,GACA,OAAA,gBAAAA,IAAA,kBAAAA,KAAA,OAAAA,EAGA,QAAA0F,GAAA1F,GACA,MAAAA,GApJA,MAAA,UAAA7C,GA8CA,QAAAwI,GAAAhD,EAAAqB,GACA,MAAA,UAAA3H,GACA,MAAAkJ,GAAAlJ,EAAA2H,GACArB,EAAArG,KAAAb,KAAAY,GACAH,EAAAG,IA0BA,QAAAuJ,GAAAjD,EAAA/G,EAAAiK,EAAAlH,GACA,GAAAmH,GAAAnD,EAAArG,KAAAV,EACA,OAAA6J,GAAAK,GACAC,EAAAD,EAAAD,EAAAlH,GACAkH,EAAAlH,GAGA,QAAAoH,GAAAD,EAAAD,EAAA7F,GACA,MAAA/D,GAAA6J,GAAAzI,KAAA,WACA,MAAAwI,GAAA7F,KAnFA,GAAA/D,GAAAkB,EAAAlB,QACAC,EAAAiB,EAAAjB,OACA8J,EAAA7I,EAAAe,UAAA,QA2HA,OAhHAf,GAAAe,UAAA+H,KAAA,SAAAC,EAAAC,GACA1K,KAAAqC,SAAAwF,MAAA7H,KAAAqC,SAAAsI,SAAAF,EAAAC,IAWAhJ,EAAAe,UAAA,SAAAf,EAAAe,UAAAmI,UAAA,SAAAC,GACA,MAAA9J,WAAAE,OAAA,EACAsJ,EAAA1J,KAAAb,KAAA6K,GAGA,kBAAAA,GACA7K,KAAA8K,OAAAlB,GAGAW,EAAA1J,KAAAb,KAAAkK,EAAAnJ,UAAA,GAAA8J,KA4BAnJ,EAAAe,UAAA,WAAAf,EAAAe,UAAAqI,OAAA,SAAA5D,GACA,MAAA,kBAAAA,GACAlH,KAGAA,KAAA4B,KAAA,SAAA2C,GACA,MAAA4F,GAAAjD,EAAAlH,KAAAiK,EAAA1F,IACA,SAAA3D,GACA,MAAAuJ,GAAAjD,EAAAlH,KAAAS,EAAAG,MAyBAc,EAAAe,UAAA,QAAAf,EAAAe,UAAAsI,OAAA,SAAAC,GACA,MAAAhL,MAAA4B,KAAA,OAAA,WACA,MAAAoJ,MAYAtJ,EAAAe,UAAA,SAAA,SAAAS,GACA,MAAAlD,MAAA4B,KAAA,WACA,MAAAsB,MAUAxB,EAAAe,UAAAwI,IAAA,SAAAC,GACA,MAAAlL,MAAA4B,KAAAsJ,GAAA,SAAAlL,OAGA0B,MAyBA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCC1JA,SAAAjD,GAAA,YACAA,GAAA,WAEA,MAAA,UAAA+B,GAcA,MAZAA,GAAAe,UAAAmC,KAAA,SAAA1E,EAAAqJ,GACA,GAAAvG,GAAAhD,KAAAmL,QAQA,OANAnL,MAAAqC,SAAAuC,KAAA,SAAA2E,EAAAhF,EAAA6G,GACA1J,EAAAW,SAAAkH,GAAA3E,KAAA,SAAAL,EAAAgF,EAAA6B,GACAA,EAAA5K,QAAAN,EAAAW,KAAAb,KAAAuJ,EAAAhF,KACAA,EAAAvE,KAAAoL,IACA7B,EAAAvG,EAAAX,SAAAsI,SAAA3H,EAAAX,UAEAW,GAGAtB,MAIA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCCtBA,SAAAjD,GAAA,YACAA,GAAA,SAAAd,GAEA,GAAAqK,GAAArK,EAAA,YAAAqK,OAEA,OAAA,UAAAxH,GAMA,MAJAA,GAAAe,UAAAyG,QAAA,WACA,MAAAA,GAAAxH,EAAAW,SAAArC,QAGA0B,MAIA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,4CCfA,SAAAc,GAAA,YACAA,GAAA,WAEA,MAAA,UAAA+B,GAqBA,QAAA2J,GAAAnL,EAAAwD,EAAAwD,EAAA3C,GACA,MAAA+G,GAAA,SAAA/G,GACA,OAAAA,EAAArE,EAAAqE,KACAb,EAAAwD,EAAA3C,GAiBA,QAAA+G,GAAAC,EAAA7H,EAAAwD,EAAA3C,GAOA,QAAAiH,GAAAC,EAAAC,GACA,MAAAlL,GAAA0G,EAAAuE,IAAA7J,KAAA,WACA,MAAA0J,GAAAC,EAAA7H,EAAAwD,EAAAwE,KARA,MAAAlL,GAAA+D,GAAA3C,KAAA,SAAA+J,GACA,MAAAnL,GAAAkD,EAAAiI,IAAA/J,KAAA,SAAA4I,GACA,MAAAA,GAAAmB,EAAAnL,EAAA+K,EAAAI,IAAAlC,OAAA+B,OA1CA,GAAAhL,GAAAkB,EAAAlB,OAKA,OAHAkB,GAAA2J,QAAAA,EACA3J,EAAA4J,OAAAA,EAEA5J,MAkDA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCC5DA,SAAAjD,GAAA,YACAA,GAAA,WAEA,MAAA,UAAA+B,GAYA,MAJAA,GAAAe,UAAAmJ,SAAA,SAAAC,GACA,MAAA7L,MAAA4B,KAAA,OAAA,OAAAiK,IAGAnK,MAIA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCCnBA,SAAAjD,GAAA,YACAA,GAAA,SAAAd,GAKA,QAAAiN,GAAA5L,EAAA6L,EAAAxH,EAAAyH,GACA,MAAAC,GAAAC,SAAA,WACAhM,EAAAqE,EAAAyH,EAAAD,IACAA,GANA,GAAAE,GAAApN,EAAA,UACA0H,EAAA1H,EAAA,kBAQA,OAAA,UAAA6C,GAaA,QAAAyK,GAAAJ,EAAAxH,EAAAlE,GACAyL,EAAAM,EAAAL,EAAAxH,EAAAlE,GAGA,QAAA+L,GAAA7H,EAAAlE,GACAA,EAAAG,QAAA+D,GAgCA,QAAA8H,GAAAC,EAAAjM,EAAA0L,GACA,GAAAnL,GAAA,mBAAA0L,GACA,GAAA/F,GAAA,mBAAAwF,EAAA,MACAO,CACAjM,GAAAI,OAAAG,GAGA,MAlDAc,GAAAe,UAAAzD,MAAA,SAAA+M,GACA,GAAAhK,GAAA/B,KAAAmL,QAEA,OADAnL,MAAAqC,SAAAuC,KAAAuH,EAAAJ,EAAA,OAAAhK,EAAAM,UACAN,GAoBAL,EAAAe,UAAA/C,QAAA,SAAAqM,EAAAO,GACA,GAAAvK,GAAA/B,KAAAmL,SACA9K,EAAA0B,EAAAM,SAEAkK,EAAAT,EAAAO,EAAAN,EAAAO,EAAAvK,EAAAM,SAaA,OAXArC,MAAAqC,SAAAwF,MAAAxH,EACA,SAAAkE,GACA0H,EAAAO,WAAAD,GACAvM,KAAAQ,QAAA+D,IAEA,SAAAA,GACA0H,EAAAO,WAAAD,GACAvM,KAAAS,OAAA8D,IAEAlE,EAAA+H,QAEArG,GAUAL,MAIA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,+DCzEA,SAAAc,GAAA,YACAA,GAAA,SAAAd,GAyEA,QAAA4N,GAAA7L,GACA,KAAAA,GAGA,QAAA8L,MA3EA,GAAAR,GAAArN,EAAA,UAAAqN,SACAS,EAAA9N,EAAA,YAEA,OAAA,UAAA6C,GAoCA,QAAAkL,GAAAC,GACAA,EAAAC,UACAC,EAAAzM,KAAAuM,GACAG,EAAA,oCAAAH,EAAAI,GAAA,KAAAN,EAAAO,YAAAL,EAAA3J,SAIA,QAAAiK,GAAAN,GACA,GAAAlI,GAAAoI,EAAAK,QAAAP,EACAlI,IAAA,IACAoI,EAAAxK,OAAAoC,EAAA,GACA0I,EAAA,+BAAAR,EAAAI,GAAA,KAAAN,EAAAW,aAAAT,EAAA3J,SAIA,QAAAiD,GAAAjG,EAAAqE,GACAgJ,EAAAjN,KAAAJ,EAAAqE,GACA,OAAAiJ,IACAA,EAAAtB,EAAAuB,EAAA,IAIA,QAAAA,KAEA,IADAD,EAAA,KACAD,EAAAtM,OAAA,GACAsM,EAAApJ,QAAAoJ,EAAApJ,SA3DA,GAEAuJ,GAFAV,EAAAN,EACAW,EAAAX,CAGA,oBAAAiB,WAIAD,EAAAC,QACAX,EAAA,mBAAAU,GAAAE,MACA,SAAAhN,GAAA8M,EAAAE,MAAAhN,IACA,SAAAA,GAAA8M,EAAAG,IAAAjN,IAEAyM,EAAA,mBAAAK,GAAAI,KACA,SAAAlN,GAAA8M,EAAAI,KAAAlN,IACA,SAAAA,GAAA8M,EAAAG,IAAAjN,KAGAc,EAAAqM,gCAAA,SAAAC,GACA7H,EAAAyG,EAAAoB,IAGAtM,EAAAuM,uCAAA,SAAAD,GACA7H,EAAAgH,EAAAa,IAGAtM,EAAAwM,iBAAA,SAAAF,GACA7H,EAAAsG,EAAAuB,EAAA9K,OAGA,IAAAqK,MACAR,KACAS,EAAA,IA+BA,OAAA9L,OAUA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,yDCjFA,SAAAc,GAAA,YACAA,GAAA,WAEA,MAAA,UAAA+B,GAyBA,MARAA,GAAAe,UAAA,QAAAf,EAAAe,UAAAkB,SAAA,SAAAgH,GACA,GAAA5I,GAAA/B,KAAAmL,SACAgD,EAAApM,EAAAM,QAGA,OAFA8L,GAAAxD,SAAAA,EACA3K,KAAAqC,SAAA+L,MAAAD,EAAAxD,GACA5I,GAGAL,MAIA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCC/BA,SAAAjD,GAAA,YACAA,GAAA,SAAAd,GAqCA,QAAAwP,KACA,MAAA,mBAAAC,UACA,qBAAA5J,OAAAjC,UAAA8L,SAAA1N,KAAAyN,SAGA,QAAAE,KACA,MAAA,mBAAAC,mBAAAA,kBACA,mBAAAC,yBAAAA,uBAGA,QAAAC,GAAAF,GAMA,QAAApI,KACA,GAAAnG,GAAA0O,CACAA,GAAA,OACA1O,IARA,GAAA0O,GACAvP,EAAAwP,SAAAC,eAAA,IACA9J,EAAA,GAAAyJ,GAAApI,EACArB,GAAA+J,QAAA1P,GAAA2P,eAAA,GAQA,IAAArK,GAAA,CACA,OAAA,UAAAzE,GACA0O,EAAA1O,EACAb,EAAA4P,KAAAtK,GAAA,GAtDA,GAAAuK,GACAC,EAAA,mBAAArD,aAAAA,WAGAI,EAAA,SAAAhM,EAAA6L,GAAA,MAAAD,YAAA5L,EAAA6L,IACAS,EAAA,SAAAD,GAAA,MAAA6C,cAAA7C,IACA/G,EAAA,SAAAtF,GAAA,MAAAiP,GAAAjP,EAAA,GAGA,IAAAmO,IACA7I,EAAA,SAAAtF,GAAA,MAAAoO,SAAAe,SAAAnP,QAEA,IAAAgP,EAAAV,IACAhJ,EAAAmJ,EAAAO,OAEA,KAAAC,EAAA,CACA,GAAAG,GAAAzQ,EACA0Q,EAAAD,EAAA,QACApD,GAAA,SAAAhM,EAAA6L,GAAA,MAAAwD,GAAArD,SAAAH,EAAA7L,IACAsM,EAAA+C,EAAAC,YACAhK,EAAA+J,EAAAE,WAAAF,EAAAG,aAGA,OACAxD,SAAAA,EACAM,WAAAA,EACAhH,KAAAA,MAgCA,kBAAA7F,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,+BCpEA,SAAAc,GAAA,YACAA,GAAA,WAeA,QAAAuN,GAAAtM,GACA,GAAA+O,GAAA,gBAAA/O,IAAA,OAAAA,IAAAA,EAAAgP,OAAAhP,EAAA4F,SAAA5F,EAAAgP,OAAAhP,EAAA4F,QAAA8G,EAAA1M,EACA,OAAAA,aAAA6F,OAAAkJ,EAAAA,EAAA,6BASA,QAAArC,GAAAtI,GACA,GAAA2K,GAAAE,OAAA7K,EAIA,OAHA,oBAAA2K,GAAA,mBAAAG,QACAH,EAAAI,EAAA/K,EAAA2K,IAEAA,EAUA,QAAAI,GAAAxL,EAAAyG,GACA,IACA,MAAA8E,MAAAE,UAAAzL,GACA,MAAA3D,GACA,MAAAoK,IA3CA,OACAkC,YAAAA,EACAI,aAAAA,EACAyC,aAAAA,MA6CA,kBAAApQ,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCCnDA,SAAAjD,GAAA,YACAA,GAAA,WAaA,QAAAsQ,GAAAjL,EAAA9E,EAAAoE,GAEA,MADAU,GAAAV,GAAApE,EACA8E,EAGA,QAAAkL,GAAA9O,GACA,MAAA,kBAAAA,GAAAA,EAAA+O,OAAAzL,OAAAkC,OAAAxF,GAjBA,MAAA,UAAAgP,EAAA/O,EAAAC,EAAAF,GAKA,MAJA,mBAAAC,KACAA,EAAA4O,GAGAvL,OAAAvF,KAAAiC,GAAAmC,OAAA,SAAAjC,EAAA+O,GACA,GAAAnQ,GAAAkB,EAAAiP,EACA,OAAA,kBAAAnQ,GAAAmB,EAAAC,EAAA8O,EAAAlQ,GAAAmQ,GAAA/O,GACA,mBAAAA,GAAA4O,EAAA9O,GAAAE,OAYA,kBAAA3B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCCvBA,SAAAjD,GAAA,YACAA,GAAA,WAEA,MAAA,UAAA2Q,GAkBA,QAAA5O,GAAAf,EAAAuG,GACAlH,KAAAqC,SAAA1B,IAAA4P,EAAArJ,EAAAsJ,EAAA7P,GAQA,QAAA6P,GAAA7P,GAgBA,QAAA8P,GAAAlM,GACA2C,EAAA1G,QAAA+D,GAOA,QAAAmM,GAAApE,GACApF,EAAAzG,OAAA6L,GAQA,QAAAqE,GAAApM,GACA2C,EAAAkB,OAAA7D,GAjCA,GAAA2C,GAAA,GAAA0J,EAEA,KACAjQ,EAAA8P,EAAAC,EAAAC,GACA,MAAA/P,GACA8P,EAAA9P,GAGA,MAAAsG,GA4CA,QAAA1G,GAAA+D,GACA,MAAAsM,GAAAtM,GAAAA,EACA,GAAA7C,GAAA6O,EAAA,GAAAO,GAAAC,EAAAxM,KAQA,QAAA9D,GAAA8D,GACA,MAAA,IAAA7C,GAAA6O,EAAA,GAAAO,GAAA,GAAAE,GAAAzM,KAOA,QAAA0M,KACA,MAAAC,IAQA,QAAAC,KACA,MAAA,IAAAzP,GAAA6O,EAAA,GAAAK,IAoDA,QAAAQ,GAAAC,EAAA3P,GACA,GAAAyM,GAAA,GAAAyC,GAAAS,EAAA1G,SAAA0G,EAAAvI,OAAAwI,QACA,OAAA,IAAA5P,GAAA6O,EAAApC,GAgBA,QAAAxM,GAAAuD,GACA,MAAAqM,GAAAC,EAAA,KAAAtM,GAUA,QAAAuM,GAAAvR,EAAAgF,GACA,MAAAqM,GAAAG,EAAAxR,EAAAgF,GAGA,QAAAqM,GAAAI,EAAAzR,EAAAgF,GAwBA,QAAA0M,GAAAjN,EAAAJ,EAAA5D,GACAA,EAAA+G,UACAmK,EAAA3M,EAAA4M,EAAAnN,EAAAgN,EAAAzR,EAAAqE,EAAAI,GAAAhE,GAIA,QAAAmR,GAAAnN,EAAAJ,EAAA5D,GACA8D,EAAAE,GAAAJ,EACA,MAAAC,GACA7D,EAAAgH,OAAA,GAAAoK,GAAAtN,IA1BA,IAAA,GAAAF,GANA2C,EAAA,kBAAAhH,GAAA0R,EAAAE,EAEAnR,EAAA,GAAAiQ,GACApM,EAAAU,EAAAjE,SAAA,EACAwD,EAAA,GAAAjC,OAAAgC,GAEAG,EAAA,EAAAA,EAAAO,EAAAjE,SAAAN,EAAA+G,WAAA/C,EACAJ,EAAAW,EAAAP,GAEA,SAAAJ,GAAAI,IAAAO,GAKA2M,EAAA3M,EAAAgC,EAAAvC,EAAAJ,EAAA5D,KAJA6D,CAWA,OAJA,KAAAA,GACA7D,EAAAgH,OAAA,GAAAoK,GAAAtN,IAGA,GAAA/C,GAAA6O,EAAA5P,GAgBA,QAAAkR,GAAA3M,EAAAgC,EAAAvC,EAAAJ,EAAA5D,GACA,GAAAqJ,EAAAzF,GAAA,CACA,GAAAlE,GAAA2R,EAAAzN,GACAoL,EAAAtP,EAAA+G,OAEA,KAAAuI,EACAtP,EAAAuE,KAAAsC,EAAAvC,EAAA,OAAAhE,GACAgP,EAAA,EACAzI,EAAAvC,EAAAtE,EAAA6C,MAAAvC,IAEAA,EAAAgH,OAAAtH,GACA4R,EAAA/M,EAAAP,EAAA,EAAAtE,QAGA6G,GAAAvC,EAAAJ,EAAA5D,GAKA,QAAAsR,GAAA/M,EAAAgN,EAAAhL,GACA,IAAA,GAAAvC,GAAAuN,EAAAvN,EAAAO,EAAAjE,SAAA0D,EACAwN,EAAApB,EAAA7L,EAAAP,IAAAuC,GAIA,QAAAiL,GAAA9R,EAAA6G,GACA,GAAA7G,IAAA6G,EAAA,CAIA,GAAAyI,GAAAtP,EAAA+G,OACA,KAAAuI,EACAtP,EAAAwH,MAAAxH,EAAA,OAAAA,EAAA4I,WACA,EAAA0G,GACAtP,EAAA4I,aAkBA,QAAAmJ,GAAAlN,GACA,MAAA,gBAAAA,IAAA,OAAAA,EACAzE,EAAA,GAAAoJ,WAAA,kCAKA,IAAA3E,EAAAjE,OAAAgQ,IACA,IAAA/L,EAAAjE,OAAAT,EAAA0E,EAAA,IACAmN,EAAAnN,GAGA,QAAAmN,GAAAnN,GACA,GACAP,GAAAJ,EAAAlE,EADAM,EAAA,GAAAiQ,EAEA,KAAAjM,EAAA,EAAAA,EAAAO,EAAAjE,SAAA0D,EAEA,GADAJ,EAAAW,EAAAP,GACA,SAAAJ,GAAAI,IAAAO,GAAA,CAKA,GADA7E,EAAA0Q,EAAAxM,GACA,IAAAlE,EAAA+G,QAAA,CACAzG,EAAAgH,OAAAtH,GACA4R,EAAA/M,EAAAP,EAAA,EAAAtE,EACA,OAEAA,EAAAwH,MAAAlH,EAAAA,EAAAH,QAAAG,EAAAF,QAGA,MAAA,IAAAiB,GAAA6O,EAAA5P,GAWA,QAAAoQ,GAAAxM,GACA,MAAAsM,GAAAtM,GACAA,EAAAlC,SAAAyG,OAEAkB,EAAAzF,GAAA+N,EAAA/N,GAAA,GAAAwN,GAAAxN,GASA,QAAAyN,GAAAzN,GACA,MAAAsM,GAAAtM,GAAAA,EAAAlC,SAAAyG,OAAAwJ,EAAA/N,GAQA,QAAA+N,GAAA/N,GACA,IACA,GAAAgO,GAAAhO,EAAA3C,IACA,OAAA,kBAAA2Q,GACA,GAAAC,GAAAD,EAAAhO,GACA,GAAAwN,GAAAxN,GACA,MAAA3D,GACA,MAAA,IAAAoQ,GAAApQ,IAQA,QAAA2P,MAmDA,QAAAkC,MAcA,QAAA7B,GAAAjG,EAAA+H,GACAhR,EAAAiR,cAAA3S,KAAA0S,GAEA1S,KAAA4S,UAAA,OACA5S,KAAA2K,SAAAA,EACA3K,KAAAkH,QAAA,OACAlH,KAAA0H,UAAA,EAsGA,QAAAoJ,GAAA5J,GACAlH,KAAAkH,QAAAA,EAuBA,QAAAsL,GAAA5Q,EAAAiR,GACAjC,EAAA/P,KAAAb,MACAuN,EAAApH,QAAA,GAAA2M,GAAAlR,EAAAiR,EAAA7S,OAUA,QAAA+R,GAAAxN,GACA7C,EAAAiR,cAAA3S,MACAA,KAAAkD,MAAAqB,EAsBA,QAAAyM,GAAAzM,GACA7C,EAAAiR,cAAA3S,MAEAA,KAAAiN,KAAA8F,EACA/S,KAAAkD,MAAAqB,EACAvE,KAAA8M,SAAA,EACA9M,KAAA+M,UAAA,EAEA/M,KAAAgT,UAoCA,QAAAC,GAAAjF,EAAAsD,GACAtR,KAAAgO,UAAAA,EACAhO,KAAAsR,QAAAA,EAWA,QAAA4B,GAAAlF,GACAhO,KAAAgO,UAAAA,EA0BA,QAAAmF,KACA,MAAA,IAAAnC,GAAA,GAAAnH,WAAA,kBASA,QAAAuJ,GAAAC,EAAAnM,GACAlH,KAAAqT,aAAAA,EACArT,KAAAkH,QAAAA,EAWA,QAAAoM,GAAApQ,EAAAgE,GACAlH,KAAAkH,QAAAA,EACAlH,KAAAkD,MAAAA,EAsBA,QAAA4P,GAAAlR,EAAAiR,EAAAlS,GACAX,KAAAuT,MAAA3R,EACA5B,KAAA6S,SAAAA,EACA7S,KAAAW,SAAAA,EAYA,QAAA6S,GAAA5R,EAAAiR,EAAArS,EAAAC,EAAA2H,GACA,IACAxG,EAAAf,KAAAgS,EAAArS,EAAAC,EAAA2H,GACA,MAAAxH,GACAH,EAAAG,IAQA,QAAA6S,GAAAvT,EAAAqJ,EAAAtC,EAAAmE,GACApL,KAAAE,EAAAA,EAAAF,KAAAuJ,EAAAA,EAAAvJ,KAAAiH,EAAAA,EAAAjH,KAAAoL,GAAAA,EACApL,KAAAW,SAAA+S,EACA1T,KAAA2K,SAAA3K,KAqBA,QAAA6Q,GAAAtM,GACA,MAAAA,aAAA7C,GASA,QAAAsI,GAAAzF,GACA,OAAA,gBAAAA,IAAA,kBAAAA,KAAA,OAAAA,EAGA,QAAAoP,GAAAzT,EAAAG,EAAAsK,EAAAa,GACA,MAAA,kBAAAtL,GACAsL,EAAA7D,OAAAtH,IAGAqB,EAAAkS,aAAAvT,GACAwT,EAAA3T,EAAAG,EAAA6C,MAAAyH,EAAAa,OACA9J,GAAAoS,eAGA,QAAAC,GAAA7T,EAAAqE,EAAAlE,EAAAsK,EAAAa,GACA,MAAA,kBAAAtL,GACAsL,EAAA7D,OAAAtH,IAGAqB,EAAAkS,aAAAvT,GACA2T,EAAA9T,EAAAqE,EAAAlE,EAAA6C,MAAAyH,EAAAa,OACA9J,GAAAoS,eAMA,QAAAG,GAAA/T,EAAAqE,EAAAlE,EAAAsK,EAAAa,GACA,MAAA,kBAAAtL,GACAsL,EAAApD,OAAA7D,IAGA7C,EAAAkS,aAAAvT,GACA6T,EAAAhU,EAAAqE,EAAAoG,EAAAa,OACA9J,GAAAoS,eAGA,QAAApC,GAAAxR,EAAAsI,EAAA2L,GACA,IACA,MAAAjU,GAAAsI,EAAA2L,GACA,MAAAvT,GACA,MAAAH,GAAAG,IAQA,QAAAiT,GAAA3T,EAAAqE,EAAApE,EAAAqL,GACA,IACAA,EAAA7D,OAAAoJ,EAAA7Q,EAAAW,KAAAV,EAAAoE,KACA,MAAA3D,GACA4K,EAAA7D,OAAA,GAAAqJ,GAAApQ,KAOA,QAAAoT,GAAA9T,EAAAqE,EAAAyH,EAAA7L,EAAAqL,GACA,IACAtL,EAAAW,KAAAV,EAAAoE,EAAAyH,EAAAR,GACA,MAAA5K,GACA4K,EAAA7D,OAAA,GAAAqJ,GAAApQ,KAQA,QAAAsT,GAAAhU,EAAAqE,EAAApE,EAAAqL,GACA,IACAA,EAAApD,OAAAlI,EAAAW,KAAAV,EAAAoE,IACA,MAAA3D,GACA4K,EAAApD,OAAAxH,IAIA,QAAAwT,GAAAC,EAAAC,GACAA,EAAA7R,UAAA8R,EAAAF,EAAA5R,WACA6R,EAAA7R,UAAAoE,YAAAyN,EAGA,QAAA9C,GAAAjN,EAAAyH,GACA,MAAAA,GAGA,QAAAU,MAEA,QAAA8H,KACA,GAAA,kBAAAC,aACA,IACA,GAAAC,GAAA,GAAAD,aAAA,qBACA,OAAAC,aAAAD,aACA,MAAAE,IAEA,OAAA,EAGA,QAAAC,KACA,GAAA,mBAAA/F,WAAA,kBAAAA,UAAAgG,YACA,IAEA,GAAAH,GAAA7F,SAAAgG,YAAA,cAEA,OADAH,GAAAI,gBAAA,aAAA,GAAA,OACA,EACA,MAAAH,IAEA,OAAA,EAGA,QAAAI,KAEA,MAAA,mBAAAzG,UAAA,OAAAA,SACA,kBAAAA,SAAA0G,KAKA,SAAAC,EAAAjH,GACA,MAAA,uBAAAiH,EACA3G,QAAA0G,KAAAC,EAAAjH,EAAA9K,MAAA8K,GACAM,QAAA0G,KAAAC,EAAAjH,IAEA,mBAAAhI,OAAAwO,IACA,SAAAxO,EAAAyO,GACA,MAAA,UAAAQ,EAAAjH,GACA,GAAA0G,GAAA,GAAAD,GAAAQ,GACAC,QACA5I,OAAA0B,EAAA9K,MACAmN,IAAArC,GAEAmH,SAAA,EACApW,YAAA,GAGA,QAAAiH,EAAAoP,cAAAV,KAEA1O,KAAAyO,aACA,mBAAAzO,OAAA4O,IACA,SAAA5O,EAAA6I,GACA,MAAA,UAAAoG,EAAAjH,GACA,GAAA0G,GAAA7F,EAAAgG,YAAA,cAMA,OALAH,GAAAI,gBAAAG,GAAA,GAAA,GACA3I,OAAA0B,EAAA9K,MACAmN,IAAArC,KAGAhI,EAAAoP,cAAAV,KAEA1O,KAAA6I,UAGAnC,EA36BA,GAAAa,GAAA+C,EAAA7K,UACA4P,EAAAN,IAEAR,EAAA7P,OAAAkC,QACA,SAAA0O,GACA,QAAAhB,MAEA,MADAA,GAAA7R,UAAA6S,EACA,GAAAhB,GA0DA5S,GAAAlB,QAAAA,EACAkB,EAAAjB,OAAAA,EACAiB,EAAAuP,MAAAA,EAEAvP,EAAAM,OAAAmP,EACAzP,EAAAW,SAAA0O,EAmDArP,EAAAe,UAAAb,KAAA,SAAA8H,EAAAmB,EAAAgB,GACA,GAAAwF,GAAArR,KAAAqC,SACA+E,EAAAiK,EAAAvI,OAAA1B,OAEA,IAAA,kBAAAsC,IAAAtC,EAAA,GACA,kBAAAyD,IAAA,EAAAzD,EAEA,MAAA,IAAApH,MAAA6G,YAAA0J,EAAAc,EAGA,IAAAtP,GAAA/B,KAAAmL,SACAgD,EAAApM,EAAAM,QAIA,OAFAgP,GAAAjD,MAAAD,EAAAkD,EAAA1G,SAAAjB,EAAAmB,EAAAgB,GAEA9J,GASAL,EAAAe,UAAA,SAAA,SAAAoI,GACA,MAAA7K,MAAA4B,KAAA,OAAAiJ,IAQAnJ,EAAAe,UAAA0I,OAAA,WACA,MAAAiG,GAAApR,KAAAqC,SAAArC,KAAA6G,cAUAnF,EAAAC,IAAAA,EACAD,EAAA0Q,KAAAA,EACA1Q,EAAA2G,UAAAoJ,EAgFA/P,EAAAkG,gBAAAqK,EAkHA1B,EAAA9N,UAAA/D,KACA6R,EAAA9N,UAAAkF,OACA4I,EAAA9N,UAAA2F,OACAmI,EAAA9N,UAAA8S,KACAhF,EAAA9N,UAAAwG,UACAsH,EAAA9N,UAAAuQ,QACAtG,EAEA6D,EAAA9N,UAAA+S,OAAA,EAEAjF,EAAA9N,UAAA2E,MAAA,WACA,MAAApH,MAAAwV,QAQAjF,EAAA9N,UAAAqG,KAAA,WAEA,IADA,GAAAzI,GAAAL,KACA,SAAAK,EAAA6G,SACA7G,EAAAA,EAAA6G,OAEA,OAAA7G,IAGAkQ,EAAA9N,UAAA2L,MAAA,SAAAhD,EAAAT,EAAA5B,EAAAC,EAAA4C,GACA5L,KAAAtB,MACAiC,SAAAyK,EACAT,SAAAA,EACA5B,UAAAA,EACAC,SAAAA,EACA4C,SAAAA,KAIA2E,EAAA9N,UAAAoF,MAAA,SAAA8C,EAAA5B,EAAAC,EAAA4C,GACA5L,KAAAoO,MAAAsF,EAAA/I,EAAA5B,EAAAC,EAAA4C,IAGA2E,EAAA9N,UAAAmC,KAAA,SAAA1E,EAAAqJ,EAAAtC,EAAAmE,GACApL,KAAAtB,KAAA,GAAA+U,GAAAvT,EAAAqJ,EAAAtC,EAAAmE,KASAgJ,EAAA7D,EAAAkC,GAEAA,EAAAhQ,UAAAkF,OAAA,SAAAtH,GACAA,EAAAkV,OAGA,IAAA7B,GAAA,GAAAjB,EAeA2B,GAAA7D,EAAAK,GAEAA,EAAAnO,UAAA+S,OAAA,EAEA5E,EAAAnO,UAAAjC,QAAA,SAAA+D,GACAvE,KAAA2H,OAAAoJ,EAAAxM,KAGAqM,EAAAnO,UAAAhC,OAAA,SAAA8D,GACAvE,KAAA0H,UAIA1H,KAAA2H,OAAA,GAAAqJ,GAAAzM,KAGAqM,EAAAnO,UAAAqG,KAAA,WACA,IAAA9I,KAAA0H,SACA,MAAA1H,KAKA,KAFA,GAAAK,GAAAL,KAEA,SAAAK,EAAA6G,SAEA,GADA7G,EAAAA,EAAA6G,QACA7G,IAAAL,KACA,MAAAA,MAAAkH,QAAAiM,GAIA,OAAA9S,IAGAuQ,EAAAnO,UAAA4D,IAAA,WACA,GAAAoP,GAAAzV,KAAA4S,UACA1L,EAAAlH,KAAAkH,OACAlH,MAAAkH,QAAAlH,KAAAkH,QAAA4B,OACA9I,KAAA4S,UAAA,MAEA,KAAA,GAAAjO,GAAA,EAAAA,EAAA8Q,EAAAxU,SAAA0D,EACAuC,EAAAxI,KAAA+W,EAAA9Q,KAIAiM,EAAAnO,UAAAkF,OAAA,SAAAT,GACAlH,KAAA0H,WAIA1H,KAAA0H,UAAA,EACA1H,KAAAkH,QAAAA,EACA,SAAAlH,KAAA4S,WACArF,EAAApH,QAAAnG,MAGA,SAAAA,KAAAsR,SACApK,EAAA8L,QAAAhT,KAAAsR,WAIAV,EAAAnO,UAAA/D,KAAA,SAAA2U,GACArT,KAAA0H,SACA6F,EAAApH,QAAA,GAAAiN,GAAAC,EAAArT,KAAAkH,UAEA,SAAAlH,KAAA4S,UACA5S,KAAA4S,WAAAS,GAEArT,KAAA4S,UAAAtS,KAAA+S,IAQAzC,EAAAnO,UAAA2F,OAAA,SAAA7D,GACAvE,KAAA0H,UACA6F,EAAApH,QAAA,GAAAmN,GAAA/O,EAAAvE,QAIA4Q,EAAAnO,UAAA8S,KAAA,SAAAjE,GACA,GAAArK,GAAA,mBAAAqK,GAAAtR,KAAAsR,QAAAA,CACAtR,MAAA0H,UAAA1H,KAAAkH,QAAA4B,OAAAyM,KAAAtO,IAGA2J,EAAAnO,UAAAuQ,QAAA,SAAA1B,GACAtR,KAAA0H,UAAA1H,KAAAkH,QAAA4B,OAAAkK,QAAA1B,IAGAV,EAAAnO,UAAAwG,UAAA,WACAjJ,KAAA0H,UAAA1H,KAAAkH,QAAA4B,OAAAG,aAYAmL,EAAA7D,EAAAO,GAEAA,EAAArO,UAAA/D,KAAA,SAAA2U,GACA9F,EAAApH,QAAA,GAAAiN,GAAAC,EAAArT,QAGA8Q,EAAArO,UAAAuQ,QAAA,SAAA1B,GACAtR,KAAA8I,OAAAkK,QAAA1B,IAGAR,EAAArO,UAAAwG,UAAA,WACAjJ,KAAA8I,OAAAG,aAcAmL,EAAAxD,EAAA4B,GAYA4B,EAAA7D,EAAAwB,GAEAA,EAAAtP,UAAA+S,OAAA,EAEAzD,EAAAtP,UAAAmC,KAAA,SAAA1E,EAAAqJ,EAAAtC,EAAAmE,GACA2I,EAAA7T,EAAAqJ,EAAAvJ,KAAAiH,EAAAmE,IAGA2G,EAAAtP,UAAA/D,KAAA,SAAAgX,GACA/B,EAAA+B,EAAA3M,UAAA/I,KAAA0V,EAAA/K,SAAA+K,EAAA/U,UAGA,IAAAoS,GAAA,CAkBAqB,GAAA7D,EAAAS,GAEAA,EAAAvO,UAAA+S,OAAA,GAEAxE,EAAAvO,UAAAmC,KAAA,SAAA1E,EAAAqJ,EAAAtC,EAAAmE,GACAA,EAAAzD,OAAA3H,OAGAgR,EAAAvO,UAAA/D,KAAA,SAAAgX,GACA,kBAAAA,GAAA1M,UACAhJ,KAAAiJ,YAEA0K,EAAA+B,EAAA1M,SAAAhJ,KAAA0V,EAAA/K,SAAA+K,EAAA/U,WAGAqQ,EAAAvO,UAAAuQ,QAAA,SAAA1B,GACA/D,EAAAjH,WAAA,GAAA2M,GAAAjT,KAAAsR,KAGAN,EAAAvO,UAAAwG,UAAA,WACAjJ,KAAA8M,UAGA9M,KAAA8M,SAAA,EACAS,EAAAjH,WAAA,GAAA4M,GAAAlT,SAGAgR,EAAAvO,UAAA8S,KAAA,SAAAjE,GACAtR,KAAA+M,UAAA,EACAsI,EAAA,qBAAArV,MACA0B,EAAAwM,iBAAAlO,KAAA,SAAAsR,EAAAtR,KAAAsR,QAAAA,IAQA2B,EAAAxQ,UAAA4D,IAAA,WACArG,KAAAgO,UAAAlB,SAAA9M,KAAAgO,UAAAjB,WACA/M,KAAAgO,UAAAjB,UAAA,EACAsI,EAAA,qBAAArV,KAAAgO,YACAtM,EAAAqM,gCAAA/N,KAAAgO,UAAAhO,KAAAsR,WAQA4B,EAAAzQ,UAAA4D,IAAA,WACArG,KAAAgO,UAAAjB,WACAsI,EAAA,mBAAArV,KAAAgO,YACAtM,EAAAuM,uCAAAjO,KAAAgO,aAOAtM,EAAAiR,cACAjR,EAAAkS,aACAlS,EAAAoS,YACApS,EAAAqM,gCACArM,EAAAuM,uCACAvM,EAAAwM,iBACAxB,CAIA,IAAAiJ,IAAA,GAAApF,GACAW,GAAA,GAAAxP,GAAA6O,EAAAoF,GA4QA,OA3PAvC,GAAA3Q,UAAA4D,IAAA,WACArG,KAAAkH,QAAA4B,OAAApK,KAAAsB,KAAAqT,eAYAC,EAAA7Q,UAAA4D,IAAA,WACA,GAAAoP,GAAAzV,KAAAkH,QAAA0L,SACA,IAAA,SAAA6C,EAIA,IAAA,GAAAxO,GAAAtC,EAAA,EAAAA,EAAA8Q,EAAAxU,SAAA0D,EACAsC,EAAAwO,EAAA9Q,GACAsP,EAAAhN,EAAA2E,SAAA5L,KAAAkD,MAAAlD,KAAAkH,QAAAD,EAAA0D,SAAA1D,EAAAtG,WAiBAmS,EAAArQ,UAAA4D,IAAA,WAIA,QAAAuP,GAAArR,GAAAlE,EAAAG,QAAA+D,GACA,QAAAsR,GAAAtR,GAAAlE,EAAAI,OAAA8D,GACA,QAAAuR,GAAAvR,GAAAlE,EAAA+H,OAAA7D,GALA,GAAAlE,GAAAL,KAAAW,QACA6S,GAAAxT,KAAAuT,MAAAvT,KAAA6S,SAAA+C,EAAAC,EAAAC,IAyBArC,EAAAhR,UAAAsG,UAAA,SAAAxE,GACAvE,KAAAE,EAAAW,KAAAb,KAAAiH,EAAAjH,KAAAuJ,EAAAhF,EAAAvE,KAAAoL,KAGAqI,EAAAhR,UAAAuG,SAAA,SAAAzE,GACAvE,KAAAoL,GAAA3K,OAAA8D,IAGAkP,EAAAhR,UAAAmJ,SAAA,SAAArH,GACAvE,KAAAoL,GAAAhD,OAAA7D,IAiLA7C,MAGA,kBAAA/B,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCCt7BA,SAAAjD,GAAA,YACAA,GAAA,WASA,QAAAoW,KACA,OAAA3O,MAAA,WAGA,QAAA4O,GAAApV,GACA,OAAAwG,MAAA,WAAAkF,OAAA1L,GAGA,QAAAqV,GAAA1R,GACA,OAAA6C,MAAA,YAAAlE,MAAAqB,GAGA,QAAA2E,GAAAhC,GACA,GAAAE,GAAAF,EAAAE,OACA,OAAA,KAAAA,EAAA2O,IACA3O,EAAA,EAAA6O,EAAA/O,EAAAhE,OACA8S,EAAA9O,EAAAhE,OAvBA,OACAsB,QAAAuR,EACAhN,UAAAkN,EACAjN,SAAAgN,EACA9M,QAAAA,MAuBA,kBAAAvJ,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,gCCxBA,SAAAjD,GACAA,EAAA,SAAAd,GAiDA,QAAAe,GAAAM,EAAAE,GACA,MAAAL,GAAAG,EAAAF,KAAAI,OAGA,QAAAH,GAAAC,EAAAC,EAAAC,EAAAC,GACA,GAAA6V,GAAAC,EAAA9V,EACA,KACA,OAAAD,EAAAa,QACA,IAAA,GAAAf,EAAAW,KAAAV,EAAAC,EAAA,GAAAA,EAAA,GAAA8V,EAAA,MACA,KAAA,GAAAhW,EAAAW,KAAAV,EAAAC,EAAA,GAAA8V,EAAA,MACA,KAAA,GAAAhW,EAAAW,KAAAV,EAAA+V,EAAA,MACA,SACA9V,EAAAE,KAAA4V,GACAhW,EAAAN,MAAAO,EAAAC,IAEA,MAAAQ,GACAP,EAAAI,OAAAG,IA6BA,QAAAC,GAAAX,GACA,MAAAH,GAAAG,EAAAF,KAAAc,EAAAD,KAAAE,UAAA,IAiCA,QAAAC,GAAAd,GACA,GAAAkW,GAAArV,UAAAE,OAAA,EAAAH,EAAAD,KAAAE,UAAA,KACA,OAAA,YAEA,GAGA4D,GAHAmC,EAAAsP,EAAAnV,OACAoV,EAAAtV,UAAAE,OACAb,EAAA,GAAAoC,OAAA6T,EAAAvP,EAEA,KAAAnC,EAAA,EAAAmC,EAAAnC,IAAAA,EACAvE,EAAAuE,GAAAyR,EAAAzR,EAEA,KAAAA,EAAA,EAAA0R,EAAA1R,IAAAA,EACAvE,EAAAuE,EAAAmC,GAAA/F,UAAA4D,EAEA,OAAA5E,GAAAG,EAAAF,KAAAI,IAeA,QAAAe,GAAAC,EAAAC,EAAAC,GACA,MAAAC,GAAAP,EAAAK,EAAAC,EAAAF,GA4BA,QAAA+U,GAAAxV,GACA,MAAA,UAAA2V,EAAApT,GACAoT,EACA3V,EAAAF,OAAA6V,GACAvV,UAAAE,OAAA,EACAN,EAAAH,QAAAM,EAAAD,KAAAE,UAAA,IAEAJ,EAAAH,QAAA0C,IAyBA,QAAAqT,GAAAvT,EAAAf,GASA,QAAAuU,GAAAtT,GACAuT,EAAA,KAAAvT,GAGA,QAAAuT,GAAAH,EAAApT,GACAgJ,EAAA,WACAjK,EAAAqU,EAAApT,IACA,GATA,MANAF,GAAAtE,EAAAsE,GAEAf,GACAe,EAAApB,KAAA4U,EAAAC,GAGAzT,EAmCA,QAAA0T,GAAAzU,GACA,MAAA,UAAAe,GACA,MAAAuT,GAAAvT,EAAAf,IApQA,GAAAvD,GAAAG,EAAA,UACA0C,EAAA1C,EAAA,iBACAqN,EAAArN,EAAA,aAAAqN,SACApL,EAAA0B,MAAAC,UAAA3B,MAEAf,EAAAlB,EAAA,eAAAH,EAAAgD,QAAAzB,EAEA,QACAe,KAAAA,EACAG,QAAAA,EACAvB,MAAAA,EACAiB,KAAAA,EACAsV,eAAAA,EACAI,aAAAA,EACAG,aAAAA,MA2PA,kBAAA/W,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,6FC1QA,SAAAc,GACAA,EAAA,SAAAd,GAEA,GAAAH,GAAAG,EAAA,UACA8C,EAAAjD,EAAAgD,QAAAC,IACAb,EAAA0B,MAAAC,UAAA3B,KAUA,OAAA,UAAAyM,GACA,MAAA5L,GAAAb,EAAAD,KAAAE,UAAA,IAAAa,KAAA,SAAAxB,GACA,MAAA1B,GAAAmG,IAAA0I,EAAA,SAAAnH,GACA,MAAAA,GAAAxG,MAAA,OAAAQ,WAMA,kBAAAT,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,0CCvBA,SAAAc,GACAA,EAAA,SAAAd,GAEA,GAAAH,GAAAG,EAAA,UACA8C,EAAAjD,EAAAgD,QAAAC,IACAb,EAAA0B,MAAAC,UAAA3B,KAUA,OAAA,UAAAyM,GAGA,GAAAoJ,GAAA,SAAAvW,EAAAgG,GAKA,MAJAuQ,GAAA,SAAAnT,EAAA4C,GACA,MAAAA,GAAA5C,IAGA4C,EAAAxG,MAAA,KAAAQ,GAGA,OAAAuB,GAAAb,EAAAD,KAAAE,UAAA,IAAAa,KAAA,SAAAxB,GACA,MAAA1B,GAAA6E,OAAAgK,EAAA,SAAA/J,EAAA4C,GACA,MAAAuQ,GAAAnT,EAAA4C,IACAhG,SAKA,kBAAAT,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,0CCrCA,SAAAc,GAAA,YACAA,GAAA,SAAAd,GAEA,GAAAH,GAAAG,EAAA,UACAyE,EAAA5E,EAAA,OACAK,EAAAF,EAAA,eA0CA,OAAA,UAAAuH,EAAAwQ,EAAAC,EAAAC,GAeA,QAAAC,GAAA1M,GACAxH,EAAArC,QAAA6J,GAGA,QAAA2M,GAAA3M,GACA/G,EAAAsT,GAAAhV,KAAAqV,EAAAxW,GACA,SAAA4J,GACAxH,EAAAuF,OAAAiC,GAIA,QAAA4M,KACAC,GACAxY,EAAA0H,IACA,SAAAiE,GACA3L,EAAAmY,EAAAxM,GACA,SAAA8M,GACA,MAAAA,GAAAJ,EAAA1M,GAAA2M,EAAA3M,IAEA,WAAA2M,EAAA3M,MAGA5J,GApCA,GAAAoC,GAAAqU,EAAAzW,CAmDA,OAjDAyW,IAAA,EACArU,EAAA9D,EAAAL,EAAAyS,QAAA,WAAA+F,GAAA,IACAzW,EAAAoC,EAAApC,OAEAoW,EAAAA,GAAA,WAAA,OAAA,GAEA,kBAAAD,KACAA,EAAA,SAAAA,GACA,MAAA,YAAA,MAAAlY,KAAAM,MAAA4X,KACAA,IA6BAE,EACAE,IAGAC,IAIApU,EAAAG,QAAA0B,OAAAkC,OAAA/D,EAAAG,SACAH,EAAAG,QAAAD,OAAAF,EAAAE,OAEAF,EAAAG,YAIA,kBAAArD,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,2DCrGA,SAAAc,GACAA,EAAA,SAAAd,GAEA,GAAAH,GAAAG,EAAA,UACA8C,EAAAjD,EAAAgD,QAAAC,IACAb,EAAA0B,MAAAC,UAAA3B,KAUA,OAAA,UAAAyM,GASA,QAAA6J,GAAA/M,GAEA,MADA5F,GAAAnE,KAAA+J,GACA5F,EAVA,GAAAA,KAEA,OAAA9C,GAAAb,EAAAD,KAAAE,UAAA,IAAAa,KAAA,SAAAxB,GACA,MAAA1B,GAAA6E,OAAAgK,EAAA,SAAA9I,EAAA2B,GACA,MAAA1H,GAAA0H,EAAAxG,MAAA,OAAAQ,GAAAgX,IACA3S,SAUA,kBAAA9E,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,0CC/BA,SAAAc,GACAA,EAAA,SAAAd,GAEA,GAAAH,GAAAG,EAAA,SAKA,OAAA,UAAAoE,EAAAoU,GACA,MAAA3Y,GAAA2Y,GAAA3X,QAAAuD,OAGA,kBAAAtD,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D,0CChBA,SAAAc,GAAA,YACAA,GAAA,SAAAd,GAwEA,QAAAH,GAAA6F,EAAAmF,EAAAmB,EAAAgB,GACA,GAAA9J,GAAAL,EAAAlB,QAAA+D,EACA,OAAAxD,WAAAE,OAAA,EACAc,EAGAA,EAAAH,KAAA8H,EAAAmB,EAAAgB,GAQA,QAAA7I,GAAArC,GACA,MAAA,IAAAe,GAAAf,GASA,QAAAK,GAAAd,GACA,MAAA,YACA,IAAA,GAAAyE,GAAA,EAAAmC,EAAA/F,UAAAE,OAAAuH,EAAA,GAAAhG,OAAAsE,GAAAA,EAAAnC,IAAAA,EACA6D,EAAA7D,GAAA5D,UAAA4D,EAEA,OAAA/E,GAAAM,EAAAF,KAAAwI,IAUA,QAAAlF,GAAApD,GAEA,IAAA,GAAAyE,GAAA,EAAAmC,EAAA/F,UAAAE,OAAA,EAAAuH,EAAA,GAAAhG,OAAAsE,GAAAA,EAAAnC,IAAAA,EACA6D,EAAA7D,GAAA5D,UAAA4D,EAAA,EAEA,OAAA/E,GAAAM,EAAAF,KAAAwI,GAQA,QAAA2I,KACA,MAAA,IAAAmG,GAGA,QAAAA,KAGA,QAAA9W,GAAA+D,GAAAxC,EAAAM,SAAA7B,QAAA+D,GACA,QAAA9D,GAAA8D,GAAAxC,EAAAM,SAAA5B,OAAA8D,GACA,QAAA6D,GAAA7D,GAAAxC,EAAAM,SAAA+F,OAAA7D,GAJA,GAAAxC,GAAAL,EAAAM,QAMAhC,MAAAgD,QAAAjB,EACA/B,KAAAQ,QAAAA,EACAR,KAAAS,OAAAA,EACAT,KAAAoI,OAAAA,EACApI,KAAAW,UAAAH,QAAAA,EAAAC,OAAAA,EAAA2H,OAAAA,GAWA,QAAAmP,GAAAhT,GACA,MAAAA,IAAA,kBAAAA,GAAA3C,KAUA,QAAAkH,KACA,MAAApH,GAAAC,IAAAZ,WASA,QAAAY,GAAAuD,GACA,MAAAxG,GAAAwG,EAAAxD,EAAAC,KAUA,QAAAsD,GAAAC,GACA,MAAAxG,GAAAwG,EAAAxD,EAAAuD,QAYA,QAAAJ,GAAAK,EAAAsS,GACA,MAAA9Y,GAAAwG,EAAA,SAAAA,GACA,MAAAxD,GAAAmD,IAAAK,EAAAsS,KAaA,QAAAlP,GAAApD,EAAAqD,GACA,MAAA7J,GAAAwG,EAAA,SAAAA,GACA,MAAAxD,GAAA4G,OAAApD,EAAAqD,KAlNA,GAAAkP,GAAA5Y,EAAA,0BACA8K,EAAA9K,EAAA,0BACA6Y,EAAA7Y,EAAA,yBACA+F,EAAA/F,EAAA,yBACAqK,EAAArK,EAAA,4BACA8Y,EAAA9Y,EAAA,4BACA+M,EAAA/M,EAAA,6BACA8E,EAAA9E,EAAA,yBACA+Y,EAAA/Y,EAAA,uCACA0H,EAAA1H,EAAA,sBAEA6C,GAAAiI,EAAA+N,EAAA9S,EAAA+S,EAAA/L,EACA1C,EAAAvF,EAAA8T,EAAAG,GACArU,OAAA,SAAA7B,EAAAmW,GACA,MAAAA,GAAAnW,IACA7C,EAAA,kBAEAe,EAAAf,EAAA,eAAA6C,EAqMA,OAjMAhD,GAAAsE,QAAAA,EACAtE,EAAA8B,QAAAkB,EAAAlB,QACA9B,EAAA+B,OAAAiB,EAAAjB,OAEA/B,EAAAsC,KAAAA,EACAtC,EAAA,OAAA4E,EACA5E,EAAA4E,QAAAA,EAEA5E,EAAA2M,QAAA3J,EAAA2J,QACA3M,EAAA4M,OAAA5J,EAAA4J,OAEA5M,EAAAoK,KAAAA,EAEApK,EAAAiD,IAAAA,EACAjD,EAAAuG,OAAAA,EAEAvG,EAAA4I,IAAAtG,EAAAU,EAAA4F,KACA5I,EAAAqJ,KAAA/G,EAAAU,EAAAqG,MACArJ,EAAA0T,KAAApR,EAAAU,EAAA0Q,MAEA1T,EAAAmG,IAAAA,EACAnG,EAAA4J,OAAAA,EACA5J,EAAA6E,OAAAvC,EAAAU,EAAA6B,QACA7E,EAAA2K,YAAArI,EAAAU,EAAA2H,aAEA3K,EAAA6Y,cAAAA,EAEA7Y,EAAAgD,QAAAA,EACAhD,EAAAyS,MAAAA,EAIAzS,EAAA6H,aAAAA,EAiKA7H,KAEA,kBAAAiB,IAAAA,EAAAgD,IAAAhD,EAAA,SAAAiD,GAAAjE,EAAAC,QAAAgE,EAAA/D;A/DnOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACtQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACtDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC3BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACxGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACxEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AClHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AChFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACvDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC3SA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AChKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC3BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACjEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC9EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACtFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACtCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACzEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACxDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC37BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACnCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC1RA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AClDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AClHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC9CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC3BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "generated.js", "sourceRoot": "https://raw.githubusercontent.com/cujojs/when/5c0a9ebaaf9bc859e76bd9584a9c9677e1e18f08", "sourcesContent": ["var when = module.exports = require('../when');\n\nwhen.callbacks = require('../callbacks');\nwhen.cancelable = require('../cancelable');\nwhen.delay = require('../delay');\nwhen.fn = require('../function');\nwhen.guard = require('../guard');\nwhen.keys = require('../keys');\nwhen.nodefn = when.node = require('../node');\nwhen.parallel = require('../parallel');\nwhen.pipeline = require('../pipeline');\nwhen.poll = require('../poll');\nwhen.sequence = require('../sequence');\nwhen.timeout = require('../timeout');\n", "/** @license MIT License (c) copyright 2013-2014 original author or authors */\n\n/**\n * Collection of helper functions for interacting with 'traditional',\n * callback-taking functions using a promise interface.\n *\n * <AUTHOR>\n * @contributor <PERSON>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar Promise = when.Promise;\n\tvar _liftAll = require('./lib/liftAll');\n\tvar slice = Array.prototype.slice;\n\n\tvar makeApply = require('./lib/apply');\n\tvar _apply = makeApply(Promise, dispatch);\n\n\treturn {\n\t\tlift: lift,\n\t\tliftAll: liftAll,\n\t\tapply: apply,\n\t\tcall: call,\n\t\tpromisify: promisify\n\t};\n\n\t/**\n\t * Takes a `traditional` callback-taking function and returns a promise for its\n\t * result, accepting an optional array of arguments (that might be values or\n\t * promises). It assumes that the function takes its callback and errback as\n\t * the last two arguments. The resolution of the promise depends on whether the\n\t * function will call its callback or its errback.\n\t *\n\t * @example\n\t *    var domIsLoaded = callbacks.apply($);\n\t *    domIsLoaded.then(function() {\n\t *\t\tdoMyDomStuff();\n\t *\t});\n\t *\n\t * @example\n\t *    function existingAjaxyFunction(url, callback, errback) {\n\t *\t\t// Complex logic you'd rather not change\n\t *\t}\n\t *\n\t *    var promise = callbacks.apply(existingAjaxyFunction, [\"/movies.json\"]);\n\t *\n\t *    promise.then(function(movies) {\n\t *\t\t// Work with movies\n\t *\t}, function(reason) {\n\t *\t\t// Handle error\n\t *\t});\n\t *\n\t * @param {function} asyncFunction function to be called\n\t * @param {Array} [extraAsyncArgs] array of arguments to asyncFunction\n\t * @returns {Promise} promise for the callback value of asyncFunction\n\t */\n\tfunction apply(asyncFunction, extraAsyncArgs) {\n\t\treturn _apply(asyncFunction, this, extraAsyncArgs || []);\n\t}\n\n\t/**\n\t * Apply helper that allows specifying thisArg\n\t * @private\n\t */\n\tfunction dispatch(f, thisArg, args, h) {\n\t\targs.push(alwaysUnary(h.resolve, h), alwaysUnary(h.reject, h));\n\t\ttryCatchResolve(f, thisArg, args, h);\n\t}\n\n\tfunction tryCatchResolve(f, thisArg, args, resolver) {\n\t\ttry {\n\t\t\tf.apply(thisArg, args);\n\t\t} catch(e) {\n\t\t\tresolver.reject(e);\n\t\t}\n\t}\n\n\t/**\n\t * Works as `callbacks.apply` does, with the difference that the arguments to\n\t * the function are passed individually, instead of as an array.\n\t *\n\t * @example\n\t *    function sumInFiveSeconds(a, b, callback) {\n\t *\t\tsetTimeout(function() {\n\t *\t\t\tcallback(a + b);\n\t *\t\t}, 5000);\n\t *\t}\n\t *\n\t *    var sumPromise = callbacks.call(sumInFiveSeconds, 5, 10);\n\t *\n\t *    // Logs '15' 5 seconds later\n\t *    sumPromise.then(console.log);\n\t *\n\t * @param {function} asyncFunction function to be called\n\t * @param {...*} args arguments that will be forwarded to the function\n\t * @returns {Promise} promise for the callback value of asyncFunction\n\t */\n\tfunction call(asyncFunction/*, arg1, arg2...*/) {\n\t\treturn _apply(asyncFunction, this, slice.call(arguments, 1));\n\t}\n\n\t/**\n\t * Takes a 'traditional' callback/errback-taking function and returns a function\n\t * that returns a promise instead. The resolution/rejection of the promise\n\t * depends on whether the original function will call its callback or its\n\t * errback.\n\t *\n\t * If additional arguments are passed to the `lift` call, they will be prepended\n\t * on the calls to the original function, much like `Function.prototype.bind`.\n\t *\n\t * The resulting function is also \"promise-aware\", in the sense that, if given\n\t * promises as arguments, it will wait for their resolution before executing.\n\t *\n\t * @example\n\t *    function traditionalAjax(method, url, callback, errback) {\n\t *\t\tvar xhr = new XMLHttpRequest();\n\t *\t\txhr.open(method, url);\n\t *\n\t *\t\txhr.onload = callback;\n\t *\t\txhr.onerror = errback;\n\t *\n\t *\t\txhr.send();\n\t *\t}\n\t *\n\t *    var promiseAjax = callbacks.lift(traditionalAjax);\n\t *    promiseAjax(\"GET\", \"/movies.json\").then(console.log, console.error);\n\t *\n\t *    var promiseAjaxGet = callbacks.lift(traditionalAjax, \"GET\");\n\t *    promiseAjaxGet(\"/movies.json\").then(console.log, console.error);\n\t *\n\t * @param {Function} f traditional async function to be decorated\n\t * @param {...*} [args] arguments to be prepended for the new function @deprecated\n\t * @returns {Function} a promise-returning function\n\t */\n\tfunction lift(f/*, args...*/) {\n\t\tvar args = arguments.length > 1 ? slice.call(arguments, 1) : [];\n\t\treturn function() {\n\t\t\treturn _apply(f, this, args.concat(slice.call(arguments)));\n\t\t};\n\t}\n\n\t/**\n\t * Lift all the functions/methods on src\n\t * @param {object|function} src source whose functions will be lifted\n\t * @param {function?} combine optional function for customizing the lifting\n\t *  process. It is passed dst, the lifted function, and the property name of\n\t *  the original function on src.\n\t * @param {(object|function)?} dst option destination host onto which to place lifted\n\t *  functions. If not provided, liftAll returns a new object.\n\t * @returns {*} If dst is provided, returns dst with lifted functions as\n\t *  properties.  If dst not provided, returns a new object with lifted functions.\n\t */\n\tfunction liftAll(src, combine, dst) {\n\t\treturn _liftAll(lift, combine, dst, src);\n\t}\n\n\t/**\n\t * `promisify` is a version of `lift` that allows fine-grained control over the\n\t * arguments that passed to the underlying function. It is intended to handle\n\t * functions that don't follow the common callback and errback positions.\n\t *\n\t * The control is done by passing an object whose 'callback' and/or 'errback'\n\t * keys, whose values are the corresponding 0-based indexes of the arguments on\n\t * the function. Negative values are interpreted as being relative to the end\n\t * of the arguments array.\n\t *\n\t * If arguments are given on the call to the 'promisified' function, they are\n\t * intermingled with the callback and errback. If a promise is given among them,\n\t * the execution of the function will only occur after its resolution.\n\t *\n\t * @example\n\t *    var delay = callbacks.promisify(setTimeout, {\n\t *\t\tcallback: 0\n\t *\t});\n\t *\n\t *    delay(100).then(function() {\n\t *\t\tconsole.log(\"This happens 100ms afterwards\");\n\t *\t});\n\t *\n\t * @example\n\t *    function callbackAsLast(errback, followsStandards, callback) {\n\t *\t\tif(followsStandards) {\n\t *\t\t\tcallback(\"well done!\");\n\t *\t\t} else {\n\t *\t\t\terrback(\"some programmers just want to watch the world burn\");\n\t *\t\t}\n\t *\t}\n\t *\n\t *    var promisified = callbacks.promisify(callbackAsLast, {\n\t *\t\tcallback: -1,\n\t *\t\terrback:   0,\n\t *\t});\n\t *\n\t *    promisified(true).then(console.log, console.error);\n\t *    promisified(false).then(console.log, console.error);\n\t *\n\t * @param {Function} asyncFunction traditional function to be decorated\n\t * @param {object} positions\n\t * @param {number} [positions.callback] index at which asyncFunction expects to\n\t *  receive a success callback\n\t * @param {number} [positions.errback] index at which asyncFunction expects to\n\t *  receive an error callback\n\t *  @returns {function} promisified function that accepts\n\t *\n\t * @deprecated\n\t */\n\tfunction promisify(asyncFunction, positions) {\n\n\t\treturn function() {\n\t\t\tvar thisArg = this;\n\t\t\treturn Promise.all(arguments).then(function(args) {\n\t\t\t\tvar p = Promise._defer();\n\n\t\t\t\tvar callbackPos, errbackPos;\n\n\t\t\t\tif(typeof positions.callback === 'number') {\n\t\t\t\t\tcallbackPos = normalizePosition(args, positions.callback);\n\t\t\t\t}\n\n\t\t\t\tif(typeof positions.errback === 'number') {\n\t\t\t\t\terrbackPos = normalizePosition(args, positions.errback);\n\t\t\t\t}\n\n\t\t\t\tif(errbackPos < callbackPos) {\n\t\t\t\t\tinsertCallback(args, errbackPos, p._handler.reject, p._handler);\n\t\t\t\t\tinsertCallback(args, callbackPos, p._handler.resolve, p._handler);\n\t\t\t\t} else {\n\t\t\t\t\tinsertCallback(args, callbackPos, p._handler.resolve, p._handler);\n\t\t\t\t\tinsertCallback(args, errbackPos, p._handler.reject, p._handler);\n\t\t\t\t}\n\n\t\t\t\tasyncFunction.apply(thisArg, args);\n\n\t\t\t\treturn p;\n\t\t\t});\n\t\t};\n\t}\n\n\tfunction normalizePosition(args, pos) {\n\t\treturn pos < 0 ? (args.length + pos + 2) : pos;\n\t}\n\n\tfunction insertCallback(args, pos, callback, thisArg) {\n\t\tif(typeof pos === 'number') {\n\t\t\targs.splice(pos, 0, alwaysUnary(callback, thisArg));\n\t\t}\n\t}\n\n\tfunction alwaysUnary(fn, thisArg) {\n\t\treturn function() {\n\t\t\tif (arguments.length > 1) {\n\t\t\t\tfn.call(thisArg, slice.call(arguments));\n\t\t\t} else {\n\t\t\t\tfn.apply(thisArg, arguments);\n\t\t\t}\n\t\t};\n\t}\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright B Cavalier & <PERSON> */\n\n/**\n * cancelable.js\n * @deprecated\n *\n * Decorator that makes a deferred \"cancelable\".  It adds a cancel() method that\n * will call a special cancel handler function and then reject the deferred.  The\n * cancel handler can be used to do resource cleanup, or anything else that should\n * be done before any other rejection handlers are executed.\n *\n * Usage:\n *\n * var cancelableDeferred = cancelable(when.defer(), myCancelHandler);\n *\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function() {\n\n    /**\n     * Makes deferred cancelable, adding a cancel() method.\n\t * @deprecated\n     *\n     * @param deferred {Deferred} the {@link Deferred} to make cancelable\n     * @param canceler {Function} cancel handler function to execute when this deferred\n\t * is canceled.  This is guaranteed to run before all other rejection handlers.\n\t * The canceler will NOT be executed if the deferred is rejected in the standard\n\t * way, i.e. deferred.reject().  It ONLY executes if the deferred is canceled,\n\t * i.e. deferred.cancel()\n     *\n     * @returns deferred, with an added cancel() method.\n     */\n    return function(deferred, canceler) {\n        // Add a cancel method to the deferred to reject the delegate\n        // with the special canceled indicator.\n        deferred.cancel = function() {\n\t\t\ttry {\n\t\t\t\tdeferred.reject(canceler(deferred));\n\t\t\t} catch(e) {\n\t\t\t\tdeferred.reject(e);\n\t\t\t}\n\n\t\t\treturn deferred.promise;\n        };\n\n        return deferred;\n    };\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * delay.js\n *\n * Helper that returns a promise that resolves after a delay.\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\n    /**\n\t * @deprecated Use when(value).delay(ms)\n     */\n    return function delay(msec, value) {\n\t\treturn when(value).delay(msec);\n    };\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2013-2014 original author or authors */\n\n/**\n * Collection of helper functions for wrapping and executing 'traditional'\n * synchronous functions in a promise interface.\n *\n * <AUTHOR>\n * @contributor <PERSON><PERSON>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar attempt = when['try'];\n\tvar _liftAll = require('./lib/liftAll');\n\tvar _apply = require('./lib/apply')(when.Promise);\n\tvar slice = Array.prototype.slice;\n\n\treturn {\n\t\tlift: lift,\n\t\tliftAll: liftAll,\n\t\tcall: attempt,\n\t\tapply: apply,\n\t\tcompose: compose\n\t};\n\n\t/**\n\t * Takes a function and an optional array of arguments (that might be promises),\n\t * and calls the function. The return value is a promise whose resolution\n\t * depends on the value returned by the function.\n\t * @param {function} f function to be called\n\t * @param {Array} [args] array of arguments to func\n\t * @returns {Promise} promise for the return value of func\n\t */\n\tfunction apply(f, args) {\n\t\t// slice args just in case the caller passed an Arguments instance\n\t\treturn _apply(f, this, args == null ? [] : slice.call(args));\n\t}\n\n\t/**\n\t * Takes a 'regular' function and returns a version of that function that\n\t * returns a promise instead of a plain value, and handles thrown errors by\n\t * returning a rejected promise. Also accepts a list of arguments to be\n\t * prepended to the new function, as does Function.prototype.bind.\n\t *\n\t * The resulting function is promise-aware, in the sense that it accepts\n\t * promise arguments, and waits for their resolution.\n\t * @param {Function} f function to be bound\n\t * @param {...*} [args] arguments to be prepended for the new function @deprecated\n\t * @returns {Function} a promise-returning function\n\t */\n\tfunction lift(f /*, args... */) {\n\t\tvar args = arguments.length > 1 ? slice.call(arguments, 1) : [];\n\t\treturn function() {\n\t\t\treturn _apply(f, this, args.concat(slice.call(arguments)));\n\t\t};\n\t}\n\n\t/**\n\t * Lift all the functions/methods on src\n\t * @param {object|function} src source whose functions will be lifted\n\t * @param {function?} combine optional function for customizing the lifting\n\t *  process. It is passed dst, the lifted function, and the property name of\n\t *  the original function on src.\n\t * @param {(object|function)?} dst option destination host onto which to place lifted\n\t *  functions. If not provided, liftAll returns a new object.\n\t * @returns {*} If dst is provided, returns dst with lifted functions as\n\t *  properties.  If dst not provided, returns a new object with lifted functions.\n\t */\n\tfunction liftAll(src, combine, dst) {\n\t\treturn _liftAll(lift, combine, dst, src);\n\t}\n\n\t/**\n\t * Composes multiple functions by piping their return values. It is\n\t * transparent to whether the functions return 'regular' values or promises:\n\t * the piped argument is always a resolved value. If one of the functions\n\t * throws or returns a rejected promise, the composed promise will be also\n\t * rejected.\n\t *\n\t * The arguments (or promises to arguments) given to the returned function (if\n\t * any), are passed directly to the first function on the 'pipeline'.\n\t * @param {Function} f the function to which the arguments will be passed\n\t * @param {...Function} [funcs] functions that will be composed, in order\n\t * @returns {Function} a promise-returning composition of the functions\n\t */\n\tfunction compose(f /*, funcs... */) {\n\t\tvar funcs = slice.call(arguments, 1);\n\n\t\treturn function() {\n\t\t\tvar thisArg = this;\n\t\t\tvar args = slice.call(arguments);\n\t\t\tvar firstPromise = attempt.apply(thisArg, [f].concat(args));\n\n\t\t\treturn when.reduce(funcs, function(arg, func) {\n\t\t\t\treturn func.call(thisArg, arg);\n\t\t\t}, firstPromise);\n\t\t};\n\t}\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * Generalized promise concurrency guard\n * Adapted from original concept by <PERSON><PERSON> (Rocket Pack, Ltd.)\n *\n * <AUTHOR>\n * <AUTHOR>\n * @contributor <PERSON><PERSON>\n */\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar slice = Array.prototype.slice;\n\n\tguard.n = n;\n\n\treturn guard;\n\n\t/**\n\t * Creates a guarded version of f that can only be entered when the supplied\n\t * condition allows.\n\t * @param {function} condition represents a critical section that may only\n\t *  be entered when allowed by the condition\n\t * @param {function} f function to guard\n\t * @returns {function} guarded version of f\n\t */\n\tfunction guard(condition, f) {\n\t\treturn function() {\n\t\t\tvar args = slice.call(arguments);\n\n\t\t\treturn when(condition()).withThis(this).then(function(exit) {\n\t\t\t\treturn when(f.apply(this, args))['finally'](exit);\n\t\t\t});\n\t\t};\n\t}\n\n\t/**\n\t * Creates a condition that allows only n simultaneous executions\n\t * of a guarded function\n\t * @param {number} allowed number of allowed simultaneous executions\n\t * @returns {function} condition function which returns a promise that\n\t *  fulfills when the critical section may be entered.  The fulfillment\n\t *  value is a function (\"notifyExit\") that must be called when the critical\n\t *  section has been exited.\n\t */\n\tfunction n(allowed) {\n\t\tvar count = 0;\n\t\tvar waiting = [];\n\n\t\treturn function enter() {\n\t\t\treturn when.promise(function(resolve) {\n\t\t\t\tif(count < allowed) {\n\t\t\t\t\tresolve(exit);\n\t\t\t\t} else {\n\t\t\t\t\twaiting.push(resolve);\n\t\t\t\t}\n\t\t\t\tcount += 1;\n\t\t\t});\n\t\t};\n\n\t\tfunction exit() {\n\t\t\tcount = Math.max(count - 1, 0);\n\t\t\tif(waiting.length > 0) {\n\t\t\t\twaiting.shift()(exit);\n\t\t\t}\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * Licensed under the MIT License at:\n * http://www.opensource.org/licenses/mit-license.php\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar Promise = when.Promise;\n\tvar toPromise = when.resolve;\n\n\treturn {\n\t\tall: when.lift(all),\n\t\tmap: map,\n\t\tsettle: settle\n\t};\n\n\t/**\n\t * Resolve all the key-value pairs in the supplied object or promise\n\t * for an object.\n\t * @param {Promise|object} object or promise for object whose key-value pairs\n\t *  will be resolved\n\t * @returns {Promise} promise for an object with the fully resolved key-value pairs\n\t */\n\tfunction all(object) {\n\t\tvar p = Promise._defer();\n\t\tvar resolver = Promise._handler(p);\n\n\t\tvar results = {};\n\t\tvar keys = Object.keys(object);\n\t\tvar pending = keys.length;\n\n\t\tfor(var i=0, k; i<keys.length; ++i) {\n\t\t\tk = keys[i];\n\t\t\tPromise._handler(object[k]).fold(settleKey, k, results, resolver);\n\t\t}\n\n\t\tif(pending === 0) {\n\t\t\tresolver.resolve(results);\n\t\t}\n\n\t\treturn p;\n\n\t\tfunction settleKey(k, x, resolver) {\n\t\t\t/*jshint validthis:true*/\n\t\t\tthis[k] = x;\n\t\t\tif(--pending === 0) {\n\t\t\t\tresolver.resolve(results);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Map values in the supplied object's keys\n\t * @param {Promise|object} object or promise for object whose key-value pairs\n\t *  will be reduced\n\t * @param {function(value:*, key:String):*} f mapping function which may\n\t *  return either a promise or a value\n\t * @returns {Promise} promise for an object with the mapped and fully\n\t *  resolved key-value pairs\n\t */\n\tfunction map(object, f) {\n\t\treturn toPromise(object).then(function(object) {\n\t\t\treturn all(Object.keys(object).reduce(function(o, k) {\n\t\t\t\to[k] = toPromise(object[k]).fold(mapWithKey, k);\n\t\t\t\treturn o;\n\t\t\t}, {}));\n\t\t});\n\n\t\tfunction mapWithKey(k, x) {\n\t\t\treturn f(x, k);\n\t\t}\n\t}\n\n\t/**\n\t * Resolve all key-value pairs in the supplied object and return a promise\n\t * that will always fulfill with the outcome states of all input promises.\n\t * @param {object} object whose key-value pairs will be settled\n\t * @returns {Promise} promise for an object with the mapped and fully\n\t *  settled key-value pairs\n\t */\n\tfunction settle(object) {\n\t\tvar keys = Object.keys(object);\n\t\tvar results = {};\n\n\t\tif(keys.length === 0) {\n\t\t\treturn toPromise(results);\n\t\t}\n\n\t\tvar p = Promise._defer();\n\t\tvar resolver = Promise._handler(p);\n\t\tvar promises = keys.map(function(k) { return object[k]; });\n\n\t\twhen.settle(promises).then(function(states) {\n\t\t\tpopulateResults(keys, states, results, resolver);\n\t\t});\n\n\t\treturn p;\n\t}\n\n\tfunction populateResults(keys, states, results, resolver) {\n\t\tfor(var i=0; i<keys.length; i++) {\n\t\t\tresults[keys[i]] = states[i];\n\t\t}\n\t\tresolver.resolve(results);\n\t}\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function (require) {\n\n\tvar makePromise = require('./makePromise');\n\tvar Scheduler = require('./Scheduler');\n\tvar async = require('./env').asap;\n\n\treturn makePromise({\n\t\tscheduler: new Scheduler(async)\n\t});\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\t// Credit to Twisol (https://github.com/Twisol) for suggesting\n\t// this type of extensible queue + trampoline approach for next-tick conflation.\n\n\t/**\n\t * Async task scheduler\n\t * @param {function} async function to schedule a single async function\n\t * @constructor\n\t */\n\tfunction Scheduler(async) {\n\t\tthis._async = async;\n\t\tthis._running = false;\n\n\t\tthis._queue = this;\n\t\tthis._queueLen = 0;\n\t\tthis._afterQueue = {};\n\t\tthis._afterQueueLen = 0;\n\n\t\tvar self = this;\n\t\tthis.drain = function() {\n\t\t\tself._drain();\n\t\t};\n\t}\n\n\t/**\n\t * Enqueue a task\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.enqueue = function(task) {\n\t\tthis._queue[this._queueLen++] = task;\n\t\tthis.run();\n\t};\n\n\t/**\n\t * Enqueue a task to run after the main task queue\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.afterQueue = function(task) {\n\t\tthis._afterQueue[this._afterQueueLen++] = task;\n\t\tthis.run();\n\t};\n\n\tScheduler.prototype.run = function() {\n\t\tif (!this._running) {\n\t\t\tthis._running = true;\n\t\t\tthis._async(this.drain);\n\t\t}\n\t};\n\n\t/**\n\t * Drain the handler queue entirely, and then the after queue\n\t */\n\tScheduler.prototype._drain = function() {\n\t\tvar i = 0;\n\t\tfor (; i < this._queueLen; ++i) {\n\t\t\tthis._queue[i].run();\n\t\t\tthis._queue[i] = void 0;\n\t\t}\n\n\t\tthis._queueLen = 0;\n\t\tthis._running = false;\n\n\t\tfor (i = 0; i < this._afterQueueLen; ++i) {\n\t\t\tthis._afterQueue[i].run();\n\t\t\tthis._afterQueue[i] = void 0;\n\t\t}\n\n\t\tthis._afterQueueLen = 0;\n\t};\n\n\treturn Scheduler;\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\t/**\n\t * Custom error type for promises rejected by promise.timeout\n\t * @param {string} message\n\t * @constructor\n\t */\n\tfunction TimeoutError (message) {\n\t\tError.call(this);\n\t\tthis.message = message;\n\t\tthis.name = TimeoutError.name;\n\t\tif (typeof Error.captureStackTrace === 'function') {\n\t\t\tError.captureStackTrace(this, TimeoutError);\n\t\t}\n\t}\n\n\tTimeoutError.prototype = Object.create(Error.prototype);\n\tTimeoutError.prototype.constructor = TimeoutError;\n\n\treturn TimeoutError;\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\tmakeApply.tryCatchResolve = tryCatchResolve;\n\n\treturn makeApply;\n\n\tfunction makeApply(Promise, call) {\n\t\tif(arguments.length < 2) {\n\t\t\tcall = tryCatchResolve;\n\t\t}\n\n\t\treturn apply;\n\n\t\tfunction apply(f, thisArg, args) {\n\t\t\tvar p = Promise._defer();\n\t\t\tvar l = args.length;\n\t\t\tvar params = new Array(l);\n\t\t\tcallAndResolve({ f:f, thisArg:thisArg, args:args, params:params, i:l-1, call:call }, p._handler);\n\n\t\t\treturn p;\n\t\t}\n\n\t\tfunction callAndResolve(c, h) {\n\t\t\tif(c.i < 0) {\n\t\t\t\treturn call(c.f, c.thisArg, c.params, h);\n\t\t\t}\n\n\t\t\tvar handler = Promise._handler(c.args[c.i]);\n\t\t\thandler.fold(callAndResolveNext, c, void 0, h);\n\t\t}\n\n\t\tfunction callAndResolveNext(c, x, h) {\n\t\t\tc.params[c.i] = x;\n\t\t\tc.i -= 1;\n\t\t\tcallAndResolve(c, h);\n\t\t}\n\t}\n\n\tfunction tryCatchResolve(f, thisArg, args, resolver) {\n\t\ttry {\n\t\t\tresolver.resolve(f.apply(thisArg, args));\n\t\t} catch(e) {\n\t\t\tresolver.reject(e);\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n\n\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar state = require('../state');\n\tvar applier = require('../apply');\n\n\treturn function array(Promise) {\n\n\t\tvar applyFold = applier(Promise);\n\t\tvar toPromise = Promise.resolve;\n\t\tvar all = Promise.all;\n\n\t\tvar ar = Array.prototype.reduce;\n\t\tvar arr = Array.prototype.reduceRight;\n\t\tvar slice = Array.prototype.slice;\n\n\t\t// Additional array combinators\n\n\t\tPromise.any = any;\n\t\tPromise.some = some;\n\t\tPromise.settle = settle;\n\n\t\tPromise.map = map;\n\t\tPromise.filter = filter;\n\t\tPromise.reduce = reduce;\n\t\tPromise.reduceRight = reduceRight;\n\n\t\t/**\n\t\t * When this promise fulfills with an array, do\n\t\t * onFulfilled.apply(void 0, array)\n\t\t * @param {function} onFulfilled function to apply\n\t\t * @returns {Promise} promise for the result of applying onFulfilled\n\t\t */\n\t\tPromise.prototype.spread = function(onFulfilled) {\n\t\t\treturn this.then(all).then(function(array) {\n\t\t\t\treturn onFulfilled.apply(this, array);\n\t\t\t});\n\t\t};\n\n\t\treturn Promise;\n\n\t\t/**\n\t\t * One-winner competitive race.\n\t\t * Return a promise that will fulfill when one of the promises\n\t\t * in the input array fulfills, or will reject when all promises\n\t\t * have rejected.\n\t\t * @param {array} promises\n\t\t * @returns {Promise} promise for the first fulfilled value\n\t\t */\n\t\tfunction any(promises) {\n\t\t\tvar p = Promise._defer();\n\t\t\tvar resolver = p._handler;\n\t\t\tvar l = promises.length>>>0;\n\n\t\t\tvar pending = l;\n\t\t\tvar errors = [];\n\n\t\t\tfor (var h, x, i = 0; i < l; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif(x === void 0 && !(i in promises)) {\n\t\t\t\t\t--pending;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\th = Promise._handler(x);\n\t\t\t\tif(h.state() > 0) {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tPromise._visitRemaining(promises, i, h);\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\th.visit(resolver, handleFulfill, handleReject);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif(pending === 0) {\n\t\t\t\tresolver.reject(new RangeError('any(): array must not be empty'));\n\t\t\t}\n\n\t\t\treturn p;\n\n\t\t\tfunction handleFulfill(x) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\terrors = null;\n\t\t\t\tthis.resolve(x); // this === resolver\n\t\t\t}\n\n\t\t\tfunction handleReject(e) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\tif(this.resolved) { // this === resolver\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\terrors.push(e);\n\t\t\t\tif(--pending === 0) {\n\t\t\t\t\tthis.reject(errors);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * N-winner competitive race\n\t\t * Return a promise that will fulfill when n input promises have\n\t\t * fulfilled, or will reject when it becomes impossible for n\n\t\t * input promises to fulfill (ie when promises.length - n + 1\n\t\t * have rejected)\n\t\t * @param {array} promises\n\t\t * @param {number} n\n\t\t * @returns {Promise} promise for the earliest n fulfillment values\n\t\t *\n\t\t * @deprecated\n\t\t */\n\t\tfunction some(promises, n) {\n\t\t\t/*jshint maxcomplexity:7*/\n\t\t\tvar p = Promise._defer();\n\t\t\tvar resolver = p._handler;\n\n\t\t\tvar results = [];\n\t\t\tvar errors = [];\n\n\t\t\tvar l = promises.length>>>0;\n\t\t\tvar nFulfill = 0;\n\t\t\tvar nReject;\n\t\t\tvar x, i; // reused in both for() loops\n\n\t\t\t// First pass: count actual array items\n\t\t\tfor(i=0; i<l; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif(x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t++nFulfill;\n\t\t\t}\n\n\t\t\t// Compute actual goals\n\t\t\tn = Math.max(n, 0);\n\t\t\tnReject = (nFulfill - n + 1);\n\t\t\tnFulfill = Math.min(n, nFulfill);\n\n\t\t\tif(n > nFulfill) {\n\t\t\t\tresolver.reject(new RangeError('some(): array must contain at least '\n\t\t\t\t+ n + ' item(s), but had ' + nFulfill));\n\t\t\t} else if(nFulfill === 0) {\n\t\t\t\tresolver.resolve(results);\n\t\t\t}\n\n\t\t\t// Second pass: observe each array item, make progress toward goals\n\t\t\tfor(i=0; i<l; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif(x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tPromise._handler(x).visit(resolver, fulfill, reject, resolver.notify);\n\t\t\t}\n\n\t\t\treturn p;\n\n\t\t\tfunction fulfill(x) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\tif(this.resolved) { // this === resolver\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tresults.push(x);\n\t\t\t\tif(--nFulfill === 0) {\n\t\t\t\t\terrors = null;\n\t\t\t\t\tthis.resolve(results);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction reject(e) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\tif(this.resolved) { // this === resolver\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\terrors.push(e);\n\t\t\t\tif(--nReject === 0) {\n\t\t\t\t\tresults = null;\n\t\t\t\t\tthis.reject(errors);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Apply f to the value of each promise in a list of promises\n\t\t * and return a new list containing the results.\n\t\t * @param {array} promises\n\t\t * @param {function(x:*, index:Number):*} f mapping function\n\t\t * @returns {Promise}\n\t\t */\n\t\tfunction map(promises, f) {\n\t\t\treturn Promise._traverse(f, promises);\n\t\t}\n\n\t\t/**\n\t\t * Filter the provided array of promises using the provided predicate.  Input may\n\t\t * contain promises and values\n\t\t * @param {Array} promises array of promises and values\n\t\t * @param {function(x:*, index:Number):boolean} predicate filtering predicate.\n\t\t *  Must return truthy (or promise for truthy) for items to retain.\n\t\t * @returns {Promise} promise that will fulfill with an array containing all items\n\t\t *  for which predicate returned truthy.\n\t\t */\n\t\tfunction filter(promises, predicate) {\n\t\t\tvar a = slice.call(promises);\n\t\t\treturn Promise._traverse(predicate, a).then(function(keep) {\n\t\t\t\treturn filterSync(a, keep);\n\t\t\t});\n\t\t}\n\n\t\tfunction filterSync(promises, keep) {\n\t\t\t// Safe because we know all promises have fulfilled if we've made it this far\n\t\t\tvar l = keep.length;\n\t\t\tvar filtered = new Array(l);\n\t\t\tfor(var i=0, j=0; i<l; ++i) {\n\t\t\t\tif(keep[i]) {\n\t\t\t\t\tfiltered[j++] = Promise._handler(promises[i]).value;\n\t\t\t\t}\n\t\t\t}\n\t\t\tfiltered.length = j;\n\t\t\treturn filtered;\n\n\t\t}\n\n\t\t/**\n\t\t * Return a promise that will always fulfill with an array containing\n\t\t * the outcome states of all input promises.  The returned promise\n\t\t * will never reject.\n\t\t * @param {Array} promises\n\t\t * @returns {Promise} promise for array of settled state descriptors\n\t\t */\n\t\tfunction settle(promises) {\n\t\t\treturn all(promises.map(settleOne));\n\t\t}\n\n\t\tfunction settleOne(p) {\n\t\t\t// Optimize the case where we get an already-resolved when.js promise\n\t\t\t//  by extracting its state:\n\t\t\tvar handler;\n\t\t\tif (p instanceof Promise) {\n\t\t\t\t// This is our own Promise type and we can reach its handler internals:\n\t\t\t\thandler = p._handler.join();\n\t\t\t}\n\t\t\tif((handler && handler.state() === 0) || !handler) {\n\t\t\t\t// Either still pending, or not a Promise at all:\n\t\t\t\treturn toPromise(p).then(state.fulfilled, state.rejected);\n\t\t\t}\n\n\t\t\t// The promise is our own, but it is already resolved. Take a shortcut.\n\t\t\t// Since we're not actually handling the resolution, we need to disable\n\t\t\t// rejection reporting.\n\t\t\thandler._unreport();\n\t\t\treturn state.inspect(handler);\n\t\t}\n\n\t\t/**\n\t\t * Traditional reduce function, similar to `Array.prototype.reduce()`, but\n\t\t * input may contain promises and/or values, and reduceFunc\n\t\t * may return either a value or a promise, *and* initialValue may\n\t\t * be a promise for the starting value.\n\t\t * @param {Array|Promise} promises array or promise for an array of anything,\n\t\t *      may contain a mix of promises and values.\n\t\t * @param {function(accumulated:*, x:*, index:Number):*} f reduce function\n\t\t * @returns {Promise} that will resolve to the final reduced value\n\t\t */\n\t\tfunction reduce(promises, f /*, initialValue */) {\n\t\t\treturn arguments.length > 2 ? ar.call(promises, liftCombine(f), arguments[2])\n\t\t\t\t\t: ar.call(promises, liftCombine(f));\n\t\t}\n\n\t\t/**\n\t\t * Traditional reduce function, similar to `Array.prototype.reduceRight()`, but\n\t\t * input may contain promises and/or values, and reduceFunc\n\t\t * may return either a value or a promise, *and* initialValue may\n\t\t * be a promise for the starting value.\n\t\t * @param {Array|Promise} promises array or promise for an array of anything,\n\t\t *      may contain a mix of promises and values.\n\t\t * @param {function(accumulated:*, x:*, index:Number):*} f reduce function\n\t\t * @returns {Promise} that will resolve to the final reduced value\n\t\t */\n\t\tfunction reduceRight(promises, f /*, initialValue */) {\n\t\t\treturn arguments.length > 2 ? arr.call(promises, liftCombine(f), arguments[2])\n\t\t\t\t\t: arr.call(promises, liftCombine(f));\n\t\t}\n\n\t\tfunction liftCombine(f) {\n\t\t\treturn function(z, x, i) {\n\t\t\t\treturn applyFold(f, void 0, [z,x,i]);\n\t\t\t};\n\t\t}\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function flow(Promise) {\n\n\t\tvar resolve = Promise.resolve;\n\t\tvar reject = Promise.reject;\n\t\tvar origCatch = Promise.prototype['catch'];\n\n\t\t/**\n\t\t * Handle the ultimate fulfillment value or rejection reason, and assume\n\t\t * responsibility for all errors.  If an error propagates out of result\n\t\t * or handleFatalError, it will be rethrown to the host, resulting in a\n\t\t * loud stack track on most platforms and a crash on some.\n\t\t * @param {function?} onResult\n\t\t * @param {function?} onError\n\t\t * @returns {undefined}\n\t\t */\n\t\tPromise.prototype.done = function(onResult, onError) {\n\t\t\tthis._handler.visit(this._handler.receiver, onResult, onError);\n\t\t};\n\n\t\t/**\n\t\t * Add Error-type and predicate matching to catch.  Examples:\n\t\t * promise.catch(TypeError, handleTypeError)\n\t\t *   .catch(predicate, handleMatchedErrors)\n\t\t *   .catch(handleRemainingErrors)\n\t\t * @param onRejected\n\t\t * @returns {*}\n\t\t */\n\t\tPromise.prototype['catch'] = Promise.prototype.otherwise = function(onRejected) {\n\t\t\tif (arguments.length < 2) {\n\t\t\t\treturn origCatch.call(this, onRejected);\n\t\t\t}\n\n\t\t\tif(typeof onRejected !== 'function') {\n\t\t\t\treturn this.ensure(rejectInvalidPredicate);\n\t\t\t}\n\n\t\t\treturn origCatch.call(this, createCatchFilter(arguments[1], onRejected));\n\t\t};\n\n\t\t/**\n\t\t * Wraps the provided catch handler, so that it will only be called\n\t\t * if the predicate evaluates truthy\n\t\t * @param {?function} handler\n\t\t * @param {function} predicate\n\t\t * @returns {function} conditional catch handler\n\t\t */\n\t\tfunction createCatchFilter(handler, predicate) {\n\t\t\treturn function(e) {\n\t\t\t\treturn evaluatePredicate(e, predicate)\n\t\t\t\t\t? handler.call(this, e)\n\t\t\t\t\t: reject(e);\n\t\t\t};\n\t\t}\n\n\t\t/**\n\t\t * Ensures that onFulfilledOrRejected will be called regardless of whether\n\t\t * this promise is fulfilled or rejected.  onFulfilledOrRejected WILL NOT\n\t\t * receive the promises' value or reason.  Any returned value will be disregarded.\n\t\t * onFulfilledOrRejected may throw or return a rejected promise to signal\n\t\t * an additional error.\n\t\t * @param {function} handler handler to be called regardless of\n\t\t *  fulfillment or rejection\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype['finally'] = Promise.prototype.ensure = function(handler) {\n\t\t\tif(typeof handler !== 'function') {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\treturn this.then(function(x) {\n\t\t\t\treturn runSideEffect(handler, this, identity, x);\n\t\t\t}, function(e) {\n\t\t\t\treturn runSideEffect(handler, this, reject, e);\n\t\t\t});\n\t\t};\n\n\t\tfunction runSideEffect (handler, thisArg, propagate, value) {\n\t\t\tvar result = handler.call(thisArg);\n\t\t\treturn maybeThenable(result)\n\t\t\t\t? propagateValue(result, propagate, value)\n\t\t\t\t: propagate(value);\n\t\t}\n\n\t\tfunction propagateValue (result, propagate, x) {\n\t\t\treturn resolve(result).then(function () {\n\t\t\t\treturn propagate(x);\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Recover from a failure by returning a defaultValue.  If defaultValue\n\t\t * is a promise, it's fulfillment value will be used.  If defaultValue is\n\t\t * a promise that rejects, the returned promise will reject with the\n\t\t * same reason.\n\t\t * @param {*} defaultValue\n\t\t * @returns {Promise} new promise\n\t\t */\n\t\tPromise.prototype['else'] = Promise.prototype.orElse = function(defaultValue) {\n\t\t\treturn this.then(void 0, function() {\n\t\t\t\treturn defaultValue;\n\t\t\t});\n\t\t};\n\n\t\t/**\n\t\t * Shortcut for .then(function() { return value; })\n\t\t * @param  {*} value\n\t\t * @return {Promise} a promise that:\n\t\t *  - is fulfilled if value is not a promise, or\n\t\t *  - if value is a promise, will fulfill with its value, or reject\n\t\t *    with its reason.\n\t\t */\n\t\tPromise.prototype['yield'] = function(value) {\n\t\t\treturn this.then(function() {\n\t\t\t\treturn value;\n\t\t\t});\n\t\t};\n\n\t\t/**\n\t\t * Runs a side effect when this promise fulfills, without changing the\n\t\t * fulfillment value.\n\t\t * @param {function} onFulfilledSideEffect\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.tap = function(onFulfilledSideEffect) {\n\t\t\treturn this.then(onFulfilledSideEffect)['yield'](this);\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n\tfunction rejectInvalidPredicate() {\n\t\tthrow new TypeError('catch predicate must be a function');\n\t}\n\n\tfunction evaluatePredicate(e, predicate) {\n\t\treturn isError(predicate) ? e instanceof predicate : predicate(e);\n\t}\n\n\tfunction isError(predicate) {\n\t\treturn predicate === Error\n\t\t\t|| (predicate != null && predicate.prototype instanceof Error);\n\t}\n\n\tfunction maybeThenable(x) {\n\t\treturn (typeof x === 'object' || typeof x === 'function') && x !== null;\n\t}\n\n\tfunction identity(x) {\n\t\treturn x;\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function fold(Promise) {\n\n\t\tPromise.prototype.fold = function(f, z) {\n\t\t\tvar promise = this._beget();\n\n\t\t\tthis._handler.fold(function(z, x, to) {\n\t\t\t\tPromise._handler(z).fold(function(x, z, to) {\n\t\t\t\t\tto.resolve(f.call(this, z, x));\n\t\t\t\t}, x, this, to);\n\t\t\t}, z, promise._handler.receiver, promise._handler);\n\n\t\t\treturn promise;\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar inspect = require('../state').inspect;\n\n\treturn function inspection(Promise) {\n\n\t\tPromise.prototype.inspect = function() {\n\t\t\treturn inspect(Promise._handler(this));\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function generate(Promise) {\n\n\t\tvar resolve = Promise.resolve;\n\n\t\tPromise.iterate = iterate;\n\t\tPromise.unfold = unfold;\n\n\t\treturn Promise;\n\n\t\t/**\n\t\t * @deprecated Use github.com/cujojs/most streams and most.iterate\n\t\t * Generate a (potentially infinite) stream of promised values:\n\t\t * x, f(x), f(f(x)), etc. until condition(x) returns true\n\t\t * @param {function} f function to generate a new x from the previous x\n\t\t * @param {function} condition function that, given the current x, returns\n\t\t *  truthy when the iterate should stop\n\t\t * @param {function} handler function to handle the value produced by f\n\t\t * @param {*|Promise} x starting value, may be a promise\n\t\t * @return {Promise} the result of the last call to f before\n\t\t *  condition returns true\n\t\t */\n\t\tfunction iterate(f, condition, handler, x) {\n\t\t\treturn unfold(function(x) {\n\t\t\t\treturn [x, f(x)];\n\t\t\t}, condition, handler, x);\n\t\t}\n\n\t\t/**\n\t\t * @deprecated Use github.com/cujojs/most streams and most.unfold\n\t\t * Generate a (potentially infinite) stream of promised values\n\t\t * by applying handler(generator(seed)) iteratively until\n\t\t * condition(seed) returns true.\n\t\t * @param {function} unspool function that generates a [value, newSeed]\n\t\t *  given a seed.\n\t\t * @param {function} condition function that, given the current seed, returns\n\t\t *  truthy when the unfold should stop\n\t\t * @param {function} handler function to handle the value produced by unspool\n\t\t * @param x {*|Promise} starting value, may be a promise\n\t\t * @return {Promise} the result of the last value produced by unspool before\n\t\t *  condition returns true\n\t\t */\n\t\tfunction unfold(unspool, condition, handler, x) {\n\t\t\treturn resolve(x).then(function(seed) {\n\t\t\t\treturn resolve(condition(seed)).then(function(done) {\n\t\t\t\t\treturn done ? seed : resolve(unspool(seed)).spread(next);\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tfunction next(item, newSeed) {\n\t\t\t\treturn resolve(handler(item)).then(function() {\n\t\t\t\t\treturn unfold(unspool, condition, handler, newSeed);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function progress(Promise) {\n\n\t\t/**\n\t\t * @deprecated\n\t\t * Register a progress handler for this promise\n\t\t * @param {function} onProgress\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.progress = function(onProgress) {\n\t\t\treturn this.then(void 0, void 0, onProgress);\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar env = require('../env');\n\tvar TimeoutError = require('../TimeoutError');\n\n\tfunction setTimeout(f, ms, x, y) {\n\t\treturn env.setTimer(function() {\n\t\t\tf(x, y, ms);\n\t\t}, ms);\n\t}\n\n\treturn function timed(Promise) {\n\t\t/**\n\t\t * Return a new promise whose fulfillment value is revealed only\n\t\t * after ms milliseconds\n\t\t * @param {number} ms milliseconds\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.delay = function(ms) {\n\t\t\tvar p = this._beget();\n\t\t\tthis._handler.fold(handleDelay, ms, void 0, p._handler);\n\t\t\treturn p;\n\t\t};\n\n\t\tfunction handleDelay(ms, x, h) {\n\t\t\tsetTimeout(resolveDelay, ms, x, h);\n\t\t}\n\n\t\tfunction resolveDelay(x, h) {\n\t\t\th.resolve(x);\n\t\t}\n\n\t\t/**\n\t\t * Return a new promise that rejects after ms milliseconds unless\n\t\t * this promise fulfills earlier, in which case the returned promise\n\t\t * fulfills with the same value.\n\t\t * @param {number} ms milliseconds\n\t\t * @param {Error|*=} reason optional rejection reason to use, defaults\n\t\t *   to a TimeoutError if not provided\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.timeout = function(ms, reason) {\n\t\t\tvar p = this._beget();\n\t\t\tvar h = p._handler;\n\n\t\t\tvar t = setTimeout(onTimeout, ms, reason, p._handler);\n\n\t\t\tthis._handler.visit(h,\n\t\t\t\tfunction onFulfill(x) {\n\t\t\t\t\tenv.clearTimer(t);\n\t\t\t\t\tthis.resolve(x); // this = h\n\t\t\t\t},\n\t\t\t\tfunction onReject(x) {\n\t\t\t\t\tenv.clearTimer(t);\n\t\t\t\t\tthis.reject(x); // this = h\n\t\t\t\t},\n\t\t\t\th.notify);\n\n\t\t\treturn p;\n\t\t};\n\n\t\tfunction onTimeout(reason, h, ms) {\n\t\t\tvar e = typeof reason === 'undefined'\n\t\t\t\t? new TimeoutError('timed out after ' + ms + 'ms')\n\t\t\t\t: reason;\n\t\t\th.reject(e);\n\t\t}\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar setTimer = require('../env').setTimer;\n\tvar format = require('../format');\n\n\treturn function unhandledRejection(Promise) {\n\n\t\tvar logError = noop;\n\t\tvar logInfo = noop;\n\t\tvar localConsole;\n\n\t\tif(typeof console !== 'undefined') {\n\t\t\t// Alias console to prevent things like uglify's drop_console option from\n\t\t\t// removing console.log/error. Unhandled rejections fall into the same\n\t\t\t// category as uncaught exceptions, and build tools shouldn't silence them.\n\t\t\tlocalConsole = console;\n\t\t\tlogError = typeof localConsole.error !== 'undefined'\n\t\t\t\t? function (e) { localConsole.error(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\n\t\t\tlogInfo = typeof localConsole.info !== 'undefined'\n\t\t\t\t? function (e) { localConsole.info(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\t\t}\n\n\t\tPromise.onPotentiallyUnhandledRejection = function(rejection) {\n\t\t\tenqueue(report, rejection);\n\t\t};\n\n\t\tPromise.onPotentiallyUnhandledRejectionHandled = function(rejection) {\n\t\t\tenqueue(unreport, rejection);\n\t\t};\n\n\t\tPromise.onFatalRejection = function(rejection) {\n\t\t\tenqueue(throwit, rejection.value);\n\t\t};\n\n\t\tvar tasks = [];\n\t\tvar reported = [];\n\t\tvar running = null;\n\n\t\tfunction report(r) {\n\t\t\tif(!r.handled) {\n\t\t\t\treported.push(r);\n\t\t\t\tlogError('Potentially unhandled rejection [' + r.id + '] ' + format.formatError(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction unreport(r) {\n\t\t\tvar i = reported.indexOf(r);\n\t\t\tif(i >= 0) {\n\t\t\t\treported.splice(i, 1);\n\t\t\t\tlogInfo('Handled previous rejection [' + r.id + '] ' + format.formatObject(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction enqueue(f, x) {\n\t\t\ttasks.push(f, x);\n\t\t\tif(running === null) {\n\t\t\t\trunning = setTimer(flush, 0);\n\t\t\t}\n\t\t}\n\n\t\tfunction flush() {\n\t\t\trunning = null;\n\t\t\twhile(tasks.length > 0) {\n\t\t\t\ttasks.shift()(tasks.shift());\n\t\t\t}\n\t\t}\n\n\t\treturn Promise;\n\t};\n\n\tfunction throwit(e) {\n\t\tthrow e;\n\t}\n\n\tfunction noop() {}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function addWith(Promise) {\n\t\t/**\n\t\t * Returns a promise whose handlers will be called with `this` set to\n\t\t * the supplied receiver.  Subsequent promises derived from the\n\t\t * returned promise will also have their handlers called with receiver\n\t\t * as `this`. Calling `with` with undefined or no arguments will return\n\t\t * a promise whose handlers will again be called in the usual Promises/A+\n\t\t * way (no `this`) thus safely undoing any previous `with` in the\n\t\t * promise chain.\n\t\t *\n\t\t * WARNING: Promises returned from `with`/`withThis` are NOT Promises/A+\n\t\t * compliant, specifically violating 2.2.5 (http://promisesaplus.com/#point-41)\n\t\t *\n\t\t * @param {object} receiver `this` value for all handlers attached to\n\t\t *  the returned promise.\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype['with'] = Promise.prototype.withThis = function(receiver) {\n\t\t\tvar p = this._beget();\n\t\t\tvar child = p._handler;\n\t\t\tchild.receiver = receiver;\n\t\t\tthis._handler.chain(child, receiver);\n\t\t\treturn p;\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n/*global process,document,setTimeout,clearTimeout,MutationObserver,WebKitMutationObserver*/\n(function(define) { 'use strict';\ndefine(function(require) {\n\t/*jshint maxcomplexity:6*/\n\n\t// Sniff \"best\" async scheduling option\n\t// Prefer process.nextTick or MutationObserver, then check for\n\t// setTimeout, and finally vertx, since its the only env that doesn't\n\t// have setTimeout\n\n\tvar MutationObs;\n\tvar capturedSetTimeout = typeof setTimeout !== 'undefined' && setTimeout;\n\n\t// Default env\n\tvar setTimer = function(f, ms) { return setTimeout(f, ms); };\n\tvar clearTimer = function(t) { return clearTimeout(t); };\n\tvar asap = function (f) { return capturedSetTimeout(f, 0); };\n\n\t// Detect specific env\n\tif (isNode()) { // Node\n\t\tasap = function (f) { return process.nextTick(f); };\n\n\t} else if (MutationObs = hasMutationObserver()) { // Modern browser\n\t\tasap = initMutationObserver(MutationObs);\n\n\t} else if (!capturedSetTimeout) { // vert.x\n\t\tvar vertxRequire = require;\n\t\tvar vertx = vertxRequire('vertx');\n\t\tsetTimer = function (f, ms) { return vertx.setTimer(ms, f); };\n\t\tclearTimer = vertx.cancelTimer;\n\t\tasap = vertx.runOnLoop || vertx.runOnContext;\n\t}\n\n\treturn {\n\t\tsetTimer: setTimer,\n\t\tclearTimer: clearTimer,\n\t\tasap: asap\n\t};\n\n\tfunction isNode () {\n\t\treturn typeof process !== 'undefined' &&\n\t\t\tObject.prototype.toString.call(process) === '[object process]';\n\t}\n\n\tfunction hasMutationObserver () {\n\t    return (typeof MutationObserver !== 'undefined' && MutationObserver) ||\n\t\t\t(typeof WebKitMutationObserver !== 'undefined' && WebKitMutationObserver);\n\t}\n\n\tfunction initMutationObserver(MutationObserver) {\n\t\tvar scheduled;\n\t\tvar node = document.createTextNode('');\n\t\tvar o = new MutationObserver(run);\n\t\to.observe(node, { characterData: true });\n\n\t\tfunction run() {\n\t\t\tvar f = scheduled;\n\t\t\tscheduled = void 0;\n\t\t\tf();\n\t\t}\n\n\t\tvar i = 0;\n\t\treturn function (f) {\n\t\t\tscheduled = f;\n\t\t\tnode.data = (i ^= 1);\n\t\t};\n\t}\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn {\n\t\tformatError: formatError,\n\t\tformatObject: formatObject,\n\t\ttryStringify: tryStringify\n\t};\n\n\t/**\n\t * Format an error into a string.  If e is an Error and has a stack property,\n\t * it's returned.  Otherwise, e is formatted using formatObject, with a\n\t * warning added about e not being a proper Error.\n\t * @param {*} e\n\t * @returns {String} formatted string, suitable for output to developers\n\t */\n\tfunction formatError(e) {\n\t\tvar s = typeof e === 'object' && e !== null && (e.stack || e.message) ? e.stack || e.message : formatObject(e);\n\t\treturn e instanceof Error ? s : s + ' (WARNING: non-Error used)';\n\t}\n\n\t/**\n\t * Format an object, detecting \"plain\" objects and running them through\n\t * JSON.stringify if possible.\n\t * @param {Object} o\n\t * @returns {string}\n\t */\n\tfunction formatObject(o) {\n\t\tvar s = String(o);\n\t\tif(s === '[object Object]' && typeof JSON !== 'undefined') {\n\t\t\ts = tryStringify(o, s);\n\t\t}\n\t\treturn s;\n\t}\n\n\t/**\n\t * Try to return the result of JSON.stringify(x).  If that fails, return\n\t * defaultValue\n\t * @param {*} x\n\t * @param {*} defaultValue\n\t * @returns {String|*} JSON.stringify(x) or defaultValue\n\t */\n\tfunction tryStringify(x, defaultValue) {\n\t\ttry {\n\t\t\treturn JSON.stringify(x);\n\t\t} catch(e) {\n\t\t\treturn defaultValue;\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function liftAll(liftOne, combine, dst, src) {\n\t\tif(typeof combine === 'undefined') {\n\t\t\tcombine = defaultCombine;\n\t\t}\n\n\t\treturn Object.keys(src).reduce(function(dst, key) {\n\t\t\tvar f = src[key];\n\t\t\treturn typeof f === 'function' ? combine(dst, liftOne(f), key) : dst;\n\t\t}, typeof dst === 'undefined' ? defaultDst(src) : dst);\n\t};\n\n\tfunction defaultCombine(o, f, k) {\n\t\to[k] = f;\n\t\treturn o;\n\t}\n\n\tfunction defaultDst(src) {\n\t\treturn typeof src === 'function' ? src.bind() : Object.create(src);\n\t}\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function makePromise(environment) {\n\n\t\tvar tasks = environment.scheduler;\n\t\tvar emitRejection = initEmitRejection();\n\n\t\tvar objectCreate = Object.create ||\n\t\t\tfunction(proto) {\n\t\t\t\tfunction Child() {}\n\t\t\t\tChild.prototype = proto;\n\t\t\t\treturn new Child();\n\t\t\t};\n\n\t\t/**\n\t\t * Create a promise whose fate is determined by resolver\n\t\t * @constructor\n\t\t * @returns {Promise} promise\n\t\t * @name Promise\n\t\t */\n\t\tfunction Promise(resolver, handler) {\n\t\t\tthis._handler = resolver === Handler ? handler : init(resolver);\n\t\t}\n\n\t\t/**\n\t\t * Run the supplied resolver\n\t\t * @param resolver\n\t\t * @returns {Pending}\n\t\t */\n\t\tfunction init(resolver) {\n\t\t\tvar handler = new Pending();\n\n\t\t\ttry {\n\t\t\t\tresolver(promiseResolve, promiseReject, promiseNotify);\n\t\t\t} catch (e) {\n\t\t\t\tpromiseReject(e);\n\t\t\t}\n\n\t\t\treturn handler;\n\n\t\t\t/**\n\t\t\t * Transition from pre-resolution state to post-resolution state, notifying\n\t\t\t * all listeners of the ultimate fulfillment or rejection\n\t\t\t * @param {*} x resolution value\n\t\t\t */\n\t\t\tfunction promiseResolve (x) {\n\t\t\t\thandler.resolve(x);\n\t\t\t}\n\t\t\t/**\n\t\t\t * Reject this promise with reason, which will be used verbatim\n\t\t\t * @param {Error|*} reason rejection reason, strongly suggested\n\t\t\t *   to be an Error type\n\t\t\t */\n\t\t\tfunction promiseReject (reason) {\n\t\t\t\thandler.reject(reason);\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * @deprecated\n\t\t\t * Issue a progress event, notifying all progress listeners\n\t\t\t * @param {*} x progress event payload to pass to all listeners\n\t\t\t */\n\t\t\tfunction promiseNotify (x) {\n\t\t\t\thandler.notify(x);\n\t\t\t}\n\t\t}\n\n\t\t// Creation\n\n\t\tPromise.resolve = resolve;\n\t\tPromise.reject = reject;\n\t\tPromise.never = never;\n\n\t\tPromise._defer = defer;\n\t\tPromise._handler = getHandler;\n\n\t\t/**\n\t\t * Returns a trusted promise. If x is already a trusted promise, it is\n\t\t * returned, otherwise returns a new trusted Promise which follows x.\n\t\t * @param  {*} x\n\t\t * @return {Promise} promise\n\t\t */\n\t\tfunction resolve(x) {\n\t\t\treturn isPromise(x) ? x\n\t\t\t\t: new Promise(Handler, new Async(getHandler(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a reject promise with x as its reason (x is used verbatim)\n\t\t * @param {*} x\n\t\t * @returns {Promise} rejected promise\n\t\t */\n\t\tfunction reject(x) {\n\t\t\treturn new Promise(Handler, new Async(new Rejected(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a promise that remains pending forever\n\t\t * @returns {Promise} forever-pending promise.\n\t\t */\n\t\tfunction never() {\n\t\t\treturn foreverPendingPromise; // Should be frozen\n\t\t}\n\n\t\t/**\n\t\t * Creates an internal {promise, resolver} pair\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tfunction defer() {\n\t\t\treturn new Promise(Handler, new Pending());\n\t\t}\n\n\t\t// Transformation and flow control\n\n\t\t/**\n\t\t * Transform this promise's fulfillment value, returning a new Promise\n\t\t * for the transformed result.  If the promise cannot be fulfilled, onRejected\n\t\t * is called with the reason.  onProgress *may* be called with updates toward\n\t\t * this promise's fulfillment.\n\t\t * @param {function=} onFulfilled fulfillment handler\n\t\t * @param {function=} onRejected rejection handler\n\t\t * @param {function=} onProgress @deprecated progress handler\n\t\t * @return {Promise} new promise\n\t\t */\n\t\tPromise.prototype.then = function(onFulfilled, onRejected, onProgress) {\n\t\t\tvar parent = this._handler;\n\t\t\tvar state = parent.join().state();\n\n\t\t\tif ((typeof onFulfilled !== 'function' && state > 0) ||\n\t\t\t\t(typeof onRejected !== 'function' && state < 0)) {\n\t\t\t\t// Short circuit: value will not change, simply share handler\n\t\t\t\treturn new this.constructor(Handler, parent);\n\t\t\t}\n\n\t\t\tvar p = this._beget();\n\t\t\tvar child = p._handler;\n\n\t\t\tparent.chain(child, parent.receiver, onFulfilled, onRejected, onProgress);\n\n\t\t\treturn p;\n\t\t};\n\n\t\t/**\n\t\t * If this promise cannot be fulfilled due to an error, call onRejected to\n\t\t * handle the error. Shortcut for .then(undefined, onRejected)\n\t\t * @param {function?} onRejected\n\t\t * @return {Promise}\n\t\t */\n\t\tPromise.prototype['catch'] = function(onRejected) {\n\t\t\treturn this.then(void 0, onRejected);\n\t\t};\n\n\t\t/**\n\t\t * Creates a new, pending promise of the same type as this promise\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype._beget = function() {\n\t\t\treturn begetFrom(this._handler, this.constructor);\n\t\t};\n\n\t\tfunction begetFrom(parent, Promise) {\n\t\t\tvar child = new Pending(parent.receiver, parent.join().context);\n\t\t\treturn new Promise(Handler, child);\n\t\t}\n\n\t\t// Array combinators\n\n\t\tPromise.all = all;\n\t\tPromise.race = race;\n\t\tPromise._traverse = traverse;\n\n\t\t/**\n\t\t * Return a promise that will fulfill when all promises in the\n\t\t * input array have fulfilled, or will reject when one of the\n\t\t * promises rejects.\n\t\t * @param {array} promises array of promises\n\t\t * @returns {Promise} promise for array of fulfillment values\n\t\t */\n\t\tfunction all(promises) {\n\t\t\treturn traverseWith(snd, null, promises);\n\t\t}\n\n\t\t/**\n\t\t * Array<Promise<X>> -> Promise<Array<f(X)>>\n\t\t * @private\n\t\t * @param {function} f function to apply to each promise's value\n\t\t * @param {Array} promises array of promises\n\t\t * @returns {Promise} promise for transformed values\n\t\t */\n\t\tfunction traverse(f, promises) {\n\t\t\treturn traverseWith(tryCatch2, f, promises);\n\t\t}\n\n\t\tfunction traverseWith(tryMap, f, promises) {\n\t\t\tvar handler = typeof f === 'function' ? mapAt : settleAt;\n\n\t\t\tvar resolver = new Pending();\n\t\t\tvar pending = promises.length >>> 0;\n\t\t\tvar results = new Array(pending);\n\n\t\t\tfor (var i = 0, x; i < promises.length && !resolver.resolved; ++i) {\n\t\t\t\tx = promises[i];\n\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\t--pending;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\ttraverseAt(promises, handler, i, x, resolver);\n\t\t\t}\n\n\t\t\tif(pending === 0) {\n\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t}\n\n\t\t\treturn new Promise(Handler, resolver);\n\n\t\t\tfunction mapAt(i, x, resolver) {\n\t\t\t\tif(!resolver.resolved) {\n\t\t\t\t\ttraverseAt(promises, settleAt, i, tryMap(f, x, i), resolver);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction settleAt(i, x, resolver) {\n\t\t\t\tresults[i] = x;\n\t\t\t\tif(--pending === 0) {\n\t\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfunction traverseAt(promises, handler, i, x, resolver) {\n\t\t\tif (maybeThenable(x)) {\n\t\t\t\tvar h = getHandlerMaybeThenable(x);\n\t\t\t\tvar s = h.state();\n\n\t\t\t\tif (s === 0) {\n\t\t\t\t\th.fold(handler, i, void 0, resolver);\n\t\t\t\t} else if (s > 0) {\n\t\t\t\t\thandler(i, h.value, resolver);\n\t\t\t\t} else {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thandler(i, x, resolver);\n\t\t\t}\n\t\t}\n\n\t\tPromise._visitRemaining = visitRemaining;\n\t\tfunction visitRemaining(promises, start, handler) {\n\t\t\tfor(var i=start; i<promises.length; ++i) {\n\t\t\t\tmarkAsHandled(getHandler(promises[i]), handler);\n\t\t\t}\n\t\t}\n\n\t\tfunction markAsHandled(h, handler) {\n\t\t\tif(h === handler) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar s = h.state();\n\t\t\tif(s === 0) {\n\t\t\t\th.visit(h, void 0, h._unreport);\n\t\t\t} else if(s < 0) {\n\t\t\t\th._unreport();\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fulfill-reject competitive race. Return a promise that will settle\n\t\t * to the same state as the earliest input promise to settle.\n\t\t *\n\t\t * WARNING: The ES6 Promise spec requires that race()ing an empty array\n\t\t * must return a promise that is pending forever.  This implementation\n\t\t * returns a singleton forever-pending promise, the same singleton that is\n\t\t * returned by Promise.never(), thus can be checked with ===\n\t\t *\n\t\t * @param {array} promises array of promises to race\n\t\t * @returns {Promise} if input is non-empty, a promise that will settle\n\t\t * to the same outcome as the earliest input promise to settle. if empty\n\t\t * is empty, returns a promise that will never settle.\n\t\t */\n\t\tfunction race(promises) {\n\t\t\tif(typeof promises !== 'object' || promises === null) {\n\t\t\t\treturn reject(new TypeError('non-iterable passed to race()'));\n\t\t\t}\n\n\t\t\t// Sigh, race([]) is untestable unless we return *something*\n\t\t\t// that is recognizable without calling .then() on it.\n\t\t\treturn promises.length === 0 ? never()\n\t\t\t\t : promises.length === 1 ? resolve(promises[0])\n\t\t\t\t : runRace(promises);\n\t\t}\n\n\t\tfunction runRace(promises) {\n\t\t\tvar resolver = new Pending();\n\t\t\tvar i, x, h;\n\t\t\tfor(i=0; i<promises.length; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\th = getHandler(x);\n\t\t\t\tif(h.state() !== 0) {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\th.visit(resolver, resolver.resolve, resolver.reject);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn new Promise(Handler, resolver);\n\t\t}\n\n\t\t// Promise internals\n\t\t// Below this, everything is @private\n\n\t\t/**\n\t\t * Get an appropriate handler for x, without checking for cycles\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandler(x) {\n\t\t\tif(isPromise(x)) {\n\t\t\t\treturn x._handler.join();\n\t\t\t}\n\t\t\treturn maybeThenable(x) ? getHandlerUntrusted(x) : new Fulfilled(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for thenable x.\n\t\t * NOTE: You must only call this if maybeThenable(x) == true\n\t\t * @param {object|function|Promise} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerMaybeThenable(x) {\n\t\t\treturn isPromise(x) ? x._handler.join() : getHandlerUntrusted(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for potentially untrusted thenable x\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerUntrusted(x) {\n\t\t\ttry {\n\t\t\t\tvar untrustedThen = x.then;\n\t\t\t\treturn typeof untrustedThen === 'function'\n\t\t\t\t\t? new Thenable(untrustedThen, x)\n\t\t\t\t\t: new Fulfilled(x);\n\t\t\t} catch(e) {\n\t\t\t\treturn new Rejected(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Handler for a promise that is pending forever\n\t\t * @constructor\n\t\t */\n\t\tfunction Handler() {}\n\n\t\tHandler.prototype.when\n\t\t\t= Handler.prototype.become\n\t\t\t= Handler.prototype.notify // deprecated\n\t\t\t= Handler.prototype.fail\n\t\t\t= Handler.prototype._unreport\n\t\t\t= Handler.prototype._report\n\t\t\t= noop;\n\n\t\tHandler.prototype._state = 0;\n\n\t\tHandler.prototype.state = function() {\n\t\t\treturn this._state;\n\t\t};\n\n\t\t/**\n\t\t * Recursively collapse handler chain to find the handler\n\t\t * nearest to the fully resolved value.\n\t\t * @returns {object} handler nearest the fully resolved value\n\t\t */\n\t\tHandler.prototype.join = function() {\n\t\t\tvar h = this;\n\t\t\twhile(h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t}\n\t\t\treturn h;\n\t\t};\n\n\t\tHandler.prototype.chain = function(to, receiver, fulfilled, rejected, progress) {\n\t\t\tthis.when({\n\t\t\t\tresolver: to,\n\t\t\t\treceiver: receiver,\n\t\t\t\tfulfilled: fulfilled,\n\t\t\t\trejected: rejected,\n\t\t\t\tprogress: progress\n\t\t\t});\n\t\t};\n\n\t\tHandler.prototype.visit = function(receiver, fulfilled, rejected, progress) {\n\t\t\tthis.chain(failIfRejected, receiver, fulfilled, rejected, progress);\n\t\t};\n\n\t\tHandler.prototype.fold = function(f, z, c, to) {\n\t\t\tthis.when(new Fold(f, z, c, to));\n\t\t};\n\n\t\t/**\n\t\t * Handler that invokes fail() on any handler it becomes\n\t\t * @constructor\n\t\t */\n\t\tfunction FailIfRejected() {}\n\n\t\tinherit(Handler, FailIfRejected);\n\n\t\tFailIfRejected.prototype.become = function(h) {\n\t\t\th.fail();\n\t\t};\n\n\t\tvar failIfRejected = new FailIfRejected();\n\n\t\t/**\n\t\t * Handler that manages a queue of consumers waiting on a pending promise\n\t\t * @constructor\n\t\t */\n\t\tfunction Pending(receiver, inheritedContext) {\n\t\t\tPromise.createContext(this, inheritedContext);\n\n\t\t\tthis.consumers = void 0;\n\t\t\tthis.receiver = receiver;\n\t\t\tthis.handler = void 0;\n\t\t\tthis.resolved = false;\n\t\t}\n\n\t\tinherit(Handler, Pending);\n\n\t\tPending.prototype._state = 0;\n\n\t\tPending.prototype.resolve = function(x) {\n\t\t\tthis.become(getHandler(x));\n\t\t};\n\n\t\tPending.prototype.reject = function(x) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.become(new Rejected(x));\n\t\t};\n\n\t\tPending.prototype.join = function() {\n\t\t\tif (!this.resolved) {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\tvar h = this;\n\n\t\t\twhile (h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t\tif (h === this) {\n\t\t\t\t\treturn this.handler = cycle();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn h;\n\t\t};\n\n\t\tPending.prototype.run = function() {\n\t\t\tvar q = this.consumers;\n\t\t\tvar handler = this.handler;\n\t\t\tthis.handler = this.handler.join();\n\t\t\tthis.consumers = void 0;\n\n\t\t\tfor (var i = 0; i < q.length; ++i) {\n\t\t\t\thandler.when(q[i]);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.become = function(handler) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.resolved = true;\n\t\t\tthis.handler = handler;\n\t\t\tif(this.consumers !== void 0) {\n\t\t\t\ttasks.enqueue(this);\n\t\t\t}\n\n\t\t\tif(this.context !== void 0) {\n\t\t\t\thandler._report(this.context);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.when = function(continuation) {\n\t\t\tif(this.resolved) {\n\t\t\t\ttasks.enqueue(new ContinuationTask(continuation, this.handler));\n\t\t\t} else {\n\t\t\t\tif(this.consumers === void 0) {\n\t\t\t\t\tthis.consumers = [continuation];\n\t\t\t\t} else {\n\t\t\t\t\tthis.consumers.push(continuation);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tPending.prototype.notify = function(x) {\n\t\t\tif(!this.resolved) {\n\t\t\t\ttasks.enqueue(new ProgressTask(x, this));\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.fail = function(context) {\n\t\t\tvar c = typeof context === 'undefined' ? this.context : context;\n\t\t\tthis.resolved && this.handler.join().fail(c);\n\t\t};\n\n\t\tPending.prototype._report = function(context) {\n\t\t\tthis.resolved && this.handler.join()._report(context);\n\t\t};\n\n\t\tPending.prototype._unreport = function() {\n\t\t\tthis.resolved && this.handler.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Wrap another handler and force it into a future stack\n\t\t * @param {object} handler\n\t\t * @constructor\n\t\t */\n\t\tfunction Async(handler) {\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tinherit(Handler, Async);\n\n\t\tAsync.prototype.when = function(continuation) {\n\t\t\ttasks.enqueue(new ContinuationTask(continuation, this));\n\t\t};\n\n\t\tAsync.prototype._report = function(context) {\n\t\t\tthis.join()._report(context);\n\t\t};\n\n\t\tAsync.prototype._unreport = function() {\n\t\t\tthis.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Handler that wraps an untrusted thenable and assimilates it in a future stack\n\t\t * @param {function} then\n\t\t * @param {{then: function}} thenable\n\t\t * @constructor\n\t\t */\n\t\tfunction Thenable(then, thenable) {\n\t\t\tPending.call(this);\n\t\t\ttasks.enqueue(new AssimilateTask(then, thenable, this));\n\t\t}\n\n\t\tinherit(Pending, Thenable);\n\n\t\t/**\n\t\t * Handler for a fulfilled promise\n\t\t * @param {*} x fulfillment value\n\t\t * @constructor\n\t\t */\n\t\tfunction Fulfilled(x) {\n\t\t\tPromise.createContext(this);\n\t\t\tthis.value = x;\n\t\t}\n\n\t\tinherit(Handler, Fulfilled);\n\n\t\tFulfilled.prototype._state = 1;\n\n\t\tFulfilled.prototype.fold = function(f, z, c, to) {\n\t\t\trunContinuation3(f, z, this, c, to);\n\t\t};\n\n\t\tFulfilled.prototype.when = function(cont) {\n\t\t\trunContinuation1(cont.fulfilled, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tvar errorId = 0;\n\n\t\t/**\n\t\t * Handler for a rejected promise\n\t\t * @param {*} x rejection reason\n\t\t * @constructor\n\t\t */\n\t\tfunction Rejected(x) {\n\t\t\tPromise.createContext(this);\n\n\t\t\tthis.id = ++errorId;\n\t\t\tthis.value = x;\n\t\t\tthis.handled = false;\n\t\t\tthis.reported = false;\n\n\t\t\tthis._report();\n\t\t}\n\n\t\tinherit(Handler, Rejected);\n\n\t\tRejected.prototype._state = -1;\n\n\t\tRejected.prototype.fold = function(f, z, c, to) {\n\t\t\tto.become(this);\n\t\t};\n\n\t\tRejected.prototype.when = function(cont) {\n\t\t\tif(typeof cont.rejected === 'function') {\n\t\t\t\tthis._unreport();\n\t\t\t}\n\t\t\trunContinuation1(cont.rejected, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tRejected.prototype._report = function(context) {\n\t\t\ttasks.afterQueue(new ReportTask(this, context));\n\t\t};\n\n\t\tRejected.prototype._unreport = function() {\n\t\t\tif(this.handled) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.handled = true;\n\t\t\ttasks.afterQueue(new UnreportTask(this));\n\t\t};\n\n\t\tRejected.prototype.fail = function(context) {\n\t\t\tthis.reported = true;\n\t\t\temitRejection('unhandledRejection', this);\n\t\t\tPromise.onFatalRejection(this, context === void 0 ? this.context : context);\n\t\t};\n\n\t\tfunction ReportTask(rejection, context) {\n\t\t\tthis.rejection = rejection;\n\t\t\tthis.context = context;\n\t\t}\n\n\t\tReportTask.prototype.run = function() {\n\t\t\tif(!this.rejection.handled && !this.rejection.reported) {\n\t\t\t\tthis.rejection.reported = true;\n\t\t\t\temitRejection('unhandledRejection', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejection(this.rejection, this.context);\n\t\t\t}\n\t\t};\n\n\t\tfunction UnreportTask(rejection) {\n\t\t\tthis.rejection = rejection;\n\t\t}\n\n\t\tUnreportTask.prototype.run = function() {\n\t\t\tif(this.rejection.reported) {\n\t\t\t\temitRejection('rejectionHandled', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejectionHandled(this.rejection);\n\t\t\t}\n\t\t};\n\n\t\t// Unhandled rejection hooks\n\t\t// By default, everything is a noop\n\n\t\tPromise.createContext\n\t\t\t= Promise.enterContext\n\t\t\t= Promise.exitContext\n\t\t\t= Promise.onPotentiallyUnhandledRejection\n\t\t\t= Promise.onPotentiallyUnhandledRejectionHandled\n\t\t\t= Promise.onFatalRejection\n\t\t\t= noop;\n\n\t\t// Errors and singletons\n\n\t\tvar foreverPendingHandler = new Handler();\n\t\tvar foreverPendingPromise = new Promise(Handler, foreverPendingHandler);\n\n\t\tfunction cycle() {\n\t\t\treturn new Rejected(new TypeError('Promise cycle'));\n\t\t}\n\n\t\t// Task runners\n\n\t\t/**\n\t\t * Run a single consumer\n\t\t * @constructor\n\t\t */\n\t\tfunction ContinuationTask(continuation, handler) {\n\t\t\tthis.continuation = continuation;\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tContinuationTask.prototype.run = function() {\n\t\t\tthis.handler.join().when(this.continuation);\n\t\t};\n\n\t\t/**\n\t\t * Run a queue of progress handlers\n\t\t * @constructor\n\t\t */\n\t\tfunction ProgressTask(value, handler) {\n\t\t\tthis.handler = handler;\n\t\t\tthis.value = value;\n\t\t}\n\n\t\tProgressTask.prototype.run = function() {\n\t\t\tvar q = this.handler.consumers;\n\t\t\tif(q === void 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tfor (var c, i = 0; i < q.length; ++i) {\n\t\t\t\tc = q[i];\n\t\t\t\trunNotify(c.progress, this.value, this.handler, c.receiver, c.resolver);\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Assimilate a thenable, sending it's value to resolver\n\t\t * @param {function} then\n\t\t * @param {object|function} thenable\n\t\t * @param {object} resolver\n\t\t * @constructor\n\t\t */\n\t\tfunction AssimilateTask(then, thenable, resolver) {\n\t\t\tthis._then = then;\n\t\t\tthis.thenable = thenable;\n\t\t\tthis.resolver = resolver;\n\t\t}\n\n\t\tAssimilateTask.prototype.run = function() {\n\t\t\tvar h = this.resolver;\n\t\t\ttryAssimilate(this._then, this.thenable, _resolve, _reject, _notify);\n\n\t\t\tfunction _resolve(x) { h.resolve(x); }\n\t\t\tfunction _reject(x)  { h.reject(x); }\n\t\t\tfunction _notify(x)  { h.notify(x); }\n\t\t};\n\n\t\tfunction tryAssimilate(then, thenable, resolve, reject, notify) {\n\t\t\ttry {\n\t\t\t\tthen.call(thenable, resolve, reject, notify);\n\t\t\t} catch (e) {\n\t\t\t\treject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fold a handler value with z\n\t\t * @constructor\n\t\t */\n\t\tfunction Fold(f, z, c, to) {\n\t\t\tthis.f = f; this.z = z; this.c = c; this.to = to;\n\t\t\tthis.resolver = failIfRejected;\n\t\t\tthis.receiver = this;\n\t\t}\n\n\t\tFold.prototype.fulfilled = function(x) {\n\t\t\tthis.f.call(this.c, this.z, x, this.to);\n\t\t};\n\n\t\tFold.prototype.rejected = function(x) {\n\t\t\tthis.to.reject(x);\n\t\t};\n\n\t\tFold.prototype.progress = function(x) {\n\t\t\tthis.to.notify(x);\n\t\t};\n\n\t\t// Other helpers\n\n\t\t/**\n\t\t * @param {*} x\n\t\t * @returns {boolean} true iff x is a trusted Promise\n\t\t */\n\t\tfunction isPromise(x) {\n\t\t\treturn x instanceof Promise;\n\t\t}\n\n\t\t/**\n\t\t * Test just enough to rule out primitives, in order to take faster\n\t\t * paths in some code\n\t\t * @param {*} x\n\t\t * @returns {boolean} false iff x is guaranteed *not* to be a thenable\n\t\t */\n\t\tfunction maybeThenable(x) {\n\t\t\treturn (typeof x === 'object' || typeof x === 'function') && x !== null;\n\t\t}\n\n\t\tfunction runContinuation1(f, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject(f, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction runContinuation3(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject3(f, x, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tfunction runNotify(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.notify(x);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReturn(f, x, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction tryCatch2(f, a, b) {\n\t\t\ttry {\n\t\t\t\treturn f(a, b);\n\t\t\t} catch(e) {\n\t\t\t\treturn reject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Return f.call(thisArg, x), or if it throws return a rejected promise for\n\t\t * the thrown exception\n\t\t */\n\t\tfunction tryCatchReject(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.become(getHandler(f.call(thisArg, x)));\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Same as above, but includes the extra argument parameter.\n\t\t */\n\t\tfunction tryCatchReject3(f, x, y, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tf.call(thisArg, x, y, next);\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t * Return f.call(thisArg, x), or if it throws, *return* the exception\n\t\t */\n\t\tfunction tryCatchReturn(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.notify(f.call(thisArg, x));\n\t\t\t} catch(e) {\n\t\t\t\tnext.notify(e);\n\t\t\t}\n\t\t}\n\n\t\tfunction inherit(Parent, Child) {\n\t\t\tChild.prototype = objectCreate(Parent.prototype);\n\t\t\tChild.prototype.constructor = Child;\n\t\t}\n\n\t\tfunction snd(x, y) {\n\t\t\treturn y;\n\t\t}\n\n\t\tfunction noop() {}\n\n\t\tfunction hasCustomEvent() {\n\t\t\tif(typeof CustomEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\tvar ev = new CustomEvent('unhandledRejection');\n\t\t\t\t\treturn ev instanceof CustomEvent;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction hasInternetExplorerCustomEvent() {\n\t\t\tif(typeof document !== 'undefined' && typeof document.createEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\t// Try to create one event to make sure it's supported\n\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\tev.initCustomEvent('eventType', false, true, {});\n\t\t\t\t\treturn true;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction initEmitRejection() {\n\t\t\t/*global process, self, CustomEvent*/\n\t\t\tif(typeof process !== 'undefined' && process !== null\n\t\t\t\t&& typeof process.emit === 'function') {\n\t\t\t\t// Returning falsy here means to call the default\n\t\t\t\t// onPotentiallyUnhandledRejection API.  This is safe even in\n\t\t\t\t// browserify since process.emit always returns falsy in browserify:\n\t\t\t\t// https://github.com/defunctzombie/node-process/blob/master/browser.js#L40-L46\n\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\treturn type === 'unhandledRejection'\n\t\t\t\t\t\t? process.emit(type, rejection.value, rejection)\n\t\t\t\t\t\t: process.emit(type, rejection);\n\t\t\t\t};\n\t\t\t} else if(typeof self !== 'undefined' && hasCustomEvent()) {\n\t\t\t\treturn (function (self, CustomEvent) {\n\t\t\t\t\treturn function (type, rejection) {\n\t\t\t\t\t\tvar ev = new CustomEvent(type, {\n\t\t\t\t\t\t\tdetail: {\n\t\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tbubbles: false,\n\t\t\t\t\t\t\tcancelable: true\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, CustomEvent));\n\t\t\t} else if(typeof self !== 'undefined' && hasInternetExplorerCustomEvent()) {\n\t\t\t\treturn (function(self, document) {\n\t\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\t\tev.initCustomEvent(type, false, true, {\n\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, document));\n\t\t\t}\n\n\t\t\treturn noop;\n\t\t}\n\n\t\treturn Promise;\n\t};\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn {\n\t\tpending: toPendingState,\n\t\tfulfilled: toFulfilledState,\n\t\trejected: toRejectedState,\n\t\tinspect: inspect\n\t};\n\n\tfunction toPendingState() {\n\t\treturn { state: 'pending' };\n\t}\n\n\tfunction toRejectedState(e) {\n\t\treturn { state: 'rejected', reason: e };\n\t}\n\n\tfunction toFulfilledState(x) {\n\t\treturn { state: 'fulfilled', value: x };\n\t}\n\n\tfunction inspect(handler) {\n\t\tvar state = handler.state();\n\t\treturn state === 0 ? toPendingState()\n\t\t\t : state > 0   ? toFulfilledState(handler.value)\n\t\t\t               : toRejectedState(handler.value);\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2013 original author or authors */\n\n/**\n * Collection of helpers for interfacing with node-style asynchronous functions\n * using promises.\n *\n * <AUTHOR>\n * @contributor <PERSON><PERSON>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar _liftAll = require('./lib/liftAll');\n\tvar setTimer = require('./lib/env').setTimer;\n\tvar slice = Array.prototype.slice;\n\n\tvar _apply = require('./lib/apply')(when.Promise, dispatch);\n\n\treturn {\n\t\tlift: lift,\n\t\tliftAll: liftAll,\n\t\tapply: apply,\n\t\tcall: call,\n\t\tcreateCallback: createCallback,\n\t\tbindCallback: bindCallback,\n\t\tliftCallback: liftCallback\n\t};\n\n\t/**\n\t * Takes a node-style async function and calls it immediately (with an optional\n\t * array of arguments or promises for arguments). It returns a promise whose\n\t * resolution depends on whether the async functions calls its callback with the\n\t * conventional error argument or not.\n\t *\n\t * With this it becomes possible to leverage existing APIs while still reaping\n\t * the benefits of promises.\n\t *\n\t * @example\n\t *    function onlySmallNumbers(n, callback) {\n\t *\t\tif(n < 10) {\n\t *\t\t\tcallback(null, n + 10);\n\t *\t\t} else {\n\t *\t\t\tcallback(new Error(\"Calculation failed\"));\n\t *\t\t}\n\t *\t}\n\t *\n\t *    var nodefn = require(\"when/node/function\");\n\t *\n\t *    // Logs '15'\n\t *    nodefn.apply(onlySmallNumbers, [5]).then(console.log, console.error);\n\t *\n\t *    // Logs 'Calculation failed'\n\t *    nodefn.apply(onlySmallNumbers, [15]).then(console.log, console.error);\n\t *\n\t * @param {function} f node-style function that will be called\n\t * @param {Array} [args] array of arguments to func\n\t * @returns {Promise} promise for the value func passes to its callback\n\t */\n\tfunction apply(f, args) {\n\t\treturn _apply(f, this, args || []);\n\t}\n\n\tfunction dispatch(f, thisArg, args, h) {\n\t\tvar cb = createCallback(h);\n\t\ttry {\n\t\t\tswitch(args.length) {\n\t\t\t\tcase 2: f.call(thisArg, args[0], args[1], cb); break;\n\t\t\t\tcase 1: f.call(thisArg, args[0], cb); break;\n\t\t\t\tcase 0: f.call(thisArg, cb); break;\n\t\t\t\tdefault:\n\t\t\t\t\targs.push(cb);\n\t\t\t\t\tf.apply(thisArg, args);\n\t\t\t}\n\t\t} catch(e) {\n\t\t\th.reject(e);\n\t\t}\n\t}\n\n\t/**\n\t * Has the same behavior that {@link apply} has, with the difference that the\n\t * arguments to the function are provided individually, while {@link apply} accepts\n\t * a single array.\n\t *\n\t * @example\n\t *    function sumSmallNumbers(x, y, callback) {\n\t *\t\tvar result = x + y;\n\t *\t\tif(result < 10) {\n\t *\t\t\tcallback(null, result);\n\t *\t\t} else {\n\t *\t\t\tcallback(new Error(\"Calculation failed\"));\n\t *\t\t}\n\t *\t}\n\t *\n\t *    // Logs '5'\n\t *    nodefn.call(sumSmallNumbers, 2, 3).then(console.log, console.error);\n\t *\n\t *    // Logs 'Calculation failed'\n\t *    nodefn.call(sumSmallNumbers, 5, 10).then(console.log, console.error);\n\t *\n\t * @param {function} f node-style function that will be called\n\t * @param {...*} [args] arguments that will be forwarded to the function\n\t * @returns {Promise} promise for the value func passes to its callback\n\t */\n\tfunction call(f /*, args... */) {\n\t\treturn _apply(f, this, slice.call(arguments, 1));\n\t}\n\n\t/**\n\t * Takes a node-style function and returns new function that wraps the\n\t * original and, instead of taking a callback, returns a promise. Also, it\n\t * knows how to handle promises given as arguments, waiting for their\n\t * resolution before executing.\n\t *\n\t * Upon execution, the orginal function is executed as well. If it passes\n\t * a truthy value as the first argument to the callback, it will be\n\t * interpreted as an error condition, and the promise will be rejected\n\t * with it. Otherwise, the call is considered a resolution, and the promise\n\t * is resolved with the callback's second argument.\n\t *\n\t * @example\n\t *    var fs = require(\"fs\"), nodefn = require(\"when/node/function\");\n\t *\n\t *    var promiseRead = nodefn.lift(fs.readFile);\n\t *\n\t *    // The promise is resolved with the contents of the file if everything\n\t *    // goes ok\n\t *    promiseRead('exists.txt').then(console.log, console.error);\n\t *\n\t *    // And will be rejected if something doesn't work out\n\t *    // (e.g. the files does not exist)\n\t *    promiseRead('doesnt_exist.txt').then(console.log, console.error);\n\t *\n\t *\n\t * @param {Function} f node-style function to be lifted\n\t * @param {...*} [args] arguments to be prepended for the new function @deprecated\n\t * @returns {Function} a promise-returning function\n\t */\n\tfunction lift(f /*, args... */) {\n\t\tvar args1 = arguments.length > 1 ? slice.call(arguments, 1) : [];\n\t\treturn function() {\n\t\t\t// TODO: Simplify once partialing has been removed\n\t\t\tvar l = args1.length;\n\t\t\tvar al = arguments.length;\n\t\t\tvar args = new Array(al + l);\n\t\t\tvar i;\n\t\t\tfor(i=0; i<l; ++i) {\n\t\t\t\targs[i] = args1[i];\n\t\t\t}\n\t\t\tfor(i=0; i<al; ++i) {\n\t\t\t\targs[i+l] = arguments[i];\n\t\t\t}\n\t\t\treturn _apply(f, this, args);\n\t\t};\n\t}\n\n\t/**\n\t * Lift all the functions/methods on src\n\t * @param {object|function} src source whose functions will be lifted\n\t * @param {function?} combine optional function for customizing the lifting\n\t *  process. It is passed dst, the lifted function, and the property name of\n\t *  the original function on src.\n\t * @param {(object|function)?} dst option destination host onto which to place lifted\n\t *  functions. If not provided, liftAll returns a new object.\n\t * @returns {*} If dst is provided, returns dst with lifted functions as\n\t *  properties.  If dst not provided, returns a new object with lifted functions.\n\t */\n\tfunction liftAll(src, combine, dst) {\n\t\treturn _liftAll(lift, combine, dst, src);\n\t}\n\n\t/**\n\t * Takes an object that responds to the resolver interface, and returns\n\t * a function that will resolve or reject it depending on how it is called.\n\t *\n\t * @example\n\t *\tfunction callbackTakingFunction(callback) {\n\t *\t\tif(somethingWrongHappened) {\n\t *\t\t\tcallback(error);\n\t *\t\t} else {\n\t *\t\t\tcallback(null, interestingValue);\n\t *\t\t}\n\t *\t}\n\t *\n\t *\tvar when = require('when'), nodefn = require('when/node/function');\n\t *\n\t *\tvar deferred = when.defer();\n\t *\tcallbackTakingFunction(nodefn.createCallback(deferred.resolver));\n\t *\n\t *\tdeferred.promise.then(function(interestingValue) {\n\t *\t\t// Use interestingValue\n\t *\t});\n\t *\n\t * @param {Resolver} resolver that will be 'attached' to the callback\n\t * @returns {Function} a node-style callback function\n\t */\n\tfunction createCallback(resolver) {\n\t\treturn function(err, value) {\n\t\t\tif(err) {\n\t\t\t\tresolver.reject(err);\n\t\t\t} else if(arguments.length > 2) {\n\t\t\t\tresolver.resolve(slice.call(arguments, 1));\n\t\t\t} else {\n\t\t\t\tresolver.resolve(value);\n\t\t\t}\n\t\t};\n\t}\n\n\t/**\n\t * Attaches a node-style callback to a promise, ensuring the callback is\n\t * called for either fulfillment or rejection. Returns a promise with the same\n\t * state as the passed-in promise.\n\t *\n\t * @example\n\t *\tvar deferred = when.defer();\n\t *\n\t *\tfunction callback(err, value) {\n\t *\t\t// Handle err or use value\n\t *\t}\n\t *\n\t *\tbindCallback(deferred.promise, callback);\n\t *\n\t *\tdeferred.resolve('interesting value');\n\t *\n\t * @param {Promise} promise The promise to be attached to.\n\t * @param {Function} callback The node-style callback to attach.\n\t * @returns {Promise} A promise with the same state as the passed-in promise.\n\t */\n\tfunction bindCallback(promise, callback) {\n\t\tpromise = when(promise);\n\n\t\tif (callback) {\n\t\t\tpromise.then(success, wrapped);\n\t\t}\n\n\t\treturn promise;\n\n\t\tfunction success(value) {\n\t\t\twrapped(null, value);\n\t\t}\n\n\t\tfunction wrapped(err, value) {\n\t\t\tsetTimer(function () {\n\t\t\t\tcallback(err, value);\n\t\t\t}, 0);\n\t\t}\n\t}\n\n\t/**\n\t * Takes a node-style callback and returns new function that accepts a\n\t * promise, calling the original callback when the promise is either\n\t * fulfilled or rejected with the appropriate arguments.\n\t *\n\t * @example\n\t *\tvar deferred = when.defer();\n\t *\n\t *\tfunction callback(err, value) {\n\t *\t\t// Handle err or use value\n\t *\t}\n\t *\n\t *\tvar wrapped = liftCallback(callback);\n\t *\n\t *\t// `wrapped` can now be passed around at will\n\t *\twrapped(deferred.promise);\n\t *\n\t *\tdeferred.resolve('interesting value');\n\t *\n\t * @param {Function} callback The node-style callback to wrap.\n\t * @returns {Function} The lifted, promise-accepting function.\n\t */\n\tfunction liftCallback(callback) {\n\t\treturn function(promise) {\n\t\t\treturn bindCallback(promise, callback);\n\t\t};\n\t}\n});\n\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * parallel.js\n *\n * Run a set of task functions in parallel.  All tasks will\n * receive the same args\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar all = when.Promise.all;\n\tvar slice = Array.prototype.slice;\n\n\t/**\n\t * Run array of tasks in parallel\n\t * @param tasks {Array|Promise} array or promiseForArray of task functions\n\t * @param [args] {*} arguments to be passed to all tasks\n\t * @return {Promise} promise for array containing the\n\t * result of each task in the array position corresponding\n\t * to position of the task in the tasks array\n\t */\n\treturn function parallel(tasks /*, args... */) {\n\t\treturn all(slice.call(arguments, 1)).then(function(args) {\n\t\t\treturn when.map(tasks, function(task) {\n\t\t\t\treturn task.apply(void 0, args);\n\t\t\t});\n\t\t});\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * pipeline.js\n *\n * Run a set of task functions in sequence, passing the result\n * of the previous as an argument to the next.  Like a shell\n * pipeline, e.g. `cat file.txt | grep 'foo' | sed -e 's/foo/bar/g'\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar all = when.Promise.all;\n\tvar slice = Array.prototype.slice;\n\n\t/**\n\t * Run array of tasks in a pipeline where the next\n\t * tasks receives the result of the previous.  The first task\n\t * will receive the initialArgs as its argument list.\n\t * @param tasks {Array|Promise} array or promise for array of task functions\n\t * @param [initialArgs...] {*} arguments to be passed to the first task\n\t * @return {Promise} promise for return value of the final task\n\t */\n\treturn function pipeline(tasks /* initialArgs... */) {\n\t\t// Self-optimizing function to run first task with multiple\n\t\t// args using apply, but subsequence tasks via direct invocation\n\t\tvar runTask = function(args, task) {\n\t\t\trunTask = function(arg, task) {\n\t\t\t\treturn task(arg);\n\t\t\t};\n\n\t\t\treturn task.apply(null, args);\n\t\t};\n\n\t\treturn all(slice.call(arguments, 1)).then(function(args) {\n\t\t\treturn when.reduce(tasks, function(arg, task) {\n\t\t\t\treturn runTask(arg, task);\n\t\t\t}, args);\n\t\t});\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2012-2013 original author or authors */\n\n/**\n * poll.js\n *\n * Helper that polls until cancelled or for a condition to become true.\n *\n * <AUTHOR>\n */\n\n(function (define) { 'use strict';\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar attempt = when['try'];\n\tvar cancelable = require('./cancelable');\n\n\t/**\n\t * Periodically execute the task function on the msec delay. The result of\n\t * the task may be verified by watching for a condition to become true. The\n\t * returned deferred is cancellable if the polling needs to be cancelled\n\t * externally before reaching a resolved state.\n\t *\n\t * The next vote is scheduled after the results of the current vote are\n\t * verified and rejected.\n\t *\n\t * Polling may be terminated by the verifier returning a truthy value,\n\t * invoking cancel() on the returned promise, or the task function returning\n\t * a rejected promise.\n\t *\n\t * Usage:\n\t *\n\t * var count = 0;\n\t * function doSomething() { return count++ }\n\t *\n\t * // poll until cancelled\n\t * var p = poll(doSomething, 1000);\n\t * ...\n\t * p.cancel();\n\t *\n\t * // poll until condition is met\n\t * poll(doSomething, 1000, function(result) { return result > 10 })\n\t *     .then(function(result) { assert result == 10 });\n\t *\n\t * // delay first vote\n\t * poll(doSomething, 1000, anyFunc, true);\n\t *\n\t * @param task {Function} function that is executed after every timeout\n\t * @param interval {number|Function} timeout in milliseconds\n\t * @param [verifier] {Function} function to evaluate the result of the vote.\n\t *     May return a {Promise} or a {Boolean}. Rejecting the promise or a\n\t *     falsey value will schedule the next vote.\n\t * @param [delayInitialTask] {boolean} if truthy, the first vote is scheduled\n\t *     instead of immediate\n\t *\n\t * @returns {Promise}\n\t */\n\treturn function poll(task, interval, verifier, delayInitialTask) {\n\t\tvar deferred, canceled, reject;\n\n\t\tcanceled = false;\n\t\tdeferred = cancelable(when.defer(), function () { canceled = true; });\n\t\treject = deferred.reject;\n\n\t\tverifier = verifier || function () { return false; };\n\n\t\tif (typeof interval !== 'function') {\n\t\t\tinterval = (function (interval) {\n\t\t\t\treturn function () { return when().delay(interval); };\n\t\t\t})(interval);\n\t\t}\n\n\t\tfunction certify(result) {\n\t\t\tdeferred.resolve(result);\n\t\t}\n\n\t\tfunction schedule(result) {\n\t\t\tattempt(interval).then(vote, reject);\n\t\t\tif (result !== void 0) {\n\t\t\t\tdeferred.notify(result);\n\t\t\t}\n\t\t}\n\n\t\tfunction vote() {\n\t\t\tif (canceled) { return; }\n\t\t\twhen(task(),\n\t\t\t\tfunction (result) {\n\t\t\t\t\twhen(verifier(result),\n\t\t\t\t\t\tfunction (verification) {\n\t\t\t\t\t\t\treturn verification ? certify(result) : schedule(result);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfunction () { schedule(result); }\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\treject\n\t\t\t);\n\t\t}\n\n\t\tif (delayInitialTask) {\n\t\t\tschedule();\n\t\t} else {\n\t\t\t// if task() is blocking, vote will also block\n\t\t\tvote();\n\t\t}\n\n\t\t// make the promise cancelable\n\t\tdeferred.promise = Object.create(deferred.promise);\n\t\tdeferred.promise.cancel = deferred.cancel;\n\n\t\treturn deferred.promise;\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * sequence.js\n *\n * Run a set of task functions in sequence.  All tasks will\n * receive the same args.\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar all = when.Promise.all;\n\tvar slice = Array.prototype.slice;\n\n\t/**\n\t * Run array of tasks in sequence with no overlap\n\t * @param tasks {Array|Promise} array or promiseForArray of task functions\n\t * @param [args] {*} arguments to be passed to all tasks\n\t * @return {Promise} promise for an array containing\n\t * the result of each task in the array position corresponding\n\t * to position of the task in the tasks array\n\t */\n\treturn function sequence(tasks /*, args... */) {\n\t\tvar results = [];\n\n\t\treturn all(slice.call(arguments, 1)).then(function(args) {\n\t\t\treturn when.reduce(tasks, function(results, task) {\n\t\t\t\treturn when(task.apply(void 0, args), addResult);\n\t\t\t}, results);\n\t\t});\n\n\t\tfunction addResult(result) {\n\t\t\tresults.push(result);\n\t\t\treturn results;\n\t\t}\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * timeout.js\n *\n * Helper that returns a promise that rejects after a specified timeout,\n * if not explicitly resolved or rejected before that.\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\n    /**\n\t * @deprecated Use when(trigger).timeout(ms)\n     */\n    return function timeout(msec, trigger) {\n\t\treturn when(trigger).timeout(msec);\n    };\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n\n/**\n * Promises/A+ and when() implementation\n * when is part of the cujoJS family of libraries (http://cujojs.com/)\n * <AUTHOR>\n * <AUTHOR>\n */\n(function(define) { 'use strict';\ndefine(function (require) {\n\n\tvar timed = require('./lib/decorators/timed');\n\tvar array = require('./lib/decorators/array');\n\tvar flow = require('./lib/decorators/flow');\n\tvar fold = require('./lib/decorators/fold');\n\tvar inspect = require('./lib/decorators/inspect');\n\tvar generate = require('./lib/decorators/iterate');\n\tvar progress = require('./lib/decorators/progress');\n\tvar withThis = require('./lib/decorators/with');\n\tvar unhandledRejection = require('./lib/decorators/unhandledRejection');\n\tvar TimeoutError = require('./lib/TimeoutError');\n\n\tvar Promise = [array, flow, fold, generate, progress,\n\t\tinspect, withThis, timed, unhandledRejection]\n\t\t.reduce(function(Promise, feature) {\n\t\t\treturn feature(Promise);\n\t\t}, require('./lib/Promise'));\n\n\tvar apply = require('./lib/apply')(Promise);\n\n\t// Public API\n\n\twhen.promise     = promise;              // Create a pending promise\n\twhen.resolve     = Promise.resolve;      // Create a resolved promise\n\twhen.reject      = Promise.reject;       // Create a rejected promise\n\n\twhen.lift        = lift;                 // lift a function to return promises\n\twhen['try']      = attempt;              // call a function and return a promise\n\twhen.attempt     = attempt;              // alias for when.try\n\n\twhen.iterate     = Promise.iterate;      // DEPRECATED (use cujojs/most streams) Generate a stream of promises\n\twhen.unfold      = Promise.unfold;       // DEPRECATED (use cujojs/most streams) Generate a stream of promises\n\n\twhen.join        = join;                 // Join 2 or more promises\n\n\twhen.all         = all;                  // Resolve a list of promises\n\twhen.settle      = settle;               // Settle a list of promises\n\n\twhen.any         = lift(Promise.any);    // One-winner race\n\twhen.some        = lift(Promise.some);   // Multi-winner race\n\twhen.race        = lift(Promise.race);   // First-to-settle race\n\n\twhen.map         = map;                  // Array.map() for promises\n\twhen.filter      = filter;               // Array.filter() for promises\n\twhen.reduce      = lift(Promise.reduce);       // Array.reduce() for promises\n\twhen.reduceRight = lift(Promise.reduceRight);  // Array.reduceRight() for promises\n\n\twhen.isPromiseLike = isPromiseLike;      // Is something promise-like, aka thenable\n\n\twhen.Promise     = Promise;              // Promise constructor\n\twhen.defer       = defer;                // Create a {promise, resolve, reject} tuple\n\n\t// Error types\n\n\twhen.TimeoutError = TimeoutError;\n\n\t/**\n\t * Get a trusted promise for x, or by transforming x with onFulfilled\n\t *\n\t * @param {*} x\n\t * @param {function?} onFulfilled callback to be called when x is\n\t *   successfully fulfilled.  If promiseOrValue is an immediate value, callback\n\t *   will be invoked immediately.\n\t * @param {function?} onRejected callback to be called when x is\n\t *   rejected.\n\t * @param {function?} onProgress callback to be called when progress updates\n\t *   are issued for x. @deprecated\n\t * @returns {Promise} a new promise that will fulfill with the return\n\t *   value of callback or errback or the completion value of promiseOrValue if\n\t *   callback and/or errback is not supplied.\n\t */\n\tfunction when(x, onFulfilled, onRejected, onProgress) {\n\t\tvar p = Promise.resolve(x);\n\t\tif (arguments.length < 2) {\n\t\t\treturn p;\n\t\t}\n\n\t\treturn p.then(onFulfilled, onRejected, onProgress);\n\t}\n\n\t/**\n\t * Creates a new promise whose fate is determined by resolver.\n\t * @param {function} resolver function(resolve, reject, notify)\n\t * @returns {Promise} promise whose fate is determine by resolver\n\t */\n\tfunction promise(resolver) {\n\t\treturn new Promise(resolver);\n\t}\n\n\t/**\n\t * Lift the supplied function, creating a version of f that returns\n\t * promises, and accepts promises as arguments.\n\t * @param {function} f\n\t * @returns {Function} version of f that returns promises\n\t */\n\tfunction lift(f) {\n\t\treturn function() {\n\t\t\tfor(var i=0, l=arguments.length, a=new Array(l); i<l; ++i) {\n\t\t\t\ta[i] = arguments[i];\n\t\t\t}\n\t\t\treturn apply(f, this, a);\n\t\t};\n\t}\n\n\t/**\n\t * Call f in a future turn, with the supplied args, and return a promise\n\t * for the result.\n\t * @param {function} f\n\t * @returns {Promise}\n\t */\n\tfunction attempt(f /*, args... */) {\n\t\t/*jshint validthis:true */\n\t\tfor(var i=0, l=arguments.length-1, a=new Array(l); i<l; ++i) {\n\t\t\ta[i] = arguments[i+1];\n\t\t}\n\t\treturn apply(f, this, a);\n\t}\n\n\t/**\n\t * Creates a {promise, resolver} pair, either or both of which\n\t * may be given out safely to consumers.\n\t * @return {{promise: Promise, resolve: function, reject: function, notify: function}}\n\t */\n\tfunction defer() {\n\t\treturn new Deferred();\n\t}\n\n\tfunction Deferred() {\n\t\tvar p = Promise._defer();\n\n\t\tfunction resolve(x) { p._handler.resolve(x); }\n\t\tfunction reject(x) { p._handler.reject(x); }\n\t\tfunction notify(x) { p._handler.notify(x); }\n\n\t\tthis.promise = p;\n\t\tthis.resolve = resolve;\n\t\tthis.reject = reject;\n\t\tthis.notify = notify;\n\t\tthis.resolver = { resolve: resolve, reject: reject, notify: notify };\n\t}\n\n\t/**\n\t * Determines if x is promise-like, i.e. a thenable object\n\t * NOTE: Will return true for *any thenable object*, and isn't truly\n\t * safe, since it may attempt to access the `then` property of x (i.e.\n\t *  clever/malicious getters may do weird things)\n\t * @param {*} x anything\n\t * @returns {boolean} true if x is promise-like\n\t */\n\tfunction isPromiseLike(x) {\n\t\treturn x && typeof x.then === 'function';\n\t}\n\n\t/**\n\t * Return a promise that will resolve only once all the supplied arguments\n\t * have resolved. The resolution value of the returned promise will be an array\n\t * containing the resolution values of each of the arguments.\n\t * @param {...*} arguments may be a mix of promises and values\n\t * @returns {Promise}\n\t */\n\tfunction join(/* ...promises */) {\n\t\treturn Promise.all(arguments);\n\t}\n\n\t/**\n\t * Return a promise that will fulfill once all input promises have\n\t * fulfilled, or reject when any one input promise rejects.\n\t * @param {array|Promise} promises array (or promise for an array) of promises\n\t * @returns {Promise}\n\t */\n\tfunction all(promises) {\n\t\treturn when(promises, Promise.all);\n\t}\n\n\t/**\n\t * Return a promise that will always fulfill with an array containing\n\t * the outcome states of all input promises.  The returned promise\n\t * will only reject if `promises` itself is a rejected promise.\n\t * @param {array|Promise} promises array (or promise for an array) of promises\n\t * @returns {Promise} promise for array of settled state descriptors\n\t */\n\tfunction settle(promises) {\n\t\treturn when(promises, Promise.settle);\n\t}\n\n\t/**\n\t * Promise-aware array map function, similar to `Array.prototype.map()`,\n\t * but input array may contain promises or values.\n\t * @param {Array|Promise} promises array of anything, may contain promises and values\n\t * @param {function(x:*, index:Number):*} mapFunc map function which may\n\t *  return a promise or value\n\t * @returns {Promise} promise that will fulfill with an array of mapped values\n\t *  or reject if any input promise rejects.\n\t */\n\tfunction map(promises, mapFunc) {\n\t\treturn when(promises, function(promises) {\n\t\t\treturn Promise.map(promises, mapFunc);\n\t\t});\n\t}\n\n\t/**\n\t * Filter the provided array of promises using the provided predicate.  Input may\n\t * contain promises and values\n\t * @param {Array|Promise} promises array of promises and values\n\t * @param {function(x:*, index:Number):boolean} predicate filtering predicate.\n\t *  Must return truthy (or promise for truthy) for items to retain.\n\t * @returns {Promise} promise that will fulfill with an array containing all items\n\t *  for which predicate returned truthy.\n\t */\n\tfunction filter(promises, predicate) {\n\t\treturn when(promises, function(promises) {\n\t\t\treturn Promise.filter(promises, predicate);\n\t\t});\n\t}\n\n\treturn when;\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "var when = module.exports = require('../when');\n\nwhen.callbacks = require('../callbacks');\nwhen.cancelable = require('../cancelable');\nwhen.delay = require('../delay');\nwhen.fn = require('../function');\nwhen.guard = require('../guard');\nwhen.keys = require('../keys');\nwhen.nodefn = when.node = require('../node');\nwhen.parallel = require('../parallel');\nwhen.pipeline = require('../pipeline');\nwhen.poll = require('../poll');\nwhen.sequence = require('../sequence');\nwhen.timeout = require('../timeout');\n", "/** @license MIT License (c) copyright 2013-2014 original author or authors */\n\n/**\n * Collection of helper functions for interacting with 'traditional',\n * callback-taking functions using a promise interface.\n *\n * <AUTHOR>\n * @contributor <PERSON>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar Promise = when.Promise;\n\tvar _liftAll = require('./lib/liftAll');\n\tvar slice = Array.prototype.slice;\n\n\tvar makeApply = require('./lib/apply');\n\tvar _apply = makeApply(Promise, dispatch);\n\n\treturn {\n\t\tlift: lift,\n\t\tliftAll: liftAll,\n\t\tapply: apply,\n\t\tcall: call,\n\t\tpromisify: promisify\n\t};\n\n\t/**\n\t * Takes a `traditional` callback-taking function and returns a promise for its\n\t * result, accepting an optional array of arguments (that might be values or\n\t * promises). It assumes that the function takes its callback and errback as\n\t * the last two arguments. The resolution of the promise depends on whether the\n\t * function will call its callback or its errback.\n\t *\n\t * @example\n\t *    var domIsLoaded = callbacks.apply($);\n\t *    domIsLoaded.then(function() {\n\t *\t\tdoMyDomStuff();\n\t *\t});\n\t *\n\t * @example\n\t *    function existingAjaxyFunction(url, callback, errback) {\n\t *\t\t// Complex logic you'd rather not change\n\t *\t}\n\t *\n\t *    var promise = callbacks.apply(existingAjaxyFunction, [\"/movies.json\"]);\n\t *\n\t *    promise.then(function(movies) {\n\t *\t\t// Work with movies\n\t *\t}, function(reason) {\n\t *\t\t// Handle error\n\t *\t});\n\t *\n\t * @param {function} asyncFunction function to be called\n\t * @param {Array} [extraAsyncArgs] array of arguments to asyncFunction\n\t * @returns {Promise} promise for the callback value of asyncFunction\n\t */\n\tfunction apply(asyncFunction, extraAsyncArgs) {\n\t\treturn _apply(asyncFunction, this, extraAsyncArgs || []);\n\t}\n\n\t/**\n\t * Apply helper that allows specifying thisArg\n\t * @private\n\t */\n\tfunction dispatch(f, thisArg, args, h) {\n\t\targs.push(alwaysUnary(h.resolve, h), alwaysUnary(h.reject, h));\n\t\ttryCatchResolve(f, thisArg, args, h);\n\t}\n\n\tfunction tryCatchResolve(f, thisArg, args, resolver) {\n\t\ttry {\n\t\t\tf.apply(thisArg, args);\n\t\t} catch(e) {\n\t\t\tresolver.reject(e);\n\t\t}\n\t}\n\n\t/**\n\t * Works as `callbacks.apply` does, with the difference that the arguments to\n\t * the function are passed individually, instead of as an array.\n\t *\n\t * @example\n\t *    function sumInFiveSeconds(a, b, callback) {\n\t *\t\tsetTimeout(function() {\n\t *\t\t\tcallback(a + b);\n\t *\t\t}, 5000);\n\t *\t}\n\t *\n\t *    var sumPromise = callbacks.call(sumInFiveSeconds, 5, 10);\n\t *\n\t *    // Logs '15' 5 seconds later\n\t *    sumPromise.then(console.log);\n\t *\n\t * @param {function} asyncFunction function to be called\n\t * @param {...*} args arguments that will be forwarded to the function\n\t * @returns {Promise} promise for the callback value of asyncFunction\n\t */\n\tfunction call(asyncFunction/*, arg1, arg2...*/) {\n\t\treturn _apply(asyncFunction, this, slice.call(arguments, 1));\n\t}\n\n\t/**\n\t * Takes a 'traditional' callback/errback-taking function and returns a function\n\t * that returns a promise instead. The resolution/rejection of the promise\n\t * depends on whether the original function will call its callback or its\n\t * errback.\n\t *\n\t * If additional arguments are passed to the `lift` call, they will be prepended\n\t * on the calls to the original function, much like `Function.prototype.bind`.\n\t *\n\t * The resulting function is also \"promise-aware\", in the sense that, if given\n\t * promises as arguments, it will wait for their resolution before executing.\n\t *\n\t * @example\n\t *    function traditionalAjax(method, url, callback, errback) {\n\t *\t\tvar xhr = new XMLHttpRequest();\n\t *\t\txhr.open(method, url);\n\t *\n\t *\t\txhr.onload = callback;\n\t *\t\txhr.onerror = errback;\n\t *\n\t *\t\txhr.send();\n\t *\t}\n\t *\n\t *    var promiseAjax = callbacks.lift(traditionalAjax);\n\t *    promiseAjax(\"GET\", \"/movies.json\").then(console.log, console.error);\n\t *\n\t *    var promiseAjaxGet = callbacks.lift(traditionalAjax, \"GET\");\n\t *    promiseAjaxGet(\"/movies.json\").then(console.log, console.error);\n\t *\n\t * @param {Function} f traditional async function to be decorated\n\t * @param {...*} [args] arguments to be prepended for the new function @deprecated\n\t * @returns {Function} a promise-returning function\n\t */\n\tfunction lift(f/*, args...*/) {\n\t\tvar args = arguments.length > 1 ? slice.call(arguments, 1) : [];\n\t\treturn function() {\n\t\t\treturn _apply(f, this, args.concat(slice.call(arguments)));\n\t\t};\n\t}\n\n\t/**\n\t * Lift all the functions/methods on src\n\t * @param {object|function} src source whose functions will be lifted\n\t * @param {function?} combine optional function for customizing the lifting\n\t *  process. It is passed dst, the lifted function, and the property name of\n\t *  the original function on src.\n\t * @param {(object|function)?} dst option destination host onto which to place lifted\n\t *  functions. If not provided, liftAll returns a new object.\n\t * @returns {*} If dst is provided, returns dst with lifted functions as\n\t *  properties.  If dst not provided, returns a new object with lifted functions.\n\t */\n\tfunction liftAll(src, combine, dst) {\n\t\treturn _liftAll(lift, combine, dst, src);\n\t}\n\n\t/**\n\t * `promisify` is a version of `lift` that allows fine-grained control over the\n\t * arguments that passed to the underlying function. It is intended to handle\n\t * functions that don't follow the common callback and errback positions.\n\t *\n\t * The control is done by passing an object whose 'callback' and/or 'errback'\n\t * keys, whose values are the corresponding 0-based indexes of the arguments on\n\t * the function. Negative values are interpreted as being relative to the end\n\t * of the arguments array.\n\t *\n\t * If arguments are given on the call to the 'promisified' function, they are\n\t * intermingled with the callback and errback. If a promise is given among them,\n\t * the execution of the function will only occur after its resolution.\n\t *\n\t * @example\n\t *    var delay = callbacks.promisify(setTimeout, {\n\t *\t\tcallback: 0\n\t *\t});\n\t *\n\t *    delay(100).then(function() {\n\t *\t\tconsole.log(\"This happens 100ms afterwards\");\n\t *\t});\n\t *\n\t * @example\n\t *    function callbackAsLast(errback, followsStandards, callback) {\n\t *\t\tif(followsStandards) {\n\t *\t\t\tcallback(\"well done!\");\n\t *\t\t} else {\n\t *\t\t\terrback(\"some programmers just want to watch the world burn\");\n\t *\t\t}\n\t *\t}\n\t *\n\t *    var promisified = callbacks.promisify(callbackAsLast, {\n\t *\t\tcallback: -1,\n\t *\t\terrback:   0,\n\t *\t});\n\t *\n\t *    promisified(true).then(console.log, console.error);\n\t *    promisified(false).then(console.log, console.error);\n\t *\n\t * @param {Function} asyncFunction traditional function to be decorated\n\t * @param {object} positions\n\t * @param {number} [positions.callback] index at which asyncFunction expects to\n\t *  receive a success callback\n\t * @param {number} [positions.errback] index at which asyncFunction expects to\n\t *  receive an error callback\n\t *  @returns {function} promisified function that accepts\n\t *\n\t * @deprecated\n\t */\n\tfunction promisify(asyncFunction, positions) {\n\n\t\treturn function() {\n\t\t\tvar thisArg = this;\n\t\t\treturn Promise.all(arguments).then(function(args) {\n\t\t\t\tvar p = Promise._defer();\n\n\t\t\t\tvar callbackPos, errbackPos;\n\n\t\t\t\tif(typeof positions.callback === 'number') {\n\t\t\t\t\tcallbackPos = normalizePosition(args, positions.callback);\n\t\t\t\t}\n\n\t\t\t\tif(typeof positions.errback === 'number') {\n\t\t\t\t\terrbackPos = normalizePosition(args, positions.errback);\n\t\t\t\t}\n\n\t\t\t\tif(errbackPos < callbackPos) {\n\t\t\t\t\tinsertCallback(args, errbackPos, p._handler.reject, p._handler);\n\t\t\t\t\tinsertCallback(args, callbackPos, p._handler.resolve, p._handler);\n\t\t\t\t} else {\n\t\t\t\t\tinsertCallback(args, callbackPos, p._handler.resolve, p._handler);\n\t\t\t\t\tinsertCallback(args, errbackPos, p._handler.reject, p._handler);\n\t\t\t\t}\n\n\t\t\t\tasyncFunction.apply(thisArg, args);\n\n\t\t\t\treturn p;\n\t\t\t});\n\t\t};\n\t}\n\n\tfunction normalizePosition(args, pos) {\n\t\treturn pos < 0 ? (args.length + pos + 2) : pos;\n\t}\n\n\tfunction insertCallback(args, pos, callback, thisArg) {\n\t\tif(typeof pos === 'number') {\n\t\t\targs.splice(pos, 0, alwaysUnary(callback, thisArg));\n\t\t}\n\t}\n\n\tfunction alwaysUnary(fn, thisArg) {\n\t\treturn function() {\n\t\t\tif (arguments.length > 1) {\n\t\t\t\tfn.call(thisArg, slice.call(arguments));\n\t\t\t} else {\n\t\t\t\tfn.apply(thisArg, arguments);\n\t\t\t}\n\t\t};\n\t}\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright B Cavalier & <PERSON> */\n\n/**\n * cancelable.js\n * @deprecated\n *\n * Decorator that makes a deferred \"cancelable\".  It adds a cancel() method that\n * will call a special cancel handler function and then reject the deferred.  The\n * cancel handler can be used to do resource cleanup, or anything else that should\n * be done before any other rejection handlers are executed.\n *\n * Usage:\n *\n * var cancelableDeferred = cancelable(when.defer(), myCancelHandler);\n *\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function() {\n\n    /**\n     * Makes deferred cancelable, adding a cancel() method.\n\t * @deprecated\n     *\n     * @param deferred {Deferred} the {@link Deferred} to make cancelable\n     * @param canceler {Function} cancel handler function to execute when this deferred\n\t * is canceled.  This is guaranteed to run before all other rejection handlers.\n\t * The canceler will NOT be executed if the deferred is rejected in the standard\n\t * way, i.e. deferred.reject().  It ONLY executes if the deferred is canceled,\n\t * i.e. deferred.cancel()\n     *\n     * @returns deferred, with an added cancel() method.\n     */\n    return function(deferred, canceler) {\n        // Add a cancel method to the deferred to reject the delegate\n        // with the special canceled indicator.\n        deferred.cancel = function() {\n\t\t\ttry {\n\t\t\t\tdeferred.reject(canceler(deferred));\n\t\t\t} catch(e) {\n\t\t\t\tdeferred.reject(e);\n\t\t\t}\n\n\t\t\treturn deferred.promise;\n        };\n\n        return deferred;\n    };\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * delay.js\n *\n * Helper that returns a promise that resolves after a delay.\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\n    /**\n\t * @deprecated Use when(value).delay(ms)\n     */\n    return function delay(msec, value) {\n\t\treturn when(value).delay(msec);\n    };\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2013-2014 original author or authors */\n\n/**\n * Collection of helper functions for wrapping and executing 'traditional'\n * synchronous functions in a promise interface.\n *\n * <AUTHOR>\n * @contributor <PERSON><PERSON>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar attempt = when['try'];\n\tvar _liftAll = require('./lib/liftAll');\n\tvar _apply = require('./lib/apply')(when.Promise);\n\tvar slice = Array.prototype.slice;\n\n\treturn {\n\t\tlift: lift,\n\t\tliftAll: liftAll,\n\t\tcall: attempt,\n\t\tapply: apply,\n\t\tcompose: compose\n\t};\n\n\t/**\n\t * Takes a function and an optional array of arguments (that might be promises),\n\t * and calls the function. The return value is a promise whose resolution\n\t * depends on the value returned by the function.\n\t * @param {function} f function to be called\n\t * @param {Array} [args] array of arguments to func\n\t * @returns {Promise} promise for the return value of func\n\t */\n\tfunction apply(f, args) {\n\t\t// slice args just in case the caller passed an Arguments instance\n\t\treturn _apply(f, this, args == null ? [] : slice.call(args));\n\t}\n\n\t/**\n\t * Takes a 'regular' function and returns a version of that function that\n\t * returns a promise instead of a plain value, and handles thrown errors by\n\t * returning a rejected promise. Also accepts a list of arguments to be\n\t * prepended to the new function, as does Function.prototype.bind.\n\t *\n\t * The resulting function is promise-aware, in the sense that it accepts\n\t * promise arguments, and waits for their resolution.\n\t * @param {Function} f function to be bound\n\t * @param {...*} [args] arguments to be prepended for the new function @deprecated\n\t * @returns {Function} a promise-returning function\n\t */\n\tfunction lift(f /*, args... */) {\n\t\tvar args = arguments.length > 1 ? slice.call(arguments, 1) : [];\n\t\treturn function() {\n\t\t\treturn _apply(f, this, args.concat(slice.call(arguments)));\n\t\t};\n\t}\n\n\t/**\n\t * Lift all the functions/methods on src\n\t * @param {object|function} src source whose functions will be lifted\n\t * @param {function?} combine optional function for customizing the lifting\n\t *  process. It is passed dst, the lifted function, and the property name of\n\t *  the original function on src.\n\t * @param {(object|function)?} dst option destination host onto which to place lifted\n\t *  functions. If not provided, liftAll returns a new object.\n\t * @returns {*} If dst is provided, returns dst with lifted functions as\n\t *  properties.  If dst not provided, returns a new object with lifted functions.\n\t */\n\tfunction liftAll(src, combine, dst) {\n\t\treturn _liftAll(lift, combine, dst, src);\n\t}\n\n\t/**\n\t * Composes multiple functions by piping their return values. It is\n\t * transparent to whether the functions return 'regular' values or promises:\n\t * the piped argument is always a resolved value. If one of the functions\n\t * throws or returns a rejected promise, the composed promise will be also\n\t * rejected.\n\t *\n\t * The arguments (or promises to arguments) given to the returned function (if\n\t * any), are passed directly to the first function on the 'pipeline'.\n\t * @param {Function} f the function to which the arguments will be passed\n\t * @param {...Function} [funcs] functions that will be composed, in order\n\t * @returns {Function} a promise-returning composition of the functions\n\t */\n\tfunction compose(f /*, funcs... */) {\n\t\tvar funcs = slice.call(arguments, 1);\n\n\t\treturn function() {\n\t\t\tvar thisArg = this;\n\t\t\tvar args = slice.call(arguments);\n\t\t\tvar firstPromise = attempt.apply(thisArg, [f].concat(args));\n\n\t\t\treturn when.reduce(funcs, function(arg, func) {\n\t\t\t\treturn func.call(thisArg, arg);\n\t\t\t}, firstPromise);\n\t\t};\n\t}\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * Generalized promise concurrency guard\n * Adapted from original concept by <PERSON><PERSON> (Rocket Pack, Ltd.)\n *\n * <AUTHOR>\n * <AUTHOR>\n * @contributor <PERSON><PERSON>\n */\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar slice = Array.prototype.slice;\n\n\tguard.n = n;\n\n\treturn guard;\n\n\t/**\n\t * Creates a guarded version of f that can only be entered when the supplied\n\t * condition allows.\n\t * @param {function} condition represents a critical section that may only\n\t *  be entered when allowed by the condition\n\t * @param {function} f function to guard\n\t * @returns {function} guarded version of f\n\t */\n\tfunction guard(condition, f) {\n\t\treturn function() {\n\t\t\tvar args = slice.call(arguments);\n\n\t\t\treturn when(condition()).withThis(this).then(function(exit) {\n\t\t\t\treturn when(f.apply(this, args))['finally'](exit);\n\t\t\t});\n\t\t};\n\t}\n\n\t/**\n\t * Creates a condition that allows only n simultaneous executions\n\t * of a guarded function\n\t * @param {number} allowed number of allowed simultaneous executions\n\t * @returns {function} condition function which returns a promise that\n\t *  fulfills when the critical section may be entered.  The fulfillment\n\t *  value is a function (\"notifyExit\") that must be called when the critical\n\t *  section has been exited.\n\t */\n\tfunction n(allowed) {\n\t\tvar count = 0;\n\t\tvar waiting = [];\n\n\t\treturn function enter() {\n\t\t\treturn when.promise(function(resolve) {\n\t\t\t\tif(count < allowed) {\n\t\t\t\t\tresolve(exit);\n\t\t\t\t} else {\n\t\t\t\t\twaiting.push(resolve);\n\t\t\t\t}\n\t\t\t\tcount += 1;\n\t\t\t});\n\t\t};\n\n\t\tfunction exit() {\n\t\t\tcount = Math.max(count - 1, 0);\n\t\t\tif(waiting.length > 0) {\n\t\t\t\twaiting.shift()(exit);\n\t\t\t}\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * Licensed under the MIT License at:\n * http://www.opensource.org/licenses/mit-license.php\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar Promise = when.Promise;\n\tvar toPromise = when.resolve;\n\n\treturn {\n\t\tall: when.lift(all),\n\t\tmap: map,\n\t\tsettle: settle\n\t};\n\n\t/**\n\t * Resolve all the key-value pairs in the supplied object or promise\n\t * for an object.\n\t * @param {Promise|object} object or promise for object whose key-value pairs\n\t *  will be resolved\n\t * @returns {Promise} promise for an object with the fully resolved key-value pairs\n\t */\n\tfunction all(object) {\n\t\tvar p = Promise._defer();\n\t\tvar resolver = Promise._handler(p);\n\n\t\tvar results = {};\n\t\tvar keys = Object.keys(object);\n\t\tvar pending = keys.length;\n\n\t\tfor(var i=0, k; i<keys.length; ++i) {\n\t\t\tk = keys[i];\n\t\t\tPromise._handler(object[k]).fold(settleKey, k, results, resolver);\n\t\t}\n\n\t\tif(pending === 0) {\n\t\t\tresolver.resolve(results);\n\t\t}\n\n\t\treturn p;\n\n\t\tfunction settleKey(k, x, resolver) {\n\t\t\t/*jshint validthis:true*/\n\t\t\tthis[k] = x;\n\t\t\tif(--pending === 0) {\n\t\t\t\tresolver.resolve(results);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Map values in the supplied object's keys\n\t * @param {Promise|object} object or promise for object whose key-value pairs\n\t *  will be reduced\n\t * @param {function(value:*, key:String):*} f mapping function which may\n\t *  return either a promise or a value\n\t * @returns {Promise} promise for an object with the mapped and fully\n\t *  resolved key-value pairs\n\t */\n\tfunction map(object, f) {\n\t\treturn toPromise(object).then(function(object) {\n\t\t\treturn all(Object.keys(object).reduce(function(o, k) {\n\t\t\t\to[k] = toPromise(object[k]).fold(mapWithKey, k);\n\t\t\t\treturn o;\n\t\t\t}, {}));\n\t\t});\n\n\t\tfunction mapWithKey(k, x) {\n\t\t\treturn f(x, k);\n\t\t}\n\t}\n\n\t/**\n\t * Resolve all key-value pairs in the supplied object and return a promise\n\t * that will always fulfill with the outcome states of all input promises.\n\t * @param {object} object whose key-value pairs will be settled\n\t * @returns {Promise} promise for an object with the mapped and fully\n\t *  settled key-value pairs\n\t */\n\tfunction settle(object) {\n\t\tvar keys = Object.keys(object);\n\t\tvar results = {};\n\n\t\tif(keys.length === 0) {\n\t\t\treturn toPromise(results);\n\t\t}\n\n\t\tvar p = Promise._defer();\n\t\tvar resolver = Promise._handler(p);\n\t\tvar promises = keys.map(function(k) { return object[k]; });\n\n\t\twhen.settle(promises).then(function(states) {\n\t\t\tpopulateResults(keys, states, results, resolver);\n\t\t});\n\n\t\treturn p;\n\t}\n\n\tfunction populateResults(keys, states, results, resolver) {\n\t\tfor(var i=0; i<keys.length; i++) {\n\t\t\tresults[keys[i]] = states[i];\n\t\t}\n\t\tresolver.resolve(results);\n\t}\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function (require) {\n\n\tvar makePromise = require('./makePromise');\n\tvar Scheduler = require('./Scheduler');\n\tvar async = require('./env').asap;\n\n\treturn makePromise({\n\t\tscheduler: new Scheduler(async)\n\t});\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\t// Credit to Twisol (https://github.com/Twisol) for suggesting\n\t// this type of extensible queue + trampoline approach for next-tick conflation.\n\n\t/**\n\t * Async task scheduler\n\t * @param {function} async function to schedule a single async function\n\t * @constructor\n\t */\n\tfunction Scheduler(async) {\n\t\tthis._async = async;\n\t\tthis._running = false;\n\n\t\tthis._queue = this;\n\t\tthis._queueLen = 0;\n\t\tthis._afterQueue = {};\n\t\tthis._afterQueueLen = 0;\n\n\t\tvar self = this;\n\t\tthis.drain = function() {\n\t\t\tself._drain();\n\t\t};\n\t}\n\n\t/**\n\t * Enqueue a task\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.enqueue = function(task) {\n\t\tthis._queue[this._queueLen++] = task;\n\t\tthis.run();\n\t};\n\n\t/**\n\t * Enqueue a task to run after the main task queue\n\t * @param {{ run:function }} task\n\t */\n\tScheduler.prototype.afterQueue = function(task) {\n\t\tthis._afterQueue[this._afterQueueLen++] = task;\n\t\tthis.run();\n\t};\n\n\tScheduler.prototype.run = function() {\n\t\tif (!this._running) {\n\t\t\tthis._running = true;\n\t\t\tthis._async(this.drain);\n\t\t}\n\t};\n\n\t/**\n\t * Drain the handler queue entirely, and then the after queue\n\t */\n\tScheduler.prototype._drain = function() {\n\t\tvar i = 0;\n\t\tfor (; i < this._queueLen; ++i) {\n\t\t\tthis._queue[i].run();\n\t\t\tthis._queue[i] = void 0;\n\t\t}\n\n\t\tthis._queueLen = 0;\n\t\tthis._running = false;\n\n\t\tfor (i = 0; i < this._afterQueueLen; ++i) {\n\t\t\tthis._afterQueue[i].run();\n\t\t\tthis._afterQueue[i] = void 0;\n\t\t}\n\n\t\tthis._afterQueueLen = 0;\n\t};\n\n\treturn Scheduler;\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\t/**\n\t * Custom error type for promises rejected by promise.timeout\n\t * @param {string} message\n\t * @constructor\n\t */\n\tfunction TimeoutError (message) {\n\t\tError.call(this);\n\t\tthis.message = message;\n\t\tthis.name = TimeoutError.name;\n\t\tif (typeof Error.captureStackTrace === 'function') {\n\t\t\tError.captureStackTrace(this, TimeoutError);\n\t\t}\n\t}\n\n\tTimeoutError.prototype = Object.create(Error.prototype);\n\tTimeoutError.prototype.constructor = TimeoutError;\n\n\treturn TimeoutError;\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\tmakeApply.tryCatchResolve = tryCatchResolve;\n\n\treturn makeApply;\n\n\tfunction makeApply(Promise, call) {\n\t\tif(arguments.length < 2) {\n\t\t\tcall = tryCatchResolve;\n\t\t}\n\n\t\treturn apply;\n\n\t\tfunction apply(f, thisArg, args) {\n\t\t\tvar p = Promise._defer();\n\t\t\tvar l = args.length;\n\t\t\tvar params = new Array(l);\n\t\t\tcallAndResolve({ f:f, thisArg:thisArg, args:args, params:params, i:l-1, call:call }, p._handler);\n\n\t\t\treturn p;\n\t\t}\n\n\t\tfunction callAndResolve(c, h) {\n\t\t\tif(c.i < 0) {\n\t\t\t\treturn call(c.f, c.thisArg, c.params, h);\n\t\t\t}\n\n\t\t\tvar handler = Promise._handler(c.args[c.i]);\n\t\t\thandler.fold(callAndResolveNext, c, void 0, h);\n\t\t}\n\n\t\tfunction callAndResolveNext(c, x, h) {\n\t\t\tc.params[c.i] = x;\n\t\t\tc.i -= 1;\n\t\t\tcallAndResolve(c, h);\n\t\t}\n\t}\n\n\tfunction tryCatchResolve(f, thisArg, args, resolver) {\n\t\ttry {\n\t\t\tresolver.resolve(f.apply(thisArg, args));\n\t\t} catch(e) {\n\t\t\tresolver.reject(e);\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n\n\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar state = require('../state');\n\tvar applier = require('../apply');\n\n\treturn function array(Promise) {\n\n\t\tvar applyFold = applier(Promise);\n\t\tvar toPromise = Promise.resolve;\n\t\tvar all = Promise.all;\n\n\t\tvar ar = Array.prototype.reduce;\n\t\tvar arr = Array.prototype.reduceRight;\n\t\tvar slice = Array.prototype.slice;\n\n\t\t// Additional array combinators\n\n\t\tPromise.any = any;\n\t\tPromise.some = some;\n\t\tPromise.settle = settle;\n\n\t\tPromise.map = map;\n\t\tPromise.filter = filter;\n\t\tPromise.reduce = reduce;\n\t\tPromise.reduceRight = reduceRight;\n\n\t\t/**\n\t\t * When this promise fulfills with an array, do\n\t\t * onFulfilled.apply(void 0, array)\n\t\t * @param {function} onFulfilled function to apply\n\t\t * @returns {Promise} promise for the result of applying onFulfilled\n\t\t */\n\t\tPromise.prototype.spread = function(onFulfilled) {\n\t\t\treturn this.then(all).then(function(array) {\n\t\t\t\treturn onFulfilled.apply(this, array);\n\t\t\t});\n\t\t};\n\n\t\treturn Promise;\n\n\t\t/**\n\t\t * One-winner competitive race.\n\t\t * Return a promise that will fulfill when one of the promises\n\t\t * in the input array fulfills, or will reject when all promises\n\t\t * have rejected.\n\t\t * @param {array} promises\n\t\t * @returns {Promise} promise for the first fulfilled value\n\t\t */\n\t\tfunction any(promises) {\n\t\t\tvar p = Promise._defer();\n\t\t\tvar resolver = p._handler;\n\t\t\tvar l = promises.length>>>0;\n\n\t\t\tvar pending = l;\n\t\t\tvar errors = [];\n\n\t\t\tfor (var h, x, i = 0; i < l; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif(x === void 0 && !(i in promises)) {\n\t\t\t\t\t--pending;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\th = Promise._handler(x);\n\t\t\t\tif(h.state() > 0) {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tPromise._visitRemaining(promises, i, h);\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\th.visit(resolver, handleFulfill, handleReject);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif(pending === 0) {\n\t\t\t\tresolver.reject(new RangeError('any(): array must not be empty'));\n\t\t\t}\n\n\t\t\treturn p;\n\n\t\t\tfunction handleFulfill(x) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\terrors = null;\n\t\t\t\tthis.resolve(x); // this === resolver\n\t\t\t}\n\n\t\t\tfunction handleReject(e) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\tif(this.resolved) { // this === resolver\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\terrors.push(e);\n\t\t\t\tif(--pending === 0) {\n\t\t\t\t\tthis.reject(errors);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * N-winner competitive race\n\t\t * Return a promise that will fulfill when n input promises have\n\t\t * fulfilled, or will reject when it becomes impossible for n\n\t\t * input promises to fulfill (ie when promises.length - n + 1\n\t\t * have rejected)\n\t\t * @param {array} promises\n\t\t * @param {number} n\n\t\t * @returns {Promise} promise for the earliest n fulfillment values\n\t\t *\n\t\t * @deprecated\n\t\t */\n\t\tfunction some(promises, n) {\n\t\t\t/*jshint maxcomplexity:7*/\n\t\t\tvar p = Promise._defer();\n\t\t\tvar resolver = p._handler;\n\n\t\t\tvar results = [];\n\t\t\tvar errors = [];\n\n\t\t\tvar l = promises.length>>>0;\n\t\t\tvar nFulfill = 0;\n\t\t\tvar nReject;\n\t\t\tvar x, i; // reused in both for() loops\n\n\t\t\t// First pass: count actual array items\n\t\t\tfor(i=0; i<l; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif(x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t++nFulfill;\n\t\t\t}\n\n\t\t\t// Compute actual goals\n\t\t\tn = Math.max(n, 0);\n\t\t\tnReject = (nFulfill - n + 1);\n\t\t\tnFulfill = Math.min(n, nFulfill);\n\n\t\t\tif(n > nFulfill) {\n\t\t\t\tresolver.reject(new RangeError('some(): array must contain at least '\n\t\t\t\t+ n + ' item(s), but had ' + nFulfill));\n\t\t\t} else if(nFulfill === 0) {\n\t\t\t\tresolver.resolve(results);\n\t\t\t}\n\n\t\t\t// Second pass: observe each array item, make progress toward goals\n\t\t\tfor(i=0; i<l; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif(x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tPromise._handler(x).visit(resolver, fulfill, reject, resolver.notify);\n\t\t\t}\n\n\t\t\treturn p;\n\n\t\t\tfunction fulfill(x) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\tif(this.resolved) { // this === resolver\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tresults.push(x);\n\t\t\t\tif(--nFulfill === 0) {\n\t\t\t\t\terrors = null;\n\t\t\t\t\tthis.resolve(results);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction reject(e) {\n\t\t\t\t/*jshint validthis:true*/\n\t\t\t\tif(this.resolved) { // this === resolver\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\terrors.push(e);\n\t\t\t\tif(--nReject === 0) {\n\t\t\t\t\tresults = null;\n\t\t\t\t\tthis.reject(errors);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Apply f to the value of each promise in a list of promises\n\t\t * and return a new list containing the results.\n\t\t * @param {array} promises\n\t\t * @param {function(x:*, index:Number):*} f mapping function\n\t\t * @returns {Promise}\n\t\t */\n\t\tfunction map(promises, f) {\n\t\t\treturn Promise._traverse(f, promises);\n\t\t}\n\n\t\t/**\n\t\t * Filter the provided array of promises using the provided predicate.  Input may\n\t\t * contain promises and values\n\t\t * @param {Array} promises array of promises and values\n\t\t * @param {function(x:*, index:Number):boolean} predicate filtering predicate.\n\t\t *  Must return truthy (or promise for truthy) for items to retain.\n\t\t * @returns {Promise} promise that will fulfill with an array containing all items\n\t\t *  for which predicate returned truthy.\n\t\t */\n\t\tfunction filter(promises, predicate) {\n\t\t\tvar a = slice.call(promises);\n\t\t\treturn Promise._traverse(predicate, a).then(function(keep) {\n\t\t\t\treturn filterSync(a, keep);\n\t\t\t});\n\t\t}\n\n\t\tfunction filterSync(promises, keep) {\n\t\t\t// Safe because we know all promises have fulfilled if we've made it this far\n\t\t\tvar l = keep.length;\n\t\t\tvar filtered = new Array(l);\n\t\t\tfor(var i=0, j=0; i<l; ++i) {\n\t\t\t\tif(keep[i]) {\n\t\t\t\t\tfiltered[j++] = Promise._handler(promises[i]).value;\n\t\t\t\t}\n\t\t\t}\n\t\t\tfiltered.length = j;\n\t\t\treturn filtered;\n\n\t\t}\n\n\t\t/**\n\t\t * Return a promise that will always fulfill with an array containing\n\t\t * the outcome states of all input promises.  The returned promise\n\t\t * will never reject.\n\t\t * @param {Array} promises\n\t\t * @returns {Promise} promise for array of settled state descriptors\n\t\t */\n\t\tfunction settle(promises) {\n\t\t\treturn all(promises.map(settleOne));\n\t\t}\n\n\t\tfunction settleOne(p) {\n\t\t\t// Optimize the case where we get an already-resolved when.js promise\n\t\t\t//  by extracting its state:\n\t\t\tvar handler;\n\t\t\tif (p instanceof Promise) {\n\t\t\t\t// This is our own Promise type and we can reach its handler internals:\n\t\t\t\thandler = p._handler.join();\n\t\t\t}\n\t\t\tif((handler && handler.state() === 0) || !handler) {\n\t\t\t\t// Either still pending, or not a Promise at all:\n\t\t\t\treturn toPromise(p).then(state.fulfilled, state.rejected);\n\t\t\t}\n\n\t\t\t// The promise is our own, but it is already resolved. Take a shortcut.\n\t\t\t// Since we're not actually handling the resolution, we need to disable\n\t\t\t// rejection reporting.\n\t\t\thandler._unreport();\n\t\t\treturn state.inspect(handler);\n\t\t}\n\n\t\t/**\n\t\t * Traditional reduce function, similar to `Array.prototype.reduce()`, but\n\t\t * input may contain promises and/or values, and reduceFunc\n\t\t * may return either a value or a promise, *and* initialValue may\n\t\t * be a promise for the starting value.\n\t\t * @param {Array|Promise} promises array or promise for an array of anything,\n\t\t *      may contain a mix of promises and values.\n\t\t * @param {function(accumulated:*, x:*, index:Number):*} f reduce function\n\t\t * @returns {Promise} that will resolve to the final reduced value\n\t\t */\n\t\tfunction reduce(promises, f /*, initialValue */) {\n\t\t\treturn arguments.length > 2 ? ar.call(promises, liftCombine(f), arguments[2])\n\t\t\t\t\t: ar.call(promises, liftCombine(f));\n\t\t}\n\n\t\t/**\n\t\t * Traditional reduce function, similar to `Array.prototype.reduceRight()`, but\n\t\t * input may contain promises and/or values, and reduceFunc\n\t\t * may return either a value or a promise, *and* initialValue may\n\t\t * be a promise for the starting value.\n\t\t * @param {Array|Promise} promises array or promise for an array of anything,\n\t\t *      may contain a mix of promises and values.\n\t\t * @param {function(accumulated:*, x:*, index:Number):*} f reduce function\n\t\t * @returns {Promise} that will resolve to the final reduced value\n\t\t */\n\t\tfunction reduceRight(promises, f /*, initialValue */) {\n\t\t\treturn arguments.length > 2 ? arr.call(promises, liftCombine(f), arguments[2])\n\t\t\t\t\t: arr.call(promises, liftCombine(f));\n\t\t}\n\n\t\tfunction liftCombine(f) {\n\t\t\treturn function(z, x, i) {\n\t\t\t\treturn applyFold(f, void 0, [z,x,i]);\n\t\t\t};\n\t\t}\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function flow(Promise) {\n\n\t\tvar resolve = Promise.resolve;\n\t\tvar reject = Promise.reject;\n\t\tvar origCatch = Promise.prototype['catch'];\n\n\t\t/**\n\t\t * Handle the ultimate fulfillment value or rejection reason, and assume\n\t\t * responsibility for all errors.  If an error propagates out of result\n\t\t * or handleFatalError, it will be rethrown to the host, resulting in a\n\t\t * loud stack track on most platforms and a crash on some.\n\t\t * @param {function?} onResult\n\t\t * @param {function?} onError\n\t\t * @returns {undefined}\n\t\t */\n\t\tPromise.prototype.done = function(onResult, onError) {\n\t\t\tthis._handler.visit(this._handler.receiver, onResult, onError);\n\t\t};\n\n\t\t/**\n\t\t * Add Error-type and predicate matching to catch.  Examples:\n\t\t * promise.catch(TypeError, handleTypeError)\n\t\t *   .catch(predicate, handleMatchedErrors)\n\t\t *   .catch(handleRemainingErrors)\n\t\t * @param onRejected\n\t\t * @returns {*}\n\t\t */\n\t\tPromise.prototype['catch'] = Promise.prototype.otherwise = function(onRejected) {\n\t\t\tif (arguments.length < 2) {\n\t\t\t\treturn origCatch.call(this, onRejected);\n\t\t\t}\n\n\t\t\tif(typeof onRejected !== 'function') {\n\t\t\t\treturn this.ensure(rejectInvalidPredicate);\n\t\t\t}\n\n\t\t\treturn origCatch.call(this, createCatchFilter(arguments[1], onRejected));\n\t\t};\n\n\t\t/**\n\t\t * Wraps the provided catch handler, so that it will only be called\n\t\t * if the predicate evaluates truthy\n\t\t * @param {?function} handler\n\t\t * @param {function} predicate\n\t\t * @returns {function} conditional catch handler\n\t\t */\n\t\tfunction createCatchFilter(handler, predicate) {\n\t\t\treturn function(e) {\n\t\t\t\treturn evaluatePredicate(e, predicate)\n\t\t\t\t\t? handler.call(this, e)\n\t\t\t\t\t: reject(e);\n\t\t\t};\n\t\t}\n\n\t\t/**\n\t\t * Ensures that onFulfilledOrRejected will be called regardless of whether\n\t\t * this promise is fulfilled or rejected.  onFulfilledOrRejected WILL NOT\n\t\t * receive the promises' value or reason.  Any returned value will be disregarded.\n\t\t * onFulfilledOrRejected may throw or return a rejected promise to signal\n\t\t * an additional error.\n\t\t * @param {function} handler handler to be called regardless of\n\t\t *  fulfillment or rejection\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype['finally'] = Promise.prototype.ensure = function(handler) {\n\t\t\tif(typeof handler !== 'function') {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\treturn this.then(function(x) {\n\t\t\t\treturn runSideEffect(handler, this, identity, x);\n\t\t\t}, function(e) {\n\t\t\t\treturn runSideEffect(handler, this, reject, e);\n\t\t\t});\n\t\t};\n\n\t\tfunction runSideEffect (handler, thisArg, propagate, value) {\n\t\t\tvar result = handler.call(thisArg);\n\t\t\treturn maybeThenable(result)\n\t\t\t\t? propagateValue(result, propagate, value)\n\t\t\t\t: propagate(value);\n\t\t}\n\n\t\tfunction propagateValue (result, propagate, x) {\n\t\t\treturn resolve(result).then(function () {\n\t\t\t\treturn propagate(x);\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Recover from a failure by returning a defaultValue.  If defaultValue\n\t\t * is a promise, it's fulfillment value will be used.  If defaultValue is\n\t\t * a promise that rejects, the returned promise will reject with the\n\t\t * same reason.\n\t\t * @param {*} defaultValue\n\t\t * @returns {Promise} new promise\n\t\t */\n\t\tPromise.prototype['else'] = Promise.prototype.orElse = function(defaultValue) {\n\t\t\treturn this.then(void 0, function() {\n\t\t\t\treturn defaultValue;\n\t\t\t});\n\t\t};\n\n\t\t/**\n\t\t * Shortcut for .then(function() { return value; })\n\t\t * @param  {*} value\n\t\t * @return {Promise} a promise that:\n\t\t *  - is fulfilled if value is not a promise, or\n\t\t *  - if value is a promise, will fulfill with its value, or reject\n\t\t *    with its reason.\n\t\t */\n\t\tPromise.prototype['yield'] = function(value) {\n\t\t\treturn this.then(function() {\n\t\t\t\treturn value;\n\t\t\t});\n\t\t};\n\n\t\t/**\n\t\t * Runs a side effect when this promise fulfills, without changing the\n\t\t * fulfillment value.\n\t\t * @param {function} onFulfilledSideEffect\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.tap = function(onFulfilledSideEffect) {\n\t\t\treturn this.then(onFulfilledSideEffect)['yield'](this);\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n\tfunction rejectInvalidPredicate() {\n\t\tthrow new TypeError('catch predicate must be a function');\n\t}\n\n\tfunction evaluatePredicate(e, predicate) {\n\t\treturn isError(predicate) ? e instanceof predicate : predicate(e);\n\t}\n\n\tfunction isError(predicate) {\n\t\treturn predicate === Error\n\t\t\t|| (predicate != null && predicate.prototype instanceof Error);\n\t}\n\n\tfunction maybeThenable(x) {\n\t\treturn (typeof x === 'object' || typeof x === 'function') && x !== null;\n\t}\n\n\tfunction identity(x) {\n\t\treturn x;\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function fold(Promise) {\n\n\t\tPromise.prototype.fold = function(f, z) {\n\t\t\tvar promise = this._beget();\n\n\t\t\tthis._handler.fold(function(z, x, to) {\n\t\t\t\tPromise._handler(z).fold(function(x, z, to) {\n\t\t\t\t\tto.resolve(f.call(this, z, x));\n\t\t\t\t}, x, this, to);\n\t\t\t}, z, promise._handler.receiver, promise._handler);\n\n\t\t\treturn promise;\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar inspect = require('../state').inspect;\n\n\treturn function inspection(Promise) {\n\n\t\tPromise.prototype.inspect = function() {\n\t\t\treturn inspect(Promise._handler(this));\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function generate(Promise) {\n\n\t\tvar resolve = Promise.resolve;\n\n\t\tPromise.iterate = iterate;\n\t\tPromise.unfold = unfold;\n\n\t\treturn Promise;\n\n\t\t/**\n\t\t * @deprecated Use github.com/cujojs/most streams and most.iterate\n\t\t * Generate a (potentially infinite) stream of promised values:\n\t\t * x, f(x), f(f(x)), etc. until condition(x) returns true\n\t\t * @param {function} f function to generate a new x from the previous x\n\t\t * @param {function} condition function that, given the current x, returns\n\t\t *  truthy when the iterate should stop\n\t\t * @param {function} handler function to handle the value produced by f\n\t\t * @param {*|Promise} x starting value, may be a promise\n\t\t * @return {Promise} the result of the last call to f before\n\t\t *  condition returns true\n\t\t */\n\t\tfunction iterate(f, condition, handler, x) {\n\t\t\treturn unfold(function(x) {\n\t\t\t\treturn [x, f(x)];\n\t\t\t}, condition, handler, x);\n\t\t}\n\n\t\t/**\n\t\t * @deprecated Use github.com/cujojs/most streams and most.unfold\n\t\t * Generate a (potentially infinite) stream of promised values\n\t\t * by applying handler(generator(seed)) iteratively until\n\t\t * condition(seed) returns true.\n\t\t * @param {function} unspool function that generates a [value, newSeed]\n\t\t *  given a seed.\n\t\t * @param {function} condition function that, given the current seed, returns\n\t\t *  truthy when the unfold should stop\n\t\t * @param {function} handler function to handle the value produced by unspool\n\t\t * @param x {*|Promise} starting value, may be a promise\n\t\t * @return {Promise} the result of the last value produced by unspool before\n\t\t *  condition returns true\n\t\t */\n\t\tfunction unfold(unspool, condition, handler, x) {\n\t\t\treturn resolve(x).then(function(seed) {\n\t\t\t\treturn resolve(condition(seed)).then(function(done) {\n\t\t\t\t\treturn done ? seed : resolve(unspool(seed)).spread(next);\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tfunction next(item, newSeed) {\n\t\t\t\treturn resolve(handler(item)).then(function() {\n\t\t\t\t\treturn unfold(unspool, condition, handler, newSeed);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function progress(Promise) {\n\n\t\t/**\n\t\t * @deprecated\n\t\t * Register a progress handler for this promise\n\t\t * @param {function} onProgress\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.progress = function(onProgress) {\n\t\t\treturn this.then(void 0, void 0, onProgress);\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar env = require('../env');\n\tvar TimeoutError = require('../TimeoutError');\n\n\tfunction setTimeout(f, ms, x, y) {\n\t\treturn env.setTimer(function() {\n\t\t\tf(x, y, ms);\n\t\t}, ms);\n\t}\n\n\treturn function timed(Promise) {\n\t\t/**\n\t\t * Return a new promise whose fulfillment value is revealed only\n\t\t * after ms milliseconds\n\t\t * @param {number} ms milliseconds\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.delay = function(ms) {\n\t\t\tvar p = this._beget();\n\t\t\tthis._handler.fold(handleDelay, ms, void 0, p._handler);\n\t\t\treturn p;\n\t\t};\n\n\t\tfunction handleDelay(ms, x, h) {\n\t\t\tsetTimeout(resolveDelay, ms, x, h);\n\t\t}\n\n\t\tfunction resolveDelay(x, h) {\n\t\t\th.resolve(x);\n\t\t}\n\n\t\t/**\n\t\t * Return a new promise that rejects after ms milliseconds unless\n\t\t * this promise fulfills earlier, in which case the returned promise\n\t\t * fulfills with the same value.\n\t\t * @param {number} ms milliseconds\n\t\t * @param {Error|*=} reason optional rejection reason to use, defaults\n\t\t *   to a TimeoutError if not provided\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype.timeout = function(ms, reason) {\n\t\t\tvar p = this._beget();\n\t\t\tvar h = p._handler;\n\n\t\t\tvar t = setTimeout(onTimeout, ms, reason, p._handler);\n\n\t\t\tthis._handler.visit(h,\n\t\t\t\tfunction onFulfill(x) {\n\t\t\t\t\tenv.clearTimer(t);\n\t\t\t\t\tthis.resolve(x); // this = h\n\t\t\t\t},\n\t\t\t\tfunction onReject(x) {\n\t\t\t\t\tenv.clearTimer(t);\n\t\t\t\t\tthis.reject(x); // this = h\n\t\t\t\t},\n\t\t\t\th.notify);\n\n\t\t\treturn p;\n\t\t};\n\n\t\tfunction onTimeout(reason, h, ms) {\n\t\t\tvar e = typeof reason === 'undefined'\n\t\t\t\t? new TimeoutError('timed out after ' + ms + 'ms')\n\t\t\t\t: reason;\n\t\t\th.reject(e);\n\t\t}\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function(require) {\n\n\tvar setTimer = require('../env').setTimer;\n\tvar format = require('../format');\n\n\treturn function unhandledRejection(Promise) {\n\n\t\tvar logError = noop;\n\t\tvar logInfo = noop;\n\t\tvar localConsole;\n\n\t\tif(typeof console !== 'undefined') {\n\t\t\t// Alias console to prevent things like uglify's drop_console option from\n\t\t\t// removing console.log/error. Unhandled rejections fall into the same\n\t\t\t// category as uncaught exceptions, and build tools shouldn't silence them.\n\t\t\tlocalConsole = console;\n\t\t\tlogError = typeof localConsole.error !== 'undefined'\n\t\t\t\t? function (e) { localConsole.error(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\n\t\t\tlogInfo = typeof localConsole.info !== 'undefined'\n\t\t\t\t? function (e) { localConsole.info(e); }\n\t\t\t\t: function (e) { localConsole.log(e); };\n\t\t}\n\n\t\tPromise.onPotentiallyUnhandledRejection = function(rejection) {\n\t\t\tenqueue(report, rejection);\n\t\t};\n\n\t\tPromise.onPotentiallyUnhandledRejectionHandled = function(rejection) {\n\t\t\tenqueue(unreport, rejection);\n\t\t};\n\n\t\tPromise.onFatalRejection = function(rejection) {\n\t\t\tenqueue(throwit, rejection.value);\n\t\t};\n\n\t\tvar tasks = [];\n\t\tvar reported = [];\n\t\tvar running = null;\n\n\t\tfunction report(r) {\n\t\t\tif(!r.handled) {\n\t\t\t\treported.push(r);\n\t\t\t\tlogError('Potentially unhandled rejection [' + r.id + '] ' + format.formatError(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction unreport(r) {\n\t\t\tvar i = reported.indexOf(r);\n\t\t\tif(i >= 0) {\n\t\t\t\treported.splice(i, 1);\n\t\t\t\tlogInfo('Handled previous rejection [' + r.id + '] ' + format.formatObject(r.value));\n\t\t\t}\n\t\t}\n\n\t\tfunction enqueue(f, x) {\n\t\t\ttasks.push(f, x);\n\t\t\tif(running === null) {\n\t\t\t\trunning = setTimer(flush, 0);\n\t\t\t}\n\t\t}\n\n\t\tfunction flush() {\n\t\t\trunning = null;\n\t\t\twhile(tasks.length > 0) {\n\t\t\t\ttasks.shift()(tasks.shift());\n\t\t\t}\n\t\t}\n\n\t\treturn Promise;\n\t};\n\n\tfunction throwit(e) {\n\t\tthrow e;\n\t}\n\n\tfunction noop() {}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function addWith(Promise) {\n\t\t/**\n\t\t * Returns a promise whose handlers will be called with `this` set to\n\t\t * the supplied receiver.  Subsequent promises derived from the\n\t\t * returned promise will also have their handlers called with receiver\n\t\t * as `this`. Calling `with` with undefined or no arguments will return\n\t\t * a promise whose handlers will again be called in the usual Promises/A+\n\t\t * way (no `this`) thus safely undoing any previous `with` in the\n\t\t * promise chain.\n\t\t *\n\t\t * WARNING: Promises returned from `with`/`withThis` are NOT Promises/A+\n\t\t * compliant, specifically violating 2.2.5 (http://promisesaplus.com/#point-41)\n\t\t *\n\t\t * @param {object} receiver `this` value for all handlers attached to\n\t\t *  the returned promise.\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype['with'] = Promise.prototype.withThis = function(receiver) {\n\t\t\tvar p = this._beget();\n\t\t\tvar child = p._handler;\n\t\t\tchild.receiver = receiver;\n\t\t\tthis._handler.chain(child, receiver);\n\t\t\treturn p;\n\t\t};\n\n\t\treturn Promise;\n\t};\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n/*global process,document,setTimeout,clearTimeout,MutationObserver,WebKitMutationObserver*/\n(function(define) { 'use strict';\ndefine(function(require) {\n\t/*jshint maxcomplexity:6*/\n\n\t// Sniff \"best\" async scheduling option\n\t// Prefer process.nextTick or MutationObserver, then check for\n\t// setTimeout, and finally vertx, since its the only env that doesn't\n\t// have setTimeout\n\n\tvar MutationObs;\n\tvar capturedSetTimeout = typeof setTimeout !== 'undefined' && setTimeout;\n\n\t// Default env\n\tvar setTimer = function(f, ms) { return setTimeout(f, ms); };\n\tvar clearTimer = function(t) { return clearTimeout(t); };\n\tvar asap = function (f) { return capturedSetTimeout(f, 0); };\n\n\t// Detect specific env\n\tif (isNode()) { // Node\n\t\tasap = function (f) { return process.nextTick(f); };\n\n\t} else if (MutationObs = hasMutationObserver()) { // Modern browser\n\t\tasap = initMutationObserver(MutationObs);\n\n\t} else if (!capturedSetTimeout) { // vert.x\n\t\tvar vertxRequire = require;\n\t\tvar vertx = vertxRequire('vertx');\n\t\tsetTimer = function (f, ms) { return vertx.setTimer(ms, f); };\n\t\tclearTimer = vertx.cancelTimer;\n\t\tasap = vertx.runOnLoop || vertx.runOnContext;\n\t}\n\n\treturn {\n\t\tsetTimer: setTimer,\n\t\tclearTimer: clearTimer,\n\t\tasap: asap\n\t};\n\n\tfunction isNode () {\n\t\treturn typeof process !== 'undefined' &&\n\t\t\tObject.prototype.toString.call(process) === '[object process]';\n\t}\n\n\tfunction hasMutationObserver () {\n\t    return (typeof MutationObserver !== 'undefined' && MutationObserver) ||\n\t\t\t(typeof WebKitMutationObserver !== 'undefined' && WebKitMutationObserver);\n\t}\n\n\tfunction initMutationObserver(MutationObserver) {\n\t\tvar scheduled;\n\t\tvar node = document.createTextNode('');\n\t\tvar o = new MutationObserver(run);\n\t\to.observe(node, { characterData: true });\n\n\t\tfunction run() {\n\t\t\tvar f = scheduled;\n\t\t\tscheduled = void 0;\n\t\t\tf();\n\t\t}\n\n\t\tvar i = 0;\n\t\treturn function (f) {\n\t\t\tscheduled = f;\n\t\t\tnode.data = (i ^= 1);\n\t\t};\n\t}\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(require); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn {\n\t\tformatError: formatError,\n\t\tformatObject: formatObject,\n\t\ttryStringify: tryStringify\n\t};\n\n\t/**\n\t * Format an error into a string.  If e is an Error and has a stack property,\n\t * it's returned.  Otherwise, e is formatted using formatObject, with a\n\t * warning added about e not being a proper Error.\n\t * @param {*} e\n\t * @returns {String} formatted string, suitable for output to developers\n\t */\n\tfunction formatError(e) {\n\t\tvar s = typeof e === 'object' && e !== null && (e.stack || e.message) ? e.stack || e.message : formatObject(e);\n\t\treturn e instanceof Error ? s : s + ' (WARNING: non-Error used)';\n\t}\n\n\t/**\n\t * Format an object, detecting \"plain\" objects and running them through\n\t * JSON.stringify if possible.\n\t * @param {Object} o\n\t * @returns {string}\n\t */\n\tfunction formatObject(o) {\n\t\tvar s = String(o);\n\t\tif(s === '[object Object]' && typeof JSON !== 'undefined') {\n\t\t\ts = tryStringify(o, s);\n\t\t}\n\t\treturn s;\n\t}\n\n\t/**\n\t * Try to return the result of JSON.stringify(x).  If that fails, return\n\t * defaultValue\n\t * @param {*} x\n\t * @param {*} defaultValue\n\t * @returns {String|*} JSON.stringify(x) or defaultValue\n\t */\n\tfunction tryStringify(x, defaultValue) {\n\t\ttry {\n\t\t\treturn JSON.stringify(x);\n\t\t} catch(e) {\n\t\t\treturn defaultValue;\n\t\t}\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function liftAll(liftOne, combine, dst, src) {\n\t\tif(typeof combine === 'undefined') {\n\t\t\tcombine = defaultCombine;\n\t\t}\n\n\t\treturn Object.keys(src).reduce(function(dst, key) {\n\t\t\tvar f = src[key];\n\t\t\treturn typeof f === 'function' ? combine(dst, liftOne(f), key) : dst;\n\t\t}, typeof dst === 'undefined' ? defaultDst(src) : dst);\n\t};\n\n\tfunction defaultCombine(o, f, k) {\n\t\to[k] = f;\n\t\treturn o;\n\t}\n\n\tfunction defaultDst(src) {\n\t\treturn typeof src === 'function' ? src.bind() : Object.create(src);\n\t}\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn function makePromise(environment) {\n\n\t\tvar tasks = environment.scheduler;\n\t\tvar emitRejection = initEmitRejection();\n\n\t\tvar objectCreate = Object.create ||\n\t\t\tfunction(proto) {\n\t\t\t\tfunction Child() {}\n\t\t\t\tChild.prototype = proto;\n\t\t\t\treturn new Child();\n\t\t\t};\n\n\t\t/**\n\t\t * Create a promise whose fate is determined by resolver\n\t\t * @constructor\n\t\t * @returns {Promise} promise\n\t\t * @name Promise\n\t\t */\n\t\tfunction Promise(resolver, handler) {\n\t\t\tthis._handler = resolver === Handler ? handler : init(resolver);\n\t\t}\n\n\t\t/**\n\t\t * Run the supplied resolver\n\t\t * @param resolver\n\t\t * @returns {Pending}\n\t\t */\n\t\tfunction init(resolver) {\n\t\t\tvar handler = new Pending();\n\n\t\t\ttry {\n\t\t\t\tresolver(promiseResolve, promiseReject, promiseNotify);\n\t\t\t} catch (e) {\n\t\t\t\tpromiseReject(e);\n\t\t\t}\n\n\t\t\treturn handler;\n\n\t\t\t/**\n\t\t\t * Transition from pre-resolution state to post-resolution state, notifying\n\t\t\t * all listeners of the ultimate fulfillment or rejection\n\t\t\t * @param {*} x resolution value\n\t\t\t */\n\t\t\tfunction promiseResolve (x) {\n\t\t\t\thandler.resolve(x);\n\t\t\t}\n\t\t\t/**\n\t\t\t * Reject this promise with reason, which will be used verbatim\n\t\t\t * @param {Error|*} reason rejection reason, strongly suggested\n\t\t\t *   to be an Error type\n\t\t\t */\n\t\t\tfunction promiseReject (reason) {\n\t\t\t\thandler.reject(reason);\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * @deprecated\n\t\t\t * Issue a progress event, notifying all progress listeners\n\t\t\t * @param {*} x progress event payload to pass to all listeners\n\t\t\t */\n\t\t\tfunction promiseNotify (x) {\n\t\t\t\thandler.notify(x);\n\t\t\t}\n\t\t}\n\n\t\t// Creation\n\n\t\tPromise.resolve = resolve;\n\t\tPromise.reject = reject;\n\t\tPromise.never = never;\n\n\t\tPromise._defer = defer;\n\t\tPromise._handler = getHandler;\n\n\t\t/**\n\t\t * Returns a trusted promise. If x is already a trusted promise, it is\n\t\t * returned, otherwise returns a new trusted Promise which follows x.\n\t\t * @param  {*} x\n\t\t * @return {Promise} promise\n\t\t */\n\t\tfunction resolve(x) {\n\t\t\treturn isPromise(x) ? x\n\t\t\t\t: new Promise(Handler, new Async(getHandler(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a reject promise with x as its reason (x is used verbatim)\n\t\t * @param {*} x\n\t\t * @returns {Promise} rejected promise\n\t\t */\n\t\tfunction reject(x) {\n\t\t\treturn new Promise(Handler, new Async(new Rejected(x)));\n\t\t}\n\n\t\t/**\n\t\t * Return a promise that remains pending forever\n\t\t * @returns {Promise} forever-pending promise.\n\t\t */\n\t\tfunction never() {\n\t\t\treturn foreverPendingPromise; // Should be frozen\n\t\t}\n\n\t\t/**\n\t\t * Creates an internal {promise, resolver} pair\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tfunction defer() {\n\t\t\treturn new Promise(Handler, new Pending());\n\t\t}\n\n\t\t// Transformation and flow control\n\n\t\t/**\n\t\t * Transform this promise's fulfillment value, returning a new Promise\n\t\t * for the transformed result.  If the promise cannot be fulfilled, onRejected\n\t\t * is called with the reason.  onProgress *may* be called with updates toward\n\t\t * this promise's fulfillment.\n\t\t * @param {function=} onFulfilled fulfillment handler\n\t\t * @param {function=} onRejected rejection handler\n\t\t * @param {function=} onProgress @deprecated progress handler\n\t\t * @return {Promise} new promise\n\t\t */\n\t\tPromise.prototype.then = function(onFulfilled, onRejected, onProgress) {\n\t\t\tvar parent = this._handler;\n\t\t\tvar state = parent.join().state();\n\n\t\t\tif ((typeof onFulfilled !== 'function' && state > 0) ||\n\t\t\t\t(typeof onRejected !== 'function' && state < 0)) {\n\t\t\t\t// Short circuit: value will not change, simply share handler\n\t\t\t\treturn new this.constructor(Handler, parent);\n\t\t\t}\n\n\t\t\tvar p = this._beget();\n\t\t\tvar child = p._handler;\n\n\t\t\tparent.chain(child, parent.receiver, onFulfilled, onRejected, onProgress);\n\n\t\t\treturn p;\n\t\t};\n\n\t\t/**\n\t\t * If this promise cannot be fulfilled due to an error, call onRejected to\n\t\t * handle the error. Shortcut for .then(undefined, onRejected)\n\t\t * @param {function?} onRejected\n\t\t * @return {Promise}\n\t\t */\n\t\tPromise.prototype['catch'] = function(onRejected) {\n\t\t\treturn this.then(void 0, onRejected);\n\t\t};\n\n\t\t/**\n\t\t * Creates a new, pending promise of the same type as this promise\n\t\t * @private\n\t\t * @returns {Promise}\n\t\t */\n\t\tPromise.prototype._beget = function() {\n\t\t\treturn begetFrom(this._handler, this.constructor);\n\t\t};\n\n\t\tfunction begetFrom(parent, Promise) {\n\t\t\tvar child = new Pending(parent.receiver, parent.join().context);\n\t\t\treturn new Promise(Handler, child);\n\t\t}\n\n\t\t// Array combinators\n\n\t\tPromise.all = all;\n\t\tPromise.race = race;\n\t\tPromise._traverse = traverse;\n\n\t\t/**\n\t\t * Return a promise that will fulfill when all promises in the\n\t\t * input array have fulfilled, or will reject when one of the\n\t\t * promises rejects.\n\t\t * @param {array} promises array of promises\n\t\t * @returns {Promise} promise for array of fulfillment values\n\t\t */\n\t\tfunction all(promises) {\n\t\t\treturn traverseWith(snd, null, promises);\n\t\t}\n\n\t\t/**\n\t\t * Array<Promise<X>> -> Promise<Array<f(X)>>\n\t\t * @private\n\t\t * @param {function} f function to apply to each promise's value\n\t\t * @param {Array} promises array of promises\n\t\t * @returns {Promise} promise for transformed values\n\t\t */\n\t\tfunction traverse(f, promises) {\n\t\t\treturn traverseWith(tryCatch2, f, promises);\n\t\t}\n\n\t\tfunction traverseWith(tryMap, f, promises) {\n\t\t\tvar handler = typeof f === 'function' ? mapAt : settleAt;\n\n\t\t\tvar resolver = new Pending();\n\t\t\tvar pending = promises.length >>> 0;\n\t\t\tvar results = new Array(pending);\n\n\t\t\tfor (var i = 0, x; i < promises.length && !resolver.resolved; ++i) {\n\t\t\t\tx = promises[i];\n\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\t--pending;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\ttraverseAt(promises, handler, i, x, resolver);\n\t\t\t}\n\n\t\t\tif(pending === 0) {\n\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t}\n\n\t\t\treturn new Promise(Handler, resolver);\n\n\t\t\tfunction mapAt(i, x, resolver) {\n\t\t\t\tif(!resolver.resolved) {\n\t\t\t\t\ttraverseAt(promises, settleAt, i, tryMap(f, x, i), resolver);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction settleAt(i, x, resolver) {\n\t\t\t\tresults[i] = x;\n\t\t\t\tif(--pending === 0) {\n\t\t\t\t\tresolver.become(new Fulfilled(results));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfunction traverseAt(promises, handler, i, x, resolver) {\n\t\t\tif (maybeThenable(x)) {\n\t\t\t\tvar h = getHandlerMaybeThenable(x);\n\t\t\t\tvar s = h.state();\n\n\t\t\t\tif (s === 0) {\n\t\t\t\t\th.fold(handler, i, void 0, resolver);\n\t\t\t\t} else if (s > 0) {\n\t\t\t\t\thandler(i, h.value, resolver);\n\t\t\t\t} else {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thandler(i, x, resolver);\n\t\t\t}\n\t\t}\n\n\t\tPromise._visitRemaining = visitRemaining;\n\t\tfunction visitRemaining(promises, start, handler) {\n\t\t\tfor(var i=start; i<promises.length; ++i) {\n\t\t\t\tmarkAsHandled(getHandler(promises[i]), handler);\n\t\t\t}\n\t\t}\n\n\t\tfunction markAsHandled(h, handler) {\n\t\t\tif(h === handler) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar s = h.state();\n\t\t\tif(s === 0) {\n\t\t\t\th.visit(h, void 0, h._unreport);\n\t\t\t} else if(s < 0) {\n\t\t\t\th._unreport();\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fulfill-reject competitive race. Return a promise that will settle\n\t\t * to the same state as the earliest input promise to settle.\n\t\t *\n\t\t * WARNING: The ES6 Promise spec requires that race()ing an empty array\n\t\t * must return a promise that is pending forever.  This implementation\n\t\t * returns a singleton forever-pending promise, the same singleton that is\n\t\t * returned by Promise.never(), thus can be checked with ===\n\t\t *\n\t\t * @param {array} promises array of promises to race\n\t\t * @returns {Promise} if input is non-empty, a promise that will settle\n\t\t * to the same outcome as the earliest input promise to settle. if empty\n\t\t * is empty, returns a promise that will never settle.\n\t\t */\n\t\tfunction race(promises) {\n\t\t\tif(typeof promises !== 'object' || promises === null) {\n\t\t\t\treturn reject(new TypeError('non-iterable passed to race()'));\n\t\t\t}\n\n\t\t\t// Sigh, race([]) is untestable unless we return *something*\n\t\t\t// that is recognizable without calling .then() on it.\n\t\t\treturn promises.length === 0 ? never()\n\t\t\t\t : promises.length === 1 ? resolve(promises[0])\n\t\t\t\t : runRace(promises);\n\t\t}\n\n\t\tfunction runRace(promises) {\n\t\t\tvar resolver = new Pending();\n\t\t\tvar i, x, h;\n\t\t\tfor(i=0; i<promises.length; ++i) {\n\t\t\t\tx = promises[i];\n\t\t\t\tif (x === void 0 && !(i in promises)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\th = getHandler(x);\n\t\t\t\tif(h.state() !== 0) {\n\t\t\t\t\tresolver.become(h);\n\t\t\t\t\tvisitRemaining(promises, i+1, h);\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\th.visit(resolver, resolver.resolve, resolver.reject);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn new Promise(Handler, resolver);\n\t\t}\n\n\t\t// Promise internals\n\t\t// Below this, everything is @private\n\n\t\t/**\n\t\t * Get an appropriate handler for x, without checking for cycles\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandler(x) {\n\t\t\tif(isPromise(x)) {\n\t\t\t\treturn x._handler.join();\n\t\t\t}\n\t\t\treturn maybeThenable(x) ? getHandlerUntrusted(x) : new Fulfilled(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for thenable x.\n\t\t * NOTE: You must only call this if maybeThenable(x) == true\n\t\t * @param {object|function|Promise} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerMaybeThenable(x) {\n\t\t\treturn isPromise(x) ? x._handler.join() : getHandlerUntrusted(x);\n\t\t}\n\n\t\t/**\n\t\t * Get a handler for potentially untrusted thenable x\n\t\t * @param {*} x\n\t\t * @returns {object} handler\n\t\t */\n\t\tfunction getHandlerUntrusted(x) {\n\t\t\ttry {\n\t\t\t\tvar untrustedThen = x.then;\n\t\t\t\treturn typeof untrustedThen === 'function'\n\t\t\t\t\t? new Thenable(untrustedThen, x)\n\t\t\t\t\t: new Fulfilled(x);\n\t\t\t} catch(e) {\n\t\t\t\treturn new Rejected(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Handler for a promise that is pending forever\n\t\t * @constructor\n\t\t */\n\t\tfunction Handler() {}\n\n\t\tHandler.prototype.when\n\t\t\t= Handler.prototype.become\n\t\t\t= Handler.prototype.notify // deprecated\n\t\t\t= Handler.prototype.fail\n\t\t\t= Handler.prototype._unreport\n\t\t\t= Handler.prototype._report\n\t\t\t= noop;\n\n\t\tHandler.prototype._state = 0;\n\n\t\tHandler.prototype.state = function() {\n\t\t\treturn this._state;\n\t\t};\n\n\t\t/**\n\t\t * Recursively collapse handler chain to find the handler\n\t\t * nearest to the fully resolved value.\n\t\t * @returns {object} handler nearest the fully resolved value\n\t\t */\n\t\tHandler.prototype.join = function() {\n\t\t\tvar h = this;\n\t\t\twhile(h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t}\n\t\t\treturn h;\n\t\t};\n\n\t\tHandler.prototype.chain = function(to, receiver, fulfilled, rejected, progress) {\n\t\t\tthis.when({\n\t\t\t\tresolver: to,\n\t\t\t\treceiver: receiver,\n\t\t\t\tfulfilled: fulfilled,\n\t\t\t\trejected: rejected,\n\t\t\t\tprogress: progress\n\t\t\t});\n\t\t};\n\n\t\tHandler.prototype.visit = function(receiver, fulfilled, rejected, progress) {\n\t\t\tthis.chain(failIfRejected, receiver, fulfilled, rejected, progress);\n\t\t};\n\n\t\tHandler.prototype.fold = function(f, z, c, to) {\n\t\t\tthis.when(new Fold(f, z, c, to));\n\t\t};\n\n\t\t/**\n\t\t * Handler that invokes fail() on any handler it becomes\n\t\t * @constructor\n\t\t */\n\t\tfunction FailIfRejected() {}\n\n\t\tinherit(Handler, FailIfRejected);\n\n\t\tFailIfRejected.prototype.become = function(h) {\n\t\t\th.fail();\n\t\t};\n\n\t\tvar failIfRejected = new FailIfRejected();\n\n\t\t/**\n\t\t * Handler that manages a queue of consumers waiting on a pending promise\n\t\t * @constructor\n\t\t */\n\t\tfunction Pending(receiver, inheritedContext) {\n\t\t\tPromise.createContext(this, inheritedContext);\n\n\t\t\tthis.consumers = void 0;\n\t\t\tthis.receiver = receiver;\n\t\t\tthis.handler = void 0;\n\t\t\tthis.resolved = false;\n\t\t}\n\n\t\tinherit(Handler, Pending);\n\n\t\tPending.prototype._state = 0;\n\n\t\tPending.prototype.resolve = function(x) {\n\t\t\tthis.become(getHandler(x));\n\t\t};\n\n\t\tPending.prototype.reject = function(x) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.become(new Rejected(x));\n\t\t};\n\n\t\tPending.prototype.join = function() {\n\t\t\tif (!this.resolved) {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\tvar h = this;\n\n\t\t\twhile (h.handler !== void 0) {\n\t\t\t\th = h.handler;\n\t\t\t\tif (h === this) {\n\t\t\t\t\treturn this.handler = cycle();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn h;\n\t\t};\n\n\t\tPending.prototype.run = function() {\n\t\t\tvar q = this.consumers;\n\t\t\tvar handler = this.handler;\n\t\t\tthis.handler = this.handler.join();\n\t\t\tthis.consumers = void 0;\n\n\t\t\tfor (var i = 0; i < q.length; ++i) {\n\t\t\t\thandler.when(q[i]);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.become = function(handler) {\n\t\t\tif(this.resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.resolved = true;\n\t\t\tthis.handler = handler;\n\t\t\tif(this.consumers !== void 0) {\n\t\t\t\ttasks.enqueue(this);\n\t\t\t}\n\n\t\t\tif(this.context !== void 0) {\n\t\t\t\thandler._report(this.context);\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.when = function(continuation) {\n\t\t\tif(this.resolved) {\n\t\t\t\ttasks.enqueue(new ContinuationTask(continuation, this.handler));\n\t\t\t} else {\n\t\t\t\tif(this.consumers === void 0) {\n\t\t\t\t\tthis.consumers = [continuation];\n\t\t\t\t} else {\n\t\t\t\t\tthis.consumers.push(continuation);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tPending.prototype.notify = function(x) {\n\t\t\tif(!this.resolved) {\n\t\t\t\ttasks.enqueue(new ProgressTask(x, this));\n\t\t\t}\n\t\t};\n\n\t\tPending.prototype.fail = function(context) {\n\t\t\tvar c = typeof context === 'undefined' ? this.context : context;\n\t\t\tthis.resolved && this.handler.join().fail(c);\n\t\t};\n\n\t\tPending.prototype._report = function(context) {\n\t\t\tthis.resolved && this.handler.join()._report(context);\n\t\t};\n\n\t\tPending.prototype._unreport = function() {\n\t\t\tthis.resolved && this.handler.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Wrap another handler and force it into a future stack\n\t\t * @param {object} handler\n\t\t * @constructor\n\t\t */\n\t\tfunction Async(handler) {\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tinherit(Handler, Async);\n\n\t\tAsync.prototype.when = function(continuation) {\n\t\t\ttasks.enqueue(new ContinuationTask(continuation, this));\n\t\t};\n\n\t\tAsync.prototype._report = function(context) {\n\t\t\tthis.join()._report(context);\n\t\t};\n\n\t\tAsync.prototype._unreport = function() {\n\t\t\tthis.join()._unreport();\n\t\t};\n\n\t\t/**\n\t\t * Handler that wraps an untrusted thenable and assimilates it in a future stack\n\t\t * @param {function} then\n\t\t * @param {{then: function}} thenable\n\t\t * @constructor\n\t\t */\n\t\tfunction Thenable(then, thenable) {\n\t\t\tPending.call(this);\n\t\t\ttasks.enqueue(new AssimilateTask(then, thenable, this));\n\t\t}\n\n\t\tinherit(Pending, Thenable);\n\n\t\t/**\n\t\t * Handler for a fulfilled promise\n\t\t * @param {*} x fulfillment value\n\t\t * @constructor\n\t\t */\n\t\tfunction Fulfilled(x) {\n\t\t\tPromise.createContext(this);\n\t\t\tthis.value = x;\n\t\t}\n\n\t\tinherit(Handler, Fulfilled);\n\n\t\tFulfilled.prototype._state = 1;\n\n\t\tFulfilled.prototype.fold = function(f, z, c, to) {\n\t\t\trunContinuation3(f, z, this, c, to);\n\t\t};\n\n\t\tFulfilled.prototype.when = function(cont) {\n\t\t\trunContinuation1(cont.fulfilled, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tvar errorId = 0;\n\n\t\t/**\n\t\t * Handler for a rejected promise\n\t\t * @param {*} x rejection reason\n\t\t * @constructor\n\t\t */\n\t\tfunction Rejected(x) {\n\t\t\tPromise.createContext(this);\n\n\t\t\tthis.id = ++errorId;\n\t\t\tthis.value = x;\n\t\t\tthis.handled = false;\n\t\t\tthis.reported = false;\n\n\t\t\tthis._report();\n\t\t}\n\n\t\tinherit(Handler, Rejected);\n\n\t\tRejected.prototype._state = -1;\n\n\t\tRejected.prototype.fold = function(f, z, c, to) {\n\t\t\tto.become(this);\n\t\t};\n\n\t\tRejected.prototype.when = function(cont) {\n\t\t\tif(typeof cont.rejected === 'function') {\n\t\t\t\tthis._unreport();\n\t\t\t}\n\t\t\trunContinuation1(cont.rejected, this, cont.receiver, cont.resolver);\n\t\t};\n\n\t\tRejected.prototype._report = function(context) {\n\t\t\ttasks.afterQueue(new ReportTask(this, context));\n\t\t};\n\n\t\tRejected.prototype._unreport = function() {\n\t\t\tif(this.handled) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.handled = true;\n\t\t\ttasks.afterQueue(new UnreportTask(this));\n\t\t};\n\n\t\tRejected.prototype.fail = function(context) {\n\t\t\tthis.reported = true;\n\t\t\temitRejection('unhandledRejection', this);\n\t\t\tPromise.onFatalRejection(this, context === void 0 ? this.context : context);\n\t\t};\n\n\t\tfunction ReportTask(rejection, context) {\n\t\t\tthis.rejection = rejection;\n\t\t\tthis.context = context;\n\t\t}\n\n\t\tReportTask.prototype.run = function() {\n\t\t\tif(!this.rejection.handled && !this.rejection.reported) {\n\t\t\t\tthis.rejection.reported = true;\n\t\t\t\temitRejection('unhandledRejection', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejection(this.rejection, this.context);\n\t\t\t}\n\t\t};\n\n\t\tfunction UnreportTask(rejection) {\n\t\t\tthis.rejection = rejection;\n\t\t}\n\n\t\tUnreportTask.prototype.run = function() {\n\t\t\tif(this.rejection.reported) {\n\t\t\t\temitRejection('rejectionHandled', this.rejection) ||\n\t\t\t\t\tPromise.onPotentiallyUnhandledRejectionHandled(this.rejection);\n\t\t\t}\n\t\t};\n\n\t\t// Unhandled rejection hooks\n\t\t// By default, everything is a noop\n\n\t\tPromise.createContext\n\t\t\t= Promise.enterContext\n\t\t\t= Promise.exitContext\n\t\t\t= Promise.onPotentiallyUnhandledRejection\n\t\t\t= Promise.onPotentiallyUnhandledRejectionHandled\n\t\t\t= Promise.onFatalRejection\n\t\t\t= noop;\n\n\t\t// Errors and singletons\n\n\t\tvar foreverPendingHandler = new Handler();\n\t\tvar foreverPendingPromise = new Promise(Handler, foreverPendingHandler);\n\n\t\tfunction cycle() {\n\t\t\treturn new Rejected(new TypeError('Promise cycle'));\n\t\t}\n\n\t\t// Task runners\n\n\t\t/**\n\t\t * Run a single consumer\n\t\t * @constructor\n\t\t */\n\t\tfunction ContinuationTask(continuation, handler) {\n\t\t\tthis.continuation = continuation;\n\t\t\tthis.handler = handler;\n\t\t}\n\n\t\tContinuationTask.prototype.run = function() {\n\t\t\tthis.handler.join().when(this.continuation);\n\t\t};\n\n\t\t/**\n\t\t * Run a queue of progress handlers\n\t\t * @constructor\n\t\t */\n\t\tfunction ProgressTask(value, handler) {\n\t\t\tthis.handler = handler;\n\t\t\tthis.value = value;\n\t\t}\n\n\t\tProgressTask.prototype.run = function() {\n\t\t\tvar q = this.handler.consumers;\n\t\t\tif(q === void 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tfor (var c, i = 0; i < q.length; ++i) {\n\t\t\t\tc = q[i];\n\t\t\t\trunNotify(c.progress, this.value, this.handler, c.receiver, c.resolver);\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Assimilate a thenable, sending it's value to resolver\n\t\t * @param {function} then\n\t\t * @param {object|function} thenable\n\t\t * @param {object} resolver\n\t\t * @constructor\n\t\t */\n\t\tfunction AssimilateTask(then, thenable, resolver) {\n\t\t\tthis._then = then;\n\t\t\tthis.thenable = thenable;\n\t\t\tthis.resolver = resolver;\n\t\t}\n\n\t\tAssimilateTask.prototype.run = function() {\n\t\t\tvar h = this.resolver;\n\t\t\ttryAssimilate(this._then, this.thenable, _resolve, _reject, _notify);\n\n\t\t\tfunction _resolve(x) { h.resolve(x); }\n\t\t\tfunction _reject(x)  { h.reject(x); }\n\t\t\tfunction _notify(x)  { h.notify(x); }\n\t\t};\n\n\t\tfunction tryAssimilate(then, thenable, resolve, reject, notify) {\n\t\t\ttry {\n\t\t\t\tthen.call(thenable, resolve, reject, notify);\n\t\t\t} catch (e) {\n\t\t\t\treject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Fold a handler value with z\n\t\t * @constructor\n\t\t */\n\t\tfunction Fold(f, z, c, to) {\n\t\t\tthis.f = f; this.z = z; this.c = c; this.to = to;\n\t\t\tthis.resolver = failIfRejected;\n\t\t\tthis.receiver = this;\n\t\t}\n\n\t\tFold.prototype.fulfilled = function(x) {\n\t\t\tthis.f.call(this.c, this.z, x, this.to);\n\t\t};\n\n\t\tFold.prototype.rejected = function(x) {\n\t\t\tthis.to.reject(x);\n\t\t};\n\n\t\tFold.prototype.progress = function(x) {\n\t\t\tthis.to.notify(x);\n\t\t};\n\n\t\t// Other helpers\n\n\t\t/**\n\t\t * @param {*} x\n\t\t * @returns {boolean} true iff x is a trusted Promise\n\t\t */\n\t\tfunction isPromise(x) {\n\t\t\treturn x instanceof Promise;\n\t\t}\n\n\t\t/**\n\t\t * Test just enough to rule out primitives, in order to take faster\n\t\t * paths in some code\n\t\t * @param {*} x\n\t\t * @returns {boolean} false iff x is guaranteed *not* to be a thenable\n\t\t */\n\t\tfunction maybeThenable(x) {\n\t\t\treturn (typeof x === 'object' || typeof x === 'function') && x !== null;\n\t\t}\n\n\t\tfunction runContinuation1(f, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject(f, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction runContinuation3(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.become(h);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReject3(f, x, h.value, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t */\n\t\tfunction runNotify(f, x, h, receiver, next) {\n\t\t\tif(typeof f !== 'function') {\n\t\t\t\treturn next.notify(x);\n\t\t\t}\n\n\t\t\tPromise.enterContext(h);\n\t\t\ttryCatchReturn(f, x, receiver, next);\n\t\t\tPromise.exitContext();\n\t\t}\n\n\t\tfunction tryCatch2(f, a, b) {\n\t\t\ttry {\n\t\t\t\treturn f(a, b);\n\t\t\t} catch(e) {\n\t\t\t\treturn reject(e);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Return f.call(thisArg, x), or if it throws return a rejected promise for\n\t\t * the thrown exception\n\t\t */\n\t\tfunction tryCatchReject(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.become(getHandler(f.call(thisArg, x)));\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Same as above, but includes the extra argument parameter.\n\t\t */\n\t\tfunction tryCatchReject3(f, x, y, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tf.call(thisArg, x, y, next);\n\t\t\t} catch(e) {\n\t\t\t\tnext.become(new Rejected(e));\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * @deprecated\n\t\t * Return f.call(thisArg, x), or if it throws, *return* the exception\n\t\t */\n\t\tfunction tryCatchReturn(f, x, thisArg, next) {\n\t\t\ttry {\n\t\t\t\tnext.notify(f.call(thisArg, x));\n\t\t\t} catch(e) {\n\t\t\t\tnext.notify(e);\n\t\t\t}\n\t\t}\n\n\t\tfunction inherit(Parent, Child) {\n\t\t\tChild.prototype = objectCreate(Parent.prototype);\n\t\t\tChild.prototype.constructor = Child;\n\t\t}\n\n\t\tfunction snd(x, y) {\n\t\t\treturn y;\n\t\t}\n\n\t\tfunction noop() {}\n\n\t\tfunction hasCustomEvent() {\n\t\t\tif(typeof CustomEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\tvar ev = new CustomEvent('unhandledRejection');\n\t\t\t\t\treturn ev instanceof CustomEvent;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction hasInternetExplorerCustomEvent() {\n\t\t\tif(typeof document !== 'undefined' && typeof document.createEvent === 'function') {\n\t\t\t\ttry {\n\t\t\t\t\t// Try to create one event to make sure it's supported\n\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\tev.initCustomEvent('eventType', false, true, {});\n\t\t\t\t\treturn true;\n\t\t\t\t} catch (ignoredException) {}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction initEmitRejection() {\n\t\t\t/*global process, self, CustomEvent*/\n\t\t\tif(typeof process !== 'undefined' && process !== null\n\t\t\t\t&& typeof process.emit === 'function') {\n\t\t\t\t// Returning falsy here means to call the default\n\t\t\t\t// onPotentiallyUnhandledRejection API.  This is safe even in\n\t\t\t\t// browserify since process.emit always returns falsy in browserify:\n\t\t\t\t// https://github.com/defunctzombie/node-process/blob/master/browser.js#L40-L46\n\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\treturn type === 'unhandledRejection'\n\t\t\t\t\t\t? process.emit(type, rejection.value, rejection)\n\t\t\t\t\t\t: process.emit(type, rejection);\n\t\t\t\t};\n\t\t\t} else if(typeof self !== 'undefined' && hasCustomEvent()) {\n\t\t\t\treturn (function (self, CustomEvent) {\n\t\t\t\t\treturn function (type, rejection) {\n\t\t\t\t\t\tvar ev = new CustomEvent(type, {\n\t\t\t\t\t\t\tdetail: {\n\t\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tbubbles: false,\n\t\t\t\t\t\t\tcancelable: true\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, CustomEvent));\n\t\t\t} else if(typeof self !== 'undefined' && hasInternetExplorerCustomEvent()) {\n\t\t\t\treturn (function(self, document) {\n\t\t\t\t\treturn function(type, rejection) {\n\t\t\t\t\t\tvar ev = document.createEvent('CustomEvent');\n\t\t\t\t\t\tev.initCustomEvent(type, false, true, {\n\t\t\t\t\t\t\treason: rejection.value,\n\t\t\t\t\t\t\tkey: rejection\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn !self.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}(self, document));\n\t\t\t}\n\n\t\t\treturn noop;\n\t\t}\n\n\t\treturn Promise;\n\t};\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n/** <AUTHOR> */\n/** <AUTHOR> */\n\n(function(define) { 'use strict';\ndefine(function() {\n\n\treturn {\n\t\tpending: toPendingState,\n\t\tfulfilled: toFulfilledState,\n\t\trejected: toRejectedState,\n\t\tinspect: inspect\n\t};\n\n\tfunction toPendingState() {\n\t\treturn { state: 'pending' };\n\t}\n\n\tfunction toRejectedState(e) {\n\t\treturn { state: 'rejected', reason: e };\n\t}\n\n\tfunction toFulfilledState(x) {\n\t\treturn { state: 'fulfilled', value: x };\n\t}\n\n\tfunction inspect(handler) {\n\t\tvar state = handler.state();\n\t\treturn state === 0 ? toPendingState()\n\t\t\t : state > 0   ? toFulfilledState(handler.value)\n\t\t\t               : toRejectedState(handler.value);\n\t}\n\n});\n}(typeof define === 'function' && define.amd ? define : function(factory) { module.exports = factory(); }));\n", "/** @license MIT License (c) copyright 2013 original author or authors */\n\n/**\n * Collection of helpers for interfacing with node-style asynchronous functions\n * using promises.\n *\n * <AUTHOR>\n * @contributor <PERSON><PERSON>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar _liftAll = require('./lib/liftAll');\n\tvar setTimer = require('./lib/env').setTimer;\n\tvar slice = Array.prototype.slice;\n\n\tvar _apply = require('./lib/apply')(when.Promise, dispatch);\n\n\treturn {\n\t\tlift: lift,\n\t\tliftAll: liftAll,\n\t\tapply: apply,\n\t\tcall: call,\n\t\tcreateCallback: createCallback,\n\t\tbindCallback: bindCallback,\n\t\tliftCallback: liftCallback\n\t};\n\n\t/**\n\t * Takes a node-style async function and calls it immediately (with an optional\n\t * array of arguments or promises for arguments). It returns a promise whose\n\t * resolution depends on whether the async functions calls its callback with the\n\t * conventional error argument or not.\n\t *\n\t * With this it becomes possible to leverage existing APIs while still reaping\n\t * the benefits of promises.\n\t *\n\t * @example\n\t *    function onlySmallNumbers(n, callback) {\n\t *\t\tif(n < 10) {\n\t *\t\t\tcallback(null, n + 10);\n\t *\t\t} else {\n\t *\t\t\tcallback(new Error(\"Calculation failed\"));\n\t *\t\t}\n\t *\t}\n\t *\n\t *    var nodefn = require(\"when/node/function\");\n\t *\n\t *    // Logs '15'\n\t *    nodefn.apply(onlySmallNumbers, [5]).then(console.log, console.error);\n\t *\n\t *    // Logs 'Calculation failed'\n\t *    nodefn.apply(onlySmallNumbers, [15]).then(console.log, console.error);\n\t *\n\t * @param {function} f node-style function that will be called\n\t * @param {Array} [args] array of arguments to func\n\t * @returns {Promise} promise for the value func passes to its callback\n\t */\n\tfunction apply(f, args) {\n\t\treturn _apply(f, this, args || []);\n\t}\n\n\tfunction dispatch(f, thisArg, args, h) {\n\t\tvar cb = createCallback(h);\n\t\ttry {\n\t\t\tswitch(args.length) {\n\t\t\t\tcase 2: f.call(thisArg, args[0], args[1], cb); break;\n\t\t\t\tcase 1: f.call(thisArg, args[0], cb); break;\n\t\t\t\tcase 0: f.call(thisArg, cb); break;\n\t\t\t\tdefault:\n\t\t\t\t\targs.push(cb);\n\t\t\t\t\tf.apply(thisArg, args);\n\t\t\t}\n\t\t} catch(e) {\n\t\t\th.reject(e);\n\t\t}\n\t}\n\n\t/**\n\t * Has the same behavior that {@link apply} has, with the difference that the\n\t * arguments to the function are provided individually, while {@link apply} accepts\n\t * a single array.\n\t *\n\t * @example\n\t *    function sumSmallNumbers(x, y, callback) {\n\t *\t\tvar result = x + y;\n\t *\t\tif(result < 10) {\n\t *\t\t\tcallback(null, result);\n\t *\t\t} else {\n\t *\t\t\tcallback(new Error(\"Calculation failed\"));\n\t *\t\t}\n\t *\t}\n\t *\n\t *    // Logs '5'\n\t *    nodefn.call(sumSmallNumbers, 2, 3).then(console.log, console.error);\n\t *\n\t *    // Logs 'Calculation failed'\n\t *    nodefn.call(sumSmallNumbers, 5, 10).then(console.log, console.error);\n\t *\n\t * @param {function} f node-style function that will be called\n\t * @param {...*} [args] arguments that will be forwarded to the function\n\t * @returns {Promise} promise for the value func passes to its callback\n\t */\n\tfunction call(f /*, args... */) {\n\t\treturn _apply(f, this, slice.call(arguments, 1));\n\t}\n\n\t/**\n\t * Takes a node-style function and returns new function that wraps the\n\t * original and, instead of taking a callback, returns a promise. Also, it\n\t * knows how to handle promises given as arguments, waiting for their\n\t * resolution before executing.\n\t *\n\t * Upon execution, the orginal function is executed as well. If it passes\n\t * a truthy value as the first argument to the callback, it will be\n\t * interpreted as an error condition, and the promise will be rejected\n\t * with it. Otherwise, the call is considered a resolution, and the promise\n\t * is resolved with the callback's second argument.\n\t *\n\t * @example\n\t *    var fs = require(\"fs\"), nodefn = require(\"when/node/function\");\n\t *\n\t *    var promiseRead = nodefn.lift(fs.readFile);\n\t *\n\t *    // The promise is resolved with the contents of the file if everything\n\t *    // goes ok\n\t *    promiseRead('exists.txt').then(console.log, console.error);\n\t *\n\t *    // And will be rejected if something doesn't work out\n\t *    // (e.g. the files does not exist)\n\t *    promiseRead('doesnt_exist.txt').then(console.log, console.error);\n\t *\n\t *\n\t * @param {Function} f node-style function to be lifted\n\t * @param {...*} [args] arguments to be prepended for the new function @deprecated\n\t * @returns {Function} a promise-returning function\n\t */\n\tfunction lift(f /*, args... */) {\n\t\tvar args1 = arguments.length > 1 ? slice.call(arguments, 1) : [];\n\t\treturn function() {\n\t\t\t// TODO: Simplify once partialing has been removed\n\t\t\tvar l = args1.length;\n\t\t\tvar al = arguments.length;\n\t\t\tvar args = new Array(al + l);\n\t\t\tvar i;\n\t\t\tfor(i=0; i<l; ++i) {\n\t\t\t\targs[i] = args1[i];\n\t\t\t}\n\t\t\tfor(i=0; i<al; ++i) {\n\t\t\t\targs[i+l] = arguments[i];\n\t\t\t}\n\t\t\treturn _apply(f, this, args);\n\t\t};\n\t}\n\n\t/**\n\t * Lift all the functions/methods on src\n\t * @param {object|function} src source whose functions will be lifted\n\t * @param {function?} combine optional function for customizing the lifting\n\t *  process. It is passed dst, the lifted function, and the property name of\n\t *  the original function on src.\n\t * @param {(object|function)?} dst option destination host onto which to place lifted\n\t *  functions. If not provided, liftAll returns a new object.\n\t * @returns {*} If dst is provided, returns dst with lifted functions as\n\t *  properties.  If dst not provided, returns a new object with lifted functions.\n\t */\n\tfunction liftAll(src, combine, dst) {\n\t\treturn _liftAll(lift, combine, dst, src);\n\t}\n\n\t/**\n\t * Takes an object that responds to the resolver interface, and returns\n\t * a function that will resolve or reject it depending on how it is called.\n\t *\n\t * @example\n\t *\tfunction callbackTakingFunction(callback) {\n\t *\t\tif(somethingWrongHappened) {\n\t *\t\t\tcallback(error);\n\t *\t\t} else {\n\t *\t\t\tcallback(null, interestingValue);\n\t *\t\t}\n\t *\t}\n\t *\n\t *\tvar when = require('when'), nodefn = require('when/node/function');\n\t *\n\t *\tvar deferred = when.defer();\n\t *\tcallbackTakingFunction(nodefn.createCallback(deferred.resolver));\n\t *\n\t *\tdeferred.promise.then(function(interestingValue) {\n\t *\t\t// Use interestingValue\n\t *\t});\n\t *\n\t * @param {Resolver} resolver that will be 'attached' to the callback\n\t * @returns {Function} a node-style callback function\n\t */\n\tfunction createCallback(resolver) {\n\t\treturn function(err, value) {\n\t\t\tif(err) {\n\t\t\t\tresolver.reject(err);\n\t\t\t} else if(arguments.length > 2) {\n\t\t\t\tresolver.resolve(slice.call(arguments, 1));\n\t\t\t} else {\n\t\t\t\tresolver.resolve(value);\n\t\t\t}\n\t\t};\n\t}\n\n\t/**\n\t * Attaches a node-style callback to a promise, ensuring the callback is\n\t * called for either fulfillment or rejection. Returns a promise with the same\n\t * state as the passed-in promise.\n\t *\n\t * @example\n\t *\tvar deferred = when.defer();\n\t *\n\t *\tfunction callback(err, value) {\n\t *\t\t// Handle err or use value\n\t *\t}\n\t *\n\t *\tbindCallback(deferred.promise, callback);\n\t *\n\t *\tdeferred.resolve('interesting value');\n\t *\n\t * @param {Promise} promise The promise to be attached to.\n\t * @param {Function} callback The node-style callback to attach.\n\t * @returns {Promise} A promise with the same state as the passed-in promise.\n\t */\n\tfunction bindCallback(promise, callback) {\n\t\tpromise = when(promise);\n\n\t\tif (callback) {\n\t\t\tpromise.then(success, wrapped);\n\t\t}\n\n\t\treturn promise;\n\n\t\tfunction success(value) {\n\t\t\twrapped(null, value);\n\t\t}\n\n\t\tfunction wrapped(err, value) {\n\t\t\tsetTimer(function () {\n\t\t\t\tcallback(err, value);\n\t\t\t}, 0);\n\t\t}\n\t}\n\n\t/**\n\t * Takes a node-style callback and returns new function that accepts a\n\t * promise, calling the original callback when the promise is either\n\t * fulfilled or rejected with the appropriate arguments.\n\t *\n\t * @example\n\t *\tvar deferred = when.defer();\n\t *\n\t *\tfunction callback(err, value) {\n\t *\t\t// Handle err or use value\n\t *\t}\n\t *\n\t *\tvar wrapped = liftCallback(callback);\n\t *\n\t *\t// `wrapped` can now be passed around at will\n\t *\twrapped(deferred.promise);\n\t *\n\t *\tdeferred.resolve('interesting value');\n\t *\n\t * @param {Function} callback The node-style callback to wrap.\n\t * @returns {Function} The lifted, promise-accepting function.\n\t */\n\tfunction liftCallback(callback) {\n\t\treturn function(promise) {\n\t\t\treturn bindCallback(promise, callback);\n\t\t};\n\t}\n});\n\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * parallel.js\n *\n * Run a set of task functions in parallel.  All tasks will\n * receive the same args\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar all = when.Promise.all;\n\tvar slice = Array.prototype.slice;\n\n\t/**\n\t * Run array of tasks in parallel\n\t * @param tasks {Array|Promise} array or promiseForArray of task functions\n\t * @param [args] {*} arguments to be passed to all tasks\n\t * @return {Promise} promise for array containing the\n\t * result of each task in the array position corresponding\n\t * to position of the task in the tasks array\n\t */\n\treturn function parallel(tasks /*, args... */) {\n\t\treturn all(slice.call(arguments, 1)).then(function(args) {\n\t\t\treturn when.map(tasks, function(task) {\n\t\t\t\treturn task.apply(void 0, args);\n\t\t\t});\n\t\t});\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * pipeline.js\n *\n * Run a set of task functions in sequence, passing the result\n * of the previous as an argument to the next.  Like a shell\n * pipeline, e.g. `cat file.txt | grep 'foo' | sed -e 's/foo/bar/g'\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar all = when.Promise.all;\n\tvar slice = Array.prototype.slice;\n\n\t/**\n\t * Run array of tasks in a pipeline where the next\n\t * tasks receives the result of the previous.  The first task\n\t * will receive the initialArgs as its argument list.\n\t * @param tasks {Array|Promise} array or promise for array of task functions\n\t * @param [initialArgs...] {*} arguments to be passed to the first task\n\t * @return {Promise} promise for return value of the final task\n\t */\n\treturn function pipeline(tasks /* initialArgs... */) {\n\t\t// Self-optimizing function to run first task with multiple\n\t\t// args using apply, but subsequence tasks via direct invocation\n\t\tvar runTask = function(args, task) {\n\t\t\trunTask = function(arg, task) {\n\t\t\t\treturn task(arg);\n\t\t\t};\n\n\t\t\treturn task.apply(null, args);\n\t\t};\n\n\t\treturn all(slice.call(arguments, 1)).then(function(args) {\n\t\t\treturn when.reduce(tasks, function(arg, task) {\n\t\t\t\treturn runTask(arg, task);\n\t\t\t}, args);\n\t\t});\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2012-2013 original author or authors */\n\n/**\n * poll.js\n *\n * Helper that polls until cancelled or for a condition to become true.\n *\n * <AUTHOR>\n */\n\n(function (define) { 'use strict';\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar attempt = when['try'];\n\tvar cancelable = require('./cancelable');\n\n\t/**\n\t * Periodically execute the task function on the msec delay. The result of\n\t * the task may be verified by watching for a condition to become true. The\n\t * returned deferred is cancellable if the polling needs to be cancelled\n\t * externally before reaching a resolved state.\n\t *\n\t * The next vote is scheduled after the results of the current vote are\n\t * verified and rejected.\n\t *\n\t * Polling may be terminated by the verifier returning a truthy value,\n\t * invoking cancel() on the returned promise, or the task function returning\n\t * a rejected promise.\n\t *\n\t * Usage:\n\t *\n\t * var count = 0;\n\t * function doSomething() { return count++ }\n\t *\n\t * // poll until cancelled\n\t * var p = poll(doSomething, 1000);\n\t * ...\n\t * p.cancel();\n\t *\n\t * // poll until condition is met\n\t * poll(doSomething, 1000, function(result) { return result > 10 })\n\t *     .then(function(result) { assert result == 10 });\n\t *\n\t * // delay first vote\n\t * poll(doSomething, 1000, anyFunc, true);\n\t *\n\t * @param task {Function} function that is executed after every timeout\n\t * @param interval {number|Function} timeout in milliseconds\n\t * @param [verifier] {Function} function to evaluate the result of the vote.\n\t *     May return a {Promise} or a {Boolean}. Rejecting the promise or a\n\t *     falsey value will schedule the next vote.\n\t * @param [delayInitialTask] {boolean} if truthy, the first vote is scheduled\n\t *     instead of immediate\n\t *\n\t * @returns {Promise}\n\t */\n\treturn function poll(task, interval, verifier, delayInitialTask) {\n\t\tvar deferred, canceled, reject;\n\n\t\tcanceled = false;\n\t\tdeferred = cancelable(when.defer(), function () { canceled = true; });\n\t\treject = deferred.reject;\n\n\t\tverifier = verifier || function () { return false; };\n\n\t\tif (typeof interval !== 'function') {\n\t\t\tinterval = (function (interval) {\n\t\t\t\treturn function () { return when().delay(interval); };\n\t\t\t})(interval);\n\t\t}\n\n\t\tfunction certify(result) {\n\t\t\tdeferred.resolve(result);\n\t\t}\n\n\t\tfunction schedule(result) {\n\t\t\tattempt(interval).then(vote, reject);\n\t\t\tif (result !== void 0) {\n\t\t\t\tdeferred.notify(result);\n\t\t\t}\n\t\t}\n\n\t\tfunction vote() {\n\t\t\tif (canceled) { return; }\n\t\t\twhen(task(),\n\t\t\t\tfunction (result) {\n\t\t\t\t\twhen(verifier(result),\n\t\t\t\t\t\tfunction (verification) {\n\t\t\t\t\t\t\treturn verification ? certify(result) : schedule(result);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfunction () { schedule(result); }\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\treject\n\t\t\t);\n\t\t}\n\n\t\tif (delayInitialTask) {\n\t\t\tschedule();\n\t\t} else {\n\t\t\t// if task() is blocking, vote will also block\n\t\t\tvote();\n\t\t}\n\n\t\t// make the promise cancelable\n\t\tdeferred.promise = Object.create(deferred.promise);\n\t\tdeferred.promise.cancel = deferred.cancel;\n\n\t\treturn deferred.promise;\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * sequence.js\n *\n * Run a set of task functions in sequence.  All tasks will\n * receive the same args.\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\tvar all = when.Promise.all;\n\tvar slice = Array.prototype.slice;\n\n\t/**\n\t * Run array of tasks in sequence with no overlap\n\t * @param tasks {Array|Promise} array or promiseForArray of task functions\n\t * @param [args] {*} arguments to be passed to all tasks\n\t * @return {Promise} promise for an array containing\n\t * the result of each task in the array position corresponding\n\t * to position of the task in the tasks array\n\t */\n\treturn function sequence(tasks /*, args... */) {\n\t\tvar results = [];\n\n\t\treturn all(slice.call(arguments, 1)).then(function(args) {\n\t\t\treturn when.reduce(tasks, function(results, task) {\n\t\t\t\treturn when(task.apply(void 0, args), addResult);\n\t\t\t}, results);\n\t\t});\n\n\t\tfunction addResult(result) {\n\t\t\tresults.push(result);\n\t\t\treturn results;\n\t\t}\n\t};\n\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2011-2013 original author or authors */\n\n/**\n * timeout.js\n *\n * Helper that returns a promise that rejects after a specified timeout,\n * if not explicitly resolved or rejected before that.\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\n(function(define) {\ndefine(function(require) {\n\n\tvar when = require('./when');\n\n    /**\n\t * @deprecated Use when(trigger).timeout(ms)\n     */\n    return function timeout(msec, trigger) {\n\t\treturn when(trigger).timeout(msec);\n    };\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n\n\n", "/** @license MIT License (c) copyright 2010-2014 original author or authors */\n\n/**\n * Promises/A+ and when() implementation\n * when is part of the cujoJS family of libraries (http://cujojs.com/)\n * <AUTHOR>\n * <AUTHOR>\n */\n(function(define) { 'use strict';\ndefine(function (require) {\n\n\tvar timed = require('./lib/decorators/timed');\n\tvar array = require('./lib/decorators/array');\n\tvar flow = require('./lib/decorators/flow');\n\tvar fold = require('./lib/decorators/fold');\n\tvar inspect = require('./lib/decorators/inspect');\n\tvar generate = require('./lib/decorators/iterate');\n\tvar progress = require('./lib/decorators/progress');\n\tvar withThis = require('./lib/decorators/with');\n\tvar unhandledRejection = require('./lib/decorators/unhandledRejection');\n\tvar TimeoutError = require('./lib/TimeoutError');\n\n\tvar Promise = [array, flow, fold, generate, progress,\n\t\tinspect, withThis, timed, unhandledRejection]\n\t\t.reduce(function(Promise, feature) {\n\t\t\treturn feature(Promise);\n\t\t}, require('./lib/Promise'));\n\n\tvar apply = require('./lib/apply')(Promise);\n\n\t// Public API\n\n\twhen.promise     = promise;              // Create a pending promise\n\twhen.resolve     = Promise.resolve;      // Create a resolved promise\n\twhen.reject      = Promise.reject;       // Create a rejected promise\n\n\twhen.lift        = lift;                 // lift a function to return promises\n\twhen['try']      = attempt;              // call a function and return a promise\n\twhen.attempt     = attempt;              // alias for when.try\n\n\twhen.iterate     = Promise.iterate;      // DEPRECATED (use cujojs/most streams) Generate a stream of promises\n\twhen.unfold      = Promise.unfold;       // DEPRECATED (use cujojs/most streams) Generate a stream of promises\n\n\twhen.join        = join;                 // Join 2 or more promises\n\n\twhen.all         = all;                  // Resolve a list of promises\n\twhen.settle      = settle;               // Settle a list of promises\n\n\twhen.any         = lift(Promise.any);    // One-winner race\n\twhen.some        = lift(Promise.some);   // Multi-winner race\n\twhen.race        = lift(Promise.race);   // First-to-settle race\n\n\twhen.map         = map;                  // Array.map() for promises\n\twhen.filter      = filter;               // Array.filter() for promises\n\twhen.reduce      = lift(Promise.reduce);       // Array.reduce() for promises\n\twhen.reduceRight = lift(Promise.reduceRight);  // Array.reduceRight() for promises\n\n\twhen.isPromiseLike = isPromiseLike;      // Is something promise-like, aka thenable\n\n\twhen.Promise     = Promise;              // Promise constructor\n\twhen.defer       = defer;                // Create a {promise, resolve, reject} tuple\n\n\t// Error types\n\n\twhen.TimeoutError = TimeoutError;\n\n\t/**\n\t * Get a trusted promise for x, or by transforming x with onFulfilled\n\t *\n\t * @param {*} x\n\t * @param {function?} onFulfilled callback to be called when x is\n\t *   successfully fulfilled.  If promiseOrValue is an immediate value, callback\n\t *   will be invoked immediately.\n\t * @param {function?} onRejected callback to be called when x is\n\t *   rejected.\n\t * @param {function?} onProgress callback to be called when progress updates\n\t *   are issued for x. @deprecated\n\t * @returns {Promise} a new promise that will fulfill with the return\n\t *   value of callback or errback or the completion value of promiseOrValue if\n\t *   callback and/or errback is not supplied.\n\t */\n\tfunction when(x, onFulfilled, onRejected, onProgress) {\n\t\tvar p = Promise.resolve(x);\n\t\tif (arguments.length < 2) {\n\t\t\treturn p;\n\t\t}\n\n\t\treturn p.then(onFulfilled, onRejected, onProgress);\n\t}\n\n\t/**\n\t * Creates a new promise whose fate is determined by resolver.\n\t * @param {function} resolver function(resolve, reject, notify)\n\t * @returns {Promise} promise whose fate is determine by resolver\n\t */\n\tfunction promise(resolver) {\n\t\treturn new Promise(resolver);\n\t}\n\n\t/**\n\t * Lift the supplied function, creating a version of f that returns\n\t * promises, and accepts promises as arguments.\n\t * @param {function} f\n\t * @returns {Function} version of f that returns promises\n\t */\n\tfunction lift(f) {\n\t\treturn function() {\n\t\t\tfor(var i=0, l=arguments.length, a=new Array(l); i<l; ++i) {\n\t\t\t\ta[i] = arguments[i];\n\t\t\t}\n\t\t\treturn apply(f, this, a);\n\t\t};\n\t}\n\n\t/**\n\t * Call f in a future turn, with the supplied args, and return a promise\n\t * for the result.\n\t * @param {function} f\n\t * @returns {Promise}\n\t */\n\tfunction attempt(f /*, args... */) {\n\t\t/*jshint validthis:true */\n\t\tfor(var i=0, l=arguments.length-1, a=new Array(l); i<l; ++i) {\n\t\t\ta[i] = arguments[i+1];\n\t\t}\n\t\treturn apply(f, this, a);\n\t}\n\n\t/**\n\t * Creates a {promise, resolver} pair, either or both of which\n\t * may be given out safely to consumers.\n\t * @return {{promise: Promise, resolve: function, reject: function, notify: function}}\n\t */\n\tfunction defer() {\n\t\treturn new Deferred();\n\t}\n\n\tfunction Deferred() {\n\t\tvar p = Promise._defer();\n\n\t\tfunction resolve(x) { p._handler.resolve(x); }\n\t\tfunction reject(x) { p._handler.reject(x); }\n\t\tfunction notify(x) { p._handler.notify(x); }\n\n\t\tthis.promise = p;\n\t\tthis.resolve = resolve;\n\t\tthis.reject = reject;\n\t\tthis.notify = notify;\n\t\tthis.resolver = { resolve: resolve, reject: reject, notify: notify };\n\t}\n\n\t/**\n\t * Determines if x is promise-like, i.e. a thenable object\n\t * NOTE: Will return true for *any thenable object*, and isn't truly\n\t * safe, since it may attempt to access the `then` property of x (i.e.\n\t *  clever/malicious getters may do weird things)\n\t * @param {*} x anything\n\t * @returns {boolean} true if x is promise-like\n\t */\n\tfunction isPromiseLike(x) {\n\t\treturn x && typeof x.then === 'function';\n\t}\n\n\t/**\n\t * Return a promise that will resolve only once all the supplied arguments\n\t * have resolved. The resolution value of the returned promise will be an array\n\t * containing the resolution values of each of the arguments.\n\t * @param {...*} arguments may be a mix of promises and values\n\t * @returns {Promise}\n\t */\n\tfunction join(/* ...promises */) {\n\t\treturn Promise.all(arguments);\n\t}\n\n\t/**\n\t * Return a promise that will fulfill once all input promises have\n\t * fulfilled, or reject when any one input promise rejects.\n\t * @param {array|Promise} promises array (or promise for an array) of promises\n\t * @returns {Promise}\n\t */\n\tfunction all(promises) {\n\t\treturn when(promises, Promise.all);\n\t}\n\n\t/**\n\t * Return a promise that will always fulfill with an array containing\n\t * the outcome states of all input promises.  The returned promise\n\t * will only reject if `promises` itself is a rejected promise.\n\t * @param {array|Promise} promises array (or promise for an array) of promises\n\t * @returns {Promise} promise for array of settled state descriptors\n\t */\n\tfunction settle(promises) {\n\t\treturn when(promises, Promise.settle);\n\t}\n\n\t/**\n\t * Promise-aware array map function, similar to `Array.prototype.map()`,\n\t * but input array may contain promises or values.\n\t * @param {Array|Promise} promises array of anything, may contain promises and values\n\t * @param {function(x:*, index:Number):*} mapFunc map function which may\n\t *  return a promise or value\n\t * @returns {Promise} promise that will fulfill with an array of mapped values\n\t *  or reject if any input promise rejects.\n\t */\n\tfunction map(promises, mapFunc) {\n\t\treturn when(promises, function(promises) {\n\t\t\treturn Promise.map(promises, mapFunc);\n\t\t});\n\t}\n\n\t/**\n\t * Filter the provided array of promises using the provided predicate.  Input may\n\t * contain promises and values\n\t * @param {Array|Promise} promises array of promises and values\n\t * @param {function(x:*, index:Number):boolean} predicate filtering predicate.\n\t *  Must return truthy (or promise for truthy) for items to retain.\n\t * @returns {Promise} promise that will fulfill with an array containing all items\n\t *  for which predicate returned truthy.\n\t */\n\tfunction filter(promises, predicate) {\n\t\treturn when(promises, function(promises) {\n\t\t\treturn Promise.filter(promises, predicate);\n\t\t});\n\t}\n\n\treturn when;\n});\n})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(require); });\n"]}