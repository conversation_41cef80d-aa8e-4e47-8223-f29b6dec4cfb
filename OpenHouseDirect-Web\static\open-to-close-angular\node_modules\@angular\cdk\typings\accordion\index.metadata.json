{"__symbolic": "module", "version": 4, "metadata": {"CdkAccordionItem": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 28, "character": 1}, "arguments": [{"selector": "cdk-accordion-item", "exportAs": "cdkAccordionItem"}]}], "members": {"closed": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 34, "character": 3}}]}], "opened": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 36, "character": 3}}]}], "destroyed": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 38, "character": 3}}]}], "expandedChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 45, "character": 3}}]}], "expanded": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 51, "character": 3}}]}], "disabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 81, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 89, "character": 15}}], null, null], "parameters": [{"__symbolic": "reference", "name": "CdkAccordion"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ChangeDetectorRef", "line": 90, "character": 42}, {"__symbolic": "reference", "module": "@angular/cdk/collections", "name": "UniqueSelectionDispatcher", "line": 91, "character": 46}]}], "ngOnDestroy": [{"__symbolic": "method"}], "toggle": [{"__symbolic": "method"}], "close": [{"__symbolic": "method"}], "open": [{"__symbolic": "method"}]}}, "CdkAccordion": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 17, "character": 1}, "arguments": [{"selector": "cdk-accordion, [cdkAccordion]", "exportAs": "cdkAccordion"}]}], "members": {"multi": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 26, "character": 3}}]}]}}, "CdkAccordionModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 13, "character": 1}, "arguments": [{"exports": [{"__symbolic": "reference", "name": "CdkAccordion"}, {"__symbolic": "reference", "name": "CdkAccordionItem"}], "declarations": [{"__symbolic": "reference", "name": "CdkAccordion"}, {"__symbolic": "reference", "name": "CdkAccordionItem"}], "providers": [{"__symbolic": "reference", "module": "@angular/cdk/collections", "name": "UNIQUE_SELECTION_DISPATCHER_PROVIDER", "line": 16, "character": 14}]}]}], "members": {}}}, "origins": {"CdkAccordionItem": "./accordion-item", "CdkAccordion": "./accordion", "CdkAccordionModule": "./accordion-module"}, "importAs": "@angular/cdk/accordion"}