{"version": 3, "file": "ro-MD.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ro-MD.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC;QAC1E,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC;KAC5E;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['miezul nopții', 'amiaz<PERSON>', 'diminea<PERSON><PERSON>', 'după-amiază', 'seară', 'noapte'],\n    ['miezul nopții', 'amiaz<PERSON>', 'dimineața', 'după-amiaza', 'seara', 'noaptea'],\n  ],\n  ,\n  [\n    '00:00', '12:00', ['05:00', '12:00'], ['12:00', '18:00'], ['18:00', '22:00'],\n    ['22:00', '05:00']\n  ]\n];\n"]}