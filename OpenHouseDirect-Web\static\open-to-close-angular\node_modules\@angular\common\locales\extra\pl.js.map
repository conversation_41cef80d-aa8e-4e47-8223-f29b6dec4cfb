{"version": 3, "file": "pl.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/pl.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC;QAC1E;YACE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW;YAChF,QAAQ;SACT;KACF;IACD;QACE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC;QACjE,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,CAAC;KAChF;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['o półn.', 'w poł.', 'rano', 'przed poł.', 'po poł.', 'wiecz.', 'w nocy'],\n    [\n      'o północy', 'w południe', 'rano', 'przed południem', 'po południu', 'wieczorem',\n      'w nocy'\n    ],\n  ],\n  [\n    ['półn.', 'poł.', 'rano', 'przedpoł.', 'popoł.', 'wiecz.', 'noc'],\n    ['północ', 'południe', 'rano', 'przedpołudnie', 'popołudnie', 'wieczór', 'noc'],\n  ],\n  [\n    '00:00', '12:00', ['06:00', '10:00'], ['10:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '21:00'], ['21:00', '06:00']\n  ]\n];\n"]}