{"_from": "ripemd160@^2.0.1", "_id": "ripemd160@2.0.2", "_inBundle": false, "_integrity": "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==", "_location": "/ripemd160", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ripemd160@^2.0.1", "name": "ripemd160", "escapedName": "ripemd160", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/create-hash", "/create-hmac"], "_resolved": "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz", "_shasum": "a1c1a6f624751577ba5d07914cbc92850585890c", "_spec": "ripemd160@^2.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\create-hash", "bugs": {"url": "https://github.com/crypto-browserify/ripemd160/issues"}, "bundleDependencies": false, "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}, "deprecated": false, "description": "Compute ripemd160 of bytes or strings.", "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^6.0.7", "tape": "^4.5.1"}, "files": ["index.js"], "homepage": "https://github.com/crypto-browserify/ripemd160#readme", "keywords": ["string", "strings", "ripemd160", "ripe160", "bitcoin", "bytes", "cryptography"], "license": "MIT", "main": "./index", "name": "ripemd160", "repository": {"url": "git+https://github.com/crypto-browserify/ripemd160.git", "type": "git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "version": "2.0.2"}