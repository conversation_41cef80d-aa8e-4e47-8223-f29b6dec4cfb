{"version": 3, "file": "ky.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ky.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC;QACvD;YACE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc;YACtD,SAAS,EAAE,YAAY;SACxB;KACF;IACD;QACE;YACE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc;YACtD,UAAU,EAAE,KAAK;SAClB;QACD,AADE;KAEH;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['түн орт', 'чт', 'эртң мн', 'түшт кйн', 'кечк', 'түн'],\n    [\n      'түн ортосу', 'чак түш', 'эртең менен', 'түштөн кийин',\n      'кечинде', 'түн ичинде'\n    ],\n  ],\n  [\n    [\n      'түн ортосу', 'чак түш', 'эртең менен', 'түштөн кийин',\n      'кечкурун', 'түн'\n    ],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '06:00']\n  ]\n];\n"]}