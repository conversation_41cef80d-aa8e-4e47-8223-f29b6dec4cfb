# is-number [![NPM version](https://img.shields.io/npm/v/is-number.svg?style=flat)](https://www.npmjs.com/package/is-number) [![NPM downloads](https://img.shields.io/npm/dm/is-number.svg?style=flat)](https://npmjs.org/package/is-number) [![Build Status](https://img.shields.io/travis/jonschlinkert/is-number.svg?style=flat)](https://travis-ci.org/jonschlinkert/is-number)

> Returns true if the value is a number. comprehensive tests.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save is-number
```

## Usage

To understand some of the rationale behind the decisions made in this library (and to learn about some oddities of number evaluation in JavaScript), [see this gist](https://gist.github.com/jonschlinkert/e30c70c713da325d0e81).

```js
var isNumber = require('is-number');
```

### true

See the [tests](./test.js) for more examples.

```js
isNumber(5e3)      //=> 'true'
isNumber(0xff)     //=> 'true'
isNumber(-1.1)     //=> 'true'
isNumber(0)        //=> 'true'
isNumber(1)        //=> 'true'
isNumber(1.1)      //=> 'true'
isNumber(10)       //=> 'true'
isNumber(10.10)    //=> 'true'
isNumber(100)      //=> 'true'
isNumber('-1.1')   //=> 'true'
isNumber('0')      //=> 'true'
isNumber('012')    //=> 'true'
isNumber('0xff')   //=> 'true'
isNumber('1')      //=> 'true'
isNumber('1.1')    //=> 'true'
isNumber('10')     //=> 'true'
isNumber('10.10')  //=> 'true'
isNumber('100')    //=> 'true'
isNumber('5e3')    //=> 'true'
isNumber(parseInt('012'))   //=> 'true'
isNumber(parseFloat('012')) //=> 'true'
```

### False

See the [tests](./test.js) for more examples.

```js
isNumber('foo')             //=> 'false'
isNumber([1])               //=> 'false'
isNumber([])                //=> 'false'
isNumber(function () {})    //=> 'false'
isNumber(Infinity)          //=> 'false'
isNumber(NaN)               //=> 'false'
isNumber(new Array('abc'))  //=> 'false'
isNumber(new Array(2))      //=> 'false'
isNumber(new Buffer('abc')) //=> 'false'
isNumber(null)              //=> 'false'
isNumber(undefined)         //=> 'false'
isNumber({abc: 'abc'})      //=> 'false'
```

## About

### Related projects

* [even](https://www.npmjs.com/package/even): Get the even numbered items from an array. | [homepage](https://github.com/jonschlinkert/even "Get the even numbered items from an array.")
* [is-even](https://www.npmjs.com/package/is-even): Return true if the given number is even. | [homepage](https://github.com/jonschlinkert/is-even "Return true if the given number is even.")
* [is-odd](https://www.npmjs.com/package/is-odd): Returns true if the given number is odd. | [homepage](https://github.com/jonschlinkert/is-odd "Returns true if the given number is odd.")
* [is-primitive](https://www.npmjs.com/package/is-primitive): Returns `true` if the value is a primitive.  | [homepage](https://github.com/jonschlinkert/is-primitive "Returns `true` if the value is a primitive. ")
* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of "Get the native type of a value.")
* [odd](https://www.npmjs.com/package/odd): Get the odd numbered items from an array. | [homepage](https://github.com/jonschlinkert/odd "Get the odd numbered items from an array.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_

To generate the readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install -g verb verb-generate-readme && verb
```

### Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

### License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/is-number/blob/master/LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.30, on September 10, 2016._