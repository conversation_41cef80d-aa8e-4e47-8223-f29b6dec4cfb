{"version": 3, "sources": ["animations-browser-testing.umd.js"], "names": ["global", "factory", "exports", "module", "require", "define", "amd", "ng", "animations", "browser", "testing", "this", "_angular_animations", "__extends", "d", "b", "__", "constructor", "extendStatics", "prototype", "Object", "create", "containsVendorPrefix", "prop", "substring", "validateStyleProperty", "_CACHED_BODY", "getBodyNode", "_IS_WEBKIT", "style", "result", "char<PERSON>t", "toUpperCase", "substr", "document", "body", "allowPreviousPlayerStylesMerge", "duration", "delay", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "_contains", "elm1", "elm2", "_matches", "element", "selector", "_query", "multi", "Element", "contains", "matches", "proto", "fn_1", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "apply", "results", "push", "querySelectorAll", "elm", "querySelector", "matchesElement", "containsElement", "invoke<PERSON><PERSON>y", "MockAnimationDriver", "query", "computeStyle", "defaultValue", "animate", "keyframes", "easing", "previousPlayers", "player", "MockAnimationPlayer", "log", "_super", "_this", "call", "__finished", "__started", "previousStyles", "_onInitFns", "currentSnapshot", "for<PERSON>ach", "styles_1", "keys", "totalTime", "onInit", "fn", "init", "finish", "destroy", "triggerMicrotask", "play", "hasStarted", "<PERSON><PERSON><PERSON><PERSON>", "captures", "kf", "AUTO_STYLE", "NoopAnimationPlayer", "defineProperty", "value"], "mappings": ";;;;;CAKC,SAAUA,OAAQC,SACC,gBAAZC,UAA0C,mBAAXC,QAAyBF,QAAQC,QAASE,QAAQ,wBACtE,kBAAXC,SAAyBA,OAAOC,IAAMD,OAAO,uCAAwC,UAAW,uBAAwBJ,SAC9HA,SAASD,OAAOO,GAAKP,OAAOO,OAAUP,OAAOO,GAAGC,WAAaR,OAAOO,GAAGC,eAAkBR,OAAOO,GAAGC,WAAWC,QAAUT,OAAOO,GAAGC,WAAWC,YAAeT,OAAOO,GAAGC,WAAWC,QAAQC,YAAcV,OAAOO,GAAGC,aACjNG,KAAM,SAAWT,QAAQU,qBAAuB,YAsBlD,SAASC,WAAUC,EAAGC,GAElB,QAASC,MAAOL,KAAKM,YAAcH,EADnCI,cAAcJ,EAAGC,GAEjBD,EAAEK,UAAkB,OAANJ,EAAaK,OAAOC,OAAON,IAAMC,GAAGG,UAAYJ,EAAEI,UAAW,GAAIH,KAuGnF,QAASM,sBAAqBC,MAG1B,MAA+B,SAAxBA,KAAKC,UAAU,EAAG,GAQ7B,QAASC,uBAAsBF,MACtBG,eACDA,aAAeC,kBACfC,aAA8B,aAAiBC,OAAS,oBAAuC,cAAiBA,MAEpH,IAAqBC,SAAS,CAC9B,IAAqB,aAAiBD,QAAUP,qBAAqBC,SACjEO,OAASP,OAAyB,cAAiBM,QACpCD,WAAY,CAEvBE,OADiC,SAAWP,KAAKQ,OAAO,GAAGC,cAAgBT,KAAKU,OAAO,IAChD,cAAiBJ,MAGhE,MAAOC,QAKX,QAASH,eACL,MAAuB,mBAAZO,UACAA,SAASC,KAEb,KA4GX,QAASC,gCAA+BC,SAAUC,OAC9C,MAAoB,KAAbD,UAA4B,IAAVC,MA5P7B,GAAIpB,eAAgBE,OAAOmB,iBACpBC,uBAA2BC,QAAS,SAAU3B,EAAGC,GAAKD,EAAE0B,UAAYzB,IACvE,SAAUD,EAAGC,GAAK,IAAK,GAAI2B,KAAK3B,GAAOA,EAAE4B,eAAeD,KAAI5B,EAAE4B,GAAK3B,EAAE2B,KAqErEE,UAAY,SAAUC,KAAMC,MAAQ,OAAO,GAC3CC,SAAW,SAAUC,QAASC,UAC9B,OAAO,GAEPC,OAAS,SAAUF,QAASC,SAAUE,OACtC,SAEJ,IAAsB,mBAAXC,SAAwB,CAG/B,GADAR,UAAY,SAAUC,KAAMC,MAAQ,MAAyBD,MAAKQ,SAASP,OACvEM,QAAQjC,UAAUmC,QAClBP,SAAW,SAAUC,QAASC,UAAY,MAAOD,SAAQM,QAAQL,eAEhE,CACD,GAAqBM,OAA0BH,QAAiB,UAC3CI,KAAOD,MAAME,iBAAmBF,MAAMG,oBAAsBH,MAAMI,mBACnFJ,MAAMK,kBAAoBL,MAAMM,qBAChCL,QACAT,SAAW,SAAUC,QAASC,UAAY,MAAOO,MAAKM,MAAMd,SAAUC,aAG9EC,OAAS,SAAUF,QAASC,SAAUE,OAClC,GAAqBY,WACrB,IAAIZ,MACAY,QAAQC,KAAKF,MAAMC,QAASf,QAAQiB,iBAAiBhB,eAEpD,CACD,GAAqBiB,KAAMlB,QAAQmB,cAAclB,SAC7CiB,MACAH,QAAQC,KAAKE,KAGrB,MAAOH,UAYf,GAAIrC,cAAe,KACfE,YAAa,EA6BbwC,eAAiBrB,SACjBsB,gBAAkBzB,UAClB0B,YAAcpB,OAyHdqB,oBAAqC,WACrC,QAASA,wBA0FT,MApFAA,qBAAoBpD,UAAUM,sBAI9B,SAAUF,MAAQ,MAAOE,uBAAsBF,OAM/CgD,oBAAoBpD,UAAUiD,eAK9B,SAAUpB,QAASC,UACf,MAAOmB,gBAAepB,QAASC,WAOnCsB,oBAAoBpD,UAAUkD,gBAK9B,SAAUxB,KAAMC,MAAQ,MAAOuB,iBAAgBxB,KAAMC,OAOrDyB,oBAAoBpD,UAAUqD,MAM9B,SAAUxB,QAASC,SAAUE,OACzB,MAAOmB,aAAYtB,QAASC,SAAUE,QAQ1CoB,oBAAoBpD,UAAUsD,aAM9B,SAAUzB,QAASzB,KAAMmD,cACrB,MAAOA,eAAgB,IAW3BH,oBAAoBpD,UAAUwD,QAS9B,SAAU3B,QAAS4B,UAAWvC,SAAUC,MAAOuC,OAAQC,qBAC3B,KAApBA,kBAA8BA,mBAClC,IAAqBC,QAAS,GAAIC,qBAAoBhC,QAAS4B,UAAWvC,SAAUC,MAAOuC,OAAQC,gBAEnG,OADAP,qBAAoBU,IAAIjB,KAAsB,QACvCe,QAEXR,oBAAoBU,OACbV,uBAKPS,oBAAqC,SAAUE,QAE/C,QAASF,qBAAoBhC,QAAS4B,UAAWvC,SAAUC,MAAOuC,OAAQC,iBACtE,GAAIK,OAAQD,OAAOE,KAAKzE,OAASA,IAqBjC,OApBAwE,OAAMnC,QAAUA,QAChBmC,MAAMP,UAAYA,UAClBO,MAAM9C,SAAWA,SACjB8C,MAAM7C,MAAQA,MACd6C,MAAMN,OAASA,OACfM,MAAML,gBAAkBA,gBACxBK,MAAME,YAAa,EACnBF,MAAMG,WAAY,EAClBH,MAAMI,kBACNJ,MAAMK,cACNL,MAAMM,mBACFrD,+BAA+BC,SAAUC,QACzCwC,gBAAgBY,QAAQ,SAAUX,QAC9B,GAAIA,iBAAkBC,qBAAqB,CACvC,GAAqBW,UAAWZ,OAAOU,eACvCrE,QAAOwE,KAAKD,UAAUD,QAAQ,SAAUnE,MAAQ,MAAO4D,OAAMI,eAAehE,MAAQoE,SAASpE,WAIzG4D,MAAMU,UAAYvD,MAAQD,SACnB8C,MA+FX,MAtHAtE,WAAUmE,oBAAqBE,QA8B/BF,oBAAoB7D,UAAU2E,OAI9B,SAAUC,IAAMpF,KAAK6E,WAAWxB,KAAK+B,KAKrCf,oBAAoB7D,UAAU6E,KAG9B,WACId,OAAO/D,UAAU6E,KAAKZ,KAAKzE,MAC3BA,KAAK6E,WAAWE,QAAQ,SAAUK,IAAM,MAAOA,QAC/CpF,KAAK6E,eAKTR,oBAAoB7D,UAAU8E,OAG9B,WACIf,OAAO/D,UAAU8E,OAAOb,KAAKzE,MAC7BA,KAAK0E,YAAa,GAKtBL,oBAAoB7D,UAAU+E,QAG9B,WACIhB,OAAO/D,UAAU+E,QAAQd,KAAKzE,MAC9BA,KAAK0E,YAAa,GAMtBL,oBAAoB7D,UAAUgF,iBAG9B,aAIAnB,oBAAoB7D,UAAUiF,KAG9B,WACIlB,OAAO/D,UAAUiF,KAAKhB,KAAKzE,MAC3BA,KAAK2E,WAAY,GAKrBN,oBAAoB7D,UAAUkF,WAG9B,WAAc,MAAO1F,MAAK2E,WAI1BN,oBAAoB7D,UAAUmF,cAG9B,WACI,GAAInB,OAAQxE,KACS4F,WACrBnF,QAAOwE,KAAKjF,KAAK4E,gBAAgBG,QAAQ,SAAUnE,MAC/CgF,SAAShF,MAAQ4D,MAAMI,eAAehE,QAEtCZ,KAAK0F,cAIL1F,KAAKiE,UAAUc,QAAQ,SAAUc,IAC7BpF,OAAOwE,KAAKY,IAAId,QAAQ,SAAUnE,MAClB,UAARA,OACAgF,SAAShF,MAAQ4D,MAAME,WAAamB,GAAGjF,MAAQX,oBAAoB6F,gBAKnF9F,KAAK8E,gBAAkBc,UAEpBvB,qBACTpE,oBAAoB8F,oBAEtBxG,SAAQqE,oBAAsBA,oBAC9BrE,QAAQ8E,oBAAsBA,oBAE9B5D,OAAOuF,eAAezG,QAAS,cAAgB0G,OAAO", "file": "animations-browser-testing.umd.min.js", "sourcesContent": ["/**\n * @license Angular v5.2.5\n * (c) 2010-2018 Google, Inc. https://angular.io/\n * License: MIT\n */\n(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('@angular/animations')) :\n\ttypeof define === 'function' && define.amd ? define('@angular/animations/browser/testing', ['exports', '@angular/animations'], factory) :\n\t(factory((global.ng = global.ng || {}, global.ng.animations = global.ng.animations || {}, global.ng.animations.browser = global.ng.animations.browser || {}, global.ng.animations.browser.testing = {}),global.ng.animations));\n}(this, (function (exports,_angular_animations) { 'use strict';\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\n\n/**\n * @license Angular v5.2.5\n * (c) 2010-2018 Google, Inc. https://angular.io/\n * License: MIT\n */\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @param {?} players\n * @return {?}\n */\n\n/**\n * @param {?} driver\n * @param {?} normalizer\n * @param {?} element\n * @param {?} keyframes\n * @param {?=} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\n\n/**\n * @param {?} player\n * @param {?} eventName\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\n\n/**\n * @param {?} e\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\n\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\n\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} defaultValue\n * @return {?}\n */\n\n/**\n * @param {?} command\n * @return {?}\n */\n\nvar _contains = function (elm1, elm2) { return false; };\nvar _matches = function (element, selector) {\n    return false;\n};\nvar _query = function (element, selector, multi) {\n    return [];\n};\nif (typeof Element != 'undefined') {\n    // this is well supported in all browsers\n    _contains = function (elm1, elm2) { return /** @type {?} */ (elm1.contains(elm2)); };\n    if (Element.prototype.matches) {\n        _matches = function (element, selector) { return element.matches(selector); };\n    }\n    else {\n        var /** @type {?} */ proto = /** @type {?} */ (Element.prototype);\n        var /** @type {?} */ fn_1 = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n            proto.oMatchesSelector || proto.webkitMatchesSelector;\n        if (fn_1) {\n            _matches = function (element, selector) { return fn_1.apply(element, [selector]); };\n        }\n    }\n    _query = function (element, selector, multi) {\n        var /** @type {?} */ results = [];\n        if (multi) {\n            results.push.apply(results, element.querySelectorAll(selector));\n        }\n        else {\n            var /** @type {?} */ elm = element.querySelector(selector);\n            if (elm) {\n                results.push(elm);\n            }\n        }\n        return results;\n    };\n}\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction containsVendorPrefix(prop) {\n    // Webkit is the only real popular vendor prefix nowadays\n    // cc: http://shouldiprefix.com/\n    return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nvar _CACHED_BODY = null;\nvar _IS_WEBKIT = false;\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction validateStyleProperty(prop) {\n    if (!_CACHED_BODY) {\n        _CACHED_BODY = getBodyNode() || {};\n        _IS_WEBKIT = /** @type {?} */ ((_CACHED_BODY)).style ? ('WebkitAppearance' in /** @type {?} */ ((_CACHED_BODY)).style) : false;\n    }\n    var /** @type {?} */ result = true;\n    if (/** @type {?} */ ((_CACHED_BODY)).style && !containsVendorPrefix(prop)) {\n        result = prop in /** @type {?} */ ((_CACHED_BODY)).style;\n        if (!result && _IS_WEBKIT) {\n            var /** @type {?} */ camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n            result = camelProp in /** @type {?} */ ((_CACHED_BODY)).style;\n        }\n    }\n    return result;\n}\n/**\n * @return {?}\n */\nfunction getBodyNode() {\n    if (typeof document != 'undefined') {\n        return document.body;\n    }\n    return null;\n}\nvar matchesElement = _matches;\nvar containsElement = _contains;\nvar invokeQuery = _query;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * @param {?} value\n * @return {?}\n */\n\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\n\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\n\n/**\n * @param {?} styles\n * @return {?}\n */\n\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\n\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\n\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\n\n/**\n * @param {?} steps\n * @return {?}\n */\n\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\n\n/**\n * @param {?} value\n * @return {?}\n */\n\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\n\n/**\n * @param {?} iterator\n * @return {?}\n */\n\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\n\n/**\n * @param {?} input\n * @return {?}\n */\n\n/**\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nfunction allowPreviousPlayerStylesMerge(duration, delay) {\n    return duration === 0 || delay === 0;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * \\@experimental Animation support is experimental.\n */\nvar MockAnimationDriver = /** @class */ (function () {\n    function MockAnimationDriver() {\n    }\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.validateStyleProperty = /**\n     * @param {?} prop\n     * @return {?}\n     */\n    function (prop) { return validateStyleProperty(prop); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.matchesElement = /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    function (element, selector) {\n        return matchesElement(element, selector);\n    };\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.containsElement = /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    function (elm1, elm2) { return containsElement(elm1, elm2); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.query = /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    function (element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    };\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.computeStyle = /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    function (element, prop, defaultValue) {\n        return defaultValue || '';\n    };\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    MockAnimationDriver.prototype.animate = /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    function (element, keyframes, duration, delay, easing, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        var /** @type {?} */ player = new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n        MockAnimationDriver.log.push(/** @type {?} */ (player));\n        return player;\n    };\n    MockAnimationDriver.log = [];\n    return MockAnimationDriver;\n}());\n/**\n * \\@experimental Animation support is experimental.\n */\nvar MockAnimationPlayer = /** @class */ (function (_super) {\n    __extends(MockAnimationPlayer, _super);\n    function MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers) {\n        var _this = _super.call(this) || this;\n        _this.element = element;\n        _this.keyframes = keyframes;\n        _this.duration = duration;\n        _this.delay = delay;\n        _this.easing = easing;\n        _this.previousPlayers = previousPlayers;\n        _this.__finished = false;\n        _this.__started = false;\n        _this.previousStyles = {};\n        _this._onInitFns = [];\n        _this.currentSnapshot = {};\n        if (allowPreviousPlayerStylesMerge(duration, delay)) {\n            previousPlayers.forEach(function (player) {\n                if (player instanceof MockAnimationPlayer) {\n                    var /** @type {?} */ styles_1 = player.currentSnapshot;\n                    Object.keys(styles_1).forEach(function (prop) { return _this.previousStyles[prop] = styles_1[prop]; });\n                }\n            });\n        }\n        _this.totalTime = delay + duration;\n        return _this;\n    }\n    /* @internal */\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.onInit = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onInitFns.push(fn); };\n    /* @internal */\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.init.call(this);\n        this._onInitFns.forEach(function (fn) { return fn(); });\n        this._onInitFns = [];\n    };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.finish.call(this);\n        this.__finished = true;\n    };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.destroy.call(this);\n        this.__finished = true;\n    };\n    /* @internal */\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.triggerMicrotask = /**\n     * @return {?}\n     */\n    function () { };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () {\n        _super.prototype.play.call(this);\n        this.__started = true;\n    };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this.__started; };\n    /**\n     * @return {?}\n     */\n    MockAnimationPlayer.prototype.beforeDestroy = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        var /** @type {?} */ captures = {};\n        Object.keys(this.previousStyles).forEach(function (prop) {\n            captures[prop] = _this.previousStyles[prop];\n        });\n        if (this.hasStarted()) {\n            // when assembling the captured styles, it's important that\n            // we build the keyframe styles in the following order:\n            // {other styles within keyframes, ... previousStyles }\n            this.keyframes.forEach(function (kf) {\n                Object.keys(kf).forEach(function (prop) {\n                    if (prop != 'offset') {\n                        captures[prop] = _this.__finished ? kf[prop] : _angular_animations.AUTO_STYLE;\n                    }\n                });\n            });\n        }\n        this.currentSnapshot = captures;\n    };\n    return MockAnimationPlayer;\n}(_angular_animations.NoopAnimationPlayer));\n\nexports.MockAnimationDriver = MockAnimationDriver;\nexports.MockAnimationPlayer = MockAnimationPlayer;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n})));\n//# sourceMappingURL=animations-browser-testing.umd.js.map\n"]}