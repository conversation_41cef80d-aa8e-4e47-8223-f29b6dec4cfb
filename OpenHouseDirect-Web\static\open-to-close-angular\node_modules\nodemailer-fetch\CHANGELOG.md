# Changelog

## v1.6.0 2016-08-18

  * Added new option `headers`

## v1.5.0 2016-08-18

  * Allow streams as POST body

## v1.3.0 2016-02-11

  * Added new option `timeout`

## v1.2.1 2016-01-18

  * Enclose http.request into try..catch to get url parse errors

## v1.2.0 2016-01-18

  * Export `Cookies` constructor

## v1.1.0 2016-01-18

  * Exposed `options` object
  * Added new options `maxRedirects`, `userAgent` and `cookie`

## v1.0.0 2015-12-30

  * Initial version
