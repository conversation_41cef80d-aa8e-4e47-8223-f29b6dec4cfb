{"_from": "on-headers@~1.1.0", "_id": "on-headers@1.1.0", "_inBundle": false, "_integrity": "sha512-737ZY3yNnXy37FHkQxPzt4UZ2UWPWiCZWLvFZ4fu5cueciegX0zGPnrlY6bwRg4FdQOe9YU8MkmJwGhoMybl8A==", "_location": "/on-headers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "on-headers@~1.1.0", "name": "on-headers", "escapedName": "on-headers", "rawSpec": "~1.1.0", "saveSpec": null, "fetchSpec": "~1.1.0"}, "_requiredBy": ["/compression"], "_resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.1.0.tgz", "_shasum": "59da4f91c45f5f989c6e4bcedc5a3b0aed70ff65", "_spec": "on-headers@~1.1.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\compression", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Execute a listener when a response is about to write headers", "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.21.2", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "10.2.0", "nyc": "15.1.0", "supertest": "4.0.2"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/on-headers#readme", "keywords": ["event", "headers", "http", "onheaders"], "license": "MIT", "name": "on-headers", "repository": {"type": "git", "url": "git+https://github.com/jshttp/on-headers.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-upstream-hashes": "node scripts/update-upstream-hashes.js", "upstream": "mocha --reporter spec --check-leaks test/upstream.js", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.1.0"}