{"_from": "resolve-url@^0.2.1", "_id": "resolve-url@0.2.1", "_inBundle": false, "_integrity": "sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg==", "_location": "/resolve-url", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resolve-url@^0.2.1", "name": "resolve-url", "escapedName": "resolve-url", "rawSpec": "^0.2.1", "saveSpec": null, "fetchSpec": "^0.2.1"}, "_requiredBy": ["/source-map-resolve"], "_resolved": "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz", "_shasum": "2c637fe77c893afd2a663fe21aa9080068e2052a", "_spec": "resolve-url@^0.2.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\source-map-resolve", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/lydell/resolve-url/issues"}, "bundleDependencies": false, "deprecated": "https://github.com/lydell/resolve-url#deprecated", "description": "Like Node.js’ `path.resolve`/`url.resolve` for the browser.", "devDependencies": {"jshint": "~2.4.3", "tape": "~2.5.0", "testling": "~1.6.0"}, "homepage": "https://github.com/lydell/resolve-url#readme", "keywords": ["resolve", "url"], "license": "MIT", "main": "resolve-url.js", "name": "resolve-url", "repository": {"type": "git", "url": "git+https://github.com/lydell/resolve-url.git"}, "scripts": {"test": "jshint resolve-url.js test/ && testling -u"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "chrome/latest", "firefox/latest", "opera/12", "opera/latest", "safari/5", "iphone/6", "android-browser/4"]}, "version": "0.2.1"}