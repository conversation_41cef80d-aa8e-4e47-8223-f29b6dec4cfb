{"_from": "object-component@0.0.3", "_id": "object-component@0.0.3", "_inBundle": false, "_integrity": "sha512-S0sN3agnVh2SZNEIGc0N1X4Z5K0JeFbGBrnuZpsxuUh5XLF0BnvWkMjRXo/zGKLd/eghvNIKcx1pQkmUjXIyrA==", "_location": "/object-component", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "object-component@0.0.3", "name": "object-component", "escapedName": "object-component", "rawSpec": "0.0.3", "saveSpec": null, "fetchSpec": "0.0.3"}, "_requiredBy": ["/socket.io-client"], "_resolved": "https://registry.npmjs.org/object-component/-/object-component-0.0.3.tgz", "_shasum": "f0c69aa50efc95b866c186f400a33769cb2f1291", "_spec": "object-component@0.0.3", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\socket.io-client", "bundleDependencies": false, "component": {"scripts": {"object/index.js": "index.js"}}, "deprecated": false, "description": "Object utils.", "devDependencies": {"mocha": "*", "should": "*"}, "name": "object-component", "version": "0.0.3"}