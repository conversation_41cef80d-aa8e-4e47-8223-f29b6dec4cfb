{"_from": "to-buffer@^1.2.0", "_id": "to-buffer@1.2.1", "_inBundle": false, "_integrity": "sha512-tB82LpAIWjhLYbqjx3X4zEeHN6M8CiuOEy2JY8SEQVdYRe3CCHOFaqrBW1doLDrfpWhplcW7BL+bO3/6S3pcDQ==", "_location": "/to-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "to-buffer@^1.2.0", "name": "to-buffer", "escapedName": "to-buffer", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/pbkdf2", "/sha.js"], "_resolved": "https://registry.npmjs.org/to-buffer/-/to-buffer-1.2.1.tgz", "_shasum": "2ce650cdb262e9112a18e65dc29dcb513c8155e0", "_spec": "to-buffer@^1.2.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\sha.js", "author": {"name": "<PERSON>", "url": "@mafintosh"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/browserify/to-buffer/issues"}, "bundleDependencies": false, "dependencies": {"isarray": "^2.0.5", "safe-buffer": "^5.2.1", "typed-array-buffer": "^1.0.3"}, "deprecated": false, "description": "Pass in a string, array, Buffer, Data View, or Uint8Array, and get a Buffer back.", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.5", "npmignore": "^0.3.1", "nyc": "^10.3.2", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "homepage": "https://github.com/browserify/to-buffer", "license": "MIT", "main": "index.js", "name": "to-buffer", "publishConfig": {"ignore": [".github/workflows", "test"]}, "repository": {"type": "git", "url": "git+https://github.com/browserify/to-buffer.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "npx npm@\">= 10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.2.1"}