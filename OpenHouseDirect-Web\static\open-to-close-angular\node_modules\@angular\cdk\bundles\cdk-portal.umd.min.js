/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@angular/core")):"function"==typeof define&&define.amd?define(["exports","@angular/core"],e):e((t.ng=t.ng||{},t.ng.cdk=t.ng.cdk||{},t.ng.cdk.portal=t.ng.cdk.portal||{}),t.ng.core)}(this,function(t,e){"use strict";function o(t,e){function o(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}function n(){throw Error("Must provide a portal to attach")}function r(){throw Error("Host already has a portal attached")}function a(){throw Error("This PortalOutlet has already been disposed")}function i(){throw Error("Attempting to attach an unknown Portal type. BasePortalOutlet accepts either a ComponentPortal or a TemplatePortal.")}function c(){throw Error("Attempting to attach a portal to a null PortalOutlet")}function s(){throw Error("Attempting to detach a portal that is not attached to a host")}var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])},l=function(){function t(){}return t.prototype.attach=function(t){return null==t&&c(),t.hasAttached()&&r(),this._attachedHost=t,t.attach(this)},t.prototype.detach=function(){var t=this._attachedHost;null==t?s():(this._attachedHost=null,t.detach())},Object.defineProperty(t.prototype,"isAttached",{get:function(){return null!=this._attachedHost},enumerable:!0,configurable:!0}),t.prototype.setAttachedHost=function(t){this._attachedHost=t},t}(),h=function(t){function e(e,o,n){var r=t.call(this)||this;return r.component=e,r.viewContainerRef=o,r.injector=n,r}return o(e,t),e}(l),u=function(t){function e(e,o,n){var r=t.call(this)||this;return r.templateRef=e,r.viewContainerRef=o,r.context=n,r}return o(e,t),Object.defineProperty(e.prototype,"origin",{get:function(){return this.templateRef.elementRef},enumerable:!0,configurable:!0}),e.prototype.attach=function(e,o){return void 0===o&&(o=this.context),this.context=o,t.prototype.attach.call(this,e)},e.prototype.detach=function(){return this.context=void 0,t.prototype.detach.call(this)},e}(l),d=function(){function t(){this._isDisposed=!1}return t.prototype.hasAttached=function(){return!!this._attachedPortal},t.prototype.attach=function(t){return t||n(),this.hasAttached()&&r(),this._isDisposed&&a(),t instanceof h?(this._attachedPortal=t,this.attachComponentPortal(t)):t instanceof u?(this._attachedPortal=t,this.attachTemplatePortal(t)):void i()},t.prototype.detach=function(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()},t.prototype.dispose=function(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0},t.prototype.setDisposeFn=function(t){this._disposeFn=t},t.prototype._invokeDisposeFn=function(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)},t}(),f=function(t){function e(e,o,n,r){var a=t.call(this)||this;return a.outletElement=e,a._componentFactoryResolver=o,a._appRef=n,a._defaultInjector=r,a}return o(e,t),e.prototype.attachComponentPortal=function(t){var e,o=this,n=this._componentFactoryResolver.resolveComponentFactory(t.component);return t.viewContainerRef?(e=t.viewContainerRef.createComponent(n,t.viewContainerRef.length,t.injector||t.viewContainerRef.parentInjector),this.setDisposeFn(function(){return e.destroy()})):(e=n.create(t.injector||this._defaultInjector),this._appRef.attachView(e.hostView),this.setDisposeFn(function(){o._appRef.detachView(e.hostView),e.destroy()})),this.outletElement.appendChild(this._getComponentRootNode(e)),e},e.prototype.attachTemplatePortal=function(t){var e=this,o=t.viewContainerRef,n=o.createEmbeddedView(t.templateRef,t.context);return n.detectChanges(),n.rootNodes.forEach(function(t){return e.outletElement.appendChild(t)}),this.setDisposeFn(function(){var t=o.indexOf(n);-1!==t&&o.remove(t)}),n},e.prototype.dispose=function(){t.prototype.dispose.call(this),null!=this.outletElement.parentNode&&this.outletElement.parentNode.removeChild(this.outletElement)},e.prototype._getComponentRootNode=function(t){return t.hostView.rootNodes[0]},e}(d),y=function(t){function n(e,o){return t.call(this,e,o)||this}return o(n,t),n.decorators=[{type:e.Directive,args:[{selector:"[cdk-portal], [cdkPortal], [portal]",exportAs:"cdkPortal"}]}],n.ctorParameters=function(){return[{type:e.TemplateRef},{type:e.ViewContainerRef}]},n}(u),_=function(t){function n(o,n){var r=t.call(this)||this;return r._componentFactoryResolver=o,r._viewContainerRef=n,r._isInitialized=!1,r.attached=new e.EventEmitter,r}return o(n,t),Object.defineProperty(n.prototype,"_deprecatedPortal",{get:function(){return this.portal},set:function(t){this.portal=t},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_deprecatedPortalHost",{get:function(){return this.portal},set:function(t){this.portal=t},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"portal",{get:function(){return this._attachedPortal},set:function(e){(!this.hasAttached()||e||this._isInitialized)&&(this.hasAttached()&&t.prototype.detach.call(this),e&&t.prototype.attach.call(this,e),this._attachedPortal=e)},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"attachedRef",{get:function(){return this._attachedRef},enumerable:!0,configurable:!0}),n.prototype.ngOnInit=function(){this._isInitialized=!0},n.prototype.ngOnDestroy=function(){t.prototype.dispose.call(this),this._attachedPortal=null,this._attachedRef=null},n.prototype.attachComponentPortal=function(e){e.setAttachedHost(this);var o=null!=e.viewContainerRef?e.viewContainerRef:this._viewContainerRef,n=this._componentFactoryResolver.resolveComponentFactory(e.component),r=o.createComponent(n,o.length,e.injector||o.parentInjector);return t.prototype.setDisposeFn.call(this,function(){return r.destroy()}),this._attachedPortal=e,this._attachedRef=r,this.attached.emit(r),r},n.prototype.attachTemplatePortal=function(e){var o=this;e.setAttachedHost(this);var n=this._viewContainerRef.createEmbeddedView(e.templateRef,e.context);return t.prototype.setDisposeFn.call(this,function(){return o._viewContainerRef.clear()}),this._attachedPortal=e,this._attachedRef=n,this.attached.emit(n),n},n.decorators=[{type:e.Directive,args:[{selector:"[cdkPortalOutlet], [cdkPortalHost], [portalHost]",exportAs:"cdkPortalOutlet, cdkPortalHost",inputs:["portal: cdkPortalOutlet"]}]}],n.ctorParameters=function(){return[{type:e.ComponentFactoryResolver},{type:e.ViewContainerRef}]},n.propDecorators={_deprecatedPortal:[{type:e.Input,args:["portalHost"]}],_deprecatedPortalHost:[{type:e.Input,args:["cdkPortalHost"]}],attached:[{type:e.Output,args:["attached"]}]},n}(d),m=function(){function t(){}return t.decorators=[{type:e.NgModule,args:[{exports:[y,_],declarations:[y,_]}]}],t.ctorParameters=function(){return[]},t}(),P=function(){function t(t,e){this._parentInjector=t,this._customTokens=e}return t.prototype.get=function(t,e){var o=this._customTokens.get(t);return void 0!==o?o:this._parentInjector.get(t,e)},t}();t.DomPortalHost=f,t.PortalHostDirective=_,t.TemplatePortalDirective=y,t.BasePortalHost=d,t.Portal=l,t.ComponentPortal=h,t.TemplatePortal=u,t.BasePortalOutlet=d,t.DomPortalOutlet=f,t.CdkPortal=y,t.CdkPortalOutlet=_,t.PortalModule=m,t.PortalInjector=P,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-portal.umd.min.js.map
