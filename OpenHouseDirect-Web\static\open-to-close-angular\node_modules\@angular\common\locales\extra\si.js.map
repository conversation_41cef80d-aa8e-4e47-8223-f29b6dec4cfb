{"version": 3, "file": "si.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/si.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;QAC5C;YACE,QAAQ,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK;YAC5C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa;SACnC;KACF;IACD;QACE;YACE,QAAQ,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK;YAC5C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa;SACnC;QACD,AADE;KAEH;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC3D;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['මැ', 'ම', 'පා', 'උ', 'ද', 'හ', 'රෑ', 'මැ'],\n    [\n      'මැදියම', 'මධ්\\u200dයාහ්නය', 'පාන්දර', 'උදේ',\n      'දවල්', 'හවස', 'රෑ', 'මැදියමට පසු'\n    ],\n  ],\n  [\n    [\n      'මැදියම', 'මධ්\\u200dයාහ්නය', 'පාන්දර', 'උදේ',\n      'දවල්', 'හවස', 'රෑ', 'මැදියමට පසු'\n    ],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['01:00', '06:00'], ['06:00', '12:00'], ['12:00', '14:00'],\n    ['14:00', '18:00'], ['18:00', '24:00'], ['00:00', '01:00']\n  ]\n];\n"]}