{"_from": "timespan@2.3.x", "_id": "timespan@2.3.0", "_inBundle": false, "_integrity": "sha512-0Jq9+58T2wbOyLth0EU+AUb6JMGCLaTWIykJFa7hyAybjVH9gpVMTfUAwo5fWAvtFt2Tjh/Elg8JtgNpnMnM8g==", "_location": "/timespan", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "timespan@2.3.x", "name": "timespan", "escapedName": "timespan", "rawSpec": "2.3.x", "saveSpec": null, "fetchSpec": "2.3.x"}, "_requiredBy": ["/loggly"], "_resolved": "https://registry.npmjs.org/timespan/-/timespan-2.3.0.tgz", "_shasum": "4902ce040bd13d845c8f59b27e9d59bad6f39929", "_spec": "timespan@2.3.x", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\loggly", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indexzero/TimeSpan.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "A JavaScript TimeSpan library for node.js (and soon the browser)", "devDependencies": {"vows": ">= 0.7.0"}, "engines": {"node": ">= 0.2.0"}, "homepage": "https://github.com/indexzero/TimeSpan.js#readme", "keywords": ["time", "dates", "utilities", "timespan"], "main": "./lib/time-span.js", "name": "timespan", "repository": {"type": "git", "url": "git+https://github.com/indexzero/TimeSpan.js.git"}, "scripts": {"test": "vows test/*-test.js --spec"}, "version": "2.3.0"}