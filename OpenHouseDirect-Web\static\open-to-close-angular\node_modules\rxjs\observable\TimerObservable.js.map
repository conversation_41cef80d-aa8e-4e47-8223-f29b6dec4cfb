{"version": 3, "file": "TimerObservable.js", "sourceRoot": "", "sources": ["../../src/observable/TimerObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,0BAA0B,mBAAmB,CAAC,CAAA;AAE9C,2BAA2B,eAAe,CAAC,CAAA;AAC3C,sBAAsB,oBAAoB,CAAC,CAAA;AAC3C,4BAA4B,qBAAqB,CAAC,CAAA;AAClD,uBAAuB,gBAAgB,CAAC,CAAA;AAIxC;;;;GAIG;AACH;IAAqC,mCAAkB;IAuErD,yBAAY,OAA0B,EAC1B,MAA4B,EAC5B,SAAsB;QAFtB,uBAA0B,GAA1B,WAA0B;QAGpC,iBAAO,CAAC;QAPF,WAAM,GAAW,CAAC,CAAC,CAAC;QACpB,YAAO,GAAW,CAAC,CAAC;QAQ1B,EAAE,CAAC,CAAC,qBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,yBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/B,SAAS,GAAgB,MAAM,CAAC;QAClC,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,yBAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5B,SAAS,GAAG,aAAK,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,eAAM,CAAC,OAAO,CAAC;YAC5B,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YACvB,OAAQ,CAAC;IACvB,CAAC;IAxFD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyCG;IACI,sBAAM,GAAb,UAAc,YAA+B,EAC/B,MAA4B,EAC5B,SAAsB;QAFtB,4BAA+B,GAA/B,gBAA+B;QAG3C,MAAM,CAAC,IAAI,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IAEM,wBAAQ,GAAf,UAAgB,KAAU;QAEhB,uBAAK,EAAE,qBAAM,EAAE,6BAAU,CAAW;QAC5C,IAAM,MAAM,GAAU,IAAK,CAAC;QAE5B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC/B,CAAC;QAED,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QACxB,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IA2BD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAA8B;QAC5E,IAAM,KAAK,GAAG,CAAC,CAAC;QAChB,IAAA,SAA2C,EAAnC,kBAAM,EAAE,oBAAO,EAAE,wBAAS,CAAU;QAE5C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE;YAC3D,YAAK,EAAE,cAAM,EAAE,sBAAU;SAC1B,CAAC,CAAC;IACL,CAAC;IACH,sBAAC;AAAD,CAAC,AApGD,CAAqC,uBAAU,GAoG9C;AApGY,uBAAe,kBAoG3B,CAAA", "sourcesContent": ["import { isNumeric } from '../util/isNumeric';\nimport { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isScheduler } from '../util/isScheduler';\nimport { isDate } from '../util/isDate';\nimport { TeardownLogic } from '../Subscription';\nimport { Subscriber } from '../Subscriber';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class TimerObservable extends Observable<number> {\n\n  /**\n   * Creates an Observable that starts emitting after an `initialDelay` and\n   * emits ever increasing numbers after each `period` of time thereafter.\n   *\n   * <span class=\"informal\">Its like {@link interval}, but you can specify when\n   * should the emissions start.</span>\n   *\n   * <img src=\"./img/timer.png\" width=\"100%\">\n   *\n   * `timer` returns an Observable that emits an infinite sequence of ascending\n   * integers, with a constant interval of time, `period` of your choosing\n   * between those emissions. The first emission happens after the specified\n   * `initialDelay`. The initial delay may be a {@link Date}. By default, this\n   * operator uses the `async` IScheduler to provide a notion of time, but you\n   * may pass any IScheduler to it. If `period` is not specified, the output\n   * Observable emits only one value, `0`. Otherwise, it emits an infinite\n   * sequence.\n   *\n   * @example <caption>Emits ascending numbers, one every second (1000ms), starting after 3 seconds</caption>\n   * var numbers = Rx.Observable.timer(3000, 1000);\n   * numbers.subscribe(x => console.log(x));\n   *\n   * @example <caption>Emits one number after five seconds</caption>\n   * var numbers = Rx.Observable.timer(5000);\n   * numbers.subscribe(x => console.log(x));\n   *\n   * @see {@link interval}\n   * @see {@link delay}\n   *\n   * @param {number|Date} initialDelay The initial delay time to wait before\n   * emitting the first value of `0`.\n   * @param {number} [period] The period of time between emissions of the\n   * subsequent numbers.\n   * @param {Scheduler} [scheduler=async] The IScheduler to use for scheduling\n   * the emission of values, and providing a notion of \"time\".\n   * @return {Observable} An Observable that emits a `0` after the\n   * `initialDelay` and ever increasing numbers after each `period` of time\n   * thereafter.\n   * @static true\n   * @name timer\n   * @owner Observable\n   */\n  static create(initialDelay: number | Date = 0,\n                period?: number | IScheduler,\n                scheduler?: IScheduler): Observable<number> {\n    return new TimerObservable(initialDelay, period, scheduler);\n  }\n\n  static dispatch(state: any) {\n\n    const { index, period, subscriber } = state;\n    const action = (<any> this);\n\n    subscriber.next(index);\n\n    if (subscriber.closed) {\n      return;\n    } else if (period === -1) {\n      return subscriber.complete();\n    }\n\n    state.index = index + 1;\n    action.schedule(state, period);\n  }\n\n  private period: number = -1;\n  private dueTime: number = 0;\n  private scheduler: IScheduler;\n\n  constructor(dueTime: number | Date = 0,\n              period?: number | IScheduler,\n              scheduler?: IScheduler) {\n    super();\n\n    if (isNumeric(period)) {\n      this.period = Number(period) < 1 && 1 || Number(period);\n    } else if (isScheduler(period)) {\n      scheduler = <IScheduler> period;\n    }\n\n    if (!isScheduler(scheduler)) {\n      scheduler = async;\n    }\n\n    this.scheduler = scheduler;\n    this.dueTime = isDate(dueTime) ?\n      (+dueTime - this.scheduler.now()) :\n      (<number> dueTime);\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<number>): TeardownLogic {\n    const index = 0;\n    const { period, dueTime, scheduler } = this;\n\n    return scheduler.schedule(TimerObservable.dispatch, dueTime, {\n      index, period, subscriber\n    });\n  }\n}\n"]}