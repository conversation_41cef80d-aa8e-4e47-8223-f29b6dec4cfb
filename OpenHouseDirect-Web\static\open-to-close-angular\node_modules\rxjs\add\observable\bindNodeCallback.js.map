{"version": 3, "file": "bindNodeCallback.js", "sourceRoot": "", "sources": ["../../../src/add/observable/bindNodeCallback.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,iCAA2D,mCAAmC,CAAC,CAAA;AAE/F,uBAAU,CAAC,gBAAgB,GAAG,mCAAsB,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { bindNodeCallback as staticBindNodeCallback } from '../../observable/bindNodeCallback';\n\nObservable.bindNodeCallback = staticBindNodeCallback;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let bindNodeCallback: typeof staticBindNodeCallback;\n  }\n}"]}