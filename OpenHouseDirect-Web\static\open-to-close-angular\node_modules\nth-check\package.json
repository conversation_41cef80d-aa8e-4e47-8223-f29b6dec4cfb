{"_from": "nth-check@^2.0.1", "_id": "nth-check@2.1.1", "_inBundle": false, "_integrity": "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==", "_location": "/nth-check", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "nth-check@^2.0.1", "name": "nth-check", "escapedName": "nth-check", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/css-select"], "_resolved": "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz", "_shasum": "c9eab428effce36cd6b92c924bdb000ef1f1ed1d", "_spec": "nth-check@^2.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\css-select", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "bundleDependencies": false, "dependencies": {"boolbase": "^1.0.0"}, "deprecated": false, "description": "Parses and compiles CSS nth-checks to highly optimized functions.", "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.5.0", "@types/node": "^17.0.35", "@typescript-eslint/eslint-plugin": "^5.25.0", "@typescript-eslint/parser": "^5.25.0", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "directories": {"lib": "lib/"}, "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}, "homepage": "https://github.com/fb55/nth-check", "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "keywords": ["nth-child", "nth", "css"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "nth-check", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git+https://github.com/fb55/nth-check.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/nth-check/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier '**/*.{ts,md,json,yml}'", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "2.1.1"}