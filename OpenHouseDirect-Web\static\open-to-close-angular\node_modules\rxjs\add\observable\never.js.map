{"version": 3, "file": "never.js", "sourceRoot": "", "sources": ["../../../src/add/observable/never.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { never as staticNever } from '../../observable/never';\n\nObservable.never = staticNever;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let never: typeof staticNever;\n  }\n}"]}