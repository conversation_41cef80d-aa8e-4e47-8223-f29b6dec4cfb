{"version": 3, "file": "toArray.js", "sourceRoot": "", "sources": ["../../../src/add/operator/toArray.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,wBAAwB,wBAAwB,CAAC,CAAA;AAEjD,uBAAU,CAAC,SAAS,CAAC,OAAO,GAAG,iBAAO,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { toArray } from '../../operator/toArray';\n\nObservable.prototype.toArray = toArray;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    toArray: typeof toArray;\n  }\n}"]}