{"_from": "tapable@^0.2.7", "_id": "tapable@0.2.9", "_inBundle": false, "_integrity": "sha512-2wsvQ+4GwBvLPLWsNfLCDYGsW6xb7aeC6utq2Qh0PFwgEy7K7dsma9Jsmb2zSQj7GvYAyUGSntLtsv++GmgL1A==", "_location": "/tapable", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tapable@^0.2.7", "name": "tapable", "escapedName": "tapable", "rawSpec": "^0.2.7", "saveSpec": null, "fetchSpec": "^0.2.7"}, "_requiredBy": ["/enhanced-resolve", "/webpack"], "_resolved": "https://registry.npmjs.org/tapable/-/tapable-0.2.9.tgz", "_shasum": "af2d8bbc9b04f74ee17af2b4d9048f807acd18a8", "_spec": "tapable@^0.2.7", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\enhanced-resolve", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Just a little module for plugins.", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}, "engines": {"node": ">=0.6"}, "files": ["lib"], "homepage": "https://github.com/webpack/tapable", "license": "MIT", "main": "lib/Tapable.js", "name": "tapable", "repository": {"type": "git", "url": "git+ssh://**************/webpack/tapable.git"}, "scripts": {"test": "mocha --reporter spec"}, "version": "0.2.9"}