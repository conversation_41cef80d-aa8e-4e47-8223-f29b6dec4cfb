/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/cdk/coercion"),require("rxjs/Subject"),require("rxjs/operators/debounceTime")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/coercion","rxjs/Subject","rxjs/operators/debounceTime"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.observers=e.ng.cdk.observers||{}),e.ng.core,e.ng.cdk.coercion,e.Rx,e.Rx.operators)}(this,function(e,t,n,r,o){"use strict";var i=function(){function e(){}return e.prototype.create=function(e){return"undefined"==typeof MutationObserver?null:new MutationObserver(e)},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[]},e}(),s=function(){function e(e,n,o){this._mutationObserverFactory=e,this._elementRef=n,this._ngZone=o,this._disabled=!1,this.event=new t.EventEmitter,this._debouncer=new r.Subject}return Object.defineProperty(e.prototype,"disabled",{get:function(){return this._disabled},set:function(e){this._disabled=n.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),e.prototype.ngAfterContentInit=function(){var e=this;this.debounce>0?this._ngZone.runOutsideAngular(function(){e._debouncer.pipe(o.debounceTime(e.debounce)).subscribe(function(t){return e.event.emit(t)})}):this._debouncer.subscribe(function(t){return e.event.emit(t)}),this._observer=this._ngZone.runOutsideAngular(function(){return e._mutationObserverFactory.create(function(t){e._debouncer.next(t)})}),this.disabled||this._enable()},e.prototype.ngOnChanges=function(e){e.disabled&&(e.disabled.currentValue?this._disable():this._enable())},e.prototype.ngOnDestroy=function(){this._disable(),this._debouncer.complete()},e.prototype._disable=function(){this._observer&&this._observer.disconnect()},e.prototype._enable=function(){this._observer&&this._observer.observe(this._elementRef.nativeElement,{characterData:!0,childList:!0,subtree:!0})},e.decorators=[{type:t.Directive,args:[{selector:"[cdkObserveContent]",exportAs:"cdkObserveContent"}]}],e.ctorParameters=function(){return[{type:i},{type:t.ElementRef},{type:t.NgZone}]},e.propDecorators={event:[{type:t.Output,args:["cdkObserveContent"]}],disabled:[{type:t.Input,args:["cdkObserveContentDisabled"]}],debounce:[{type:t.Input}]},e}(),c=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{exports:[s],declarations:[s],providers:[i]}]}],e.ctorParameters=function(){return[]},e}();e.ObserveContent=s,e.MutationObserverFactory=i,e.CdkObserveContent=s,e.ObserversModule=c,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-observers.umd.min.js.map
