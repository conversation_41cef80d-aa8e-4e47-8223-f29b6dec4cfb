{"version": 3, "file": "TimerObservable.js", "sourceRoot": "", "sources": ["../../src/observable/TimerObservable.ts"], "names": [], "mappings": "OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB;OAEtC,EAAE,UAAU,EAAE,MAAM,eAAe;OACnC,EAAE,KAAK,EAAE,MAAM,oBAAoB;OACnC,EAAE,WAAW,EAAE,MAAM,qBAAqB;OAC1C,EAAE,MAAM,EAAE,MAAM,gBAAgB;AAIvC;;;;GAIG;AACH,qCAAqC,UAAU;IAuE7C,YAAY,OAAO,GAAkB,CAAC,EAC1B,MAA4B,EAC5B,SAAsB;QAChC,OAAO,CAAC;QAPF,WAAM,GAAW,CAAC,CAAC,CAAC;QACpB,YAAO,GAAW,CAAC,CAAC;QAQ1B,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/B,SAAS,GAAgB,MAAM,CAAC;QAClC,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5B,SAAS,GAAG,KAAK,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAC5B,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YACvB,OAAQ,CAAC;IACvB,CAAC;IAxFD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyCG;IACH,OAAO,MAAM,CAAC,YAAY,GAAkB,CAAC,EAC/B,MAA4B,EAC5B,SAAsB;QAClC,MAAM,CAAC,IAAI,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED,OAAO,QAAQ,CAAC,KAAU;QAExB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;QAC5C,MAAM,MAAM,GAAU,IAAK,CAAC;QAE5B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC/B,CAAC;QAED,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QACxB,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IA2BD,oCAAoC,CAAC,UAAU,CAAC,UAA8B;QAC5E,MAAM,KAAK,GAAG,CAAC,CAAC;QAChB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAE5C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE;YAC3D,KAAK,EAAE,MAAM,EAAE,UAAU;SAC1B,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAAA"}