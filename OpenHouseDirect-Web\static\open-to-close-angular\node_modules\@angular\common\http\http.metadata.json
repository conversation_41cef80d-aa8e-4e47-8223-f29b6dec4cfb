{"__symbolic": "module", "version": 4, "metadata": {"HttpBackend": {"__symbolic": "class", "members": {"handle": [{"__symbolic": "method"}]}}, "HttpHandler": {"__symbolic": "class", "members": {"handle": [{"__symbolic": "method"}]}}, "HttpClient": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 61, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "HttpHandler"}]}], "request": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "delete": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "get": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "head": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "jsonp": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "options": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "patch": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "post": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "put": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}]}}, "HttpHeaders": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 41, "character": 31, "module": "./src/headers"}]}], "has": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "keys": [{"__symbolic": "method"}], "getAll": [{"__symbolic": "method"}], "append": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}], "delete": [{"__symbolic": "method"}], "maybeSetNormalizedName": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "copyFrom": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}], "applyUpdate": [{"__symbolic": "method"}], "forEach": [{"__symbolic": "method"}]}}, "ɵa": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 67, "character": 1}}], "members": {"intercept": [{"__symbolic": "method"}]}}, "HTTP_INTERCEPTORS": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken", "line": 65, "character": 37}, "arguments": ["HTTP_INTERCEPTORS"]}, "HttpInterceptor": {"__symbolic": "interface"}, "ɵb": {"__symbolic": "class", "members": {}}, "JsonpClientBackend": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 47, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 49, "character": 58}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 49, "character": 65}]}]], "parameters": [{"__symbolic": "reference", "name": "ɵb"}, {"__symbolic": "reference", "name": "any"}]}], "nextCallback": [{"__symbolic": "method"}], "handle": [{"__symbolic": "method"}]}}, "JsonpInterceptor": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 211, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "JsonpClientBackend"}]}], "intercept": [{"__symbolic": "method"}]}}, "ɵc": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 28, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "HttpBackend"}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 32, "character": 62}]}], "handle": [{"__symbolic": "method"}]}}, "ɵd": {"__symbolic": "function"}, "HttpClientJsonpModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 163, "character": 1}, "arguments": [{"providers": [{"__symbolic": "reference", "name": "JsonpClientBackend"}, {"provide": {"__symbolic": "reference", "name": "ɵb"}, "useFactory": {"__symbolic": "reference", "name": "ɵd"}}, {"provide": {"__symbolic": "reference", "name": "HTTP_INTERCEPTORS"}, "useClass": {"__symbolic": "reference", "name": "JsonpInterceptor"}, "multi": true}]}]}], "members": {}}, "HttpClientModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 136, "character": 1}, "arguments": [{"imports": [{"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "HttpClientXsrfModule"}, "member": "withOptions"}, "arguments": [{"cookieName": "XSRF-TOKEN", "headerName": "X-XSRF-TOKEN"}]}], "providers": [{"__symbolic": "reference", "name": "HttpClient"}, {"provide": {"__symbolic": "reference", "name": "HttpHandler"}, "useClass": {"__symbolic": "reference", "name": "ɵc"}}, {"__symbolic": "reference", "name": "HttpXhrBackend"}, {"provide": {"__symbolic": "reference", "name": "HttpBackend"}, "useExisting": {"__symbolic": "reference", "name": "HttpXhrBackend"}}, {"__symbolic": "reference", "name": "ɵe"}, {"provide": {"__symbolic": "reference", "name": "XhrFactory"}, "useExisting": {"__symbolic": "reference", "name": "ɵe"}}]}]}], "members": {}}, "HttpClientXsrfModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 88, "character": 1}, "arguments": [{"providers": [{"__symbolic": "reference", "name": "ɵi"}, {"provide": {"__symbolic": "reference", "name": "HTTP_INTERCEPTORS"}, "useExisting": {"__symbolic": "reference", "name": "ɵi"}, "multi": true}, {"provide": {"__symbolic": "reference", "name": "HttpXsrfTokenExtractor"}, "useClass": {"__symbolic": "reference", "name": "ɵh"}}, {"provide": {"__symbolic": "reference", "name": "ɵf"}, "useValue": "XSRF-TOKEN"}, {"provide": {"__symbolic": "reference", "name": "ɵg"}, "useValue": "X-XSRF-TOKEN"}]}]}], "members": {}, "statics": {"disable": {"__symbolic": "function", "parameters": [], "value": {"ngModule": {"__symbolic": "reference", "name": "HttpClientXsrfModule"}, "providers": [{"provide": {"__symbolic": "reference", "name": "ɵi"}, "useClass": {"__symbolic": "reference", "name": "ɵa"}}]}}, "withOptions": {"__symbolic": "function", "parameters": ["options"], "defaults": [{}], "value": {"ngModule": {"__symbolic": "reference", "name": "HttpClientXsrfModule"}, "providers": [{"__symbolic": "if", "condition": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "options"}, "member": "cookieName"}, "thenExpression": {"provide": {"__symbolic": "reference", "name": "ɵf"}, "useValue": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "options"}, "member": "cookieName"}}, "elseExpression": []}, {"__symbolic": "if", "condition": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "options"}, "member": "headerName"}, "thenExpression": {"provide": {"__symbolic": "reference", "name": "ɵg"}, "useValue": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "options"}, "member": "headerName"}}, "elseExpression": []}]}}}}, "ɵinterceptingHandler": {"__symbolic": "function"}, "HttpParameterCodec": {"__symbolic": "interface"}, "HttpParams": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [null]}], "has": [{"__symbolic": "method"}], "get": [{"__symbolic": "method"}], "getAll": [{"__symbolic": "method"}], "keys": [{"__symbolic": "method"}], "append": [{"__symbolic": "method"}], "set": [{"__symbolic": "method"}], "delete": [{"__symbolic": "method"}], "toString": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}]}}, "HttpUrlEncodingCodec": {"__symbolic": "class", "members": {"encodeKey": [{"__symbolic": "method"}], "encodeValue": [{"__symbolic": "method"}], "decodeKey": [{"__symbolic": "method"}], "decodeValue": [{"__symbolic": "method"}]}}, "HttpRequest": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 128, "character": 22, "module": "./src/request"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 128, "character": 83, "module": "./src/request"}]}, {"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 135, "character": 22, "module": "./src/request"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 135, "character": 63, "context": {"typeName": "T"}, "module": "./src/request"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 135, "character": 78, "module": "./src/request"}]}, {"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 142, "character": 49, "context": {"typeName": "T"}, "module": "./src/request"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 142, "character": 64, "module": "./src/request"}]}, {"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 150, "character": 52, "context": {"typeName": "T"}, "module": "./src/request"}, {"__symbolic": "error", "message": "Expression form not supported", "line": 157, "character": 15, "module": "./src/request"}]}], "serializeBody": [{"__symbolic": "method"}], "detectContentTypeHeader": [{"__symbolic": "method"}], "clone": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}]}}, "HttpDownloadProgressEvent": {"__symbolic": "interface"}, "HttpErrorResponse": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "HttpResponseBase"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 311, "character": 20, "module": "./src/response"}]}]}}, "HttpEvent": {"__symbolic": "interface"}, "HttpEventType": {"Sent": 0, "UploadProgress": 1, "ResponseHeader": 2, "DownloadProgress": 3, "Response": 4, "User": 5}, "HttpHeaderResponse": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "HttpResponseBase"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 214, "character": 20, "module": "./src/response"}]}], "clone": [{"__symbolic": "method"}]}}, "HttpProgressEvent": {"__symbolic": "interface"}, "HttpResponse": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "HttpResponseBase"}, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 260, "character": 20, "module": "./src/response"}]}], "clone": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}]}}, "HttpResponseBase": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Expression form not supported", "line": 182, "character": 12, "module": "./src/response"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "string"}]}]}}, "HttpSentEvent": {"__symbolic": "interface"}, "HttpUserEvent": {"__symbolic": "interface"}, "ɵe": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 45, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor"}], "build": [{"__symbolic": "method"}]}}, "HttpXhrBackend": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 67, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "XhrFactory"}]}], "handle": [{"__symbolic": "method"}]}}, "XhrFactory": {"__symbolic": "class", "members": {"build": [{"__symbolic": "method"}]}}, "ɵf": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken", "line": 17, "character": 36}, "arguments": ["XSRF_COOKIE_NAME"]}, "ɵg": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken", "line": 18, "character": 36}, "arguments": ["XSRF_HEADER_NAME"]}, "ɵh": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 37, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 48, "character": 7}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 48, "character": 14}]}], [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 48, "character": 43}, "arguments": [{"__symbolic": "reference", "module": "@angular/core", "name": "PLATFORM_ID", "line": 48, "character": 50}]}], [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 49, "character": 7}, "arguments": [{"__symbolic": "reference", "name": "ɵf"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "string"}, {"__symbolic": "reference", "name": "string"}]}], "getToken": [{"__symbolic": "method"}]}}, "ɵi": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 68, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 72, "character": 7}, "arguments": [{"__symbolic": "reference", "name": "ɵg"}]}]], "parameters": [{"__symbolic": "reference", "name": "HttpXsrfTokenExtractor"}, {"__symbolic": "reference", "name": "string"}]}], "intercept": [{"__symbolic": "method"}]}}, "HttpXsrfTokenExtractor": {"__symbolic": "class", "members": {"getToken": [{"__symbolic": "method"}]}}}, "origins": {"HttpBackend": "./src/backend", "HttpHandler": "./src/backend", "HttpClient": "./src/client", "HttpHeaders": "./src/headers", "ɵa": "./src/interceptor", "HTTP_INTERCEPTORS": "./src/interceptor", "HttpInterceptor": "./src/interceptor", "ɵb": "./src/jsonp", "JsonpClientBackend": "./src/jsonp", "JsonpInterceptor": "./src/jsonp", "ɵc": "./src/module", "ɵd": "./src/module", "HttpClientJsonpModule": "./src/module", "HttpClientModule": "./src/module", "HttpClientXsrfModule": "./src/module", "ɵinterceptingHandler": "./src/module", "HttpParameterCodec": "./src/params", "HttpParams": "./src/params", "HttpUrlEncodingCodec": "./src/params", "HttpRequest": "./src/request", "HttpDownloadProgressEvent": "./src/response", "HttpErrorResponse": "./src/response", "HttpEvent": "./src/response", "HttpEventType": "./src/response", "HttpHeaderResponse": "./src/response", "HttpProgressEvent": "./src/response", "HttpResponse": "./src/response", "HttpResponseBase": "./src/response", "HttpSentEvent": "./src/response", "HttpUserEvent": "./src/response", "ɵe": "./src/xhr", "HttpXhrBackend": "./src/xhr", "XhrFactory": "./src/xhr", "ɵf": "./src/xsrf", "ɵg": "./src/xsrf", "ɵh": "./src/xsrf", "ɵi": "./src/xsrf", "HttpXsrfTokenExtractor": "./src/xsrf"}, "importAs": "@angular/common/http"}