{"_from": "strip-bom@^3.0.0", "_id": "strip-bom@3.0.0", "_inBundle": false, "_integrity": "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==", "_location": "/tsconfig/strip-bom", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-bom@^3.0.0", "name": "strip-bom", "escapedName": "strip-bom", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/tsconfig"], "_resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "_shasum": "2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3", "_spec": "strip-bom@^3.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\tsconfig", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Strip UTF-8 byte order mark (BOM) from a string", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/strip-bom#readme", "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "license": "MIT", "name": "strip-bom", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}