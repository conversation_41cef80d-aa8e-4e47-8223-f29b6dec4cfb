{"version": 3, "file": "refCount.js", "sourceRoot": "", "sources": ["../../src/operators/refCount.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAM3C;IACE,MAAM,CAAC,kCAAkC,MAAgC;QACvE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;AACJ,CAAC;AAJe,gBAAQ,WAIvB,CAAA;AAED;IACE,0BAAoB,WAAqC;QAArC,gBAAW,GAAX,WAAW,CAA0B;IACzD,CAAC;IACD,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QAEjC,kCAAW,CAAU;QACtB,WAAY,CAAC,SAAS,EAAE,CAAC;QAEhC,IAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACnE,IAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAElD,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YAChB,UAAW,CAAC,UAAU,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QACxD,CAAC;QAED,MAAM,CAAC,YAAY,CAAC;IACtB,CAAC;IACH,uBAAC;AAAD,CAAC,AAjBD,IAiBC;AAED;IAAoC,sCAAa;IAI/C,4BAAY,WAA0B,EAClB,WAAqC;QACvD,kBAAM,WAAW,CAAC,CAAC;QADD,gBAAW,GAAX,WAAW,CAA0B;IAEzD,CAAC;IAED,oCAAoC,CAAC,yCAAY,GAAZ;QAE3B,kCAAW,CAAU;QAC7B,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAM,QAAQ,GAAU,WAAY,CAAC,SAAS,CAAC;QAC/C,EAAE,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAEM,WAAY,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7C,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAED,GAAG;QACH,wEAAwE;QACxE,wEAAwE;QACxE,yEAAyE;QACzE,2EAA2E;QAC3E,0EAA0E;QAC1E,yEAAyE;QACzE,eAAe;QACf,MAAM;QACN,0BAA0B;QAC1B,eAAe;QACf,gBAAgB;QAChB,aAAa;QACb,kBAAkB;QAClB,MAAM;QACN,4EAA4E;QAC5E,oEAAoE;QACpE,gDAAgD;QAChD,4EAA4E;QAC5E,6BAA6B;QAC7B,2EAA2E;QAC3E,6CAA6C;QAC7C,GAAG;QACK,gCAAU,CAAU;QAC5B,IAAM,gBAAgB,GAAU,WAAY,CAAC,WAAW,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,EAAE,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,UAAU,IAAI,gBAAgB,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC;YACzE,gBAAgB,CAAC,WAAW,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AA7DD,CAAoC,uBAAU,GA6D7C", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription, TeardownLogic } from '../Subscription';\nimport { MonoTypeOperatorFunction } from '../interfaces';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { Observable } from '../Observable';\n\nexport function refCount<T>(): MonoTypeOperatorFunction<T> {\n  return function refCountOperatorFunction(source: ConnectableObservable<T>): Observable<T> {\n    return source.lift(new RefCountOperator(source));\n  };\n}\n\nclass RefCountOperator<T> implements Operator<T, T> {\n  constructor(private connectable: ConnectableObservable<T>) {\n  }\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n\n    const { connectable } = this;\n    (<any> connectable)._refCount++;\n\n    const refCounter = new RefCountSubscriber(subscriber, connectable);\n    const subscription = source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      (<any> refCounter).connection = connectable.connect();\n    }\n\n    return subscription;\n  }\n}\n\nclass RefCountSubscriber<T> extends Subscriber<T> {\n\n  private connection: Subscription;\n\n  constructor(destination: Subscriber<T>,\n              private connectable: ConnectableObservable<T>) {\n    super(destination);\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n\n    const { connectable } = this;\n    if (!connectable) {\n      this.connection = null;\n      return;\n    }\n\n    this.connectable = null;\n    const refCount = (<any> connectable)._refCount;\n    if (refCount <= 0) {\n      this.connection = null;\n      return;\n    }\n\n    (<any> connectable)._refCount = refCount - 1;\n    if (refCount > 1) {\n      this.connection = null;\n      return;\n    }\n\n    ///\n    // Compare the local RefCountSubscriber's connection Subscription to the\n    // connection Subscription on the shared ConnectableObservable. In cases\n    // where the ConnectableObservable source synchronously emits values, and\n    // the RefCountSubscriber's downstream Observers synchronously unsubscribe,\n    // execution continues to here before the RefCountOperator has a chance to\n    // supply the RefCountSubscriber with the shared connection Subscription.\n    // For example:\n    // ```\n    // Observable.range(0, 10)\n    //   .publish()\n    //   .refCount()\n    //   .take(5)\n    //   .subscribe();\n    // ```\n    // In order to account for this case, RefCountSubscriber should only dispose\n    // the ConnectableObservable's shared connection Subscription if the\n    // connection Subscription exists, *and* either:\n    //   a. RefCountSubscriber doesn't have a reference to the shared connection\n    //      Subscription yet, or,\n    //   b. RefCountSubscriber's connection Subscription reference is identical\n    //      to the shared connection Subscription\n    ///\n    const { connection } = this;\n    const sharedConnection = (<any> connectable)._connection;\n    this.connection = null;\n\n    if (sharedConnection && (!connection || sharedConnection === connection)) {\n      sharedConnection.unsubscribe();\n    }\n  }\n}\n"]}