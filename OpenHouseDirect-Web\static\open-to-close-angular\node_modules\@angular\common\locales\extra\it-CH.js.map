{"version": 3, "file": "it-CH.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/it-CH.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,UAAU,CAAC;QACpF,AADqF;KAEtF;IACD;QACE,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,CAAC;QACvE,AADwE;KAEzE;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['mezzanotte', 'mezzogiorno', 'di mattina', 'del pomeriggio', 'di sera', 'di notte'],\n    ,\n  ],\n  [\n    ['mezzanotte', 'mezzogiorno', 'mattina', 'pomeriggio', 'sera', 'notte'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'],\n    ['00:00', '06:00']\n  ]\n];\n"]}