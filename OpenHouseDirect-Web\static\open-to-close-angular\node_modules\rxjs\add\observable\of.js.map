{"version": 3, "file": "of.js", "sourceRoot": "", "sources": ["../../../src/add/observable/of.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,mBAA+B,qBAAqB,CAAC,CAAA;AAErD,uBAAU,CAAC,EAAE,GAAG,OAAQ,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { of as staticOf } from '../../observable/of';\n\nObservable.of = staticOf;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let of: typeof staticOf; //formOf an iceberg!\n  }\n}"]}