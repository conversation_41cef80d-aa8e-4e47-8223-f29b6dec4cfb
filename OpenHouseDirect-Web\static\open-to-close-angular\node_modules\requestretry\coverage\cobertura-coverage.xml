<?xml version="1.0" ?>
<!DOCTYPE coverage SYSTEM "http://cobertura.sourceforge.net/xml/coverage-04.dtd">
<coverage lines-valid="119" lines-covered="119" line-rate="1" branches-valid="63" branches-covered="63" branch-rate="1" timestamp="1516271027647" complexity="0" version="0.1">
  <sources>
    <source>/www/labs/node-request-retry</source>
  </sources>
  <packages>
    <package name="node-request-retry" line-rate="1" branch-rate="1">
      <classes>
        <class name="index.js" filename="index.js" line-rate="1" branch-rate="1">
          <methods>
            <method name="defaultPromiseFactory" hits="13" signature="()V">
              <lines>
                <line number="24" hits="13"/>
              </lines>
            </method>
            <method name="makePromise" hits="21" signature="()V">
              <lines>
                <line number="35" hits="21"/>
              </lines>
            </method>
            <method name="Resolver" hits="21" signature="()V">
              <lines>
                <line number="39" hits="21"/>
              </lines>
            </method>
            <method name="Request" hits="40" signature="()V">
              <lines>
                <line number="47" hits="40"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="4" signature="()V">
              <lines>
                <line number="91" hits="4"/>
              </lines>
            </method>
            <method name="requestRetryReply" hits="41" signature="()V">
              <lines>
                <line number="103" hits="41"/>
              </lines>
            </method>
            <method name="(anonymous_6)" hits="46" signature="()V">
              <lines>
                <line number="120" hits="46"/>
              </lines>
            </method>
            <method name="(anonymous_7)" hits="44" signature="()V">
              <lines>
                <line number="124" hits="44"/>
              </lines>
            </method>
            <method name="(anonymous_8)" hits="3" signature="()V">
              <lines>
                <line number="142" hits="3"/>
              </lines>
            </method>
            <method name="(anonymous_9)" hits="10" signature="()V">
              <lines>
                <line number="151" hits="10"/>
              </lines>
            </method>
            <method name="exposedRequestMethod" hits="2" signature="()V">
              <lines>
                <line number="152" hits="2"/>
              </lines>
            </method>
            <method name="(anonymous_11)" hits="5" signature="()V">
              <lines>
                <line number="158" hits="5"/>
              </lines>
            </method>
            <method name="exposedPromiseMethod" hits="21" signature="()V">
              <lines>
                <line number="159" hits="21"/>
              </lines>
            </method>
            <method name="Factory" hits="40" signature="()V">
              <lines>
                <line number="167" hits="40"/>
              </lines>
            </method>
            <method name="makeHelper" hits="54" signature="()V">
              <lines>
                <line number="175" hits="54"/>
              </lines>
            </method>
            <method name="helper" hits="10" signature="()V">
              <lines>
                <line number="176" hits="10"/>
              </lines>
            </method>
            <method name="defaults" hits="8" signature="()V">
              <lines>
                <line number="204" hits="8"/>
              </lines>
            </method>
            <method name="(anonymous_17)" hits="6" signature="()V">
              <lines>
                <line number="205" hits="6"/>
              </lines>
            </method>
            <method name="(anonymous_18)" hits="2" signature="()V">
              <lines>
                <line number="212" hits="2"/>
              </lines>
            </method>
            <method name="(anonymous_19)" hits="48" signature="()V">
              <lines>
                <line number="219" hits="48"/>
              </lines>
            </method>
            <method name="(anonymous_20)" hits="16" signature="()V">
              <lines>
                <line number="224" hits="16"/>
              </lines>
            </method>
            <method name="(anonymous_21)" hits="6" signature="()V">
              <lines>
                <line number="238" hits="6"/>
              </lines>
            </method>
            <method name="(anonymous_22)" hits="2" signature="()V">
              <lines>
                <line number="243" hits="2"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="10" hits="1" branch="false"/>
            <line number="11" hits="1" branch="false"/>
            <line number="12" hits="1" branch="false"/>
            <line number="13" hits="1" branch="false"/>
            <line number="14" hits="1" branch="false"/>
            <line number="16" hits="1" branch="false"/>
            <line number="25" hits="13" branch="false"/>
            <line number="40" hits="21" branch="false"/>
            <line number="41" hits="21" branch="false"/>
            <line number="44" hits="21" branch="false"/>
            <line number="49" hits="40" branch="true" condition-coverage="100% (2/2)"/>
            <line number="51" hits="5" branch="true" condition-coverage="100% (2/2)"/>
            <line number="52" hits="1" branch="false"/>
            <line number="55" hits="5" branch="true" condition-coverage="100% (2/2)"/>
            <line number="56" hits="2" branch="false"/>
            <line number="60" hits="5" branch="false"/>
            <line number="63" hits="40" branch="true" condition-coverage="100% (2/2)"/>
            <line number="64" hits="35" branch="true" condition-coverage="100% (2/2)"/>
            <line number="65" hits="17" branch="false"/>
            <line number="67" hits="35" branch="false"/>
            <line number="70" hits="40" branch="false"/>
            <line number="71" hits="40" branch="false"/>
            <line number="72" hits="40" branch="false"/>
            <line number="73" hits="40" branch="false"/>
            <line number="79" hits="40" branch="false"/>
            <line number="85" hits="40" branch="true" condition-coverage="100% (2/2)"/>
            <line number="91" hits="40" branch="true" condition-coverage="100% (2/2)"/>
            <line number="93" hits="40" branch="false"/>
            <line number="94" hits="40" branch="false"/>
            <line number="96" hits="40" branch="true" condition-coverage="100% (2/2)"/>
            <line number="99" hits="40" branch="true" condition-coverage="100% (2/2)"/>
            <line number="100" hits="21" branch="false"/>
            <line number="103" hits="40" branch="false"/>
            <line number="104" hits="41" branch="true" condition-coverage="100% (2/2)"/>
            <line number="105" hits="19" branch="false"/>
            <line number="108" hits="22" branch="true" condition-coverage="100% (2/2)"/>
            <line number="109" hits="3" branch="false"/>
            <line number="113" hits="19" branch="true" condition-coverage="100% (2/2)"/>
            <line number="114" hits="19" branch="false"/>
            <line number="118" hits="1" branch="false"/>
            <line number="120" hits="1" branch="false"/>
            <line number="121" hits="46" branch="false"/>
            <line number="122" hits="46" branch="false"/>
            <line number="124" hits="46" branch="false"/>
            <line number="125" hits="44" branch="true" condition-coverage="100% (2/2)"/>
            <line number="126" hits="40" branch="false"/>
            <line number="129" hits="44" branch="true" condition-coverage="100% (2/2)"/>
            <line number="130" hits="4" branch="false"/>
            <line number="133" hits="44" branch="true" condition-coverage="100% (4/4)"/>
            <line number="134" hits="6" branch="false"/>
            <line number="135" hits="6" branch="false"/>
            <line number="138" hits="38" branch="false"/>
            <line number="142" hits="1" branch="false"/>
            <line number="143" hits="3" branch="true" condition-coverage="100% (2/2)"/>
            <line number="144" hits="2" branch="false"/>
            <line number="146" hits="3" branch="false"/>
            <line number="147" hits="3" branch="false"/>
            <line number="151" hits="1" branch="false"/>
            <line number="152" hits="10" branch="false"/>
            <line number="153" hits="2" branch="false"/>
            <line number="158" hits="1" branch="false"/>
            <line number="159" hits="5" branch="false"/>
            <line number="160" hits="21" branch="true" condition-coverage="100% (2/2)"/>
            <line number="161" hits="1" branch="false"/>
            <line number="163" hits="20" branch="false"/>
            <line number="168" hits="40" branch="true" condition-coverage="100% (4/4)"/>
            <line number="169" hits="40" branch="false"/>
            <line number="170" hits="40" branch="false"/>
            <line number="171" hits="40" branch="false"/>
            <line number="176" hits="54" branch="false"/>
            <line number="178" hits="10" branch="true" condition-coverage="100% (2/2)"/>
            <line number="180" hits="6" branch="true" condition-coverage="100% (2/2)"/>
            <line number="181" hits="2" branch="false"/>
            <line number="184" hits="6" branch="true" condition-coverage="100% (2/2)"/>
            <line number="185" hits="2" branch="false"/>
            <line number="189" hits="6" branch="false"/>
            <line number="192" hits="10" branch="true" condition-coverage="100% (2/2)"/>
            <line number="193" hits="4" branch="true" condition-coverage="100% (2/2)"/>
            <line number="194" hits="2" branch="false"/>
            <line number="196" hits="4" branch="false"/>
            <line number="199" hits="10" branch="false"/>
            <line number="200" hits="10" branch="false"/>
            <line number="205" hits="8" branch="false"/>
            <line number="206" hits="6" branch="true" condition-coverage="100% (2/2)"/>
            <line number="207" hits="2" branch="false"/>
            <line number="209" hits="6" branch="true" condition-coverage="100% (2/2)"/>
            <line number="212" hits="8" branch="false"/>
            <line number="213" hits="2" branch="true" condition-coverage="100% (2/2)"/>
            <line number="216" hits="8" branch="false"/>
            <line number="217" hits="8" branch="false"/>
            <line number="219" hits="8" branch="false"/>
            <line number="220" hits="48" branch="false"/>
            <line number="222" hits="8" branch="false"/>
            <line number="224" hits="8" branch="false"/>
            <line number="225" hits="16" branch="false"/>
            <line number="228" hits="8" branch="false"/>
            <line number="231" hits="1" branch="false"/>
            <line number="233" hits="1" branch="false"/>
            <line number="234" hits="1" branch="false"/>
            <line number="235" hits="1" branch="false"/>
            <line number="238" hits="1" branch="false"/>
            <line number="239" hits="6" branch="false"/>
            <line number="241" hits="1" branch="false"/>
            <line number="243" hits="1" branch="false"/>
            <line number="244" hits="2" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="node-request-retry.strategies" line-rate="1" branch-rate="1">
      <classes>
        <class name="HTTPError.js" filename="strategies/HTTPError.js" line-rate="1" branch-rate="1">
          <methods>
            <method name="HTTPError" hits="64" signature="()V">
              <lines>
                <line number="8" hits="64"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="8" hits="1" branch="false"/>
            <line number="9" hits="64" branch="true" condition-coverage="100% (3/3)"/>
          </lines>
        </class>
        <class name="HTTPOrNetworkError.js" filename="strategies/HTTPOrNetworkError.js" line-rate="1" branch-rate="1">
          <methods>
            <method name="HTTPOrNetworkError" hits="1" signature="()V">
              <lines>
                <line number="2" hits="1"/>
              </lines>
            </method>
            <method name="HTTPError" hits="59" signature="()V">
              <lines>
                <line number="8" hits="59"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="2" hits="1" branch="false"/>
            <line number="8" hits="1" branch="false"/>
            <line number="9" hits="59" branch="true" condition-coverage="100% (2/2)"/>
          </lines>
        </class>
        <class name="NetworkError.js" filename="strategies/NetworkError.js" line-rate="1" branch-rate="1">
          <methods>
            <method name="NetworkError" hits="57" signature="()V">
              <lines>
                <line number="11" hits="57"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="3" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="12" hits="57" branch="true" condition-coverage="100% (2/2)"/>
            <line number="15" hits="1" branch="false"/>
            <line number="16" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="index.js" filename="strategies/index.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="2" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="5" hits="1" branch="false"/>
            <line number="6" hits="1" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>
