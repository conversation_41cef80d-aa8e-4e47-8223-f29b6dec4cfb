{"version": 3, "file": "FromEventPatternObservable.js", "sourceRoot": "", "sources": ["../../src/observable/FromEventPatternObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAA2B,oBAAoB,CAAC,CAAA;AAChD,2BAA2B,eAAe,CAAC,CAAA;AAC3C,6BAA6B,iBAAiB,CAAC,CAAA;AAG/C;;;;GAIG;AACH;IAAmD,8CAAa;IAwD9D,oCAAoB,UAAsC,EACtC,aAAyD,EACzD,QAAqC;QACvD,iBAAO,CAAC;QAHU,eAAU,GAAV,UAAU,CAA4B;QACtC,kBAAa,GAAb,aAAa,CAA4C;QACzD,aAAQ,GAAR,QAAQ,CAA6B;IAEzD,CAAC;IA1DD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+CG;IACI,iCAAM,GAAb,UAAiB,UAAsC,EACtC,aAAyD,EACzD,QAAqC;QACpD,MAAM,CAAC,IAAI,0BAA0B,CAAC,UAAU,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAQD,oCAAoC,CAAC,+CAAU,GAAV,UAAW,UAAyB;QAApC,iBAiBpC;QAhBC,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEzC,IAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG;YAAC,cAAmB;iBAAnB,WAAmB,CAAnB,sBAAmB,CAAnB,IAAmB;gBAAnB,6BAAmB;;YACpD,KAAI,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,GAAG,UAAS,CAAM,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7C,IAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAE3D,EAAE,CAAC,CAAC,CAAC,uBAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,GAAG,CAAC,IAAI,2BAAY,CAAC;YAC9B,4DAA4D;YAC5D,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAE;QACpC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,kDAAa,GAArB,UAAsB,UAAyB,EAAE,IAAgB;QAC/D,IAAI,CAAC;YACH,IAAM,MAAM,GAAM,IAAI,CAAC,QAAQ,OAAb,IAAI,EAAa,IAAI,CAAC,CAAC;YACzC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,CACA;QAAA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,oDAAe,GAAvB,UAAwB,OAAyB,EAAE,eAA8B;QAC/E,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;QAC1C,CACA;QAAA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IACH,iCAAC;AAAD,CAAC,AAnGD,CAAmD,uBAAU,GAmG5D;AAnGY,kCAA0B,6BAmGtC,CAAA", "sourcesContent": ["import { isFunction } from '../util/isFunction';\nimport { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { Subscriber } from '../Subscriber';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class FromEventPatternObservable<T> extends Observable<T> {\n\n  /**\n   * Creates an Observable from an API based on addHandler/removeHandler\n   * functions.\n   *\n   * <span class=\"informal\">Converts any addHandler/removeHandler API to an\n   * Observable.</span>\n   *\n   * <img src=\"./img/fromEventPattern.png\" width=\"100%\">\n   *\n   * Creates an Observable by using the `addHandler` and `removeHandler`\n   * functions to add and remove the handlers, with an optional selector\n   * function to project the event arguments to a result. The `addHandler` is\n   * called when the output Observable is subscribed, and `removeHandler` is\n   * called when the Subscription is unsubscribed.\n   *\n   * @example <caption>Emits clicks happening on the DOM document</caption>\n   * function addClickHandler(handler) {\n   *   document.addEventListener('click', handler);\n   * }\n   *\n   * function removeClickHandler(handler) {\n   *   document.removeEventListener('click', handler);\n   * }\n   *\n   * var clicks = Rx.Observable.fromEventPattern(\n   *   addClickHandler,\n   *   removeClickHandler\n   * );\n   * clicks.subscribe(x => console.log(x));\n   *\n   * @see {@link from}\n   * @see {@link fromEvent}\n   *\n   * @param {function(handler: Function): any} addHandler A function that takes\n   * a `handler` function as argument and attaches it somehow to the actual\n   * source of events.\n   * @param {function(handler: Function, signal?: any): void} [removeHandler] An optional function that\n   * takes a `handler` function as argument and removes it in case it was\n   * previously attached using `addHandler`. if addHandler returns signal to teardown when remove,\n   * removeHandler function will forward it.\n   * @param {function(...args: any): T} [selector] An optional function to\n   * post-process results. It takes the arguments from the event handler and\n   * should return a single value.\n   * @return {Observable<T>}\n   * @static true\n   * @name fromEventPattern\n   * @owner Observable\n   */\n  static create<T>(addHandler: (handler: Function) => any,\n                   removeHandler?: (handler: Function, signal?: any) => void,\n                   selector?: (...args: Array<any>) => T) {\n    return new FromEventPatternObservable(addHandler, removeHandler, selector);\n  }\n\n  constructor(private addHandler: (handler: Function) => any,\n              private removeHandler?: (handler: Function, signal?: any) => void,\n              private selector?: (...args: Array<any>) => T) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>) {\n    const removeHandler = this.removeHandler;\n\n    const handler = !!this.selector ? (...args: Array<any>) => {\n      this._callSelector(subscriber, args);\n    } : function(e: any) { subscriber.next(e); };\n\n    const retValue = this._callAddHandler(handler, subscriber);\n\n    if (!isFunction(removeHandler)) {\n      return;\n    }\n\n    subscriber.add(new Subscription(() => {\n      //TODO: determine whether or not to forward to error handler\n      removeHandler(handler, retValue) ;\n    }));\n  }\n\n  private _callSelector(subscriber: Subscriber<T>, args: Array<any>): void {\n    try {\n      const result: T = this.selector(...args);\n      subscriber.next(result);\n    }\n    catch (e) {\n      subscriber.error(e);\n    }\n  }\n\n  private _callAddHandler(handler: (e: any) => void, errorSubscriber: Subscriber<T>): any | null {\n    try {\n      return this.addHandler(handler) || null;\n    }\n    catch (e) {\n      errorSubscriber.error(e);\n    }\n  }\n}\n"]}