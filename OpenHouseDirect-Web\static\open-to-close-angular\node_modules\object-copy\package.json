{"_from": "object-copy@^0.1.0", "_id": "object-copy@0.1.0", "_inBundle": false, "_integrity": "sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ==", "_location": "/object-copy", "_phantomChildren": {"is-descriptor": "0.1.7"}, "_requested": {"type": "range", "registry": true, "raw": "object-copy@^0.1.0", "name": "object-copy", "escapedName": "object-copy", "rawSpec": "^0.1.0", "saveSpec": null, "fetchSpec": "^0.1.0"}, "_requiredBy": ["/static-extend"], "_resolved": "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz", "_shasum": "7e7d858b781bd7c991a41ba975ed3812754e998c", "_spec": "object-copy@^0.1.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\static-extend", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/object-copy/issues"}, "bundleDependencies": false, "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "deprecated": false, "description": "Copy static properties, prototype properties, and descriptors from one object to another.", "devDependencies": {"gulp-format-md": "*", "mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/object-copy", "keywords": ["copy", "object"], "license": "MIT", "main": "index.js", "name": "object-copy", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/object-copy.git"}, "scripts": {"test": "mocha"}, "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": []}, "reflinks": ["verb"]}, "version": "0.1.0"}