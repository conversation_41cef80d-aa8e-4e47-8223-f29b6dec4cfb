{"version": 3, "file": "cdk.js", "sources": ["../../src/cdk/index.ts", "../../src/cdk/public-api.ts", "../../src/cdk/version.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './version';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Version} from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nexport const VERSION = new Version('5.2.5');\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AEQA;;;AAGA,AAAO,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC;;;;;GDHxD,AAA0B;;;;;;;;GDJ1B,AAA6B;;"}