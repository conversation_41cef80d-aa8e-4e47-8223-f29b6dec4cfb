{"version": 3, "file": "windowToggle.js", "sourceRoot": "", "sources": ["../../../src/add/operator/windowToggle.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,6BAA6B,6BAA6B,CAAC,CAAA;AAE3D,uBAAU,CAAC,SAAS,CAAC,YAAY,GAAG,2BAAY,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { windowToggle } from '../../operator/windowToggle';\n\nObservable.prototype.windowToggle = windowToggle;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    windowToggle: typeof windowToggle;\n  }\n}"]}