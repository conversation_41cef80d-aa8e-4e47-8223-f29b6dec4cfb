{"version": 3, "file": "ka.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ka.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU;YACxC,MAAM,EAAE,MAAM;SACf;QACD,AADE;QAEF;YACE,UAAU,EAAE,SAAS,EAAE,OAAO;YAC9B,YAAY,EAAE,SAAS,EAAE,OAAO;SACjC;KACF;IACD;QACE;YACE,SAAS,EAAE,QAAQ,EAAE,MAAM;YAC3B,YAAY,EAAE,QAAQ,EAAE,MAAM;SAC/B;QACD,AADE;KAEH;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'შუაღამეს', 'შუადღ.', 'დილ.', 'ნაშუადღ.',\n      'საღ.', 'ღამ.'\n    ],\n    ,\n    [\n      'შუაღამეს', 'შუადღეს', 'დილით',\n      'ნაშუადღევს', 'საღამოს', 'ღამით'\n    ]\n  ],\n  [\n    [\n      'შუაღამე', 'შუადღე', 'დილა',\n      'ნაშუადღევი', 'საღამო', 'ღამე'\n    ],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['05:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '05:00']\n  ]\n];\n"]}