/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/cdk/platform"),require("rxjs/Subject"),require("rxjs/Observable"),require("rxjs/observable/of"),require("rxjs/observable/fromEvent"),require("rxjs/operators/auditTime"),require("rxjs/operators/filter"),require("rxjs/observable/merge")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/platform","rxjs/Subject","rxjs/Observable","rxjs/observable/of","rxjs/observable/fromEvent","rxjs/operators/auditTime","rxjs/operators/filter","rxjs/observable/merge"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.scrolling=e.ng.cdk.scrolling||{}),e.ng.core,e.ng.cdk.platform,e.Rx,e.Rx,e.Rx.Observable,e.Rx.Observable,e.Rx.operators,e.Rx.operators,e.Rx.Observable)}(this,function(e,t,r,o,n,i,s,l,c,u){"use strict";function a(e,t,r){return e||new d(t,r)}function p(e,t,r){return e||new b(t,r)}var d=function(){function e(e,t){this._ngZone=e,this._platform=t,this._scrolled=new o.Subject,this._globalSubscription=null,this._scrolledCount=0,this.scrollContainers=new Map}return e.prototype.register=function(e){var t=this,r=e.elementScrolled().subscribe(function(){return t._scrolled.next(e)});this.scrollContainers.set(e,r)},e.prototype.deregister=function(e){var t=this.scrollContainers.get(e);t&&(t.unsubscribe(),this.scrollContainers.delete(e))},e.prototype.scrolled=function(e){var t=this;return void 0===e&&(e=20),this._platform.isBrowser?n.Observable.create(function(r){t._globalSubscription||t._addGlobalListener();var o=e>0?t._scrolled.pipe(l.auditTime(e)).subscribe(r):t._scrolled.subscribe(r);return t._scrolledCount++,function(){o.unsubscribe(),--t._scrolledCount||t._removeGlobalListener()}}):i.of()},e.prototype.ngOnDestroy=function(){var e=this;this._removeGlobalListener(),this.scrollContainers.forEach(function(t,r){return e.deregister(r)})},e.prototype.ancestorScrolled=function(e,t){var r=this.getAncestorScrollContainers(e);return this.scrolled(t).pipe(c.filter(function(e){return!e||r.indexOf(e)>-1}))},e.prototype.getAncestorScrollContainers=function(e){var t=this,r=[];return this.scrollContainers.forEach(function(o,n){t._scrollableContainsElement(n,e)&&r.push(n)}),r},e.prototype._scrollableContainsElement=function(e,t){var r=t.nativeElement,o=e.getElementRef().nativeElement;do{if(r==o)return!0}while(r=r.parentElement);return!1},e.prototype._addGlobalListener=function(){var e=this;this._globalSubscription=this._ngZone.runOutsideAngular(function(){return s.fromEvent(window.document,"scroll").subscribe(function(){return e._scrolled.next()})})},e.prototype._removeGlobalListener=function(){this._globalSubscription&&(this._globalSubscription.unsubscribe(),this._globalSubscription=null)},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:t.NgZone},{type:r.Platform}]},e}(),f={provide:d,deps:[[new t.Optional,new t.SkipSelf,d],t.NgZone,r.Platform],useFactory:a},h=function(){function e(e,t,r){var n=this;this._elementRef=e,this._scroll=t,this._ngZone=r,this._elementScrolled=new o.Subject,this._scrollListener=function(e){return n._elementScrolled.next(e)}}return e.prototype.ngOnInit=function(){var e=this;this._ngZone.runOutsideAngular(function(){e.getElementRef().nativeElement.addEventListener("scroll",e._scrollListener)}),this._scroll.register(this)},e.prototype.ngOnDestroy=function(){this._scroll.deregister(this),this._scrollListener&&this.getElementRef().nativeElement.removeEventListener("scroll",this._scrollListener)},e.prototype.elementScrolled=function(){return this._elementScrolled.asObservable()},e.prototype.getElementRef=function(){return this._elementRef},e.decorators=[{type:t.Directive,args:[{selector:"[cdk-scrollable], [cdkScrollable]"}]}],e.ctorParameters=function(){return[{type:t.ElementRef},{type:d},{type:t.NgZone}]},e}(),b=function(){function e(e,t){var r=this;this._change=e.isBrowser?t.runOutsideAngular(function(){return u.merge(s.fromEvent(window,"resize"),s.fromEvent(window,"orientationchange"))}):i.of(),this._invalidateCache=this.change().subscribe(function(){return r._updateViewportSize()})}return e.prototype.ngOnDestroy=function(){this._invalidateCache.unsubscribe()},e.prototype.getViewportSize=function(){return this._viewportSize||this._updateViewportSize(),{width:this._viewportSize.width,height:this._viewportSize.height}},e.prototype.getViewportRect=function(){var e=this.getViewportScrollPosition(),t=this.getViewportSize(),r=t.width,o=t.height;return{top:e.top,left:e.left,bottom:e.top+o,right:e.left+r,height:o,width:r}},e.prototype.getViewportScrollPosition=function(){var e=document.documentElement.getBoundingClientRect();return{top:-e.top||document.body.scrollTop||window.scrollY||document.documentElement.scrollTop||0,left:-e.left||document.body.scrollLeft||window.scrollX||document.documentElement.scrollLeft||0}},e.prototype.change=function(e){return void 0===e&&(e=20),e>0?this._change.pipe(l.auditTime(e)):this._change},e.prototype._updateViewportSize=function(){this._viewportSize={width:window.innerWidth,height:window.innerHeight}},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:r.Platform},{type:t.NgZone}]},e}(),g={provide:b,deps:[[new t.Optional,new t.SkipSelf,b],r.Platform,t.NgZone],useFactory:p},_=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{imports:[r.PlatformModule],exports:[h],declarations:[h],providers:[f]}]}],e.ctorParameters=function(){return[]},e}();e.DEFAULT_SCROLL_TIME=20,e.ScrollDispatcher=d,e.SCROLL_DISPATCHER_PROVIDER_FACTORY=a,e.SCROLL_DISPATCHER_PROVIDER=f,e.CdkScrollable=h,e.DEFAULT_RESIZE_TIME=20,e.ViewportRuler=b,e.VIEWPORT_RULER_PROVIDER_FACTORY=p,e.VIEWPORT_RULER_PROVIDER=g,e.ScrollDispatchModule=_,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-scrolling.umd.min.js.map
