{"version": 3, "file": "cdk-accordion.umd.js", "sources": ["../../src/cdk/accordion/accordion-module.ts", "../../src/cdk/accordion/accordion-item.ts", "../../src/cdk/accordion/accordion.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {UNIQUE_SELECTION_DISPATCHER_PROVIDER} from '@angular/cdk/collections';\nimport {CdkAccordion} from './accordion';\nimport {CdkAccordionItem} from './accordion-item';\n\n@NgModule({\n  exports: [CdkAccordion, CdkAccordionItem],\n  declarations: [CdkAccordion, CdkAccordionItem],\n  providers: [UNIQUE_SELECTION_DISPATCHER_PROVIDER],\n})\nexport class CdkAccordionModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Output,\n  Directive,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Optional,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {CdkAccordion} from './accordion';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\n\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\n@Directive({\n  selector: 'cdk-accordion-item',\n  exportAs: 'cdkAccordionItem',\n})\nexport class CdkAccordionItem implements OnDestroy {\n  /** Event emitted every time the AccordionItem is closed. */\n  @Output() closed: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted every time the AccordionItem is opened. */\n  @Output() opened: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted when the AccordionItem is destroyed. */\n  @Output() destroyed: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Emits whenever the expanded state of the accordion changes.\n   * Primarily used to facilitate two-way binding.\n   * @docs-private\n   */\n  @Output() expandedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** The unique AccordionItem id. */\n  readonly id: string = `cdk-accordion-child-${nextId++}`;\n\n  /** Whether the AccordionItem is expanded. */\n  @Input()\n  get expanded(): any { return this._expanded; }\n  set expanded(expanded: any) {\n    expanded = coerceBooleanProperty(expanded);\n\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _expanded = false;\n\n  /** Whether the AccordionItem is disabled. */\n  @Input()\n  get disabled() { return this._disabled; }\n  set disabled(disabled: any) { this._disabled = coerceBooleanProperty(disabled); }\n  private _disabled: boolean = false;\n\n  /** Unregister function for _expansionDispatcher. */\n  private _removeUniqueSelectionListener: () => void = () => {};\n\n  constructor(@Optional() public accordion: CdkAccordion,\n              private _changeDetectorRef: ChangeDetectorRef,\n              protected _expansionDispatcher: UniqueSelectionDispatcher) {\n    this._removeUniqueSelectionListener =\n      _expansionDispatcher.listen((id: string, accordionId: string) => {\n        if (this.accordion && !this.accordion.multi &&\n            this.accordion.id === accordionId && this.id !== id) {\n          this.expanded = false;\n        }\n      });\n  }\n\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.destroyed.emit();\n    this._removeUniqueSelectionListener();\n  }\n\n  /** Toggles the expanded state of the accordion item. */\n  toggle(): void {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to false. */\n  close(): void {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to true. */\n  open(): void {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\n\n/** Used to generate unique ID for each accordion. */\nlet nextId = 0;\n\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\n@Directive({\n  selector: 'cdk-accordion, [cdkAccordion]',\n  exportAs: 'cdkAccordion',\n})\nexport class CdkAccordion {\n  /** A readonly id value to use for unique selection coordination. */\n  readonly id = `cdk-accordion-${nextId++}`;\n\n  /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n  @Input()\n  get multi(): boolean { return this._multi; }\n  set multi(multi: boolean) { this._multi = coerceBooleanProperty(multi); }\n  private _multi: boolean = false;\n}\n"], "names": ["UNIQUE_SELECTION_DISPATCHER_PROVIDER", "NgModule", "Input", "Output", "UniqueSelectionDispatcher", "ChangeDetectorRef", "Optional", "Directive", "coerceBooleanProperty", "EventEmitter", "nextId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AEYA,IAAIU,QAAM,GAAG,CAAC,CAAC;;;;;;;;;QAWf,IAAA,CAAA,EAAA,GAAgB,gBAAhB,GAAiCA,QAAM,EAAI,CAA3C;QAMA,IAAA,CAAA,MAAA,GAA4B,KAAK,CAAjC;;IAFA,MAAA,CAAA,cAAA,CAAM,YAAN,CAAA,SAAA,EAAA,OAAW,EAAX;;;;;QAAA,YAAA,EAAyB,OAAO,IAAI,CAAC,MAAM,CAAC,EAA5C;;;;;QACE,UAAU,KAAc,EAA1B,EAA8B,IAAI,CAAC,MAAM,GAAGF,2CAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;;;;;QAX3E,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,+BAA+B;oBACzC,QAAQ,EAAE,cAAc;iBACzB,EAAD,EAAA;;;;;QAMA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAGL,mBAAK,EAAR,EAAA;;IA1BA,OAAA,YAAA,CAAA;CAqBA,EAAA,CAAA,CAAA;;;;;;;;;;ADCA,IAAI,MAAM,GAAG,CAAC,CAAC;;;;;;IAmEb,SAAF,gBAAA,CAAiC,SAAjC,EACsB,kBADtB,EAEwB,oBAA+C,EAFvE;QAAE,IAAF,KAAA,GAAA,IAAA,CAUG;QAV8B,IAAjC,CAAA,SAA0C,GAAT,SAAS,CAA1C;QACsB,IAAtB,CAAA,kBAAwC,GAAlB,kBAAkB,CAAxC;QACwB,IAAxB,CAAA,oBAA4C,GAApB,oBAAoB,CAA2B;;;;QAzDvE,IAAA,CAAA,MAAA,GAAyC,IAAIO,0BAAY,EAAQ,CAAjE;;;;QAEA,IAAA,CAAA,MAAA,GAAyC,IAAIA,0BAAY,EAAQ,CAAjE;;;;QAEA,IAAA,CAAA,SAAA,GAA4C,IAAIA,0BAAY,EAAQ,CAApE;;;;;;QAOA,IAAA,CAAA,cAAA,GAAoD,IAAIA,0BAAY,EAAW,CAA/E;;;;QAGA,IAAA,CAAA,EAAA,GAAwB,sBAAxB,GAA+C,MAAM,EAAI,CAAzD;QA8BA,IAAA,CAAA,SAAA,GAAsB,KAAK,CAA3B;QAMA,IAAA,CAAA,SAAA,GAA+B,KAAK,CAApC;;;;QAGA,IAAA,CAAA,8BAAA,GAAuD,YAAvD,GAA+D,CAA/D;QAKI,IAAI,CAAC,8BAA8B;YACjC,oBAAoB,CAAC,MAAM,CAAC,UAAC,EAAU,EAAE,WAAmB,EAAlE;gBACQ,IAAI,KAAI,CAAC,SAAS,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,KAAK;oBACvC,KAAI,CAAC,SAAS,CAAC,EAAE,KAAK,WAAW,IAAI,KAAI,CAAC,EAAE,KAAK,EAAE,EAAE;oBACvD,KAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;iBACvB;aACF,CAAC,CAAC;KACN;IA/CH,MAAA,CAAA,cAAA,CAAM,gBAAN,CAAA,SAAA,EAAA,UAAc,EAAd;;;;;QAAA,YAAA,EAAwB,OAAO,IAAI,CAAC,SAAS,CAAC,EAA9C;;;;;QACE,UAAa,QAAa,EAA5B;YACI,QAAQ,GAAGD,2CAAqB,CAAC,QAAQ,CAAC,CAAC;;YAG3C,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;gBAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEnC,IAAI,QAAQ,EAAE;oBACZ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;;;;;oBAKnB,qBAAM,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;oBACjE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;iBACxD;qBAAM;oBACL,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;iBACpB;;;gBAID,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;aACxC;SACF;;;;IAKH,MAAA,CAAA,cAAA,CAAM,gBAAN,CAAA,SAAA,EAAA,UAAc,EAAd;;;;;QAAA,YAAA,EAAmB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAzC;;;;;QACE,UAAa,QAAa,EAA5B,EAAgC,IAAI,CAAC,SAAS,GAAGA,2CAAqB,CAAC,QAAQ,CAAC,CAAC,EAAE;;;;;;;;;IAmBjF,gBAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,YAAF;QACI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,8BAA8B,EAAE,CAAC;KACvC,CAAH;;;;;;IAGE,gBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;SAChC;KACF,CAAH;;;;;;IAGE,gBAAF,CAAA,SAAA,CAAA,KAAO;;;;IAAL,YAAF;QACI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;KACF,CAAH;;;;;;IAGE,gBAAF,CAAA,SAAA,CAAA,IAAM;;;;IAAJ,YAAF;QACI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;KACF,CAAH;;QAlGA,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,oBAAoB;oBAC9B,QAAQ,EAAE,kBAAkB;iBAC7B,EAAD,EAAA;;;;QAbA,EAAA,IAAA,EAAQ,YAAY,EAApB,UAAA,EAAA,CAAA,EAAA,IAAA,EAuEeD,sBAAQ,EAvEvB,EAAA,EAAA;QAHA,EAAA,IAAA,EAAED,+BAAiB,GAAnB;QAEA,EAAA,IAAA,EAAQD,kDAAyB,GAAjC;;;QAiBA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,oBAAM,EAAT,EAAA;QAEA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,oBAAM,EAAT,EAAA;QAEA,WAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,oBAAM,EAAT,EAAA;QAOA,gBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,oBAAM,EAAT,EAAA;QAMA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,mBAAK,EAAR,EAAA;QA8BA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,EAAA;;IAjFA,OAAA,gBAAA,CAAA;CAgCA,EAAA,CAAA,CAAA;;;;;;;ADxBA,IAAA,kBAAA,kBAAA,YAAA;;;;QAKA,EAAA,IAAA,EAACD,sBAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;oBACzC,YAAY,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;oBAC9C,SAAS,EAAE,CAACD,6DAAoC,CAAC;iBAClD,EAAD,EAAA;;;;IAjBA,OAAA,kBAAA,CAAA;CAkBA,EAAA,CAAA,CAAA;;;;;;;;"}