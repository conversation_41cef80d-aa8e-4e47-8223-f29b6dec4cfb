{"_from": "supports-color@^4.0.0", "_id": "supports-color@4.5.0", "_inBundle": false, "_integrity": "sha512-ycQR/UbvI9xIlEdQT1TQqwoXtEldExbCEAJgRo5YXlmSKjv6ThHnP9/vwGa1gr19Gfw+LkFd7KqYMhzrRC5JYw==", "_location": "/supports-color", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "supports-color@^4.0.0", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/chalk", "/webpack"], "_resolved": "https://registry.npmjs.org/supports-color/-/supports-color-4.5.0.tgz", "_shasum": "be7a0de484dec5c5cddf8b3d59125044912f635b", "_spec": "supports-color@^4.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "bundleDependencies": false, "dependencies": {"has-flag": "^2.0.0"}, "deprecated": false, "description": "Detect whether a terminal supports color", "devDependencies": {"ava": "*", "import-fresh": "^2.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "browser.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "license": "MIT", "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "xo && ava"}, "version": "4.5.0"}