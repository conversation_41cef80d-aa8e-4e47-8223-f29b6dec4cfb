/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/cdk/coercion"),require("rxjs/operators/take"),require("@angular/cdk/platform"),require("@angular/common"),require("rxjs/Subject"),require("rxjs/Subscription"),require("@angular/cdk/keycodes"),require("rxjs/operators/debounceTime"),require("rxjs/operators/filter"),require("rxjs/operators/map"),require("rxjs/operators/tap"),require("rxjs/observable/of")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/coercion","rxjs/operators/take","@angular/cdk/platform","@angular/common","rxjs/Subject","rxjs/Subscription","@angular/cdk/keycodes","rxjs/operators/debounceTime","rxjs/operators/filter","rxjs/operators/map","rxjs/operators/tap","rxjs/observable/of"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.a11y=e.ng.cdk.a11y||{}),e.ng.core,e.ng.cdk.coercion,e.Rx.operators,e.ng.cdk.platform,e.ng.common,e.Rx,e.Rx,e.ng.cdk.keycodes,e.Rx.operators,e.Rx.operators,e.Rx.operators,e.Rx.operators,e.Rx.Observable)}(this,function(e,t,n,r,i,o,s,c,a,u,l,d,h,p){"use strict";function f(e,t){function n(){this.constructor=e}D(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function _(e){try{return e.frameElement}catch(e){return null}}function m(e){return!!(e.offsetWidth||e.offsetHeight||"function"==typeof e.getClientRects&&e.getClientRects().length)}function b(e){var t=e.nodeName.toLowerCase();return"input"===t||"select"===t||"button"===t||"textarea"===t}function y(e){return g(e)&&"hidden"==e.type}function v(e){return I(e)&&e.hasAttribute("href")}function g(e){return"input"==e.nodeName.toLowerCase()}function I(e){return"a"==e.nodeName.toLowerCase()}function E(e){if(!e.hasAttribute("tabindex")||void 0===e.tabIndex)return!1;var t=e.getAttribute("tabindex");return"-32768"!=t&&!(!t||isNaN(parseInt(t,10)))}function A(e){if(!E(e))return null;var t=parseInt(e.getAttribute("tabindex")||"",10);return isNaN(t)?-1:t}function T(e){var t=e.nodeName.toLowerCase(),n="input"===t&&e.type;return"text"===n||"password"===n||"select"===t||"textarea"===t}function k(e){return!y(e)&&(b(e)||v(e)||e.hasAttribute("contenteditable")||E(e))}function C(e){return e.ownerDocument.defaultView||window}function O(e,t,n){var r=R(e,t);r.some(function(e){return e.trim()==n.trim()})||(r.push(n.trim()),e.setAttribute(t,r.join(K)))}function F(e,t,n){var r=R(e,t),i=r.filter(function(e){return e!=n.trim()});e.setAttribute(t,i.join(K))}function R(e,t){return(e.getAttribute(t)||"").match(/\S+/g)||[]}function x(e,t){return e||new V(t)}function w(e,t,n){return e||new Q(t,n)}function N(e,t,n){return e||new J(t,n)}function L(e){return 0===e.buttons}var D=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},M=function(){function e(e){this._platform=e}return e.prototype.isDisabled=function(e){return e.hasAttribute("disabled")},e.prototype.isVisible=function(e){return m(e)&&"visible"===getComputedStyle(e).visibility},e.prototype.isTabbable=function(e){if(!this._platform.isBrowser)return!1;var t=_(C(e));if(t){var n=t&&t.nodeName.toLowerCase();if(-1===A(t))return!1;if((this._platform.BLINK||this._platform.WEBKIT)&&"object"===n)return!1;if((this._platform.BLINK||this._platform.WEBKIT)&&!this.isVisible(t))return!1}var r=e.nodeName.toLowerCase(),i=A(e);if(e.hasAttribute("contenteditable"))return-1!==i;if("iframe"===r)return!1;if("audio"===r){if(!e.hasAttribute("controls"))return!1;if(this._platform.BLINK)return!0}if("video"===r){if(!e.hasAttribute("controls")&&this._platform.TRIDENT)return!1;if(this._platform.BLINK||this._platform.FIREFOX)return!0}return("object"!==r||!this._platform.BLINK&&!this._platform.WEBKIT)&&(!(this._platform.WEBKIT&&this._platform.IOS&&!T(e))&&e.tabIndex>=0)},e.prototype.isFocusable=function(e){return k(e)&&!this.isDisabled(e)&&this.isVisible(e)},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:i.Platform}]},e}(),j=function(){function e(e,t,n,r,i){void 0===i&&(i=!1),this._element=e,this._checker=t,this._ngZone=n,this._document=r,this._enabled=!0,i||this.attachAnchors()}return Object.defineProperty(e.prototype,"enabled",{get:function(){return this._enabled},set:function(e){this._enabled=e,this._startAnchor&&this._endAnchor&&(this._startAnchor.tabIndex=this._endAnchor.tabIndex=this._enabled?0:-1)},enumerable:!0,configurable:!0}),e.prototype.destroy=function(){this._startAnchor&&this._startAnchor.parentNode&&this._startAnchor.parentNode.removeChild(this._startAnchor),this._endAnchor&&this._endAnchor.parentNode&&this._endAnchor.parentNode.removeChild(this._endAnchor),this._startAnchor=this._endAnchor=null},e.prototype.attachAnchors=function(){var e=this;this._startAnchor||(this._startAnchor=this._createAnchor()),this._endAnchor||(this._endAnchor=this._createAnchor()),this._ngZone.runOutsideAngular(function(){e._startAnchor.addEventListener("focus",function(){e.focusLastTabbableElement()}),e._endAnchor.addEventListener("focus",function(){e.focusFirstTabbableElement()}),e._element.parentNode&&(e._element.parentNode.insertBefore(e._startAnchor,e._element),e._element.parentNode.insertBefore(e._endAnchor,e._element.nextSibling))})},e.prototype.focusInitialElementWhenReady=function(){var e=this;return new Promise(function(t){e._executeOnStable(function(){return t(e.focusInitialElement())})})},e.prototype.focusFirstTabbableElementWhenReady=function(){var e=this;return new Promise(function(t){e._executeOnStable(function(){return t(e.focusFirstTabbableElement())})})},e.prototype.focusLastTabbableElementWhenReady=function(){var e=this;return new Promise(function(t){e._executeOnStable(function(){return t(e.focusLastTabbableElement())})})},e.prototype._getRegionBoundary=function(e){for(var t=this._element.querySelectorAll("[cdk-focus-region-"+e+"], [cdkFocusRegion"+e+"], [cdk-focus-"+e+"]"),n=0;n<t.length;n++)t[n].hasAttribute("cdk-focus-"+e)?console.warn("Found use of deprecated attribute 'cdk-focus-"+e+"', use 'cdkFocusRegion"+e+"' instead.",t[n]):t[n].hasAttribute("cdk-focus-region-"+e)&&console.warn("Found use of deprecated attribute 'cdk-focus-region-"+e+"', use 'cdkFocusRegion"+e+"' instead.",t[n]);return"start"==e?t.length?t[0]:this._getFirstTabbableElement(this._element):t.length?t[t.length-1]:this._getLastTabbableElement(this._element)},e.prototype.focusInitialElement=function(){var e=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");return this._element.hasAttribute("cdk-focus-initial")&&console.warn("Found use of deprecated attribute 'cdk-focus-initial', use 'cdkFocusInitial' instead.",this._element),e?(e.focus(),!0):this.focusFirstTabbableElement()},e.prototype.focusFirstTabbableElement=function(){var e=this._getRegionBoundary("start");return e&&e.focus(),!!e},e.prototype.focusLastTabbableElement=function(){var e=this._getRegionBoundary("end");return e&&e.focus(),!!e},e.prototype._getFirstTabbableElement=function(e){if(this._checker.isFocusable(e)&&this._checker.isTabbable(e))return e;for(var t=e.children||e.childNodes,n=0;n<t.length;n++){var r=t[n].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(t[n]):null;if(r)return r}return null},e.prototype._getLastTabbableElement=function(e){if(this._checker.isFocusable(e)&&this._checker.isTabbable(e))return e;for(var t=e.children||e.childNodes,n=t.length-1;n>=0;n--){var r=t[n].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(t[n]):null;if(r)return r}return null},e.prototype._createAnchor=function(){var e=this._document.createElement("div");return e.tabIndex=this._enabled?0:-1,e.classList.add("cdk-visually-hidden"),e.classList.add("cdk-focus-trap-anchor"),e},e.prototype._executeOnStable=function(e){this._ngZone.isStable?e():this._ngZone.onStable.asObservable().pipe(r.take(1)).subscribe(e)},e}(),S=function(){function e(e,t,n){this._checker=e,this._ngZone=t,this._document=n}return e.prototype.create=function(e,t){return void 0===t&&(t=!1),new j(e,this._checker,this._ngZone,this._document,t)},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:M},{type:t.NgZone},{type:void 0,decorators:[{type:t.Inject,args:[o.DOCUMENT]}]}]},e}(),P=function(){function e(e,t){this._elementRef=e,this._focusTrapFactory=t,this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0)}return Object.defineProperty(e.prototype,"disabled",{get:function(){return!this.focusTrap.enabled},set:function(e){this.focusTrap.enabled=!n.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),e.prototype.ngOnDestroy=function(){this.focusTrap.destroy()},e.prototype.ngAfterContentInit=function(){this.focusTrap.attachAnchors()},e.decorators=[{type:t.Directive,args:[{selector:"cdk-focus-trap"}]}],e.ctorParameters=function(){return[{type:t.ElementRef},{type:S}]},e.propDecorators={disabled:[{type:t.Input}]},e}(),B=function(){function e(e,t,n){this._elementRef=e,this._focusTrapFactory=t,this._previouslyFocusedElement=null,this._document=n,this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0)}return Object.defineProperty(e.prototype,"enabled",{get:function(){return this.focusTrap.enabled},set:function(e){this.focusTrap.enabled=n.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"autoCapture",{get:function(){return this._autoCapture},set:function(e){this._autoCapture=n.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),e.prototype.ngOnDestroy=function(){this.focusTrap.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)},e.prototype.ngAfterContentInit=function(){this.focusTrap.attachAnchors(),this.autoCapture&&(this._previouslyFocusedElement=this._document.activeElement,this.focusTrap.focusInitialElementWhenReady())},e.decorators=[{type:t.Directive,args:[{selector:"[cdkTrapFocus]",exportAs:"cdkTrapFocus"}]}],e.ctorParameters=function(){return[{type:t.ElementRef},{type:S},{type:void 0,decorators:[{type:t.Inject,args:[o.DOCUMENT]}]}]},e.propDecorators={enabled:[{type:t.Input,args:["cdkTrapFocus"]}],autoCapture:[{type:t.Input,args:["cdkTrapFocusAutoCapture"]}]},e}(),K=" ",U=0,W=new Map,q=null,V=function(){function e(e){this._document=e}return e.prototype.describe=function(e,t){this._canBeDescribed(e,t)&&(W.has(t)||this._createMessageElement(t),this._isElementDescribedByMessage(e,t)||this._addMessageReference(e,t))},e.prototype.removeDescription=function(e,t){if(this._canBeDescribed(e,t)){this._isElementDescribedByMessage(e,t)&&this._removeMessageReference(e,t);var n=W.get(t);n&&0===n.referenceCount&&this._deleteMessageElement(t),q&&0===q.childNodes.length&&this._deleteMessagesContainer()}},e.prototype.ngOnDestroy=function(){for(var e=this._document.querySelectorAll("[cdk-describedby-host]"),t=0;t<e.length;t++)this._removeCdkDescribedByReferenceIds(e[t]),e[t].removeAttribute("cdk-describedby-host");q&&this._deleteMessagesContainer(),W.clear()},e.prototype._createMessageElement=function(e){var t=this._document.createElement("div");t.setAttribute("id","cdk-describedby-message-"+U++),t.appendChild(this._document.createTextNode(e)),q||this._createMessagesContainer(),q.appendChild(t),W.set(e,{messageElement:t,referenceCount:0})},e.prototype._deleteMessageElement=function(e){var t=W.get(e),n=t&&t.messageElement;q&&n&&q.removeChild(n),W.delete(e)},e.prototype._createMessagesContainer=function(){q=this._document.createElement("div"),q.setAttribute("id","cdk-describedby-message-container"),q.setAttribute("aria-hidden","true"),q.style.display="none",this._document.body.appendChild(q)},e.prototype._deleteMessagesContainer=function(){q&&q.parentNode&&(q.parentNode.removeChild(q),q=null)},e.prototype._removeCdkDescribedByReferenceIds=function(e){var t=R(e,"aria-describedby").filter(function(e){return 0!=e.indexOf("cdk-describedby-message")});e.setAttribute("aria-describedby",t.join(" "))},e.prototype._addMessageReference=function(e,t){var n=W.get(t);O(e,"aria-describedby",n.messageElement.id),e.setAttribute("cdk-describedby-host",""),n.referenceCount++},e.prototype._removeMessageReference=function(e,t){var n=W.get(t);n.referenceCount--,F(e,"aria-describedby",n.messageElement.id),e.removeAttribute("cdk-describedby-host")},e.prototype._isElementDescribedByMessage=function(e,t){var n=R(e,"aria-describedby"),r=W.get(t),i=r&&r.messageElement.id;return!!i&&-1!=n.indexOf(i)},e.prototype._canBeDescribed=function(e,t){return e.nodeType===this._document.ELEMENT_NODE&&null!=t&&!!(""+t).trim()},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:void 0,decorators:[{type:t.Inject,args:[o.DOCUMENT]}]}]},e}(),Z={provide:V,deps:[[new t.Optional,new t.SkipSelf,V],o.DOCUMENT],useFactory:x},G=function(){function e(e){var t=this;this._items=e,this._activeItemIndex=-1,this._wrap=!1,this._letterKeyStream=new s.Subject,this._typeaheadSubscription=c.Subscription.EMPTY,this._vertical=!0,this._skipPredicateFn=function(e){return e.disabled},this._pressedLetters=[],this.tabOut=new s.Subject,this.change=new s.Subject,e.changes.subscribe(function(e){if(t._activeItem){var n=e.toArray(),r=n.indexOf(t._activeItem);r>-1&&r!==t._activeItemIndex&&(t._activeItemIndex=r)}})}return e.prototype.skipPredicate=function(e){return this._skipPredicateFn=e,this},e.prototype.withWrap=function(){return this._wrap=!0,this},e.prototype.withVerticalOrientation=function(e){return void 0===e&&(e=!0),this._vertical=e,this},e.prototype.withHorizontalOrientation=function(e){return this._horizontal=e,this},e.prototype.withTypeAhead=function(e){var t=this;if(void 0===e&&(e=200),this._items.length&&this._items.some(function(e){return"function"!=typeof e.getLabel}))throw Error("ListKeyManager items in typeahead mode must implement the `getLabel` method.");return this._typeaheadSubscription.unsubscribe(),this._typeaheadSubscription=this._letterKeyStream.pipe(h.tap(function(e){return t._pressedLetters.push(e)}),u.debounceTime(e),l.filter(function(){return t._pressedLetters.length>0}),d.map(function(){return t._pressedLetters.join("")})).subscribe(function(e){for(var n=t._items.toArray(),r=1;r<n.length+1;r++){var i=(t._activeItemIndex+r)%n.length,o=n[i];if(!t._skipPredicateFn(o)&&0===o.getLabel().toUpperCase().trim().indexOf(e)){t.setActiveItem(i);break}}t._pressedLetters=[]}),this},e.prototype.setActiveItem=function(e){var t=this._activeItemIndex;this._activeItemIndex=e,this._activeItem=this._items.toArray()[e],this._activeItemIndex!==t&&this.change.next(e)},e.prototype.onKeydown=function(e){var t=e.keyCode;switch(t){case a.TAB:return void this.tabOut.next();case a.DOWN_ARROW:if(this._vertical){this.setNextItemActive();break}return;case a.UP_ARROW:if(this._vertical){this.setPreviousItemActive();break}return;case a.RIGHT_ARROW:if("ltr"===this._horizontal){this.setNextItemActive();break}if("rtl"===this._horizontal){this.setPreviousItemActive();break}return;case a.LEFT_ARROW:if("ltr"===this._horizontal){this.setPreviousItemActive();break}if("rtl"===this._horizontal){this.setNextItemActive();break}return;default:return void(e.key&&1===e.key.length?this._letterKeyStream.next(e.key.toLocaleUpperCase()):(t>=a.A&&t<=a.Z||t>=a.ZERO&&t<=a.NINE)&&this._letterKeyStream.next(String.fromCharCode(t)))}this._pressedLetters=[],e.preventDefault()},Object.defineProperty(e.prototype,"activeItemIndex",{get:function(){return this._activeItemIndex},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"activeItem",{get:function(){return this._activeItem},enumerable:!0,configurable:!0}),e.prototype.setFirstItemActive=function(){this._setActiveItemByIndex(0,1)},e.prototype.setLastItemActive=function(){this._setActiveItemByIndex(this._items.length-1,-1)},e.prototype.setNextItemActive=function(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)},e.prototype.setPreviousItemActive=function(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)},e.prototype.updateActiveItemIndex=function(e){this._activeItemIndex=e},e.prototype._setActiveItemByDelta=function(e,t){void 0===t&&(t=this._items.toArray()),this._wrap?this._setActiveInWrapMode(e,t):this._setActiveInDefaultMode(e,t)},e.prototype._setActiveInWrapMode=function(e,t){for(var n=1;n<=t.length;n++){var r=(this._activeItemIndex+e*n+t.length)%t.length,i=t[r];if(!this._skipPredicateFn(i))return void this.setActiveItem(r)}},e.prototype._setActiveInDefaultMode=function(e,t){this._setActiveItemByIndex(this._activeItemIndex+e,e,t)},e.prototype._setActiveItemByIndex=function(e,t,n){if(void 0===n&&(n=this._items.toArray()),n[e]){for(;this._skipPredicateFn(n[e]);)if(e+=t,!n[e])return;this.setActiveItem(e)}},e}(),z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return f(t,e),t.prototype.setActiveItem=function(t){this.activeItem&&this.activeItem.setInactiveStyles(),e.prototype.setActiveItem.call(this,t),this.activeItem&&this.activeItem.setActiveStyles()},t}(G),Y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._origin="program",t}return f(t,e),t.prototype.setFocusOrigin=function(e){return this._origin=e,this},t.prototype.setActiveItem=function(t){e.prototype.setActiveItem.call(this,t),this.activeItem&&this.activeItem.focus(this._origin)},t}(G),H=new t.InjectionToken("liveAnnouncerElement"),Q=function(){function e(e,t){this._document=t,this._liveElement=e||this._createLiveElement()}return e.prototype.announce=function(e,t){var n=this;return void 0===t&&(t="polite"),this._liveElement.textContent="",this._liveElement.setAttribute("aria-live",t),new Promise(function(t){setTimeout(function(){n._liveElement.textContent=e,t()},100)})},e.prototype.ngOnDestroy=function(){this._liveElement&&this._liveElement.parentNode&&this._liveElement.parentNode.removeChild(this._liveElement)},e.prototype._createLiveElement=function(){var e=this._document.createElement("div");return e.classList.add("cdk-visually-hidden"),e.setAttribute("aria-atomic","true"),e.setAttribute("aria-live","polite"),this._document.body.appendChild(e),e},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:void 0,decorators:[{type:t.Optional},{type:t.Inject,args:[H]}]},{type:void 0,decorators:[{type:t.Inject,args:[o.DOCUMENT]}]}]},e}(),X={provide:Q,deps:[[new t.Optional,new t.SkipSelf,Q],[new t.Optional,new t.Inject(H)],o.DOCUMENT],useFactory:w},J=function(){function e(e,t){this._ngZone=e,this._platform=t,this._origin=null,this._windowFocused=!1,this._elementInfo=new Map,this._unregisterGlobalListeners=function(){},this._monitoredElementCount=0}return e.prototype.monitor=function(e,n,r){var i=this;if(n instanceof t.Renderer2||(r=n),r=!!r,!this._platform.isBrowser)return p.of(null);if(this._elementInfo.has(e)){var o=this._elementInfo.get(e);return o.checkChildren=r,o.subject.asObservable()}var c={unlisten:function(){},checkChildren:r,subject:new s.Subject};this._elementInfo.set(e,c),this._incrementMonitoredElementCount();var a=function(t){return i._onFocus(t,e)},u=function(t){return i._onBlur(t,e)};return this._ngZone.runOutsideAngular(function(){e.addEventListener("focus",a,!0),e.addEventListener("blur",u,!0)}),c.unlisten=function(){e.removeEventListener("focus",a,!0),e.removeEventListener("blur",u,!0)},c.subject.asObservable()},e.prototype.stopMonitoring=function(e){var t=this._elementInfo.get(e);t&&(t.unlisten(),t.subject.complete(),this._setClasses(e),this._elementInfo.delete(e),this._decrementMonitoredElementCount())},e.prototype.focusVia=function(e,t){this._setOriginForCurrentEventQueue(t),e.focus()},e.prototype.ngOnDestroy=function(){var e=this;this._elementInfo.forEach(function(t,n){return e.stopMonitoring(n)})},e.prototype._registerGlobalListeners=function(){var e=this;if(this._platform.isBrowser){var t=function(){e._lastTouchTarget=null,e._setOriginForCurrentEventQueue("keyboard")},n=function(){e._lastTouchTarget||e._setOriginForCurrentEventQueue("mouse")},r=function(t){null!=e._touchTimeoutId&&clearTimeout(e._touchTimeoutId),e._lastTouchTarget=t.target,e._touchTimeoutId=setTimeout(function(){return e._lastTouchTarget=null},650)},o=function(){e._windowFocused=!0,e._windowFocusTimeoutId=setTimeout(function(){return e._windowFocused=!1},0)};this._ngZone.runOutsideAngular(function(){document.addEventListener("keydown",t,!0),document.addEventListener("mousedown",n,!0),document.addEventListener("touchstart",r,!i.supportsPassiveEventListeners()||{passive:!0,capture:!0}),window.addEventListener("focus",o)}),this._unregisterGlobalListeners=function(){document.removeEventListener("keydown",t,!0),document.removeEventListener("mousedown",n,!0),document.removeEventListener("touchstart",r,!i.supportsPassiveEventListeners()||{passive:!0,capture:!0}),window.removeEventListener("focus",o),clearTimeout(e._windowFocusTimeoutId),clearTimeout(e._touchTimeoutId),clearTimeout(e._originTimeoutId)}}},e.prototype._toggleClass=function(e,t,n){n?e.classList.add(t):e.classList.remove(t)},e.prototype._setClasses=function(e,t){this._elementInfo.get(e)&&(this._toggleClass(e,"cdk-focused",!!t),this._toggleClass(e,"cdk-touch-focused","touch"===t),this._toggleClass(e,"cdk-keyboard-focused","keyboard"===t),this._toggleClass(e,"cdk-mouse-focused","mouse"===t),this._toggleClass(e,"cdk-program-focused","program"===t))},e.prototype._setOriginForCurrentEventQueue=function(e){var t=this;this._origin=e,this._originTimeoutId=setTimeout(function(){return t._origin=null},0)},e.prototype._wasCausedByTouch=function(e){var t=e.target;return this._lastTouchTarget instanceof Node&&t instanceof Node&&(t===this._lastTouchTarget||t.contains(this._lastTouchTarget))},e.prototype._onFocus=function(e,t){var n=this._elementInfo.get(t);n&&(n.checkChildren||t===e.target)&&(this._origin||(this._windowFocused&&this._lastFocusOrigin?this._origin=this._lastFocusOrigin:this._wasCausedByTouch(e)?this._origin="touch":this._origin="program"),this._setClasses(t,this._origin),n.subject.next(this._origin),this._lastFocusOrigin=this._origin,this._origin=null)},e.prototype._onBlur=function(e,t){var n=this._elementInfo.get(t);!n||n.checkChildren&&e.relatedTarget instanceof Node&&t.contains(e.relatedTarget)||(this._setClasses(t),n.subject.next(null))},e.prototype._incrementMonitoredElementCount=function(){1==++this._monitoredElementCount&&this._registerGlobalListeners()},e.prototype._decrementMonitoredElementCount=function(){--this._monitoredElementCount||(this._unregisterGlobalListeners(),this._unregisterGlobalListeners=function(){})},e.decorators=[{type:t.Injectable}],e.ctorParameters=function(){return[{type:t.NgZone},{type:i.Platform}]},e}(),$=function(){function e(e,n){var r=this;this._elementRef=e,this._focusMonitor=n,this.cdkFocusChange=new t.EventEmitter,this._monitorSubscription=this._focusMonitor.monitor(this._elementRef.nativeElement,this._elementRef.nativeElement.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(function(e){return r.cdkFocusChange.emit(e)})}return e.prototype.ngOnDestroy=function(){this._focusMonitor.stopMonitoring(this._elementRef.nativeElement),this._monitorSubscription.unsubscribe()},e.decorators=[{type:t.Directive,args:[{selector:"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]"}]}],e.ctorParameters=function(){return[{type:t.ElementRef},{type:J}]},e.propDecorators={cdkFocusChange:[{type:t.Output}]},e}(),ee={provide:J,deps:[[new t.Optional,new t.SkipSelf,J],t.NgZone,i.Platform],useFactory:N},te=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{imports:[o.CommonModule,i.PlatformModule],declarations:[B,P,$],exports:[B,P,$],providers:[M,S,V,X,Z,ee]}]}],e.ctorParameters=function(){return[]},e}();e.FocusTrapDirective=B,e.MESSAGES_CONTAINER_ID="cdk-describedby-message-container",e.CDK_DESCRIBEDBY_ID_PREFIX="cdk-describedby-message",e.CDK_DESCRIBEDBY_HOST_ATTRIBUTE="cdk-describedby-host",e.AriaDescriber=V,e.ARIA_DESCRIBER_PROVIDER_FACTORY=x,e.ARIA_DESCRIBER_PROVIDER=Z,e.ActiveDescendantKeyManager=z,e.FocusKeyManager=Y,e.ListKeyManager=G,e.FocusTrap=j,e.FocusTrapFactory=S,e.FocusTrapDeprecatedDirective=P,e.CdkTrapFocus=B,e.InteractivityChecker=M,e.LIVE_ANNOUNCER_ELEMENT_TOKEN=H,e.LiveAnnouncer=Q,e.LIVE_ANNOUNCER_PROVIDER_FACTORY=w,e.LIVE_ANNOUNCER_PROVIDER=X,e.TOUCH_BUFFER_MS=650,e.FocusMonitor=J,e.CdkMonitorFocus=$,e.FOCUS_MONITOR_PROVIDER_FACTORY=N,e.FOCUS_MONITOR_PROVIDER=ee,e.isFakeMousedownFromScreenReader=L,e.A11yModule=te,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-a11y.umd.min.js.map
