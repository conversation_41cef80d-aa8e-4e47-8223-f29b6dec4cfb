{"version": 3, "file": "bufferWhen.js", "sourceRoot": "", "sources": ["../../src/operators/bufferWhen.ts"], "names": [], "mappings": ";;;;;;AAGA,6BAA6B,iBAAiB,CAAC,CAAA;AAC/C,yBAAyB,kBAAkB,CAAC,CAAA;AAC5C,4BAA4B,qBAAqB,CAAC,CAAA;AAClD,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAG9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,oBAA8B,eAAsC;IAClE,MAAM,CAAC,UAAU,MAAqB;QACpC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC;AACJ,CAAC;AAJe,kBAAU,aAIzB,CAAA;AAED;IAEE,4BAAoB,eAAsC;QAAtC,oBAAe,GAAf,eAAe,CAAuB;IAC1D,CAAC;IAED,iCAAI,GAAJ,UAAK,UAA2B,EAAE,MAAW;QAC3C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACtF,CAAC;IACH,yBAAC;AAAD,CAAC,AARD,IAQC;AAED;;;;GAIG;AACH;IAAsC,wCAAuB;IAK3D,8BAAY,WAA4B,EAAU,eAAsC;QACtF,kBAAM,WAAW,CAAC,CAAC;QAD6B,oBAAe,GAAf,eAAe,CAAuB;QAHhF,gBAAW,GAAY,KAAK,CAAC;QAKnC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAES,oCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAES,wCAAS,GAAnB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;QACD,gBAAK,CAAC,SAAS,WAAE,CAAC;IACpB,CAAC;IAED,oCAAoC,CAAC,2CAAY,GAAZ;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,yCAAU,GAAV,UAAW,UAAa,EAAE,UAAe,EAC9B,UAAkB,EAAE,UAAkB,EACtC,QAAiC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,6CAAc,GAAd;QACE,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,yCAAU,GAAV;QAEQ,kDAAmB,CAAU;QAEnC,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YACjC,mBAAmB,CAAC,WAAW,EAAE,CAAC;QACpC,CAAC;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAM,eAAe,GAAG,mBAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;QAEzD,EAAE,CAAC,CAAC,eAAe,KAAK,yBAAW,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,mBAAmB,GAAG,IAAI,2BAAY,EAAE,CAAC;YACzC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;YAC/C,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,mBAAmB,CAAC,GAAG,CAAC,qCAAiB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC;IACH,CAAC;IACH,2BAAC;AAAD,CAAC,AAtED,CAAsC,iCAAe,GAsEpD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { tryCatch } from '../util/tryCatch';\nimport { errorObject } from '../util/errorObject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OperatorFunction } from '../interfaces';\n\n/**\n * Buffers the source Observable values, using a factory function of closing\n * Observables to determine when to close, emit, and reset the buffer.\n *\n * <span class=\"informal\">Collects values from the past as an array. When it\n * starts collecting values, it calls a function that returns an Observable that\n * tells when to close the buffer and restart collecting.</span>\n *\n * <img src=\"./img/bufferWhen.png\" width=\"100%\">\n *\n * Opens a buffer immediately, then closes the buffer when the observable\n * returned by calling `closingSelector` function emits a value. When it closes\n * the buffer, it immediately opens a new buffer and repeats the process.\n *\n * @example <caption>Emit an array of the last clicks every [1-5] random seconds</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var buffered = clicks.bufferWhen(() =>\n *   Rx.Observable.interval(1000 + Math.random() * 4000)\n * );\n * buffered.subscribe(x => console.log(x));\n *\n * @see {@link buffer}\n * @see {@link bufferCount}\n * @see {@link bufferTime}\n * @see {@link bufferToggle}\n * @see {@link windowWhen}\n *\n * @param {function(): Observable} closingSelector A function that takes no\n * arguments and returns an Observable that signals buffer closure.\n * @return {Observable<T[]>} An observable of arrays of buffered values.\n * @method bufferWhen\n * @owner Observable\n */\nexport function bufferWhen<T>(closingSelector: () => Observable<any>): OperatorFunction<T, T[]> {\n  return function (source: Observable<T>) {\n    return source.lift(new BufferWhenOperator(closingSelector));\n  };\n}\n\nclass BufferWhenOperator<T> implements Operator<T, T[]> {\n\n  constructor(private closingSelector: () => Observable<any>) {\n  }\n\n  call(subscriber: Subscriber<T[]>, source: any): any {\n    return source.subscribe(new BufferWhenSubscriber(subscriber, this.closingSelector));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass BufferWhenSubscriber<T> extends OuterSubscriber<T, any> {\n  private buffer: T[];\n  private subscribing: boolean = false;\n  private closingSubscription: Subscription;\n\n  constructor(destination: Subscriber<T[]>, private closingSelector: () => Observable<any>) {\n    super(destination);\n    this.openBuffer();\n  }\n\n  protected _next(value: T) {\n    this.buffer.push(value);\n  }\n\n  protected _complete() {\n    const buffer = this.buffer;\n    if (buffer) {\n      this.destination.next(buffer);\n    }\n    super._complete();\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    this.buffer = null;\n    this.subscribing = false;\n  }\n\n  notifyNext(outerValue: T, innerValue: any,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, any>): void {\n    this.openBuffer();\n  }\n\n  notifyComplete(): void {\n    if (this.subscribing) {\n      this.complete();\n    } else {\n      this.openBuffer();\n    }\n  }\n\n  openBuffer() {\n\n    let { closingSubscription } = this;\n\n    if (closingSubscription) {\n      this.remove(closingSubscription);\n      closingSubscription.unsubscribe();\n    }\n\n    const buffer = this.buffer;\n    if (this.buffer) {\n      this.destination.next(buffer);\n    }\n\n    this.buffer = [];\n\n    const closingNotifier = tryCatch(this.closingSelector)();\n\n    if (closingNotifier === errorObject) {\n      this.error(errorObject.e);\n    } else {\n      closingSubscription = new Subscription();\n      this.closingSubscription = closingSubscription;\n      this.add(closingSubscription);\n      this.subscribing = true;\n      closingSubscription.add(subscribeToResult(this, closingNotifier));\n      this.subscribing = false;\n    }\n  }\n}\n"]}