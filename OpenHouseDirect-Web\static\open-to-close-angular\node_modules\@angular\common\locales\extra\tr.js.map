{"version": 3, "file": "tr.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/tr.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC;QACrF;YACE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW;YAC5E,OAAO,EAAE,MAAM;SAChB;KACF;IACD;QACE;YACE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW;YAC5E,OAAO,EAAE,MAAM;SAChB;QACD,AADE;KAEH;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC3D;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['gece', 'ö', 'sabah', 'öğleden önce', 'öğleden sonra', 'akşamüstü', 'akşam', 'gece'],\n    [\n      'gece yarısı', 'öğle', 'sabah', 'öğleden önce', 'öğleden sonra', 'ak<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      'akşam', 'gece'\n    ],\n  ],\n  [\n    [\n      'gece yarısı', 'öğle', 'sabah', 'öğleden önce', 'öğleden sonra', 'ak<PERSON><PERSON><PERSON>st<PERSON>',\n      'akşam', 'gece'\n    ],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '11:00'], ['11:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '19:00'], ['19:00', '21:00'], ['21:00', '06:00']\n  ]\n];\n"]}