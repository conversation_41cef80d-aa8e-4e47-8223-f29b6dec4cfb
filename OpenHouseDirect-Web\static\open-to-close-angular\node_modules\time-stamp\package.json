{"_from": "time-stamp@^2.0.0", "_id": "time-stamp@2.2.0", "_inBundle": false, "_integrity": "sha512-zxke8goJQpBeEgD82CXABeMh0LSJcj7CXEd0OHOg45HgcofF7pxNwZm9+RknpxpDhwN4gFpySkApKfFYfRQnUA==", "_location": "/time-stamp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "time-stamp@^2.0.0", "name": "time-stamp", "escapedName": "time-stamp", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/webpack-dev-middleware"], "_resolved": "https://registry.npmjs.org/time-stamp/-/time-stamp-2.2.0.tgz", "_shasum": "917e0a66905688790ec7bbbde04046259af83f57", "_spec": "time-stamp@^2.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\webpack-dev-middleware", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/time-stamp/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://evocateur.org"}, {"name": "<PERSON>", "url": "https://github.com/mendenhallmagic"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>i", "url": "https://leesei.github.io"}], "deprecated": false, "description": "Get a formatted timestamp.", "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^5.2.0", "pad-left": "^2.1.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/jonschlinkert/time-stamp", "keywords": ["console", "date", "format", "formatting", "log", "pretty", "stamp", "terminal", "time", "time-stamp"], "license": "MIT", "main": "index.js", "name": "time-stamp", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/time-stamp.git"}, "scripts": {"test": "mocha"}, "types": "index.d.ts", "verb": {"run": true, "toc": true, "layout": "default", "tasks": ["readme"], "helpers": ["./helpers.js"], "plugins": ["gulp-format-md"], "related": {"list": ["days", "iso-week", "month", "months", "o-clock", "seconds", "week", "weekday", "year"]}, "lint": {"reflinks": true}}, "version": "2.2.0"}