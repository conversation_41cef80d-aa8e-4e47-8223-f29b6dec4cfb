{"version": 3, "file": "cdk-keycodes.umd.min.js", "sources": ["../../src/cdk/keycodes/keycodes.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport const UP_ARROW = 38;\nexport const DOWN_ARROW = 40;\nexport const RIGHT_ARROW = 39;\nexport const LEFT_ARROW = 37;\nexport const PAGE_UP = 33;\nexport const PAGE_DOWN = 34;\nexport const HOME = 36;\nexport const END = 35;\nexport const ENTER = 13;\nexport const SPACE = 32;\nexport const TAB = 9;\nexport const ESCAPE = 27;\nexport const BACKSPACE = 8;\nexport const DELETE = 46;\nexport const A = 65;\nexport const Z = 90;\nexport const ZERO = 48;\nexport const NINE = 57;\nexport const COMMA = 188;\n"], "names": [], "mappings": ";;;;;;;sQAQwB,gBACE,iBACC,gBACD,aACH,eACE,UACL,SACD,WACE,WACA,SACF,WACG,eACG,WACH,OACL,OACA,UACG,UACA,WACC"}