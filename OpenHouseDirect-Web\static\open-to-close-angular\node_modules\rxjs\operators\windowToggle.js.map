{"version": 3, "file": "windowToggle.js", "sourceRoot": "", "sources": ["../../src/operators/windowToggle.ts"], "names": [], "mappings": ";;;;;;AAGA,wBAAwB,YAAY,CAAC,CAAA;AACrC,6BAA6B,iBAAiB,CAAC,CAAA;AAC/C,yBAAyB,kBAAkB,CAAC,CAAA;AAC5C,4BAA4B,qBAAqB,CAAC,CAAA;AAClD,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAG9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AACH,sBAAmC,QAAuB,EACvB,eAAkD;IACnF,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAO,QAAQ,EAAE,eAAe,CAAC,CAAC,EAAtE,CAAsE,CAAC;AAC3G,CAAC;AAHe,oBAAY,eAG3B,CAAA;AAED;IAEE,8BAAoB,QAAuB,EACvB,eAAkD;QADlD,aAAQ,GAAR,QAAQ,CAAe;QACvB,oBAAe,GAAf,eAAe,CAAmC;IACtE,CAAC;IAED,mCAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,sBAAsB,CAChD,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAChD,CAAC,CAAC;IACL,CAAC;IACH,2BAAC;AAAD,CAAC,AAXD,IAWC;AAOD;;;;GAIG;AACH;IAA2C,0CAAuB;IAIhE,gCAAY,WAAsC,EAC9B,QAAuB,EACvB,eAAkD;QACpE,kBAAM,WAAW,CAAC,CAAC;QAFD,aAAQ,GAAR,QAAQ,CAAe;QACvB,oBAAe,GAAf,eAAe,CAAmC;QAL9D,aAAQ,GAAuB,EAAE,CAAC;QAOxC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,qCAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAChF,CAAC;IAES,sCAAK,GAAf,UAAgB,KAAQ;QACd,4BAAQ,CAAU;QAC1B,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACb,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7B,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IAES,uCAAM,GAAhB,UAAiB,GAAQ;QAEf,4BAAQ,CAAU;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACb,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YAEf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC;gBACrB,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1B,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;QAED,gBAAK,CAAC,MAAM,YAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAES,0CAAS,GAAnB;QACU,4BAAQ,CAAU;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACb,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YACf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC;gBACrB,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC1B,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;QACD,gBAAK,CAAC,SAAS,WAAE,CAAC;IACpB,CAAC;IAED,oCAAoC,CAAC,6CAAY,GAAZ;QAC3B,4BAAQ,CAAU;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACb,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YACf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC;gBACrB,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC7B,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IAED,2CAAU,GAAV,UAAW,UAAe,EAAE,UAAe,EAChC,UAAkB,EAAE,UAAkB,EACtC,QAAiC;QAE1C,EAAE,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEzB,0CAAe,CAAU;YACjC,IAAM,eAAe,GAAG,mBAAQ,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,CAAC;YAE9D,EAAE,CAAC,CAAC,eAAe,KAAK,yBAAW,CAAC,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAM,QAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;gBAChC,IAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;gBACxC,IAAM,OAAO,GAAG,EAAE,gBAAM,EAAE,0BAAY,EAAE,CAAC;gBACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,IAAM,iBAAiB,GAAG,qCAAiB,CAAC,IAAI,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;gBAE5E,EAAE,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC7B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7C,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACC,iBAAkB,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;gBACtC,CAAC;gBAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;YAEhC,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,4CAAW,GAAX,UAAY,GAAQ;QAClB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,+CAAc,GAAd,UAAe,KAAmB;QAChC,EAAE,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAQ,KAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAEO,4CAAW,GAAnB,UAAoB,KAAa;QAC/B,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,MAAM,CAAC;QACT,CAAC;QAEO,4BAAQ,CAAU;QAC1B,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxB,2BAAM,EAAE,mCAAY,CAAa;QACzC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,YAAY,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;IACH,6BAAC;AAAD,CAAC,AA5HD,CAA2C,iCAAe,GA4HzD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { tryCatch } from '../util/tryCatch';\nimport { errorObject } from '../util/errorObject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OperatorFunction } from '../interfaces';\n\n/**\n * Branch out the source Observable values as a nested Observable starting from\n * an emission from `openings` and ending when the output of `closingSelector`\n * emits.\n *\n * <span class=\"informal\">It's like {@link bufferToggle}, but emits a nested\n * Observable instead of an array.</span>\n *\n * <img src=\"./img/windowToggle.png\" width=\"100%\">\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable emits windows that contain those items\n * emitted by the source Observable between the time when the `openings`\n * Observable emits an item and when the Observable returned by\n * `closingSelector` emits an item.\n *\n * @example <caption>Every other second, emit the click events from the next 500ms</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var openings = Rx.Observable.interval(1000);\n * var result = clicks.windowToggle(openings, i =>\n *   i % 2 ? Rx.Observable.interval(500) : Rx.Observable.empty()\n * ).mergeAll();\n * result.subscribe(x => console.log(x));\n *\n * @see {@link window}\n * @see {@link windowCount}\n * @see {@link windowTime}\n * @see {@link windowWhen}\n * @see {@link bufferToggle}\n *\n * @param {Observable<O>} openings An observable of notifications to start new\n * windows.\n * @param {function(value: O): Observable} closingSelector A function that takes\n * the value emitted by the `openings` observable and returns an Observable,\n * which, when it emits (either `next` or `complete`), signals that the\n * associated window should complete.\n * @return {Observable<Observable<T>>} An observable of windows, which in turn\n * are Observables.\n * @method windowToggle\n * @owner Observable\n */\nexport function windowToggle<T, O>(openings: Observable<O>,\n                                   closingSelector: (openValue: O) => Observable<any>): OperatorFunction<T, Observable<T>> {\n  return (source: Observable<T>) => source.lift(new WindowToggleOperator<T, O>(openings, closingSelector));\n}\n\nclass WindowToggleOperator<T, O> implements Operator<T, Observable<T>> {\n\n  constructor(private openings: Observable<O>,\n              private closingSelector: (openValue: O) => Observable<any>) {\n  }\n\n  call(subscriber: Subscriber<Observable<T>>, source: any): any {\n    return source.subscribe(new WindowToggleSubscriber(\n      subscriber, this.openings, this.closingSelector\n    ));\n  }\n}\n\ninterface WindowContext<T> {\n  window: Subject<T>;\n  subscription: Subscription;\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass WindowToggleSubscriber<T, O> extends OuterSubscriber<T, any> {\n  private contexts: WindowContext<T>[] = [];\n  private openSubscription: Subscription;\n\n  constructor(destination: Subscriber<Observable<T>>,\n              private openings: Observable<O>,\n              private closingSelector: (openValue: O) => Observable<any>) {\n    super(destination);\n    this.add(this.openSubscription = subscribeToResult(this, openings, openings));\n  }\n\n  protected _next(value: T) {\n    const { contexts } = this;\n    if (contexts) {\n      const len = contexts.length;\n      for (let i = 0; i < len; i++) {\n        contexts[i].window.next(value);\n      }\n    }\n  }\n\n  protected _error(err: any) {\n\n    const { contexts } = this;\n    this.contexts = null;\n\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.error(err);\n        context.subscription.unsubscribe();\n      }\n    }\n\n    super._error(err);\n  }\n\n  protected _complete() {\n    const { contexts } = this;\n    this.contexts = null;\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.complete();\n        context.subscription.unsubscribe();\n      }\n    }\n    super._complete();\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    const { contexts } = this;\n    this.contexts = null;\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.unsubscribe();\n        context.subscription.unsubscribe();\n      }\n    }\n  }\n\n  notifyNext(outerValue: any, innerValue: any,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, any>): void {\n\n    if (outerValue === this.openings) {\n\n      const { closingSelector } = this;\n      const closingNotifier = tryCatch(closingSelector)(innerValue);\n\n      if (closingNotifier === errorObject) {\n        return this.error(errorObject.e);\n      } else {\n        const window = new Subject<T>();\n        const subscription = new Subscription();\n        const context = { window, subscription };\n        this.contexts.push(context);\n        const innerSubscription = subscribeToResult(this, closingNotifier, context);\n\n        if (innerSubscription.closed) {\n          this.closeWindow(this.contexts.length - 1);\n        } else {\n          (<any> innerSubscription).context = context;\n          subscription.add(innerSubscription);\n        }\n\n        this.destination.next(window);\n\n      }\n    } else {\n      this.closeWindow(this.contexts.indexOf(outerValue));\n    }\n  }\n\n  notifyError(err: any): void {\n    this.error(err);\n  }\n\n  notifyComplete(inner: Subscription): void {\n    if (inner !== this.openSubscription) {\n      this.closeWindow(this.contexts.indexOf((<any> inner).context));\n    }\n  }\n\n  private closeWindow(index: number): void {\n    if (index === -1) {\n      return;\n    }\n\n    const { contexts } = this;\n    const context = contexts[index];\n    const { window, subscription } = context;\n    contexts.splice(index, 1);\n    window.complete();\n    subscription.unsubscribe();\n  }\n}\n"]}