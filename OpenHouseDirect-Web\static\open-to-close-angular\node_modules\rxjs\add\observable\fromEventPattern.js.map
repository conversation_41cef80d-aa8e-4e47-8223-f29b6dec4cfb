{"version": 3, "file": "fromEventPattern.js", "sourceRoot": "", "sources": ["../../../src/add/observable/fromEventPattern.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,iCAA2D,mCAAmC,CAAC,CAAA;AAE/F,uBAAU,CAAC,gBAAgB,GAAG,mCAAsB,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { fromEventPattern as staticFromEventPattern } from '../../observable/fromEventPattern';\n\nObservable.fromEventPattern = staticFromEventPattern;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let fromEventPattern: typeof staticFromEventPattern;\n  }\n}"]}