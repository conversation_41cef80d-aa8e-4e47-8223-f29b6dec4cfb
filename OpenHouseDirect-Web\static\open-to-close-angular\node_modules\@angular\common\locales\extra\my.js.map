{"version": 3, "file": "my.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/my.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,aAAa,EAAE,WAAW,EAAE,OAAO;YACnC,QAAQ,EAAE,KAAK,EAAE,GAAG;SACrB;QACD,AADE;KAEH;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'သန်းခေါင်ယံ', 'မွန်းတည့်', 'နံနက်',\n      'နေ့လယ်', 'ညနေ', 'ည'\n    ],\n    ,\n  ],\n  ,\n  [\n    '00:00', '12:00', ['00:00', '12:00'], ['12:00', '16:00'], ['16:00', '19:00'],\n    ['19:00', '24:00']\n  ]\n];\n"]}