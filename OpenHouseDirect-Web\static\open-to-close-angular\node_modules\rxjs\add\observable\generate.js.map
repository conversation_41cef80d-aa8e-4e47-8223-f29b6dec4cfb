{"version": 3, "file": "generate.js", "sourceRoot": "", "sources": ["../../../src/add/observable/generate.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,yBAA2C,2BAA2B,CAAC,CAAA;AAEvE,uBAAU,CAAC,QAAQ,GAAG,mBAAc,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { generate as staticGenerate } from '../../observable/generate';\n\nObservable.generate = staticGenerate;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let generate: typeof staticGenerate;\n  }\n}"]}