{"version": 3, "file": "groupBy.js", "sourceRoot": "", "sources": ["../../src/operators/groupBy.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAA2B,eAAe,CAAC,CAAA;AAC3C,6BAA6B,iBAAiB,CAAC,CAAA;AAC/C,2BAA2B,eAAe,CAAC,CAAA;AAE3C,wBAAwB,YAAY,CAAC,CAAA;AACrC,oBAAoB,aAAa,CAAC,CAAA;AAClC,wBAAwB,iBAAiB,CAAC,CAAA;AAQ1C,mCAAmC;AAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkEG;AACH,iBAAiC,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;IACjE,MAAM,CAAC,UAAC,MAAqB;QAC3B,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;IAAjG,CAAiG,CAAC;AACtG,CAAC;AANe,eAAO,UAMtB,CAAA;AASD;IACE,yBAAoB,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;QAHlC,gBAAW,GAAX,WAAW,CAAiB;QAC5B,oBAAe,GAAf,eAAe,CAA2B;QAC1C,qBAAgB,GAAhB,gBAAgB,CAAwD;QACxE,oBAAe,GAAf,eAAe,CAAmB;IACtD,CAAC;IAED,8BAAI,GAAJ,UAAK,UAA+C,EAAE,MAAW;QAC/D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,iBAAiB,CAC3C,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAChG,CAAC,CAAC;IACL,CAAC;IACH,sBAAC;AAAD,CAAC,AAZD,IAYC;AAED;;;;GAIG;AACH;IAAyC,qCAAa;IAKpD,2BAAY,WAAgD,EACxC,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;QACpD,kBAAM,WAAW,CAAC,CAAC;QAJD,gBAAW,GAAX,WAAW,CAAiB;QAC5B,oBAAe,GAAf,eAAe,CAA2B;QAC1C,qBAAgB,GAAhB,gBAAgB,CAAwD;QACxE,oBAAe,GAAf,eAAe,CAAmB;QAR9C,WAAM,GAAyB,IAAI,CAAC;QACrC,2BAAsB,GAAY,KAAK,CAAC;QACxC,UAAK,GAAW,CAAC,CAAC;IAQzB,CAAC;IAES,iCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,GAAM,CAAC;QACX,IAAI,CAAC;YACH,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAChC,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChB,MAAM,CAAC;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC1B,CAAC;IAEO,kCAAM,GAAd,UAAe,KAAQ,EAAE,GAAM;QAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACZ,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,KAAK,QAAQ,GAAG,IAAI,iBAAO,EAAE,GAAG,IAAI,SAAG,EAAE,CAAC;QAC7E,CAAC;QAED,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE5B,IAAI,OAAU,CAAC;QACf,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC;gBACH,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACxC,CAAE;YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACb,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,OAAO,GAAQ,KAAK,CAAC;QACvB,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACX,KAAK,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,iBAAO,EAAK,CAAC;YACzE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACvB,IAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzC,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAC1B,IAAI,QAAQ,SAAK,CAAC;gBAClB,IAAI,CAAC;oBACH,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,iBAAiB,CAAO,GAAG,EAAc,KAAK,CAAC,CAAC,CAAC;gBACxF,CAAE;gBAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAChB,MAAM,CAAC;gBACT,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAES,kCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;gBACxB,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,qCAAS,GAAnB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;gBACxB,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED,uCAAW,GAAX,UAAY,GAAM;QAChB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,uCAAW,GAAX;QACE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrB,gBAAK,CAAC,WAAW,WAAE,CAAC;YACtB,CAAC;QACH,CAAC;IACH,CAAC;IACH,wBAAC;AAAD,CAAC,AAvGD,CAAyC,uBAAU,GAuGlD;AAED;;;;GAIG;AACH;IAA4C,2CAAa;IACvD,iCAAoB,GAAM,EACN,KAAiB,EACjB,MAAoC;QACtD,kBAAM,KAAK,CAAC,CAAC;QAHK,QAAG,GAAH,GAAG,CAAG;QACN,UAAK,GAAL,KAAK,CAAY;QACjB,WAAM,GAAN,MAAM,CAA8B;IAExD,CAAC;IAES,uCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,oCAAoC,CAAC,8CAAY,GAAZ;QACnC,IAAA,SAA4B,EAApB,kBAAM,EAAE,YAAG,CAAU;QAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAC9B,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IACH,8BAAC;AAAD,CAAC,AAlBD,CAA4C,uBAAU,GAkBrD;AAED;;;;;;;GAOG;AACH;IAA6C,qCAAa;IACxD,2BAAmB,GAAM,EACL,YAAwB,EACxB,oBAA2C;QAC7D,iBAAO,CAAC;QAHS,QAAG,GAAH,GAAG,CAAG;QACL,iBAAY,GAAZ,YAAY,CAAY;QACxB,yBAAoB,GAApB,oBAAoB,CAAuB;IAE/D,CAAC;IAED,oCAAoC,CAAC,sCAAU,GAAV,UAAW,UAAyB;QACvE,IAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACxC,IAAA,SAAiD,EAA1C,8CAAoB,EAAE,8BAAY,CAAS;QAClD,EAAE,CAAC,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;YACzD,YAAY,CAAC,GAAG,CAAC,IAAI,yBAAyB,CAAC,oBAAoB,CAAC,CAAC,CAAC;QACxE,CAAC;QACD,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QACrD,MAAM,CAAC,YAAY,CAAC;IACtB,CAAC;IACH,wBAAC;AAAD,CAAC,AAhBD,CAA6C,uBAAU,GAgBtD;AAhBY,yBAAiB,oBAgB7B,CAAA;AAED;;;;GAIG;AACH;IAAwC,6CAAY;IAClD,mCAAoB,MAA4B;QAC9C,iBAAO,CAAC;QADU,WAAM,GAAN,MAAM,CAAsB;QAE9C,MAAM,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,+CAAW,GAAX;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACnC,gBAAK,CAAC,WAAW,WAAE,CAAC;YACpB,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;YAClB,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC;gBACxD,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;IACH,gCAAC;AAAD,CAAC,AAhBD,CAAwC,2BAAY,GAgBnD", "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { Observable } from '../Observable';\nimport { Operator } from '../Operator';\nimport { Subject } from '../Subject';\nimport { Map } from '../util/Map';\nimport { FastMap } from '../util/FastMap';\nimport { OperatorFunction } from '../interfaces';\n\n/* tslint:disable:max-line-length */\nexport function groupBy<T, K>(keySelector: (value: T) => K): OperatorFunction<T, GroupedObservable<K, T>>;\nexport function groupBy<T, K>(keySelector: (value: T) => K, elementSelector: void, durationSelector: (grouped: GroupedObservable<K, T>) => Observable<any>): OperatorFunction<T, GroupedObservable<K, T>>;\nexport function groupBy<T, K, R>(keySelector: (value: T) => K, elementSelector?: (value: T) => R, durationSelector?: (grouped: GroupedObservable<K, R>) => Observable<any>): OperatorFunction<T, GroupedObservable<K, R>>;\nexport function groupBy<T, K, R>(keySelector: (value: T) => K, elementSelector?: (value: T) => R, durationSelector?: (grouped: GroupedObservable<K, R>) => Observable<any>, subjectSelector?: () => Subject<R>): OperatorFunction<T, GroupedObservable<K, R>>;\n/* tslint:enable:max-line-length */\n\n/**\n * Groups the items emitted by an Observable according to a specified criterion,\n * and emits these grouped items as `GroupedObservables`, one\n * {@link GroupedObservable} per group.\n *\n * <img src=\"./img/groupBy.png\" width=\"100%\">\n *\n * @example <caption>Group objects by id and return as array</caption>\n * Observable.of<Obj>({id: 1, name: 'aze1'},\n *                    {id: 2, name: 'sf2'},\n *                    {id: 2, name: 'dg2'},\n *                    {id: 1, name: 'erg1'},\n *                    {id: 1, name: 'df1'},\n *                    {id: 2, name: 'sfqfb2'},\n *                    {id: 3, name: 'qfs3'},\n *                    {id: 2, name: 'qsgqsfg2'}\n *     )\n *     .groupBy(p => p.id)\n *     .flatMap( (group$) => group$.reduce((acc, cur) => [...acc, cur], []))\n *     .subscribe(p => console.log(p));\n *\n * // displays:\n * // [ { id: 1, name: 'aze1' },\n * //   { id: 1, name: 'erg1' },\n * //   { id: 1, name: 'df1' } ]\n * //\n * // [ { id: 2, name: 'sf2' },\n * //   { id: 2, name: 'dg2' },\n * //   { id: 2, name: 'sfqfb2' },\n * //   { id: 2, name: 'qsgqsfg2' } ]\n * //\n * // [ { id: 3, name: 'qfs3' } ]\n *\n * @example <caption>Pivot data on the id field</caption>\n * Observable.of<Obj>({id: 1, name: 'aze1'},\n *                    {id: 2, name: 'sf2'},\n *                    {id: 2, name: 'dg2'},\n *                    {id: 1, name: 'erg1'},\n *                    {id: 1, name: 'df1'},\n *                    {id: 2, name: 'sfqfb2'},\n *                    {id: 3, name: 'qfs1'},\n *                    {id: 2, name: 'qsgqsfg2'}\n *                   )\n *     .groupBy(p => p.id, p => p.name)\n *     .flatMap( (group$) => group$.reduce((acc, cur) => [...acc, cur], [\"\" + group$.key]))\n *     .map(arr => ({'id': parseInt(arr[0]), 'values': arr.slice(1)}))\n *     .subscribe(p => console.log(p));\n *\n * // displays:\n * // { id: 1, values: [ 'aze1', 'erg1', 'df1' ] }\n * // { id: 2, values: [ 'sf2', 'dg2', 'sfqfb2', 'qsgqsfg2' ] }\n * // { id: 3, values: [ 'qfs1' ] }\n *\n * @param {function(value: T): K} keySelector A function that extracts the key\n * for each item.\n * @param {function(value: T): R} [elementSelector] A function that extracts the\n * return element for each item.\n * @param {function(grouped: GroupedObservable<K,R>): Observable<any>} [durationSelector]\n * A function that returns an Observable to determine how long each group should\n * exist.\n * @return {Observable<GroupedObservable<K,R>>} An Observable that emits\n * GroupedObservables, each of which corresponds to a unique key value and each\n * of which emits those items from the source Observable that share that key\n * value.\n * @method groupBy\n * @owner Observable\n */\nexport function groupBy<T, K, R>(keySelector: (value: T) => K,\n                                 elementSelector?: ((value: T) => R) | void,\n                                 durationSelector?: (grouped: GroupedObservable<K, R>) => Observable<any>,\n                                 subjectSelector?: () => Subject<R>): OperatorFunction<T, GroupedObservable<K, R>> {\n  return (source: Observable<T>) =>\n    source.lift(new GroupByOperator(keySelector, elementSelector, durationSelector, subjectSelector));\n}\n\nexport interface RefCountSubscription {\n  count: number;\n  unsubscribe: () => void;\n  closed: boolean;\n  attemptedToUnsubscribe: boolean;\n}\n\nclass GroupByOperator<T, K, R> implements Operator<T, GroupedObservable<K, R>> {\n  constructor(private keySelector: (value: T) => K,\n              private elementSelector?: ((value: T) => R) | void,\n              private durationSelector?: (grouped: GroupedObservable<K, R>) => Observable<any>,\n              private subjectSelector?: () => Subject<R>) {\n  }\n\n  call(subscriber: Subscriber<GroupedObservable<K, R>>, source: any): any {\n    return source.subscribe(new GroupBySubscriber(\n      subscriber, this.keySelector, this.elementSelector, this.durationSelector, this.subjectSelector\n    ));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass GroupBySubscriber<T, K, R> extends Subscriber<T> implements RefCountSubscription {\n  private groups: Map<K, Subject<T|R>> = null;\n  public attemptedToUnsubscribe: boolean = false;\n  public count: number = 0;\n\n  constructor(destination: Subscriber<GroupedObservable<K, R>>,\n              private keySelector: (value: T) => K,\n              private elementSelector?: ((value: T) => R) | void,\n              private durationSelector?: (grouped: GroupedObservable<K, R>) => Observable<any>,\n              private subjectSelector?: () => Subject<R>) {\n    super(destination);\n  }\n\n  protected _next(value: T): void {\n    let key: K;\n    try {\n      key = this.keySelector(value);\n    } catch (err) {\n      this.error(err);\n      return;\n    }\n\n    this._group(value, key);\n  }\n\n  private _group(value: T, key: K) {\n    let groups = this.groups;\n\n    if (!groups) {\n      groups = this.groups = typeof key === 'string' ? new FastMap() : new Map();\n    }\n\n    let group = groups.get(key);\n\n    let element: R;\n    if (this.elementSelector) {\n      try {\n        element = this.elementSelector(value);\n      } catch (err) {\n        this.error(err);\n      }\n    } else {\n      element = <any>value;\n    }\n\n    if (!group) {\n      group = this.subjectSelector ? this.subjectSelector() : new Subject<R>();\n      groups.set(key, group);\n      const groupedObservable = new GroupedObservable(key, group, this);\n      this.destination.next(groupedObservable);\n      if (this.durationSelector) {\n        let duration: any;\n        try {\n          duration = this.durationSelector(new GroupedObservable<K, R>(key, <Subject<R>>group));\n        } catch (err) {\n          this.error(err);\n          return;\n        }\n        this.add(duration.subscribe(new GroupDurationSubscriber(key, group, this)));\n      }\n    }\n\n    if (!group.closed) {\n      group.next(element);\n    }\n  }\n\n  protected _error(err: any): void {\n    const groups = this.groups;\n    if (groups) {\n      groups.forEach((group, key) => {\n        group.error(err);\n      });\n\n      groups.clear();\n    }\n    this.destination.error(err);\n  }\n\n  protected _complete(): void {\n    const groups = this.groups;\n    if (groups) {\n      groups.forEach((group, key) => {\n        group.complete();\n      });\n\n      groups.clear();\n    }\n    this.destination.complete();\n  }\n\n  removeGroup(key: K): void {\n    this.groups.delete(key);\n  }\n\n  unsubscribe() {\n    if (!this.closed) {\n      this.attemptedToUnsubscribe = true;\n      if (this.count === 0) {\n        super.unsubscribe();\n      }\n    }\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass GroupDurationSubscriber<K, T> extends Subscriber<T> {\n  constructor(private key: K,\n              private group: Subject<T>,\n              private parent: GroupBySubscriber<any, K, T>) {\n    super(group);\n  }\n\n  protected _next(value: T): void {\n    this.complete();\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    const { parent, key } = this;\n    this.key = this.parent = null;\n    if (parent) {\n      parent.removeGroup(key);\n    }\n  }\n}\n\n/**\n * An Observable representing values belonging to the same group represented by\n * a common key. The values emitted by a GroupedObservable come from the source\n * Observable. The common key is available as the field `key` on a\n * GroupedObservable instance.\n *\n * @class GroupedObservable<K, T>\n */\nexport class GroupedObservable<K, T> extends Observable<T> {\n  constructor(public key: K,\n              private groupSubject: Subject<T>,\n              private refCountSubscription?: RefCountSubscription) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>) {\n    const subscription = new Subscription();\n    const {refCountSubscription, groupSubject} = this;\n    if (refCountSubscription && !refCountSubscription.closed) {\n      subscription.add(new InnerRefCountSubscription(refCountSubscription));\n    }\n    subscription.add(groupSubject.subscribe(subscriber));\n    return subscription;\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass InnerRefCountSubscription extends Subscription {\n  constructor(private parent: RefCountSubscription) {\n    super();\n    parent.count++;\n  }\n\n  unsubscribe() {\n    const parent = this.parent;\n    if (!parent.closed && !this.closed) {\n      super.unsubscribe();\n      parent.count -= 1;\n      if (parent.count === 0 && parent.attemptedToUnsubscribe) {\n        parent.unsubscribe();\n      }\n    }\n  }\n}\n"]}