{"_from": "@braintree/browser-detection@^1.17.2", "_id": "@braintree/browser-detection@1.17.2", "_inBundle": false, "_integrity": "sha512-DdEX09uYs6kHwGt4cbONlxlta/0hfmrDUncP6EtfZxFVywNF9LeRUyon+2LihJTbqgSnGqz9ZL450hkqBd6oSw==", "_location": "/restricted-input/@braintree/browser-detection", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@braintree/browser-detection@^1.17.2", "name": "@braintree/browser-detection", "escapedName": "@braintree%2fbrowser-detection", "scope": "@braintree", "rawSpec": "^1.17.2", "saveSpec": null, "fetchSpec": "^1.17.2"}, "_requiredBy": ["/restricted-input"], "_resolved": "https://registry.npmjs.org/@braintree/browser-detection/-/browser-detection-1.17.2.tgz", "_shasum": "bf4edf97a90897aaa0956869316e50be0c4fbcb5", "_spec": "@braintree/browser-detection@^1.17.2", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\restricted-input", "author": {"name": "Braintree", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/braintree/browser-detection/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A small lib to detect browser compatibility for braintree products", "devDependencies": {"@types/jest": "^29.4.0", "@types/node": "^18.14.6", "@typescript-eslint/eslint-plugin": "^5.54.1", "eslint": "^8.35.0", "eslint-config-braintree": "^6.0.0-typescript-prep-rc.2", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "prettier": "^2.8.4", "ts-jest": "^29.1.1", "typescript": "^4.9.5"}, "files": ["dist", "*.js"], "homepage": "https://github.com/braintree/browser-detection#readme", "jest": {"testEnvironment": "jsdom", "preset": "ts-jest", "restoreMocks": true, "resetMocks": true}, "keywords": [], "license": "MIT", "main": "dist/browser-detection.js", "name": "@braintree/browser-detection", "repository": {"type": "git", "url": "git+https://github.com/braintree/browser-detection.git"}, "scripts": {"build": "tsc --declaration", "lint": "eslint --ext js,ts .", "posttest": "npm run lint", "prebuild": "prettier --write .", "prepublishOnly": "npm run build", "pretest": "npm run build", "test": "jest", "version": "./version"}, "types": "dist/browser-detection.d.ts", "version": "1.17.2"}