{"version": 3, "file": "range.js", "sourceRoot": "", "sources": ["../../../src/add/observable/range.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { range as staticRange } from '../../observable/range';\n\nObservable.range = staticRange;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let range: typeof staticRange;\n  }\n}"]}