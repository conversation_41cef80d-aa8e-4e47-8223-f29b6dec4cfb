{"version": 3, "file": "table.es5.js", "sources": ["../../../src/cdk/table/index.ts", "../../../src/cdk/table/public-api.ts", "../../../src/cdk/table/table-module.ts", "../../../src/cdk/table/table.ts", "../../../src/cdk/table/table-errors.ts", "../../../src/cdk/table/cell.ts", "../../../src/cdk/table/row.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './table';\nexport * from './cell';\nexport * from './row';\nexport * from './table-module';\n\n/** Re-export DataSource for a more intuitive experience for users of just the table. */\nexport {DataSource} from '@angular/cdk/collections';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {HeaderRowPlaceholder, RowPlaceholder, CdkTable} from './table';\nimport {CdkCellOutlet, CdkHeaderRow, CdkHeaderRowDef, CdkRow, CdkRowDef} from './row';\nimport {CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCell, CdkCellDef} from './cell';\n\nconst EXPORTED_DECLARATIONS = [\n  CdkTable,\n  CdkRowDef,\n  CdkCellDef,\n  CdkCellOutlet,\n  CdkHeaderCellDef,\n  CdkColumnDef,\n  CdkCell,\n  CdkRow,\n  CdkHeaderCell,\n  CdkHeaderRow,\n  CdkHeader<PERSON>owDef,\n  RowPlaceholder,\n  HeaderRowPlaceholder,\n];\n\n@NgModule({\n  imports: [CommonModule],\n  exports: [EXPORTED_DECLARATIONS],\n  declarations: [EXPORTED_DECLARATIONS]\n\n})\nexport class CdkTableModule { }\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterContentChecked,\n  Attribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EmbeddedViewRef,\n  Input,\n  isDevMode,\n  IterableChangeRecord,\n  IterableDiffer,\n  IterableDiffers,\n  OnInit,\n  QueryList,\n  TrackByFunction,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {CollectionViewer, DataSource} from '@angular/cdk/collections';\nimport {CdkCellOutlet, CdkCellOutletRowContext, CdkHeaderRowDef, CdkRowDef} from './row';\nimport {takeUntil} from 'rxjs/operators/takeUntil';\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Subject} from 'rxjs/Subject';\nimport {CdkCellDef, CdkColumnDef, CdkHeaderCellDef} from './cell';\nimport {\n  getTableDuplicateColumnNameError,\n  getTableMissingMatchingRowDefError,\n  getTableMissingRowDefsError,\n  getTableMultipleDefaultRowDefsError,\n  getTableUnknownColumnError,\n  getTableUnknownDataSourceError\n} from './table-errors';\nimport {Observable} from 'rxjs/Observable';\nimport {of as observableOf} from 'rxjs/observable/of';\n\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\n@Directive({selector: '[rowPlaceholder]'})\nexport class RowPlaceholder {\n  constructor(public viewContainer: ViewContainerRef) { }\n}\n\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\n@Directive({selector: '[headerRowPlaceholder]'})\nexport class HeaderRowPlaceholder {\n  constructor(public viewContainer: ViewContainerRef) { }\n}\n\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nexport const CDK_TABLE_TEMPLATE = `\n  <ng-container headerRowPlaceholder></ng-container>\n  <ng-container rowPlaceholder></ng-container>`;\n\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nabstract class RowViewRef<T> extends EmbeddedViewRef<CdkCellOutletRowContext<T>> { }\n\n/**\n * A data table that renders a header row and data rows. Uses the dataSource input to determine\n * the data to be rendered. The data can be provided either as a data array, an Observable stream\n * that emits the data array to render, or a DataSource with a connect function that will\n * return an Observable stream that emits the data array to render.\n */\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-table',\n  exportAs: 'cdkTable',\n  template: CDK_TABLE_TEMPLATE,\n  host: {\n    'class': 'cdk-table',\n  },\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class CdkTable<T> implements CollectionViewer, OnInit, AfterContentChecked {\n  /** Subject that emits when the component has been destroyed. */\n  private _onDestroy = new Subject<void>();\n\n  /** Latest data provided by the data source. */\n  private _data: T[];\n\n  /** Subscription that listens for the data provided by the data source. */\n  private _renderChangeSubscription: Subscription | null;\n\n  /**\n   * Map of all the user's defined columns (header and data cell template) identified by name.\n   * Collection populated by the column definitions gathered by `ContentChildren` as well as any\n   * custom column definitions added to `_customColumnDefs`.\n   */\n  private _columnDefsByName = new Map<string,  CdkColumnDef>();\n\n  /**\n   * Set of all row defitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n   */\n  private _rowDefs: CdkRowDef<T>[];\n\n  /** Differ used to find the changes in the data provided by the data source. */\n  private _dataDiffer: IterableDiffer<T>;\n\n  /** Stores the row definition that does not have a when predicate. */\n  private _defaultRowDef: CdkRowDef<T> | null;\n\n  /** Column definitions that were defined outside of the direct content children of the table. */\n  private _customColumnDefs = new Set<CdkColumnDef>();\n\n  /** Row definitions that were defined outside of the direct content children of the table. */\n  private _customRowDefs = new Set<CdkRowDef<T>>();\n\n  /**\n   * Whether the header row definition has been changed. Triggers an update to the header row after\n   * content is checked.\n   */\n  private _headerRowDefChanged = false;\n\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  @Input()\n  get trackBy(): TrackByFunction<T> { return this._trackByFn; }\n  set trackBy(fn: TrackByFunction<T>) {\n    if (isDevMode() &&\n        fn != null && typeof fn !== 'function' &&\n        <any>console && <any>console.warn) {\n        console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  private _trackByFn: TrackByFunction<T>;\n\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  @Input()\n  get dataSource(): DataSource<T> | Observable<T[]> | T[] { return this._dataSource; }\n  set dataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  private _dataSource: DataSource<T> | Observable<T[]> | T[] | T[];\n\n  // TODO(andrewseguin): Remove max value as the end index\n  //   and instead calculate the view on init and scroll.\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   */\n  viewChange: BehaviorSubject<{start: number, end: number}> =\n      new BehaviorSubject<{start: number, end: number}>({start: 0, end: Number.MAX_VALUE});\n\n  // Placeholders within the table's template where the header and data rows will be inserted.\n  @ViewChild(RowPlaceholder) _rowPlaceholder: RowPlaceholder;\n  @ViewChild(HeaderRowPlaceholder) _headerRowPlaceholder: HeaderRowPlaceholder;\n\n  /**\n   * The column definitions provided by the user that contain what the header and cells should\n   * render for each column.\n   */\n  @ContentChildren(CdkColumnDef) _contentColumnDefs: QueryList<CdkColumnDef>;\n\n  /** Set of template definitions that used as the data row containers. */\n  @ContentChildren(CdkRowDef) _contentRowDefs: QueryList<CdkRowDef<T>>;\n\n  /**\n   * Template definition used as the header container. By default it stores the header row\n   * definition found as a direct content child. Override this value through `setHeaderRowDef` if\n   * the header row definition should be changed or was not defined as a part of the table's\n   * content.\n   */\n  @ContentChild(CdkHeaderRowDef) _headerRowDef: CdkHeaderRowDef;\n\n  constructor(private readonly _differs: IterableDiffers,\n              private readonly _changeDetectorRef: ChangeDetectorRef,\n              elementRef: ElementRef,\n              @Attribute('role') role: string) {\n    if (!role) {\n      elementRef.nativeElement.setAttribute('role', 'grid');\n    }\n  }\n\n  ngOnInit() {\n    // TODO(andrewseguin): Setup a listener for scrolling, emit the calculated view to viewChange\n    this._dataDiffer = this._differs.find([]).create(this._trackByFn);\n\n    // If the table has a header row definition defined as part of its content, flag this as a\n    // header row def change so that the content check will render the header row.\n    if (this._headerRowDef) {\n      this._headerRowDefChanged = true;\n    }\n  }\n\n  ngAfterContentChecked() {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n\n    // Make sure that the user has at least added a header row or row def.\n    if (!this._headerRowDef && !this._rowDefs.length) {\n      throw getTableMissingRowDefsError();\n    }\n\n    // Render updates if the list of columns have been changed for the header or row definitions.\n    this._renderUpdatedColumns();\n\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._renderHeaderRow();\n      this._headerRowDefChanged = false;\n    }\n\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    }\n  }\n\n  ngOnDestroy() {\n    this._rowPlaceholder.viewContainer.clear();\n    this._headerRowPlaceholder.viewContainer.clear();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n\n    if (this.dataSource instanceof DataSource) {\n      this.dataSource.disconnect(this);\n    }\n  }\n\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    const changes = this._dataDiffer.diff(this._data);\n    if (!changes) { return; }\n\n    const viewContainer = this._rowPlaceholder.viewContainer;\n    changes.forEachOperation(\n        (record: IterableChangeRecord<T>, adjustedPreviousIndex: number, currentIndex: number) => {\n          if (record.previousIndex == null) {\n            this._insertRow(record.item, currentIndex);\n          } else if (currentIndex == null) {\n            viewContainer.remove(adjustedPreviousIndex);\n          } else {\n            const view = <RowViewRef<T>>viewContainer.get(adjustedPreviousIndex);\n            viewContainer.move(view!, currentIndex);\n          }\n        });\n\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange((record: IterableChangeRecord<T>) => {\n      const rowView = <RowViewRef<T>>viewContainer.get(record.currentIndex!);\n      rowView.context.$implicit = record.item;\n    });\n  }\n\n  /**\n   * Sets the header row definition to be used. Overrides the header row definition gathered by\n   * using `ContentChild`, if one exists. Sets a flag that will re-render the header row after the\n   * table's content is checked.\n   */\n  setHeaderRowDef(headerRowDef: CdkHeaderRowDef) {\n    this._headerRowDef = headerRowDef;\n    this._headerRowDefChanged = true;\n  }\n\n  /** Adds a column definition that was not included as part of the direct content children. */\n  addColumnDef(columnDef: CdkColumnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n\n  /** Removes a column definition that was not included as part of the direct content children. */\n  removeColumnDef(columnDef: CdkColumnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n\n  /** Adds a row definition that was not included as part of the direct content children. */\n  addRowDef(rowDef: CdkRowDef<T>) {\n    this._customRowDefs.add(rowDef);\n  }\n\n  /** Removes a row definition that was not included as part of the direct content children. */\n  removeRowDef(rowDef: CdkRowDef<T>) {\n    this._customRowDefs.delete(rowDef);\n  }\n\n  /** Update the map containing the content's column definitions. */\n  private _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n\n    const columnDefs = this._contentColumnDefs ? this._contentColumnDefs.toArray() : [];\n    this._customColumnDefs.forEach(columnDef => columnDefs.push(columnDef));\n\n    columnDefs.forEach(columnDef => {\n      if (this._columnDefsByName.has(columnDef.name)) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n\n  /** Update the list of all available row definitions that can be used. */\n  private _cacheRowDefs() {\n    this._rowDefs = this._contentRowDefs ? this._contentRowDefs.toArray() : [];\n    this._customRowDefs.forEach(rowDef => this._rowDefs.push(rowDef));\n\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (defaultRowDefs.length > 1) { throw getTableMultipleDefaultRowDefsError(); }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n\n  /**\n   * Check if the header or rows have changed what columns they want to display. If there is a diff,\n   * then re-render that section.\n   */\n  private _renderUpdatedColumns() {\n    // Re-render the rows when the row definition columns change.\n    this._rowDefs.forEach(def => {\n      if (!!def.getColumnsDiff()) {\n        // Reset the data to an empty array so that renderRowChanges will re-render all new rows.\n        this._dataDiffer.diff([]);\n\n        this._rowPlaceholder.viewContainer.clear();\n        this.renderRows();\n      }\n    });\n\n    // Re-render the header row if there is a difference in its columns.\n    if (this._headerRowDef && this._headerRowDef.getColumnsDiff()) {\n      this._renderHeaderRow();\n    }\n  }\n\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row placeholder. Otherwise start listening for new data.\n   */\n  private _switchDataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    this._data = [];\n\n    if (this.dataSource instanceof DataSource) {\n      this.dataSource.disconnect(this);\n    }\n\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      this._rowPlaceholder.viewContainer.clear();\n    }\n\n    this._dataSource = dataSource;\n  }\n\n  /** Set up a subscription for the data provided by the data source. */\n  private _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) { return; }\n\n    let dataStream: Observable<T[]> | undefined;\n\n    // Check if the datasource is a DataSource object by observing if it has a connect function.\n    // Cannot check this.dataSource['connect'] due to potential property renaming, nor can it\n    // checked as an instanceof DataSource<T> since the table should allow for data sources\n    // that did not explicitly extend DataSource<T>.\n    if ((this.dataSource as DataSource<T>).connect  instanceof Function) {\n      dataStream = (this.dataSource as DataSource<T>).connect(this);\n    } else if (this.dataSource instanceof Observable) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = observableOf(this.dataSource);\n    }\n\n    if (dataStream === undefined) {\n      throw getTableUnknownDataSourceError();\n    }\n\n    this._renderChangeSubscription = dataStream\n        .pipe(takeUntil(this._onDestroy))\n        .subscribe(data => {\n          this._data = data;\n          this.renderRows();\n        });\n  }\n\n  /**\n   * Clears any existing content in the header row placeholder and creates a new embedded view\n   * in the placeholder using the header row definition.\n   */\n  private _renderHeaderRow() {\n    // Clear the header row placeholder if any content exists.\n    if (this._headerRowPlaceholder.viewContainer.length > 0) {\n      this._headerRowPlaceholder.viewContainer.clear();\n    }\n\n    const cells = this._getHeaderCellTemplatesForRow(this._headerRowDef);\n    if (!cells.length) { return; }\n\n    // TODO(andrewseguin): add some code to enforce that exactly\n    //   one CdkCellOutlet was instantiated as a result\n    //   of `createEmbeddedView`.\n    this._headerRowPlaceholder.viewContainer\n        .createEmbeddedView(this._headerRowDef.template, {cells});\n\n    cells.forEach(cell => {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cell.template, {});\n      }\n    });\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Finds the matching row definition that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definition that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDef(data: T, i: number): CdkRowDef<T> {\n    if (this._rowDefs.length == 1) { return this._rowDefs[0]; }\n\n    let rowDef = this._rowDefs.find(def => def.when && def.when(i, data)) || this._defaultRowDef;\n    if (!rowDef) { throw getTableMissingMatchingRowDefError(); }\n\n    return rowDef;\n  }\n\n  /**\n   * Create the embedded view for the data row template and place it in the correct index location\n   * within the data row view container.\n   */\n  private _insertRow(rowData: T, index: number) {\n    const row = this._getRowDef(rowData, index);\n\n    // Row context that will be provided to both the created embedded row view and its cells.\n    const context: CdkCellOutletRowContext<T> = {$implicit: rowData};\n\n    // TODO(andrewseguin): add some code to enforce that exactly one\n    //   CdkCellOutlet was instantiated as a result  of `createEmbeddedView`.\n    this._rowPlaceholder.viewContainer.createEmbeddedView(row.template, context, index);\n\n    this._getCellTemplatesForRow(row).forEach(cell => {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer\n            .createEmbeddedView(cell.template, context);\n      }\n    });\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  private _updateRowIndexContext() {\n    const viewContainer = this._rowPlaceholder.viewContainer;\n    for (let index = 0, count = viewContainer.length; index < count; index++) {\n      const viewRef = viewContainer.get(index) as RowViewRef<T>;\n      viewRef.context.index = index;\n      viewRef.context.count = count;\n      viewRef.context.first = index === 0;\n      viewRef.context.last = index === count - 1;\n      viewRef.context.even = index % 2 === 0;\n      viewRef.context.odd = !viewRef.context.even;\n    }\n  }\n\n  /**\n   * Returns the cell template definitions to insert into the header\n   * as defined by its list of columns to display.\n   */\n  private _getHeaderCellTemplatesForRow(headerDef: CdkHeaderRowDef): CdkHeaderCellDef[] {\n    if (!headerDef || !headerDef.columns) { return []; }\n    return headerDef.columns.map(columnId => {\n      const column = this._columnDefsByName.get(columnId);\n\n      if (!column) {\n        throw getTableUnknownColumnError(columnId);\n      }\n\n      return column.headerCell;\n    });\n  }\n\n  /**\n   * Returns the cell template definitions to insert in the provided row\n   * as defined by its list of columns to display.\n   */\n  private _getCellTemplatesForRow(rowDef: CdkRowDef<T>): CdkCellDef[] {\n    if (!rowDef.columns) { return []; }\n    return rowDef.columns.map(columnId => {\n      const column = this._columnDefsByName.get(columnId);\n\n      if (!column) {\n        throw getTableUnknownColumnError(columnId);\n      }\n\n      return column.cell;\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an error to be thrown when attempting to find an unexisting column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nexport function getTableUnknownColumnError(id: string) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nexport function getTableDuplicateColumnNameError(name: string) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nexport function getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nexport function getTableMissingMatchingRowDefError() {\n  return Error(`Could not find a matching row definition for the provided row data.`);\n}\n\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nexport function getTableMissingRowDefsError() {\n  return Error('Missing definitions for header and row, ' +\n      'cannot determine which columns should be rendered.');\n}\n\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nexport function getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentChild, Directive, ElementRef, Input, TemplateRef} from '@angular/core';\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\n@Directive({selector: '[cdkCellDef]'})\nexport class CdkCellDef {\n  constructor(/** @docs-private */ public template: TemplateRef<any>) { }\n}\n\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\n@Directive({selector: '[cdkHeaderCellDef]'})\nexport class CdkHeaderCellDef {\n  constructor(/** @docs-private */ public template: TemplateRef<any>) { }\n}\n\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\n@Directive({selector: '[cdkColumnDef]'})\nexport class CdkColumnDef {\n  /** Unique name for this column. */\n  @Input('cdkColumnDef')\n  get name(): string { return this._name; }\n  set name(name: string) {\n    // If the directive is set without a name (updated programatically), then this setter will\n    // trigger with an empty string and should not overwrite the programatically set value.\n    if (!name) { return; }\n\n    this._name = name;\n    this.cssClassFriendlyName = name.replace(/[^a-z0-9_-]/ig, '-');\n  }\n  _name: string;\n\n  /** @docs-private */\n  @ContentChild(CdkCellDef) cell: CdkCellDef;\n\n  /** @docs-private */\n  @ContentChild(CdkHeaderCellDef) headerCell: CdkHeaderCellDef;\n\n  /**\n   * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n   * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n   * do not match are replaced by the '-' character.\n   */\n  cssClassFriendlyName: string;\n}\n\n/** Header cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'cdk-header-cell',\n  host: {\n    'class': 'cdk-header-cell',\n    'role': 'columnheader',\n  },\n})\nexport class CdkHeaderCell {\n  constructor(columnDef: CdkColumnDef, elementRef: ElementRef) {\n    elementRef.nativeElement.classList.add(`cdk-column-${columnDef.cssClassFriendlyName}`);\n  }\n}\n\n/** Cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'cdk-cell',\n  host: {\n    'class': 'cdk-cell',\n    'role': 'gridcell',\n  },\n})\nexport class CdkCell {\n  constructor(columnDef: CdkColumnDef, elementRef: ElementRef) {\n    elementRef.nativeElement.classList.add(`cdk-column-${columnDef.cssClassFriendlyName}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  IterableChanges,\n  IterableDiffer,\n  IterableDiffers,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {CdkCellDef} from './cell';\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nexport const CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nexport abstract class BaseRowDef {\n  /** The columns to be displayed on this row. */\n  columns: string[];\n\n  /** Differ used to check if any changes were made to the columns. */\n  protected _columnsDiffer: IterableDiffer<any>;\n\n  constructor(/** @docs-private */ public template: TemplateRef<any>,\n              protected _differs: IterableDiffers) { }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    const columns = changes['columns'].currentValue || [];\n    if (!this._columnsDiffer) {\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff(): IterableChanges<any> | null {\n    return this._columnsDiffer.diff(this.columns);\n  }\n}\n\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\n@Directive({\n  selector: '[cdkHeaderRowDef]',\n  inputs: ['columns: cdkHeaderRowDef'],\n})\nexport class CdkHeaderRowDef extends BaseRowDef {\n  constructor(template: TemplateRef<any>, _differs: IterableDiffers) {\n    super(template, _differs);\n  }\n}\n\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\n@Directive({\n  selector: '[cdkRowDef]',\n  inputs: ['columns: cdkRowDefColumns', 'when: cdkRowDefWhen'],\n})\nexport class CdkRowDef<T> extends BaseRowDef {\n  /**\n   * Function that should return true if this row template should be used for the provided index\n   * and row data. If left undefined, this row will be considered the default row template to use\n   * when no other when functions return true for the data.\n   * For every row, there must be at least one when function that passes or an undefined to default.\n   */\n  when: (index: number, rowData: T) => boolean;\n\n  // TODO(andrewseguin): Add an input for providing a switch function to determine\n  //   if this template should be used.\n  constructor(template: TemplateRef<any>, _differs: IterableDiffers) {\n    super(template, _differs);\n  }\n}\n\n/** Context provided to the row cells */\nexport interface CdkCellOutletRowContext<T> {\n  /** Data for the row that this cell is located within. */\n  $implicit: T;\n\n  /** Index location of the row that this cell is located within. */\n  index?: number;\n\n  /** Length of the number of total rows. */\n  count?: number;\n\n  /** True if this cell is contained in the first row. */\n  first?: boolean;\n\n  /** True if this cell is contained in the last row. */\n  last?: boolean;\n\n  /** True if this cell is contained in a row with an even-numbered index. */\n  even?: boolean;\n\n  /** True if this cell is contained in a row with an odd-numbered index. */\n  odd?: boolean;\n}\n\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\n@Directive({selector: '[cdkCellOutlet]'})\nexport class CdkCellOutlet {\n  /** The ordered list of cells to render within this outlet's view container */\n  cells: CdkCellDef[];\n\n  /** The data context to be provided to each cell */\n  context: any;\n\n  /**\n   * Static property containing the latest constructed instance of this class.\n   * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n   * createEmbeddedView. After one of these components are created, this property will provide\n   * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n   * construct the cells with the provided context.\n   */\n  static mostRecentCellOutlet: CdkCellOutlet | null = null;\n\n  constructor(public _viewContainer: ViewContainerRef) {\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n}\n\n/** Header template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-header-row',\n  template: CDK_ROW_TEMPLATE,\n  host: {\n    'class': 'cdk-header-row',\n    'role': 'row',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n})\nexport class CdkHeaderRow { }\n\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-row',\n  template: CDK_ROW_TEMPLATE,\n  host: {\n    'class': 'cdk-row',\n    'role': 'row',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n})\nexport class CdkRow { }\n"], "names": ["observableOf", "tslib_1.__extends"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AMQA;;;;AAkBA,AAAO,IAAM,gBAAgB,GAAG,6CAA6C,CAAC;;;;;;AAM9E,IAAA,UAAA,kBAAA,YAAA;IAOE,SAAF,UAAA,CAA0C,QAA1C,EACwB,QAAyB,EADjD;QAA0C,IAA1C,CAAA,QAAkD,GAAR,QAAQ,CAAlD;QACwB,IAAxB,CAAA,QAAgC,GAAR,QAAQ,CAAiB;KAAK;;;;;IAEpD,UAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,UAAY,OAAsB,EAApC;;;QAGI,qBAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,YAAY,IAAI,EAAE,CAAC;QACtD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;YAC3D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACnC;KACF,CAAH;;;;;;;;;;IAME,UAAF,CAAA,SAAA,CAAA,cAAgB;;;;;IAAd,YAAF;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC/C,CAAH;IA1DA,OAAA,UAAA,CAAA;CA2DA,EAAA,CAAC,CAAA;;;;;;IAUoCC,SAArC,CAAA,eAAA,EAAA,MAAA,CAAA,CAA+C;IAC7C,SAAF,eAAA,CAAc,QAA0B,EAAE,QAAyB,EAAnE;QACA,OAAI,MAAJ,CAAA,IAAA,CAAA,IAAA,EAAU,QAAQ,EAAE,QAAQ,CAAC,IAA7B,IAAA,CAAA;KACG;;QAPH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,mBAAmB;oBAC7B,MAAM,EAAE,CAAC,0BAA0B,CAAC;iBACrC,EAAD,EAAA;;;;QApDA,EAAA,IAAA,EAAE,WAAW,GAAb;QAFA,EAAA,IAAA,EAAE,eAAe,GAAjB;;IAdA,OAAA,eAAA,CAAA;CAqEA,CAAqC,UAAU,CAA/C,CAAA,CAAA;AAAA;;;;;;IAekCA,SAAlC,CAAA,SAAA,EAAA,MAAA,CAAA,CAA4C;;;IAW1C,SAAF,SAAA,CAAc,QAA0B,EAAE,QAAyB,EAAnE;QACA,OAAI,MAAJ,CAAA,IAAA,CAAA,IAAA,EAAU,QAAQ,EAAE,QAAQ,CAAC,IAA7B,IAAA,CAAA;KACG;;QAjBH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,aAAa;oBACvB,MAAM,EAAE,CAAC,2BAA2B,EAAE,qBAAqB,CAAC;iBAC7D,EAAD,EAAA;;;;QAnEA,EAAA,IAAA,EAAE,WAAW,GAAb;QAFA,EAAA,IAAA,EAAE,eAAe,GAAjB;;IAdA,OAAA,SAAA,CAAA;CAoFA,CAAkC,UAAU,CAA5C,CAAA,CAAA;AAAA;;;;;;;;;;IA6DE,SAAF,aAAA,CAAqB,cAAgC,EAArD;QAAqB,IAArB,CAAA,cAAmC,GAAd,cAAc,CAAkB;QACjD,aAAa,CAAC,oBAAoB,GAAG,IAAI,CAAC;KAC3C;;;;;;;;IAJH,aAAA,CAAA,oBAAA,GAAsD,IAAI,CAA1D;;QAfA,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,EAAC,QAAQ,EAAE,iBAAiB,EAAC,EAAxC,EAAA;;;;QA/GA,EAAA,IAAA,EAAE,gBAAgB,GAAlB;;IAjBA,OAAA,aAAA,CAAA;;AAiIA;;;;;;;QAsBA,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,CAAX,QAAA,EAAA,gBAAA;oBACE,QAAQ,EAAE,gBAAZ;oBACE,IAAF,EAAA;wBACA,OAAA,EAAA,gBAAA;wBACM,MAAN,EAAA,KAAA;qBACA;oBACA,eAAA,EAAiB,uBAAjB,CAAA,MAAA;oBACA,aAAA,EAAA,iBAAA,CAAA,IAAA;oBACE,mBAAF,EAAA,KAAA;iBACA,EAAA,EAAA;KACA,CAAA;;;;;AAjKA;;;;;;;;;oBAsKA,IAAA,EAAA;wBACA,OAAA,EAAA,SAAA;wBACA,MAAY,EAAZ,KAAA;qBACA;oBACE,eAAF,EAAA,uBAAA,CAAA,MAAA;oBACA,aAAa,EAAb,iBAAA,CAAA,IAAA;oBACA,mBAAA,EAAA,KAAA;iBACA,EAAA,EAAG;KACH,CAAA;;IAEA,MAAA,CAAA,cAAA,GAAA,YAAA,EAAA,OAAA,EAAA,CAAA,EAAA,CAAA;IACA,OAAA,MAAC,CAAD;;;;;;;;ADzKA;;;;;IAQE,SAAF,UAAA,CAA0C,QAA1C,EAAA;QAA0C,IAA1C,CAAA,QAAkD,GAAR,QAAQ,CAAlD;KAAyE;;QAFzE,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,EAAC,QAAQ,EAAE,cAAc,EAAC,EAArC,EAAA;;;;QANA,EAAA,IAAA,EAAoD,WAAW,GAA/D;;IARA,OAAA,UAAA,CAAA;;AAeA;;;;;IAUE,SAAF,gBAAA,CAA0C,QAA1C,EAAA;QAA0C,IAA1C,CAAA,QAAkD,GAAR,QAAQ,CAAlD;KAAyE;;QAFzE,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,EAAC,QAAQ,EAAE,oBAAoB,EAAC,EAA3C,EAAA;;;;QAfA,EAAA,IAAA,EAAoD,WAAW,GAA/D;;IARA,OAAA,gBAAA,CAAA;;AAwBA;;;;;;;IAYA,MAAA,CAAA,cAAA,CAAM,YAAN,CAAA,SAAA,EAAA,MAAU,EAAV;;;;;QAAA,YAAA,EAAuB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAzC;;;;;QACE,UAAS,IAAY,EAAvB;;;YAGI,IAAI,CAAC,IAAI,EAAE;gBAAE,OAAO;aAAE;YAEtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;SAChE;;;;;QAZH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,EAAC,QAAQ,EAAE,gBAAgB,EAAC,EAAvC,EAAA;;;;;QAGA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,IAAA,EAAA,CAAS,cAAc,EAAvB,EAAA,EAAA;QAaA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAG,YAAY,EAAf,IAAA,EAAA,CAAgB,UAAU,EAA1B,EAAA,EAAA;QAGA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAG,YAAY,EAAf,IAAA,EAAA,CAAgB,gBAAgB,EAAhC,EAAA,EAAA;;IAnDA,OAAA,YAAA,CAAA;;AAiCA;;;;IAqCE,SAAF,aAAA,CAAc,SAAuB,EAAE,UAAsB,EAA7D;QACI,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,aAA3C,GAAyD,SAAS,CAAC,oBAAsB,CAAC,CAAC;KACxF;;QAVH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,iBAAiB;oBAC3B,IAAI,EAAE;wBACJ,OAAO,EAAE,iBAAiB;wBAC1B,MAAM,EAAE,cAAc;qBACvB;iBACF,EAAD,EAAA;;;;QAnCA,EAAA,IAAA,EAAa,YAAY,GAAzB;QAzBA,EAAA,IAAA,EAAiC,UAAU,GAA3C;;IARA,OAAA,aAAA,CAAA;;AAqEA;;;;IAeE,SAAF,OAAA,CAAc,SAAuB,EAAE,UAAsB,EAA7D;QACI,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,aAA3C,GAAyD,SAAS,CAAC,oBAAsB,CAAC,CAAC;KACxF;;QAVH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,UAAU;oBACpB,IAAI,EAAE;wBACJ,OAAO,EAAE,UAAU;wBACnB,MAAM,EAAE,UAAU;qBACnB;iBACF,EAAD,EAAA;;;;QAjDA,EAAA,IAAA,EAAa,YAAY,GAAzB;QAzBA,EAAA,IAAA,EAAiC,UAAU,GAA3C;;IARA,OAAA,OAAA,CAAA;KAmFA;;;;;;;;;;;;;ADtEA,AAAA,SAAA,0BAAA,CAA2C,EAAU,EAArD;IACE,OAAO,KAAK,CAAC,kCAAf,GAAiD,EAAE,GAAnD,KAAuD,CAAC,CAAC;CACxD;;;;;;;AAMD,AAAA,SAAA,gCAAA,CAAiD,IAAY,EAA7D;IACE,OAAO,KAAK,CAAC,+CAAf,GAA8D,IAAI,GAAlE,KAAsE,CAAC,CAAC;CACvE;;;;;;AAMD,AAAA,SAAA,mCAAA,GAAA;IACE,OAAO,KAAK,CAAC,sEAAsE,CAAC,CAAC;CACtF;;;;;;AAMD,AAAA,SAAA,kCAAA,GAAA;IACE,OAAO,KAAK,CAAC,qEAAqE,CAAC,CAAC;CACrF;;;;;;AAMD,AAAA,SAAA,2BAAA,GAAA;IACE,OAAO,KAAK,CAAC,0CAA0C;QACnD,oDAAoD,CAAC,CAAC;CAC3D;;;;;;AAMD,AAAA,SAAA,8BAAA,GAAA;IACE,OAAO,KAAK,CAAC,wEAAwE,CAAC,CAAC;CACxF;;;;;;;ADhDD,AAuBA,AACA,AACA,AACA,AAEA,AACA,AACA,AAQA,AACA;;;;;IAQE,SAAF,cAAA,CAAqB,aAA+B,EAApD;QAAqB,IAArB,CAAA,aAAkC,GAAb,aAAa,CAAkB;KAAK;;QAFzD,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,EAAC,QAAQ,EAAE,kBAAkB,EAAC,EAAzC,EAAA;;;;QAzBA,EAAA,IAAA,EAAE,gBAAgB,GAAlB;;IA5BA,OAAA,cAAA,CAAA;;AAsDA;;;;;IAUE,SAAF,oBAAA,CAAqB,aAA+B,EAApD;QAAqB,IAArB,CAAA,aAAkC,GAAb,aAAa,CAAkB;KAAK;;QAFzD,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,EAAC,QAAQ,EAAE,wBAAwB,EAAC,EAA/C,EAAA;;;;QAlCA,EAAA,IAAA,EAAE,gBAAgB,GAAlB;;IA5BA,OAAA,oBAAA,CAAA;;AA+DA;;;;AAQA,AAAO,IAAM,kBAAkB,GAAG,wGAEa,CAAC;;;;;;AAMhD,IAAA,UAAA,kBAAA,UAAA,MAAA,EAAA;IAAqCA,SAArC,CAAA,UAAA,EAAA,MAAA,CAAA,CAAgF;;;;IA/EhF,OAAA,UAAA,CAAA;CA+EA,CAAqC,eAAe,CAApD,CAAoF,CAAA;;;;;;;;IA0IlF,SAAF,QAAA,CAA+B,QAAyB,EACzB,kBAD/B,EAEc,UAAsB,EACH,IAHjC,EAAA;QAA+B,IAA/B,CAAA,QAAuC,GAAR,QAAQ,CAAiB;QACzB,IAA/B,CAAA,kBAAiD,GAAlB,kBAAkB,CAAjD;;;;QArHA,IAAA,CAAA,UAAA,GAAuB,IAAI,OAAO,EAAQ,CAA1C;;;;;;QAaA,IAAA,CAAA,iBAAA,GAA8B,IAAI,GAAG,EAAyB,CAA9D;;;;QAeA,IAAA,CAAA,iBAAA,GAA8B,IAAI,GAAG,EAAgB,CAArD;;;;QAGA,IAAA,CAAA,cAAA,GAA2B,IAAI,GAAG,EAAgB,CAAlD;;;;;QAMA,IAAA,CAAA,oBAAA,GAAiC,KAAK,CAAtC;;;;;QAwDA,IAAA,CAAA,UAAA,GAAM,IAAI,eAAe,CAA+B,EAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,SAAS,EAAC,CAAC,CAA1F;QA2BI,IAAI,CAAC,IAAI,EAAE;YACT,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACvD;KACF;IA7EH,MAAA,CAAA,cAAA,CAAM,QAAN,CAAA,SAAA,EAAA,SAAa,EAAb;;;;;;;;QAAA,YAAA,EAAsC,OAAO,IAAI,CAAC,UAAU,CAAC,EAA7D;;;;;QACE,UAAY,EAAsB,EAApC;YACI,IAAI,SAAS,EAAE;gBACX,EAAE,IAAI,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU,sBACjC,OAAO,CAAA,sBAAS,OAAO,CAAC,IAAI,CAAA,EAAE;gBACnC,OAAO,CAAC,IAAI,CAAC,2CAArB,GAAiE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAnF,GAAsF,CAAC,CAAC;aACnF;YACD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACtB;;;;IAwBH,MAAA,CAAA,cAAA,CAAM,QAAN,CAAA,SAAA,EAAA,YAAgB,EAAhB;;;;;;;;;;;;;;;;;;;;;;QAAA,YAAA,EAA4D,OAAO,IAAI,CAAC,WAAW,CAAC,EAApF;;;;;QACE,UAAe,UAAiD,EAAlE;YACI,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;gBACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;aACpC;SACF;;;;;;;IA0CD,QAAF,CAAA,SAAA,CAAA,QAAU;;;IAAR,YAAF;;QAEI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;;;QAIlE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAClC;KACF,CAAH;;;;IAEE,QAAF,CAAA,SAAA,CAAA,qBAAuB;;;IAArB,YAAF;;QAEI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;;QAGxB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAChD,MAAM,2BAA2B,EAAE,CAAC;SACrC;;QAGD,IAAI,CAAC,qBAAqB,EAAE,CAAC;;QAG7B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;SACnC;;;QAID,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAClF,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;KACF,CAAH;;;;IAEE,QAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,UAAU,YAAY,UAAU,EAAE;YACzC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAClC;KACF,CAAH;;;;;;;;;;;;;;;;;;;;;;IAYE,QAAF,CAAA,SAAA,CAAA,UAAY;;;;;;;;;;;IAAV,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CA0BG;QAzBC,qBAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO;SAAE;QAEzB,qBAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;QACzD,OAAO,CAAC,gBAAgB,CACpB,UAAC,MAA+B,EAAE,qBAA6B,EAAE,YAAoB,EAD7F;YAEU,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;gBAChC,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;aAC5C;iBAAM,IAAI,YAAY,IAAI,IAAI,EAAE;gBAC/B,aAAa,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;aAC7C;iBAAM;gBACL,qBAAM,IAAI,qBAAkB,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA,CAAC;gBACrE,aAAa,CAAC,IAAI,oBAAC,IAAI,IAAG,YAAY,CAAC,CAAC;aACzC;SACF,CAAC,CAAC;;QAGP,IAAI,CAAC,sBAAsB,EAAE,CAAC;;;QAI9B,OAAO,CAAC,qBAAqB,CAAC,UAAC,MAA+B,EAAlE;YACM,qBAAM,OAAO,qBAAkB,aAAa,CAAC,GAAG,oBAAC,MAAM,CAAC,YAAY,GAAE,CAAA,CAAC;YACvE,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC;SACzC,CAAC,CAAC;KACJ,CAAH;;;;;;;;;;;;;IAOE,QAAF,CAAA,SAAA,CAAA,eAAiB;;;;;;;IAAf,UAAgB,YAA6B,EAA/C;QACI,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;KAClC,CAAH;;;;;;;IAGE,QAAF,CAAA,SAAA,CAAA,YAAc;;;;;IAAZ,UAAa,SAAuB,EAAtC;QACI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KACvC,CAAH;;;;;;;IAGE,QAAF,CAAA,SAAA,CAAA,eAAiB;;;;;IAAf,UAAgB,SAAuB,EAAzC;QACI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KAC1C,CAAH;;;;;;;IAGE,QAAF,CAAA,SAAA,CAAA,SAAW;;;;;IAAT,UAAU,MAAoB,EAAhC;QACI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KACjC,CAAH;;;;;;;IAGE,QAAF,CAAA,SAAA,CAAA,YAAc;;;;;IAAZ,UAAa,MAAoB,EAAnC;QACI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KACpC,CAAH;;;;;IAGU,QAAV,CAAA,SAAA,CAAA,gBAA0B;;;;;;QACtB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,qBAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;QACpF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAA,SAAS,EAA5C,EAAgD,OAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAA1E,EAA0E,CAAC,CAAC;QAExE,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS,EAAhC;YACM,IAAI,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBAC9C,MAAM,gCAAgC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aACxD;YACD,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACvD,CAAC,CAAC;;;;;;IAIG,QAAV,CAAA,SAAA,CAAA,aAAuB;;;;;;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;QAC3E,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,MAAM,EAAtC,EAA0C,OAAA,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAApE,EAAoE,CAAC,CAAC;QAElE,qBAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAA,GAAG,EAAnD,EAAuD,OAAA,CAAC,GAAG,CAAC,IAAI,CAAhE,EAAgE,CAAC,CAAC;QAC9D,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAAE,MAAM,mCAAmC,EAAE,CAAC;SAAE;QAC/E,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;;;;;;;IAOlC,QAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;;;;QAE3B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,GAAG,EAA7B;YACM,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE;;;gBAE1B,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAE1B,KAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;gBAC3C,KAAI,CAAC,UAAU,EAAE,CAAC;aACnB;SACF,CAAC,CAAC;;QAGH,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE;YAC7D,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;;;;;;;;;IAQK,QAAV,CAAA,SAAA,CAAA,iBAA2B;;;;;;;IAA3B,UAA4B,UAAiD,EAA7E;QACI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,UAAU,YAAY,UAAU,EAAE;YACzC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAClC;;QAGD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACvC;QAED,IAAI,CAAC,UAAU,EAAE;YACf,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;SAC5C;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;;;;;;IAIxB,QAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;;;QAE3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO;SAAE;QAEjC,qBAAI,UAAuC,CAAC;;;;;QAM5C,IAAI,mBAAC,IAAI,CAAC,UAA2B,GAAE,OAAO,YAAa,QAAQ,EAAE;YACnE,UAAU,GAAG,mBAAC,IAAI,CAAC,UAA2B,GAAE,OAAO,CAAC,IAAI,CAAC,CAAC;SAC/D;aAAM,IAAI,IAAI,CAAC,UAAU,YAAY,UAAU,EAAE;YAChD,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;SAC9B;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACzC,UAAU,GAAGD,EAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC5C;QAED,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,MAAM,8BAA8B,EAAE,CAAC;SACxC;QAED,IAAI,CAAC,yBAAyB,GAAG,UAAU;aACtC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,UAAA,IAAI,EAAvB;YACU,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,KAAI,CAAC,UAAU,EAAE,CAAC;SACnB,CAAC,CAAC;;;;;;;IAOD,QAAV,CAAA,SAAA,CAAA,gBAA0B;;;;;;;QAEtB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YACvD,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;SAClD;QAED,qBAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAAE,OAAO;SAAE;;;;QAK9B,IAAI,CAAC,qBAAqB,CAAC,aAAa;aACnC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAC,KAAK,EAA/D,KAA+D,EAAC,CAAC,CAAC;QAE9D,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,EAAtB;YACM,IAAI,aAAa,CAAC,oBAAoB,EAAE;gBACtC,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;aACzF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;;;;;;;;;;;;;;;;IASzC,QAAF,CAAA,SAAA,CAAA,UAAY;;;;;;;;;IAAV,UAAW,IAAO,EAAE,CAAS,EAA/B;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAAE;QAE3D,qBAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,GAAG,EAAvC,EAA2C,OAAA,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAxE,EAAwE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC;QAC7F,IAAI,CAAC,MAAM,EAAE;YAAE,MAAM,kCAAkC,EAAE,CAAC;SAAE;QAE5D,OAAO,MAAM,CAAC;KACf,CAAH;;;;;;;;IAMU,QAAV,CAAA,SAAA,CAAA,UAAoB;;;;;;;IAApB,UAAqB,OAAU,EAAE,KAAa,EAA9C;QACI,qBAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;;QAG5C,qBAAM,OAAO,GAA+B,EAAC,SAAS,EAAE,OAAO,EAAC,CAAC;;;QAIjE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpF,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI,EAAlD;YACM,IAAI,aAAa,CAAC,oBAAoB,EAAE;gBACtC,aAAa,CAAC,oBAAoB,CAAC,cAAc;qBAC5C,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;aACjD;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;;;;;;IAOjC,QAAV,CAAA,SAAA,CAAA,sBAAgC;;;;;;QAC5B,qBAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;QACzD,KAAK,qBAAI,KAAK,GAAG,CAAC,mBAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;YACxE,qBAAM,OAAO,qBAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAkB,CAAA,CAAC;YAC1D,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC,CAAC;YACpC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC;YAC3C,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;YACvC,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;SAC7C;;;;;;;;IAOK,QAAV,CAAA,SAAA,CAAA,6BAAuC;;;;;;IAAvC,UAAwC,SAA0B,EAAlE;;QACI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAAE,OAAO,EAAE,CAAC;SAAE;QACpD,OAAO,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,QAAQ,EAAzC;YACM,qBAAM,MAAM,GAAG,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,0BAA0B,CAAC,QAAQ,CAAC,CAAC;aAC5C;YAED,OAAO,MAAM,CAAC,UAAU,CAAC;SAC1B,CAAC,CAAC;;;;;;;;IAOG,QAAV,CAAA,SAAA,CAAA,uBAAiC;;;;;;IAAjC,UAAkC,MAAoB,EAAtD;;QACI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YAAE,OAAO,EAAE,CAAC;SAAE;QACnC,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,QAAQ,EAAtC;YACM,qBAAM,MAAM,GAAG,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,0BAA0B,CAAC,QAAQ,CAAC,CAAC;aAC5C;YAED,OAAO,MAAM,CAAC,IAAI,CAAC;SACpB,CAAC,CAAC;;;QA5dP,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW,CAAX,QAAA,EAAA,WAAA;oBACE,QAAQ,EAAE,UAAZ;oBACE,QAAQ,EAAE,kBAAZ;oBACE,IAAF,EAAA;wBACA,OAAA,EAAA,WAAA;qBACA;oBACA,aAAa,EAAb,iBAAA,CAAA,IAAA;oBACA,mBAAA,EAAA,KAAA;oBACE,eAAe,EAAjB,uBAAA,CAAA,MAAA;iBACA,EAAA,EAAA;KACA,CAAA;;;;;QA1EA,EAAA,IAAA,EAAE,UAAF,GAAA;QAXA,EAAA,IAAA,EAAE,SAAF,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;KAKA,CAAA,EAAA,CAAA;IA2MA,QAAA,CAAA,cAAA,GAAA;;;QA1EA,iBAAA,EAAA,CAAG,EAAH,IAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA;QAgCA,uBAAG,EAAH,CAAA,EAAQ,IAAR,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,oBAAA,EAAA,EAAA,EAAA;QAmBA,oBAAA,EAAA,CAAA,EAAA,IAAA,EAAA,eAAa,EAAb,IAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA;QACA,iBAAA,EAAA,CAAA,EAAA,IAAA,EAAA,eAAY,EAAZ,IAAA,EAAA,CAAa,SAAb,EAAA,EAAA,EAAA;QAMA,eAAA,EAAA,CAAA,EAAA,IAAA,EAAA,YAAA,EAAA,IAAA,EAAkB,CAAlB,eAAA,EAAA,EAAA,EAAA;KAGA,CAAA;IAQA,OAAA,QAAA,CAAA;KAvNA;;;;;;;ADQA,AACA,AACA,AACA,AACA,AAEA,IAAM,qBAAqB,GAAG;IAC5B,QAAQ;IACR,SAAS;IACT,UAAU;IACV,aAAa;IACb,gBAAgB;IAChB,YAAY;IACZ,OAAO;IACP,MAAM;IACN,aAAa;IACb,YAAY;IACZ,eAAe;IACf,cAAc;IACd,oBAAoB;CACrB,CAAC;;;;;QAEF,EAAA,IAAA,EAAC,QAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,qBAAqB,CAAC;oBAChC,YAAY,EAAE,CAAC,qBAAqB,CAAC;iBAEtC,EAAD,EAAA;;;;IAnCA,OAAA,cAAA,CAAA;KAoCA;;;;;GD5BA,AACA,AACA,AACA,AAGA,AAAoD;;;;;;;;GDVpD,AAA6B;;"}