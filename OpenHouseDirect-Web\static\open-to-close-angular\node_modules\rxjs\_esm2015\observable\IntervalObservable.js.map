{"version": 3, "file": "IntervalObservable.js", "sourceRoot": "", "sources": ["../../src/observable/IntervalObservable.ts"], "names": [], "mappings": "OACO,EAAE,SAAS,EAAE,MAAM,mBAAmB;OAEtC,EAAE,UAAU,EAAE,MAAM,eAAe;OACnC,EAAE,KAAK,EAAE,MAAM,oBAAoB;AAE1C;;;;GAIG;AACH,wCAAwC,UAAU;IAqDhD,YAAoB,MAAM,GAAW,CAAC,EAClB,SAAS,GAAe,KAAK;QAC/C,OAAO,CAAC;QAFU,WAAM,GAAN,MAAM,CAAY;QAClB,cAAS,GAAT,SAAS,CAAoB;QAE/C,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAClB,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IA7DD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,OAAO,MAAM,CAAC,MAAM,GAAW,CAAC,EAClB,SAAS,GAAe,KAAK;QACzC,MAAM,CAAC,IAAI,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,QAAQ,CAAC,KAAU;QACxB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAE5C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;QAEV,IAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IAaD,oCAAoC,CAAC,UAAU,CAAC,UAA8B;QAC5E,MAAM,KAAK,GAAG,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE;YACrE,KAAK,EAAE,UAAU,EAAE,MAAM;SAC1B,CAAC,CAAC,CAAC;IACN,CAAC;AACH,CAAC;AAAA"}