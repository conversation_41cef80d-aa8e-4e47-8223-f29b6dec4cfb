{"__symbolic": "module", "version": 4, "metadata": {"FocusTrapDirective": {"__symbolic": "reference", "name": "CdkTrapFocus"}, "RegisteredMessage": {"__symbolic": "interface"}, "MESSAGES_CONTAINER_ID": "cdk-describedby-message-container", "CDK_DESCRIBEDBY_ID_PREFIX": "cdk-describedby-message", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE": "cdk-describedby-host", "AriaDescriber": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 48, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 52, "character": 15}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 52, "character": 22}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "describe": [{"__symbolic": "method"}], "removeDescription": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "_createMessageElement": [{"__symbolic": "method"}], "_deleteMessageElement": [{"__symbolic": "method"}], "_createMessagesContainer": [{"__symbolic": "method"}], "_deleteMessagesContainer": [{"__symbolic": "method"}], "_removeCdkDescribedByReferenceIds": [{"__symbolic": "method"}], "_addMessageReference": [{"__symbolic": "method"}], "_removeMessageReference": [{"__symbolic": "method"}], "_isElementDescribedByMessage": [{"__symbolic": "method"}]}}, "ARIA_DESCRIBER_PROVIDER_FACTORY": {"__symbolic": "function", "parameters": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_document"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "AriaDescriber"}, "arguments": [{"__symbolic": "reference", "name": "_document"}]}}}, "ARIA_DESCRIBER_PROVIDER": {"provide": {"__symbolic": "reference", "name": "AriaDescriber"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 210, "character": 9}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 210, "character": 25}}, {"__symbolic": "reference", "name": "AriaDescriber"}], {"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 211, "character": 4}], "useFactory": {"__symbolic": "reference", "name": "ARIA_DESCRIBER_PROVIDER_FACTORY"}}, "Highlightable": {"__symbolic": "interface"}, "ActiveDescendantKeyManager": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "ListKeyManager"}, "members": {"setActiveItem": [{"__symbolic": "method"}]}}, "FocusableOption": {"__symbolic": "interface"}, "FocusKeyManager": {"__symbolic": "class", "arity": 1, "extends": {"__symbolic": "reference", "name": "ListKeyManager"}, "members": {"setFocusOrigin": [{"__symbolic": "method"}], "setActiveItem": [{"__symbolic": "method"}]}}, "ListKeyManagerOption": {"__symbolic": "interface"}, "ListKeyManager": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "QueryList", "module": "@angular/core", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 52, "character": 40, "context": {"typeName": "T"}, "module": "./key-manager/list-key-manager"}]}]}], "withWrap": [{"__symbolic": "method"}], "withVerticalOrientation": [{"__symbolic": "method"}], "withHorizontalOrientation": [{"__symbolic": "method"}], "withTypeAhead": [{"__symbolic": "method"}], "setActiveItem": [{"__symbolic": "method"}], "onKeydown": [{"__symbolic": "method"}], "setFirstItemActive": [{"__symbolic": "method"}], "setLastItemActive": [{"__symbolic": "method"}], "setNextItemActive": [{"__symbolic": "method"}], "setPreviousItemActive": [{"__symbolic": "method"}], "updateActiveItemIndex": [{"__symbolic": "method"}], "_setActiveItemByDelta": [{"__symbolic": "method"}], "_setActiveInWrapMode": [{"__symbolic": "method"}], "_setActiveInDefaultMode": [{"__symbolic": "method"}], "_setActiveItemByIndex": [{"__symbolic": "method"}]}}, "FocusTrap": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "error", "message": "Could not resolve type", "line": 47, "character": 22, "context": {"typeName": "HTMLElement"}, "module": "./focus-trap/focus-trap"}, {"__symbolic": "reference", "name": "InteractivityChecker"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 49, "character": 21}, {"__symbolic": "error", "message": "Could not resolve type", "line": 50, "character": 23, "context": {"typeName": "Document"}, "module": "./focus-trap/focus-trap"}, null]}], "destroy": [{"__symbolic": "method"}], "attachAnchors": [{"__symbolic": "method"}], "focusInitialElementWhenReady": [{"__symbolic": "method"}], "focusFirstTabbableElementWhenReady": [{"__symbolic": "method"}], "focusLastTabbableElementWhenReady": [{"__symbolic": "method"}], "_getRegionBoundary": [{"__symbolic": "method"}], "focusInitialElement": [{"__symbolic": "method"}], "focusFirstTabbableElement": [{"__symbolic": "method"}], "focusLastTabbableElement": [{"__symbolic": "method"}], "_getFirstTabbableElement": [{"__symbolic": "method"}], "_getLastTabbableElement": [{"__symbolic": "method"}], "_createAnchor": [{"__symbolic": "method"}], "_executeOnStable": [{"__symbolic": "method"}]}}, "FocusTrapFactory": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 280, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 287, "character": 7}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 287, "character": 14}]}]], "parameters": [{"__symbolic": "reference", "name": "InteractivityChecker"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 49, "character": 21}, {"__symbolic": "reference", "name": "any"}]}], "create": [{"__symbolic": "method"}]}}, "FocusTrapDeprecatedDirective": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 312, "character": 1}, "arguments": [{"selector": "cdk-focus-trap"}]}], "members": {"disabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 319, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 325, "character": 35}, {"__symbolic": "reference", "name": "FocusTrapFactory"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "ngAfterContentInit": [{"__symbolic": "method"}]}}, "CdkTrapFocus": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 340, "character": 1}, "arguments": [{"selector": "[cdkTrapFocus]", "exportAs": "cdkTrapFocus"}]}], "members": {"enabled": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 354, "character": 3}, "arguments": ["cdkTrapFocus"]}]}], "autoCapture": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 362, "character": 3}, "arguments": ["cdkTrapFocusAutoCapture"]}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 370, "character": 7}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 370, "character": 14}]}]], "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 325, "character": 35}, {"__symbolic": "reference", "name": "FocusTrapFactory"}, {"__symbolic": "reference", "name": "any"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "ngAfterContentInit": [{"__symbolic": "method"}]}}, "InteractivityChecker": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 20, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 23, "character": 33}]}], "isDisabled": [{"__symbolic": "method"}], "isVisible": [{"__symbolic": "method"}], "isTabbable": [{"__symbolic": "method"}], "isFocusable": [{"__symbolic": "method"}]}}, "LIVE_ANNOUNCER_ELEMENT_TOKEN": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken", "line": 19, "character": 48}, "arguments": ["liveAnnouncerElement"]}, "AriaLivePoliteness": {"__symbolic": "interface"}, "LiveAnnouncer": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 24, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 29, "character": 7}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 29, "character": 19}, "arguments": [{"__symbolic": "reference", "name": "LIVE_ANNOUNCER_ELEMENT_TOKEN"}]}], [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 30, "character": 7}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 30, "character": 14}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "any"}]}], "announce": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "_createLiveElement": [{"__symbolic": "method"}]}}, "LIVE_ANNOUNCER_PROVIDER_FACTORY": {"__symbolic": "function", "parameters": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liveElement", "_document"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "LiveAnnouncer"}, "arguments": [{"__symbolic": "reference", "name": "liveElement"}, {"__symbolic": "reference", "name": "_document"}]}}}, "LIVE_ANNOUNCER_PROVIDER": {"provide": {"__symbolic": "reference", "name": "LiveAnnouncer"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 94, "character": 9}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 94, "character": 25}}, {"__symbolic": "reference", "name": "LiveAnnouncer"}], [{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 95, "character": 9}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 95, "character": 25}, "arguments": [{"__symbolic": "reference", "name": "LIVE_ANNOUNCER_ELEMENT_TOKEN"}]}], {"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 96, "character": 4}], "useFactory": {"__symbolic": "reference", "name": "LIVE_ANNOUNCER_PROVIDER_FACTORY"}}, "TOUCH_BUFFER_MS": 650, "FocusOrigin": {"__symbolic": "interface"}, "FocusMonitor": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 42, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 74, "character": 31}, {"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 74, "character": 58}]}], "monitor": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "stopMonitoring": [{"__symbolic": "method"}], "focusVia": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "_registerGlobalListeners": [{"__symbolic": "method"}], "_toggleClass": [{"__symbolic": "method"}], "_setClasses": [{"__symbolic": "method"}], "_setOriginForCurrentEventQueue": [{"__symbolic": "method"}], "_wasCausedByTouch": [{"__symbolic": "method"}], "_onFocus": [{"__symbolic": "method"}], "_onBlur": [{"__symbolic": "method"}], "_incrementMonitoredElementCount": [{"__symbolic": "method"}], "_decrementMonitoredElementCount": [{"__symbolic": "method"}]}}, "CdkMonitorFocus": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 380, "character": 1}, "arguments": [{"selector": "[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]"}]}], "members": {"cdkFocusChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 385, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 387, "character": 35}, {"__symbolic": "reference", "name": "FocusMonitor"}]}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "FOCUS_MONITOR_PROVIDER_FACTORY": {"__symbolic": "function", "parameters": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngZone", "platform"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "FocusMonitor"}, "arguments": [{"__symbolic": "reference", "name": "ngZone"}, {"__symbolic": "reference", "name": "platform"}]}}}, "FOCUS_MONITOR_PROVIDER": {"provide": {"__symbolic": "reference", "name": "FocusMonitor"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 410, "character": 14}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 410, "character": 30}}, {"__symbolic": "reference", "name": "FocusMonitor"}], {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 74, "character": 31}, {"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 74, "character": 58}], "useFactory": {"__symbolic": "reference", "name": "FOCUS_MONITOR_PROVIDER_FACTORY"}}, "isFakeMousedownFromScreenReader": {"__symbolic": "function", "parameters": ["event"], "value": {"__symbolic": "binop", "operator": "===", "left": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "event"}, "member": "buttons"}, "right": 0}}, "A11yModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 21, "character": 1}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/common", "name": "CommonModule", "line": 22, "character": 12}, {"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "PlatformModule", "line": 22, "character": 26}], "declarations": [{"__symbolic": "reference", "name": "CdkTrapFocus"}, {"__symbolic": "reference", "name": "FocusTrapDeprecatedDirective"}, {"__symbolic": "reference", "name": "CdkMonitorFocus"}], "exports": [{"__symbolic": "reference", "name": "CdkTrapFocus"}, {"__symbolic": "reference", "name": "FocusTrapDeprecatedDirective"}, {"__symbolic": "reference", "name": "CdkMonitorFocus"}], "providers": [{"__symbolic": "reference", "name": "InteractivityChecker"}, {"__symbolic": "reference", "name": "FocusTrapFactory"}, {"__symbolic": "reference", "name": "AriaDescriber"}, {"__symbolic": "reference", "name": "LIVE_ANNOUNCER_PROVIDER"}, {"__symbolic": "reference", "name": "ARIA_DESCRIBER_PROVIDER"}, {"__symbolic": "reference", "name": "FOCUS_MONITOR_PROVIDER"}]}]}], "members": {}}}, "origins": {"FocusTrapDirective": "./focus-trap/focus-trap", "RegisteredMessage": "./aria-describer/aria-describer", "MESSAGES_CONTAINER_ID": "./aria-describer/aria-describer", "CDK_DESCRIBEDBY_ID_PREFIX": "./aria-describer/aria-describer", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE": "./aria-describer/aria-describer", "AriaDescriber": "./aria-describer/aria-describer", "ARIA_DESCRIBER_PROVIDER_FACTORY": "./aria-describer/aria-describer", "ARIA_DESCRIBER_PROVIDER": "./aria-describer/aria-describer", "Highlightable": "./key-manager/activedescendant-key-manager", "ActiveDescendantKeyManager": "./key-manager/activedescendant-key-manager", "FocusableOption": "./key-manager/focus-key-manager", "FocusKeyManager": "./key-manager/focus-key-manager", "ListKeyManagerOption": "./key-manager/list-key-manager", "ListKeyManager": "./key-manager/list-key-manager", "FocusTrap": "./focus-trap/focus-trap", "FocusTrapFactory": "./focus-trap/focus-trap", "FocusTrapDeprecatedDirective": "./focus-trap/focus-trap", "CdkTrapFocus": "./focus-trap/focus-trap", "InteractivityChecker": "./interactivity-checker/interactivity-checker", "LIVE_ANNOUNCER_ELEMENT_TOKEN": "./live-announcer/live-announcer", "AriaLivePoliteness": "./live-announcer/live-announcer", "LiveAnnouncer": "./live-announcer/live-announcer", "LIVE_ANNOUNCER_PROVIDER_FACTORY": "./live-announcer/live-announcer", "LIVE_ANNOUNCER_PROVIDER": "./live-announcer/live-announcer", "TOUCH_BUFFER_MS": "./focus-monitor/focus-monitor", "FocusOrigin": "./focus-monitor/focus-monitor", "FocusMonitor": "./focus-monitor/focus-monitor", "CdkMonitorFocus": "./focus-monitor/focus-monitor", "FOCUS_MONITOR_PROVIDER_FACTORY": "./focus-monitor/focus-monitor", "FOCUS_MONITOR_PROVIDER": "./focus-monitor/focus-monitor", "isFakeMousedownFromScreenReader": "./fake-mousedown", "A11yModule": "./a11y-module"}, "importAs": "@angular/cdk/a11y"}