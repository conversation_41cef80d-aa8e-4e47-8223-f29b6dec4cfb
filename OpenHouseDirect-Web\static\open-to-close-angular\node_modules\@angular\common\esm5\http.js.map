{"version": 3, "file": "http.js", "sources": ["../../../packages/common/esm5/http/src/backend.js", "../../../packages/common/esm5/http/src/headers.js", "../../../packages/common/esm5/http/src/params.js", "../../../packages/common/esm5/http/src/request.js", "../../../packages/common/esm5/http/src/response.js", "../../../packages/common/esm5/http/src/client.js", "../../../packages/common/esm5/http/src/interceptor.js", "../../../packages/common/esm5/http/src/jsonp.js", "../../../packages/common/esm5/http/src/xhr.js", "../../../packages/common/esm5/http/src/xsrf.js", "../../../packages/common/esm5/http/src/module.js", "../../../packages/common/esm5/http/public_api.js", "../../../packages/common/esm5/http/http.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * \\@stable\n * @abstract\n */\nHttpHandler = /** @class */ (function () {\n    function HttpHandler() {\n    }\n    return HttpHandler;\n}());\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * \\@stable\n * @abstract\n */\nexport { HttpHandler };\nfunction HttpHandler_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?} req\n     * @return {?}\n     */\n    HttpHandler.prototype.handle = function (req) { };\n}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * \\@stable\n * @abstract\n */\nHttpBackend = /** @class */ (function () {\n    function HttpBackend() {\n    }\n    return HttpBackend;\n}());\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * \\@stable\n * @abstract\n */\nexport { HttpBackend };\nfunction HttpBackend_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?} req\n     * @return {?}\n     */\n    HttpBackend.prototype.handle = function (req) { };\n}\n//# sourceMappingURL=backend.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @record\n */\nfunction Update() { }\nfunction Update_tsickle_Closure_declarations() {\n    /** @type {?} */\n    Update.prototype.name;\n    /** @type {?|undefined} */\n    Update.prototype.value;\n    /** @type {?} */\n    Update.prototype.op;\n}\n/**\n * Immutable set of Http headers, with lazy parsing.\n * \\@stable\n */\nvar /**\n * Immutable set of Http headers, with lazy parsing.\n * \\@stable\n */\nHttpHeaders = /** @class */ (function () {\n    function HttpHeaders(headers) {\n        var _this = this;\n        /**\n         * Internal map of lowercased header names to the normalized\n         * form of the name (the form seen first).\n         */\n        this.normalizedNames = new Map();\n        /**\n         * Queued updates to be materialized the next initialization.\n         */\n        this.lazyUpdate = null;\n        if (!headers) {\n            this.headers = new Map();\n        }\n        else if (typeof headers === 'string') {\n            this.lazyInit = function () {\n                _this.headers = new Map();\n                headers.split('\\n').forEach(function (line) {\n                    var /** @type {?} */ index = line.indexOf(':');\n                    if (index > 0) {\n                        var /** @type {?} */ name_1 = line.slice(0, index);\n                        var /** @type {?} */ key = name_1.toLowerCase();\n                        var /** @type {?} */ value = line.slice(index + 1).trim();\n                        _this.maybeSetNormalizedName(name_1, key);\n                        if (_this.headers.has(key)) {\n                            /** @type {?} */ ((_this.headers.get(key))).push(value);\n                        }\n                        else {\n                            _this.headers.set(key, [value]);\n                        }\n                    }\n                });\n            };\n        }\n        else {\n            this.lazyInit = function () {\n                _this.headers = new Map();\n                Object.keys(headers).forEach(function (name) {\n                    var /** @type {?} */ values = headers[name];\n                    var /** @type {?} */ key = name.toLowerCase();\n                    if (typeof values === 'string') {\n                        values = [values];\n                    }\n                    if (values.length > 0) {\n                        _this.headers.set(key, values);\n                        _this.maybeSetNormalizedName(name, key);\n                    }\n                });\n            };\n        }\n    }\n    /**\n     * Checks for existence of header by given name.\n     */\n    /**\n     * Checks for existence of header by given name.\n     * @param {?} name\n     * @return {?}\n     */\n    HttpHeaders.prototype.has = /**\n     * Checks for existence of header by given name.\n     * @param {?} name\n     * @return {?}\n     */\n    function (name) {\n        this.init();\n        return this.headers.has(name.toLowerCase());\n    };\n    /**\n     * Returns first header that matches given name.\n     */\n    /**\n     * Returns first header that matches given name.\n     * @param {?} name\n     * @return {?}\n     */\n    HttpHeaders.prototype.get = /**\n     * Returns first header that matches given name.\n     * @param {?} name\n     * @return {?}\n     */\n    function (name) {\n        this.init();\n        var /** @type {?} */ values = this.headers.get(name.toLowerCase());\n        return values && values.length > 0 ? values[0] : null;\n    };\n    /**\n     * Returns the names of the headers\n     */\n    /**\n     * Returns the names of the headers\n     * @return {?}\n     */\n    HttpHeaders.prototype.keys = /**\n     * Returns the names of the headers\n     * @return {?}\n     */\n    function () {\n        this.init();\n        return Array.from(this.normalizedNames.values());\n    };\n    /**\n     * Returns list of header values for a given name.\n     */\n    /**\n     * Returns list of header values for a given name.\n     * @param {?} name\n     * @return {?}\n     */\n    HttpHeaders.prototype.getAll = /**\n     * Returns list of header values for a given name.\n     * @param {?} name\n     * @return {?}\n     */\n    function (name) {\n        this.init();\n        return this.headers.get(name.toLowerCase()) || null;\n    };\n    /**\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    HttpHeaders.prototype.append = /**\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    function (name, value) {\n        return this.clone({ name: name, value: value, op: 'a' });\n    };\n    /**\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    HttpHeaders.prototype.set = /**\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    function (name, value) {\n        return this.clone({ name: name, value: value, op: 's' });\n    };\n    /**\n     * @param {?} name\n     * @param {?=} value\n     * @return {?}\n     */\n    HttpHeaders.prototype.delete = /**\n     * @param {?} name\n     * @param {?=} value\n     * @return {?}\n     */\n    function (name, value) {\n        return this.clone({ name: name, value: value, op: 'd' });\n    };\n    /**\n     * @param {?} name\n     * @param {?} lcName\n     * @return {?}\n     */\n    HttpHeaders.prototype.maybeSetNormalizedName = /**\n     * @param {?} name\n     * @param {?} lcName\n     * @return {?}\n     */\n    function (name, lcName) {\n        if (!this.normalizedNames.has(lcName)) {\n            this.normalizedNames.set(lcName, name);\n        }\n    };\n    /**\n     * @return {?}\n     */\n    HttpHeaders.prototype.init = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        if (!!this.lazyInit) {\n            if (this.lazyInit instanceof HttpHeaders) {\n                this.copyFrom(this.lazyInit);\n            }\n            else {\n                this.lazyInit();\n            }\n            this.lazyInit = null;\n            if (!!this.lazyUpdate) {\n                this.lazyUpdate.forEach(function (update) { return _this.applyUpdate(update); });\n                this.lazyUpdate = null;\n            }\n        }\n    };\n    /**\n     * @param {?} other\n     * @return {?}\n     */\n    HttpHeaders.prototype.copyFrom = /**\n     * @param {?} other\n     * @return {?}\n     */\n    function (other) {\n        var _this = this;\n        other.init();\n        Array.from(other.headers.keys()).forEach(function (key) {\n            _this.headers.set(key, /** @type {?} */ ((other.headers.get(key))));\n            _this.normalizedNames.set(key, /** @type {?} */ ((other.normalizedNames.get(key))));\n        });\n    };\n    /**\n     * @param {?} update\n     * @return {?}\n     */\n    HttpHeaders.prototype.clone = /**\n     * @param {?} update\n     * @return {?}\n     */\n    function (update) {\n        var /** @type {?} */ clone = new HttpHeaders();\n        clone.lazyInit =\n            (!!this.lazyInit && this.lazyInit instanceof HttpHeaders) ? this.lazyInit : this;\n        clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n        return clone;\n    };\n    /**\n     * @param {?} update\n     * @return {?}\n     */\n    HttpHeaders.prototype.applyUpdate = /**\n     * @param {?} update\n     * @return {?}\n     */\n    function (update) {\n        var /** @type {?} */ key = update.name.toLowerCase();\n        switch (update.op) {\n            case 'a':\n            case 's':\n                var /** @type {?} */ value = /** @type {?} */ ((update.value));\n                if (typeof value === 'string') {\n                    value = [value];\n                }\n                if (value.length === 0) {\n                    return;\n                }\n                this.maybeSetNormalizedName(update.name, key);\n                var /** @type {?} */ base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n                base.push.apply(base, value);\n                this.headers.set(key, base);\n                break;\n            case 'd':\n                var /** @type {?} */ toDelete_1 = /** @type {?} */ (update.value);\n                if (!toDelete_1) {\n                    this.headers.delete(key);\n                    this.normalizedNames.delete(key);\n                }\n                else {\n                    var /** @type {?} */ existing = this.headers.get(key);\n                    if (!existing) {\n                        return;\n                    }\n                    existing = existing.filter(function (value) { return toDelete_1.indexOf(value) === -1; });\n                    if (existing.length === 0) {\n                        this.headers.delete(key);\n                        this.normalizedNames.delete(key);\n                    }\n                    else {\n                        this.headers.set(key, existing);\n                    }\n                }\n                break;\n        }\n    };\n    /**\n     * @internal\n     */\n    /**\n     * \\@internal\n     * @param {?} fn\n     * @return {?}\n     */\n    HttpHeaders.prototype.forEach = /**\n     * \\@internal\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        var _this = this;\n        this.init();\n        Array.from(this.normalizedNames.keys())\n            .forEach(function (key) { return fn(/** @type {?} */ ((_this.normalizedNames.get(key))), /** @type {?} */ ((_this.headers.get(key)))); });\n    };\n    return HttpHeaders;\n}());\n/**\n * Immutable set of Http headers, with lazy parsing.\n * \\@stable\n */\nexport { HttpHeaders };\nfunction HttpHeaders_tsickle_Closure_declarations() {\n    /**\n     * Internal map of lowercase header names to values.\n     * @type {?}\n     */\n    HttpHeaders.prototype.headers;\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     * @type {?}\n     */\n    HttpHeaders.prototype.normalizedNames;\n    /**\n     * Complete the lazy initialization of this object (needed before reading).\n     * @type {?}\n     */\n    HttpHeaders.prototype.lazyInit;\n    /**\n     * Queued updates to be materialized the next initialization.\n     * @type {?}\n     */\n    HttpHeaders.prototype.lazyUpdate;\n}\n//# sourceMappingURL=headers.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A codec for encoding and decoding parameters in URLs.\n *\n * Used by `HttpParams`.\n *\n * \\@stable\n *\n * @record\n */\nexport function HttpParameterCodec() { }\nfunction HttpParameterCodec_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpParameterCodec.prototype.encodeKey;\n    /** @type {?} */\n    HttpParameterCodec.prototype.encodeValue;\n    /** @type {?} */\n    HttpParameterCodec.prototype.decodeKey;\n    /** @type {?} */\n    HttpParameterCodec.prototype.decodeValue;\n}\n/**\n * A `HttpParameterCodec` that uses `encodeURIComponent` and `decodeURIComponent` to\n * serialize and parse URL parameter keys and values.\n *\n * \\@stable\n */\nvar /**\n * A `HttpParameterCodec` that uses `encodeURIComponent` and `decodeURIComponent` to\n * serialize and parse URL parameter keys and values.\n *\n * \\@stable\n */\nHttpUrlEncodingCodec = /** @class */ (function () {\n    function HttpUrlEncodingCodec() {\n    }\n    /**\n     * @param {?} k\n     * @return {?}\n     */\n    HttpUrlEncodingCodec.prototype.encodeKey = /**\n     * @param {?} k\n     * @return {?}\n     */\n    function (k) { return standardEncoding(k); };\n    /**\n     * @param {?} v\n     * @return {?}\n     */\n    HttpUrlEncodingCodec.prototype.encodeValue = /**\n     * @param {?} v\n     * @return {?}\n     */\n    function (v) { return standardEncoding(v); };\n    /**\n     * @param {?} k\n     * @return {?}\n     */\n    HttpUrlEncodingCodec.prototype.decodeKey = /**\n     * @param {?} k\n     * @return {?}\n     */\n    function (k) { return decodeURIComponent(k); };\n    /**\n     * @param {?} v\n     * @return {?}\n     */\n    HttpUrlEncodingCodec.prototype.decodeValue = /**\n     * @param {?} v\n     * @return {?}\n     */\n    function (v) { return decodeURIComponent(v); };\n    return HttpUrlEncodingCodec;\n}());\n/**\n * A `HttpParameterCodec` that uses `encodeURIComponent` and `decodeURIComponent` to\n * serialize and parse URL parameter keys and values.\n *\n * \\@stable\n */\nexport { HttpUrlEncodingCodec };\n/**\n * @param {?} rawParams\n * @param {?} codec\n * @return {?}\n */\nfunction paramParser(rawParams, codec) {\n    var /** @type {?} */ map = new Map();\n    if (rawParams.length > 0) {\n        var /** @type {?} */ params = rawParams.split('&');\n        params.forEach(function (param) {\n            var /** @type {?} */ eqIdx = param.indexOf('=');\n            var _a = eqIdx == -1 ?\n                [codec.decodeKey(param), ''] :\n                [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))], key = _a[0], val = _a[1];\n            var /** @type {?} */ list = map.get(key) || [];\n            list.push(val);\n            map.set(key, list);\n        });\n    }\n    return map;\n}\n/**\n * @param {?} v\n * @return {?}\n */\nfunction standardEncoding(v) {\n    return encodeURIComponent(v)\n        .replace(/%40/gi, '@')\n        .replace(/%3A/gi, ':')\n        .replace(/%24/gi, '$')\n        .replace(/%2C/gi, ',')\n        .replace(/%3B/gi, ';')\n        .replace(/%2B/gi, '+')\n        .replace(/%3D/gi, '=')\n        .replace(/%3F/gi, '?')\n        .replace(/%2F/gi, '/');\n}\n/**\n * @record\n */\nfunction Update() { }\nfunction Update_tsickle_Closure_declarations() {\n    /** @type {?} */\n    Update.prototype.param;\n    /** @type {?|undefined} */\n    Update.prototype.value;\n    /** @type {?} */\n    Update.prototype.op;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable - all mutation operations return a new instance.\n *\n * \\@stable\n */\nvar /**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable - all mutation operations return a new instance.\n *\n * \\@stable\n */\nHttpParams = /** @class */ (function () {\n    function HttpParams(options) {\n        if (options === void 0) { options = /** @type {?} */ ({}); }\n        var _this = this;\n        this.updates = null;\n        this.cloneFrom = null;\n        this.encoder = options.encoder || new HttpUrlEncodingCodec();\n        if (!!options.fromString) {\n            if (!!options.fromObject) {\n                throw new Error(\"Cannot specify both fromString and fromObject.\");\n            }\n            this.map = paramParser(options.fromString, this.encoder);\n        }\n        else if (!!options.fromObject) {\n            this.map = new Map();\n            Object.keys(options.fromObject).forEach(function (key) {\n                var /** @type {?} */ value = (/** @type {?} */ (options.fromObject))[key]; /** @type {?} */\n                ((_this.map)).set(key, Array.isArray(value) ? value : [value]);\n            });\n        }\n        else {\n            this.map = null;\n        }\n    }\n    /**\n     * Check whether the body has one or more values for the given parameter name.\n     */\n    /**\n     * Check whether the body has one or more values for the given parameter name.\n     * @param {?} param\n     * @return {?}\n     */\n    HttpParams.prototype.has = /**\n     * Check whether the body has one or more values for the given parameter name.\n     * @param {?} param\n     * @return {?}\n     */\n    function (param) {\n        this.init();\n        return /** @type {?} */ ((this.map)).has(param);\n    };\n    /**\n     * Get the first value for the given parameter name, or `null` if it's not present.\n     */\n    /**\n     * Get the first value for the given parameter name, or `null` if it's not present.\n     * @param {?} param\n     * @return {?}\n     */\n    HttpParams.prototype.get = /**\n     * Get the first value for the given parameter name, or `null` if it's not present.\n     * @param {?} param\n     * @return {?}\n     */\n    function (param) {\n        this.init();\n        var /** @type {?} */ res = /** @type {?} */ ((this.map)).get(param);\n        return !!res ? res[0] : null;\n    };\n    /**\n     * Get all values for the given parameter name, or `null` if it's not present.\n     */\n    /**\n     * Get all values for the given parameter name, or `null` if it's not present.\n     * @param {?} param\n     * @return {?}\n     */\n    HttpParams.prototype.getAll = /**\n     * Get all values for the given parameter name, or `null` if it's not present.\n     * @param {?} param\n     * @return {?}\n     */\n    function (param) {\n        this.init();\n        return /** @type {?} */ ((this.map)).get(param) || null;\n    };\n    /**\n     * Get all the parameter names for this body.\n     */\n    /**\n     * Get all the parameter names for this body.\n     * @return {?}\n     */\n    HttpParams.prototype.keys = /**\n     * Get all the parameter names for this body.\n     * @return {?}\n     */\n    function () {\n        this.init();\n        return Array.from(/** @type {?} */ ((this.map)).keys());\n    };\n    /**\n     * Construct a new body with an appended value for the given parameter name.\n     */\n    /**\n     * Construct a new body with an appended value for the given parameter name.\n     * @param {?} param\n     * @param {?} value\n     * @return {?}\n     */\n    HttpParams.prototype.append = /**\n     * Construct a new body with an appended value for the given parameter name.\n     * @param {?} param\n     * @param {?} value\n     * @return {?}\n     */\n    function (param, value) { return this.clone({ param: param, value: value, op: 'a' }); };\n    /**\n     * Construct a new body with a new value for the given parameter name.\n     */\n    /**\n     * Construct a new body with a new value for the given parameter name.\n     * @param {?} param\n     * @param {?} value\n     * @return {?}\n     */\n    HttpParams.prototype.set = /**\n     * Construct a new body with a new value for the given parameter name.\n     * @param {?} param\n     * @param {?} value\n     * @return {?}\n     */\n    function (param, value) { return this.clone({ param: param, value: value, op: 's' }); };\n    /**\n     * Construct a new body with either the given value for the given parameter\n     * removed, if a value is given, or all values for the given parameter removed\n     * if not.\n     */\n    /**\n     * Construct a new body with either the given value for the given parameter\n     * removed, if a value is given, or all values for the given parameter removed\n     * if not.\n     * @param {?} param\n     * @param {?=} value\n     * @return {?}\n     */\n    HttpParams.prototype.delete = /**\n     * Construct a new body with either the given value for the given parameter\n     * removed, if a value is given, or all values for the given parameter removed\n     * if not.\n     * @param {?} param\n     * @param {?=} value\n     * @return {?}\n     */\n    function (param, value) { return this.clone({ param: param, value: value, op: 'd' }); };\n    /**\n     * Serialize the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     */\n    /**\n     * Serialize the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     * @return {?}\n     */\n    HttpParams.prototype.toString = /**\n     * Serialize the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        this.init();\n        return this.keys()\n            .map(function (key) {\n            var /** @type {?} */ eKey = _this.encoder.encodeKey(key);\n            return /** @type {?} */ ((/** @type {?} */ ((_this.map)).get(key))).map(function (value) { return eKey + '=' + _this.encoder.encodeValue(value); }).join('&');\n        })\n            .join('&');\n    };\n    /**\n     * @param {?} update\n     * @return {?}\n     */\n    HttpParams.prototype.clone = /**\n     * @param {?} update\n     * @return {?}\n     */\n    function (update) {\n        var /** @type {?} */ clone = new HttpParams({ encoder: this.encoder });\n        clone.cloneFrom = this.cloneFrom || this;\n        clone.updates = (this.updates || []).concat([update]);\n        return clone;\n    };\n    /**\n     * @return {?}\n     */\n    HttpParams.prototype.init = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        if (this.map === null) {\n            this.map = new Map();\n        }\n        if (this.cloneFrom !== null) {\n            this.cloneFrom.init();\n            this.cloneFrom.keys().forEach(function (key) { return ((_this.map)).set(key, /** @type {?} */ ((/** @type {?} */ ((/** @type {?} */ ((_this.cloneFrom)).map)).get(key)))); }); /** @type {?} */\n            ((this.updates)).forEach(function (update) {\n                switch (update.op) {\n                    case 'a':\n                    case 's':\n                        var /** @type {?} */ base = (update.op === 'a' ? /** @type {?} */ ((_this.map)).get(update.param) : undefined) || [];\n                        base.push(/** @type {?} */ ((update.value))); /** @type {?} */\n                        ((_this.map)).set(update.param, base);\n                        break;\n                    case 'd':\n                        if (update.value !== undefined) {\n                            var /** @type {?} */ base_1 = /** @type {?} */ ((_this.map)).get(update.param) || [];\n                            var /** @type {?} */ idx = base_1.indexOf(update.value);\n                            if (idx !== -1) {\n                                base_1.splice(idx, 1);\n                            }\n                            if (base_1.length > 0) {\n                                /** @type {?} */ ((_this.map)).set(update.param, base_1);\n                            }\n                            else {\n                                /** @type {?} */ ((_this.map)).delete(update.param);\n                            }\n                        }\n                        else {\n                            /** @type {?} */ ((_this.map)).delete(update.param);\n                            break;\n                        }\n                }\n            });\n            this.cloneFrom = null;\n        }\n    };\n    return HttpParams;\n}());\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable - all mutation operations return a new instance.\n *\n * \\@stable\n */\nexport { HttpParams };\nfunction HttpParams_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpParams.prototype.map;\n    /** @type {?} */\n    HttpParams.prototype.encoder;\n    /** @type {?} */\n    HttpParams.prototype.updates;\n    /** @type {?} */\n    HttpParams.prototype.cloneFrom;\n}\n//# sourceMappingURL=params.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { HttpHeaders } from './headers';\nimport { HttpParams } from './params';\n/**\n * Construction interface for `HttpRequest`s.\n *\n * All values are optional and will override default values if provided.\n * @record\n */\nfunction HttpRequestInit() { }\nfunction HttpRequestInit_tsickle_Closure_declarations() {\n    /** @type {?|undefined} */\n    HttpRequestInit.prototype.headers;\n    /** @type {?|undefined} */\n    HttpRequestInit.prototype.reportProgress;\n    /** @type {?|undefined} */\n    HttpRequestInit.prototype.params;\n    /** @type {?|undefined} */\n    HttpRequestInit.prototype.responseType;\n    /** @type {?|undefined} */\n    HttpRequestInit.prototype.withCredentials;\n}\n/**\n * Determine whether the given HTTP method may include a body.\n * @param {?} method\n * @return {?}\n */\nfunction mightHaveBody(method) {\n    switch (method) {\n        case 'DELETE':\n        case 'GET':\n        case 'HEAD':\n        case 'OPTIONS':\n        case 'JSONP':\n            return false;\n        default:\n            return true;\n    }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n * @param {?} value\n * @return {?}\n */\nfunction isArrayBuffer(value) {\n    return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n * @param {?} value\n * @return {?}\n */\nfunction isBlob(value) {\n    return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n * @param {?} value\n * @return {?}\n */\nfunction isFormData(value) {\n    return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * \\@stable\n * @template T\n */\nvar /**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * \\@stable\n * @template T\n */\nHttpRequest = /** @class */ (function () {\n    function HttpRequest(method, url, third, fourth) {\n        this.url = url;\n        /**\n         * The request body, or `null` if one isn't set.\n         *\n         * Bodies are not enforced to be immutable, as they can include a reference to any\n         * user-defined data type. However, interceptors should take care to preserve\n         * idempotence by treating them as such.\n         */\n        this.body = null;\n        /**\n         * Whether this request should be made in a way that exposes progress events.\n         *\n         * Progress events are expensive (change detection runs on each event) and so\n         * they should only be requested if the consumer intends to monitor them.\n         */\n        this.reportProgress = false;\n        /**\n         * Whether this request should be sent with outgoing credentials (cookies).\n         */\n        this.withCredentials = false;\n        /**\n         * The expected response type of the server.\n         *\n         * This is used to parse the response appropriately before returning it to\n         * the requestee.\n         */\n        this.responseType = 'json';\n        this.method = method.toUpperCase();\n        // Next, need to figure out which argument holds the HttpRequestInit\n        // options, if any.\n        var /** @type {?} */ options;\n        // Check whether a body argument is expected. The only valid way to omit\n        // the body argument is to use a known no-body method like GET.\n        if (mightHaveBody(this.method) || !!fourth) {\n            // Body is the third argument, options are the fourth.\n            this.body = (third !== undefined) ? /** @type {?} */ (third) : null;\n            options = fourth;\n        }\n        else {\n            // No body required, options are the third argument. The body stays null.\n            options = /** @type {?} */ (third);\n        }\n        // If options have been passed, interpret them.\n        if (options) {\n            // Normalize reportProgress and withCredentials.\n            this.reportProgress = !!options.reportProgress;\n            this.withCredentials = !!options.withCredentials;\n            // Override default response type of 'json' if one is provided.\n            if (!!options.responseType) {\n                this.responseType = options.responseType;\n            }\n            // Override headers if they're provided.\n            if (!!options.headers) {\n                this.headers = options.headers;\n            }\n            if (!!options.params) {\n                this.params = options.params;\n            }\n        }\n        // If no headers have been passed in, construct a new HttpHeaders instance.\n        if (!this.headers) {\n            this.headers = new HttpHeaders();\n        }\n        // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n        if (!this.params) {\n            this.params = new HttpParams();\n            this.urlWithParams = url;\n        }\n        else {\n            // Encode the parameters to a string in preparation for inclusion in the URL.\n            var /** @type {?} */ params = this.params.toString();\n            if (params.length === 0) {\n                // No parameters, the visible URL is just the URL given at creation time.\n                this.urlWithParams = url;\n            }\n            else {\n                // Does the URL already have query parameters? Look for '?'.\n                var /** @type {?} */ qIdx = url.indexOf('?');\n                // There are 3 cases to handle:\n                // 1) No existing parameters -> append '?' followed by params.\n                // 2) '?' exists and is followed by existing query string ->\n                //    append '&' followed by params.\n                // 3) '?' exists at the end of the url -> append params directly.\n                // This basically amounts to determining the character, if any, with\n                // which to join the URL and parameters.\n                var /** @type {?} */ sep = qIdx === -1 ? '?' : (qIdx < url.length - 1 ? '&' : '');\n                this.urlWithParams = url + sep + params;\n            }\n        }\n    }\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     */\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     * @return {?}\n     */\n    HttpRequest.prototype.serializeBody = /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     * @return {?}\n     */\n    function () {\n        // If no body is present, no need to serialize it.\n        if (this.body === null) {\n            return null;\n        }\n        // Check whether the body is already in a serialized form. If so,\n        // it can just be returned directly.\n        if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) ||\n            typeof this.body === 'string') {\n            return this.body;\n        }\n        // Check whether the body is an instance of HttpUrlEncodedParams.\n        if (this.body instanceof HttpParams) {\n            return this.body.toString();\n        }\n        // Check whether the body is an object or array, and serialize with JSON if so.\n        if (typeof this.body === 'object' || typeof this.body === 'boolean' ||\n            Array.isArray(this.body)) {\n            return JSON.stringify(this.body);\n        }\n        // Fall back on toString() for everything else.\n        return (/** @type {?} */ (this.body)).toString();\n    };\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     */\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     * @return {?}\n     */\n    HttpRequest.prototype.detectContentTypeHeader = /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     * @return {?}\n     */\n    function () {\n        // An empty body has no content type.\n        if (this.body === null) {\n            return null;\n        }\n        // FormData bodies rely on the browser's content type assignment.\n        if (isFormData(this.body)) {\n            return null;\n        }\n        // Blobs usually have their own content type. If it doesn't, then\n        // no type can be inferred.\n        if (isBlob(this.body)) {\n            return this.body.type || null;\n        }\n        // Array buffers have unknown contents and thus no type can be inferred.\n        if (isArrayBuffer(this.body)) {\n            return null;\n        }\n        // Technically, strings could be a form of JSON data, but it's safe enough\n        // to assume they're plain strings.\n        if (typeof this.body === 'string') {\n            return 'text/plain';\n        }\n        // `HttpUrlEncodedParams` has its own content-type.\n        if (this.body instanceof HttpParams) {\n            return 'application/x-www-form-urlencoded;charset=UTF-8';\n        }\n        // Arrays, objects, and numbers will be encoded as JSON.\n        if (typeof this.body === 'object' || typeof this.body === 'number' ||\n            Array.isArray(this.body)) {\n            return 'application/json';\n        }\n        // No type could be inferred.\n        return null;\n    };\n    /**\n     * @param {?=} update\n     * @return {?}\n     */\n    HttpRequest.prototype.clone = /**\n     * @param {?=} update\n     * @return {?}\n     */\n    function (update) {\n        if (update === void 0) { update = {}; }\n        // For method, url, and responseType, take the current value unless\n        // it is overridden in the update hash.\n        var /** @type {?} */ method = update.method || this.method;\n        var /** @type {?} */ url = update.url || this.url;\n        var /** @type {?} */ responseType = update.responseType || this.responseType;\n        // The body is somewhat special - a `null` value in update.body means\n        // whatever current body is present is being overridden with an empty\n        // body, whereas an `undefined` value in update.body implies no\n        // override.\n        var /** @type {?} */ body = (update.body !== undefined) ? update.body : this.body;\n        // Carefully handle the boolean options to differentiate between\n        // `false` and `undefined` in the update args.\n        var /** @type {?} */ withCredentials = (update.withCredentials !== undefined) ? update.withCredentials : this.withCredentials;\n        var /** @type {?} */ reportProgress = (update.reportProgress !== undefined) ? update.reportProgress : this.reportProgress;\n        // Headers and params may be appended to if `setHeaders` or\n        // `setParams` are used.\n        var /** @type {?} */ headers = update.headers || this.headers;\n        var /** @type {?} */ params = update.params || this.params;\n        // Check whether the caller has asked to add headers.\n        if (update.setHeaders !== undefined) {\n            // Set every requested header.\n            headers =\n                Object.keys(update.setHeaders)\n                    .reduce(function (headers, name) { return headers.set(name, /** @type {?} */ ((update.setHeaders))[name]); }, headers);\n        }\n        // Check whether the caller has asked to set params.\n        if (update.setParams) {\n            // Set every requested param.\n            params = Object.keys(update.setParams)\n                .reduce(function (params, param) { return params.set(param, /** @type {?} */ ((update.setParams))[param]); }, params);\n        }\n        // Finally, construct the new HttpRequest using the pieces from above.\n        return new HttpRequest(method, url, body, {\n            params: params, headers: headers, reportProgress: reportProgress, responseType: responseType, withCredentials: withCredentials,\n        });\n    };\n    return HttpRequest;\n}());\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * \\@stable\n * @template T\n */\nexport { HttpRequest };\nfunction HttpRequest_tsickle_Closure_declarations() {\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     * @type {?}\n     */\n    HttpRequest.prototype.body;\n    /**\n     * Outgoing headers for this request.\n     * @type {?}\n     */\n    HttpRequest.prototype.headers;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     * @type {?}\n     */\n    HttpRequest.prototype.reportProgress;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     * @type {?}\n     */\n    HttpRequest.prototype.withCredentials;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     * @type {?}\n     */\n    HttpRequest.prototype.responseType;\n    /**\n     * The outgoing HTTP request method.\n     * @type {?}\n     */\n    HttpRequest.prototype.method;\n    /**\n     * Outgoing URL parameters.\n     * @type {?}\n     */\n    HttpRequest.prototype.params;\n    /**\n     * The outgoing URL with all URL parameters set.\n     * @type {?}\n     */\n    HttpRequest.prototype.urlWithParams;\n}\n//# sourceMappingURL=request.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport * as tslib_1 from \"tslib\";\nimport { HttpHeaders } from './headers';\n/** @enum {number} */\nvar HttpEventType = {\n    /**\n       * The request was sent out over the wire.\n       */\n    Sent: 0,\n    /**\n       * An upload progress event was received.\n       */\n    UploadProgress: 1,\n    /**\n       * The response status code and headers were received.\n       */\n    ResponseHeader: 2,\n    /**\n       * A download progress event was received.\n       */\n    DownloadProgress: 3,\n    /**\n       * The full response including the body was received.\n       */\n    Response: 4,\n    /**\n       * A custom event from an interceptor or a backend.\n       */\n    User: 5,\n};\nexport { HttpEventType };\nHttpEventType[HttpEventType.Sent] = \"Sent\";\nHttpEventType[HttpEventType.UploadProgress] = \"UploadProgress\";\nHttpEventType[HttpEventType.ResponseHeader] = \"ResponseHeader\";\nHttpEventType[HttpEventType.DownloadProgress] = \"DownloadProgress\";\nHttpEventType[HttpEventType.Response] = \"Response\";\nHttpEventType[HttpEventType.User] = \"User\";\n/**\n * Base interface for progress events.\n *\n * \\@stable\n * @record\n */\nexport function HttpProgressEvent() { }\nfunction HttpProgressEvent_tsickle_Closure_declarations() {\n    /**\n     * Progress event type is either upload or download.\n     * @type {?}\n     */\n    HttpProgressEvent.prototype.type;\n    /**\n     * Number of bytes uploaded or downloaded.\n     * @type {?}\n     */\n    HttpProgressEvent.prototype.loaded;\n    /**\n     * Total number of bytes to upload or download. Depending on the request or\n     * response, this may not be computable and thus may not be present.\n     * @type {?|undefined}\n     */\n    HttpProgressEvent.prototype.total;\n}\n/**\n * A download progress event.\n *\n * \\@stable\n * @record\n */\nexport function HttpDownloadProgressEvent() { }\nfunction HttpDownloadProgressEvent_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpDownloadProgressEvent.prototype.type;\n    /**\n     * The partial response body as downloaded so far.\n     *\n     * Only present if the responseType was `text`.\n     * @type {?|undefined}\n     */\n    HttpDownloadProgressEvent.prototype.partialText;\n}\n/**\n * An upload progress event.\n *\n * \\@stable\n * @record\n */\nexport function HttpUploadProgressEvent() { }\nfunction HttpUploadProgressEvent_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpUploadProgressEvent.prototype.type;\n}\n/**\n * An event indicating that the request was sent to the server. Useful\n * when a request may be retried multiple times, to distinguish between\n * retries on the final event stream.\n *\n * \\@stable\n * @record\n */\nexport function HttpSentEvent() { }\nfunction HttpSentEvent_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpSentEvent.prototype.type;\n}\n/**\n * A user-defined event.\n *\n * Grouping all custom events under this type ensures they will be handled\n * and forwarded by all implementations of interceptors.\n *\n * \\@stable\n * @record\n * @template T\n */\nexport function HttpUserEvent() { }\nfunction HttpUserEvent_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpUserEvent.prototype.type;\n}\n/**\n * An error that represents a failed attempt to JSON.parse text coming back\n * from the server.\n *\n * It bundles the Error object with the actual response body that failed to parse.\n *\n * \\@stable\n * @record\n */\nexport function HttpJsonParseError() { }\nfunction HttpJsonParseError_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpJsonParseError.prototype.error;\n    /** @type {?} */\n    HttpJsonParseError.prototype.text;\n}\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * \\@stable\n * @abstract\n */\nHttpResponseBase = /** @class */ (function () {\n    /**\n     * Super-constructor for all responses.\n     *\n     * The single parameter accepted is an initialization hash. Any properties\n     * of the response passed there will override the default values.\n     */\n    function HttpResponseBase(init, defaultStatus, defaultStatusText) {\n        if (defaultStatus === void 0) { defaultStatus = 200; }\n        if (defaultStatusText === void 0) { defaultStatusText = 'OK'; }\n        // If the hash has values passed, use them to initialize the response.\n        // Otherwise use the default values.\n        this.headers = init.headers || new HttpHeaders();\n        this.status = init.status !== undefined ? init.status : defaultStatus;\n        this.statusText = init.statusText || defaultStatusText;\n        this.url = init.url || null;\n        // Cache the ok value to avoid defining a getter.\n        this.ok = this.status >= 200 && this.status < 300;\n    }\n    return HttpResponseBase;\n}());\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * \\@stable\n * @abstract\n */\nexport { HttpResponseBase };\nfunction HttpResponseBase_tsickle_Closure_declarations() {\n    /**\n     * All response headers.\n     * @type {?}\n     */\n    HttpResponseBase.prototype.headers;\n    /**\n     * Response status code.\n     * @type {?}\n     */\n    HttpResponseBase.prototype.status;\n    /**\n     * Textual description of response status code.\n     *\n     * Do not depend on this.\n     * @type {?}\n     */\n    HttpResponseBase.prototype.statusText;\n    /**\n     * URL of the resource retrieved, or null if not available.\n     * @type {?}\n     */\n    HttpResponseBase.prototype.url;\n    /**\n     * Whether the status code falls in the 2xx range.\n     * @type {?}\n     */\n    HttpResponseBase.prototype.ok;\n    /**\n     * Type of the response, narrowed to either the full response or the header.\n     * @type {?}\n     */\n    HttpResponseBase.prototype.type;\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * \\@stable\n */\nvar /**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * \\@stable\n */\nHttpHeaderResponse = /** @class */ (function (_super) {\n    tslib_1.__extends(HttpHeaderResponse, _super);\n    /**\n     * Create a new `HttpHeaderResponse` with the given parameters.\n     */\n    function HttpHeaderResponse(init) {\n        if (init === void 0) { init = {}; }\n        var _this = _super.call(this, init) || this;\n        _this.type = HttpEventType.ResponseHeader;\n        return _this;\n    }\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     */\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     * @param {?=} update\n     * @return {?}\n     */\n    HttpHeaderResponse.prototype.clone = /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     * @param {?=} update\n     * @return {?}\n     */\n    function (update) {\n        if (update === void 0) { update = {}; }\n        // Perform a straightforward initialization of the new HttpHeaderResponse,\n        // overriding the current parameters with new ones if given.\n        return new HttpHeaderResponse({\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    };\n    return HttpHeaderResponse;\n}(HttpResponseBase));\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * \\@stable\n */\nexport { HttpHeaderResponse };\nfunction HttpHeaderResponse_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpHeaderResponse.prototype.type;\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * \\@stable\n * @template T\n */\nvar /**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * \\@stable\n * @template T\n */\nHttpResponse = /** @class */ (function (_super) {\n    tslib_1.__extends(HttpResponse, _super);\n    /**\n     * Construct a new `HttpResponse`.\n     */\n    function HttpResponse(init) {\n        if (init === void 0) { init = {}; }\n        var _this = _super.call(this, init) || this;\n        _this.type = HttpEventType.Response;\n        _this.body = init.body !== undefined ? init.body : null;\n        return _this;\n    }\n    /**\n     * @param {?=} update\n     * @return {?}\n     */\n    HttpResponse.prototype.clone = /**\n     * @param {?=} update\n     * @return {?}\n     */\n    function (update) {\n        if (update === void 0) { update = {}; }\n        return new HttpResponse({\n            body: (update.body !== undefined) ? update.body : this.body,\n            headers: update.headers || this.headers,\n            status: (update.status !== undefined) ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    };\n    return HttpResponse;\n}(HttpResponseBase));\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * \\@stable\n * @template T\n */\nexport { HttpResponse };\nfunction HttpResponse_tsickle_Closure_declarations() {\n    /**\n     * The response body, or `null` if one was not returned.\n     * @type {?}\n     */\n    HttpResponse.prototype.body;\n    /** @type {?} */\n    HttpResponse.prototype.type;\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * \\@stable\n */\nvar /**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * \\@stable\n */\nHttpErrorResponse = /** @class */ (function (_super) {\n    tslib_1.__extends(HttpErrorResponse, _super);\n    function HttpErrorResponse(init) {\n        var _this = \n        // Initialize with a default status of 0 / Unknown Error.\n        _super.call(this, init, 0, 'Unknown Error') || this;\n        _this.name = 'HttpErrorResponse';\n        /**\n         * Errors are never okay, even when the status code is in the 2xx success range.\n         */\n        _this.ok = false;\n        // If the response was successful, then this was a parse error. Otherwise, it was\n        // a protocol-level failure of some sort. Either the request failed in transit\n        // or the server returned an unsuccessful status code.\n        if (_this.status >= 200 && _this.status < 300) {\n            _this.message = \"Http failure during parsing for \" + (init.url || '(unknown url)');\n        }\n        else {\n            _this.message =\n                \"Http failure response for \" + (init.url || '(unknown url)') + \": \" + init.status + \" \" + init.statusText;\n        }\n        _this.error = init.error || null;\n        return _this;\n    }\n    return HttpErrorResponse;\n}(HttpResponseBase));\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * \\@stable\n */\nexport { HttpErrorResponse };\nfunction HttpErrorResponse_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpErrorResponse.prototype.name;\n    /** @type {?} */\n    HttpErrorResponse.prototype.message;\n    /** @type {?} */\n    HttpErrorResponse.prototype.error;\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     * @type {?}\n     */\n    HttpErrorResponse.prototype.ok;\n}\n//# sourceMappingURL=response.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Injectable } from '@angular/core';\nimport { of } from 'rxjs/observable/of';\nimport { concatMap } from 'rxjs/operator/concatMap';\nimport { filter } from 'rxjs/operator/filter';\nimport { map } from 'rxjs/operator/map';\nimport { HttpHandler } from './backend';\nimport { HttpHeaders } from './headers';\nimport { HttpParams } from './params';\nimport { HttpRequest } from './request';\nimport { HttpResponse } from './response';\n/**\n * Construct an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. Basically, this clones the object and adds the body.\n * @template T\n * @param {?} options\n * @param {?} body\n * @return {?}\n */\nfunction addBody(options, body) {\n    return {\n        body: body,\n        headers: options.headers,\n        observe: options.observe,\n        params: options.params,\n        reportProgress: options.reportProgress,\n        responseType: options.responseType,\n        withCredentials: options.withCredentials,\n    };\n}\n/**\n * Perform HTTP requests.\n *\n * `HttpClient` is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies according to which\n * signature is called (mainly the values of `observe` and `responseType`).\n *\n * \\@stable\n */\nvar HttpClient = /** @class */ (function () {\n    function HttpClient(handler) {\n        this.handler = handler;\n    }\n    /**\n     * Constructs an `Observable` for a particular HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * This method can be called in one of two ways. Either an `HttpRequest`\n     * instance can be passed directly as the only parameter, or a method can be\n     * passed as the first parameter, a string URL as the second, and an\n     * options hash as the third.\n     *\n     * If a `HttpRequest` object is passed directly, an `Observable` of the\n     * raw `HttpEvent` stream will be returned.\n     *\n     * If a request is instead built by providing a URL, the options object\n     * determines the return type of `request()`. In addition to configuring\n     * request parameters such as the outgoing headers and/or the body, the options\n     * hash specifies two key pieces of information about the request: the\n     * `responseType` and what to `observe`.\n     *\n     * The `responseType` value determines how a successful response body will be\n     * parsed. If `responseType` is the default `json`, a type interface for the\n     * resulting object may be passed as a type parameter to `request()`.\n     *\n     * The `observe` value determines the return type of `request()`, based on what\n     * the consumer is interested in observing. A value of `events` will return an\n     * `Observable<HttpEvent>` representing the raw `HttpEvent` stream,\n     * including progress events by default. A value of `response` will return an\n     * `Observable<HttpResponse<T>>` where the `T` parameter of `HttpResponse`\n     * depends on the `responseType` and any optionally provided type parameter.\n     * A value of `body` will return an `Observable<T>` with the same `T` body type.\n     */\n    /**\n     * Constructs an `Observable` for a particular HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * This method can be called in one of two ways. Either an `HttpRequest`\n     * instance can be passed directly as the only parameter, or a method can be\n     * passed as the first parameter, a string URL as the second, and an\n     * options hash as the third.\n     *\n     * If a `HttpRequest` object is passed directly, an `Observable` of the\n     * raw `HttpEvent` stream will be returned.\n     *\n     * If a request is instead built by providing a URL, the options object\n     * determines the return type of `request()`. In addition to configuring\n     * request parameters such as the outgoing headers and/or the body, the options\n     * hash specifies two key pieces of information about the request: the\n     * `responseType` and what to `observe`.\n     *\n     * The `responseType` value determines how a successful response body will be\n     * parsed. If `responseType` is the default `json`, a type interface for the\n     * resulting object may be passed as a type parameter to `request()`.\n     *\n     * The `observe` value determines the return type of `request()`, based on what\n     * the consumer is interested in observing. A value of `events` will return an\n     * `Observable<HttpEvent>` representing the raw `HttpEvent` stream,\n     * including progress events by default. A value of `response` will return an\n     * `Observable<HttpResponse<T>>` where the `T` parameter of `HttpResponse`\n     * depends on the `responseType` and any optionally provided type parameter.\n     * A value of `body` will return an `Observable<T>` with the same `T` body type.\n     * @param {?} first\n     * @param {?=} url\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.request = /**\n     * Constructs an `Observable` for a particular HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * This method can be called in one of two ways. Either an `HttpRequest`\n     * instance can be passed directly as the only parameter, or a method can be\n     * passed as the first parameter, a string URL as the second, and an\n     * options hash as the third.\n     *\n     * If a `HttpRequest` object is passed directly, an `Observable` of the\n     * raw `HttpEvent` stream will be returned.\n     *\n     * If a request is instead built by providing a URL, the options object\n     * determines the return type of `request()`. In addition to configuring\n     * request parameters such as the outgoing headers and/or the body, the options\n     * hash specifies two key pieces of information about the request: the\n     * `responseType` and what to `observe`.\n     *\n     * The `responseType` value determines how a successful response body will be\n     * parsed. If `responseType` is the default `json`, a type interface for the\n     * resulting object may be passed as a type parameter to `request()`.\n     *\n     * The `observe` value determines the return type of `request()`, based on what\n     * the consumer is interested in observing. A value of `events` will return an\n     * `Observable<HttpEvent>` representing the raw `HttpEvent` stream,\n     * including progress events by default. A value of `response` will return an\n     * `Observable<HttpResponse<T>>` where the `T` parameter of `HttpResponse`\n     * depends on the `responseType` and any optionally provided type parameter.\n     * A value of `body` will return an `Observable<T>` with the same `T` body type.\n     * @param {?} first\n     * @param {?=} url\n     * @param {?=} options\n     * @return {?}\n     */\n    function (first, url, options) {\n        var _this = this;\n        if (options === void 0) { options = {}; }\n        var /** @type {?} */ req;\n        // Firstly, check whether the primary argument is an instance of `HttpRequest`.\n        if (first instanceof HttpRequest) {\n            // It is. The other arguments must be undefined (per the signatures) and can be\n            // ignored.\n            req = /** @type {?} */ (first);\n        }\n        else {\n            // It's a string, so it represents a URL. Construct a request based on it,\n            // and incorporate the remaining arguments (assuming GET unless a method is\n            // provided.\n            // Figure out the headers.\n            var /** @type {?} */ headers = undefined;\n            if (options.headers instanceof HttpHeaders) {\n                headers = options.headers;\n            }\n            else {\n                headers = new HttpHeaders(options.headers);\n            }\n            // Sort out parameters.\n            var /** @type {?} */ params = undefined;\n            if (!!options.params) {\n                if (options.params instanceof HttpParams) {\n                    params = options.params;\n                }\n                else {\n                    params = new HttpParams({ fromObject: options.params });\n                }\n            }\n            // Construct the request.\n            req = new HttpRequest(first, /** @type {?} */ ((url)), (options.body !== undefined ? options.body : null), {\n                headers: headers,\n                params: params,\n                reportProgress: options.reportProgress,\n                // By default, JSON is assumed to be returned for all calls.\n                responseType: options.responseType || 'json',\n                withCredentials: options.withCredentials,\n            });\n        }\n        // Start with an Observable.of() the initial request, and run the handler (which\n        // includes all interceptors) inside a concatMap(). This way, the handler runs\n        // inside an Observable chain, which causes interceptors to be re-run on every\n        // subscription (this also makes retries re-run the handler, including interceptors).\n        var /** @type {?} */ events$ = concatMap.call(of(req), function (req) { return _this.handler.handle(req); });\n        // If coming via the API signature which accepts a previously constructed HttpRequest,\n        // the only option is to get the event stream. Otherwise, return the event stream if\n        // that is what was requested.\n        if (first instanceof HttpRequest || options.observe === 'events') {\n            return events$;\n        }\n        // The requested stream contains either the full response or the body. In either\n        // case, the first step is to filter the event stream to extract a stream of\n        // responses(s).\n        var /** @type {?} */ res$ = filter.call(events$, function (event) { return event instanceof HttpResponse; });\n        // Decide which stream to return.\n        switch (options.observe || 'body') {\n            case 'body':\n                // The requested stream is the body. Map the response stream to the response\n                // body. This could be done more simply, but a misbehaving interceptor might\n                // transform the response body into a different format and ignore the requested\n                // responseType. Guard against this by validating that the response is of the\n                // requested type.\n                switch (req.responseType) {\n                    case 'arraybuffer':\n                        return map.call(res$, function (res) {\n                            // Validate that the body is an ArrayBuffer.\n                            if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                                throw new Error('Response is not an ArrayBuffer.');\n                            }\n                            return res.body;\n                        });\n                    case 'blob':\n                        return map.call(res$, function (res) {\n                            // Validate that the body is a Blob.\n                            if (res.body !== null && !(res.body instanceof Blob)) {\n                                throw new Error('Response is not a Blob.');\n                            }\n                            return res.body;\n                        });\n                    case 'text':\n                        return map.call(res$, function (res) {\n                            // Validate that the body is a string.\n                            if (res.body !== null && typeof res.body !== 'string') {\n                                throw new Error('Response is not a string.');\n                            }\n                            return res.body;\n                        });\n                    case 'json':\n                    default:\n                        // No validation needed for JSON responses, as they can be of any type.\n                        return map.call(res$, function (res) { return res.body; });\n                }\n            case 'response':\n                // The response stream was requested directly, so return it.\n                return res$;\n            default:\n                // Guard against new future observe types being added.\n                throw new Error(\"Unreachable: unhandled observe type \" + options.observe + \"}\");\n        }\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * DELETE request to be executed on the server. See the individual overloads for\n     * details of `delete()`'s return type based on the provided options.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * DELETE request to be executed on the server. See the individual overloads for\n     * details of `delete()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.delete = /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * DELETE request to be executed on the server. See the individual overloads for\n     * details of `delete()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    function (url, options) {\n        if (options === void 0) { options = {}; }\n        return this.request('DELETE', url, /** @type {?} */ (options));\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * GET request to be executed on the server. See the individual overloads for\n     * details of `get()`'s return type based on the provided options.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * GET request to be executed on the server. See the individual overloads for\n     * details of `get()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.get = /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * GET request to be executed on the server. See the individual overloads for\n     * details of `get()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    function (url, options) {\n        if (options === void 0) { options = {}; }\n        return this.request('GET', url, /** @type {?} */ (options));\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * HEAD request to be executed on the server. See the individual overloads for\n     * details of `head()`'s return type based on the provided options.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * HEAD request to be executed on the server. See the individual overloads for\n     * details of `head()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.head = /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * HEAD request to be executed on the server. See the individual overloads for\n     * details of `head()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    function (url, options) {\n        if (options === void 0) { options = {}; }\n        return this.request('HEAD', url, /** @type {?} */ (options));\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause a request\n     * with the special method `JSONP` to be dispatched via the interceptor pipeline.\n     *\n     * A suitable interceptor must be installed (e.g. via the `HttpClientJsonpModule`).\n     * If no such interceptor is reached, then the `JSONP` request will likely be\n     * rejected by the configured backend.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause a request\n     * with the special method `JSONP` to be dispatched via the interceptor pipeline.\n     *\n     * A suitable interceptor must be installed (e.g. via the `HttpClientJsonpModule`).\n     * If no such interceptor is reached, then the `JSONP` request will likely be\n     * rejected by the configured backend.\n     * @template T\n     * @param {?} url\n     * @param {?} callbackParam\n     * @return {?}\n     */\n    HttpClient.prototype.jsonp = /**\n     * Constructs an `Observable` which, when subscribed, will cause a request\n     * with the special method `JSONP` to be dispatched via the interceptor pipeline.\n     *\n     * A suitable interceptor must be installed (e.g. via the `HttpClientJsonpModule`).\n     * If no such interceptor is reached, then the `JSONP` request will likely be\n     * rejected by the configured backend.\n     * @template T\n     * @param {?} url\n     * @param {?} callbackParam\n     * @return {?}\n     */\n    function (url, callbackParam) {\n        return this.request('JSONP', url, {\n            params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n            observe: 'body',\n            responseType: 'json',\n        });\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * OPTIONS request to be executed on the server. See the individual overloads for\n     * details of `options()`'s return type based on the provided options.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * OPTIONS request to be executed on the server. See the individual overloads for\n     * details of `options()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.options = /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * OPTIONS request to be executed on the server. See the individual overloads for\n     * details of `options()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?=} options\n     * @return {?}\n     */\n    function (url, options) {\n        if (options === void 0) { options = {}; }\n        return this.request('OPTIONS', url, /** @type {?} */ (options));\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * PATCH request to be executed on the server. See the individual overloads for\n     * details of `patch()`'s return type based on the provided options.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * PATCH request to be executed on the server. See the individual overloads for\n     * details of `patch()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?} body\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.patch = /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * PATCH request to be executed on the server. See the individual overloads for\n     * details of `patch()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?} body\n     * @param {?=} options\n     * @return {?}\n     */\n    function (url, body, options) {\n        if (options === void 0) { options = {}; }\n        return this.request('PATCH', url, addBody(options, body));\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * POST request to be executed on the server. See the individual overloads for\n     * details of `post()`'s return type based on the provided options.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * POST request to be executed on the server. See the individual overloads for\n     * details of `post()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?} body\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.post = /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * POST request to be executed on the server. See the individual overloads for\n     * details of `post()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?} body\n     * @param {?=} options\n     * @return {?}\n     */\n    function (url, body, options) {\n        if (options === void 0) { options = {}; }\n        return this.request('POST', url, addBody(options, body));\n    };\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * POST request to be executed on the server. See the individual overloads for\n     * details of `post()`'s return type based on the provided options.\n     */\n    /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * POST request to be executed on the server. See the individual overloads for\n     * details of `post()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?} body\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClient.prototype.put = /**\n     * Constructs an `Observable` which, when subscribed, will cause the configured\n     * POST request to be executed on the server. See the individual overloads for\n     * details of `post()`'s return type based on the provided options.\n     * @param {?} url\n     * @param {?} body\n     * @param {?=} options\n     * @return {?}\n     */\n    function (url, body, options) {\n        if (options === void 0) { options = {}; }\n        return this.request('PUT', url, addBody(options, body));\n    };\n    HttpClient.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    HttpClient.ctorParameters = function () { return [\n        { type: HttpHandler, },\n    ]; };\n    return HttpClient;\n}());\nexport { HttpClient };\nfunction HttpClient_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpClient.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpClient.ctorParameters;\n    /** @type {?} */\n    HttpClient.prototype.handler;\n}\n//# sourceMappingURL=client.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Injectable, InjectionToken } from '@angular/core';\n/**\n * Intercepts `HttpRequest` and handles them.\n *\n * Most interceptors will transform the outgoing request before passing it to the\n * next interceptor in the chain, by calling `next.handle(transformedReq)`.\n *\n * In rare cases, interceptors may wish to completely handle a request themselves,\n * and not delegate to the remainder of the chain. This behavior is allowed.\n *\n * \\@stable\n * @record\n */\nexport function HttpInterceptor() { }\nfunction HttpInterceptor_tsickle_Closure_declarations() {\n    /**\n     * Intercept an outgoing `HttpRequest` and optionally transform it or the\n     * response.\n     *\n     * Typically an interceptor will transform the outgoing request before returning\n     * `next.handle(transformedReq)`. An interceptor may choose to transform the\n     * response event stream as well, by applying additional Rx operators on the stream\n     * returned by `next.handle()`.\n     *\n     * More rarely, an interceptor may choose to completely handle the request itself,\n     * and compose a new event stream instead of invoking `next.handle()`. This is\n     * acceptable behavior, but keep in mind further interceptors will be skipped entirely.\n     *\n     * It is also rare but valid for an interceptor to return multiple responses on the\n     * event stream for a single request.\n     * @type {?}\n     */\n    HttpInterceptor.prototype.intercept;\n}\n/**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n * \\@stable\n */\nvar /**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n * \\@stable\n */\nHttpInterceptorHandler = /** @class */ (function () {\n    function HttpInterceptorHandler(next, interceptor) {\n        this.next = next;\n        this.interceptor = interceptor;\n    }\n    /**\n     * @param {?} req\n     * @return {?}\n     */\n    HttpInterceptorHandler.prototype.handle = /**\n     * @param {?} req\n     * @return {?}\n     */\n    function (req) {\n        return this.interceptor.intercept(req, this.next);\n    };\n    return HttpInterceptorHandler;\n}());\n/**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n * \\@stable\n */\nexport { HttpInterceptorHandler };\nfunction HttpInterceptorHandler_tsickle_Closure_declarations() {\n    /** @type {?} */\n    HttpInterceptorHandler.prototype.next;\n    /** @type {?} */\n    HttpInterceptorHandler.prototype.interceptor;\n}\n/**\n * A multi-provider token which represents the array of `HttpInterceptor`s that\n * are registered.\n *\n * \\@stable\n */\nexport var /** @type {?} */ HTTP_INTERCEPTORS = new InjectionToken('HTTP_INTERCEPTORS');\nvar NoopInterceptor = /** @class */ (function () {\n    function NoopInterceptor() {\n    }\n    /**\n     * @param {?} req\n     * @param {?} next\n     * @return {?}\n     */\n    NoopInterceptor.prototype.intercept = /**\n     * @param {?} req\n     * @param {?} next\n     * @return {?}\n     */\n    function (req, next) {\n        return next.handle(req);\n    };\n    NoopInterceptor.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    NoopInterceptor.ctorParameters = function () { return []; };\n    return NoopInterceptor;\n}());\nexport { NoopInterceptor };\nfunction NoopInterceptor_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    NoopInterceptor.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    NoopInterceptor.ctorParameters;\n}\n//# sourceMappingURL=interceptor.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { DOCUMENT } from '@angular/common';\nimport { Inject, Injectable } from '@angular/core';\nimport { Observable } from 'rxjs/Observable';\nimport { HttpErrorResponse, HttpEventType, HttpResponse } from './response';\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nvar /** @type {?} */ nextRequestId = 0;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nexport var /** @type {?} */ JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nexport var /** @type {?} */ JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nexport var /** @type {?} */ JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n * \\@stable\n * @abstract\n */\nJsonpCallbackContext = /** @class */ (function () {\n    function JsonpCallbackContext() {\n    }\n    return JsonpCallbackContext;\n}());\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n * \\@stable\n * @abstract\n */\nexport { JsonpCallbackContext };\n/**\n * `HttpBackend` that only processes `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n *\n * \\@stable\n */\nvar JsonpClientBackend = /** @class */ (function () {\n    function JsonpClientBackend(callbackMap, document) {\n        this.callbackMap = callbackMap;\n        this.document = document;\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     * @return {?}\n     */\n    JsonpClientBackend.prototype.nextCallback = /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     * @return {?}\n     */\n    function () { return \"ng_jsonp_callback_\" + nextRequestId++; };\n    /**\n     * Process a JSONP request and return an event stream of the results.\n     */\n    /**\n     * Process a JSONP request and return an event stream of the results.\n     * @param {?} req\n     * @return {?}\n     */\n    JsonpClientBackend.prototype.handle = /**\n     * Process a JSONP request and return an event stream of the results.\n     * @param {?} req\n     * @return {?}\n     */\n    function (req) {\n        var _this = this;\n        // Firstly, check both the method and response type. If either doesn't match\n        // then the request was improperly routed here and cannot be handled.\n        if (req.method !== 'JSONP') {\n            throw new Error(JSONP_ERR_WRONG_METHOD);\n        }\n        else if (req.responseType !== 'json') {\n            throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n        }\n        // Everything else happens inside the Observable boundary.\n        return new Observable(function (observer) {\n            // The first step to make a request is to generate the callback name, and replace the\n            // callback placeholder in the URL with the name. Care has to be taken here to ensure\n            // a trailing &, if matched, gets inserted back into the URL in the correct place.\n            var /** @type {?} */ callback = _this.nextCallback();\n            var /** @type {?} */ url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, \"=\" + callback + \"$1\");\n            // Construct the <script> tag and point it at the URL.\n            var /** @type {?} */ node = _this.document.createElement('script');\n            node.src = url;\n            // A JSONP request requires waiting for multiple callbacks. These variables\n            // are closed over and track state across those callbacks.\n            // The response object, if one has been received, or null otherwise.\n            var /** @type {?} */ body = null;\n            // Whether the response callback has been called.\n            var /** @type {?} */ finished = false;\n            // Whether the request has been cancelled (and thus any other callbacks)\n            // should be ignored.\n            var /** @type {?} */ cancelled = false;\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            _this.callbackMap[callback] = function (data) {\n                // Data has been received from the JSONP script. Firstly, delete this callback.\n                delete _this.callbackMap[callback];\n                // Next, make sure the request wasn't cancelled in the meantime.\n                if (cancelled) {\n                    return;\n                }\n                // Set state to indicate data was received.\n                body = data;\n                finished = true;\n            };\n            // cleanup() is a utility closure that removes the <script> from the page and\n            // the response callback from the window. This logic is used in both the\n            // success, error, and cancellation paths, so it's extracted out for convenience.\n            var /** @type {?} */ cleanup = function () {\n                // Remove the <script> tag if it's still on the page.\n                if (node.parentNode) {\n                    node.parentNode.removeChild(node);\n                }\n                // Remove the response callback from the callbackMap (window object in the\n                // browser).\n                delete _this.callbackMap[callback];\n            };\n            // onLoad() is the success callback which runs after the response callback\n            // if the JSONP script loads successfully. The event itself is unimportant.\n            // If something went wrong, onLoad() may run without the response callback\n            // having been invoked.\n            var /** @type {?} */ onLoad = function (event) {\n                // Do nothing if the request has been cancelled.\n                if (cancelled) {\n                    return;\n                }\n                // Cleanup the page.\n                cleanup();\n                // Check whether the response callback has run.\n                if (!finished) {\n                    // It hasn't, something went wrong with the request. Return an error via\n                    // the Observable error path. All JSONP errors have status 0.\n                    observer.error(new HttpErrorResponse({\n                        url: url,\n                        status: 0,\n                        statusText: 'JSONP Error',\n                        error: new Error(JSONP_ERR_NO_CALLBACK),\n                    }));\n                    return;\n                }\n                // Success. body either contains the response body or null if none was\n                // returned.\n                observer.next(new HttpResponse({\n                    body: body,\n                    status: 200,\n                    statusText: 'OK', url: url,\n                }));\n                // Complete the stream, the response is over.\n                observer.complete();\n            };\n            // onError() is the error callback, which runs if the script returned generates\n            // a Javascript error. It emits the error via the Observable error channel as\n            // a HttpErrorResponse.\n            var /** @type {?} */ onError = function (error) {\n                // If the request was already cancelled, no need to emit anything.\n                if (cancelled) {\n                    return;\n                }\n                cleanup();\n                // Wrap the error in a HttpErrorResponse.\n                observer.error(new HttpErrorResponse({\n                    error: error,\n                    status: 0,\n                    statusText: 'JSONP Error', url: url,\n                }));\n            };\n            // Subscribe to both the success (load) and error events on the <script> tag,\n            // and add it to the page.\n            node.addEventListener('load', onLoad);\n            node.addEventListener('error', onError);\n            _this.document.body.appendChild(node);\n            // The request has now been successfully sent.\n            observer.next({ type: HttpEventType.Sent });\n            // Cancellation handler.\n            return function () {\n                // Track the cancellation so event listeners won't do anything even if already scheduled.\n                cancelled = true;\n                // Remove the event listeners so they won't run if the events later fire.\n                node.removeEventListener('load', onLoad);\n                node.removeEventListener('error', onError);\n                // And finally, clean up the page.\n                cleanup();\n            };\n        });\n    };\n    JsonpClientBackend.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    JsonpClientBackend.ctorParameters = function () { return [\n        { type: JsonpCallbackContext, },\n        { type: undefined, decorators: [{ type: Inject, args: [DOCUMENT,] },] },\n    ]; };\n    return JsonpClientBackend;\n}());\nexport { JsonpClientBackend };\nfunction JsonpClientBackend_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    JsonpClientBackend.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    JsonpClientBackend.ctorParameters;\n    /** @type {?} */\n    JsonpClientBackend.prototype.callbackMap;\n    /** @type {?} */\n    JsonpClientBackend.prototype.document;\n}\n/**\n * An `HttpInterceptor` which identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * \\@stable\n */\nvar JsonpInterceptor = /** @class */ (function () {\n    function JsonpInterceptor(jsonp) {\n        this.jsonp = jsonp;\n    }\n    /**\n     * @param {?} req\n     * @param {?} next\n     * @return {?}\n     */\n    JsonpInterceptor.prototype.intercept = /**\n     * @param {?} req\n     * @param {?} next\n     * @return {?}\n     */\n    function (req, next) {\n        if (req.method === 'JSONP') {\n            return this.jsonp.handle(/** @type {?} */ (req));\n        }\n        // Fall through for normal HTTP requests.\n        return next.handle(req);\n    };\n    JsonpInterceptor.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    JsonpInterceptor.ctorParameters = function () { return [\n        { type: JsonpClientBackend, },\n    ]; };\n    return JsonpInterceptor;\n}());\nexport { JsonpInterceptor };\nfunction JsonpInterceptor_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    JsonpInterceptor.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    JsonpInterceptor.ctorParameters;\n    /** @type {?} */\n    JsonpInterceptor.prototype.jsonp;\n}\n//# sourceMappingURL=jsonp.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs/Observable';\nimport { HttpHeaders } from './headers';\nimport { HttpErrorResponse, HttpEventType, HttpHeaderResponse, HttpResponse } from './response';\nvar /** @type {?} */ XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n * @param {?} xhr\n * @return {?}\n */\nfunction getResponseUrl(xhr) {\n    if ('responseURL' in xhr && xhr.responseURL) {\n        return xhr.responseURL;\n    }\n    if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n        return xhr.getResponseHeader('X-Request-URL');\n    }\n    return null;\n}\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * \\@stable\n * @abstract\n */\nXhrFactory = /** @class */ (function () {\n    function XhrFactory() {\n    }\n    return XhrFactory;\n}());\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * \\@stable\n * @abstract\n */\nexport { XhrFactory };\nfunction XhrFactory_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @return {?}\n     */\n    XhrFactory.prototype.build = function () { };\n}\n/**\n * A factory for \\@{link HttpXhrBackend} that uses the `XMLHttpRequest` browser API.\n *\n * \\@stable\n */\nvar BrowserXhr = /** @class */ (function () {\n    function BrowserXhr() {\n    }\n    /**\n     * @return {?}\n     */\n    BrowserXhr.prototype.build = /**\n     * @return {?}\n     */\n    function () { return /** @type {?} */ ((new XMLHttpRequest())); };\n    BrowserXhr.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    BrowserXhr.ctorParameters = function () { return []; };\n    return BrowserXhr;\n}());\nexport { BrowserXhr };\nfunction BrowserXhr_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    BrowserXhr.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    BrowserXhr.ctorParameters;\n}\n/**\n * Tracks a response from the server that does not yet have a body.\n * @record\n */\nfunction PartialResponse() { }\nfunction PartialResponse_tsickle_Closure_declarations() {\n    /** @type {?} */\n    PartialResponse.prototype.headers;\n    /** @type {?} */\n    PartialResponse.prototype.status;\n    /** @type {?} */\n    PartialResponse.prototype.statusText;\n    /** @type {?} */\n    PartialResponse.prototype.url;\n}\n/**\n * An `HttpBackend` which uses the XMLHttpRequest API to send\n * requests to a backend server.\n *\n * \\@stable\n */\nvar HttpXhrBackend = /** @class */ (function () {\n    function HttpXhrBackend(xhrFactory) {\n        this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Process a request and return a stream of response events.\n     */\n    /**\n     * Process a request and return a stream of response events.\n     * @param {?} req\n     * @return {?}\n     */\n    HttpXhrBackend.prototype.handle = /**\n     * Process a request and return a stream of response events.\n     * @param {?} req\n     * @return {?}\n     */\n    function (req) {\n        var _this = this;\n        // Quick check to give a better error message when a user attempts to use\n        // HttpClient.jsonp() without installing the JsonpClientModule\n        if (req.method === 'JSONP') {\n            throw new Error(\"Attempted to construct Jsonp request without JsonpClientModule installed.\");\n        }\n        // Everything happens on Observable subscription.\n        return new Observable(function (observer) {\n            // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n            var /** @type {?} */ xhr = _this.xhrFactory.build();\n            xhr.open(req.method, req.urlWithParams);\n            if (!!req.withCredentials) {\n                xhr.withCredentials = true;\n            }\n            // Add all the requested headers.\n            req.headers.forEach(function (name, values) { return xhr.setRequestHeader(name, values.join(',')); });\n            // Add an Accept header if one isn't present already.\n            if (!req.headers.has('Accept')) {\n                xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n            }\n            // Auto-detect the Content-Type header if one isn't present already.\n            if (!req.headers.has('Content-Type')) {\n                var /** @type {?} */ detectedType = req.detectContentTypeHeader();\n                // Sometimes Content-Type detection fails.\n                if (detectedType !== null) {\n                    xhr.setRequestHeader('Content-Type', detectedType);\n                }\n            }\n            // Set the responseType if one was requested.\n            if (req.responseType) {\n                var /** @type {?} */ responseType = req.responseType.toLowerCase();\n                // JSON responses need to be processed as text. This is because if the server\n                // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n                // xhr.response will be null, and xhr.responseText cannot be accessed to\n                // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n                // is parsed by first requesting text and then applying JSON.parse.\n                xhr.responseType = /** @type {?} */ (((responseType !== 'json') ? responseType : 'text'));\n            }\n            // Serialize the request body if one is present. If not, this will be set to null.\n            var /** @type {?} */ reqBody = req.serializeBody();\n            // If progress events are enabled, response headers will be delivered\n            // in two events - the HttpHeaderResponse event and the full HttpResponse\n            // event. However, since response headers don't change in between these\n            // two events, it doesn't make sense to parse them twice. So headerResponse\n            // caches the data extracted from the response whenever it's first parsed,\n            // to ensure parsing isn't duplicated.\n            var /** @type {?} */ headerResponse = null;\n            // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n            // state, and memoizes it into headerResponse.\n            var /** @type {?} */ partialFromXhr = function () {\n                if (headerResponse !== null) {\n                    return headerResponse;\n                }\n                // Read status and normalize an IE9 bug (http://bugs.jquery.com/ticket/1450).\n                var /** @type {?} */ status = xhr.status === 1223 ? 204 : xhr.status;\n                var /** @type {?} */ statusText = xhr.statusText || 'OK';\n                // Parse headers from XMLHttpRequest - this step is lazy.\n                var /** @type {?} */ headers = new HttpHeaders(xhr.getAllResponseHeaders());\n                // Read the response URL from the XMLHttpResponse instance and fall back on the\n                // request URL.\n                var /** @type {?} */ url = getResponseUrl(xhr) || req.url;\n                // Construct the HttpHeaderResponse and memoize it.\n                headerResponse = new HttpHeaderResponse({ headers: headers, status: status, statusText: statusText, url: url });\n                return headerResponse;\n            };\n            // Next, a few closures are defined for the various events which XMLHttpRequest can\n            // emit. This allows them to be unregistered as event listeners later.\n            // First up is the load event, which represents a response being fully available.\n            var /** @type {?} */ onLoad = function () {\n                // Read response state from the memoized partial data.\n                var _a = partialFromXhr(), headers = _a.headers, status = _a.status, statusText = _a.statusText, url = _a.url;\n                // The body will be read out if present.\n                var /** @type {?} */ body = null;\n                if (status !== 204) {\n                    // Use XMLHttpRequest.response if set, responseText otherwise.\n                    body = (typeof xhr.response === 'undefined') ? xhr.responseText : xhr.response;\n                }\n                // Normalize another potential bug (this one comes from CORS).\n                if (status === 0) {\n                    status = !!body ? 200 : 0;\n                }\n                // ok determines whether the response will be transmitted on the event or\n                // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n                // but a successful status code can still result in an error if the user\n                // asked for JSON data and the body cannot be parsed as such.\n                var /** @type {?} */ ok = status >= 200 && status < 300;\n                // Check whether the body needs to be parsed as JSON (in many cases the browser\n                // will have done that already).\n                if (req.responseType === 'json' && typeof body === 'string') {\n                    // Save the original body, before attempting XSSI prefix stripping.\n                    var /** @type {?} */ originalBody = body;\n                    body = body.replace(XSSI_PREFIX, '');\n                    try {\n                        // Attempt the parse. If it fails, a parse error should be delivered to the user.\n                        body = body !== '' ? JSON.parse(body) : null;\n                    }\n                    catch (/** @type {?} */ error) {\n                        // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n                        // JSON response. Restore the original body (including any XSSI prefix) to deliver\n                        // a better error response.\n                        body = originalBody;\n                        // If this was an error request to begin with, leave it as a string, it probably\n                        // just isn't JSON. Otherwise, deliver the parsing error to the user.\n                        if (ok) {\n                            // Even though the response status was 2xx, this is still an error.\n                            ok = false;\n                            // The parse error contains the text of the body that failed to parse.\n                            body = /** @type {?} */ ({ error: error, text: body });\n                        }\n                    }\n                }\n                if (ok) {\n                    // A successful response is delivered on the event stream.\n                    observer.next(new HttpResponse({\n                        body: body,\n                        headers: headers,\n                        status: status,\n                        statusText: statusText,\n                        url: url || undefined,\n                    }));\n                    // The full body has been received and delivered, no further events\n                    // are possible. This request is complete.\n                    observer.complete();\n                }\n                else {\n                    // An unsuccessful request is delivered on the error channel.\n                    observer.error(new HttpErrorResponse({\n                        // The error in this case is the response body (error from the server).\n                        error: body,\n                        headers: headers,\n                        status: status,\n                        statusText: statusText,\n                        url: url || undefined,\n                    }));\n                }\n            };\n            // The onError callback is called when something goes wrong at the network level.\n            // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n            // transmitted on the error channel.\n            var /** @type {?} */ onError = function (error) {\n                var /** @type {?} */ res = new HttpErrorResponse({\n                    error: error,\n                    status: xhr.status || 0,\n                    statusText: xhr.statusText || 'Unknown Error',\n                });\n                observer.error(res);\n            };\n            // The sentHeaders flag tracks whether the HttpResponseHeaders event\n            // has been sent on the stream. This is necessary to track if progress\n            // is enabled since the event will be sent on only the first download\n            // progerss event.\n            var /** @type {?} */ sentHeaders = false;\n            // The download progress event handler, which is only registered if\n            // progress events are enabled.\n            var /** @type {?} */ onDownProgress = function (event) {\n                // Send the HttpResponseHeaders event if it hasn't been sent already.\n                if (!sentHeaders) {\n                    observer.next(partialFromXhr());\n                    sentHeaders = true;\n                }\n                // Start building the download progress event to deliver on the response\n                // event stream.\n                var /** @type {?} */ progressEvent = {\n                    type: HttpEventType.DownloadProgress,\n                    loaded: event.loaded,\n                };\n                // Set the total number of bytes in the event if it's available.\n                if (event.lengthComputable) {\n                    progressEvent.total = event.total;\n                }\n                // If the request was for text content and a partial response is\n                // available on XMLHttpRequest, include it in the progress event\n                // to allow for streaming reads.\n                if (req.responseType === 'text' && !!xhr.responseText) {\n                    progressEvent.partialText = xhr.responseText;\n                }\n                // Finally, fire the event.\n                observer.next(progressEvent);\n            };\n            // The upload progress event handler, which is only registered if\n            // progress events are enabled.\n            var /** @type {?} */ onUpProgress = function (event) {\n                // Upload progress events are simpler. Begin building the progress\n                // event.\n                var /** @type {?} */ progress = {\n                    type: HttpEventType.UploadProgress,\n                    loaded: event.loaded,\n                };\n                // If the total number of bytes being uploaded is available, include\n                // it.\n                if (event.lengthComputable) {\n                    progress.total = event.total;\n                }\n                // Send the event.\n                observer.next(progress);\n            };\n            // By default, register for load and error events.\n            xhr.addEventListener('load', onLoad);\n            xhr.addEventListener('error', onError);\n            // Progress events are only enabled if requested.\n            if (req.reportProgress) {\n                // Download progress is always enabled if requested.\n                xhr.addEventListener('progress', onDownProgress);\n                // Upload progress depends on whether there is a body to upload.\n                if (reqBody !== null && xhr.upload) {\n                    xhr.upload.addEventListener('progress', onUpProgress);\n                }\n            }\n            // Fire the request, and notify the event stream that it was fired.\n            xhr.send(reqBody);\n            observer.next({ type: HttpEventType.Sent });\n            // This is the return from the Observable function, which is the\n            // request cancellation handler.\n            return function () {\n                // On a cancellation, remove all registered event listeners.\n                xhr.removeEventListener('error', onError);\n                xhr.removeEventListener('load', onLoad);\n                if (req.reportProgress) {\n                    xhr.removeEventListener('progress', onDownProgress);\n                    if (reqBody !== null && xhr.upload) {\n                        xhr.upload.removeEventListener('progress', onUpProgress);\n                    }\n                }\n                // Finally, abort the in-flight request.\n                xhr.abort();\n            };\n        });\n    };\n    HttpXhrBackend.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    HttpXhrBackend.ctorParameters = function () { return [\n        { type: XhrFactory, },\n    ]; };\n    return HttpXhrBackend;\n}());\nexport { HttpXhrBackend };\nfunction HttpXhrBackend_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpXhrBackend.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpXhrBackend.ctorParameters;\n    /** @type {?} */\n    HttpXhrBackend.prototype.xhrFactory;\n}\n//# sourceMappingURL=xhr.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { DOCUMENT, ɵparseCookieValue as parseCookieValue } from '@angular/common';\nimport { Inject, Injectable, InjectionToken, PLATFORM_ID } from '@angular/core';\nexport var /** @type {?} */ XSRF_COOKIE_NAME = new InjectionToken('XSRF_COOKIE_NAME');\nexport var /** @type {?} */ XSRF_HEADER_NAME = new InjectionToken('XSRF_HEADER_NAME');\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * \\@stable\n * @abstract\n */\nHttpXsrfTokenExtractor = /** @class */ (function () {\n    function HttpXsrfTokenExtractor() {\n    }\n    return HttpXsrfTokenExtractor;\n}());\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * \\@stable\n * @abstract\n */\nexport { HttpXsrfTokenExtractor };\nfunction HttpXsrfTokenExtractor_tsickle_Closure_declarations() {\n    /**\n     * Get the XSRF token to use with an outgoing request.\n     *\n     * Will be called for every request, so the token may change between requests.\n     * @abstract\n     * @return {?}\n     */\n    HttpXsrfTokenExtractor.prototype.getToken = function () { };\n}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nvar HttpXsrfCookieExtractor = /** @class */ (function () {\n    function HttpXsrfCookieExtractor(doc, platform, cookieName) {\n        this.doc = doc;\n        this.platform = platform;\n        this.cookieName = cookieName;\n        this.lastCookieString = '';\n        this.lastToken = null;\n        /**\n         * \\@internal for testing\n         */\n        this.parseCount = 0;\n    }\n    /**\n     * @return {?}\n     */\n    HttpXsrfCookieExtractor.prototype.getToken = /**\n     * @return {?}\n     */\n    function () {\n        if (this.platform === 'server') {\n            return null;\n        }\n        var /** @type {?} */ cookieString = this.doc.cookie || '';\n        if (cookieString !== this.lastCookieString) {\n            this.parseCount++;\n            this.lastToken = parseCookieValue(cookieString, this.cookieName);\n            this.lastCookieString = cookieString;\n        }\n        return this.lastToken;\n    };\n    HttpXsrfCookieExtractor.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    HttpXsrfCookieExtractor.ctorParameters = function () { return [\n        { type: undefined, decorators: [{ type: Inject, args: [DOCUMENT,] },] },\n        { type: undefined, decorators: [{ type: Inject, args: [PLATFORM_ID,] },] },\n        { type: undefined, decorators: [{ type: Inject, args: [XSRF_COOKIE_NAME,] },] },\n    ]; };\n    return HttpXsrfCookieExtractor;\n}());\nexport { HttpXsrfCookieExtractor };\nfunction HttpXsrfCookieExtractor_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpXsrfCookieExtractor.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpXsrfCookieExtractor.ctorParameters;\n    /** @type {?} */\n    HttpXsrfCookieExtractor.prototype.lastCookieString;\n    /** @type {?} */\n    HttpXsrfCookieExtractor.prototype.lastToken;\n    /**\n     * \\@internal for testing\n     * @type {?}\n     */\n    HttpXsrfCookieExtractor.prototype.parseCount;\n    /** @type {?} */\n    HttpXsrfCookieExtractor.prototype.doc;\n    /** @type {?} */\n    HttpXsrfCookieExtractor.prototype.platform;\n    /** @type {?} */\n    HttpXsrfCookieExtractor.prototype.cookieName;\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nvar HttpXsrfInterceptor = /** @class */ (function () {\n    function HttpXsrfInterceptor(tokenService, headerName) {\n        this.tokenService = tokenService;\n        this.headerName = headerName;\n    }\n    /**\n     * @param {?} req\n     * @param {?} next\n     * @return {?}\n     */\n    HttpXsrfInterceptor.prototype.intercept = /**\n     * @param {?} req\n     * @param {?} next\n     * @return {?}\n     */\n    function (req, next) {\n        var /** @type {?} */ lcUrl = req.url.toLowerCase();\n        // Skip both non-mutating requests and absolute URLs.\n        // Non-mutating requests don't require a token, and absolute URLs require special handling\n        // anyway as the cookie set\n        // on our origin is not the same as the token expected by another origin.\n        if (req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') ||\n            lcUrl.startsWith('https://')) {\n            return next.handle(req);\n        }\n        var /** @type {?} */ token = this.tokenService.getToken();\n        // Be careful not to overwrite an existing header of the same name.\n        if (token !== null && !req.headers.has(this.headerName)) {\n            req = req.clone({ headers: req.headers.set(this.headerName, token) });\n        }\n        return next.handle(req);\n    };\n    HttpXsrfInterceptor.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    HttpXsrfInterceptor.ctorParameters = function () { return [\n        { type: HttpXsrfTokenExtractor, },\n        { type: undefined, decorators: [{ type: Inject, args: [XSRF_HEADER_NAME,] },] },\n    ]; };\n    return HttpXsrfInterceptor;\n}());\nexport { HttpXsrfInterceptor };\nfunction HttpXsrfInterceptor_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpXsrfInterceptor.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpXsrfInterceptor.ctorParameters;\n    /** @type {?} */\n    HttpXsrfInterceptor.prototype.tokenService;\n    /** @type {?} */\n    HttpXsrfInterceptor.prototype.headerName;\n}\n//# sourceMappingURL=xsrf.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Injectable, Injector, NgModule } from '@angular/core';\nimport { HttpBackend, HttpHandler } from './backend';\nimport { HttpClient } from './client';\nimport { HTTP_INTERCEPTORS, HttpInterceptorHandler, NoopInterceptor } from './interceptor';\nimport { JsonpCallbackContext, JsonpClientBackend, JsonpInterceptor } from './jsonp';\nimport { BrowserXhr, HttpXhrBackend, XhrFactory } from './xhr';\nimport { HttpXsrfCookieExtractor, HttpXsrfInterceptor, HttpXsrfTokenExtractor, XSRF_COOKIE_NAME, XSRF_HEADER_NAME } from './xsrf';\n/**\n * An `<PERSON>tt<PERSON>Hand<PERSON>` that applies a bunch of `HttpInterceptor`s\n * to a request before passing it to the given `HttpBackend`.\n *\n * The interceptors are loaded lazily from the injector, to allow\n * interceptors to themselves inject classes depending indirectly\n * on `HttpInterceptingHandler` itself.\n */\nvar HttpInterceptingHandler = /** @class */ (function () {\n    function HttpInterceptingHandler(backend, injector) {\n        this.backend = backend;\n        this.injector = injector;\n        this.chain = null;\n    }\n    /**\n     * @param {?} req\n     * @return {?}\n     */\n    HttpInterceptingHandler.prototype.handle = /**\n     * @param {?} req\n     * @return {?}\n     */\n    function (req) {\n        if (this.chain === null) {\n            var /** @type {?} */ interceptors = this.injector.get(HTTP_INTERCEPTORS, []);\n            this.chain = interceptors.reduceRight(function (next, interceptor) { return new HttpInterceptorHandler(next, interceptor); }, this.backend);\n        }\n        return this.chain.handle(req);\n    };\n    HttpInterceptingHandler.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    HttpInterceptingHandler.ctorParameters = function () { return [\n        { type: HttpBackend, },\n        { type: Injector, },\n    ]; };\n    return HttpInterceptingHandler;\n}());\nexport { HttpInterceptingHandler };\nfunction HttpInterceptingHandler_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpInterceptingHandler.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpInterceptingHandler.ctorParameters;\n    /** @type {?} */\n    HttpInterceptingHandler.prototype.chain;\n    /** @type {?} */\n    HttpInterceptingHandler.prototype.backend;\n    /** @type {?} */\n    HttpInterceptingHandler.prototype.injector;\n}\n/**\n * Constructs an `HttpHandler` that applies a bunch of `HttpInterceptor`s\n * to a request before passing it to the given `HttpBackend`.\n *\n * Meant to be used as a factory function within `HttpClientModule`.\n *\n * \\@stable\n * @param {?} backend\n * @param {?=} interceptors\n * @return {?}\n */\nexport function interceptingHandler(backend, interceptors) {\n    if (interceptors === void 0) { interceptors = []; }\n    if (!interceptors) {\n        return backend;\n    }\n    return interceptors.reduceRight(function (next, interceptor) { return new HttpInterceptorHandler(next, interceptor); }, backend);\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n * \\@stable\n * @return {?}\n */\nexport function jsonpCallbackContext() {\n    if (typeof window === 'object') {\n        return window;\n    }\n    return {};\n}\n/**\n * `NgModule` which adds XSRF protection support to outgoing requests.\n *\n * Provided the server supports a cookie-based XSRF protection system, this\n * module can be used directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no such names are provided, the default is to use `X-XSRF-TOKEN` for\n * the header name and `XSRF-TOKEN` for the cookie name.\n *\n * \\@stable\n */\nvar HttpClientXsrfModule = /** @class */ (function () {\n    function HttpClientXsrfModule() {\n    }\n    /**\n     * Disable the default XSRF protection.\n     */\n    /**\n     * Disable the default XSRF protection.\n     * @return {?}\n     */\n    HttpClientXsrfModule.disable = /**\n     * Disable the default XSRF protection.\n     * @return {?}\n     */\n    function () {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [\n                { provide: HttpXsrfInterceptor, useClass: NoopInterceptor },\n            ],\n        };\n    };\n    /**\n     * Configure XSRF protection to use the given cookie name or header name,\n     * or the default names (as described above) if not provided.\n     */\n    /**\n     * Configure XSRF protection to use the given cookie name or header name,\n     * or the default names (as described above) if not provided.\n     * @param {?=} options\n     * @return {?}\n     */\n    HttpClientXsrfModule.withOptions = /**\n     * Configure XSRF protection to use the given cookie name or header name,\n     * or the default names (as described above) if not provided.\n     * @param {?=} options\n     * @return {?}\n     */\n    function (options) {\n        if (options === void 0) { options = {}; }\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [\n                options.cookieName ? { provide: XSRF_COOKIE_NAME, useValue: options.cookieName } : [],\n                options.headerName ? { provide: XSRF_HEADER_NAME, useValue: options.headerName } : [],\n            ],\n        };\n    };\n    HttpClientXsrfModule.decorators = [\n        { type: NgModule, args: [{\n                    providers: [\n                        HttpXsrfInterceptor,\n                        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n                        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n                        { provide: XSRF_COOKIE_NAME, useValue: 'XSRF-TOKEN' },\n                        { provide: XSRF_HEADER_NAME, useValue: 'X-XSRF-TOKEN' },\n                    ],\n                },] },\n    ];\n    /** @nocollapse */\n    HttpClientXsrfModule.ctorParameters = function () { return []; };\n    return HttpClientXsrfModule;\n}());\nexport { HttpClientXsrfModule };\nfunction HttpClientXsrfModule_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpClientXsrfModule.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpClientXsrfModule.ctorParameters;\n}\n/**\n * `NgModule` which provides the `HttpClient` and associated services.\n *\n * Interceptors can be added to the chain behind `HttpClient` by binding them\n * to the multiprovider for `HTTP_INTERCEPTORS`.\n *\n * \\@stable\n */\nvar HttpClientModule = /** @class */ (function () {\n    function HttpClientModule() {\n    }\n    HttpClientModule.decorators = [\n        { type: NgModule, args: [{\n                    imports: [\n                        HttpClientXsrfModule.withOptions({\n                            cookieName: 'XSRF-TOKEN',\n                            headerName: 'X-XSRF-TOKEN',\n                        }),\n                    ],\n                    providers: [\n                        HttpClient,\n                        { provide: HttpHandler, useClass: HttpInterceptingHandler },\n                        HttpXhrBackend,\n                        { provide: HttpBackend, useExisting: HttpXhrBackend },\n                        BrowserXhr,\n                        { provide: XhrFactory, useExisting: BrowserXhr },\n                    ],\n                },] },\n    ];\n    /** @nocollapse */\n    HttpClientModule.ctorParameters = function () { return []; };\n    return HttpClientModule;\n}());\nexport { HttpClientModule };\nfunction HttpClientModule_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpClientModule.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpClientModule.ctorParameters;\n}\n/**\n * `NgModule` which enables JSONP support in `HttpClient`.\n *\n * Without this module, Jsonp requests will reach the backend\n * with method JSONP, where they'll be rejected.\n *\n * \\@stable\n */\nvar HttpClientJsonpModule = /** @class */ (function () {\n    function HttpClientJsonpModule() {\n    }\n    HttpClientJsonpModule.decorators = [\n        { type: NgModule, args: [{\n                    providers: [\n                        JsonpClientBackend,\n                        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n                        { provide: HTTP_INTERCEPTORS, useClass: JsonpInterceptor, multi: true },\n                    ],\n                },] },\n    ];\n    /** @nocollapse */\n    HttpClientJsonpModule.ctorParameters = function () { return []; };\n    return HttpClientJsonpModule;\n}());\nexport { HttpClientJsonpModule };\nfunction HttpClientJsonpModule_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpClientJsonpModule.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpClientJsonpModule.ctorParameters;\n}\n//# sourceMappingURL=module.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport { HttpBackend, HttpHandler } from './src/backend';\nexport { HttpClient } from './src/client';\nexport { HttpHeaders } from './src/headers';\nexport { HTTP_INTERCEPTORS } from './src/interceptor';\nexport { JsonpClientBackend, JsonpInterceptor } from './src/jsonp';\nexport { HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, interceptingHandler as ɵinterceptingHandler } from './src/module';\nexport { HttpParams, HttpUrlEncodingCodec } from './src/params';\nexport { HttpRequest } from './src/request';\nexport { HttpErrorResponse, HttpEventType, HttpHeaderResponse, HttpResponse, HttpResponseBase } from './src/response';\nexport { HttpXhrBackend, XhrFactory } from './src/xhr';\nexport { HttpXsrfTokenExtractor } from './src/xsrf';\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { HttpBackend, HttpHandler, HttpClient, HttpHeaders, HTTP_INTERCEPTORS, JsonpClientBackend, JsonpInterceptor, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, ɵinterceptingHandler, HttpParams, HttpUrlEncodingCodec, HttpRequest, HttpErrorResponse, HttpEventType, HttpHeaderResponse, HttpResponse, HttpResponseBase, HttpXhrBackend, XhrFactory, HttpXsrfTokenExtractor } from './public_api';\nexport { NoopInterceptor as ɵa } from './src/interceptor';\nexport { JsonpCallbackContext as ɵb } from './src/jsonp';\nexport { HttpInterceptingHandler as ɵc, jsonpCallbackContext as ɵd } from './src/module';\nexport { BrowserXhr as ɵe } from './src/xhr';\nexport { HttpXsrfCookieExtractor as ɵh, HttpXsrfInterceptor as ɵi, XSRF_COOKIE_NAME as ɵf, XSRF_HEADER_NAME as ɵg } from './src/xsrf';\n//# sourceMappingURL=http.js.map"], "names": ["map", "tslib_1.__extends", "parseCookieValue"], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAaA,WAAW,kBAAkB,YAAY;IACrC,SAAS,WAAW,GAAG;KACtB;IACD,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC,CAAC;AACL,AAsBA;;;;;;;;;;;AAWA,IAWA,WAAW,kBAAkB,YAAY;IACrC,SAAS,WAAW,GAAG;KACtB;IACD,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC;;AC1FJ;;;;;;;;;;;;;;AAcA,AASA;;;;AAIA,IAIA,WAAW,kBAAkB,YAAY;IACrC,SAAS,WAAW,CAAC,OAAO,EAAE;QAC1B,IAAI,KAAK,GAAG,IAAI,CAAC;;;;;QAKjB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;;;;QAIjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;SAC5B;aACI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAClC,IAAI,CAAC,QAAQ,GAAG,YAAY;gBACxB,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;gBAC1B,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;oBACxC,qBAAqB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBAC/C,IAAI,KAAK,GAAG,CAAC,EAAE;wBACX,qBAAqB,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;wBACnD,qBAAqB,GAAG,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;wBAChD,qBAAqB,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBAC1D,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;wBAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;6CACP,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;yBAC3D;6BACI;4BACD,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;yBACnC;qBACJ;iBACJ,CAAC,CAAC;aACN,CAAC;SACL;aACI;YACD,IAAI,CAAC,QAAQ,GAAG,YAAY;gBACxB,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;oBACzC,qBAAqB,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC5C,qBAAqB,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC9C,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;wBAC5B,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;qBACrB;oBACD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;wBACnB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;wBAC/B,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;qBAC3C;iBACJ,CAAC,CAAC;aACN,CAAC;SACL;KACJ;;;;;;;;;IASD,WAAW,CAAC,SAAS,CAAC,GAAG;;;;;IAKzB,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KAC/C,CAAC;;;;;;;;;IASF,WAAW,CAAC,SAAS,CAAC,GAAG;;;;;IAKzB,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,qBAAqB,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACnE,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACzD,CAAC;;;;;;;;IAQF,WAAW,CAAC,SAAS,CAAC,IAAI;;;;IAI1B,YAAY;QACR,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;KACpD,CAAC;;;;;;;;;IASF,WAAW,CAAC,SAAS,CAAC,MAAM;;;;;IAK5B,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC;KACvD,CAAC;;;;;;IAMF,WAAW,CAAC,SAAS,CAAC,MAAM;;;;;IAK5B,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;KAC5D,CAAC;;;;;;IAMF,WAAW,CAAC,SAAS,CAAC,GAAG;;;;;IAKzB,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;KAC5D,CAAC;;;;;;IAMF,WAAW,CAAC,SAAS,CAAC,MAAM;;;;;IAK5B,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;KAC5D,CAAC;;;;;;IAMF,WAAW,CAAC,SAAS,CAAC,sBAAsB;;;;;IAK5C,UAAU,IAAI,EAAE,MAAM,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC1C;KACJ,CAAC;;;;IAIF,WAAW,CAAC,SAAS,CAAC,IAAI;;;IAG1B,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,IAAI,CAAC,QAAQ,YAAY,WAAW,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAChC;iBACI;gBACD,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;gBACjF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aAC1B;SACJ;KACJ,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,QAAQ;;;;IAI9B,UAAU,KAAK,EAAE;QACb,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;YACpD,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,qBAAqB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;YACpE,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,qBAAqB,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;SACvF,CAAC,CAAC;KACN,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,KAAK;;;;IAI3B,UAAU,MAAM,EAAE;QACd,qBAAqB,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;QAC/C,KAAK,CAAC,QAAQ;YACV,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,YAAY,WAAW,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrF,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5D,OAAO,KAAK,CAAC;KAChB,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,WAAW;;;;IAIjC,UAAU,MAAM,EAAE;QACd,qBAAqB,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QACrD,QAAQ,MAAM,CAAC,EAAE;YACb,KAAK,GAAG,CAAC;YACT,KAAK,GAAG;gBACJ,qBAAqB,KAAK,sBAAsB,MAAM,CAAC,KAAK,EAAE,CAAC;gBAC/D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC3B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;iBACnB;gBACD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,OAAO;iBACV;gBACD,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC9C,qBAAqB,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,KAAK,EAAE,CAAC;gBAC1F,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC5B,MAAM;YACV,KAAK,GAAG;gBACJ,qBAAqB,UAAU,qBAAqB,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClE,IAAI,CAAC,UAAU,EAAE;oBACb,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACzB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;iBACpC;qBACI;oBACD,qBAAqB,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBACtD,IAAI,CAAC,QAAQ,EAAE;wBACX,OAAO;qBACV;oBACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAC1F,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;wBACvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBACzB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpC;yBACI;wBACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;qBACnC;iBACJ;gBACD,MAAM;SACb;KACJ,CAAC;;;;;;;;;IASF,WAAW,CAAC,SAAS,CAAC,OAAO;;;;;IAK7B,UAAU,EAAE,EAAE;QACV,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;aAClC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,EAAE,oBAAoB,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;KACjJ,CAAC;IACF,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC;;ACrUJ;;;;;;;;;;;;;;;;;;;;AAoBA,AAAwC;AACxC,AAUA;;;;;;AAMA,IAMA,oBAAoB,kBAAkB,YAAY;IAC9C,SAAS,oBAAoB,GAAG;KAC/B;;;;;IAKD,oBAAoB,CAAC,SAAS,CAAC,SAAS;;;;IAIxC,UAAU,CAAC,EAAE,EAAE,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;;;;IAK7C,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;;IAI1C,UAAU,CAAC,EAAE,EAAE,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;;;;IAK7C,oBAAoB,CAAC,SAAS,CAAC,SAAS;;;;IAIxC,UAAU,CAAC,EAAE,EAAE,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;;;;IAK/C,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;;IAI1C,UAAU,CAAC,EAAE,EAAE,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/C,OAAO,oBAAoB,CAAC;CAC/B,EAAE,CAAC,CAAC;AACL,AAOA;;;;;AAKA,SAAS,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE;IACnC,qBAAqBA,MAAG,GAAG,IAAI,GAAG,EAAE,CAAC;IACrC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,qBAAqB,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnD,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;YAC5B,qBAAqB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,EAAE,GAAG,KAAK,IAAI,CAAC,CAAC;gBAChB,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;gBAC5B,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAClH,qBAAqB,IAAI,GAAGA,MAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACfA,MAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SACtB,CAAC,CAAC;KACN;IACD,OAAOA,MAAG,CAAC;CACd;;;;;AAKD,SAAS,gBAAgB,CAAC,CAAC,EAAE;IACzB,OAAO,kBAAkB,CAAC,CAAC,CAAC;SACvB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;CAC9B;AACD,AAYA;;;;;;;;AAQA,IAQA,UAAU,kBAAkB,YAAY;IACpC,SAAS,UAAU,CAAC,OAAO,EAAE;QACzB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,qBAAqB,EAAE,CAAC,CAAC,EAAE;QAC5D,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,oBAAoB,EAAE,CAAC;QAC7D,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE;YACtB,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE;gBACtB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;aACrE;YACD,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC5D;aACI,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;gBACnD,qBAAqB,KAAK,GAAG,mBAAmB,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;gBAC1E,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;aAClE,CAAC,CAAC;SACN;aACI;YACD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;SACnB;KACJ;;;;;;;;;IASD,UAAU,CAAC,SAAS,CAAC,GAAG;;;;;IAKxB,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,wBAAwB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;KACnD,CAAC;;;;;;;;;IASF,UAAU,CAAC,SAAS,CAAC,GAAG;;;;;IAKxB,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,qBAAqB,GAAG,oBAAoB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACpE,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KAChC,CAAC;;;;;;;;;IASF,UAAU,CAAC,SAAS,CAAC,MAAM;;;;;IAK3B,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,wBAAwB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;KAC3D,CAAC;;;;;;;;IAQF,UAAU,CAAC,SAAS,CAAC,IAAI;;;;IAIzB,YAAY;QACR,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,KAAK,CAAC,IAAI,kBAAkB,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;KAC3D,CAAC;;;;;;;;;;IAUF,UAAU,CAAC,SAAS,CAAC,MAAM;;;;;;IAM3B,UAAU,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;;;;;;IAUxF,UAAU,CAAC,SAAS,CAAC,GAAG;;;;;;IAMxB,UAAU,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;;;;;;;;;;IAcxF,UAAU,CAAC,SAAS,CAAC,MAAM;;;;;;;;IAQ3B,UAAU,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;;;;;;IAUxF,UAAU,CAAC,SAAS,CAAC,QAAQ;;;;;IAK7B,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,IAAI,EAAE;aACb,GAAG,CAAC,UAAU,GAAG,EAAE;YACpB,qBAAqB,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACzD,wBAAwB,mBAAmB,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACjK,CAAC;aACG,IAAI,CAAC,GAAG,CAAC,CAAC;KAClB,CAAC;;;;;IAKF,UAAU,CAAC,SAAS,CAAC,KAAK;;;;IAI1B,UAAU,MAAM,EAAE;QACd,qBAAqB,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;QACzC,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACtD,OAAO,KAAK,CAAC;KAChB,CAAC;;;;IAIF,UAAU,CAAC,SAAS,CAAC,IAAI;;;IAGzB,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;YACnB,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;SACxB;QACD,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YACzB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,sCAAsC,mBAAmB,EAAE,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC9K,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,MAAM,EAAE;gBACvC,QAAQ,MAAM,CAAC,EAAE;oBACb,KAAK,GAAG,CAAC;oBACT,KAAK,GAAG;wBACJ,qBAAqB,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,oBAAoB,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,KAAK,EAAE,CAAC;wBACrH,IAAI,CAAC,IAAI,oBAAoB,MAAM,CAAC,KAAK,GAAG,CAAC;wBAC7C,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;wBACtC,MAAM;oBACV,KAAK,GAAG;wBACJ,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;4BAC5B,qBAAqB,MAAM,oBAAoB,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;4BACrF,qBAAqB,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BACxD,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gCACZ,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;6BACzB;4BACD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;iDACF,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;6BAC5D;iCACI;iDACgB,EAAE,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;6BACvD;yBACJ;6BACI;6CACgB,EAAE,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BACpD,MAAM;yBACT;iBACR;aACJ,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB;KACJ,CAAC;IACF,OAAO,UAAU,CAAC;CACrB,EAAE,CAAC;;ACjYJ;;;;;;;;;;;AAWA,AAqBA;;;;;AAKA,SAAS,aAAa,CAAC,MAAM,EAAE;IAC3B,QAAQ,MAAM;QACV,KAAK,QAAQ,CAAC;QACd,KAAK,KAAK,CAAC;QACX,KAAK,MAAM,CAAC;QACZ,KAAK,SAAS,CAAC;QACf,KAAK,OAAO;YACR,OAAO,KAAK,CAAC;QACjB;YACI,OAAO,IAAI,CAAC;KACnB;CACJ;;;;;;;;AAQD,SAAS,aAAa,CAAC,KAAK,EAAE;IAC1B,OAAO,OAAO,WAAW,KAAK,WAAW,IAAI,KAAK,YAAY,WAAW,CAAC;CAC7E;;;;;;;;AAQD,SAAS,MAAM,CAAC,KAAK,EAAE;IACnB,OAAO,OAAO,IAAI,KAAK,WAAW,IAAI,KAAK,YAAY,IAAI,CAAC;CAC/D;;;;;;;;AAQD,SAAS,UAAU,CAAC,KAAK,EAAE;IACvB,OAAO,OAAO,QAAQ,KAAK,WAAW,IAAI,KAAK,YAAY,QAAQ,CAAC;CACvE;;;;;;;;;;;;AAYD,IAWA,WAAW,kBAAkB,YAAY;IACrC,SAAS,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;QAC7C,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;;;;;;;;QAQf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;;;;;;;QAOjB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;;;;QAI5B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;;;;;;;QAO7B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;;;QAGnC,qBAAqB,OAAO,CAAC;;;QAG7B,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;;YAExC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS,sBAAsB,KAAK,IAAI,IAAI,CAAC;YACpE,OAAO,GAAG,MAAM,CAAC;SACpB;aACI;;YAED,OAAO,qBAAqB,KAAK,CAAC,CAAC;SACtC;;QAED,IAAI,OAAO,EAAE;;YAET,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC;YAC/C,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;;YAEjD,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE;gBACxB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;aAC5C;;YAED,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;gBACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aAClC;YACD,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;gBAClB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;aAChC;SACJ;;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;SACpC;;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;SAC5B;aACI;;YAED,qBAAqB,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;;gBAErB,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;aAC5B;iBACI;;gBAED,qBAAqB,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;gBAQ7C,qBAAqB,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;gBAClF,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC;aAC3C;SACJ;KACJ;;;;;;;;;;IAUD,WAAW,CAAC,SAAS,CAAC,aAAa;;;;;IAKnC,YAAY;;QAER,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;;;QAGD,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;YACtE,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC/B,OAAO,IAAI,CAAC,IAAI,CAAC;SACpB;;QAED,IAAI,IAAI,CAAC,IAAI,YAAY,UAAU,EAAE;YACjC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;SAC/B;;QAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS;YAC/D,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpC;;QAED,OAAO,mBAAmB,IAAI,CAAC,IAAI,GAAG,QAAQ,EAAE,CAAC;KACpD,CAAC;;;;;;;;;;;;;;IAcF,WAAW,CAAC,SAAS,CAAC,uBAAuB;;;;;;;IAO7C,YAAY;;QAER,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;;QAED,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;;;QAGD,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACnB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;SACjC;;QAED,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC;SACf;;;QAGD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC/B,OAAO,YAAY,CAAC;SACvB;;QAED,IAAI,IAAI,CAAC,IAAI,YAAY,UAAU,EAAE;YACjC,OAAO,iDAAiD,CAAC;SAC5D;;QAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;YAC9D,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,kBAAkB,CAAC;SAC7B;;QAED,OAAO,IAAI,CAAC;KACf,CAAC;;;;;IAKF,WAAW,CAAC,SAAS,CAAC,KAAK;;;;IAI3B,UAAU,MAAM,EAAE;QACd,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;;;QAGvC,qBAAqB,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QAC3D,qBAAqB,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC;QAClD,qBAAqB,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;;;;;QAK7E,qBAAqB,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;;;QAGlF,qBAAqB,eAAe,GAAG,CAAC,MAAM,CAAC,eAAe,KAAK,SAAS,IAAI,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC9H,qBAAqB,cAAc,GAAG,CAAC,MAAM,CAAC,cAAc,KAAK,SAAS,IAAI,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;;;QAG1H,qBAAqB,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QAC9D,qBAAqB,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;;QAE3D,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE;;YAEjC,OAAO;gBACH,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;qBACzB,MAAM,CAAC,UAAU,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,mBAAmB,EAAE,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;SAClI;;QAED,IAAI,MAAM,CAAC,SAAS,EAAE;;YAElB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;iBACjC,MAAM,CAAC,UAAU,MAAM,EAAE,KAAK,EAAE,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,mBAAmB,EAAE,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;SAC7H;;QAED,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;YACtC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe;SACjI,CAAC,CAAC;KACN,CAAC;IACF,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC;;AC5UJ;;;;;;;;;;;AAWA,AAEA;AACA,IAAI,aAAa,GAAG;;;;IAIhB,IAAI,EAAE,CAAC;;;;IAIP,cAAc,EAAE,CAAC;;;;IAIjB,cAAc,EAAE,CAAC;;;;IAIjB,gBAAgB,EAAE,CAAC;;;;IAInB,QAAQ,EAAE,CAAC;;;;IAIX,IAAI,EAAE,CAAC;CACV,CAAC;AACF,AACA,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC3C,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,gBAAgB,CAAC;AAC/D,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,gBAAgB,CAAC;AAC/D,aAAa,CAAC,aAAa,CAAC,gBAAgB,CAAC,GAAG,kBAAkB,CAAC;AACnE,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC;AACnD,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;;;;;;;AAO3C,AAAuC;AACvC,AAkBA;;;;;;AAMA,AAA+C;AAC/C,AAWA;;;;;;AAMA,AAA6C;AAC7C,AAIA;;;;;;;;AAQA,AAAmC;AACnC,AAIA;;;;;;;;;;AAUA,AAAmC;AACnC,AAIA;;;;;;;;;AASA,AAAwC;AACxC,AAMA;;;;;;AAMA,IAMA,gBAAgB,kBAAkB,YAAY;;;;;;;IAO1C,SAAS,gBAAgB,CAAC,IAAI,EAAE,aAAa,EAAE,iBAAiB,EAAE;QAC9D,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,GAAG,GAAG,CAAC,EAAE;QACtD,IAAI,iBAAiB,KAAK,KAAK,CAAC,EAAE,EAAE,iBAAiB,GAAG,IAAI,CAAC,EAAE;;;QAG/D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;QACtE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,iBAAiB,CAAC;QACvD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;;QAE5B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACrD;IACD,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC,CAAC;AACL,AAyCA;;;;;;;;;AASA,IASA,kBAAkB,kBAAkB,UAAU,MAAM,EAAE;IAClDC,SAAiB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;;;;IAI9C,SAAS,kBAAkB,CAAC,IAAI,EAAE;QAC9B,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE;QACnC,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;QAC5C,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,cAAc,CAAC;QAC1C,OAAO,KAAK,CAAC;KAChB;;;;;;;;;;;IAWD,kBAAkB,CAAC,SAAS,CAAC,KAAK;;;;;;IAMlC,UAAU,MAAM,EAAE;QACd,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;;;QAGvC,OAAO,IAAI,kBAAkB,CAAC;YAC1B,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;YACvC,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;YACjE,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU;YAChD,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,SAAS;SAC3C,CAAC,CAAC;KACN,CAAC;IACF,OAAO,kBAAkB,CAAC;CAC7B,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACrB,AAcA;;;;;;;;;;AAUA,IAUA,YAAY,kBAAkB,UAAU,MAAM,EAAE;IAC5CA,SAAiB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;;;;IAIxC,SAAS,YAAY,CAAC,IAAI,EAAE;QACxB,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE;QACnC,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;QAC5C,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,QAAQ,CAAC;QACpC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACxD,OAAO,KAAK,CAAC;KAChB;;;;;IAKD,YAAY,CAAC,SAAS,CAAC,KAAK;;;;IAI5B,UAAU,MAAM,EAAE;QACd,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;QACvC,OAAO,IAAI,YAAY,CAAC;YACpB,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;YAC3D,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;YACvC,MAAM,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;YACnE,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU;YAChD,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,SAAS;SAC3C,CAAC,CAAC;KACN,CAAC;IACF,OAAO,YAAY,CAAC;CACvB,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACrB,AAoBA;;;;;;;;;;;;;AAaA,IAaA,iBAAiB,kBAAkB,UAAU,MAAM,EAAE;IACjDA,SAAiB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IAC7C,SAAS,iBAAiB,CAAC,IAAI,EAAE;QAC7B,IAAI,KAAK;;QAET,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC,IAAI,IAAI,CAAC;QACpD,KAAK,CAAC,IAAI,GAAG,mBAAmB,CAAC;;;;QAIjC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC;;;;QAIjB,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE;YAC3C,KAAK,CAAC,OAAO,GAAG,kCAAkC,IAAI,IAAI,CAAC,GAAG,IAAI,eAAe,CAAC,CAAC;SACtF;aACI;YACD,KAAK,CAAC,OAAO;gBACT,4BAA4B,IAAI,IAAI,CAAC,GAAG,IAAI,eAAe,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;SACjH;QACD,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QACjC,OAAO,KAAK,CAAC;KAChB;IACD,OAAO,iBAAiB,CAAC;CAC5B,CAAC,gBAAgB,CAAC,CAAC;;AC9ZpB;;;;;;;;;;;AAWA,AAUA;;;;;;;;AAQA,SAAS,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE;IAC5B,OAAO;QACH,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,eAAe,EAAE,OAAO,CAAC,eAAe;KAC3C,CAAC;CACL;;;;;;;;;;AAUD,IAAI,UAAU,kBAAkB,YAAY;IACxC,SAAS,UAAU,CAAC,OAAO,EAAE;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmED,UAAU,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmC5B,UAAU,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;QAC3B,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,qBAAqB,GAAG,CAAC;;QAEzB,IAAI,KAAK,YAAY,WAAW,EAAE;;;YAG9B,GAAG,qBAAqB,KAAK,CAAC,CAAC;SAClC;aACI;;;;;YAKD,qBAAqB,OAAO,GAAG,SAAS,CAAC;YACzC,IAAI,OAAO,CAAC,OAAO,YAAY,WAAW,EAAE;gBACxC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aAC7B;iBACI;gBACD,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAC9C;;YAED,qBAAqB,MAAM,GAAG,SAAS,CAAC;YACxC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;gBAClB,IAAI,OAAO,CAAC,MAAM,YAAY,UAAU,EAAE;oBACtC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;iBAC3B;qBACI;oBACD,MAAM,GAAG,IAAI,UAAU,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;iBAC3D;aACJ;;YAED,GAAG,GAAG,IAAI,WAAW,CAAC,KAAK,qBAAqB,GAAG,KAAK,OAAO,CAAC,IAAI,KAAK,SAAS,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,GAAG;gBACvG,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;gBACd,cAAc,EAAE,OAAO,CAAC,cAAc;;gBAEtC,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,MAAM;gBAC5C,eAAe,EAAE,OAAO,CAAC,eAAe;aAC3C,CAAC,CAAC;SACN;;;;;QAKD,qBAAqB,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,GAAG,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;;;QAI7G,IAAI,KAAK,YAAY,WAAW,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;YAC9D,OAAO,OAAO,CAAC;SAClB;;;;QAID,qBAAqB,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,YAAY,CAAC,EAAE,CAAC,CAAC;;QAE7G,QAAQ,OAAO,CAAC,OAAO,IAAI,MAAM;YAC7B,KAAK,MAAM;;;;;;gBAMP,QAAQ,GAAG,CAAC,YAAY;oBACpB,KAAK,aAAa;wBACd,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,EAAE;;4BAEjC,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,YAAY,WAAW,CAAC,EAAE;gCACzD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;6BACtD;4BACD,OAAO,GAAG,CAAC,IAAI,CAAC;yBACnB,CAAC,CAAC;oBACP,KAAK,MAAM;wBACP,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,EAAE;;4BAEjC,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,YAAY,IAAI,CAAC,EAAE;gCAClD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;6BAC9C;4BACD,OAAO,GAAG,CAAC,IAAI,CAAC;yBACnB,CAAC,CAAC;oBACP,KAAK,MAAM;wBACP,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,EAAE;;4BAEjC,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;gCACnD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;6BAChD;4BACD,OAAO,GAAG,CAAC,IAAI,CAAC;yBACnB,CAAC,CAAC;oBACP,KAAK,MAAM,CAAC;oBACZ;;wBAEI,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAClE;YACL,KAAK,UAAU;;gBAEX,OAAO,IAAI,CAAC;YAChB;;gBAEI,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;SACvF;KACJ,CAAC;;;;;;;;;;;;;;IAcF,UAAU,CAAC,SAAS,CAAC,MAAM;;;;;;;;IAQ3B,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,oBAAoB,OAAO,EAAE,CAAC;KAClE,CAAC;;;;;;;;;;;;;;IAcF,UAAU,CAAC,SAAS,CAAC,GAAG;;;;;;;;IAQxB,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,oBAAoB,OAAO,EAAE,CAAC;KAC/D,CAAC;;;;;;;;;;;;;;IAcF,UAAU,CAAC,SAAS,CAAC,IAAI;;;;;;;;IAQzB,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,oBAAoB,OAAO,EAAE,CAAC;KAChE,CAAC;;;;;;;;;;;;;;;;;;;;;IAqBF,UAAU,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;IAY1B,UAAU,GAAG,EAAE,aAAa,EAAE;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE;YAC9B,MAAM,EAAE,IAAI,UAAU,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC;YAChE,OAAO,EAAE,MAAM;YACf,YAAY,EAAE,MAAM;SACvB,CAAC,CAAC;KACN,CAAC;;;;;;;;;;;;;;IAcF,UAAU,CAAC,SAAS,CAAC,OAAO;;;;;;;;IAQ5B,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,oBAAoB,OAAO,EAAE,CAAC;KACnE,CAAC;;;;;;;;;;;;;;;IAeF,UAAU,CAAC,SAAS,CAAC,KAAK;;;;;;;;;IAS1B,UAAU,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE;QAC1B,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;KAC7D,CAAC;;;;;;;;;;;;;;;IAeF,UAAU,CAAC,SAAS,CAAC,IAAI;;;;;;;;;IASzB,UAAU,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE;QAC1B,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;KAC5D,CAAC;;;;;;;;;;;;;;;IAeF,UAAU,CAAC,SAAS,CAAC,GAAG;;;;;;;;;IASxB,UAAU,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE;QAC1B,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;KAC3D,CAAC;IACF,UAAU,CAAC,UAAU,GAAG;QACpB,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,UAAU,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC7C,EAAE,IAAI,EAAE,WAAW,GAAG;KACzB,CAAC,EAAE,CAAC;IACL,OAAO,UAAU,CAAC;CACrB,EAAE,CAAC;;ACreJ;;;;;;;;;;;AAWA,AACA;;;;;;;;;;;;AAYA,AAAqC;AACrC,AAoBA;;;;;AAKA,IAKA,sBAAsB,kBAAkB,YAAY;IAChD,SAAS,sBAAsB,CAAC,IAAI,EAAE,WAAW,EAAE;QAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAClC;;;;;IAKD,sBAAsB,CAAC,SAAS,CAAC,MAAM;;;;IAIvC,UAAU,GAAG,EAAE;QACX,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KACrD,CAAC;IACF,OAAO,sBAAsB,CAAC;CACjC,EAAE,CAAC,CAAC;AACL,AAYA;;;;;;AAMA,AAAO,IAAqB,iBAAiB,GAAG,IAAI,cAAc,CAAC,mBAAmB,CAAC,CAAC;AACxF,IAAI,eAAe,kBAAkB,YAAY;IAC7C,SAAS,eAAe,GAAG;KAC1B;;;;;;IAMD,eAAe,CAAC,SAAS,CAAC,SAAS;;;;;IAKnC,UAAU,GAAG,EAAE,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC3B,CAAC;IACF,eAAe,CAAC,UAAU,GAAG;QACzB,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,eAAe,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC5D,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC;;AClHJ;;;;;;;;;;;AAWA,AAIA;;;;AAIA,IAAqB,aAAa,GAAG,CAAC,CAAC;;;AAGvC,AAAO,IAAqB,qBAAqB,GAAG,gDAAgD,CAAC;;;AAGrG,AAAO,IAAqB,sBAAsB,GAAG,+CAA+C,CAAC;AACrG,AAAO,IAAqB,6BAA6B,GAAG,6CAA6C,CAAC;;;;;;;;;AAS1G,IAQA,oBAAoB,kBAAkB,YAAY;IAC9C,SAAS,oBAAoB,GAAG;KAC/B;IACD,OAAO,oBAAoB,CAAC;CAC/B,EAAE,CAAC,CAAC;AACL,AASA;;;;;;AAMA,IAAI,kBAAkB,kBAAkB,YAAY;IAChD,SAAS,kBAAkB,CAAC,WAAW,EAAE,QAAQ,EAAE;QAC/C,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC5B;;;;;IAKD,kBAAkB,CAAC,SAAS,CAAC,YAAY;;;;IAIzC,YAAY,EAAE,OAAO,oBAAoB,GAAG,aAAa,EAAE,CAAC,EAAE,CAAC;;;;;;;;;IAS/D,kBAAkB,CAAC,SAAS,CAAC,MAAM;;;;;IAKnC,UAAU,GAAG,EAAE;QACX,IAAI,KAAK,GAAG,IAAI,CAAC;;;QAGjB,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SAC3C;aACI,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAClD;;QAED,OAAO,IAAI,UAAU,CAAC,UAAU,QAAQ,EAAE;;;;YAItC,qBAAqB,QAAQ,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YACrD,qBAAqB,GAAG,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,sBAAsB,EAAE,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;;YAEpG,qBAAqB,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACnE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;;;;YAIf,qBAAqB,IAAI,GAAG,IAAI,CAAC;;YAEjC,qBAAqB,QAAQ,GAAG,KAAK,CAAC;;;YAGtC,qBAAqB,SAAS,GAAG,KAAK,CAAC;;;;;;;YAOvC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,UAAU,IAAI,EAAE;;gBAE1C,OAAO,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;;gBAEnC,IAAI,SAAS,EAAE;oBACX,OAAO;iBACV;;gBAED,IAAI,GAAG,IAAI,CAAC;gBACZ,QAAQ,GAAG,IAAI,CAAC;aACnB,CAAC;;;;YAIF,qBAAqB,OAAO,GAAG,YAAY;;gBAEvC,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBACrC;;;gBAGD,OAAO,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aACtC,CAAC;;;;;YAKF,qBAAqB,MAAM,GAAG,UAAU,KAAK,EAAE;;gBAE3C,IAAI,SAAS,EAAE;oBACX,OAAO;iBACV;;gBAED,OAAO,EAAE,CAAC;;gBAEV,IAAI,CAAC,QAAQ,EAAE;;;oBAGX,QAAQ,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC;wBACjC,GAAG,EAAE,GAAG;wBACR,MAAM,EAAE,CAAC;wBACT,UAAU,EAAE,aAAa;wBACzB,KAAK,EAAE,IAAI,KAAK,CAAC,qBAAqB,CAAC;qBAC1C,CAAC,CAAC,CAAC;oBACJ,OAAO;iBACV;;;gBAGD,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC;oBAC3B,IAAI,EAAE,IAAI;oBACV,MAAM,EAAE,GAAG;oBACX,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;iBAC7B,CAAC,CAAC,CAAC;;gBAEJ,QAAQ,CAAC,QAAQ,EAAE,CAAC;aACvB,CAAC;;;;YAIF,qBAAqB,OAAO,GAAG,UAAU,KAAK,EAAE;;gBAE5C,IAAI,SAAS,EAAE;oBACX,OAAO;iBACV;gBACD,OAAO,EAAE,CAAC;;gBAEV,QAAQ,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC;oBACjC,KAAK,EAAE,KAAK;oBACZ,MAAM,EAAE,CAAC;oBACT,UAAU,EAAE,aAAa,EAAE,GAAG,EAAE,GAAG;iBACtC,CAAC,CAAC,CAAC;aACP,CAAC;;;YAGF,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;YAEtC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;;YAE5C,OAAO,YAAY;;gBAEf,SAAS,GAAG,IAAI,CAAC;;gBAEjB,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACzC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;gBAE3C,OAAO,EAAE,CAAC;aACb,CAAC;SACL,CAAC,CAAC;KACN,CAAC;IACF,kBAAkB,CAAC,UAAU,GAAG;QAC5B,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,kBAAkB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACrD,EAAE,IAAI,EAAE,oBAAoB,GAAG;QAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE;KAC1E,CAAC,EAAE,CAAC;IACL,OAAO,kBAAkB,CAAC;CAC7B,EAAE,CAAC,CAAC;AACL,AAcA;;;;;;AAMA,IAAI,gBAAgB,kBAAkB,YAAY;IAC9C,SAAS,gBAAgB,CAAC,KAAK,EAAE;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;;;;;;IAMD,gBAAgB,CAAC,SAAS,CAAC,SAAS;;;;;IAKpC,UAAU,GAAG,EAAE,IAAI,EAAE;QACjB,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,EAAE;YACxB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,mBAAmB,GAAG,EAAE,CAAC;SACpD;;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC3B,CAAC;IACF,gBAAgB,CAAC,UAAU,GAAG;QAC1B,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,gBAAgB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACnD,EAAE,IAAI,EAAE,kBAAkB,GAAG;KAChC,CAAC,EAAE,CAAC;IACL,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC;;ACnRJ;;;;;;;;;;;AAWA,AAIA,IAAqB,WAAW,GAAG,cAAc,CAAC;;;;;;;AAOlD,SAAS,cAAc,CAAC,GAAG,EAAE;IACzB,IAAI,aAAa,IAAI,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE;QACzC,OAAO,GAAG,CAAC,WAAW,CAAC;KAC1B;IACD,IAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,EAAE;QACtD,OAAO,GAAG,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;KACjD;IACD,OAAO,IAAI,CAAC;CACf;;;;;;;AAOD,IAMA,UAAU,kBAAkB,YAAY;IACpC,SAAS,UAAU,GAAG;KACrB;IACD,OAAO,UAAU,CAAC;CACrB,EAAE,CAAC,CAAC;AACL,AAcA;;;;;AAKA,IAAI,UAAU,kBAAkB,YAAY;IACxC,SAAS,UAAU,GAAG;KACrB;;;;IAID,UAAU,CAAC,SAAS,CAAC,KAAK;;;IAG1B,YAAY,EAAE,0BAA0B,IAAI,cAAc,EAAE,GAAG,EAAE,CAAC;IAClE,UAAU,CAAC,UAAU,GAAG;QACpB,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,UAAU,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACvD,OAAO,UAAU,CAAC;CACrB,EAAE,CAAC,CAAC;AACL,AAyBA;;;;;;AAMA,IAAI,cAAc,kBAAkB,YAAY;IAC5C,SAAS,cAAc,CAAC,UAAU,EAAE;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAChC;;;;;;;;;IASD,cAAc,CAAC,SAAS,CAAC,MAAM;;;;;IAK/B,UAAU,GAAG,EAAE;QACX,IAAI,KAAK,GAAG,IAAI,CAAC;;;QAGjB,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;SAChG;;QAED,OAAO,IAAI,UAAU,CAAC,UAAU,QAAQ,EAAE;;YAEtC,qBAAqB,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACpD,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;YACxC,IAAI,CAAC,CAAC,GAAG,CAAC,eAAe,EAAE;gBACvB,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;aAC9B;;YAED,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;YAEtG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC5B,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC;aACvE;;YAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBAClC,qBAAqB,YAAY,GAAG,GAAG,CAAC,uBAAuB,EAAE,CAAC;;gBAElE,IAAI,YAAY,KAAK,IAAI,EAAE;oBACvB,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;iBACtD;aACJ;;YAED,IAAI,GAAG,CAAC,YAAY,EAAE;gBAClB,qBAAqB,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;;;;;;gBAMnE,GAAG,CAAC,YAAY,sBAAsB,CAAC,YAAY,KAAK,MAAM,IAAI,YAAY,GAAG,MAAM,EAAE,CAAC;aAC7F;;YAED,qBAAqB,OAAO,GAAG,GAAG,CAAC,aAAa,EAAE,CAAC;;;;;;;YAOnD,qBAAqB,cAAc,GAAG,IAAI,CAAC;;;YAG3C,qBAAqB,cAAc,GAAG,YAAY;gBAC9C,IAAI,cAAc,KAAK,IAAI,EAAE;oBACzB,OAAO,cAAc,CAAC;iBACzB;;gBAED,qBAAqB,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;gBACrE,qBAAqB,UAAU,GAAG,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC;;gBAEzD,qBAAqB,OAAO,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,CAAC;;;gBAG5E,qBAAqB,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC;;gBAE1D,cAAc,GAAG,IAAI,kBAAkB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBAChH,OAAO,cAAc,CAAC;aACzB,CAAC;;;;YAIF,qBAAqB,MAAM,GAAG,YAAY;;gBAEtC,IAAI,EAAE,GAAG,cAAc,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;;gBAE9G,qBAAqB,IAAI,GAAG,IAAI,CAAC;gBACjC,IAAI,MAAM,KAAK,GAAG,EAAE;;oBAEhB,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,QAAQ,KAAK,WAAW,IAAI,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,CAAC;iBAClF;;gBAED,IAAI,MAAM,KAAK,CAAC,EAAE;oBACd,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;iBAC7B;;;;;gBAKD,qBAAqB,EAAE,GAAG,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC;;;gBAGxD,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;oBAEzD,qBAAqB,YAAY,GAAG,IAAI,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;oBACrC,IAAI;;wBAEA,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;qBAChD;oBACD,wBAAwB,KAAK,EAAE;;;;wBAI3B,IAAI,GAAG,YAAY,CAAC;;;wBAGpB,IAAI,EAAE,EAAE;;4BAEJ,EAAE,GAAG,KAAK,CAAC;;4BAEX,IAAI,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;yBAC1D;qBACJ;iBACJ;gBACD,IAAI,EAAE,EAAE;;oBAEJ,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC;wBAC3B,IAAI,EAAE,IAAI;wBACV,OAAO,EAAE,OAAO;wBAChB,MAAM,EAAE,MAAM;wBACd,UAAU,EAAE,UAAU;wBACtB,GAAG,EAAE,GAAG,IAAI,SAAS;qBACxB,CAAC,CAAC,CAAC;;;oBAGJ,QAAQ,CAAC,QAAQ,EAAE,CAAC;iBACvB;qBACI;;oBAED,QAAQ,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC;;wBAEjC,KAAK,EAAE,IAAI;wBACX,OAAO,EAAE,OAAO;wBAChB,MAAM,EAAE,MAAM;wBACd,UAAU,EAAE,UAAU;wBACtB,GAAG,EAAE,GAAG,IAAI,SAAS;qBACxB,CAAC,CAAC,CAAC;iBACP;aACJ,CAAC;;;;YAIF,qBAAqB,OAAO,GAAG,UAAU,KAAK,EAAE;gBAC5C,qBAAqB,GAAG,GAAG,IAAI,iBAAiB,CAAC;oBAC7C,KAAK,EAAE,KAAK;oBACZ,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC;oBACvB,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,eAAe;iBAChD,CAAC,CAAC;gBACH,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACvB,CAAC;;;;;YAKF,qBAAqB,WAAW,GAAG,KAAK,CAAC;;;YAGzC,qBAAqB,cAAc,GAAG,UAAU,KAAK,EAAE;;gBAEnD,IAAI,CAAC,WAAW,EAAE;oBACd,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;oBAChC,WAAW,GAAG,IAAI,CAAC;iBACtB;;;gBAGD,qBAAqB,aAAa,GAAG;oBACjC,IAAI,EAAE,aAAa,CAAC,gBAAgB;oBACpC,MAAM,EAAE,KAAK,CAAC,MAAM;iBACvB,CAAC;;gBAEF,IAAI,KAAK,CAAC,gBAAgB,EAAE;oBACxB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;iBACrC;;;;gBAID,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE;oBACnD,aAAa,CAAC,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC;iBAChD;;gBAED,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAChC,CAAC;;;YAGF,qBAAqB,YAAY,GAAG,UAAU,KAAK,EAAE;;;gBAGjD,qBAAqB,QAAQ,GAAG;oBAC5B,IAAI,EAAE,aAAa,CAAC,cAAc;oBAClC,MAAM,EAAE,KAAK,CAAC,MAAM;iBACvB,CAAC;;;gBAGF,IAAI,KAAK,CAAC,gBAAgB,EAAE;oBACxB,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;iBAChC;;gBAED,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC3B,CAAC;;YAEF,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACrC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;YAEvC,IAAI,GAAG,CAAC,cAAc,EAAE;;gBAEpB,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;;gBAEjD,IAAI,OAAO,KAAK,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;oBAChC,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;iBACzD;aACJ;;YAED,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;;;YAG5C,OAAO,YAAY;;gBAEf,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC1C,GAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACxC,IAAI,GAAG,CAAC,cAAc,EAAE;oBACpB,GAAG,CAAC,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;oBACpD,IAAI,OAAO,KAAK,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;wBAChC,GAAG,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;qBAC5D;iBACJ;;gBAED,GAAG,CAAC,KAAK,EAAE,CAAC;aACf,CAAC;SACL,CAAC,CAAC;KACN,CAAC;IACF,cAAc,CAAC,UAAU,GAAG;QACxB,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,cAAc,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACjD,EAAE,IAAI,EAAE,UAAU,GAAG;KACxB,CAAC,EAAE,CAAC;IACL,OAAO,cAAc,CAAC;CACzB,EAAE,CAAC;;ACjXJ;;;;;;;;;;;AAWA,AAEO,IAAqB,gBAAgB,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,CAAC;AACtF,AAAO,IAAqB,gBAAgB,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,CAAC;;;;;;;AAOtF,IAMA,sBAAsB,kBAAkB,YAAY;IAChD,SAAS,sBAAsB,GAAG;KACjC;IACD,OAAO,sBAAsB,CAAC;CACjC,EAAE,CAAC,CAAC;AACL,AAiBA;;;AAGA,IAAI,uBAAuB,kBAAkB,YAAY;IACrD,SAAS,uBAAuB,CAAC,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;;;QAItB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;KACvB;;;;IAID,uBAAuB,CAAC,SAAS,CAAC,QAAQ;;;IAG1C,YAAY;QACR,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;QACD,qBAAqB,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC;QAC1D,IAAI,YAAY,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACxC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,GAAGC,iBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACjE,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC;SACxC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB,CAAC;IACF,uBAAuB,CAAC,UAAU,GAAG;QACjC,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,uBAAuB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC1D,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE;QACvE,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE;QAC1E,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,gBAAgB,EAAE,EAAE,EAAE,EAAE;KAClF,CAAC,EAAE,CAAC;IACL,OAAO,uBAAuB,CAAC;CAClC,EAAE,CAAC,CAAC;AACL,AAyBA;;;AAGA,IAAI,mBAAmB,kBAAkB,YAAY;IACjD,SAAS,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAE;QACnD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAChC;;;;;;IAMD,mBAAmB,CAAC,SAAS,CAAC,SAAS;;;;;IAKvC,UAAU,GAAG,EAAE,IAAI,EAAE;QACjB,qBAAqB,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;;;;;QAKnD,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC;YAC5E,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAC3B;QACD,qBAAqB,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;;QAE1D,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACrD,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC3B,CAAC;IACF,mBAAmB,CAAC,UAAU,GAAG;QAC7B,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,mBAAmB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QACtD,EAAE,IAAI,EAAE,sBAAsB,GAAG;QACjC,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,gBAAgB,EAAE,EAAE,EAAE,EAAE;KAClF,CAAC,EAAE,CAAC;IACL,OAAO,mBAAmB,CAAC;CAC9B,EAAE,CAAC;;AClKJ;;;;;;;;;;;AAWA,AAOA;;;;;;;;AAQA,IAAI,uBAAuB,kBAAkB,YAAY;IACrD,SAAS,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KACrB;;;;;IAKD,uBAAuB,CAAC,SAAS,CAAC,MAAM;;;;IAIxC,UAAU,GAAG,EAAE;QACX,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;YACrB,qBAAqB,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;YAC7E,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,UAAU,IAAI,EAAE,WAAW,EAAE,EAAE,OAAO,IAAI,sBAAsB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/I;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACjC,CAAC;IACF,uBAAuB,CAAC,UAAU,GAAG;QACjC,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,uBAAuB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO;QAC1D,EAAE,IAAI,EAAE,WAAW,GAAG;QACtB,EAAE,IAAI,EAAE,QAAQ,GAAG;KACtB,CAAC,EAAE,CAAC;IACL,OAAO,uBAAuB,CAAC;CAClC,EAAE,CAAC,CAAC;AACL,AAgBA;;;;;;;;;;;AAWA,AAAO,SAAS,mBAAmB,CAAC,OAAO,EAAE,YAAY,EAAE;IACvD,IAAI,YAAY,KAAK,KAAK,CAAC,EAAE,EAAE,YAAY,GAAG,EAAE,CAAC,EAAE;IACnD,IAAI,CAAC,YAAY,EAAE;QACf,OAAO,OAAO,CAAC;KAClB;IACD,OAAO,YAAY,CAAC,WAAW,CAAC,UAAU,IAAI,EAAE,WAAW,EAAE,EAAE,OAAO,IAAI,sBAAsB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;CACpI;;;;;;;;;;AAUD,AAAO,SAAS,oBAAoB,GAAG;IACnC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC5B,OAAO,MAAM,CAAC;KACjB;IACD,OAAO,EAAE,CAAC;CACb;;;;;;;;;;;;;AAaD,IAAI,oBAAoB,kBAAkB,YAAY;IAClD,SAAS,oBAAoB,GAAG;KAC/B;;;;;;;;IAQD,oBAAoB,CAAC,OAAO;;;;IAI5B,YAAY;QACR,OAAO;YACH,QAAQ,EAAE,oBAAoB;YAC9B,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,eAAe,EAAE;aAC9D;SACJ,CAAC;KACL,CAAC;;;;;;;;;;;IAWF,oBAAoB,CAAC,WAAW;;;;;;IAMhC,UAAU,OAAO,EAAE;QACf,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,OAAO;YACH,QAAQ,EAAE,oBAAoB;YAC9B,SAAS,EAAE;gBACP,OAAO,CAAC,UAAU,GAAG,EAAE,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,GAAG,EAAE;gBACrF,OAAO,CAAC,UAAU,GAAG,EAAE,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,GAAG,EAAE;aACxF;SACJ,CAAC;KACL,CAAC;IACF,oBAAoB,CAAC,UAAU,GAAG;QAC9B,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBACb,SAAS,EAAE;wBACP,mBAAmB;wBACnB,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE;wBAC7E,EAAE,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,uBAAuB,EAAE;wBACtE,EAAE,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,EAAE;wBACrD,EAAE,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE;qBAC1D;iBACJ,EAAE,EAAE;KAChB,CAAC;;IAEF,oBAAoB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACjE,OAAO,oBAAoB,CAAC;CAC/B,EAAE,CAAC,CAAC;AACL,AAUA;;;;;;;;AAQA,IAAI,gBAAgB,kBAAkB,YAAY;IAC9C,SAAS,gBAAgB,GAAG;KAC3B;IACD,gBAAgB,CAAC,UAAU,GAAG;QAC1B,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBACb,OAAO,EAAE;wBACL,oBAAoB,CAAC,WAAW,CAAC;4BAC7B,UAAU,EAAE,YAAY;4BACxB,UAAU,EAAE,cAAc;yBAC7B,CAAC;qBACL;oBACD,SAAS,EAAE;wBACP,UAAU;wBACV,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,uBAAuB,EAAE;wBAC3D,cAAc;wBACd,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE;wBACrD,UAAU;wBACV,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE;qBACnD;iBACJ,EAAE,EAAE;KAChB,CAAC;;IAEF,gBAAgB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC7D,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC,CAAC;AACL,AAUA;;;;;;;;AAQA,IAAI,qBAAqB,kBAAkB,YAAY;IACnD,SAAS,qBAAqB,GAAG;KAChC;IACD,qBAAqB,CAAC,UAAU,GAAG;QAC/B,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBACb,SAAS,EAAE;wBACP,kBAAkB;wBAClB,EAAE,OAAO,EAAE,oBAAoB,EAAE,UAAU,EAAE,oBAAoB,EAAE;wBACnE,EAAE,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE;qBAC1E;iBACJ,EAAE,EAAE;KAChB,CAAC;;IAEF,qBAAqB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAClE,OAAO,qBAAqB,CAAC;CAChC,EAAE,CAAC;;ACjQJ;;;;;;;;;;GAUG;;ACVH;;;;;;GAMG;;;;"}