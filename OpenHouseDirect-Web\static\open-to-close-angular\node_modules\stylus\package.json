{"_from": "stylus@^0.54.5", "_id": "stylus@0.54.8", "_inBundle": false, "_integrity": "sha512-vr54Or4BZ7pJafo2mpf0ZcwA74rpuYCZbxrHBsH8kbcXOwSfvBFwsRfpGO5OD5fhG5HDCFW737PKaawI7OqEAg==", "_location": "/stylus", "_phantomChildren": {"ms": "2.0.0"}, "_requested": {"type": "range", "registry": true, "raw": "stylus@^0.54.5", "name": "stylus", "escapedName": "stylus", "rawSpec": "^0.54.5", "saveSpec": null, "fetchSpec": "^0.54.5"}, "_requiredBy": ["/@angular/cli"], "_resolved": "https://registry.npmjs.org/stylus/-/stylus-0.54.8.tgz", "_shasum": "3da3e65966bc567a7b044bfe0eece653e099d147", "_spec": "stylus@^0.54.5", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\@angular\\cli", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"stylus": "bin/stylus"}, "browserify": "./lib/browserify.js", "bugs": {"url": "https://github.com/stylus/stylus/issues"}, "bundleDependencies": false, "dependencies": {"css-parse": "~2.0.0", "debug": "~3.1.0", "glob": "^7.1.6", "mkdirp": "~1.0.4", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "semver": "^6.3.0", "source-map": "^0.7.3"}, "deprecated": false, "description": "Robust, expressive, and feature-rich CSS superset", "devDependencies": {"jscoverage": "~0.6.0", "mocha": "^8.0.1", "should": "^13.2.3"}, "directories": {"doc": "docs", "example": "examples", "test": "test"}, "engines": {"node": "*"}, "homepage": "https://github.com/stylus/stylus", "keywords": ["css", "parser", "style", "stylesheets", "jade", "language"], "license": "MIT", "main": "./index.js", "name": "stylus", "repository": {"type": "git", "url": "git://github.com/stylus/stylus.git"}, "scripts": {"prepublish": "npm prune", "test": "mocha test/ test/middleware/ --require should --bail --check-leaks --reporter dot", "test-cov": "mocha test/ test/middleware/ --require should --bail --reporter html-cov > coverage.html"}, "version": "0.54.8"}