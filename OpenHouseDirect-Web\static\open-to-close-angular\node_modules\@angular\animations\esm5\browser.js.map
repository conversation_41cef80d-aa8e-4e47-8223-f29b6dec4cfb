{"version": 3, "file": "browser.js", "sources": ["../../../packages/animations/esm5/browser/src/render/shared.js", "../../../packages/animations/esm5/browser/src/render/animation_driver.js", "../../../packages/animations/esm5/browser/src/util.js", "../../../packages/animations/esm5/browser/src/dsl/animation_transition_expr.js", "../../../packages/animations/esm5/browser/src/dsl/animation_ast_builder.js", "../../../packages/animations/esm5/browser/src/dsl/animation_timeline_instruction.js", "../../../packages/animations/esm5/browser/src/dsl/element_instruction_map.js", "../../../packages/animations/esm5/browser/src/dsl/animation_timeline_builder.js", "../../../packages/animations/esm5/browser/src/dsl/animation.js", "../../../packages/animations/esm5/browser/src/dsl/style_normalization/animation_style_normalizer.js", "../../../packages/animations/esm5/browser/src/dsl/style_normalization/web_animations_style_normalizer.js", "../../../packages/animations/esm5/browser/src/dsl/animation_transition_instruction.js", "../../../packages/animations/esm5/browser/src/dsl/animation_transition_factory.js", "../../../packages/animations/esm5/browser/src/dsl/animation_trigger.js", "../../../packages/animations/esm5/browser/src/render/timeline_animation_engine.js", "../../../packages/animations/esm5/browser/src/render/transition_animation_engine.js", "../../../packages/animations/esm5/browser/src/render/animation_engine_next.js", "../../../packages/animations/esm5/browser/src/render/web_animations/web_animations_player.js", "../../../packages/animations/esm5/browser/src/render/web_animations/web_animations_driver.js", "../../../packages/animations/esm5/browser/src/private_export.js", "../../../packages/animations/esm5/browser/src/browser.js", "../../../packages/animations/esm5/browser/public_api.js", "../../../packages/animations/esm5/browser/browser.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\n/**\n * @param {?} players\n * @return {?}\n */\nexport function optimizeGroupPlayer(players) {\n    switch (players.length) {\n        case 0:\n            return new NoopAnimationPlayer();\n        case 1:\n            return players[0];\n        default:\n            return new ɵAnimationGroupPlayer(players);\n    }\n}\n/**\n * @param {?} driver\n * @param {?} normalizer\n * @param {?} element\n * @param {?} keyframes\n * @param {?=} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nexport function normalizeKeyframes(driver, normalizer, element, keyframes, preStyles, postStyles) {\n    if (preStyles === void 0) { preStyles = {}; }\n    if (postStyles === void 0) { postStyles = {}; }\n    var /** @type {?} */ errors = [];\n    var /** @type {?} */ normalizedKeyframes = [];\n    var /** @type {?} */ previousOffset = -1;\n    var /** @type {?} */ previousKeyframe = null;\n    keyframes.forEach(function (kf) {\n        var /** @type {?} */ offset = /** @type {?} */ (kf['offset']);\n        var /** @type {?} */ isSameOffset = offset == previousOffset;\n        var /** @type {?} */ normalizedKeyframe = (isSameOffset && previousKeyframe) || {};\n        Object.keys(kf).forEach(function (prop) {\n            var /** @type {?} */ normalizedProp = prop;\n            var /** @type {?} */ normalizedValue = kf[prop];\n            if (prop !== 'offset') {\n                normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n                switch (normalizedValue) {\n                    case PRE_STYLE:\n                        normalizedValue = preStyles[prop];\n                        break;\n                    case AUTO_STYLE:\n                        normalizedValue = postStyles[prop];\n                        break;\n                    default:\n                        normalizedValue =\n                            normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n                        break;\n                }\n            }\n            normalizedKeyframe[normalizedProp] = normalizedValue;\n        });\n        if (!isSameOffset) {\n            normalizedKeyframes.push(normalizedKeyframe);\n        }\n        previousKeyframe = normalizedKeyframe;\n        previousOffset = offset;\n    });\n    if (errors.length) {\n        var /** @type {?} */ LINE_START = '\\n - ';\n        throw new Error(\"Unable to animate due to the following errors:\" + LINE_START + errors.join(LINE_START));\n    }\n    return normalizedKeyframes;\n}\n/**\n * @param {?} player\n * @param {?} eventName\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nexport function listenOnPlayer(player, eventName, event, callback) {\n    switch (eventName) {\n        case 'start':\n            player.onStart(function () { return callback(event && copyAnimationEvent(event, 'start', player.totalTime)); });\n            break;\n        case 'done':\n            player.onDone(function () { return callback(event && copyAnimationEvent(event, 'done', player.totalTime)); });\n            break;\n        case 'destroy':\n            player.onDestroy(function () { return callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)); });\n            break;\n    }\n}\n/**\n * @param {?} e\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function copyAnimationEvent(e, phaseName, totalTime) {\n    var /** @type {?} */ event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime);\n    var /** @type {?} */ data = (/** @type {?} */ (e))['_data'];\n    if (data != null) {\n        (/** @type {?} */ (event))['_data'] = data;\n    }\n    return event;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function makeAnimationEvent(element, triggerName, fromState, toState, phaseName, totalTime) {\n    if (phaseName === void 0) { phaseName = ''; }\n    if (totalTime === void 0) { totalTime = 0; }\n    return { element: element, triggerName: triggerName, fromState: fromState, toState: toState, phaseName: phaseName, totalTime: totalTime };\n}\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} defaultValue\n * @return {?}\n */\nexport function getOrSetAsInMap(map, key, defaultValue) {\n    var /** @type {?} */ value;\n    if (map instanceof Map) {\n        value = map.get(key);\n        if (!value) {\n            map.set(key, value = defaultValue);\n        }\n    }\n    else {\n        value = map[key];\n        if (!value) {\n            value = map[key] = defaultValue;\n        }\n    }\n    return value;\n}\n/**\n * @param {?} command\n * @return {?}\n */\nexport function parseTimelineCommand(command) {\n    var /** @type {?} */ separatorPos = command.indexOf(':');\n    var /** @type {?} */ id = command.substring(1, separatorPos);\n    var /** @type {?} */ action = command.substr(separatorPos + 1);\n    return [id, action];\n}\nvar /** @type {?} */ _contains = function (elm1, elm2) { return false; };\nvar ɵ0 = _contains;\nvar /** @type {?} */ _matches = function (element, selector) {\n    return false;\n};\nvar ɵ1 = _matches;\nvar /** @type {?} */ _query = function (element, selector, multi) {\n    return [];\n};\nvar ɵ2 = _query;\nif (typeof Element != 'undefined') {\n    // this is well supported in all browsers\n    _contains = function (elm1, elm2) { return /** @type {?} */ (elm1.contains(elm2)); };\n    if (Element.prototype.matches) {\n        _matches = function (element, selector) { return element.matches(selector); };\n    }\n    else {\n        var /** @type {?} */ proto = /** @type {?} */ (Element.prototype);\n        var /** @type {?} */ fn_1 = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n            proto.oMatchesSelector || proto.webkitMatchesSelector;\n        if (fn_1) {\n            _matches = function (element, selector) { return fn_1.apply(element, [selector]); };\n        }\n    }\n    _query = function (element, selector, multi) {\n        var /** @type {?} */ results = [];\n        if (multi) {\n            results.push.apply(results, element.querySelectorAll(selector));\n        }\n        else {\n            var /** @type {?} */ elm = element.querySelector(selector);\n            if (elm) {\n                results.push(elm);\n            }\n        }\n        return results;\n    };\n}\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction containsVendorPrefix(prop) {\n    // Webkit is the only real popular vendor prefix nowadays\n    // cc: http://shouldiprefix.com/\n    return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nvar /** @type {?} */ _CACHED_BODY = null;\nvar /** @type {?} */ _IS_WEBKIT = false;\n/**\n * @param {?} prop\n * @return {?}\n */\nexport function validateStyleProperty(prop) {\n    if (!_CACHED_BODY) {\n        _CACHED_BODY = getBodyNode() || {};\n        _IS_WEBKIT = /** @type {?} */ ((_CACHED_BODY)).style ? ('WebkitAppearance' in /** @type {?} */ ((_CACHED_BODY)).style) : false;\n    }\n    var /** @type {?} */ result = true;\n    if (/** @type {?} */ ((_CACHED_BODY)).style && !containsVendorPrefix(prop)) {\n        result = prop in /** @type {?} */ ((_CACHED_BODY)).style;\n        if (!result && _IS_WEBKIT) {\n            var /** @type {?} */ camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n            result = camelProp in /** @type {?} */ ((_CACHED_BODY)).style;\n        }\n    }\n    return result;\n}\n/**\n * @return {?}\n */\nexport function getBodyNode() {\n    if (typeof document != 'undefined') {\n        return document.body;\n    }\n    return null;\n}\nexport var /** @type {?} */ matchesElement = _matches;\nexport var /** @type {?} */ containsElement = _contains;\nexport var /** @type {?} */ invokeQuery = _query;\nexport { ɵ0, ɵ1, ɵ2 };\n//# sourceMappingURL=shared.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { NoopAnimationPlayer } from '@angular/animations';\nimport { containsElement, invokeQuery, matchesElement, validateStyleProperty } from './shared';\n/**\n * \\@experimental\n */\nvar /**\n * \\@experimental\n */\nNoopAnimationDriver = /** @class */ (function () {\n    function NoopAnimationDriver() {\n    }\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.validateStyleProperty = /**\n     * @param {?} prop\n     * @return {?}\n     */\n    function (prop) { return validateStyleProperty(prop); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.matchesElement = /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    function (element, selector) {\n        return matchesElement(element, selector);\n    };\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.containsElement = /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    function (elm1, elm2) { return containsElement(elm1, elm2); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.query = /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    function (element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    };\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.computeStyle = /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    function (element, prop, defaultValue) {\n        return defaultValue || '';\n    };\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.animate = /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    function (element, keyframes, duration, delay, easing, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        return new NoopAnimationPlayer();\n    };\n    return NoopAnimationDriver;\n}());\n/**\n * \\@experimental\n */\nexport { NoopAnimationDriver };\n/**\n * \\@experimental\n * @abstract\n */\nvar AnimationDriver = /** @class */ (function () {\n    function AnimationDriver() {\n    }\n    AnimationDriver.NOOP = new NoopAnimationDriver();\n    return AnimationDriver;\n}());\nexport { AnimationDriver };\nfunction AnimationDriver_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationDriver.NOOP;\n    /**\n     * @abstract\n     * @param {?} prop\n     * @return {?}\n     */\n    AnimationDriver.prototype.validateStyleProperty = function (prop) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    AnimationDriver.prototype.matchesElement = function (element, selector) { };\n    /**\n     * @abstract\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    AnimationDriver.prototype.containsElement = function (elm1, elm2) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    AnimationDriver.prototype.query = function (element, selector, multi) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    AnimationDriver.prototype.computeStyle = function (element, prop, defaultValue) { };\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?=} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    AnimationDriver.prototype.animate = function (element, keyframes, duration, delay, easing, previousPlayers) { };\n}\n//# sourceMappingURL=animation_driver.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { sequence } from '@angular/animations';\nexport var /** @type {?} */ ONE_SECOND = 1000;\nexport var /** @type {?} */ SUBSTITUTION_EXPR_START = '{{';\nexport var /** @type {?} */ SUBSTITUTION_EXPR_END = '}}';\nexport var /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport var /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport var /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport var /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport var /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport var /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport var /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport var /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value) {\n    if (typeof value == 'number')\n        return value;\n    var /** @type {?} */ matches = (/** @type {?} */ (value)).match(/^(-?[\\.\\d]+)(m?s)/);\n    if (!matches || matches.length < 2)\n        return 0;\n    return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value, unit) {\n    switch (unit) {\n        case 's':\n            return value * ONE_SECOND;\n        default:\n            // ms or something else\n            return value;\n    }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(timings, errors, allowNegativeValues) {\n    return timings.hasOwnProperty('duration') ? /** @type {?} */ (timings) :\n        parseTimeExpression(/** @type {?} */ (timings), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n    var /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n    var /** @type {?} */ duration;\n    var /** @type {?} */ delay = 0;\n    var /** @type {?} */ easing = '';\n    if (typeof exp === 'string') {\n        var /** @type {?} */ matches = exp.match(regex);\n        if (matches === null) {\n            errors.push(\"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n            return { duration: 0, delay: 0, easing: '' };\n        }\n        duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n        var /** @type {?} */ delayMatch = matches[3];\n        if (delayMatch != null) {\n            delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n        }\n        var /** @type {?} */ easingVal = matches[5];\n        if (easingVal) {\n            easing = easingVal;\n        }\n    }\n    else {\n        duration = /** @type {?} */ (exp);\n    }\n    if (!allowNegativeValues) {\n        var /** @type {?} */ containsErrors = false;\n        var /** @type {?} */ startIndex = errors.length;\n        if (duration < 0) {\n            errors.push(\"Duration values below 0 are not allowed for this animation step.\");\n            containsErrors = true;\n        }\n        if (delay < 0) {\n            errors.push(\"Delay values below 0 are not allowed for this animation step.\");\n            containsErrors = true;\n        }\n        if (containsErrors) {\n            errors.splice(startIndex, 0, \"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n        }\n    }\n    return { duration: duration, delay: delay, easing: easing };\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(obj, destination) {\n    if (destination === void 0) { destination = {}; }\n    Object.keys(obj).forEach(function (prop) { destination[prop] = obj[prop]; });\n    return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles) {\n    var /** @type {?} */ normalizedStyles = {};\n    if (Array.isArray(styles)) {\n        styles.forEach(function (data) { return copyStyles(data, false, normalizedStyles); });\n    }\n    else {\n        copyStyles(styles, false, normalizedStyles);\n    }\n    return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(styles, readPrototype, destination) {\n    if (destination === void 0) { destination = {}; }\n    if (readPrototype) {\n        // we make use of a for-in loop so that the\n        // prototypically inherited properties are\n        // revealed from the backFill map\n        for (var /** @type {?} */ prop in styles) {\n            destination[prop] = styles[prop];\n        }\n    }\n    else {\n        copyObj(styles, destination);\n    }\n    return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = styles[prop];\n        });\n    }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = '';\n        });\n    }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps) {\n    if (Array.isArray(steps)) {\n        if (steps.length == 1)\n            return steps[0];\n        return sequence(steps);\n    }\n    return /** @type {?} */ (steps);\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(value, options, errors) {\n    var /** @type {?} */ params = options.params || {};\n    var /** @type {?} */ matches = extractStyleParams(value);\n    if (matches.length) {\n        matches.forEach(function (varName) {\n            if (!params.hasOwnProperty(varName)) {\n                errors.push(\"Unable to resolve the local animation param \" + varName + \" in the given list of values\");\n            }\n        });\n    }\n}\nvar /** @type {?} */ PARAM_REGEX = new RegExp(SUBSTITUTION_EXPR_START + \"\\\\s*(.+?)\\\\s*\" + SUBSTITUTION_EXPR_END, 'g');\n/**\n * @param {?} value\n * @return {?}\n */\nexport function extractStyleParams(value) {\n    var /** @type {?} */ params = [];\n    if (typeof value === 'string') {\n        var /** @type {?} */ val = value.toString();\n        var /** @type {?} */ match = void 0;\n        while (match = PARAM_REGEX.exec(val)) {\n            params.push(/** @type {?} */ (match[1]));\n        }\n        PARAM_REGEX.lastIndex = 0;\n    }\n    return params;\n}\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(value, params, errors) {\n    var /** @type {?} */ original = value.toString();\n    var /** @type {?} */ str = original.replace(PARAM_REGEX, function (_, varName) {\n        var /** @type {?} */ localVal = params[varName];\n        // this means that the value was never overidden by the data passed in by the user\n        if (!params.hasOwnProperty(varName)) {\n            errors.push(\"Please provide a value for the animation param \" + varName);\n            localVal = '';\n        }\n        return localVal.toString();\n    });\n    // we do this to assert that numeric values stay as they are\n    return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator) {\n    var /** @type {?} */ arr = [];\n    var /** @type {?} */ item = iterator.next();\n    while (!item.done) {\n        arr.push(item.value);\n        item = iterator.next();\n    }\n    return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(source, destination) {\n    if (source.params) {\n        var /** @type {?} */ p0_1 = source.params;\n        if (!destination.params) {\n            destination.params = {};\n        }\n        var /** @type {?} */ p1_1 = destination.params;\n        Object.keys(p0_1).forEach(function (param) {\n            if (!p1_1.hasOwnProperty(param)) {\n                p1_1[param] = p0_1[param];\n            }\n        });\n    }\n    return destination;\n}\nvar /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, function () {\n        var m = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            m[_i] = arguments[_i];\n        }\n        return m[1].toUpperCase();\n    });\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nexport function allowPreviousPlayerStylesMerge(duration, delay) {\n    return duration === 0 || delay === 0;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitDslNode(visitor, node, context) {\n    switch (node.type) {\n        case 7 /* Trigger */:\n            return visitor.visitTrigger(node, context);\n        case 0 /* State */:\n            return visitor.visitState(node, context);\n        case 1 /* Transition */:\n            return visitor.visitTransition(node, context);\n        case 2 /* Sequence */:\n            return visitor.visitSequence(node, context);\n        case 3 /* Group */:\n            return visitor.visitGroup(node, context);\n        case 4 /* Animate */:\n            return visitor.visitAnimate(node, context);\n        case 5 /* Keyframes */:\n            return visitor.visitKeyframes(node, context);\n        case 6 /* Style */:\n            return visitor.visitStyle(node, context);\n        case 8 /* Reference */:\n            return visitor.visitReference(node, context);\n        case 9 /* AnimateChild */:\n            return visitor.visitAnimateChild(node, context);\n        case 10 /* AnimateRef */:\n            return visitor.visitAnimateRef(node, context);\n        case 11 /* Query */:\n            return visitor.visitQuery(node, context);\n        case 12 /* Stagger */:\n            return visitor.visitStagger(node, context);\n        default:\n            throw new Error(\"Unable to resolve animation metadata node #\" + node.type);\n    }\n}\n//# sourceMappingURL=util.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport var /** @type {?} */ ANY_STATE = '*';\n/**\n * @param {?} transitionValue\n * @param {?} errors\n * @return {?}\n */\nexport function parseTransitionExpr(transitionValue, errors) {\n    var /** @type {?} */ expressions = [];\n    if (typeof transitionValue == 'string') {\n        (/** @type {?} */ (transitionValue))\n            .split(/\\s*,\\s*/)\n            .forEach(function (str) { return parseInnerTransitionStr(str, expressions, errors); });\n    }\n    else {\n        expressions.push(/** @type {?} */ (transitionValue));\n    }\n    return expressions;\n}\n/**\n * @param {?} eventStr\n * @param {?} expressions\n * @param {?} errors\n * @return {?}\n */\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n    if (eventStr[0] == ':') {\n        var /** @type {?} */ result = parseAnimationAlias(eventStr, errors);\n        if (typeof result == 'function') {\n            expressions.push(result);\n            return;\n        }\n        eventStr = /** @type {?} */ (result);\n    }\n    var /** @type {?} */ match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n    if (match == null || match.length < 4) {\n        errors.push(\"The provided transition expression \\\"\" + eventStr + \"\\\" is not supported\");\n        return expressions;\n    }\n    var /** @type {?} */ fromState = match[1];\n    var /** @type {?} */ separator = match[2];\n    var /** @type {?} */ toState = match[3];\n    expressions.push(makeLambdaFromStates(fromState, toState));\n    var /** @type {?} */ isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n    if (separator[0] == '<' && !isFullAnyStateExpr) {\n        expressions.push(makeLambdaFromStates(toState, fromState));\n    }\n}\n/**\n * @param {?} alias\n * @param {?} errors\n * @return {?}\n */\nfunction parseAnimationAlias(alias, errors) {\n    switch (alias) {\n        case ':enter':\n            return 'void => *';\n        case ':leave':\n            return '* => void';\n        case ':increment':\n            return function (fromState, toState) { return parseFloat(toState) > parseFloat(fromState); };\n        case ':decrement':\n            return function (fromState, toState) { return parseFloat(toState) < parseFloat(fromState); };\n        default:\n            errors.push(\"The transition alias value \\\"\" + alias + \"\\\" is not supported\");\n            return '* => *';\n    }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nvar /** @type {?} */ TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\nvar /** @type {?} */ FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\n/**\n * @param {?} lhs\n * @param {?} rhs\n * @return {?}\n */\nfunction makeLambdaFromStates(lhs, rhs) {\n    var /** @type {?} */ LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n    var /** @type {?} */ RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n    return function (fromState, toState) {\n        var /** @type {?} */ lhsMatch = lhs == ANY_STATE || lhs == fromState;\n        var /** @type {?} */ rhsMatch = rhs == ANY_STATE || rhs == toState;\n        if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n            lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n        }\n        if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n            rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n        }\n        return lhsMatch && rhsMatch;\n    };\n}\n//# sourceMappingURL=animation_transition_expr.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, style } from '@angular/animations';\nimport { getOrSetAsInMap } from '../render/shared';\nimport { NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, SUBSTITUTION_EXPR_START, copyObj, extractStyleParams, iteratorToArray, normalizeAnimationEntry, resolveTiming, validateStyleParams, visitDslNode } from '../util';\nimport { parseTransitionExpr } from './animation_transition_expr';\nvar /** @type {?} */ SELF_TOKEN = ':self';\nvar /** @type {?} */ SELF_TOKEN_REGEX = new RegExp(\"s*\" + SELF_TOKEN + \"s*,?\", 'g');\n/**\n * @param {?} driver\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nexport function buildAnimationAst(driver, metadata, errors) {\n    return new AnimationAstBuilderVisitor(driver).build(metadata, errors);\n}\nvar /** @type {?} */ ROOT_SELECTOR = '';\nvar AnimationAstBuilderVisitor = /** @class */ (function () {\n    function AnimationAstBuilderVisitor(_driver) {\n        this._driver = _driver;\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.build = /**\n     * @param {?} metadata\n     * @param {?} errors\n     * @return {?}\n     */\n    function (metadata, errors) {\n        var /** @type {?} */ context = new AnimationAstBuilderContext(errors);\n        this._resetContextStyleTimingState(context);\n        return /** @type {?} */ (visitDslNode(this, normalizeAnimationEntry(metadata), context));\n    };\n    /**\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype._resetContextStyleTimingState = /**\n     * @param {?} context\n     * @return {?}\n     */\n    function (context) {\n        context.currentQuerySelector = ROOT_SELECTOR;\n        context.collectedStyles = {};\n        context.collectedStyles[ROOT_SELECTOR] = {};\n        context.currentTime = 0;\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitTrigger = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        var /** @type {?} */ queryCount = context.queryCount = 0;\n        var /** @type {?} */ depCount = context.depCount = 0;\n        var /** @type {?} */ states = [];\n        var /** @type {?} */ transitions = [];\n        if (metadata.name.charAt(0) == '@') {\n            context.errors.push('animation triggers cannot be prefixed with an `@` sign (e.g. trigger(\\'@foo\\', [...]))');\n        }\n        metadata.definitions.forEach(function (def) {\n            _this._resetContextStyleTimingState(context);\n            if (def.type == 0 /* State */) {\n                var /** @type {?} */ stateDef_1 = /** @type {?} */ (def);\n                var /** @type {?} */ name_1 = stateDef_1.name;\n                name_1.split(/\\s*,\\s*/).forEach(function (n) {\n                    stateDef_1.name = n;\n                    states.push(_this.visitState(stateDef_1, context));\n                });\n                stateDef_1.name = name_1;\n            }\n            else if (def.type == 1 /* Transition */) {\n                var /** @type {?} */ transition = _this.visitTransition(/** @type {?} */ (def), context);\n                queryCount += transition.queryCount;\n                depCount += transition.depCount;\n                transitions.push(transition);\n            }\n            else {\n                context.errors.push('only state() and transition() definitions can sit inside of a trigger()');\n            }\n        });\n        return {\n            type: 7 /* Trigger */,\n            name: metadata.name, states: states, transitions: transitions, queryCount: queryCount, depCount: depCount,\n            options: null\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitState = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ styleAst = this.visitStyle(metadata.styles, context);\n        var /** @type {?} */ astParams = (metadata.options && metadata.options.params) || null;\n        if (styleAst.containsDynamicStyles) {\n            var /** @type {?} */ missingSubs_1 = new Set();\n            var /** @type {?} */ params_1 = astParams || {};\n            styleAst.styles.forEach(function (value) {\n                if (isObject(value)) {\n                    var /** @type {?} */ stylesObj_1 = /** @type {?} */ (value);\n                    Object.keys(stylesObj_1).forEach(function (prop) {\n                        extractStyleParams(stylesObj_1[prop]).forEach(function (sub) {\n                            if (!params_1.hasOwnProperty(sub)) {\n                                missingSubs_1.add(sub);\n                            }\n                        });\n                    });\n                }\n            });\n            if (missingSubs_1.size) {\n                var /** @type {?} */ missingSubsArr = iteratorToArray(missingSubs_1.values());\n                context.errors.push(\"state(\\\"\" + metadata.name + \"\\\", ...) must define default values for all the following style substitutions: \" + missingSubsArr.join(', '));\n            }\n        }\n        return {\n            type: 0 /* State */,\n            name: metadata.name,\n            style: styleAst,\n            options: astParams ? { params: astParams } : null\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitTransition = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        context.queryCount = 0;\n        context.depCount = 0;\n        var /** @type {?} */ animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        var /** @type {?} */ matchers = parseTransitionExpr(metadata.expr, context.errors);\n        return {\n            type: 1 /* Transition */,\n            matchers: matchers,\n            animation: animation,\n            queryCount: context.queryCount,\n            depCount: context.depCount,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitSequence = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        return {\n            type: 2 /* Sequence */,\n            steps: metadata.steps.map(function (s) { return visitDslNode(_this, s, context); }),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitGroup = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        var /** @type {?} */ currentTime = context.currentTime;\n        var /** @type {?} */ furthestTime = 0;\n        var /** @type {?} */ steps = metadata.steps.map(function (step) {\n            context.currentTime = currentTime;\n            var /** @type {?} */ innerAst = visitDslNode(_this, step, context);\n            furthestTime = Math.max(furthestTime, context.currentTime);\n            return innerAst;\n        });\n        context.currentTime = furthestTime;\n        return {\n            type: 3 /* Group */,\n            steps: steps,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitAnimate = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ timingAst = constructTimingAst(metadata.timings, context.errors);\n        context.currentAnimateTimings = timingAst;\n        var /** @type {?} */ styleAst;\n        var /** @type {?} */ styleMetadata = metadata.styles ? metadata.styles : style({});\n        if (styleMetadata.type == 5 /* Keyframes */) {\n            styleAst = this.visitKeyframes(/** @type {?} */ (styleMetadata), context);\n        }\n        else {\n            var /** @type {?} */ styleMetadata_1 = /** @type {?} */ (metadata.styles);\n            var /** @type {?} */ isEmpty = false;\n            if (!styleMetadata_1) {\n                isEmpty = true;\n                var /** @type {?} */ newStyleData = {};\n                if (timingAst.easing) {\n                    newStyleData['easing'] = timingAst.easing;\n                }\n                styleMetadata_1 = style(newStyleData);\n            }\n            context.currentTime += timingAst.duration + timingAst.delay;\n            var /** @type {?} */ _styleAst = this.visitStyle(styleMetadata_1, context);\n            _styleAst.isEmptyStep = isEmpty;\n            styleAst = _styleAst;\n        }\n        context.currentAnimateTimings = null;\n        return {\n            type: 4 /* Animate */,\n            timings: timingAst,\n            style: styleAst,\n            options: null\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitStyle = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ ast = this._makeStyleAst(metadata, context);\n        this._validateStyleAst(ast, context);\n        return ast;\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype._makeStyleAst = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ styles = [];\n        if (Array.isArray(metadata.styles)) {\n            (/** @type {?} */ (metadata.styles)).forEach(function (styleTuple) {\n                if (typeof styleTuple == 'string') {\n                    if (styleTuple == AUTO_STYLE) {\n                        styles.push(/** @type {?} */ (styleTuple));\n                    }\n                    else {\n                        context.errors.push(\"The provided style string value \" + styleTuple + \" is not allowed.\");\n                    }\n                }\n                else {\n                    styles.push(/** @type {?} */ (styleTuple));\n                }\n            });\n        }\n        else {\n            styles.push(metadata.styles);\n        }\n        var /** @type {?} */ containsDynamicStyles = false;\n        var /** @type {?} */ collectedEasing = null;\n        styles.forEach(function (styleData) {\n            if (isObject(styleData)) {\n                var /** @type {?} */ styleMap = /** @type {?} */ (styleData);\n                var /** @type {?} */ easing = styleMap['easing'];\n                if (easing) {\n                    collectedEasing = /** @type {?} */ (easing);\n                    delete styleMap['easing'];\n                }\n                if (!containsDynamicStyles) {\n                    for (var /** @type {?} */ prop in styleMap) {\n                        var /** @type {?} */ value = styleMap[prop];\n                        if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n                            containsDynamicStyles = true;\n                            break;\n                        }\n                    }\n                }\n            }\n        });\n        return {\n            type: 6 /* Style */,\n            styles: styles,\n            easing: collectedEasing,\n            offset: metadata.offset, containsDynamicStyles: containsDynamicStyles,\n            options: null\n        };\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype._validateStyleAst = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        var /** @type {?} */ timings = context.currentAnimateTimings;\n        var /** @type {?} */ endTime = context.currentTime;\n        var /** @type {?} */ startTime = context.currentTime;\n        if (timings && startTime > 0) {\n            startTime -= timings.duration + timings.delay;\n        }\n        ast.styles.forEach(function (tuple) {\n            if (typeof tuple == 'string')\n                return;\n            Object.keys(tuple).forEach(function (prop) {\n                if (!_this._driver.validateStyleProperty(prop)) {\n                    context.errors.push(\"The provided animation property \\\"\" + prop + \"\\\" is not a supported CSS property for animations\");\n                    return;\n                }\n                var /** @type {?} */ collectedStyles = context.collectedStyles[/** @type {?} */ ((context.currentQuerySelector))];\n                var /** @type {?} */ collectedEntry = collectedStyles[prop];\n                var /** @type {?} */ updateCollectedStyle = true;\n                if (collectedEntry) {\n                    if (startTime != endTime && startTime >= collectedEntry.startTime &&\n                        endTime <= collectedEntry.endTime) {\n                        context.errors.push(\"The CSS property \\\"\" + prop + \"\\\" that exists between the times of \\\"\" + collectedEntry.startTime + \"ms\\\" and \\\"\" + collectedEntry.endTime + \"ms\\\" is also being animated in a parallel animation between the times of \\\"\" + startTime + \"ms\\\" and \\\"\" + endTime + \"ms\\\"\");\n                        updateCollectedStyle = false;\n                    }\n                    // we always choose the smaller start time value since we\n                    // want to have a record of the entire animation window where\n                    // the style property is being animated in between\n                    startTime = collectedEntry.startTime;\n                }\n                if (updateCollectedStyle) {\n                    collectedStyles[prop] = { startTime: startTime, endTime: endTime };\n                }\n                if (context.options) {\n                    validateStyleParams(tuple[prop], context.options, context.errors);\n                }\n            });\n        });\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitKeyframes = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        var /** @type {?} */ ast = { type: 5 /* Keyframes */, styles: [], options: null };\n        if (!context.currentAnimateTimings) {\n            context.errors.push(\"keyframes() must be placed inside of a call to animate()\");\n            return ast;\n        }\n        var /** @type {?} */ MAX_KEYFRAME_OFFSET = 1;\n        var /** @type {?} */ totalKeyframesWithOffsets = 0;\n        var /** @type {?} */ offsets = [];\n        var /** @type {?} */ offsetsOutOfOrder = false;\n        var /** @type {?} */ keyframesOutOfRange = false;\n        var /** @type {?} */ previousOffset = 0;\n        var /** @type {?} */ keyframes = metadata.steps.map(function (styles) {\n            var /** @type {?} */ style = _this._makeStyleAst(styles, context);\n            var /** @type {?} */ offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n            var /** @type {?} */ offset = 0;\n            if (offsetVal != null) {\n                totalKeyframesWithOffsets++;\n                offset = style.offset = offsetVal;\n            }\n            keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n            offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n            previousOffset = offset;\n            offsets.push(offset);\n            return style;\n        });\n        if (keyframesOutOfRange) {\n            context.errors.push(\"Please ensure that all keyframe offsets are between 0 and 1\");\n        }\n        if (offsetsOutOfOrder) {\n            context.errors.push(\"Please ensure that all keyframe offsets are in order\");\n        }\n        var /** @type {?} */ length = metadata.steps.length;\n        var /** @type {?} */ generatedOffset = 0;\n        if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n            context.errors.push(\"Not all style() steps within the declared keyframes() contain offsets\");\n        }\n        else if (totalKeyframesWithOffsets == 0) {\n            generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n        }\n        var /** @type {?} */ limit = length - 1;\n        var /** @type {?} */ currentTime = context.currentTime;\n        var /** @type {?} */ currentAnimateTimings = /** @type {?} */ ((context.currentAnimateTimings));\n        var /** @type {?} */ animateDuration = currentAnimateTimings.duration;\n        keyframes.forEach(function (kf, i) {\n            var /** @type {?} */ offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n            var /** @type {?} */ durationUpToThisFrame = offset * animateDuration;\n            context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n            currentAnimateTimings.duration = durationUpToThisFrame;\n            _this._validateStyleAst(kf, context);\n            kf.offset = offset;\n            ast.styles.push(kf);\n        });\n        return ast;\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitReference = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        return {\n            type: 8 /* Reference */,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitAnimateChild = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        context.depCount++;\n        return {\n            type: 9 /* AnimateChild */,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitAnimateRef = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        return {\n            type: 10 /* AnimateRef */,\n            animation: this.visitReference(metadata.animation, context),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitQuery = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ parentSelector = /** @type {?} */ ((context.currentQuerySelector));\n        var /** @type {?} */ options = /** @type {?} */ ((metadata.options || {}));\n        context.queryCount++;\n        context.currentQuery = metadata;\n        var _a = normalizeSelector(metadata.selector), selector = _a[0], includeSelf = _a[1];\n        context.currentQuerySelector =\n            parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n        getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n        var /** @type {?} */ animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        context.currentQuery = null;\n        context.currentQuerySelector = parentSelector;\n        return {\n            type: 11 /* Query */,\n            selector: selector,\n            limit: options.limit || 0,\n            optional: !!options.optional, includeSelf: includeSelf, animation: animation,\n            originalSelector: metadata.selector,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitStagger = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        if (!context.currentQuery) {\n            context.errors.push(\"stagger() can only be used inside of query()\");\n        }\n        var /** @type {?} */ timings = metadata.timings === 'full' ?\n            { duration: 0, delay: 0, easing: 'full' } :\n            resolveTiming(metadata.timings, context.errors, true);\n        return {\n            type: 12 /* Stagger */,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context), timings: timings,\n            options: null\n        };\n    };\n    return AnimationAstBuilderVisitor;\n}());\nexport { AnimationAstBuilderVisitor };\nfunction AnimationAstBuilderVisitor_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationAstBuilderVisitor.prototype._driver;\n}\n/**\n * @param {?} selector\n * @return {?}\n */\nfunction normalizeSelector(selector) {\n    var /** @type {?} */ hasAmpersand = selector.split(/\\s*,\\s*/).find(function (token) { return token == SELF_TOKEN; }) ? true : false;\n    if (hasAmpersand) {\n        selector = selector.replace(SELF_TOKEN_REGEX, '');\n    }\n    // the :enter and :leave selectors are filled in at runtime during timeline building\n    selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n        .replace(/@\\w+/g, function (match) { return NG_TRIGGER_SELECTOR + '-' + match.substr(1); })\n        .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n    return [selector, hasAmpersand];\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction normalizeParams(obj) {\n    return obj ? copyObj(obj) : null;\n}\nvar AnimationAstBuilderContext = /** @class */ (function () {\n    function AnimationAstBuilderContext(errors) {\n        this.errors = errors;\n        this.queryCount = 0;\n        this.depCount = 0;\n        this.currentTransition = null;\n        this.currentQuery = null;\n        this.currentQuerySelector = null;\n        this.currentAnimateTimings = null;\n        this.currentTime = 0;\n        this.collectedStyles = {};\n        this.options = null;\n    }\n    return AnimationAstBuilderContext;\n}());\nexport { AnimationAstBuilderContext };\nfunction AnimationAstBuilderContext_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.queryCount;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.depCount;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentTransition;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentQuery;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentQuerySelector;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentAnimateTimings;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.currentTime;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.collectedStyles;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.options;\n    /** @type {?} */\n    AnimationAstBuilderContext.prototype.errors;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction consumeOffset(styles) {\n    if (typeof styles == 'string')\n        return null;\n    var /** @type {?} */ offset = null;\n    if (Array.isArray(styles)) {\n        styles.forEach(function (styleTuple) {\n            if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n                var /** @type {?} */ obj = /** @type {?} */ (styleTuple);\n                offset = parseFloat(/** @type {?} */ (obj['offset']));\n                delete obj['offset'];\n            }\n        });\n    }\n    else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n        var /** @type {?} */ obj = /** @type {?} */ (styles);\n        offset = parseFloat(/** @type {?} */ (obj['offset']));\n        delete obj['offset'];\n    }\n    return offset;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isObject(value) {\n    return !Array.isArray(value) && typeof value == 'object';\n}\n/**\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nfunction constructTimingAst(value, errors) {\n    var /** @type {?} */ timings = null;\n    if (value.hasOwnProperty('duration')) {\n        timings = /** @type {?} */ (value);\n    }\n    else if (typeof value == 'number') {\n        var /** @type {?} */ duration = resolveTiming(/** @type {?} */ (value), errors).duration;\n        return makeTimingAst(/** @type {?} */ (duration), 0, '');\n    }\n    var /** @type {?} */ strValue = /** @type {?} */ (value);\n    var /** @type {?} */ isDynamic = strValue.split(/\\s+/).some(function (v) { return v.charAt(0) == '{' && v.charAt(1) == '{'; });\n    if (isDynamic) {\n        var /** @type {?} */ ast = /** @type {?} */ (makeTimingAst(0, 0, ''));\n        ast.dynamic = true;\n        ast.strValue = strValue;\n        return /** @type {?} */ (ast);\n    }\n    timings = timings || resolveTiming(strValue, errors);\n    return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction normalizeAnimationOptions(options) {\n    if (options) {\n        options = copyObj(options);\n        if (options['params']) {\n            options['params'] = /** @type {?} */ ((normalizeParams(options['params'])));\n        }\n    }\n    else {\n        options = {};\n    }\n    return options;\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @param {?} easing\n * @return {?}\n */\nfunction makeTimingAst(duration, delay, easing) {\n    return { duration: duration, delay: delay, easing: easing };\n}\n//# sourceMappingURL=animation_ast_builder.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @record\n */\nexport function AnimationTimelineInstruction() { }\nfunction AnimationTimelineInstruction_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.element;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.keyframes;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.preStyleProps;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.postStyleProps;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.duration;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.delay;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.totalTime;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.easing;\n    /** @type {?|undefined} */\n    AnimationTimelineInstruction.prototype.stretchStartingKeyframe;\n    /** @type {?} */\n    AnimationTimelineInstruction.prototype.subTimeline;\n}\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} duration\n * @param {?} delay\n * @param {?=} easing\n * @param {?=} subTimeline\n * @return {?}\n */\nexport function createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing, subTimeline) {\n    if (easing === void 0) { easing = null; }\n    if (subTimeline === void 0) { subTimeline = false; }\n    return {\n        type: 1 /* TimelineAnimation */,\n        element: element,\n        keyframes: keyframes,\n        preStyleProps: preStyleProps,\n        postStyleProps: postStyleProps,\n        duration: duration,\n        delay: delay,\n        totalTime: duration + delay, easing: easing, subTimeline: subTimeline\n    };\n}\n//# sourceMappingURL=animation_timeline_instruction.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar ElementInstructionMap = /** @class */ (function () {\n    function ElementInstructionMap() {\n        this._map = new Map();\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.consume = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ instructions = this._map.get(element);\n        if (instructions) {\n            this._map.delete(element);\n        }\n        else {\n            instructions = [];\n        }\n        return instructions;\n    };\n    /**\n     * @param {?} element\n     * @param {?} instructions\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.append = /**\n     * @param {?} element\n     * @param {?} instructions\n     * @return {?}\n     */\n    function (element, instructions) {\n        var /** @type {?} */ existingInstructions = this._map.get(element);\n        if (!existingInstructions) {\n            this._map.set(element, existingInstructions = []);\n        }\n        existingInstructions.push.apply(existingInstructions, instructions);\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.has = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) { return this._map.has(element); };\n    /**\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.clear = /**\n     * @return {?}\n     */\n    function () { this._map.clear(); };\n    return ElementInstructionMap;\n}());\nexport { ElementInstructionMap };\nfunction ElementInstructionMap_tsickle_Closure_declarations() {\n    /** @type {?} */\n    ElementInstructionMap.prototype._map;\n}\n//# sourceMappingURL=element_instruction_map.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport * as tslib_1 from \"tslib\";\nimport { AUTO_STYLE, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\nimport { copyObj, copyStyles, interpolateParams, iteratorToArray, resolveTiming, resolveTimingValue, visitDslNode } from '../util';\nimport { createTimelineInstruction } from './animation_timeline_instruction';\nimport { ElementInstructionMap } from './element_instruction_map';\nvar /** @type {?} */ ONE_FRAME_IN_MILLISECONDS = 1;\nvar /** @type {?} */ ENTER_TOKEN = ':enter';\nvar /** @type {?} */ ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nvar /** @type {?} */ LEAVE_TOKEN = ':leave';\nvar /** @type {?} */ LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?} enterClassName\n * @param {?} leaveClassName\n * @param {?=} startingStyles\n * @param {?=} finalStyles\n * @param {?=} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nexport function buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors) {\n    if (startingStyles === void 0) { startingStyles = {}; }\n    if (finalStyles === void 0) { finalStyles = {}; }\n    if (errors === void 0) { errors = []; }\n    return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nvar AnimationTimelineBuilderVisitor = /** @class */ (function () {\n    function AnimationTimelineBuilderVisitor() {\n    }\n    /**\n     * @param {?} driver\n     * @param {?} rootElement\n     * @param {?} ast\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?} startingStyles\n     * @param {?} finalStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @param {?=} errors\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.buildKeyframes = /**\n     * @param {?} driver\n     * @param {?} rootElement\n     * @param {?} ast\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?} startingStyles\n     * @param {?} finalStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @param {?=} errors\n     * @return {?}\n     */\n    function (driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors) {\n        if (errors === void 0) { errors = []; }\n        subInstructions = subInstructions || new ElementInstructionMap();\n        var /** @type {?} */ context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n        context.options = options;\n        context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n        visitDslNode(this, ast, context);\n        // this checks to see if an actual animation happened\n        var /** @type {?} */ timelines = context.timelines.filter(function (timeline) { return timeline.containsAnimation(); });\n        if (timelines.length && Object.keys(finalStyles).length) {\n            var /** @type {?} */ tl = timelines[timelines.length - 1];\n            if (!tl.allowOnlyTimelineStyles()) {\n                tl.setStyles([finalStyles], null, context.errors, options);\n            }\n        }\n        return timelines.length ? timelines.map(function (timeline) { return timeline.buildKeyframes(); }) :\n            [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitTrigger = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        // these values are not visited in this AST\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitState = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        // these values are not visited in this AST\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitTransition = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        // these values are not visited in this AST\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitAnimateChild = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ elementInstructions = context.subInstructions.consume(context.element);\n        if (elementInstructions) {\n            var /** @type {?} */ innerContext = context.createSubContext(ast.options);\n            var /** @type {?} */ startTime = context.currentTimeline.currentTime;\n            var /** @type {?} */ endTime = this._visitSubInstructions(elementInstructions, innerContext, /** @type {?} */ (innerContext.options));\n            if (startTime != endTime) {\n                // we do this on the upper context because we created a sub context for\n                // the sub child animations\n                context.transformIntoNewTimeline(endTime);\n            }\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitAnimateRef = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ innerContext = context.createSubContext(ast.options);\n        innerContext.transformIntoNewTimeline();\n        this.visitReference(ast.animation, innerContext);\n        context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} instructions\n     * @param {?} context\n     * @param {?} options\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype._visitSubInstructions = /**\n     * @param {?} instructions\n     * @param {?} context\n     * @param {?} options\n     * @return {?}\n     */\n    function (instructions, context, options) {\n        var /** @type {?} */ startTime = context.currentTimeline.currentTime;\n        var /** @type {?} */ furthestTime = startTime;\n        // this is a special-case for when a user wants to skip a sub\n        // animation from being fired entirely.\n        var /** @type {?} */ duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n        var /** @type {?} */ delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n        if (duration !== 0) {\n            instructions.forEach(function (instruction) {\n                var /** @type {?} */ instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n                furthestTime =\n                    Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n            });\n        }\n        return furthestTime;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitReference = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        context.updateOptions(ast.options, true);\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitSequence = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        var /** @type {?} */ subContextCount = context.subContextCount;\n        var /** @type {?} */ ctx = context;\n        var /** @type {?} */ options = ast.options;\n        if (options && (options.params || options.delay)) {\n            ctx = context.createSubContext(options);\n            ctx.transformIntoNewTimeline();\n            if (options.delay != null) {\n                if (ctx.previousNode.type == 6 /* Style */) {\n                    ctx.currentTimeline.snapshotCurrentStyles();\n                    ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n                }\n                var /** @type {?} */ delay = resolveTimingValue(options.delay);\n                ctx.delayNextStep(delay);\n            }\n        }\n        if (ast.steps.length) {\n            ast.steps.forEach(function (s) { return visitDslNode(_this, s, ctx); });\n            // this is here just incase the inner steps only contain or end with a style() call\n            ctx.currentTimeline.applyStylesToKeyframe();\n            // this means that some animation function within the sequence\n            // ended up creating a sub timeline (which means the current\n            // timeline cannot overlap with the contents of the sequence)\n            if (ctx.subContextCount > subContextCount) {\n                ctx.transformIntoNewTimeline();\n            }\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitGroup = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        var /** @type {?} */ innerTimelines = [];\n        var /** @type {?} */ furthestTime = context.currentTimeline.currentTime;\n        var /** @type {?} */ delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n        ast.steps.forEach(function (s) {\n            var /** @type {?} */ innerContext = context.createSubContext(ast.options);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            visitDslNode(_this, s, innerContext);\n            furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n            innerTimelines.push(innerContext.currentTimeline);\n        });\n        // this operation is run after the AST loop because otherwise\n        // if the parent timeline's collected styles were updated then\n        // it would pass in invalid data into the new-to-be forked items\n        innerTimelines.forEach(function (timeline) { return context.currentTimeline.mergeTimelineCollectedStyles(timeline); });\n        context.transformIntoNewTimeline(furthestTime);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype._visitTiming = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        if ((/** @type {?} */ (ast)).dynamic) {\n            var /** @type {?} */ strValue = (/** @type {?} */ (ast)).strValue;\n            var /** @type {?} */ timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n            return resolveTiming(timingValue, context.errors);\n        }\n        else {\n            return { duration: ast.duration, delay: ast.delay, easing: ast.easing };\n        }\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitAnimate = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n        var /** @type {?} */ timeline = context.currentTimeline;\n        if (timings.delay) {\n            context.incrementTime(timings.delay);\n            timeline.snapshotCurrentStyles();\n        }\n        var /** @type {?} */ style = ast.style;\n        if (style.type == 5 /* Keyframes */) {\n            this.visitKeyframes(style, context);\n        }\n        else {\n            context.incrementTime(timings.duration);\n            this.visitStyle(/** @type {?} */ (style), context);\n            timeline.applyStylesToKeyframe();\n        }\n        context.currentAnimateTimings = null;\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitStyle = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ timeline = context.currentTimeline;\n        var /** @type {?} */ timings = /** @type {?} */ ((context.currentAnimateTimings));\n        // this is a special case for when a style() call\n        // directly follows  an animate() call (but not inside of an animate() call)\n        if (!timings && timeline.getCurrentStyleProperties().length) {\n            timeline.forwardFrame();\n        }\n        var /** @type {?} */ easing = (timings && timings.easing) || ast.easing;\n        if (ast.isEmptyStep) {\n            timeline.applyEmptyStep(easing);\n        }\n        else {\n            timeline.setStyles(ast.styles, easing, context.errors, context.options);\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitKeyframes = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ currentAnimateTimings = /** @type {?} */ ((context.currentAnimateTimings));\n        var /** @type {?} */ startTime = (/** @type {?} */ ((context.currentTimeline))).duration;\n        var /** @type {?} */ duration = currentAnimateTimings.duration;\n        var /** @type {?} */ innerContext = context.createSubContext();\n        var /** @type {?} */ innerTimeline = innerContext.currentTimeline;\n        innerTimeline.easing = currentAnimateTimings.easing;\n        ast.styles.forEach(function (step) {\n            var /** @type {?} */ offset = step.offset || 0;\n            innerTimeline.forwardTime(offset * duration);\n            innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n            innerTimeline.applyStylesToKeyframe();\n        });\n        // this will ensure that the parent timeline gets all the styles from\n        // the child even if the new timeline below is not used\n        context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n        // we do this because the window between this timeline and the sub timeline\n        // should ensure that the styles within are exactly the same as they were before\n        context.transformIntoNewTimeline(startTime + duration);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitQuery = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        // in the event that the first step before this is a style step we need\n        // to ensure the styles are applied before the children are animated\n        var /** @type {?} */ startTime = context.currentTimeline.currentTime;\n        var /** @type {?} */ options = /** @type {?} */ ((ast.options || {}));\n        var /** @type {?} */ delay = options.delay ? resolveTimingValue(options.delay) : 0;\n        if (delay && (context.previousNode.type === 6 /* Style */ ||\n            (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n            context.currentTimeline.snapshotCurrentStyles();\n            context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        var /** @type {?} */ furthestTime = startTime;\n        var /** @type {?} */ elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n        context.currentQueryTotal = elms.length;\n        var /** @type {?} */ sameElementTimeline = null;\n        elms.forEach(function (element, i) {\n            context.currentQueryIndex = i;\n            var /** @type {?} */ innerContext = context.createSubContext(ast.options, element);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            if (element === context.element) {\n                sameElementTimeline = innerContext.currentTimeline;\n            }\n            visitDslNode(_this, ast.animation, innerContext);\n            // this is here just incase the inner steps only contain or end\n            // with a style() call (which is here to signal that this is a preparatory\n            // call to style an element before it is animated again)\n            innerContext.currentTimeline.applyStylesToKeyframe();\n            var /** @type {?} */ endTime = innerContext.currentTimeline.currentTime;\n            furthestTime = Math.max(furthestTime, endTime);\n        });\n        context.currentQueryIndex = 0;\n        context.currentQueryTotal = 0;\n        context.transformIntoNewTimeline(furthestTime);\n        if (sameElementTimeline) {\n            context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n            context.currentTimeline.snapshotCurrentStyles();\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitStagger = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ parentContext = /** @type {?} */ ((context.parentContext));\n        var /** @type {?} */ tl = context.currentTimeline;\n        var /** @type {?} */ timings = ast.timings;\n        var /** @type {?} */ duration = Math.abs(timings.duration);\n        var /** @type {?} */ maxTime = duration * (context.currentQueryTotal - 1);\n        var /** @type {?} */ delay = duration * context.currentQueryIndex;\n        var /** @type {?} */ staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n        switch (staggerTransformer) {\n            case 'reverse':\n                delay = maxTime - delay;\n                break;\n            case 'full':\n                delay = parentContext.currentStaggerTime;\n                break;\n        }\n        var /** @type {?} */ timeline = context.currentTimeline;\n        if (delay) {\n            timeline.delayNextStep(delay);\n        }\n        var /** @type {?} */ startingTime = timeline.currentTime;\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n        // time = duration + delay\n        // the reason why this computation is so complex is because\n        // the inner timeline may either have a delay value or a stretched\n        // keyframe depending on if a subtimeline is not used or is used.\n        parentContext.currentStaggerTime =\n            (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n    };\n    return AnimationTimelineBuilderVisitor;\n}());\nexport { AnimationTimelineBuilderVisitor };\nvar /** @type {?} */ DEFAULT_NOOP_PREVIOUS_NODE = /** @type {?} */ ({});\nvar AnimationTimelineContext = /** @class */ (function () {\n    function AnimationTimelineContext(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n        this._driver = _driver;\n        this.element = element;\n        this.subInstructions = subInstructions;\n        this._enterClassName = _enterClassName;\n        this._leaveClassName = _leaveClassName;\n        this.errors = errors;\n        this.timelines = timelines;\n        this.parentContext = null;\n        this.currentAnimateTimings = null;\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.subContextCount = 0;\n        this.options = {};\n        this.currentQueryIndex = 0;\n        this.currentQueryTotal = 0;\n        this.currentStaggerTime = 0;\n        this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n        timelines.push(this.currentTimeline);\n    }\n    Object.defineProperty(AnimationTimelineContext.prototype, \"params\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.options.params; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} options\n     * @param {?=} skipIfExists\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.updateOptions = /**\n     * @param {?} options\n     * @param {?=} skipIfExists\n     * @return {?}\n     */\n    function (options, skipIfExists) {\n        var _this = this;\n        if (!options)\n            return;\n        var /** @type {?} */ newOptions = /** @type {?} */ (options);\n        var /** @type {?} */ optionsToUpdate = this.options;\n        // NOTE: this will get patched up when other animation methods support duration overrides\n        if (newOptions.duration != null) {\n            (/** @type {?} */ (optionsToUpdate)).duration = resolveTimingValue(newOptions.duration);\n        }\n        if (newOptions.delay != null) {\n            optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n        }\n        var /** @type {?} */ newParams = newOptions.params;\n        if (newParams) {\n            var /** @type {?} */ paramsToUpdate_1 = /** @type {?} */ ((optionsToUpdate.params));\n            if (!paramsToUpdate_1) {\n                paramsToUpdate_1 = this.options.params = {};\n            }\n            Object.keys(newParams).forEach(function (name) {\n                if (!skipIfExists || !paramsToUpdate_1.hasOwnProperty(name)) {\n                    paramsToUpdate_1[name] = interpolateParams(newParams[name], paramsToUpdate_1, _this.errors);\n                }\n            });\n        }\n    };\n    /**\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype._copyOptions = /**\n     * @return {?}\n     */\n    function () {\n        var /** @type {?} */ options = {};\n        if (this.options) {\n            var /** @type {?} */ oldParams_1 = this.options.params;\n            if (oldParams_1) {\n                var /** @type {?} */ params_1 = options['params'] = {};\n                Object.keys(oldParams_1).forEach(function (name) { params_1[name] = oldParams_1[name]; });\n            }\n        }\n        return options;\n    };\n    /**\n     * @param {?=} options\n     * @param {?=} element\n     * @param {?=} newTime\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.createSubContext = /**\n     * @param {?=} options\n     * @param {?=} element\n     * @param {?=} newTime\n     * @return {?}\n     */\n    function (options, element, newTime) {\n        if (options === void 0) { options = null; }\n        var /** @type {?} */ target = element || this.element;\n        var /** @type {?} */ context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n        context.previousNode = this.previousNode;\n        context.currentAnimateTimings = this.currentAnimateTimings;\n        context.options = this._copyOptions();\n        context.updateOptions(options);\n        context.currentQueryIndex = this.currentQueryIndex;\n        context.currentQueryTotal = this.currentQueryTotal;\n        context.parentContext = this;\n        this.subContextCount++;\n        return context;\n    };\n    /**\n     * @param {?=} newTime\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.transformIntoNewTimeline = /**\n     * @param {?=} newTime\n     * @return {?}\n     */\n    function (newTime) {\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n        this.timelines.push(this.currentTimeline);\n        return this.currentTimeline;\n    };\n    /**\n     * @param {?} instruction\n     * @param {?} duration\n     * @param {?} delay\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.appendInstructionToTimeline = /**\n     * @param {?} instruction\n     * @param {?} duration\n     * @param {?} delay\n     * @return {?}\n     */\n    function (instruction, duration, delay) {\n        var /** @type {?} */ updatedTimings = {\n            duration: duration != null ? duration : instruction.duration,\n            delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n            easing: ''\n        };\n        var /** @type {?} */ builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n        this.timelines.push(builder);\n        return updatedTimings;\n    };\n    /**\n     * @param {?} time\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.incrementTime = /**\n     * @param {?} time\n     * @return {?}\n     */\n    function (time) {\n        this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n    };\n    /**\n     * @param {?} delay\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.delayNextStep = /**\n     * @param {?} delay\n     * @return {?}\n     */\n    function (delay) {\n        // negative delays are not yet supported\n        if (delay > 0) {\n            this.currentTimeline.delayNextStep(delay);\n        }\n    };\n    /**\n     * @param {?} selector\n     * @param {?} originalSelector\n     * @param {?} limit\n     * @param {?} includeSelf\n     * @param {?} optional\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.invokeQuery = /**\n     * @param {?} selector\n     * @param {?} originalSelector\n     * @param {?} limit\n     * @param {?} includeSelf\n     * @param {?} optional\n     * @param {?} errors\n     * @return {?}\n     */\n    function (selector, originalSelector, limit, includeSelf, optional, errors) {\n        var /** @type {?} */ results = [];\n        if (includeSelf) {\n            results.push(this.element);\n        }\n        if (selector.length > 0) {\n            // if :self is only used then the selector is empty\n            selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n            selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n            var /** @type {?} */ multi = limit != 1;\n            var /** @type {?} */ elements = this._driver.query(this.element, selector, multi);\n            if (limit !== 0) {\n                elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) :\n                    elements.slice(0, limit);\n            }\n            results.push.apply(results, elements);\n        }\n        if (!optional && results.length == 0) {\n            errors.push(\"`query(\\\"\" + originalSelector + \"\\\")` returned zero elements. (Use `query(\\\"\" + originalSelector + \"\\\", { optional: true })` if you wish to allow this.)\");\n        }\n        return results;\n    };\n    return AnimationTimelineContext;\n}());\nexport { AnimationTimelineContext };\nfunction AnimationTimelineContext_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTimelineContext.prototype.parentContext;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentTimeline;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentAnimateTimings;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.previousNode;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.subContextCount;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.options;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentQueryIndex;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentQueryTotal;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.currentStaggerTime;\n    /** @type {?} */\n    AnimationTimelineContext.prototype._driver;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.element;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.subInstructions;\n    /** @type {?} */\n    AnimationTimelineContext.prototype._enterClassName;\n    /** @type {?} */\n    AnimationTimelineContext.prototype._leaveClassName;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.errors;\n    /** @type {?} */\n    AnimationTimelineContext.prototype.timelines;\n}\nvar TimelineBuilder = /** @class */ (function () {\n    function TimelineBuilder(_driver, element, startTime, _elementTimelineStylesLookup) {\n        this._driver = _driver;\n        this.element = element;\n        this.startTime = startTime;\n        this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n        this.duration = 0;\n        this._previousKeyframe = {};\n        this._currentKeyframe = {};\n        this._keyframes = new Map();\n        this._styleSummary = {};\n        this._pendingStyles = {};\n        this._backFill = {};\n        this._currentEmptyStepKeyframe = null;\n        if (!this._elementTimelineStylesLookup) {\n            this._elementTimelineStylesLookup = new Map();\n        }\n        this._localTimelineStyles = Object.create(this._backFill, {});\n        this._globalTimelineStyles = /** @type {?} */ ((this._elementTimelineStylesLookup.get(element)));\n        if (!this._globalTimelineStyles) {\n            this._globalTimelineStyles = this._localTimelineStyles;\n            this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n        }\n        this._loadKeyframe();\n    }\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.containsAnimation = /**\n     * @return {?}\n     */\n    function () {\n        switch (this._keyframes.size) {\n            case 0:\n                return false;\n            case 1:\n                return this.getCurrentStyleProperties().length > 0;\n            default:\n                return true;\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.getCurrentStyleProperties = /**\n     * @return {?}\n     */\n    function () { return Object.keys(this._currentKeyframe); };\n    Object.defineProperty(TimelineBuilder.prototype, \"currentTime\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.startTime + this.duration; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} delay\n     * @return {?}\n     */\n    TimelineBuilder.prototype.delayNextStep = /**\n     * @param {?} delay\n     * @return {?}\n     */\n    function (delay) {\n        // in the event that a style() step is placed right before a stagger()\n        // and that style() step is the very first style() value in the animation\n        // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n        // properly applies the style() values to work with the stagger...\n        var /** @type {?} */ hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n        if (this.duration || hasPreStyleStep) {\n            this.forwardTime(this.currentTime + delay);\n            if (hasPreStyleStep) {\n                this.snapshotCurrentStyles();\n            }\n        }\n        else {\n            this.startTime += delay;\n        }\n    };\n    /**\n     * @param {?} element\n     * @param {?=} currentTime\n     * @return {?}\n     */\n    TimelineBuilder.prototype.fork = /**\n     * @param {?} element\n     * @param {?=} currentTime\n     * @return {?}\n     */\n    function (element, currentTime) {\n        this.applyStylesToKeyframe();\n        return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype._loadKeyframe = /**\n     * @return {?}\n     */\n    function () {\n        if (this._currentKeyframe) {\n            this._previousKeyframe = this._currentKeyframe;\n        }\n        this._currentKeyframe = /** @type {?} */ ((this._keyframes.get(this.duration)));\n        if (!this._currentKeyframe) {\n            this._currentKeyframe = Object.create(this._backFill, {});\n            this._keyframes.set(this.duration, this._currentKeyframe);\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.forwardFrame = /**\n     * @return {?}\n     */\n    function () {\n        this.duration += ONE_FRAME_IN_MILLISECONDS;\n        this._loadKeyframe();\n    };\n    /**\n     * @param {?} time\n     * @return {?}\n     */\n    TimelineBuilder.prototype.forwardTime = /**\n     * @param {?} time\n     * @return {?}\n     */\n    function (time) {\n        this.applyStylesToKeyframe();\n        this.duration = time;\n        this._loadKeyframe();\n    };\n    /**\n     * @param {?} prop\n     * @param {?} value\n     * @return {?}\n     */\n    TimelineBuilder.prototype._updateStyle = /**\n     * @param {?} prop\n     * @param {?} value\n     * @return {?}\n     */\n    function (prop, value) {\n        this._localTimelineStyles[prop] = value;\n        this._globalTimelineStyles[prop] = value;\n        this._styleSummary[prop] = { time: this.currentTime, value: value };\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.allowOnlyTimelineStyles = /**\n     * @return {?}\n     */\n    function () { return this._currentEmptyStepKeyframe !== this._currentKeyframe; };\n    /**\n     * @param {?} easing\n     * @return {?}\n     */\n    TimelineBuilder.prototype.applyEmptyStep = /**\n     * @param {?} easing\n     * @return {?}\n     */\n    function (easing) {\n        var _this = this;\n        if (easing) {\n            this._previousKeyframe['easing'] = easing;\n        }\n        // special case for animate(duration):\n        // all missing styles are filled with a `*` value then\n        // if any destination styles are filled in later on the same\n        // keyframe then they will override the overridden styles\n        // We use `_globalTimelineStyles` here because there may be\n        // styles in previous keyframes that are not present in this timeline\n        Object.keys(this._globalTimelineStyles).forEach(function (prop) {\n            _this._backFill[prop] = _this._globalTimelineStyles[prop] || AUTO_STYLE;\n            _this._currentKeyframe[prop] = AUTO_STYLE;\n        });\n        this._currentEmptyStepKeyframe = this._currentKeyframe;\n    };\n    /**\n     * @param {?} input\n     * @param {?} easing\n     * @param {?} errors\n     * @param {?=} options\n     * @return {?}\n     */\n    TimelineBuilder.prototype.setStyles = /**\n     * @param {?} input\n     * @param {?} easing\n     * @param {?} errors\n     * @param {?=} options\n     * @return {?}\n     */\n    function (input, easing, errors, options) {\n        var _this = this;\n        if (easing) {\n            this._previousKeyframe['easing'] = easing;\n        }\n        var /** @type {?} */ params = (options && options.params) || {};\n        var /** @type {?} */ styles = flattenStyles(input, this._globalTimelineStyles);\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ val = interpolateParams(styles[prop], params, errors);\n            _this._pendingStyles[prop] = val;\n            if (!_this._localTimelineStyles.hasOwnProperty(prop)) {\n                _this._backFill[prop] = _this._globalTimelineStyles.hasOwnProperty(prop) ?\n                    _this._globalTimelineStyles[prop] :\n                    AUTO_STYLE;\n            }\n            _this._updateStyle(prop, val);\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.applyStylesToKeyframe = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        var /** @type {?} */ styles = this._pendingStyles;\n        var /** @type {?} */ props = Object.keys(styles);\n        if (props.length == 0)\n            return;\n        this._pendingStyles = {};\n        props.forEach(function (prop) {\n            var /** @type {?} */ val = styles[prop];\n            _this._currentKeyframe[prop] = val;\n        });\n        Object.keys(this._localTimelineStyles).forEach(function (prop) {\n            if (!_this._currentKeyframe.hasOwnProperty(prop)) {\n                _this._currentKeyframe[prop] = _this._localTimelineStyles[prop];\n            }\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.snapshotCurrentStyles = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        Object.keys(this._localTimelineStyles).forEach(function (prop) {\n            var /** @type {?} */ val = _this._localTimelineStyles[prop];\n            _this._pendingStyles[prop] = val;\n            _this._updateStyle(prop, val);\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.getFinalKeyframe = /**\n     * @return {?}\n     */\n    function () { return this._keyframes.get(this.duration); };\n    Object.defineProperty(TimelineBuilder.prototype, \"properties\", {\n        get: /**\n         * @return {?}\n         */\n        function () {\n            var /** @type {?} */ properties = [];\n            for (var /** @type {?} */ prop in this._currentKeyframe) {\n                properties.push(prop);\n            }\n            return properties;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} timeline\n     * @return {?}\n     */\n    TimelineBuilder.prototype.mergeTimelineCollectedStyles = /**\n     * @param {?} timeline\n     * @return {?}\n     */\n    function (timeline) {\n        var _this = this;\n        Object.keys(timeline._styleSummary).forEach(function (prop) {\n            var /** @type {?} */ details0 = _this._styleSummary[prop];\n            var /** @type {?} */ details1 = timeline._styleSummary[prop];\n            if (!details0 || details1.time > details0.time) {\n                _this._updateStyle(prop, details1.value);\n            }\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.buildKeyframes = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        this.applyStylesToKeyframe();\n        var /** @type {?} */ preStyleProps = new Set();\n        var /** @type {?} */ postStyleProps = new Set();\n        var /** @type {?} */ isEmpty = this._keyframes.size === 1 && this.duration === 0;\n        var /** @type {?} */ finalKeyframes = [];\n        this._keyframes.forEach(function (keyframe, time) {\n            var /** @type {?} */ finalKeyframe = copyStyles(keyframe, true);\n            Object.keys(finalKeyframe).forEach(function (prop) {\n                var /** @type {?} */ value = finalKeyframe[prop];\n                if (value == PRE_STYLE) {\n                    preStyleProps.add(prop);\n                }\n                else if (value == AUTO_STYLE) {\n                    postStyleProps.add(prop);\n                }\n            });\n            if (!isEmpty) {\n                finalKeyframe['offset'] = time / _this.duration;\n            }\n            finalKeyframes.push(finalKeyframe);\n        });\n        var /** @type {?} */ preProps = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n        var /** @type {?} */ postProps = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n        // special case for a 0-second animation (which is designed just to place styles onscreen)\n        if (isEmpty) {\n            var /** @type {?} */ kf0 = finalKeyframes[0];\n            var /** @type {?} */ kf1 = copyObj(kf0);\n            kf0['offset'] = 0;\n            kf1['offset'] = 1;\n            finalKeyframes = [kf0, kf1];\n        }\n        return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n    };\n    return TimelineBuilder;\n}());\nexport { TimelineBuilder };\nfunction TimelineBuilder_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TimelineBuilder.prototype.duration;\n    /** @type {?} */\n    TimelineBuilder.prototype.easing;\n    /** @type {?} */\n    TimelineBuilder.prototype._previousKeyframe;\n    /** @type {?} */\n    TimelineBuilder.prototype._currentKeyframe;\n    /** @type {?} */\n    TimelineBuilder.prototype._keyframes;\n    /** @type {?} */\n    TimelineBuilder.prototype._styleSummary;\n    /** @type {?} */\n    TimelineBuilder.prototype._localTimelineStyles;\n    /** @type {?} */\n    TimelineBuilder.prototype._globalTimelineStyles;\n    /** @type {?} */\n    TimelineBuilder.prototype._pendingStyles;\n    /** @type {?} */\n    TimelineBuilder.prototype._backFill;\n    /** @type {?} */\n    TimelineBuilder.prototype._currentEmptyStepKeyframe;\n    /** @type {?} */\n    TimelineBuilder.prototype._driver;\n    /** @type {?} */\n    TimelineBuilder.prototype.element;\n    /** @type {?} */\n    TimelineBuilder.prototype.startTime;\n    /** @type {?} */\n    TimelineBuilder.prototype._elementTimelineStylesLookup;\n}\nvar SubTimelineBuilder = /** @class */ (function (_super) {\n    tslib_1.__extends(SubTimelineBuilder, _super);\n    function SubTimelineBuilder(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe) {\n        if (_stretchStartingKeyframe === void 0) { _stretchStartingKeyframe = false; }\n        var _this = _super.call(this, driver, element, timings.delay) || this;\n        _this.element = element;\n        _this.keyframes = keyframes;\n        _this.preStyleProps = preStyleProps;\n        _this.postStyleProps = postStyleProps;\n        _this._stretchStartingKeyframe = _stretchStartingKeyframe;\n        _this.timings = { duration: timings.duration, delay: timings.delay, easing: timings.easing };\n        return _this;\n    }\n    /**\n     * @return {?}\n     */\n    SubTimelineBuilder.prototype.containsAnimation = /**\n     * @return {?}\n     */\n    function () { return this.keyframes.length > 1; };\n    /**\n     * @return {?}\n     */\n    SubTimelineBuilder.prototype.buildKeyframes = /**\n     * @return {?}\n     */\n    function () {\n        var /** @type {?} */ keyframes = this.keyframes;\n        var _a = this.timings, delay = _a.delay, duration = _a.duration, easing = _a.easing;\n        if (this._stretchStartingKeyframe && delay) {\n            var /** @type {?} */ newKeyframes = [];\n            var /** @type {?} */ totalTime = duration + delay;\n            var /** @type {?} */ startingGap = delay / totalTime;\n            // the original starting keyframe now starts once the delay is done\n            var /** @type {?} */ newFirstKeyframe = copyStyles(keyframes[0], false);\n            newFirstKeyframe['offset'] = 0;\n            newKeyframes.push(newFirstKeyframe);\n            var /** @type {?} */ oldFirstKeyframe = copyStyles(keyframes[0], false);\n            oldFirstKeyframe['offset'] = roundOffset(startingGap);\n            newKeyframes.push(oldFirstKeyframe);\n            /*\n                    When the keyframe is stretched then it means that the delay before the animation\n                    starts is gone. Instead the first keyframe is placed at the start of the animation\n                    and it is then copied to where it starts when the original delay is over. This basically\n                    means nothing animates during that delay, but the styles are still renderered. For this\n                    to work the original offset values that exist in the original keyframes must be \"warped\"\n                    so that they can take the new keyframe + delay into account.\n            \n                    delay=1000, duration=1000, keyframes = 0 .5 1\n            \n                    turns into\n            \n                    delay=0, duration=2000, keyframes = 0 .33 .66 1\n                   */\n            // offsets between 1 ... n -1 are all warped by the keyframe stretch\n            var /** @type {?} */ limit = keyframes.length - 1;\n            for (var /** @type {?} */ i = 1; i <= limit; i++) {\n                var /** @type {?} */ kf = copyStyles(keyframes[i], false);\n                var /** @type {?} */ oldOffset = /** @type {?} */ (kf['offset']);\n                var /** @type {?} */ timeAtKeyframe = delay + oldOffset * duration;\n                kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n                newKeyframes.push(kf);\n            }\n            // the new starting keyframe should be added at the start\n            duration = totalTime;\n            delay = 0;\n            easing = '';\n            keyframes = newKeyframes;\n        }\n        return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n    };\n    return SubTimelineBuilder;\n}(TimelineBuilder));\nfunction SubTimelineBuilder_tsickle_Closure_declarations() {\n    /** @type {?} */\n    SubTimelineBuilder.prototype.timings;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.element;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.keyframes;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.preStyleProps;\n    /** @type {?} */\n    SubTimelineBuilder.prototype.postStyleProps;\n    /** @type {?} */\n    SubTimelineBuilder.prototype._stretchStartingKeyframe;\n}\n/**\n * @param {?} offset\n * @param {?=} decimalPoints\n * @return {?}\n */\nfunction roundOffset(offset, decimalPoints) {\n    if (decimalPoints === void 0) { decimalPoints = 3; }\n    var /** @type {?} */ mult = Math.pow(10, decimalPoints - 1);\n    return Math.round(offset * mult) / mult;\n}\n/**\n * @param {?} input\n * @param {?} allStyles\n * @return {?}\n */\nfunction flattenStyles(input, allStyles) {\n    var /** @type {?} */ styles = {};\n    var /** @type {?} */ allProperties;\n    input.forEach(function (token) {\n        if (token === '*') {\n            allProperties = allProperties || Object.keys(allStyles);\n            allProperties.forEach(function (prop) { styles[prop] = AUTO_STYLE; });\n        }\n        else {\n            copyStyles(/** @type {?} */ (token), false, styles);\n        }\n    });\n    return styles;\n}\n//# sourceMappingURL=animation_timeline_builder.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { ENTER_CLASSNAME, LEAVE_CLASSNAME, normalizeStyles } from '../util';\nimport { buildAnimationAst } from './animation_ast_builder';\nimport { buildAnimationTimelines } from './animation_timeline_builder';\nimport { ElementInstructionMap } from './element_instruction_map';\nvar Animation = /** @class */ (function () {\n    function Animation(_driver, input) {\n        this._driver = _driver;\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ ast = buildAnimationAst(_driver, input, errors);\n        if (errors.length) {\n            var /** @type {?} */ errorMessage = \"animation validation failed:\\n\" + errors.join(\"\\n\");\n            throw new Error(errorMessage);\n        }\n        this._animationAst = ast;\n    }\n    /**\n     * @param {?} element\n     * @param {?} startingStyles\n     * @param {?} destinationStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    Animation.prototype.buildTimelines = /**\n     * @param {?} element\n     * @param {?} startingStyles\n     * @param {?} destinationStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    function (element, startingStyles, destinationStyles, options, subInstructions) {\n        var /** @type {?} */ start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : /** @type {?} */ (startingStyles);\n        var /** @type {?} */ dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : /** @type {?} */ (destinationStyles);\n        var /** @type {?} */ errors = [];\n        subInstructions = subInstructions || new ElementInstructionMap();\n        var /** @type {?} */ result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n        if (errors.length) {\n            var /** @type {?} */ errorMessage = \"animation building failed:\\n\" + errors.join(\"\\n\");\n            throw new Error(errorMessage);\n        }\n        return result;\n    };\n    return Animation;\n}());\nexport { Animation };\nfunction Animation_tsickle_Closure_declarations() {\n    /** @type {?} */\n    Animation.prototype._animationAst;\n    /** @type {?} */\n    Animation.prototype._driver;\n}\n//# sourceMappingURL=animation.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nvar /**\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nAnimationStyleNormalizer = /** @class */ (function () {\n    function AnimationStyleNormalizer() {\n    }\n    return AnimationStyleNormalizer;\n}());\n/**\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport { AnimationStyleNormalizer };\nfunction AnimationStyleNormalizer_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationStyleNormalizer.prototype.normalizePropertyName = function (propertyName, errors) { };\n    /**\n     * @abstract\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationStyleNormalizer.prototype.normalizeStyleValue = function (userProvidedProperty, normalizedProperty, value, errors) { };\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nvar /**\n * \\@experimental Animation support is experimental.\n */\nNoopAnimationStyleNormalizer = /** @class */ (function () {\n    function NoopAnimationStyleNormalizer() {\n    }\n    /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    NoopAnimationStyleNormalizer.prototype.normalizePropertyName = /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    function (propertyName, errors) { return propertyName; };\n    /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    NoopAnimationStyleNormalizer.prototype.normalizeStyleValue = /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    function (userProvidedProperty, normalizedProperty, value, errors) {\n        return /** @type {?} */ (value);\n    };\n    return NoopAnimationStyleNormalizer;\n}());\n/**\n * \\@experimental Animation support is experimental.\n */\nexport { NoopAnimationStyleNormalizer };\n//# sourceMappingURL=animation_style_normalizer.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport * as tslib_1 from \"tslib\";\nimport { dashCaseToCamelCase } from '../../util';\nimport { AnimationStyleNormalizer } from './animation_style_normalizer';\nvar WebAnimationsStyleNormalizer = /** @class */ (function (_super) {\n    tslib_1.__extends(WebAnimationsStyleNormalizer, _super);\n    function WebAnimationsStyleNormalizer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    WebAnimationsStyleNormalizer.prototype.normalizePropertyName = /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    function (propertyName, errors) {\n        return dashCaseToCamelCase(propertyName);\n    };\n    /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    WebAnimationsStyleNormalizer.prototype.normalizeStyleValue = /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    function (userProvidedProperty, normalizedProperty, value, errors) {\n        var /** @type {?} */ unit = '';\n        var /** @type {?} */ strVal = value.toString().trim();\n        if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n            if (typeof value === 'number') {\n                unit = 'px';\n            }\n            else {\n                var /** @type {?} */ valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n                if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n                    errors.push(\"Please provide a CSS unit value for \" + userProvidedProperty + \":\" + value);\n                }\n            }\n        }\n        return strVal + unit;\n    };\n    return WebAnimationsStyleNormalizer;\n}(AnimationStyleNormalizer));\nexport { WebAnimationsStyleNormalizer };\nvar /** @type {?} */ DIMENSIONAL_PROP_MAP = makeBooleanMap('width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n    .split(','));\n/**\n * @param {?} keys\n * @return {?}\n */\nfunction makeBooleanMap(keys) {\n    var /** @type {?} */ map = {};\n    keys.forEach(function (key) { return map[key] = true; });\n    return map;\n}\n//# sourceMappingURL=web_animations_style_normalizer.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @record\n */\nexport function AnimationTransitionInstruction() { }\nfunction AnimationTransitionInstruction_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.element;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.triggerName;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.isRemovalTransition;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.fromState;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.fromStyles;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.toState;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.toStyles;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.timelines;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.queriedElements;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.preStyleProps;\n    /** @type {?} */\n    AnimationTransitionInstruction.prototype.postStyleProps;\n    /** @type {?|undefined} */\n    AnimationTransitionInstruction.prototype.errors;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?} isRemovalTransition\n * @param {?} fromStyles\n * @param {?} toStyles\n * @param {?} timelines\n * @param {?} queriedElements\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?=} errors\n * @return {?}\n */\nexport function createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, errors) {\n    return {\n        type: 0 /* TransitionAnimation */,\n        element: element,\n        triggerName: triggerName,\n        isRemovalTransition: isRemovalTransition,\n        fromState: fromState,\n        fromStyles: fromStyles,\n        toState: toState,\n        toStyles: toStyles,\n        timelines: timelines,\n        queriedElements: queriedElements,\n        preStyleProps: preStyleProps,\n        postStyleProps: postStyleProps,\n        errors: errors\n    };\n}\n//# sourceMappingURL=animation_transition_instruction.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport * as tslib_1 from \"tslib\";\nimport { getOrSetAsInMap } from '../render/shared';\nimport { copyObj, interpolateParams, iteratorToArray } from '../util';\nimport { buildAnimationTimelines } from './animation_timeline_builder';\nimport { createTransitionInstruction } from './animation_transition_instruction';\nvar /** @type {?} */ EMPTY_OBJECT = {};\nvar AnimationTransitionFactory = /** @class */ (function () {\n    function AnimationTransitionFactory(_triggerName, ast, _stateStyles) {\n        this._triggerName = _triggerName;\n        this.ast = ast;\n        this._stateStyles = _stateStyles;\n    }\n    /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    AnimationTransitionFactory.prototype.match = /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    function (currentState, nextState) {\n        return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState);\n    };\n    /**\n     * @param {?} stateName\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationTransitionFactory.prototype.buildStyles = /**\n     * @param {?} stateName\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    function (stateName, params, errors) {\n        var /** @type {?} */ backupStateStyler = this._stateStyles['*'];\n        var /** @type {?} */ stateStyler = this._stateStyles[stateName];\n        var /** @type {?} */ backupStyles = backupStateStyler ? backupStateStyler.buildStyles(params, errors) : {};\n        return stateStyler ? stateStyler.buildStyles(params, errors) : backupStyles;\n    };\n    /**\n     * @param {?} driver\n     * @param {?} element\n     * @param {?} currentState\n     * @param {?} nextState\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?=} currentOptions\n     * @param {?=} nextOptions\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    AnimationTransitionFactory.prototype.build = /**\n     * @param {?} driver\n     * @param {?} element\n     * @param {?} currentState\n     * @param {?} nextState\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?=} currentOptions\n     * @param {?=} nextOptions\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    function (driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions) {\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n        var /** @type {?} */ currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n        var /** @type {?} */ currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n        var /** @type {?} */ nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n        var /** @type {?} */ nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n        var /** @type {?} */ queriedElements = new Set();\n        var /** @type {?} */ preStyleMap = new Map();\n        var /** @type {?} */ postStyleMap = new Map();\n        var /** @type {?} */ isRemoval = nextState === 'void';\n        var /** @type {?} */ animationOptions = { params: tslib_1.__assign({}, transitionAnimationParams, nextAnimationParams) };\n        var /** @type {?} */ timelines = buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n        if (errors.length) {\n            return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, errors);\n        }\n        timelines.forEach(function (tl) {\n            var /** @type {?} */ elm = tl.element;\n            var /** @type {?} */ preProps = getOrSetAsInMap(preStyleMap, elm, {});\n            tl.preStyleProps.forEach(function (prop) { return preProps[prop] = true; });\n            var /** @type {?} */ postProps = getOrSetAsInMap(postStyleMap, elm, {});\n            tl.postStyleProps.forEach(function (prop) { return postProps[prop] = true; });\n            if (elm !== element) {\n                queriedElements.add(elm);\n            }\n        });\n        var /** @type {?} */ queriedElementsList = iteratorToArray(queriedElements.values());\n        return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap, postStyleMap);\n    };\n    return AnimationTransitionFactory;\n}());\nexport { AnimationTransitionFactory };\nfunction AnimationTransitionFactory_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTransitionFactory.prototype._triggerName;\n    /** @type {?} */\n    AnimationTransitionFactory.prototype.ast;\n    /** @type {?} */\n    AnimationTransitionFactory.prototype._stateStyles;\n}\n/**\n * @param {?} matchFns\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState) {\n    return matchFns.some(function (fn) { return fn(currentState, nextState); });\n}\nvar AnimationStateStyles = /** @class */ (function () {\n    function AnimationStateStyles(styles, defaultParams) {\n        this.styles = styles;\n        this.defaultParams = defaultParams;\n    }\n    /**\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationStateStyles.prototype.buildStyles = /**\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    function (params, errors) {\n        var /** @type {?} */ finalStyles = {};\n        var /** @type {?} */ combinedParams = copyObj(this.defaultParams);\n        Object.keys(params).forEach(function (key) {\n            var /** @type {?} */ value = params[key];\n            if (value != null) {\n                combinedParams[key] = value;\n            }\n        });\n        this.styles.styles.forEach(function (value) {\n            if (typeof value !== 'string') {\n                var /** @type {?} */ styleObj_1 = /** @type {?} */ (value);\n                Object.keys(styleObj_1).forEach(function (prop) {\n                    var /** @type {?} */ val = styleObj_1[prop];\n                    if (val.length > 1) {\n                        val = interpolateParams(val, combinedParams, errors);\n                    }\n                    finalStyles[prop] = val;\n                });\n            }\n        });\n        return finalStyles;\n    };\n    return AnimationStateStyles;\n}());\nexport { AnimationStateStyles };\nfunction AnimationStateStyles_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationStateStyles.prototype.styles;\n    /** @type {?} */\n    AnimationStateStyles.prototype.defaultParams;\n}\n//# sourceMappingURL=animation_transition_factory.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AnimationStateStyles, AnimationTransitionFactory } from './animation_transition_factory';\n/**\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nexport function buildTrigger(name, ast) {\n    return new AnimationTrigger(name, ast);\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nvar /**\n * \\@experimental Animation support is experimental.\n */\nAnimationTrigger = /** @class */ (function () {\n    function AnimationTrigger(name, ast) {\n        var _this = this;\n        this.name = name;\n        this.ast = ast;\n        this.transitionFactories = [];\n        this.states = {};\n        ast.states.forEach(function (ast) {\n            var /** @type {?} */ defaultParams = (ast.options && ast.options.params) || {};\n            _this.states[ast.name] = new AnimationStateStyles(ast.style, defaultParams);\n        });\n        balanceProperties(this.states, 'true', '1');\n        balanceProperties(this.states, 'false', '0');\n        ast.transitions.forEach(function (ast) {\n            _this.transitionFactories.push(new AnimationTransitionFactory(name, ast, _this.states));\n        });\n        this.fallbackTransition = createFallbackTransition(name, this.states);\n    }\n    Object.defineProperty(AnimationTrigger.prototype, \"containsQueries\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.ast.queryCount > 0; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    AnimationTrigger.prototype.matchTransition = /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    function (currentState, nextState) {\n        var /** @type {?} */ entry = this.transitionFactories.find(function (f) { return f.match(currentState, nextState); });\n        return entry || null;\n    };\n    /**\n     * @param {?} currentState\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationTrigger.prototype.matchStyles = /**\n     * @param {?} currentState\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    function (currentState, params, errors) {\n        return this.fallbackTransition.buildStyles(currentState, params, errors);\n    };\n    return AnimationTrigger;\n}());\n/**\n * \\@experimental Animation support is experimental.\n */\nexport { AnimationTrigger };\nfunction AnimationTrigger_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTrigger.prototype.transitionFactories;\n    /** @type {?} */\n    AnimationTrigger.prototype.fallbackTransition;\n    /** @type {?} */\n    AnimationTrigger.prototype.states;\n    /** @type {?} */\n    AnimationTrigger.prototype.name;\n    /** @type {?} */\n    AnimationTrigger.prototype.ast;\n}\n/**\n * @param {?} triggerName\n * @param {?} states\n * @return {?}\n */\nfunction createFallbackTransition(triggerName, states) {\n    var /** @type {?} */ matchers = [function (fromState, toState) { return true; }];\n    var /** @type {?} */ animation = { type: 2 /* Sequence */, steps: [], options: null };\n    var /** @type {?} */ transition = {\n        type: 1 /* Transition */,\n        animation: animation,\n        matchers: matchers,\n        options: null,\n        queryCount: 0,\n        depCount: 0\n    };\n    return new AnimationTransitionFactory(triggerName, transition, states);\n}\n/**\n * @param {?} obj\n * @param {?} key1\n * @param {?} key2\n * @return {?}\n */\nfunction balanceProperties(obj, key1, key2) {\n    if (obj.hasOwnProperty(key1)) {\n        if (!obj.hasOwnProperty(key2)) {\n            obj[key2] = obj[key1];\n        }\n    }\n    else if (obj.hasOwnProperty(key2)) {\n        obj[key1] = obj[key2];\n    }\n}\n//# sourceMappingURL=animation_trigger.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE } from '@angular/animations';\nimport { buildAnimationAst } from '../dsl/animation_ast_builder';\nimport { buildAnimationTimelines } from '../dsl/animation_timeline_builder';\nimport { ElementInstructionMap } from '../dsl/element_instruction_map';\nimport { ENTER_CLASSNAME, LEAVE_CLASSNAME } from '../util';\nimport { getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer } from './shared';\nvar /** @type {?} */ EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nvar TimelineAnimationEngine = /** @class */ (function () {\n    function TimelineAnimationEngine(_driver, _normalizer) {\n        this._driver = _driver;\n        this._normalizer = _normalizer;\n        this._animations = {};\n        this._playersById = {};\n        this.players = [];\n    }\n    /**\n     * @param {?} id\n     * @param {?} metadata\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.register = /**\n     * @param {?} id\n     * @param {?} metadata\n     * @return {?}\n     */\n    function (id, metadata) {\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ ast = buildAnimationAst(this._driver, metadata, errors);\n        if (errors.length) {\n            throw new Error(\"Unable to build the animation due to the following errors: \" + errors.join(\"\\n\"));\n        }\n        else {\n            this._animations[id] = ast;\n        }\n    };\n    /**\n     * @param {?} i\n     * @param {?} preStyles\n     * @param {?=} postStyles\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype._buildPlayer = /**\n     * @param {?} i\n     * @param {?} preStyles\n     * @param {?=} postStyles\n     * @return {?}\n     */\n    function (i, preStyles, postStyles) {\n        var /** @type {?} */ element = i.element;\n        var /** @type {?} */ keyframes = normalizeKeyframes(this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n        return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, []);\n    };\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?=} options\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.create = /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?=} options\n     * @return {?}\n     */\n    function (id, element, options) {\n        var _this = this;\n        if (options === void 0) { options = {}; }\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ ast = this._animations[id];\n        var /** @type {?} */ instructions;\n        var /** @type {?} */ autoStylesMap = new Map();\n        if (ast) {\n            instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n            instructions.forEach(function (inst) {\n                var /** @type {?} */ styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n                inst.postStyleProps.forEach(function (prop) { return styles[prop] = null; });\n            });\n        }\n        else {\n            errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n            instructions = [];\n        }\n        if (errors.length) {\n            throw new Error(\"Unable to create the animation due to the following errors: \" + errors.join(\"\\n\"));\n        }\n        autoStylesMap.forEach(function (styles, element) {\n            Object.keys(styles).forEach(function (prop) { styles[prop] = _this._driver.computeStyle(element, prop, AUTO_STYLE); });\n        });\n        var /** @type {?} */ players = instructions.map(function (i) {\n            var /** @type {?} */ styles = autoStylesMap.get(i.element);\n            return _this._buildPlayer(i, {}, styles);\n        });\n        var /** @type {?} */ player = optimizeGroupPlayer(players);\n        this._playersById[id] = player;\n        player.onDestroy(function () { return _this.destroy(id); });\n        this.players.push(player);\n        return player;\n    };\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.destroy = /**\n     * @param {?} id\n     * @return {?}\n     */\n    function (id) {\n        var /** @type {?} */ player = this._getPlayer(id);\n        player.destroy();\n        delete this._playersById[id];\n        var /** @type {?} */ index = this.players.indexOf(player);\n        if (index >= 0) {\n            this.players.splice(index, 1);\n        }\n    };\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype._getPlayer = /**\n     * @param {?} id\n     * @return {?}\n     */\n    function (id) {\n        var /** @type {?} */ player = this._playersById[id];\n        if (!player) {\n            throw new Error(\"Unable to find the timeline player referenced by \" + id);\n        }\n        return player;\n    };\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} callback\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.listen = /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} callback\n     * @return {?}\n     */\n    function (id, element, eventName, callback) {\n        // triggerName, fromState, toState are all ignored for timeline animations\n        var /** @type {?} */ baseEvent = makeAnimationEvent(element, '', '', '');\n        listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n        return function () { };\n    };\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} command\n     * @param {?} args\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.command = /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} command\n     * @param {?} args\n     * @return {?}\n     */\n    function (id, element, command, args) {\n        if (command == 'register') {\n            this.register(id, /** @type {?} */ (args[0]));\n            return;\n        }\n        if (command == 'create') {\n            var /** @type {?} */ options = /** @type {?} */ ((args[0] || {}));\n            this.create(id, element, options);\n            return;\n        }\n        var /** @type {?} */ player = this._getPlayer(id);\n        switch (command) {\n            case 'play':\n                player.play();\n                break;\n            case 'pause':\n                player.pause();\n                break;\n            case 'reset':\n                player.reset();\n                break;\n            case 'restart':\n                player.restart();\n                break;\n            case 'finish':\n                player.finish();\n                break;\n            case 'init':\n                player.init();\n                break;\n            case 'setPosition':\n                player.setPosition(parseFloat(/** @type {?} */ (args[0])));\n                break;\n            case 'destroy':\n                this.destroy(id);\n                break;\n        }\n    };\n    return TimelineAnimationEngine;\n}());\nexport { TimelineAnimationEngine };\nfunction TimelineAnimationEngine_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._animations;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._playersById;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype.players;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._driver;\n    /** @type {?} */\n    TimelineAnimationEngine.prototype._normalizer;\n}\n//# sourceMappingURL=timeline_animation_engine.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport * as tslib_1 from \"tslib\";\nimport { AUTO_STYLE, NoopAnimationPlayer, ɵAnimationGroupPlayer as AnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\nimport { ElementInstructionMap } from '../dsl/element_instruction_map';\nimport { ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, copyObj, eraseStyles, setStyles } from '../util';\nimport { getBodyNode, getOrSetAsInMap, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer } from './shared';\nvar /** @type {?} */ QUEUED_CLASSNAME = 'ng-animate-queued';\nvar /** @type {?} */ QUEUED_SELECTOR = '.ng-animate-queued';\nvar /** @type {?} */ DISABLED_CLASSNAME = 'ng-animate-disabled';\nvar /** @type {?} */ DISABLED_SELECTOR = '.ng-animate-disabled';\nvar /** @type {?} */ STAR_CLASSNAME = 'ng-star-inserted';\nvar /** @type {?} */ STAR_SELECTOR = '.ng-star-inserted';\nvar /** @type {?} */ EMPTY_PLAYER_ARRAY = [];\nvar /** @type {?} */ NULL_REMOVAL_STATE = {\n    namespaceId: '',\n    setForRemoval: null,\n    hasAnimation: false,\n    removedBeforeQueried: false\n};\nvar /** @type {?} */ NULL_REMOVED_QUERIED_STATE = {\n    namespaceId: '',\n    setForRemoval: null,\n    hasAnimation: false,\n    removedBeforeQueried: true\n};\n/**\n * @record\n */\nfunction TriggerListener() { }\nfunction TriggerListener_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TriggerListener.prototype.name;\n    /** @type {?} */\n    TriggerListener.prototype.phase;\n    /** @type {?} */\n    TriggerListener.prototype.callback;\n}\n/**\n * @record\n */\nexport function QueueInstruction() { }\nfunction QueueInstruction_tsickle_Closure_declarations() {\n    /** @type {?} */\n    QueueInstruction.prototype.element;\n    /** @type {?} */\n    QueueInstruction.prototype.triggerName;\n    /** @type {?} */\n    QueueInstruction.prototype.fromState;\n    /** @type {?} */\n    QueueInstruction.prototype.toState;\n    /** @type {?} */\n    QueueInstruction.prototype.transition;\n    /** @type {?} */\n    QueueInstruction.prototype.player;\n    /** @type {?} */\n    QueueInstruction.prototype.isFallbackTransition;\n}\nexport var /** @type {?} */ REMOVAL_FLAG = '__ng_removed';\n/**\n * @record\n */\nexport function ElementAnimationState() { }\nfunction ElementAnimationState_tsickle_Closure_declarations() {\n    /** @type {?} */\n    ElementAnimationState.prototype.setForRemoval;\n    /** @type {?} */\n    ElementAnimationState.prototype.hasAnimation;\n    /** @type {?} */\n    ElementAnimationState.prototype.namespaceId;\n    /** @type {?} */\n    ElementAnimationState.prototype.removedBeforeQueried;\n}\nvar StateValue = /** @class */ (function () {\n    function StateValue(input, namespaceId) {\n        if (namespaceId === void 0) { namespaceId = ''; }\n        this.namespaceId = namespaceId;\n        var /** @type {?} */ isObj = input && input.hasOwnProperty('value');\n        var /** @type {?} */ value = isObj ? input['value'] : input;\n        this.value = normalizeTriggerValue(value);\n        if (isObj) {\n            var /** @type {?} */ options = copyObj(/** @type {?} */ (input));\n            delete options['value'];\n            this.options = /** @type {?} */ (options);\n        }\n        else {\n            this.options = {};\n        }\n        if (!this.options.params) {\n            this.options.params = {};\n        }\n    }\n    Object.defineProperty(StateValue.prototype, \"params\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return /** @type {?} */ (this.options.params); },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} options\n     * @return {?}\n     */\n    StateValue.prototype.absorbOptions = /**\n     * @param {?} options\n     * @return {?}\n     */\n    function (options) {\n        var /** @type {?} */ newParams = options.params;\n        if (newParams) {\n            var /** @type {?} */ oldParams_1 = /** @type {?} */ ((this.options.params));\n            Object.keys(newParams).forEach(function (prop) {\n                if (oldParams_1[prop] == null) {\n                    oldParams_1[prop] = newParams[prop];\n                }\n            });\n        }\n    };\n    return StateValue;\n}());\nexport { StateValue };\nfunction StateValue_tsickle_Closure_declarations() {\n    /** @type {?} */\n    StateValue.prototype.value;\n    /** @type {?} */\n    StateValue.prototype.options;\n    /** @type {?} */\n    StateValue.prototype.namespaceId;\n}\nexport var /** @type {?} */ VOID_VALUE = 'void';\nexport var /** @type {?} */ DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nexport var /** @type {?} */ DELETED_STATE_VALUE = new StateValue('DELETED');\nvar AnimationTransitionNamespace = /** @class */ (function () {\n    function AnimationTransitionNamespace(id, hostElement, _engine) {\n        this.id = id;\n        this.hostElement = hostElement;\n        this._engine = _engine;\n        this.players = [];\n        this._triggers = {};\n        this._queue = [];\n        this._elementListeners = new Map();\n        this._hostClassName = 'ng-tns-' + id;\n        addClass(hostElement, this._hostClassName);\n    }\n    /**\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.listen = /**\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    function (element, name, phase, callback) {\n        var _this = this;\n        if (!this._triggers.hasOwnProperty(name)) {\n            throw new Error(\"Unable to listen on the animation trigger event \\\"\" + phase + \"\\\" because the animation trigger \\\"\" + name + \"\\\" doesn't exist!\");\n        }\n        if (phase == null || phase.length == 0) {\n            throw new Error(\"Unable to listen on the animation trigger \\\"\" + name + \"\\\" because the provided event is undefined!\");\n        }\n        if (!isTriggerEventValid(phase)) {\n            throw new Error(\"The provided animation trigger event \\\"\" + phase + \"\\\" for the animation trigger \\\"\" + name + \"\\\" is not supported!\");\n        }\n        var /** @type {?} */ listeners = getOrSetAsInMap(this._elementListeners, element, []);\n        var /** @type {?} */ data = { name: name, phase: phase, callback: callback };\n        listeners.push(data);\n        var /** @type {?} */ triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n        if (!triggersWithStates.hasOwnProperty(name)) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n            triggersWithStates[name] = DEFAULT_STATE_VALUE;\n        }\n        return function () {\n            // the event listener is removed AFTER the flush has occurred such\n            // that leave animations callbacks can fire (otherwise if the node\n            // is removed in between then the listeners would be deregistered)\n            // the event listener is removed AFTER the flush has occurred such\n            // that leave animations callbacks can fire (otherwise if the node\n            // is removed in between then the listeners would be deregistered)\n            _this._engine.afterFlush(function () {\n                var /** @type {?} */ index = listeners.indexOf(data);\n                if (index >= 0) {\n                    listeners.splice(index, 1);\n                }\n                if (!_this._triggers[name]) {\n                    delete triggersWithStates[name];\n                }\n            });\n        };\n    };\n    /**\n     * @param {?} name\n     * @param {?} ast\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.register = /**\n     * @param {?} name\n     * @param {?} ast\n     * @return {?}\n     */\n    function (name, ast) {\n        if (this._triggers[name]) {\n            // throw\n            return false;\n        }\n        else {\n            this._triggers[name] = ast;\n            return true;\n        }\n    };\n    /**\n     * @param {?} name\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype._getTrigger = /**\n     * @param {?} name\n     * @return {?}\n     */\n    function (name) {\n        var /** @type {?} */ trigger = this._triggers[name];\n        if (!trigger) {\n            throw new Error(\"The provided animation trigger \\\"\" + name + \"\\\" has not been registered!\");\n        }\n        return trigger;\n    };\n    /**\n     * @param {?} element\n     * @param {?} triggerName\n     * @param {?} value\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.trigger = /**\n     * @param {?} element\n     * @param {?} triggerName\n     * @param {?} value\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    function (element, triggerName, value, defaultToFallback) {\n        var _this = this;\n        if (defaultToFallback === void 0) { defaultToFallback = true; }\n        var /** @type {?} */ trigger = this._getTrigger(triggerName);\n        var /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        var /** @type {?} */ triggersWithStates = this._engine.statesByElement.get(element);\n        if (!triggersWithStates) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n            this._engine.statesByElement.set(element, triggersWithStates = {});\n        }\n        var /** @type {?} */ fromState = triggersWithStates[triggerName];\n        var /** @type {?} */ toState = new StateValue(value, this.id);\n        var /** @type {?} */ isObj = value && value.hasOwnProperty('value');\n        if (!isObj && fromState) {\n            toState.absorbOptions(fromState.options);\n        }\n        triggersWithStates[triggerName] = toState;\n        if (!fromState) {\n            fromState = DEFAULT_STATE_VALUE;\n        }\n        else if (fromState === DELETED_STATE_VALUE) {\n            return player;\n        }\n        var /** @type {?} */ isRemoval = toState.value === VOID_VALUE;\n        // normally this isn't reached by here, however, if an object expression\n        // is passed in then it may be a new object each time. Comparing the value\n        // is important since that will stay the same despite there being a new object.\n        // The removal arc here is special cased because the same element is triggered\n        // twice in the event that it contains animations on the outer/inner portions\n        // of the host container\n        if (!isRemoval && fromState.value === toState.value) {\n            // this means that despite the value not changing, some inner params\n            // have changed which means that the animation final styles need to be applied\n            if (!objEquals(fromState.params, toState.params)) {\n                var /** @type {?} */ errors = [];\n                var /** @type {?} */ fromStyles_1 = trigger.matchStyles(fromState.value, fromState.params, errors);\n                var /** @type {?} */ toStyles_1 = trigger.matchStyles(toState.value, toState.params, errors);\n                if (errors.length) {\n                    this._engine.reportError(errors);\n                }\n                else {\n                    this._engine.afterFlush(function () {\n                        eraseStyles(element, fromStyles_1);\n                        setStyles(element, toStyles_1);\n                    });\n                }\n            }\n            return;\n        }\n        var /** @type {?} */ playersOnElement = getOrSetAsInMap(this._engine.playersByElement, element, []);\n        playersOnElement.forEach(function (player) {\n            // only remove the player if it is queued on the EXACT same trigger/namespace\n            // we only also deal with queued players here because if the animation has\n            // started then we want to keep the player alive until the flush happens\n            // (which is where the previousPlayers are passed into the new palyer)\n            if (player.namespaceId == _this.id && player.triggerName == triggerName && player.queued) {\n                player.destroy();\n            }\n        });\n        var /** @type {?} */ transition = trigger.matchTransition(fromState.value, toState.value);\n        var /** @type {?} */ isFallbackTransition = false;\n        if (!transition) {\n            if (!defaultToFallback)\n                return;\n            transition = trigger.fallbackTransition;\n            isFallbackTransition = true;\n        }\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({ element: element, triggerName: triggerName, transition: transition, fromState: fromState, toState: toState, player: player, isFallbackTransition: isFallbackTransition });\n        if (!isFallbackTransition) {\n            addClass(element, QUEUED_CLASSNAME);\n            player.onStart(function () { removeClass(element, QUEUED_CLASSNAME); });\n        }\n        player.onDone(function () {\n            var /** @type {?} */ index = _this.players.indexOf(player);\n            if (index >= 0) {\n                _this.players.splice(index, 1);\n            }\n            var /** @type {?} */ players = _this._engine.playersByElement.get(element);\n            if (players) {\n                var /** @type {?} */ index_1 = players.indexOf(player);\n                if (index_1 >= 0) {\n                    players.splice(index_1, 1);\n                }\n            }\n        });\n        this.players.push(player);\n        playersOnElement.push(player);\n        return player;\n    };\n    /**\n     * @param {?} name\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.deregister = /**\n     * @param {?} name\n     * @return {?}\n     */\n    function (name) {\n        var _this = this;\n        delete this._triggers[name];\n        this._engine.statesByElement.forEach(function (stateMap, element) { delete stateMap[name]; });\n        this._elementListeners.forEach(function (listeners, element) {\n            _this._elementListeners.set(element, listeners.filter(function (entry) { return entry.name != name; }));\n        });\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.clearElementCache = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        this._engine.statesByElement.delete(element);\n        this._elementListeners.delete(element);\n        var /** @type {?} */ elementPlayers = this._engine.playersByElement.get(element);\n        if (elementPlayers) {\n            elementPlayers.forEach(function (player) { return player.destroy(); });\n            this._engine.playersByElement.delete(element);\n        }\n    };\n    /**\n     * @param {?} rootElement\n     * @param {?} context\n     * @param {?=} animate\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype._signalRemovalForInnerTriggers = /**\n     * @param {?} rootElement\n     * @param {?} context\n     * @param {?=} animate\n     * @return {?}\n     */\n    function (rootElement, context, animate) {\n        var _this = this;\n        if (animate === void 0) { animate = false; }\n        // emulate a leave animation for all inner nodes within this node.\n        // If there are no animations found for any of the nodes then clear the cache\n        // for the element.\n        this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(function (elm) {\n            // this means that an inner remove() operation has already kicked off\n            // the animation on this element...\n            if (elm[REMOVAL_FLAG])\n                return;\n            var /** @type {?} */ namespaces = _this._engine.fetchNamespacesByElement(elm);\n            if (namespaces.size) {\n                namespaces.forEach(function (ns) { return ns.triggerLeaveAnimation(elm, context, false, true); });\n            }\n            else {\n                _this.clearElementCache(elm);\n            }\n        });\n    };\n    /**\n     * @param {?} element\n     * @param {?} context\n     * @param {?=} destroyAfterComplete\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.triggerLeaveAnimation = /**\n     * @param {?} element\n     * @param {?} context\n     * @param {?=} destroyAfterComplete\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    function (element, context, destroyAfterComplete, defaultToFallback) {\n        var _this = this;\n        var /** @type {?} */ triggerStates = this._engine.statesByElement.get(element);\n        if (triggerStates) {\n            var /** @type {?} */ players_1 = [];\n            Object.keys(triggerStates).forEach(function (triggerName) {\n                // this check is here in the event that an element is removed\n                // twice (both on the host level and the component level)\n                if (_this._triggers[triggerName]) {\n                    var /** @type {?} */ player = _this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n                    if (player) {\n                        players_1.push(player);\n                    }\n                }\n            });\n            if (players_1.length) {\n                this._engine.markElementAsRemoved(this.id, element, true, context);\n                if (destroyAfterComplete) {\n                    optimizeGroupPlayer(players_1).onDone(function () { return _this._engine.processLeaveNode(element); });\n                }\n                return true;\n            }\n        }\n        return false;\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.prepareLeaveAnimationListeners = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var _this = this;\n        var /** @type {?} */ listeners = this._elementListeners.get(element);\n        if (listeners) {\n            var /** @type {?} */ visitedTriggers_1 = new Set();\n            listeners.forEach(function (listener) {\n                var /** @type {?} */ triggerName = listener.name;\n                if (visitedTriggers_1.has(triggerName))\n                    return;\n                visitedTriggers_1.add(triggerName);\n                var /** @type {?} */ trigger = _this._triggers[triggerName];\n                var /** @type {?} */ transition = trigger.fallbackTransition;\n                var /** @type {?} */ elementStates = /** @type {?} */ ((_this._engine.statesByElement.get(element)));\n                var /** @type {?} */ fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n                var /** @type {?} */ toState = new StateValue(VOID_VALUE);\n                var /** @type {?} */ player = new TransitionAnimationPlayer(_this.id, triggerName, element);\n                _this._engine.totalQueuedPlayers++;\n                _this._queue.push({\n                    element: element,\n                    triggerName: triggerName,\n                    transition: transition,\n                    fromState: fromState,\n                    toState: toState,\n                    player: player,\n                    isFallbackTransition: true\n                });\n            });\n        }\n    };\n    /**\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.removeNode = /**\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (element, context) {\n        var _this = this;\n        var /** @type {?} */ engine = this._engine;\n        if (element.childElementCount) {\n            this._signalRemovalForInnerTriggers(element, context, true);\n        }\n        // this means that a * => VOID animation was detected and kicked off\n        if (this.triggerLeaveAnimation(element, context, true))\n            return;\n        // find the player that is animating and make sure that the\n        // removal is delayed until that player has completed\n        var /** @type {?} */ containsPotentialParentTransition = false;\n        if (engine.totalAnimations) {\n            var /** @type {?} */ currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n            // when this `if statement` does not continue forward it means that\n            // a previous animation query has selected the current element and\n            // is animating it. In this situation want to continue fowards and\n            // allow the element to be queued up for animation later.\n            if (currentPlayers && currentPlayers.length) {\n                containsPotentialParentTransition = true;\n            }\n            else {\n                var /** @type {?} */ parent_1 = element;\n                while (parent_1 = parent_1.parentNode) {\n                    var /** @type {?} */ triggers = engine.statesByElement.get(parent_1);\n                    if (triggers) {\n                        containsPotentialParentTransition = true;\n                        break;\n                    }\n                }\n            }\n        }\n        // at this stage we know that the element will either get removed\n        // during flush or will be picked up by a parent query. Either way\n        // we need to fire the listeners for this element when it DOES get\n        // removed (once the query parent animation is done or after flush)\n        this.prepareLeaveAnimationListeners(element);\n        // whether or not a parent has an animation we need to delay the deferral of the leave\n        // operation until we have more information (which we do after flush() has been called)\n        if (containsPotentialParentTransition) {\n            engine.markElementAsRemoved(this.id, element, false, context);\n        }\n        else {\n            // we do this after the flush has occurred such\n            // that the callbacks can be fired\n            engine.afterFlush(function () { return _this.clearElementCache(element); });\n            engine.destroyInnerAnimations(element);\n            engine._onRemovalComplete(element, context);\n        }\n    };\n    /**\n     * @param {?} element\n     * @param {?} parent\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.insertNode = /**\n     * @param {?} element\n     * @param {?} parent\n     * @return {?}\n     */\n    function (element, parent) { addClass(element, this._hostClassName); };\n    /**\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.drainQueuedTransitions = /**\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    function (microtaskId) {\n        var _this = this;\n        var /** @type {?} */ instructions = [];\n        this._queue.forEach(function (entry) {\n            var /** @type {?} */ player = entry.player;\n            if (player.destroyed)\n                return;\n            var /** @type {?} */ element = entry.element;\n            var /** @type {?} */ listeners = _this._elementListeners.get(element);\n            if (listeners) {\n                listeners.forEach(function (listener) {\n                    if (listener.name == entry.triggerName) {\n                        var /** @type {?} */ baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n                        (/** @type {?} */ (baseEvent))['_data'] = microtaskId;\n                        listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n                    }\n                });\n            }\n            if (player.markedForDestroy) {\n                _this._engine.afterFlush(function () {\n                    // now we can destroy the element properly since the event listeners have\n                    // been bound to the player\n                    player.destroy();\n                });\n            }\n            else {\n                instructions.push(entry);\n            }\n        });\n        this._queue = [];\n        return instructions.sort(function (a, b) {\n            // if depCount == 0 them move to front\n            // otherwise if a contains b then move back\n            var /** @type {?} */ d0 = a.transition.ast.depCount;\n            var /** @type {?} */ d1 = b.transition.ast.depCount;\n            if (d0 == 0 || d1 == 0) {\n                return d0 - d1;\n            }\n            return _this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n        });\n    };\n    /**\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.destroy = /**\n     * @param {?} context\n     * @return {?}\n     */\n    function (context) {\n        this.players.forEach(function (p) { return p.destroy(); });\n        this._signalRemovalForInnerTriggers(this.hostElement, context);\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.elementContainsData = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ containsData = false;\n        if (this._elementListeners.has(element))\n            containsData = true;\n        containsData =\n            (this._queue.find(function (entry) { return entry.element === element; }) ? true : false) || containsData;\n        return containsData;\n    };\n    return AnimationTransitionNamespace;\n}());\nexport { AnimationTransitionNamespace };\nfunction AnimationTransitionNamespace_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype.players;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._triggers;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._queue;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._elementListeners;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._hostClassName;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype.id;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype.hostElement;\n    /** @type {?} */\n    AnimationTransitionNamespace.prototype._engine;\n}\n/**\n * @record\n */\nexport function QueuedTransition() { }\nfunction QueuedTransition_tsickle_Closure_declarations() {\n    /** @type {?} */\n    QueuedTransition.prototype.element;\n    /** @type {?} */\n    QueuedTransition.prototype.instruction;\n    /** @type {?} */\n    QueuedTransition.prototype.player;\n}\nvar TransitionAnimationEngine = /** @class */ (function () {\n    function TransitionAnimationEngine(driver, _normalizer) {\n        this.driver = driver;\n        this._normalizer = _normalizer;\n        this.players = [];\n        this.newHostElements = new Map();\n        this.playersByElement = new Map();\n        this.playersByQueriedElement = new Map();\n        this.statesByElement = new Map();\n        this.disabledNodes = new Set();\n        this.totalAnimations = 0;\n        this.totalQueuedPlayers = 0;\n        this._namespaceLookup = {};\n        this._namespaceList = [];\n        this._flushFns = [];\n        this._whenQuietFns = [];\n        this.namespacesByHostElement = new Map();\n        this.collectedEnterElements = [];\n        this.collectedLeaveElements = [];\n        this.onRemovalComplete = function (element, context) { };\n    }\n    /** @internal */\n    /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._onRemovalComplete = /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (element, context) { this.onRemovalComplete(element, context); };\n    Object.defineProperty(TransitionAnimationEngine.prototype, \"queuedPlayers\", {\n        get: /**\n         * @return {?}\n         */\n        function () {\n            var /** @type {?} */ players = [];\n            this._namespaceList.forEach(function (ns) {\n                ns.players.forEach(function (player) {\n                    if (player.queued) {\n                        players.push(player);\n                    }\n                });\n            });\n            return players;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.createNamespace = /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (namespaceId, hostElement) {\n        var /** @type {?} */ ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n        if (hostElement.parentNode) {\n            this._balanceNamespaceList(ns, hostElement);\n        }\n        else {\n            // defer this later until flush during when the host element has\n            // been inserted so that we know exactly where to place it in\n            // the namespace list\n            this.newHostElements.set(hostElement, ns);\n            // given that this host element is apart of the animation code, it\n            // may or may not be inserted by a parent node that is an of an\n            // animation renderer type. If this happens then we can still have\n            // access to this item when we query for :enter nodes. If the parent\n            // is a renderer then the set data-structure will normalize the entry\n            this.collectEnterElement(hostElement);\n        }\n        return this._namespaceLookup[namespaceId] = ns;\n    };\n    /**\n     * @param {?} ns\n     * @param {?} hostElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._balanceNamespaceList = /**\n     * @param {?} ns\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (ns, hostElement) {\n        var /** @type {?} */ limit = this._namespaceList.length - 1;\n        if (limit >= 0) {\n            var /** @type {?} */ found = false;\n            for (var /** @type {?} */ i = limit; i >= 0; i--) {\n                var /** @type {?} */ nextNamespace = this._namespaceList[i];\n                if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n                    this._namespaceList.splice(i + 1, 0, ns);\n                    found = true;\n                    break;\n                }\n            }\n            if (!found) {\n                this._namespaceList.splice(0, 0, ns);\n            }\n        }\n        else {\n            this._namespaceList.push(ns);\n        }\n        this.namespacesByHostElement.set(hostElement, ns);\n        return ns;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.register = /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (namespaceId, hostElement) {\n        var /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n        if (!ns) {\n            ns = this.createNamespace(namespaceId, hostElement);\n        }\n        return ns;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} name\n     * @param {?} trigger\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.registerTrigger = /**\n     * @param {?} namespaceId\n     * @param {?} name\n     * @param {?} trigger\n     * @return {?}\n     */\n    function (namespaceId, name, trigger) {\n        var /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n        if (ns && ns.register(name, trigger)) {\n            this.totalAnimations++;\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.destroy = /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, context) {\n        var _this = this;\n        if (!namespaceId)\n            return;\n        var /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n        this.afterFlush(function () {\n            _this.namespacesByHostElement.delete(ns.hostElement);\n            delete _this._namespaceLookup[namespaceId];\n            var /** @type {?} */ index = _this._namespaceList.indexOf(ns);\n            if (index >= 0) {\n                _this._namespaceList.splice(index, 1);\n            }\n        });\n        this.afterFlushAnimationsDone(function () { return ns.destroy(context); });\n    };\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._fetchNamespace = /**\n     * @param {?} id\n     * @return {?}\n     */\n    function (id) { return this._namespaceLookup[id]; };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.fetchNamespacesByElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        // normally there should only be one namespace per element, however\n        // if @triggers are placed on both the component element and then\n        // its host element (within the component code) then there will be\n        // two namespaces returned. We use a set here to simply the dedupe\n        // of namespaces incase there are multiple triggers both the elm and host\n        var /** @type {?} */ namespaces = new Set();\n        var /** @type {?} */ elementStates = this.statesByElement.get(element);\n        if (elementStates) {\n            var /** @type {?} */ keys = Object.keys(elementStates);\n            for (var /** @type {?} */ i = 0; i < keys.length; i++) {\n                var /** @type {?} */ nsId = elementStates[keys[i]].namespaceId;\n                if (nsId) {\n                    var /** @type {?} */ ns = this._fetchNamespace(nsId);\n                    if (ns) {\n                        namespaces.add(ns);\n                    }\n                }\n            }\n        }\n        return namespaces;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.trigger = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    function (namespaceId, element, name, value) {\n        if (isElementNode(element)) {\n            this._fetchNamespace(namespaceId).trigger(element, name, value);\n            return true;\n        }\n        return false;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.insertNode = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    function (namespaceId, element, parent, insertBefore) {\n        if (!isElementNode(element))\n            return;\n        // special case for when an element is removed and reinserted (move operation)\n        // when this occurs we do not want to use the element for deletion later\n        var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval) {\n            details.setForRemoval = false;\n        }\n        // in the event that the namespaceId is blank then the caller\n        // code does not contain any animation code in it, but it is\n        // just being called so that the node is marked as being inserted\n        if (namespaceId) {\n            var /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n            // This if-statement is a workaround for router issue #21947.\n            // The router sometimes hits a race condition where while a route\n            // is being instantiated a new navigation arrives, triggering leave\n            // animation of DOM that has not been fully initialized, until this\n            // is resolved, we need to handle the scenario when DOM is not in a\n            // consistent state during the animation.\n            if (ns) {\n                ns.insertNode(element, parent);\n            }\n        }\n        // only *directives and host elements are inserted before\n        if (insertBefore) {\n            this.collectEnterElement(element);\n        }\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.collectEnterElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) { this.collectedEnterElements.push(element); };\n    /**\n     * @param {?} element\n     * @param {?} value\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.markElementAsDisabled = /**\n     * @param {?} element\n     * @param {?} value\n     * @return {?}\n     */\n    function (element, value) {\n        if (value) {\n            if (!this.disabledNodes.has(element)) {\n                this.disabledNodes.add(element);\n                addClass(element, DISABLED_CLASSNAME);\n            }\n        }\n        else if (this.disabledNodes.has(element)) {\n            this.disabledNodes.delete(element);\n            removeClass(element, DISABLED_CLASSNAME);\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.removeNode = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, element, context) {\n        if (!isElementNode(element)) {\n            this._onRemovalComplete(element, context);\n            return;\n        }\n        var /** @type {?} */ ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n        if (ns) {\n            ns.removeNode(element, context);\n        }\n        else {\n            this.markElementAsRemoved(namespaceId, element, false, context);\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?=} hasAnimation\n     * @param {?=} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.markElementAsRemoved = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?=} hasAnimation\n     * @param {?=} context\n     * @return {?}\n     */\n    function (namespaceId, element, hasAnimation, context) {\n        this.collectedLeaveElements.push(element);\n        element[REMOVAL_FLAG] = {\n            namespaceId: namespaceId,\n            setForRemoval: context, hasAnimation: hasAnimation,\n            removedBeforeQueried: false\n        };\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.listen = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    function (namespaceId, element, name, phase, callback) {\n        if (isElementNode(element)) {\n            return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n        }\n        return function () { };\n    };\n    /**\n     * @param {?} entry\n     * @param {?} subTimelines\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._buildInstruction = /**\n     * @param {?} entry\n     * @param {?} subTimelines\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @return {?}\n     */\n    function (entry, subTimelines, enterClassName, leaveClassName) {\n        return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines);\n    };\n    /**\n     * @param {?} containerElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.destroyInnerAnimations = /**\n     * @param {?} containerElement\n     * @return {?}\n     */\n    function (containerElement) {\n        var _this = this;\n        var /** @type {?} */ elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n        elements.forEach(function (element) { return _this.destroyActiveAnimationsForElement(element); });\n        if (this.playersByQueriedElement.size == 0)\n            return;\n        elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n        elements.forEach(function (element) { return _this.finishActiveQueriedAnimationOnElement(element); });\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.destroyActiveAnimationsForElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ players = this.playersByElement.get(element);\n        if (players) {\n            players.forEach(function (player) {\n                // special case for when an element is set for destruction, but hasn't started.\n                // in this situation we want to delay the destruction until the flush occurs\n                // so that any event listeners attached to the player are triggered.\n                if (player.queued) {\n                    player.markedForDestroy = true;\n                }\n                else {\n                    player.destroy();\n                }\n            });\n        }\n        var /** @type {?} */ stateMap = this.statesByElement.get(element);\n        if (stateMap) {\n            Object.keys(stateMap).forEach(function (triggerName) { return stateMap[triggerName] = DELETED_STATE_VALUE; });\n        }\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.finishActiveQueriedAnimationOnElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ players = this.playersByQueriedElement.get(element);\n        if (players) {\n            players.forEach(function (player) { return player.finish(); });\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.whenRenderingDone = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        return new Promise(function (resolve) {\n            if (_this.players.length) {\n                return optimizeGroupPlayer(_this.players).onDone(function () { return resolve(); });\n            }\n            else {\n                resolve();\n            }\n        });\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.processLeaveNode = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var _this = this;\n        var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval) {\n            // this will prevent it from removing it twice\n            element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n            if (details.namespaceId) {\n                this.destroyInnerAnimations(element);\n                var /** @type {?} */ ns = this._fetchNamespace(details.namespaceId);\n                if (ns) {\n                    ns.clearElementCache(element);\n                }\n            }\n            this._onRemovalComplete(element, details.setForRemoval);\n        }\n        if (this.driver.matchesElement(element, DISABLED_SELECTOR)) {\n            this.markElementAsDisabled(element, false);\n        }\n        this.driver.query(element, DISABLED_SELECTOR, true).forEach(function (node) {\n            _this.markElementAsDisabled(element, false);\n        });\n    };\n    /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.flush = /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    function (microtaskId) {\n        var _this = this;\n        if (microtaskId === void 0) { microtaskId = -1; }\n        var /** @type {?} */ players = [];\n        if (this.newHostElements.size) {\n            this.newHostElements.forEach(function (ns, element) { return _this._balanceNamespaceList(ns, element); });\n            this.newHostElements.clear();\n        }\n        if (this.totalAnimations && this.collectedEnterElements.length) {\n            for (var /** @type {?} */ i = 0; i < this.collectedEnterElements.length; i++) {\n                var /** @type {?} */ elm = this.collectedEnterElements[i];\n                addClass(elm, STAR_CLASSNAME);\n            }\n        }\n        if (this._namespaceList.length &&\n            (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n            var /** @type {?} */ cleanupFns = [];\n            try {\n                players = this._flushAnimations(cleanupFns, microtaskId);\n            }\n            finally {\n                for (var /** @type {?} */ i = 0; i < cleanupFns.length; i++) {\n                    cleanupFns[i]();\n                }\n            }\n        }\n        else {\n            for (var /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n                var /** @type {?} */ element = this.collectedLeaveElements[i];\n                this.processLeaveNode(element);\n            }\n        }\n        this.totalQueuedPlayers = 0;\n        this.collectedEnterElements.length = 0;\n        this.collectedLeaveElements.length = 0;\n        this._flushFns.forEach(function (fn) { return fn(); });\n        this._flushFns = [];\n        if (this._whenQuietFns.length) {\n            // we move these over to a variable so that\n            // if any new callbacks are registered in another\n            // flush they do not populate the existing set\n            var /** @type {?} */ quietFns_1 = this._whenQuietFns;\n            this._whenQuietFns = [];\n            if (players.length) {\n                optimizeGroupPlayer(players).onDone(function () { quietFns_1.forEach(function (fn) { return fn(); }); });\n            }\n            else {\n                quietFns_1.forEach(function (fn) { return fn(); });\n            }\n        }\n    };\n    /**\n     * @param {?} errors\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.reportError = /**\n     * @param {?} errors\n     * @return {?}\n     */\n    function (errors) {\n        throw new Error(\"Unable to process animations due to the following failed trigger transitions\\n \" + errors.join('\\n'));\n    };\n    /**\n     * @param {?} cleanupFns\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._flushAnimations = /**\n     * @param {?} cleanupFns\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    function (cleanupFns, microtaskId) {\n        var _this = this;\n        var /** @type {?} */ subTimelines = new ElementInstructionMap();\n        var /** @type {?} */ skippedPlayers = [];\n        var /** @type {?} */ skippedPlayersMap = new Map();\n        var /** @type {?} */ queuedInstructions = [];\n        var /** @type {?} */ queriedElements = new Map();\n        var /** @type {?} */ allPreStyleElements = new Map();\n        var /** @type {?} */ allPostStyleElements = new Map();\n        var /** @type {?} */ disabledElementsSet = new Set();\n        this.disabledNodes.forEach(function (node) {\n            disabledElementsSet.add(node);\n            var /** @type {?} */ nodesThatAreDisabled = _this.driver.query(node, QUEUED_SELECTOR, true);\n            for (var /** @type {?} */ i_1 = 0; i_1 < nodesThatAreDisabled.length; i_1++) {\n                disabledElementsSet.add(nodesThatAreDisabled[i_1]);\n            }\n        });\n        var /** @type {?} */ bodyNode = getBodyNode();\n        var /** @type {?} */ allTriggerElements = Array.from(this.statesByElement.keys());\n        var /** @type {?} */ enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n        // this must occur before the instructions are built below such that\n        // the :enter queries match the elements (since the timeline queries\n        // are fired during instruction building).\n        var /** @type {?} */ enterNodeMapIds = new Map();\n        var /** @type {?} */ i = 0;\n        enterNodeMap.forEach(function (nodes, root) {\n            var /** @type {?} */ className = ENTER_CLASSNAME + i++;\n            enterNodeMapIds.set(root, className);\n            nodes.forEach(function (node) { return addClass(node, className); });\n        });\n        var /** @type {?} */ allLeaveNodes = [];\n        var /** @type {?} */ mergedLeaveNodes = new Set();\n        var /** @type {?} */ leaveNodesWithoutAnimations = new Set();\n        for (var /** @type {?} */ i_2 = 0; i_2 < this.collectedLeaveElements.length; i_2++) {\n            var /** @type {?} */ element = this.collectedLeaveElements[i_2];\n            var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n            if (details && details.setForRemoval) {\n                allLeaveNodes.push(element);\n                mergedLeaveNodes.add(element);\n                if (details.hasAnimation) {\n                    this.driver.query(element, STAR_SELECTOR, true).forEach(function (elm) { return mergedLeaveNodes.add(elm); });\n                }\n                else {\n                    leaveNodesWithoutAnimations.add(element);\n                }\n            }\n        }\n        var /** @type {?} */ leaveNodeMapIds = new Map();\n        var /** @type {?} */ leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n        leaveNodeMap.forEach(function (nodes, root) {\n            var /** @type {?} */ className = LEAVE_CLASSNAME + i++;\n            leaveNodeMapIds.set(root, className);\n            nodes.forEach(function (node) { return addClass(node, className); });\n        });\n        cleanupFns.push(function () {\n            enterNodeMap.forEach(function (nodes, root) {\n                var /** @type {?} */ className = /** @type {?} */ ((enterNodeMapIds.get(root)));\n                nodes.forEach(function (node) { return removeClass(node, className); });\n            });\n            leaveNodeMap.forEach(function (nodes, root) {\n                var /** @type {?} */ className = /** @type {?} */ ((leaveNodeMapIds.get(root)));\n                nodes.forEach(function (node) { return removeClass(node, className); });\n            });\n            allLeaveNodes.forEach(function (element) { _this.processLeaveNode(element); });\n        });\n        var /** @type {?} */ allPlayers = [];\n        var /** @type {?} */ erroneousTransitions = [];\n        for (var /** @type {?} */ i_3 = this._namespaceList.length - 1; i_3 >= 0; i_3--) {\n            var /** @type {?} */ ns = this._namespaceList[i_3];\n            ns.drainQueuedTransitions(microtaskId).forEach(function (entry) {\n                var /** @type {?} */ player = entry.player;\n                allPlayers.push(player);\n                var /** @type {?} */ element = entry.element;\n                if (!bodyNode || !_this.driver.containsElement(bodyNode, element)) {\n                    player.destroy();\n                    return;\n                }\n                var /** @type {?} */ leaveClassName = /** @type {?} */ ((leaveNodeMapIds.get(element)));\n                var /** @type {?} */ enterClassName = /** @type {?} */ ((enterNodeMapIds.get(element)));\n                var /** @type {?} */ instruction = /** @type {?} */ ((_this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName)));\n                if (instruction.errors && instruction.errors.length) {\n                    erroneousTransitions.push(instruction);\n                    return;\n                }\n                // if a unmatched transition is queued to go then it SHOULD NOT render\n                // an animation and cancel the previously running animations.\n                if (entry.isFallbackTransition) {\n                    player.onStart(function () { return eraseStyles(element, instruction.fromStyles); });\n                    player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this means that if a parent animation uses this animation as a sub trigger\n                // then it will instruct the timeline builder to not add a player delay, but\n                // instead stretch the first keyframe gap up until the animation starts. The\n                // reason this is important is to prevent extra initialization styles from being\n                // required by the user in the animation.\n                instruction.timelines.forEach(function (tl) { return tl.stretchStartingKeyframe = true; });\n                subTimelines.append(element, instruction.timelines);\n                var /** @type {?} */ tuple = { instruction: instruction, player: player, element: element };\n                queuedInstructions.push(tuple);\n                instruction.queriedElements.forEach(function (element) { return getOrSetAsInMap(queriedElements, element, []).push(player); });\n                instruction.preStyleProps.forEach(function (stringMap, element) {\n                    var /** @type {?} */ props = Object.keys(stringMap);\n                    if (props.length) {\n                        var /** @type {?} */ setVal_1 = /** @type {?} */ ((allPreStyleElements.get(element)));\n                        if (!setVal_1) {\n                            allPreStyleElements.set(element, setVal_1 = new Set());\n                        }\n                        props.forEach(function (prop) { return setVal_1.add(prop); });\n                    }\n                });\n                instruction.postStyleProps.forEach(function (stringMap, element) {\n                    var /** @type {?} */ props = Object.keys(stringMap);\n                    var /** @type {?} */ setVal = /** @type {?} */ ((allPostStyleElements.get(element)));\n                    if (!setVal) {\n                        allPostStyleElements.set(element, setVal = new Set());\n                    }\n                    props.forEach(function (prop) { return setVal.add(prop); });\n                });\n            });\n        }\n        if (erroneousTransitions.length) {\n            var /** @type {?} */ errors_1 = [];\n            erroneousTransitions.forEach(function (instruction) {\n                errors_1.push(\"@\" + instruction.triggerName + \" has failed due to:\\n\"); /** @type {?} */\n                ((instruction.errors)).forEach(function (error) { return errors_1.push(\"- \" + error + \"\\n\"); });\n            });\n            allPlayers.forEach(function (player) { return player.destroy(); });\n            this.reportError(errors_1);\n        }\n        var /** @type {?} */ allPreviousPlayersMap = new Map();\n        // this map works to tell which element in the DOM tree is contained by\n        // which animation. Further down below this map will get populated once\n        // the players are built and in doing so it can efficiently figure out\n        // if a sub player is skipped due to a parent player having priority.\n        var /** @type {?} */ animationElementMap = new Map();\n        queuedInstructions.forEach(function (entry) {\n            var /** @type {?} */ element = entry.element;\n            if (subTimelines.has(element)) {\n                animationElementMap.set(element, element);\n                _this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n            }\n        });\n        skippedPlayers.forEach(function (player) {\n            var /** @type {?} */ element = player.element;\n            var /** @type {?} */ previousPlayers = _this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n            previousPlayers.forEach(function (prevPlayer) {\n                getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer);\n                prevPlayer.destroy();\n            });\n        });\n        // this is a special case for nodes that will be removed (either by)\n        // having their own leave animations or by being queried in a container\n        // that will be removed once a parent animation is complete. The idea\n        // here is that * styles must be identical to ! styles because of\n        // backwards compatibility (* is also filled in by default in many places).\n        // Otherwise * styles will return an empty value or auto since the element\n        // that is being getComputedStyle'd will not be visible (since * = destination)\n        var /** @type {?} */ replaceNodes = allLeaveNodes.filter(function (node) {\n            return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n        });\n        // POST STAGE: fill the * styles\n        var /** @type {?} */ postStylesMap = new Map();\n        var /** @type {?} */ allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n        allLeaveQueriedNodes.forEach(function (node) {\n            if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n                replaceNodes.push(node);\n            }\n        });\n        // PRE STAGE: fill the ! styles\n        var /** @type {?} */ preStylesMap = new Map();\n        enterNodeMap.forEach(function (nodes, root) {\n            cloakAndComputeStyles(preStylesMap, _this.driver, new Set(nodes), allPreStyleElements, PRE_STYLE);\n        });\n        replaceNodes.forEach(function (node) {\n            var /** @type {?} */ post = postStylesMap.get(node);\n            var /** @type {?} */ pre = preStylesMap.get(node);\n            postStylesMap.set(node, /** @type {?} */ (tslib_1.__assign({}, post, pre)));\n        });\n        var /** @type {?} */ rootPlayers = [];\n        var /** @type {?} */ subPlayers = [];\n        var /** @type {?} */ NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n        queuedInstructions.forEach(function (entry) {\n            var element = entry.element, player = entry.player, instruction = entry.instruction;\n            // this means that it was never consumed by a parent animation which\n            // means that it is independent and therefore should be set for animation\n            if (subTimelines.has(element)) {\n                if (disabledElementsSet.has(element)) {\n                    player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this will flow up the DOM and query the map to figure out\n                // if a parent animation has priority over it. In the situation\n                // that a parent is detected then it will cancel the loop. If\n                // nothing is detected, or it takes a few hops to find a parent,\n                // then it will fill in the missing nodes and signal them as having\n                // a detected parent (or a NO_PARENT value via a special constant).\n                var /** @type {?} */ parentWithAnimation_1 = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n                if (animationElementMap.size > 1) {\n                    var /** @type {?} */ elm = element;\n                    var /** @type {?} */ parentsToAdd = [];\n                    while (elm = elm.parentNode) {\n                        var /** @type {?} */ detectedParent = animationElementMap.get(elm);\n                        if (detectedParent) {\n                            parentWithAnimation_1 = detectedParent;\n                            break;\n                        }\n                        parentsToAdd.push(elm);\n                    }\n                    parentsToAdd.forEach(function (parent) { return animationElementMap.set(parent, parentWithAnimation_1); });\n                }\n                var /** @type {?} */ innerPlayer = _this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n                player.setRealPlayer(innerPlayer);\n                if (parentWithAnimation_1 === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n                    rootPlayers.push(player);\n                }\n                else {\n                    var /** @type {?} */ parentPlayers = _this.playersByElement.get(parentWithAnimation_1);\n                    if (parentPlayers && parentPlayers.length) {\n                        player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n                    }\n                    skippedPlayers.push(player);\n                }\n            }\n            else {\n                eraseStyles(element, instruction.fromStyles);\n                player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                // there still might be a ancestor player animating this\n                // element therefore we will still add it as a sub player\n                // even if its animation may be disabled\n                subPlayers.push(player);\n                if (disabledElementsSet.has(element)) {\n                    skippedPlayers.push(player);\n                }\n            }\n        });\n        // find all of the sub players' corresponding inner animation player\n        subPlayers.forEach(function (player) {\n            // even if any players are not found for a sub animation then it\n            // will still complete itself after the next tick since it's Noop\n            var /** @type {?} */ playersForElement = skippedPlayersMap.get(player.element);\n            if (playersForElement && playersForElement.length) {\n                var /** @type {?} */ innerPlayer = optimizeGroupPlayer(playersForElement);\n                player.setRealPlayer(innerPlayer);\n            }\n        });\n        // the reason why we don't actually play the animation is\n        // because all that a skipped player is designed to do is to\n        // fire the start/done transition callback events\n        skippedPlayers.forEach(function (player) {\n            if (player.parentPlayer) {\n                player.syncPlayerEvents(player.parentPlayer);\n            }\n            else {\n                player.destroy();\n            }\n        });\n        // run through all of the queued removals and see if they\n        // were picked up by a query. If not then perform the removal\n        // operation right away unless a parent animation is ongoing.\n        for (var /** @type {?} */ i_4 = 0; i_4 < allLeaveNodes.length; i_4++) {\n            var /** @type {?} */ element = allLeaveNodes[i_4];\n            var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n            removeClass(element, LEAVE_CLASSNAME);\n            // this means the element has a removal animation that is being\n            // taken care of and therefore the inner elements will hang around\n            // until that animation is over (or the parent queried animation)\n            if (details && details.hasAnimation)\n                continue;\n            var /** @type {?} */ players = [];\n            // if this element is queried or if it contains queried children\n            // then we want for the element not to be removed from the page\n            // until the queried animations have finished\n            if (queriedElements.size) {\n                var /** @type {?} */ queriedPlayerResults = queriedElements.get(element);\n                if (queriedPlayerResults && queriedPlayerResults.length) {\n                    players.push.apply(players, queriedPlayerResults);\n                }\n                var /** @type {?} */ queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n                for (var /** @type {?} */ j = 0; j < queriedInnerElements.length; j++) {\n                    var /** @type {?} */ queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n                    if (queriedPlayers && queriedPlayers.length) {\n                        players.push.apply(players, queriedPlayers);\n                    }\n                }\n            }\n            var /** @type {?} */ activePlayers = players.filter(function (p) { return !p.destroyed; });\n            if (activePlayers.length) {\n                removeNodesAfterAnimationDone(this, element, activePlayers);\n            }\n            else {\n                this.processLeaveNode(element);\n            }\n        }\n        // this is required so the cleanup method doesn't remove them\n        allLeaveNodes.length = 0;\n        rootPlayers.forEach(function (player) {\n            _this.players.push(player);\n            player.onDone(function () {\n                player.destroy();\n                var /** @type {?} */ index = _this.players.indexOf(player);\n                _this.players.splice(index, 1);\n            });\n            player.play();\n        });\n        return rootPlayers;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.elementContainsData = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @return {?}\n     */\n    function (namespaceId, element) {\n        var /** @type {?} */ containsData = false;\n        var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval)\n            containsData = true;\n        if (this.playersByElement.has(element))\n            containsData = true;\n        if (this.playersByQueriedElement.has(element))\n            containsData = true;\n        if (this.statesByElement.has(element))\n            containsData = true;\n        return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n    };\n    /**\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.afterFlush = /**\n     * @param {?} callback\n     * @return {?}\n     */\n    function (callback) { this._flushFns.push(callback); };\n    /**\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.afterFlushAnimationsDone = /**\n     * @param {?} callback\n     * @return {?}\n     */\n    function (callback) { this._whenQuietFns.push(callback); };\n    /**\n     * @param {?} element\n     * @param {?} isQueriedElement\n     * @param {?=} namespaceId\n     * @param {?=} triggerName\n     * @param {?=} toStateValue\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._getPreviousPlayers = /**\n     * @param {?} element\n     * @param {?} isQueriedElement\n     * @param {?=} namespaceId\n     * @param {?=} triggerName\n     * @param {?=} toStateValue\n     * @return {?}\n     */\n    function (element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n        var /** @type {?} */ players = [];\n        if (isQueriedElement) {\n            var /** @type {?} */ queriedElementPlayers = this.playersByQueriedElement.get(element);\n            if (queriedElementPlayers) {\n                players = queriedElementPlayers;\n            }\n        }\n        else {\n            var /** @type {?} */ elementPlayers = this.playersByElement.get(element);\n            if (elementPlayers) {\n                var /** @type {?} */ isRemovalAnimation_1 = !toStateValue || toStateValue == VOID_VALUE;\n                elementPlayers.forEach(function (player) {\n                    if (player.queued)\n                        return;\n                    if (!isRemovalAnimation_1 && player.triggerName != triggerName)\n                        return;\n                    players.push(player);\n                });\n            }\n        }\n        if (namespaceId || triggerName) {\n            players = players.filter(function (player) {\n                if (namespaceId && namespaceId != player.namespaceId)\n                    return false;\n                if (triggerName && triggerName != player.triggerName)\n                    return false;\n                return true;\n            });\n        }\n        return players;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._beforeAnimationBuild = /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @return {?}\n     */\n    function (namespaceId, instruction, allPreviousPlayersMap) {\n        var /** @type {?} */ triggerName = instruction.triggerName;\n        var /** @type {?} */ rootElement = instruction.element;\n        // when a removal animation occurs, ALL previous players are collected\n        // and destroyed (even if they are outside of the current namespace)\n        var /** @type {?} */ targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n        var /** @type {?} */ targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n        var _loop_1 = function (timelineInstruction) {\n            var /** @type {?} */ element = timelineInstruction.element;\n            var /** @type {?} */ isQueriedElement = element !== rootElement;\n            var /** @type {?} */ players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n            var /** @type {?} */ previousPlayers = this_1._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n            previousPlayers.forEach(function (player) {\n                var /** @type {?} */ realPlayer = /** @type {?} */ (player.getRealPlayer());\n                if (realPlayer.beforeDestroy) {\n                    realPlayer.beforeDestroy();\n                }\n                player.destroy();\n                players.push(player);\n            });\n        };\n        var this_1 = this;\n        for (var _i = 0, _a = instruction.timelines; _i < _a.length; _i++) {\n            var timelineInstruction = _a[_i];\n            _loop_1(timelineInstruction);\n        }\n        // this needs to be done so that the PRE/POST styles can be\n        // computed properly without interfering with the previous animation\n        eraseStyles(rootElement, instruction.fromStyles);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @param {?} skippedPlayersMap\n     * @param {?} preStylesMap\n     * @param {?} postStylesMap\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._buildAnimation = /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @param {?} skippedPlayersMap\n     * @param {?} preStylesMap\n     * @param {?} postStylesMap\n     * @return {?}\n     */\n    function (namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n        var _this = this;\n        var /** @type {?} */ triggerName = instruction.triggerName;\n        var /** @type {?} */ rootElement = instruction.element;\n        // we first run this so that the previous animation player\n        // data can be passed into the successive animation players\n        var /** @type {?} */ allQueriedPlayers = [];\n        var /** @type {?} */ allConsumedElements = new Set();\n        var /** @type {?} */ allSubElements = new Set();\n        var /** @type {?} */ allNewPlayers = instruction.timelines.map(function (timelineInstruction) {\n            var /** @type {?} */ element = timelineInstruction.element;\n            allConsumedElements.add(element);\n            // FIXME (matsko): make sure to-be-removed animations are removed properly\n            var /** @type {?} */ details = element[REMOVAL_FLAG];\n            if (details && details.removedBeforeQueried)\n                return new NoopAnimationPlayer();\n            var /** @type {?} */ isQueriedElement = element !== rootElement;\n            var /** @type {?} */ previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY)\n                .map(function (p) { return p.getRealPlayer(); }))\n                .filter(function (p) {\n                // the `element` is not apart of the AnimationPlayer definition, but\n                // Mock/WebAnimations\n                // use the element within their implementation. This will be added in Angular5 to\n                // AnimationPlayer\n                var /** @type {?} */ pp = /** @type {?} */ (p);\n                return pp.element ? pp.element === element : false;\n            });\n            var /** @type {?} */ preStyles = preStylesMap.get(element);\n            var /** @type {?} */ postStyles = postStylesMap.get(element);\n            var /** @type {?} */ keyframes = normalizeKeyframes(_this.driver, _this._normalizer, element, timelineInstruction.keyframes, preStyles, postStyles);\n            var /** @type {?} */ player = _this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n            // this means that this particular player belongs to a sub trigger. It is\n            // important that we match this player up with the corresponding (@trigger.listener)\n            if (timelineInstruction.subTimeline && skippedPlayersMap) {\n                allSubElements.add(element);\n            }\n            if (isQueriedElement) {\n                var /** @type {?} */ wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n                wrappedPlayer.setRealPlayer(player);\n                allQueriedPlayers.push(wrappedPlayer);\n            }\n            return player;\n        });\n        allQueriedPlayers.forEach(function (player) {\n            getOrSetAsInMap(_this.playersByQueriedElement, player.element, []).push(player);\n            player.onDone(function () { return deleteOrUnsetInMap(_this.playersByQueriedElement, player.element, player); });\n        });\n        allConsumedElements.forEach(function (element) { return addClass(element, NG_ANIMATING_CLASSNAME); });\n        var /** @type {?} */ player = optimizeGroupPlayer(allNewPlayers);\n        player.onDestroy(function () {\n            allConsumedElements.forEach(function (element) { return removeClass(element, NG_ANIMATING_CLASSNAME); });\n            setStyles(rootElement, instruction.toStyles);\n        });\n        // this basically makes all of the callbacks for sub element animations\n        // be dependent on the upper players for when they finish\n        allSubElements.forEach(function (element) { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n        return player;\n    };\n    /**\n     * @param {?} instruction\n     * @param {?} keyframes\n     * @param {?} previousPlayers\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._buildPlayer = /**\n     * @param {?} instruction\n     * @param {?} keyframes\n     * @param {?} previousPlayers\n     * @return {?}\n     */\n    function (instruction, keyframes, previousPlayers) {\n        if (keyframes.length > 0) {\n            return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n        }\n        // special case for when an empty transition|definition is provided\n        // ... there is no point in rendering an empty animation\n        return new NoopAnimationPlayer();\n    };\n    return TransitionAnimationEngine;\n}());\nexport { TransitionAnimationEngine };\nfunction TransitionAnimationEngine_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.players;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.newHostElements;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.playersByElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.playersByQueriedElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.statesByElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.disabledNodes;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.totalAnimations;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.totalQueuedPlayers;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._namespaceLookup;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._namespaceList;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._flushFns;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._whenQuietFns;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.namespacesByHostElement;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.collectedEnterElements;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.collectedLeaveElements;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.onRemovalComplete;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype.driver;\n    /** @type {?} */\n    TransitionAnimationEngine.prototype._normalizer;\n}\nvar TransitionAnimationPlayer = /** @class */ (function () {\n    function TransitionAnimationPlayer(namespaceId, triggerName, element) {\n        this.namespaceId = namespaceId;\n        this.triggerName = triggerName;\n        this.element = element;\n        this._player = new NoopAnimationPlayer();\n        this._containsRealPlayer = false;\n        this._queuedCallbacks = {};\n        this.destroyed = false;\n        this.markedForDestroy = false;\n        this.queued = true;\n    }\n    /**\n     * @param {?} player\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.setRealPlayer = /**\n     * @param {?} player\n     * @return {?}\n     */\n    function (player) {\n        var _this = this;\n        if (this._containsRealPlayer)\n            return;\n        this._player = player;\n        Object.keys(this._queuedCallbacks).forEach(function (phase) {\n            _this._queuedCallbacks[phase].forEach(function (callback) { return listenOnPlayer(player, phase, undefined, callback); });\n        });\n        this._queuedCallbacks = {};\n        this._containsRealPlayer = true;\n        (/** @type {?} */ (this)).queued = false;\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.getRealPlayer = /**\n     * @return {?}\n     */\n    function () { return this._player; };\n    /**\n     * @param {?} player\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.syncPlayerEvents = /**\n     * @param {?} player\n     * @return {?}\n     */\n    function (player) {\n        var _this = this;\n        var /** @type {?} */ p = /** @type {?} */ (this._player);\n        if (p.triggerCallback) {\n            player.onStart(function () { return p.triggerCallback('start'); });\n        }\n        player.onDone(function () { return _this.finish(); });\n        player.onDestroy(function () { return _this.destroy(); });\n    };\n    /**\n     * @param {?} name\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype._queueEvent = /**\n     * @param {?} name\n     * @param {?} callback\n     * @return {?}\n     */\n    function (name, callback) {\n        getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.onDone = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        if (this.queued) {\n            this._queueEvent('done', fn);\n        }\n        this._player.onDone(fn);\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.onStart = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        if (this.queued) {\n            this._queueEvent('start', fn);\n        }\n        this._player.onStart(fn);\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.onDestroy = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        if (this.queued) {\n            this._queueEvent('destroy', fn);\n        }\n        this._player.onDestroy(fn);\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () { this._player.init(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this.queued ? false : this._player.hasStarted(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.play(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.pause = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.pause(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.restart = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.restart(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () { this._player.finish(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        (/** @type {?} */ (this)).destroyed = true;\n        this._player.destroy();\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.reset = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.reset(); };\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.setPosition = /**\n     * @param {?} p\n     * @return {?}\n     */\n    function (p) {\n        if (!this.queued) {\n            this._player.setPosition(p);\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.getPosition = /**\n     * @return {?}\n     */\n    function () { return this.queued ? 0 : this._player.getPosition(); };\n    Object.defineProperty(TransitionAnimationPlayer.prototype, \"totalTime\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this._player.totalTime; },\n        enumerable: true,\n        configurable: true\n    });\n    /* @internal */\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.triggerCallback = /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    function (phaseName) {\n        var /** @type {?} */ p = /** @type {?} */ (this._player);\n        if (p.triggerCallback) {\n            p.triggerCallback(phaseName);\n        }\n    };\n    return TransitionAnimationPlayer;\n}());\nexport { TransitionAnimationPlayer };\nfunction TransitionAnimationPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype._player;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype._containsRealPlayer;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype._queuedCallbacks;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.destroyed;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.parentPlayer;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.markedForDestroy;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.queued;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.namespaceId;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.triggerName;\n    /** @type {?} */\n    TransitionAnimationPlayer.prototype.element;\n}\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} value\n * @return {?}\n */\nfunction deleteOrUnsetInMap(map, key, value) {\n    var /** @type {?} */ currentValues;\n    if (map instanceof Map) {\n        currentValues = map.get(key);\n        if (currentValues) {\n            if (currentValues.length) {\n                var /** @type {?} */ index = currentValues.indexOf(value);\n                currentValues.splice(index, 1);\n            }\n            if (currentValues.length == 0) {\n                map.delete(key);\n            }\n        }\n    }\n    else {\n        currentValues = map[key];\n        if (currentValues) {\n            if (currentValues.length) {\n                var /** @type {?} */ index = currentValues.indexOf(value);\n                currentValues.splice(index, 1);\n            }\n            if (currentValues.length == 0) {\n                delete map[key];\n            }\n        }\n    }\n    return currentValues;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction normalizeTriggerValue(value) {\n    // we use `!= null` here because it's the most simple\n    // way to test against a \"falsy\" value without mixing\n    // in empty strings or a zero value. DO NOT OPTIMIZE.\n    return value != null ? value : null;\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction isElementNode(node) {\n    return node && node['nodeType'] === 1;\n}\n/**\n * @param {?} eventName\n * @return {?}\n */\nfunction isTriggerEventValid(eventName) {\n    return eventName == 'start' || eventName == 'done';\n}\n/**\n * @param {?} element\n * @param {?=} value\n * @return {?}\n */\nfunction cloakElement(element, value) {\n    var /** @type {?} */ oldValue = element.style.display;\n    element.style.display = value != null ? value : 'none';\n    return oldValue;\n}\n/**\n * @param {?} valuesMap\n * @param {?} driver\n * @param {?} elements\n * @param {?} elementPropsMap\n * @param {?} defaultStyle\n * @return {?}\n */\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n    var /** @type {?} */ cloakVals = [];\n    elements.forEach(function (element) { return cloakVals.push(cloakElement(element)); });\n    var /** @type {?} */ failedElements = [];\n    elementPropsMap.forEach(function (props, element) {\n        var /** @type {?} */ styles = {};\n        props.forEach(function (prop) {\n            var /** @type {?} */ value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n            // there is no easy way to detect this because a sub element could be removed\n            // by a parent animation element being detached.\n            if (!value || value.length == 0) {\n                element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n                failedElements.push(element);\n            }\n        });\n        valuesMap.set(element, styles);\n    });\n    // we use a index variable here since Set.forEach(a, i) does not return\n    // an index value for the closure (but instead just the value)\n    var /** @type {?} */ i = 0;\n    elements.forEach(function (element) { return cloakElement(element, cloakVals[i++]); });\n    return failedElements;\n}\n/**\n * @param {?} roots\n * @param {?} nodes\n * @return {?}\n */\nfunction buildRootMap(roots, nodes) {\n    var /** @type {?} */ rootMap = new Map();\n    roots.forEach(function (root) { return rootMap.set(root, []); });\n    if (nodes.length == 0)\n        return rootMap;\n    var /** @type {?} */ NULL_NODE = 1;\n    var /** @type {?} */ nodeSet = new Set(nodes);\n    var /** @type {?} */ localRootMap = new Map();\n    /**\n     * @param {?} node\n     * @return {?}\n     */\n    function getRoot(node) {\n        if (!node)\n            return NULL_NODE;\n        var /** @type {?} */ root = localRootMap.get(node);\n        if (root)\n            return root;\n        var /** @type {?} */ parent = node.parentNode;\n        if (rootMap.has(parent)) {\n            // ngIf inside @trigger\n            root = parent;\n        }\n        else if (nodeSet.has(parent)) {\n            // ngIf inside ngIf\n            root = NULL_NODE;\n        }\n        else {\n            // recurse upwards\n            root = getRoot(parent);\n        }\n        localRootMap.set(node, root);\n        return root;\n    }\n    nodes.forEach(function (node) {\n        var /** @type {?} */ root = getRoot(node);\n        if (root !== NULL_NODE) {\n            /** @type {?} */ ((rootMap.get(root))).push(node);\n        }\n    });\n    return rootMap;\n}\nvar /** @type {?} */ CLASSES_CACHE_KEY = '$$classes';\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction containsClass(element, className) {\n    if (element.classList) {\n        return element.classList.contains(className);\n    }\n    else {\n        var /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        return classes && classes[className];\n    }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction addClass(element, className) {\n    if (element.classList) {\n        element.classList.add(className);\n    }\n    else {\n        var /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        if (!classes) {\n            classes = element[CLASSES_CACHE_KEY] = {};\n        }\n        classes[className] = true;\n    }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction removeClass(element, className) {\n    if (element.classList) {\n        element.classList.remove(className);\n    }\n    else {\n        var /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        if (classes) {\n            delete classes[className];\n        }\n    }\n}\n/**\n * @param {?} engine\n * @param {?} element\n * @param {?} players\n * @return {?}\n */\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n    optimizeGroupPlayer(players).onDone(function () { return engine.processLeaveNode(element); });\n}\n/**\n * @param {?} players\n * @return {?}\n */\nfunction flattenGroupPlayers(players) {\n    var /** @type {?} */ finalPlayers = [];\n    _flattenGroupPlayersRecur(players, finalPlayers);\n    return finalPlayers;\n}\n/**\n * @param {?} players\n * @param {?} finalPlayers\n * @return {?}\n */\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n    for (var /** @type {?} */ i = 0; i < players.length; i++) {\n        var /** @type {?} */ player = players[i];\n        if (player instanceof AnimationGroupPlayer) {\n            _flattenGroupPlayersRecur(player.players, finalPlayers);\n        }\n        else {\n            finalPlayers.push(/** @type {?} */ (player));\n        }\n    }\n}\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nfunction objEquals(a, b) {\n    var /** @type {?} */ k1 = Object.keys(a);\n    var /** @type {?} */ k2 = Object.keys(b);\n    if (k1.length != k2.length)\n        return false;\n    for (var /** @type {?} */ i = 0; i < k1.length; i++) {\n        var /** @type {?} */ prop = k1[i];\n        if (!b.hasOwnProperty(prop) || a[prop] !== b[prop])\n            return false;\n    }\n    return true;\n}\n/**\n * @param {?} element\n * @param {?} allPreStyleElements\n * @param {?} allPostStyleElements\n * @return {?}\n */\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n    var /** @type {?} */ postEntry = allPostStyleElements.get(element);\n    if (!postEntry)\n        return false;\n    var /** @type {?} */ preEntry = allPreStyleElements.get(element);\n    if (preEntry) {\n        postEntry.forEach(function (data) { return ((preEntry)).add(data); });\n    }\n    else {\n        allPreStyleElements.set(element, postEntry);\n    }\n    allPostStyleElements.delete(element);\n    return true;\n}\n//# sourceMappingURL=transition_animation_engine.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { buildAnimationAst } from '../dsl/animation_ast_builder';\nimport { buildTrigger } from '../dsl/animation_trigger';\nimport { parseTimelineCommand } from './shared';\nimport { TimelineAnimationEngine } from './timeline_animation_engine';\nimport { TransitionAnimationEngine } from './transition_animation_engine';\nvar AnimationEngine = /** @class */ (function () {\n    function AnimationEngine(_driver, normalizer) {\n        var _this = this;\n        this._driver = _driver;\n        this._triggerCache = {};\n        this.onRemovalComplete = function (element, context) { };\n        this._transitionEngine = new TransitionAnimationEngine(_driver, normalizer);\n        this._timelineEngine = new TimelineAnimationEngine(_driver, normalizer);\n        this._transitionEngine.onRemovalComplete = function (element, context) {\n            return _this.onRemovalComplete(element, context);\n        };\n    }\n    /**\n     * @param {?} componentId\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @param {?} name\n     * @param {?} metadata\n     * @return {?}\n     */\n    AnimationEngine.prototype.registerTrigger = /**\n     * @param {?} componentId\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @param {?} name\n     * @param {?} metadata\n     * @return {?}\n     */\n    function (componentId, namespaceId, hostElement, name, metadata) {\n        var /** @type {?} */ cacheKey = componentId + '-' + name;\n        var /** @type {?} */ trigger = this._triggerCache[cacheKey];\n        if (!trigger) {\n            var /** @type {?} */ errors = [];\n            var /** @type {?} */ ast = /** @type {?} */ (buildAnimationAst(this._driver, /** @type {?} */ (metadata), errors));\n            if (errors.length) {\n                throw new Error(\"The animation trigger \\\"\" + name + \"\\\" has failed to build due to the following errors:\\n - \" + errors.join(\"\\n - \"));\n            }\n            trigger = buildTrigger(name, ast);\n            this._triggerCache[cacheKey] = trigger;\n        }\n        this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    AnimationEngine.prototype.register = /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (namespaceId, hostElement) {\n        this._transitionEngine.register(namespaceId, hostElement);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationEngine.prototype.destroy = /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, context) {\n        this._transitionEngine.destroy(namespaceId, context);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    AnimationEngine.prototype.onInsert = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    function (namespaceId, element, parent, insertBefore) {\n        this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationEngine.prototype.onRemove = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, element, context) {\n        this._transitionEngine.removeNode(namespaceId, element, context);\n    };\n    /**\n     * @param {?} element\n     * @param {?} disable\n     * @return {?}\n     */\n    AnimationEngine.prototype.disableAnimations = /**\n     * @param {?} element\n     * @param {?} disable\n     * @return {?}\n     */\n    function (element, disable) {\n        this._transitionEngine.markElementAsDisabled(element, disable);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} property\n     * @param {?} value\n     * @return {?}\n     */\n    AnimationEngine.prototype.process = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} property\n     * @param {?} value\n     * @return {?}\n     */\n    function (namespaceId, element, property, value) {\n        if (property.charAt(0) == '@') {\n            var _a = parseTimelineCommand(property), id = _a[0], action = _a[1];\n            var /** @type {?} */ args = /** @type {?} */ (value);\n            this._timelineEngine.command(id, element, action, args);\n        }\n        else {\n            this._transitionEngine.trigger(namespaceId, element, property, value);\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} eventPhase\n     * @param {?} callback\n     * @return {?}\n     */\n    AnimationEngine.prototype.listen = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} eventPhase\n     * @param {?} callback\n     * @return {?}\n     */\n    function (namespaceId, element, eventName, eventPhase, callback) {\n        // @@listen\n        if (eventName.charAt(0) == '@') {\n            var _a = parseTimelineCommand(eventName), id = _a[0], action = _a[1];\n            return this._timelineEngine.listen(id, element, action, callback);\n        }\n        return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n    };\n    /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    AnimationEngine.prototype.flush = /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    function (microtaskId) {\n        if (microtaskId === void 0) { microtaskId = -1; }\n        this._transitionEngine.flush(microtaskId);\n    };\n    Object.defineProperty(AnimationEngine.prototype, \"players\", {\n        get: /**\n         * @return {?}\n         */\n        function () {\n            return (/** @type {?} */ (this._transitionEngine.players))\n                .concat(/** @type {?} */ (this._timelineEngine.players));\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @return {?}\n     */\n    AnimationEngine.prototype.whenRenderingDone = /**\n     * @return {?}\n     */\n    function () { return this._transitionEngine.whenRenderingDone(); };\n    return AnimationEngine;\n}());\nexport { AnimationEngine };\nfunction AnimationEngine_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationEngine.prototype._transitionEngine;\n    /** @type {?} */\n    AnimationEngine.prototype._timelineEngine;\n    /** @type {?} */\n    AnimationEngine.prototype._triggerCache;\n    /** @type {?} */\n    AnimationEngine.prototype.onRemovalComplete;\n    /** @type {?} */\n    AnimationEngine.prototype._driver;\n}\n//# sourceMappingURL=animation_engine_next.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { allowPreviousPlayerStylesMerge, copyStyles } from '../../util';\nvar WebAnimationsPlayer = /** @class */ (function () {\n    function WebAnimationsPlayer(element, keyframes, options, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        var _this = this;\n        this.element = element;\n        this.keyframes = keyframes;\n        this.options = options;\n        this.previousPlayers = previousPlayers;\n        this._onDoneFns = [];\n        this._onStartFns = [];\n        this._onDestroyFns = [];\n        this._initialized = false;\n        this._finished = false;\n        this._started = false;\n        this._destroyed = false;\n        this.time = 0;\n        this.parentPlayer = null;\n        this.previousStyles = {};\n        this.currentSnapshot = {};\n        this._duration = /** @type {?} */ (options['duration']);\n        this._delay = /** @type {?} */ (options['delay']) || 0;\n        this.time = this._duration + this._delay;\n        if (allowPreviousPlayerStylesMerge(this._duration, this._delay)) {\n            previousPlayers.forEach(function (player) {\n                var /** @type {?} */ styles = player.currentSnapshot;\n                Object.keys(styles).forEach(function (prop) { return _this.previousStyles[prop] = styles[prop]; });\n            });\n        }\n    }\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._onFinish = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach(function (fn) { return fn(); });\n            this._onDoneFns = [];\n        }\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () {\n        this._buildPlayer();\n        this._preparePlayerBeforeStart();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._buildPlayer = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        if (this._initialized)\n            return;\n        this._initialized = true;\n        var /** @type {?} */ keyframes = this.keyframes.map(function (styles) { return copyStyles(styles, false); });\n        var /** @type {?} */ previousStyleProps = Object.keys(this.previousStyles);\n        if (previousStyleProps.length && keyframes.length) {\n            var /** @type {?} */ startingKeyframe_1 = keyframes[0];\n            var /** @type {?} */ missingStyleProps_1 = [];\n            previousStyleProps.forEach(function (prop) {\n                if (!startingKeyframe_1.hasOwnProperty(prop)) {\n                    missingStyleProps_1.push(prop);\n                }\n                startingKeyframe_1[prop] = _this.previousStyles[prop];\n            });\n            if (missingStyleProps_1.length) {\n                var /** @type {?} */ self_1 = this;\n                var _loop_1 = function () {\n                    var /** @type {?} */ kf = keyframes[i];\n                    missingStyleProps_1.forEach(function (prop) {\n                        kf[prop] = _computeStyle(self_1.element, prop);\n                    });\n                };\n                // tslint:disable-next-line\n                for (var /** @type {?} */ i = 1; i < keyframes.length; i++) {\n                    _loop_1();\n                }\n            }\n        }\n        (/** @type {?} */ (this)).domPlayer =\n            this._triggerWebAnimation(this.element, keyframes, this.options);\n        this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n        this.domPlayer.addEventListener('finish', function () { return _this._onFinish(); });\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._preparePlayerBeforeStart = /**\n     * @return {?}\n     */\n    function () {\n        // this is required so that the player doesn't start to animate right away\n        if (this._delay) {\n            this._resetDomPlayerState();\n        }\n        else {\n            this.domPlayer.pause();\n        }\n    };\n    /** @internal */\n    /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} options\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._triggerWebAnimation = /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} options\n     * @return {?}\n     */\n    function (element, keyframes, options) {\n        // jscompiler doesn't seem to know animate is a native property because it's not fully\n        // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n        return /** @type {?} */ (element['animate'](keyframes, options));\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.onStart = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onStartFns.push(fn); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.onDone = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDoneFns.push(fn); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.onDestroy = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDestroyFns.push(fn); };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () {\n        this._buildPlayer();\n        if (!this.hasStarted()) {\n            this._onStartFns.forEach(function (fn) { return fn(); });\n            this._onStartFns = [];\n            this._started = true;\n        }\n        this.domPlayer.play();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.pause = /**\n     * @return {?}\n     */\n    function () {\n        this.init();\n        this.domPlayer.pause();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () {\n        this.init();\n        this._onFinish();\n        this.domPlayer.finish();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.reset = /**\n     * @return {?}\n     */\n    function () {\n        this._resetDomPlayerState();\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._resetDomPlayerState = /**\n     * @return {?}\n     */\n    function () {\n        if (this.domPlayer) {\n            this.domPlayer.cancel();\n        }\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.restart = /**\n     * @return {?}\n     */\n    function () {\n        this.reset();\n        this.play();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this._started; };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._resetDomPlayerState();\n            this._onFinish();\n            this._onDestroyFns.forEach(function (fn) { return fn(); });\n            this._onDestroyFns = [];\n        }\n    };\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.setPosition = /**\n     * @param {?} p\n     * @return {?}\n     */\n    function (p) { this.domPlayer.currentTime = p * this.time; };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.getPosition = /**\n     * @return {?}\n     */\n    function () { return this.domPlayer.currentTime / this.time; };\n    Object.defineProperty(WebAnimationsPlayer.prototype, \"totalTime\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this._delay + this._duration; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.beforeDestroy = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        var /** @type {?} */ styles = {};\n        if (this.hasStarted()) {\n            Object.keys(this._finalKeyframe).forEach(function (prop) {\n                if (prop != 'offset') {\n                    styles[prop] =\n                        _this._finished ? _this._finalKeyframe[prop] : _computeStyle(_this.element, prop);\n                }\n            });\n        }\n        this.currentSnapshot = styles;\n    };\n    /* @internal */\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.triggerCallback = /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    function (phaseName) {\n        var /** @type {?} */ methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach(function (fn) { return fn(); });\n        methods.length = 0;\n    };\n    return WebAnimationsPlayer;\n}());\nexport { WebAnimationsPlayer };\nfunction WebAnimationsPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._onDoneFns;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._onStartFns;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._onDestroyFns;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._duration;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._delay;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._initialized;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._finished;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._started;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._destroyed;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype._finalKeyframe;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.domPlayer;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.time;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.parentPlayer;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.previousStyles;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.currentSnapshot;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.element;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.keyframes;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.options;\n    /** @type {?} */\n    WebAnimationsPlayer.prototype.previousPlayers;\n}\n/**\n * @param {?} element\n * @param {?} prop\n * @return {?}\n */\nfunction _computeStyle(element, prop) {\n    return (/** @type {?} */ (window.getComputedStyle(element)))[prop];\n}\n//# sourceMappingURL=web_animations_player.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { containsElement, invokeQuery, matchesElement, validateStyleProperty } from '../shared';\nimport { WebAnimationsPlayer } from './web_animations_player';\nvar WebAnimationsDriver = /** @class */ (function () {\n    function WebAnimationsDriver() {\n    }\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.validateStyleProperty = /**\n     * @param {?} prop\n     * @return {?}\n     */\n    function (prop) { return validateStyleProperty(prop); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.matchesElement = /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    function (element, selector) {\n        return matchesElement(element, selector);\n    };\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.containsElement = /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    function (elm1, elm2) { return containsElement(elm1, elm2); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.query = /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    function (element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    };\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.computeStyle = /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    function (element, prop, defaultValue) {\n        return /** @type {?} */ ((/** @type {?} */ (window.getComputedStyle(element)))[prop]);\n    };\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.animate = /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    function (element, keyframes, duration, delay, easing, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        var /** @type {?} */ fill = delay == 0 ? 'both' : 'forwards';\n        var /** @type {?} */ playerOptions = { duration: duration, delay: delay, fill: fill };\n        // we check for this to avoid having a null|undefined value be present\n        // for the easing (which results in an error for certain browsers #9752)\n        if (easing) {\n            playerOptions['easing'] = easing;\n        }\n        var /** @type {?} */ previousWebAnimationPlayers = /** @type {?} */ (previousPlayers.filter(function (player) { return player instanceof WebAnimationsPlayer; }));\n        return new WebAnimationsPlayer(element, keyframes, playerOptions, previousWebAnimationPlayers);\n    };\n    return WebAnimationsDriver;\n}());\nexport { WebAnimationsDriver };\n/**\n * @return {?}\n */\nexport function supportsWebAnimations() {\n    return typeof Element !== 'undefined' && typeof (/** @type {?} */ (Element)).prototype['animate'] === 'function';\n}\n//# sourceMappingURL=web_animations_driver.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nexport { Animation as ɵAnimation } from './dsl/animation';\nexport { AnimationStyleNormalizer as ɵAnimationStyleNormalizer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer } from './dsl/style_normalization/animation_style_normalizer';\nexport { WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer } from './dsl/style_normalization/web_animations_style_normalizer';\nexport { NoopAnimationDriver as ɵNoopAnimationDriver } from './render/animation_driver';\nexport { AnimationEngine as ɵAnimationEngine } from './render/animation_engine_next';\nexport { WebAnimationsDriver as ɵWebAnimationsDriver, supportsWebAnimations as ɵsupportsWebAnimations } from './render/web_animations/web_animations_driver';\nexport { WebAnimationsPlayer as ɵWebAnimationsPlayer } from './render/web_animations/web_animations_player';\n//# sourceMappingURL=private_export.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport { AnimationDriver } from './render/animation_driver';\nexport { ɵAnimation, ɵAnimationStyleNormalizer, ɵNoopAnimationStyleNormalizer, ɵWebAnimationsStyleNormalizer, ɵNoopAnimationDriver, ɵAnimationEngine, ɵWebAnimationsDriver, ɵsupportsWebAnimations, ɵWebAnimationsPlayer } from './private_export';\n//# sourceMappingURL=browser.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport { AnimationDriver, ɵAnimation, ɵAnimationStyleNormalizer, ɵNoopAnimationStyleNormalizer, ɵWebAnimationsStyleNormalizer, ɵNoopAnimationDriver, ɵAnimationEngine, ɵWebAnimationsDriver, ɵsupportsWebAnimations, ɵWebAnimationsPlayer } from './src/browser';\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { AnimationDriver, ɵAnimation, ɵAnimationStyleNormalizer, ɵNoopAnimationStyleNormalizer, ɵWebAnimationsStyleNormalizer, ɵNoopAnimationDriver, ɵAnimationEngine, ɵWebAnimationsDriver, ɵsupportsWebAnimations, ɵWebAnimationsPlayer } from './public_api';\n//# sourceMappingURL=browser.js.map"], "names": ["PRE_STYLE", "style", "tslib_1.__extends", "tslib_1.__assign", "AnimationGroupPlayer"], "mappings": ";;;;;;;;AAAA;;;;AAIA,AACA;;;;AAIA,AAAO,SAAS,mBAAmB,CAAC,OAAO,EAAE;IACzC,QAAQ,OAAO,CAAC,MAAM;QAClB,KAAK,CAAC;YACF,OAAO,IAAI,mBAAmB,EAAE,CAAC;QACrC,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;QACtB;YACI,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;KACjD;CACJ;;;;;;;;;;AAUD,AAAO,SAAS,kBAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE;IAC9F,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,EAAE,CAAC,EAAE;IAC7C,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC,EAAE;IAC/C,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,qBAAqB,mBAAmB,GAAG,EAAE,CAAC;IAC9C,qBAAqB,cAAc,GAAG,CAAC,CAAC,CAAC;IACzC,qBAAqB,gBAAgB,GAAG,IAAI,CAAC;IAC7C,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;QAC5B,qBAAqB,MAAM,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9D,qBAAqB,YAAY,GAAG,MAAM,IAAI,cAAc,CAAC;QAC7D,qBAAqB,kBAAkB,GAAG,CAAC,YAAY,IAAI,gBAAgB,KAAK,EAAE,CAAC;QACnF,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACpC,qBAAqB,cAAc,GAAG,IAAI,CAAC;YAC3C,qBAAqB,eAAe,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACnB,cAAc,GAAG,UAAU,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAC1E,QAAQ,eAAe;oBACnB,KAAKA,UAAS;wBACV,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM;oBACV,KAAK,UAAU;wBACX,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;wBACnC,MAAM;oBACV;wBACI,eAAe;4BACX,UAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;wBAClF,MAAM;iBACb;aACJ;YACD,kBAAkB,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC;SACxD,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE;YACf,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAChD;QACD,gBAAgB,GAAG,kBAAkB,CAAC;QACtC,cAAc,GAAG,MAAM,CAAC;KAC3B,CAAC,CAAC;IACH,IAAI,MAAM,CAAC,MAAM,EAAE;QACf,qBAAqB,UAAU,GAAG,OAAO,CAAC;QAC1C,MAAM,IAAI,KAAK,CAAC,gDAAgD,GAAG,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;KAC5G;IACD,OAAO,mBAAmB,CAAC;CAC9B;;;;;;;;AAQD,AAAO,SAAS,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC/D,QAAQ,SAAS;QACb,KAAK,OAAO;YACR,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAChH,MAAM;QACV,KAAK,MAAM;YACP,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9G,MAAM;QACV,KAAK,SAAS;YACV,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpH,MAAM;KACb;CACJ;;;;;;;AAOD,AAAO,SAAS,kBAAkB,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE;IACxD,qBAAqB,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;IAC9K,qBAAqB,IAAI,GAAG,mBAAmB,CAAC,GAAG,OAAO,CAAC,CAAC;IAC5D,IAAI,IAAI,IAAI,IAAI,EAAE;QACd,mBAAmB,KAAK,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC;CAChB;;;;;;;;;;AAUD,AAAO,SAAS,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE;IAC/F,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,EAAE,CAAC,EAAE;IAC7C,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,CAAC,CAAC,EAAE;IAC5C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;CAC7I;;;;;;;AAOD,AAAO,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE;IACpD,qBAAqB,KAAK,CAAC;IAC3B,IAAI,GAAG,YAAY,GAAG,EAAE;QACpB,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,KAAK,EAAE;YACR,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;SACtC;KACJ;SACI;QACD,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;SACnC;KACJ;IACD,OAAO,KAAK,CAAC;CAChB;;;;;AAKD,AAAO,SAAS,oBAAoB,CAAC,OAAO,EAAE;IAC1C,qBAAqB,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACzD,qBAAqB,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IAC7D,qBAAqB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAC/D,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;CACvB;AACD,IAAqB,SAAS,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;AACzE,AACA,IAAqB,QAAQ,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE;IACzD,OAAO,KAAK,CAAC;CAChB,CAAC;AACF,AACA,IAAqB,MAAM,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;IAC9D,OAAO,EAAE,CAAC;CACb,CAAC;AACF,AACA,IAAI,OAAO,OAAO,IAAI,WAAW,EAAE;;IAE/B,SAAS,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,yBAAyB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACrF,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC3B,QAAQ,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;KACjF;SACI;QACD,qBAAqB,KAAK,qBAAqB,OAAO,CAAC,SAAS,CAAC,CAAC;QAClE,qBAAqB,IAAI,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,iBAAiB;YACpG,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,qBAAqB,CAAC;QAC1D,IAAI,IAAI,EAAE;YACN,QAAQ,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;SACvF;KACJ;IACD,MAAM,GAAG,UAAU,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QACzC,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,KAAK,EAAE;YACP,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACnE;aACI;YACD,qBAAqB,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC3D,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrB;SACJ;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;CACL;;;;;AAKD,SAAS,oBAAoB,CAAC,IAAI,EAAE;;;IAGhC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC;CAC1C;AACD,IAAqB,YAAY,GAAG,IAAI,CAAC;AACzC,IAAqB,UAAU,GAAG,KAAK,CAAC;;;;;AAKxC,AAAO,SAAS,qBAAqB,CAAC,IAAI,EAAE;IACxC,IAAI,CAAC,YAAY,EAAE;QACf,YAAY,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC;QACnC,UAAU,oBAAoB,EAAE,YAAY,GAAG,KAAK,IAAI,kBAAkB,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,KAAK,CAAC;KAClI;IACD,qBAAqB,MAAM,GAAG,IAAI,CAAC;IACnC,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;QACxE,MAAM,GAAG,IAAI,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;QACzD,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;YACvB,qBAAqB,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1F,MAAM,GAAG,SAAS,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;SACjE;KACJ;IACD,OAAO,MAAM,CAAC;CACjB;;;;AAID,AAAO,SAAS,WAAW,GAAG;IAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;QAChC,OAAO,QAAQ,CAAC,IAAI,CAAC;KACxB;IACD,OAAO,IAAI,CAAC;CACf;AACD,AAAO,IAAqB,cAAc,GAAG,QAAQ,CAAC;AACtD,AAAO,IAAqB,eAAe,GAAG,SAAS,CAAC;AACxD,AAAO,IAAqB,WAAW,GAAG,MAAM;;ACtOhD;;;;AAIA,AAEA;;;AAGA,IAGA,mBAAmB,kBAAkB,YAAY;IAC7C,SAAS,mBAAmB,GAAG;KAC9B;;;;;IAKD,mBAAmB,CAAC,SAAS,CAAC,qBAAqB;;;;IAInD,UAAU,IAAI,EAAE,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;;;;;;IAMxD,mBAAmB,CAAC,SAAS,CAAC,cAAc;;;;;IAK5C,UAAU,OAAO,EAAE,QAAQ,EAAE;QACzB,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC5C,CAAC;;;;;;IAMF,mBAAmB,CAAC,SAAS,CAAC,eAAe;;;;;IAK7C,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;;;;;;;IAO9D,mBAAmB,CAAC,SAAS,CAAC,KAAK;;;;;;IAMnC,UAAU,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAChC,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAChD,CAAC;;;;;;;IAOF,mBAAmB,CAAC,SAAS,CAAC,YAAY;;;;;;IAM1C,UAAU,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE;QACnC,OAAO,YAAY,IAAI,EAAE,CAAC;KAC7B,CAAC;;;;;;;;;;IAUF,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;;;;;;;IASrC,UAAU,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE;QACpE,IAAI,eAAe,KAAK,KAAK,CAAC,EAAE,EAAE,eAAe,GAAG,EAAE,CAAC,EAAE;QACzD,OAAO,IAAI,mBAAmB,EAAE,CAAC;KACpC,CAAC;IACF,OAAO,mBAAmB,CAAC;CAC9B,EAAE,CAAC,CAAC;AACL,AAIA;;;;AAIA,IAAI,eAAe,kBAAkB,YAAY;IAC7C,SAAS,eAAe,GAAG;KAC1B;IACD,eAAe,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;IACjD,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC;;ACnHJ;;;;AAIA,AACO,IAAqB,UAAU,GAAG,IAAI,CAAC;AAC9C,AAAO,IAAqB,uBAAuB,GAAG,IAAI,CAAC;AAC3D,AAAO,IAAqB,qBAAqB,GAAG,IAAI,CAAC;AACzD,AAAO,IAAqB,eAAe,GAAG,UAAU,CAAC;AACzD,AAAO,IAAqB,eAAe,GAAG,UAAU,CAAC;AACzD,AAAyD;AACzD,AAAyD;AACzD,AAAO,IAAqB,oBAAoB,GAAG,YAAY,CAAC;AAChE,AAAO,IAAqB,mBAAmB,GAAG,aAAa,CAAC;AAChE,AAAO,IAAqB,sBAAsB,GAAG,cAAc,CAAC;AACpE,AAAO,IAAqB,qBAAqB,GAAG,eAAe,CAAC;;;;;AAKpE,AAAO,SAAS,kBAAkB,CAAC,KAAK,EAAE;IACtC,IAAI,OAAO,KAAK,IAAI,QAAQ;QACxB,OAAO,KAAK,CAAC;IACjB,qBAAqB,OAAO,GAAG,mBAAmB,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACrF,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;QAC9B,OAAO,CAAC,CAAC;IACb,OAAO,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;CACpE;;;;;;AAMD,SAAS,qBAAqB,CAAC,KAAK,EAAE,IAAI,EAAE;IACxC,QAAQ,IAAI;QACR,KAAK,GAAG;YACJ,OAAO,KAAK,GAAG,UAAU,CAAC;QAC9B;;YAEI,OAAO,KAAK,CAAC;KACpB;CACJ;;;;;;;AAOD,AAAO,SAAS,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE;IAChE,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,qBAAqB,OAAO;QACjE,mBAAmB,mBAAmB,OAAO,GAAG,MAAM,EAAE,mBAAmB,CAAC,CAAC;CACpF;;;;;;;AAOD,SAAS,mBAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,mBAAmB,EAAE;IAC3D,qBAAqB,KAAK,GAAG,0EAA0E,CAAC;IACxG,qBAAqB,QAAQ,CAAC;IAC9B,qBAAqB,KAAK,GAAG,CAAC,CAAC;IAC/B,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACzB,qBAAqB,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,OAAO,KAAK,IAAI,EAAE;YAClB,MAAM,CAAC,IAAI,CAAC,8BAA8B,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC;YACrE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;SAChD;QACD,QAAQ,GAAG,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,qBAAqB,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,UAAU,IAAI,IAAI,EAAE;YACpB,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACjF;QACD,qBAAqB,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,SAAS,EAAE;YACX,MAAM,GAAG,SAAS,CAAC;SACtB;KACJ;SACI;QACD,QAAQ,qBAAqB,GAAG,CAAC,CAAC;KACrC;IACD,IAAI,CAAC,mBAAmB,EAAE;QACtB,qBAAqB,cAAc,GAAG,KAAK,CAAC;QAC5C,qBAAqB,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;QAChD,IAAI,QAAQ,GAAG,CAAC,EAAE;YACd,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;YAChF,cAAc,GAAG,IAAI,CAAC;SACzB;QACD,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;YAC7E,cAAc,GAAG,IAAI,CAAC;SACzB;QACD,IAAI,cAAc,EAAE;YAChB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,8BAA8B,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC;SACzF;KACJ;IACD,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAC/D;;;;;;AAMD,AAAO,SAAS,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE;IACtC,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;IACjD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7E,OAAO,WAAW,CAAC;CACtB;;;;;AAKD,AAAO,SAAS,eAAe,CAAC,MAAM,EAAE;IACpC,qBAAqB,gBAAgB,GAAG,EAAE,CAAC;IAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;KACzF;SACI;QACD,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;KAC/C;IACD,OAAO,gBAAgB,CAAC;CAC3B;;;;;;;AAOD,AAAO,SAAS,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE;IAC3D,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;IACjD,IAAI,aAAa,EAAE;;;;QAIf,KAAK,qBAAqB,IAAI,IAAI,MAAM,EAAE;YACtC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SACpC;KACJ;SACI;QACD,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;KAChC;IACD,OAAO,WAAW,CAAC;CACtB;;;;;;AAMD,AAAO,SAAS,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE;IACvC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACxC,qBAAqB,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC3D,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SAC3C,CAAC,CAAC;KACN;CACJ;;;;;;AAMD,AAAO,SAAS,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE;IACzC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACxC,qBAAqB,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC3D,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;SACjC,CAAC,CAAC;KACN;CACJ;;;;;AAKD,AAAO,SAAS,uBAAuB,CAAC,KAAK,EAAE;IAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACtB,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;YACjB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QACpB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC1B;IACD,yBAAyB,KAAK,EAAE;CACnC;;;;;;;AAOD,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE;IACxD,qBAAqB,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;IACnD,qBAAqB,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACzD,IAAI,OAAO,CAAC,MAAM,EAAE;QAChB,OAAO,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBACjC,MAAM,CAAC,IAAI,CAAC,8CAA8C,GAAG,OAAO,GAAG,8BAA8B,CAAC,CAAC;aAC1G;SACJ,CAAC,CAAC;KACN;CACJ;AACD,IAAqB,WAAW,GAAG,IAAI,MAAM,CAAC,uBAAuB,GAAG,eAAe,GAAG,qBAAqB,EAAE,GAAG,CAAC,CAAC;;;;;AAKtH,AAAO,SAAS,kBAAkB,CAAC,KAAK,EAAE;IACtC,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,qBAAqB,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC5C,qBAAqB,KAAK,GAAG,KAAK,CAAC,CAAC;QACpC,OAAO,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClC,MAAM,CAAC,IAAI,mBAAmB,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;SAC5C;QACD,WAAW,CAAC,SAAS,GAAG,CAAC,CAAC;KAC7B;IACD,OAAO,MAAM,CAAC;CACjB;;;;;;;AAOD,AAAO,SAAS,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;IACrD,qBAAqB,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;IACjD,qBAAqB,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,OAAO,EAAE;QAC3E,qBAAqB,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;;QAEhD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,iDAAiD,GAAG,OAAO,CAAC,CAAC;YACzE,QAAQ,GAAG,EAAE,CAAC;SACjB;QACD,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;KAC9B,CAAC,CAAC;;IAEH,OAAO,GAAG,IAAI,QAAQ,GAAG,KAAK,GAAG,GAAG,CAAC;CACxC;;;;;AAKD,AAAO,SAAS,eAAe,CAAC,QAAQ,EAAE;IACtC,qBAAqB,GAAG,GAAG,EAAE,CAAC;IAC9B,qBAAqB,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC5C,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;QACf,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC1B;IACD,OAAO,GAAG,CAAC;CACd;;;;;;AAMD,AAcC;AACD,IAAqB,gBAAgB,GAAG,eAAe,CAAC;;;;;AAKxD,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;IACvC,OAAO,KAAK,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY;QAC/C,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YAC1C,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;SACzB;QACD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;KAC7B,CAAC,CAAC;CACN;;;;;;AAMD,AAAO,SAAS,8BAA8B,CAAC,QAAQ,EAAE,KAAK,EAAE;IAC5D,OAAO,QAAQ,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;CACxC;;;;;;;AAOD,AAAO,SAAS,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;IACjD,QAAQ,IAAI,CAAC,IAAI;QACb,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC;YACF,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACpD,KAAK,EAAE;YACH,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClD,KAAK,EAAE;YACH,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,EAAE;YACH,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C;YACI,MAAM,IAAI,KAAK,CAAC,6CAA6C,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;KAClF;CACJ;;ACxUD;;;;;;;;;;;AAWA,AAAO,IAAqB,SAAS,GAAG,GAAG,CAAC;;;;;;AAM5C,AAAO,SAAS,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAE;IACzD,qBAAqB,WAAW,GAAG,EAAE,CAAC;IACtC,IAAI,OAAO,eAAe,IAAI,QAAQ,EAAE;QACpC,mBAAmB,eAAe;aAC7B,KAAK,CAAC,SAAS,CAAC;aAChB,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,uBAAuB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;KAC9F;SACI;QACD,WAAW,CAAC,IAAI,mBAAmB,eAAe,EAAE,CAAC;KACxD;IACD,OAAO,WAAW,CAAC;CACtB;;;;;;;AAOD,SAAS,uBAAuB,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE;IAC5D,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;QACpB,qBAAqB,MAAM,GAAG,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACpE,IAAI,OAAO,MAAM,IAAI,UAAU,EAAE;YAC7B,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,OAAO;SACV;QACD,QAAQ,qBAAqB,MAAM,CAAC,CAAC;KACxC;IACD,qBAAqB,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;IACvF,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACnC,MAAM,CAAC,IAAI,CAAC,uCAAuC,GAAG,QAAQ,GAAG,qBAAqB,CAAC,CAAC;QACxF,OAAO,WAAW,CAAC;KACtB;IACD,qBAAqB,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1C,qBAAqB,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1C,qBAAqB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACxC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IAC3D,qBAAqB,kBAAkB,GAAG,SAAS,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,CAAC;IACzF,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE;QAC5C,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;KAC9D;CACJ;;;;;;AAMD,SAAS,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE;IACxC,QAAQ,KAAK;QACT,KAAK,QAAQ;YACT,OAAO,WAAW,CAAC;QACvB,KAAK,QAAQ;YACT,OAAO,WAAW,CAAC;QACvB,KAAK,YAAY;YACb,OAAO,UAAU,SAAS,EAAE,OAAO,EAAE,EAAE,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;QACjG,KAAK,YAAY;YACb,OAAO,UAAU,SAAS,EAAE,OAAO,EAAE,EAAE,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;QACjG;YACI,MAAM,CAAC,IAAI,CAAC,+BAA+B,GAAG,KAAK,GAAG,qBAAqB,CAAC,CAAC;YAC7E,OAAO,QAAQ,CAAC;KACvB;CACJ;;;;;AAKD,IAAqB,mBAAmB,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AAClE,IAAqB,oBAAoB,GAAG,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;;;;;;AAMpE,SAAS,oBAAoB,CAAC,GAAG,EAAE,GAAG,EAAE;IACpC,qBAAqB,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACvG,qBAAqB,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACvG,OAAO,UAAU,SAAS,EAAE,OAAO,EAAE;QACjC,qBAAqB,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS,CAAC;QACrE,qBAAqB,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,OAAO,CAAC;QACnE,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;YAClE,QAAQ,GAAG,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACvF;QACD,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAChE,QAAQ,GAAG,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACrF;QACD,OAAO,QAAQ,IAAI,QAAQ,CAAC;KAC/B,CAAC;CACL;;ACvGD;;;;AAIA,AAIA,IAAqB,UAAU,GAAG,OAAO,CAAC;AAC1C,IAAqB,gBAAgB,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,UAAU,GAAG,MAAM,EAAE,GAAG,CAAC,CAAC;;;;;;;AAOpF,AAAO,SAAS,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE;IACxD,OAAO,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;CACzE;AACD,IAAqB,aAAa,GAAG,EAAE,CAAC;AACxC,IAAI,0BAA0B,kBAAkB,YAAY;IACxD,SAAS,0BAA0B,CAAC,OAAO,EAAE;QACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;;;;;;IAMD,0BAA0B,CAAC,SAAS,CAAC,KAAK;;;;;IAK1C,UAAU,QAAQ,EAAE,MAAM,EAAE;QACxB,qBAAqB,OAAO,GAAG,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACtE,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;QAC5C,yBAAyB,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,EAAE;KAC5F,CAAC;;;;;IAKF,0BAA0B,CAAC,SAAS,CAAC,6BAA6B;;;;IAIlE,UAAU,OAAO,EAAE;QACf,OAAO,CAAC,oBAAoB,GAAG,aAAa,CAAC;QAC7C,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;QAC7B,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;QAC5C,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;KAC3B,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,YAAY;;;;;IAKjD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;QACzD,qBAAqB,QAAQ,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrD,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,qBAAqB,WAAW,GAAG,EAAE,CAAC;QACtC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAChC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;SACjH;QACD,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;YACxC,KAAK,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;YAC7C,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc;gBAC3B,qBAAqB,UAAU,qBAAqB,GAAG,CAAC,CAAC;gBACzD,qBAAqB,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;oBACzC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;oBACpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;iBACtD,CAAC,CAAC;gBACH,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC;aAC5B;iBACI,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,mBAAmB;gBACrC,qBAAqB,UAAU,GAAG,KAAK,CAAC,eAAe,mBAAmB,GAAG,GAAG,OAAO,CAAC,CAAC;gBACzF,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC;gBACpC,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC;gBAChC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC;iBACI;gBACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;aAClG;SACJ,CAAC,CAAC;QACH,OAAO;YACH,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;YACzG,OAAO,EAAE,IAAI;SAChB,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,UAAU;;;;;IAK/C,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,qBAAqB,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1E,qBAAqB,SAAS,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;QACvF,IAAI,QAAQ,CAAC,qBAAqB,EAAE;YAChC,qBAAqB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;YAC/C,qBAAqB,QAAQ,GAAG,SAAS,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;gBACrC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;oBACjB,qBAAqB,WAAW,qBAAqB,KAAK,CAAC,CAAC;oBAC5D,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;wBAC7C,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;4BACzD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gCAC/B,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;6BAC1B;yBACJ,CAAC,CAAC;qBACN,CAAC,CAAC;iBACN;aACJ,CAAC,CAAC;YACH,IAAI,aAAa,CAAC,IAAI,EAAE;gBACpB,qBAAqB,cAAc,GAAG,eAAe,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC9E,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI,GAAG,iFAAiF,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aACnK;SACJ;QACD,OAAO;YACH,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,SAAS,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI;SACpD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,eAAe;;;;;IAKpD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;QACvB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrB,qBAAqB,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;QAC1G,qBAAqB,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACnF,OAAO;YACH,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,aAAa;;;;;IAKlD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO;YACH,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC;YACnF,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,UAAU;;;;;IAK/C,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvD,qBAAqB,YAAY,GAAG,CAAC,CAAC;QACtC,qBAAqB,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;YAC5D,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;YAClC,qBAAqB,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACnE,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;YAC3D,OAAO,QAAQ,CAAC;SACnB,CAAC,CAAC;QACH,OAAO,CAAC,WAAW,GAAG,YAAY,CAAC;QACnC,OAAO;YACH,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,YAAY;;;;;IAKjD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,qBAAqB,SAAS,GAAG,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACtF,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;QAC1C,qBAAqB,QAAQ,CAAC;QAC9B,qBAAqB,aAAa,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;QACnF,IAAI,aAAa,CAAC,IAAI,IAAI,CAAC,kBAAkB;YACzC,QAAQ,GAAG,IAAI,CAAC,cAAc,mBAAmB,aAAa,GAAG,OAAO,CAAC,CAAC;SAC7E;aACI;YACD,qBAAqB,eAAe,qBAAqB,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1E,qBAAqB,OAAO,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,eAAe,EAAE;gBAClB,OAAO,GAAG,IAAI,CAAC;gBACf,qBAAqB,YAAY,GAAG,EAAE,CAAC;gBACvC,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClB,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;iBAC7C;gBACD,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;aACzC;YACD,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;YAC5D,qBAAqB,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAC3E,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;YAChC,QAAQ,GAAG,SAAS,CAAC;SACxB;QACD,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACrC,OAAO;YACH,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,IAAI;SAChB,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,UAAU;;;;;IAK/C,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,qBAAqB,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACrC,OAAO,GAAG,CAAC;KACd,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,aAAa;;;;;IAKlD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAChC,mBAAmB,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,UAAU,EAAE;gBAC/D,IAAI,OAAO,UAAU,IAAI,QAAQ,EAAE;oBAC/B,IAAI,UAAU,IAAI,UAAU,EAAE;wBAC1B,MAAM,CAAC,IAAI,mBAAmB,UAAU,EAAE,CAAC;qBAC9C;yBACI;wBACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,GAAG,UAAU,GAAG,kBAAkB,CAAC,CAAC;qBAC7F;iBACJ;qBACI;oBACD,MAAM,CAAC,IAAI,mBAAmB,UAAU,EAAE,CAAC;iBAC9C;aACJ,CAAC,CAAC;SACN;aACI;YACD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SAChC;QACD,qBAAqB,qBAAqB,GAAG,KAAK,CAAC;QACnD,qBAAqB,eAAe,GAAG,IAAI,CAAC;QAC5C,MAAM,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;YAChC,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;gBACrB,qBAAqB,QAAQ,qBAAqB,SAAS,CAAC,CAAC;gBAC7D,qBAAqB,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACjD,IAAI,MAAM,EAAE;oBACR,eAAe,qBAAqB,MAAM,CAAC,CAAC;oBAC5C,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBAC7B;gBACD,IAAI,CAAC,qBAAqB,EAAE;oBACxB,KAAK,qBAAqB,IAAI,IAAI,QAAQ,EAAE;wBACxC,qBAAqB,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAC5C,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;4BACxD,qBAAqB,GAAG,IAAI,CAAC;4BAC7B,MAAM;yBACT;qBACJ;iBACJ;aACJ;SACJ,CAAC,CAAC;QACH,OAAO;YACH,IAAI,EAAE,CAAC;YACP,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,qBAAqB,EAAE,qBAAqB;YACrE,OAAO,EAAE,IAAI;SAChB,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,iBAAiB;;;;;IAKtD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAC7D,qBAAqB,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC;QACnD,qBAAqB,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC;QACrD,IAAI,OAAO,IAAI,SAAS,GAAG,CAAC,EAAE;YAC1B,SAAS,IAAI,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;SACjD;QACD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;YAChC,IAAI,OAAO,KAAK,IAAI,QAAQ;gBACxB,OAAO;YACX,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBACvC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;oBAC5C,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,GAAG,IAAI,GAAG,mDAAmD,CAAC,CAAC;oBACvH,OAAO;iBACV;gBACD,qBAAqB,eAAe,GAAG,OAAO,CAAC,eAAe,oBAAoB,OAAO,CAAC,oBAAoB,GAAG,CAAC;gBAClH,qBAAqB,cAAc,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC5D,qBAAqB,oBAAoB,GAAG,IAAI,CAAC;gBACjD,IAAI,cAAc,EAAE;oBAChB,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,cAAc,CAAC,SAAS;wBAC7D,OAAO,IAAI,cAAc,CAAC,OAAO,EAAE;wBACnC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,wCAAwC,GAAG,cAAc,CAAC,SAAS,GAAG,aAAa,GAAG,cAAc,CAAC,OAAO,GAAG,6EAA6E,GAAG,SAAS,GAAG,aAAa,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC;wBAChS,oBAAoB,GAAG,KAAK,CAAC;qBAChC;;;;oBAID,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;iBACxC;gBACD,IAAI,oBAAoB,EAAE;oBACtB,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;iBACtE;gBACD,IAAI,OAAO,CAAC,OAAO,EAAE;oBACjB,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;iBACrE;aACJ,CAAC,CAAC;SACN,CAAC,CAAC;KACN,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,cAAc;;;;;IAKnD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC,kBAAkB,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAClF,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YAChC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;YAChF,OAAO,GAAG,CAAC;SACd;QACD,qBAAqB,mBAAmB,GAAG,CAAC,CAAC;QAC7C,qBAAqB,yBAAyB,GAAG,CAAC,CAAC;QACnD,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,qBAAqB,iBAAiB,GAAG,KAAK,CAAC;QAC/C,qBAAqB,mBAAmB,GAAG,KAAK,CAAC;QACjD,qBAAqB,cAAc,GAAG,CAAC,CAAC;QACxC,qBAAqB,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE;YAClE,qBAAqBC,QAAK,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAClE,qBAAqB,SAAS,GAAGA,QAAK,CAAC,MAAM,IAAI,IAAI,GAAGA,QAAK,CAAC,MAAM,GAAG,aAAa,CAACA,QAAK,CAAC,MAAM,CAAC,CAAC;YACnG,qBAAqB,MAAM,GAAG,CAAC,CAAC;YAChC,IAAI,SAAS,IAAI,IAAI,EAAE;gBACnB,yBAAyB,EAAE,CAAC;gBAC5B,MAAM,GAAGA,QAAK,CAAC,MAAM,GAAG,SAAS,CAAC;aACrC;YACD,mBAAmB,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;YACtE,iBAAiB,GAAG,iBAAiB,IAAI,MAAM,GAAG,cAAc,CAAC;YACjE,cAAc,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,OAAOA,QAAK,CAAC;SAChB,CAAC,CAAC;QACH,IAAI,mBAAmB,EAAE;YACrB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;SACtF;QACD,IAAI,iBAAiB,EAAE;YACnB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;SAC/E;QACD,qBAAqB,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QACpD,qBAAqB,eAAe,GAAG,CAAC,CAAC;QACzC,IAAI,yBAAyB,GAAG,CAAC,IAAI,yBAAyB,GAAG,MAAM,EAAE;YACrE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;SAChG;aACI,IAAI,yBAAyB,IAAI,CAAC,EAAE;YACrC,eAAe,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;SACxD;QACD,qBAAqB,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;QACxC,qBAAqB,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvD,qBAAqB,qBAAqB,sBAAsB,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAChG,qBAAqB,eAAe,GAAG,qBAAqB,CAAC,QAAQ,CAAC;QACtE,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE;YAC/B,qBAAqB,MAAM,GAAG,eAAe,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1G,qBAAqB,qBAAqB,GAAG,MAAM,GAAG,eAAe,CAAC;YACtE,OAAO,CAAC,WAAW,GAAG,WAAW,GAAG,qBAAqB,CAAC,KAAK,GAAG,qBAAqB,CAAC;YACxF,qBAAqB,CAAC,QAAQ,GAAG,qBAAqB,CAAC;YACvD,KAAK,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YACrC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;YACnB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACvB,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;KACd,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,cAAc;;;;;IAKnD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,OAAO;YACH,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;YACnF,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,iBAAiB;;;;;IAKtD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,OAAO,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO;YACH,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,eAAe;;;;;IAKpD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,OAAO;YACH,IAAI,EAAE,EAAE;YACR,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC;YAC3D,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,UAAU;;;;;IAK/C,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,qBAAqB,cAAc,sBAAsB,OAAO,CAAC,oBAAoB,EAAE,CAAC;QACxF,qBAAqB,OAAO,sBAAsB,QAAQ,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,CAAC;QACrB,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;QAChC,IAAI,EAAE,GAAG,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrF,OAAO,CAAC,oBAAoB;YACxB,cAAc,CAAC,MAAM,IAAI,cAAc,GAAG,GAAG,GAAG,QAAQ,IAAI,QAAQ,CAAC;QACzE,eAAe,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;QAC3E,qBAAqB,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;QAC1G,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;QAC5B,OAAO,CAAC,oBAAoB,GAAG,cAAc,CAAC;QAC9C,OAAO;YACH,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YACzB,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS;YAC5E,gBAAgB,EAAE,QAAQ,CAAC,QAAQ;YACnC,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACvD,CAAC;KACL,CAAC;;;;;;IAMF,0BAA0B,CAAC,SAAS,CAAC,YAAY;;;;;IAKjD,UAAU,QAAQ,EAAE,OAAO,EAAE;QACzB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YACvB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;SACvE;QACD,qBAAqB,OAAO,GAAG,QAAQ,CAAC,OAAO,KAAK,MAAM;YACtD,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE;YACzC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1D,OAAO;YACH,IAAI,EAAE,EAAE;YACR,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO;YACrG,OAAO,EAAE,IAAI;SAChB,CAAC;KACL,CAAC;IACF,OAAO,0BAA0B,CAAC;CACrC,EAAE,CAAC,CAAC;AACL,AAKA;;;;AAIA,SAAS,iBAAiB,CAAC,QAAQ,EAAE;IACjC,qBAAqB,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;IACpI,IAAI,YAAY,EAAE;QACd,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;KACrD;;IAED,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,mBAAmB,CAAC;SACnD,OAAO,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE,EAAE,OAAO,mBAAmB,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SAC1F,OAAO,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;IACnD,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;CACnC;;;;;AAKD,SAAS,eAAe,CAAC,GAAG,EAAE;IAC1B,OAAO,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;CACpC;AACD,IAAI,0BAA0B,kBAAkB,YAAY;IACxD,SAAS,0BAA0B,CAAC,MAAM,EAAE;QACxC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;IACD,OAAO,0BAA0B,CAAC;CACrC,EAAE,CAAC,CAAC;AACL,AAuBA;;;;AAIA,SAAS,aAAa,CAAC,MAAM,EAAE;IAC3B,IAAI,OAAO,MAAM,IAAI,QAAQ;QACzB,OAAO,IAAI,CAAC;IAChB,qBAAqB,MAAM,GAAG,IAAI,CAAC;IACnC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,MAAM,CAAC,OAAO,CAAC,UAAU,UAAU,EAAE;YACjC,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC7D,qBAAqB,GAAG,qBAAqB,UAAU,CAAC,CAAC;gBACzD,MAAM,GAAG,UAAU,mBAAmB,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtD,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;aACxB;SACJ,CAAC,CAAC;KACN;SACI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;QAC1D,qBAAqB,GAAG,qBAAqB,MAAM,CAAC,CAAC;QACrD,MAAM,GAAG,UAAU,mBAAmB,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;QACtD,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;KACxB;IACD,OAAO,MAAM,CAAC;CACjB;;;;;AAKD,SAAS,QAAQ,CAAC,KAAK,EAAE;IACrB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,IAAI,QAAQ,CAAC;CAC5D;;;;;;AAMD,SAAS,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE;IACvC,qBAAqB,OAAO,GAAG,IAAI,CAAC;IACpC,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;QAClC,OAAO,qBAAqB,KAAK,CAAC,CAAC;KACtC;SACI,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;QAC/B,qBAAqB,QAAQ,GAAG,aAAa,mBAAmB,KAAK,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC;QACzF,OAAO,aAAa,mBAAmB,QAAQ,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;KAC5D;IACD,qBAAqB,QAAQ,qBAAqB,KAAK,CAAC,CAAC;IACzD,qBAAqB,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/H,IAAI,SAAS,EAAE;QACX,qBAAqB,GAAG,qBAAqB,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACtE,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;QACnB,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxB,yBAAyB,GAAG,EAAE;KACjC;IACD,OAAO,GAAG,OAAO,IAAI,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACrD,OAAO,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;CACzE;;;;;AAKD,SAAS,yBAAyB,CAAC,OAAO,EAAE;IACxC,IAAI,OAAO,EAAE;QACT,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;YACnB,OAAO,CAAC,QAAQ,CAAC,sBAAsB,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;SAC/E;KACJ;SACI;QACD,OAAO,GAAG,EAAE,CAAC;KAChB;IACD,OAAO,OAAO,CAAC;CAClB;;;;;;;AAOD,SAAS,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;IAC5C,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAC/D;;ACnrBD;;;;;;;AAOA,AAAkD;AAClD,AAsBA;;;;;;;;;;;AAWA,AAAO,SAAS,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;IAC/H,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,EAAE;IACzC,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE;IACpD,OAAO;QACH,IAAI,EAAE,CAAC;QACP,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,aAAa;QAC5B,cAAc,EAAE,cAAc;QAC9B,QAAQ,EAAE,QAAQ;QAClB,KAAK,EAAE,KAAK;QACZ,SAAS,EAAE,QAAQ,GAAG,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW;KACxE,CAAC;CACL;;ACtDD;;;;AAIA,IAAI,qBAAqB,kBAAkB,YAAY;IACnD,SAAS,qBAAqB,GAAG;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;KACzB;;;;;IAKD,qBAAqB,CAAC,SAAS,CAAC,OAAO;;;;IAIvC,UAAU,OAAO,EAAE;QACf,qBAAqB,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC7B;aACI;YACD,YAAY,GAAG,EAAE,CAAC;SACrB;QACD,OAAO,YAAY,CAAC;KACvB,CAAC;;;;;;IAMF,qBAAqB,CAAC,SAAS,CAAC,MAAM;;;;;IAKtC,UAAU,OAAO,EAAE,YAAY,EAAE;QAC7B,qBAAqB,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,oBAAoB,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,oBAAoB,GAAG,EAAE,CAAC,CAAC;SACrD;QACD,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;KACvE,CAAC;;;;;IAKF,qBAAqB,CAAC,SAAS,CAAC,GAAG;;;;IAInC,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;;;;IAItD,qBAAqB,CAAC,SAAS,CAAC,KAAK;;;IAGrC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;IACnC,OAAO,qBAAqB,CAAC;CAChC,EAAE,CAAC;;AC5DJ;;;;AAIA,AAKA,IAAqB,yBAAyB,GAAG,CAAC,CAAC;AACnD,IAAqB,WAAW,GAAG,QAAQ,CAAC;AAC5C,IAAqB,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACtE,IAAqB,WAAW,GAAG,QAAQ,CAAC;AAC5C,IAAqB,iBAAiB,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;;;;;;;;;;;;;;AActE,AAAO,SAAS,uBAAuB,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE;IAC7J,IAAI,cAAc,KAAK,KAAK,CAAC,EAAE,EAAE,cAAc,GAAG,EAAE,CAAC,EAAE;IACvD,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;IACjD,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;IACvC,OAAO,IAAI,+BAA+B,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;CACxL;AACD,IAAI,+BAA+B,kBAAkB,YAAY;IAC7D,SAAS,+BAA+B,GAAG;KAC1C;;;;;;;;;;;;;;IAcD,+BAA+B,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;IAaxD,UAAU,MAAM,EAAE,WAAW,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE;QAC/H,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;QACvC,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;QACjE,qBAAqB,OAAO,GAAG,IAAI,wBAAwB,CAAC,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC9I,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnF,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;;QAEjC,qBAAqB,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC;QACxH,IAAI,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;YACrD,qBAAqB,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,EAAE,CAAC,uBAAuB,EAAE,EAAE;gBAC/B,EAAE,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC9D;SACJ;QACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC;YAC9F,CAAC,yBAAyB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;KAC7E,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,YAAY;;;;;IAKtD,UAAU,GAAG,EAAE,OAAO,EAAE;;KAEvB,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,UAAU;;;;;IAKpD,UAAU,GAAG,EAAE,OAAO,EAAE;;KAEvB,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,eAAe;;;;;IAKzD,UAAU,GAAG,EAAE,OAAO,EAAE;;KAEvB,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,iBAAiB;;;;;IAK3D,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,qBAAqB,mBAAmB,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC5F,IAAI,mBAAmB,EAAE;YACrB,qBAAqB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1E,qBAAqB,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;YACrE,qBAAqB,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,YAAY,oBAAoB,YAAY,CAAC,OAAO,EAAE,CAAC;YACtI,IAAI,SAAS,IAAI,OAAO,EAAE;;;gBAGtB,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;aAC7C;SACJ;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,eAAe;;;;;IAKzD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,qBAAqB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1E,YAAY,CAAC,wBAAwB,EAAE,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACjD,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC3E,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;;IAOF,+BAA+B,CAAC,SAAS,CAAC,qBAAqB;;;;;;IAM/D,UAAU,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE;QACtC,qBAAqB,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACrE,qBAAqB,YAAY,GAAG,SAAS,CAAC;;;QAG9C,qBAAqB,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QACvG,qBAAqB,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QAC9F,IAAI,QAAQ,KAAK,CAAC,EAAE;YAChB,YAAY,CAAC,OAAO,CAAC,UAAU,WAAW,EAAE;gBACxC,qBAAqB,kBAAkB,GAAG,OAAO,CAAC,2BAA2B,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC5G,YAAY;oBACR,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACtF,CAAC,CAAC;SACN;QACD,OAAO,YAAY,CAAC;KACvB,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,cAAc;;;;;IAKxD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC3C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,aAAa;;;;;IAKvD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/D,qBAAqB,GAAG,GAAG,OAAO,CAAC;QACnC,qBAAqB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC3C,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9C,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACxC,GAAG,CAAC,wBAAwB,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;gBACvB,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,cAAc;oBACxC,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;oBAC5C,GAAG,CAAC,YAAY,GAAG,0BAA0B,CAAC;iBACjD;gBACD,qBAAqB,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC/D,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aAC5B;SACJ;QACD,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;YAClB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;YAExE,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;;;;YAI5C,IAAI,GAAG,CAAC,eAAe,GAAG,eAAe,EAAE;gBACvC,GAAG,CAAC,wBAAwB,EAAE,CAAC;aAClC;SACJ;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,UAAU;;;;;IAKpD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,cAAc,GAAG,EAAE,CAAC;QACzC,qBAAqB,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACxE,qBAAqB,KAAK,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1G,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC3B,qBAAqB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1E,IAAI,KAAK,EAAE;gBACP,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACrC;YACD,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YACrC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAChF,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;SACrD,CAAC,CAAC;;;;QAIH,cAAc,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACvH,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC/C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,YAAY;;;;;IAKtD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,mBAAmB,GAAG,GAAG,OAAO,EAAE;YAClC,qBAAqB,QAAQ,GAAG,mBAAmB,GAAG,GAAG,QAAQ,CAAC;YAClE,qBAAqB,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;YAC3H,OAAO,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;SACrD;aACI;YACD,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC;SAC3E;KACJ,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,YAAY;;;;;IAKtD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,qBAAqB,OAAO,GAAG,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACvG,qBAAqB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;QACxD,IAAI,OAAO,CAAC,KAAK,EAAE;YACf,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;SACpC;QACD,qBAAqBA,QAAK,GAAG,GAAG,CAAC,KAAK,CAAC;QACvC,IAAIA,QAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB;YACjC,IAAI,CAAC,cAAc,CAACA,QAAK,EAAE,OAAO,CAAC,CAAC;SACvC;aACI;YACD,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACxC,IAAI,CAAC,UAAU,mBAAmBA,QAAK,GAAG,OAAO,CAAC,CAAC;YACnD,QAAQ,CAAC,qBAAqB,EAAE,CAAC;SACpC;QACD,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACrC,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,UAAU;;;;;IAKpD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,qBAAqB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;QACxD,qBAAqB,OAAO,sBAAsB,OAAO,CAAC,qBAAqB,EAAE,CAAC;;;QAGlF,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE;YACzD,QAAQ,CAAC,YAAY,EAAE,CAAC;SAC3B;QACD,qBAAqB,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC;QACxE,IAAI,GAAG,CAAC,WAAW,EAAE;YACjB,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACnC;aACI;YACD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3E;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,cAAc;;;;;IAKxD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,qBAAqB,qBAAqB,sBAAsB,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAChG,qBAAqB,SAAS,GAAG,oBAAoB,OAAO,CAAC,eAAe,IAAI,QAAQ,CAAC;QACzF,qBAAqB,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC;QAC/D,qBAAqB,YAAY,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC/D,qBAAqB,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC;QAClE,aAAa,CAAC,MAAM,GAAG,qBAAqB,CAAC,MAAM,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YAC/B,qBAAqB,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;YAC/C,aAAa,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;YAC7C,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACnF,aAAa,CAAC,qBAAqB,EAAE,CAAC;SACzC,CAAC,CAAC;;;QAGH,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;;;QAGpE,OAAO,CAAC,wBAAwB,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;QACvD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,UAAU;;;;;IAKpD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,IAAI,KAAK,GAAG,IAAI,CAAC;;;QAGjB,qBAAqB,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;QACrE,qBAAqB,OAAO,sBAAsB,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QACtE,qBAAqB,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnF,IAAI,KAAK,KAAK,OAAO,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;aACxC,SAAS,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;YACjF,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;YAChD,OAAO,CAAC,YAAY,GAAG,0BAA0B,CAAC;SACrD;QACD,qBAAqB,YAAY,GAAG,SAAS,CAAC;QAC9C,qBAAqB,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACjK,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;QACxC,qBAAqB,mBAAmB,GAAG,IAAI,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,CAAC,EAAE;YAC/B,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC9B,qBAAqB,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACnF,IAAI,KAAK,EAAE;gBACP,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACrC;YACD,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE;gBAC7B,mBAAmB,GAAG,YAAY,CAAC,eAAe,CAAC;aACtD;YACD,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;;;;YAIjD,YAAY,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;YACrD,qBAAqB,OAAO,GAAG,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC;YACxE,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;SAClD,CAAC,CAAC;QACH,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC9B,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC9B,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,mBAAmB,EAAE;YACrB,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,CAAC;YAC1E,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;SACnD;QACD,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;KAC9B,CAAC;;;;;;IAMF,+BAA+B,CAAC,SAAS,CAAC,YAAY;;;;;IAKtD,UAAU,GAAG,EAAE,OAAO,EAAE;QACpB,qBAAqB,aAAa,sBAAsB,OAAO,CAAC,aAAa,EAAE,CAAC;QAChF,qBAAqB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC;QAClD,qBAAqB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC3C,qBAAqB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3D,qBAAqB,OAAO,GAAG,QAAQ,IAAI,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC1E,qBAAqB,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAClE,qBAAqB,kBAAkB,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;QAC5F,QAAQ,kBAAkB;YACtB,KAAK,SAAS;gBACV,KAAK,GAAG,OAAO,GAAG,KAAK,CAAC;gBACxB,MAAM;YACV,KAAK,MAAM;gBACP,KAAK,GAAG,aAAa,CAAC,kBAAkB,CAAC;gBACzC,MAAM;SACb;QACD,qBAAqB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC;QACxD,IAAI,KAAK,EAAE;YACP,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,qBAAqB,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzD,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC3C,OAAO,CAAC,YAAY,GAAG,GAAG,CAAC;;;;;QAK3B,aAAa,CAAC,kBAAkB;YAC5B,CAAC,EAAE,CAAC,WAAW,GAAG,YAAY,KAAK,EAAE,CAAC,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;KAClG,CAAC;IACF,OAAO,+BAA+B,CAAC;CAC1C,EAAE,CAAC,CAAC;AACL,AACA,IAAqB,0BAA0B,qBAAqB,EAAE,CAAC,CAAC;AACxE,IAAI,wBAAwB,kBAAkB,YAAY;IACtD,SAAS,wBAAwB,CAAC,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE;QACvI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,0BAA0B,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACxF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACxC;IACD,MAAM,CAAC,cAAc,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,EAAE;QAChE,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC3C,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;;IAMH,wBAAwB,CAAC,SAAS,CAAC,aAAa;;;;;IAKhD,UAAU,OAAO,EAAE,YAAY,EAAE;QAC7B,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO;YACR,OAAO;QACX,qBAAqB,UAAU,qBAAqB,OAAO,CAAC,CAAC;QAC7D,qBAAqB,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;;QAEpD,IAAI,UAAU,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC7B,mBAAmB,eAAe,GAAG,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;SAC3F;QACD,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE;YAC1B,eAAe,CAAC,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAChE;QACD,qBAAqB,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;QACnD,IAAI,SAAS,EAAE;YACX,qBAAqB,gBAAgB,sBAAsB,eAAe,CAAC,MAAM,EAAE,CAAC;YACpF,IAAI,CAAC,gBAAgB,EAAE;gBACnB,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;aAC/C;YACD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBAC3C,IAAI,CAAC,YAAY,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBACzD,gBAAgB,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,gBAAgB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;iBAC/F;aACJ,CAAC,CAAC;SACN;KACJ,CAAC;;;;IAIF,wBAAwB,CAAC,SAAS,CAAC,YAAY;;;IAG/C,YAAY;QACR,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,qBAAqB,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACvD,IAAI,WAAW,EAAE;gBACb,qBAAqB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACvD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;aAC7F;SACJ;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;;;;;;;IAOF,wBAAwB,CAAC,SAAS,CAAC,gBAAgB;;;;;;IAMnD,UAAU,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE;QACjC,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;QAC3C,qBAAqB,MAAM,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QACtD,qBAAqB,OAAO,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;QAClO,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACzC,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAC3D,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACnD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACnD,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,OAAO,CAAC;KAClB,CAAC;;;;;IAKF,wBAAwB,CAAC,SAAS,CAAC,wBAAwB;;;;IAI3D,UAAU,OAAO,EAAE;QACf,IAAI,CAAC,YAAY,GAAG,0BAA0B,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B,CAAC;;;;;;;IAOF,wBAAwB,CAAC,SAAS,CAAC,2BAA2B;;;;;;IAM9D,UAAU,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE;QACpC,qBAAqB,cAAc,GAAG;YAClC,QAAQ,EAAE,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,WAAW,CAAC,QAAQ;YAC5D,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK;YACzF,MAAM,EAAE,EAAE;SACb,CAAC;QACF,qBAAqB,OAAO,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW,CAAC,uBAAuB,CAAC,CAAC;QAC5N,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO,cAAc,CAAC;KACzB,CAAC;;;;;IAKF,wBAAwB,CAAC,SAAS,CAAC,aAAa;;;;IAIhD,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;KAC1E,CAAC;;;;;IAKF,wBAAwB,CAAC,SAAS,CAAC,aAAa;;;;IAIhD,UAAU,KAAK,EAAE;;QAEb,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7C;KACJ,CAAC;;;;;;;;;;IAUF,wBAAwB,CAAC,SAAS,CAAC,WAAW;;;;;;;;;IAS9C,UAAU,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE;QACxE,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC9B;QACD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;;YAErB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3E,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3E,qBAAqB,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;YACxC,qBAAqB,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAClF,IAAI,KAAK,KAAK,CAAC,EAAE;gBACb,QAAQ,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;oBAC3E,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aAChC;YACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,gBAAgB,GAAG,6CAA6C,GAAG,gBAAgB,GAAG,sDAAsD,CAAC,CAAC;SAC3K;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;IACF,OAAO,wBAAwB,CAAC;CACnC,EAAE,CAAC,CAAC;AACL,AAmCA,IAAI,eAAe,kBAAkB,YAAY;IAC7C,SAAS,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,4BAA4B,EAAE;QAChF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,4BAA4B,GAAG,4BAA4B,CAAC;QACjE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACpC,IAAI,CAAC,4BAA4B,GAAG,IAAI,GAAG,EAAE,CAAC;SACjD;QACD,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,sBAAsB,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;QACjG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACvD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC7E;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB;;;;IAID,eAAe,CAAC,SAAS,CAAC,iBAAiB;;;IAG3C,YAAY;QACR,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI;YACxB,KAAK,CAAC;gBACF,OAAO,KAAK,CAAC;YACjB,KAAK,CAAC;gBACF,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;YACvD;gBACI,OAAO,IAAI,CAAC;SACnB;KACJ,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,yBAAyB;;;IAGnD,YAAY,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC;IAC3D,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAE,aAAa,EAAE;QAC5D,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;QACtD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;IAKH,eAAe,CAAC,SAAS,CAAC,aAAa;;;;IAIvC,UAAU,KAAK,EAAE;;;;;QAKb,qBAAqB,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;QAC5G,IAAI,IAAI,CAAC,QAAQ,IAAI,eAAe,EAAE;YAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;YAC3C,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAChC;SACJ;aACI;YACD,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC;SAC3B;KACJ,CAAC;;;;;;IAMF,eAAe,CAAC,SAAS,CAAC,IAAI;;;;;IAK9B,UAAU,OAAO,EAAE,WAAW,EAAE;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;KACzH,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,aAAa;;;IAGvC,YAAY;QACR,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC;SAClD;QACD,IAAI,CAAC,gBAAgB,sBAAsB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAChF,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC7D;KACJ,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,YAAY;;;IAGtC,YAAY;QACR,IAAI,CAAC,QAAQ,IAAI,yBAAyB,CAAC;QAC3C,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB,CAAC;;;;;IAKF,eAAe,CAAC,SAAS,CAAC,WAAW;;;;IAIrC,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB,CAAC;;;;;;IAMF,eAAe,CAAC,SAAS,CAAC,YAAY;;;;;IAKtC,UAAU,IAAI,EAAE,KAAK,EAAE;QACnB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;KACvE,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,uBAAuB;;;IAGjD,YAAY,EAAE,OAAO,IAAI,CAAC,yBAAyB,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;;;;;IAKjF,eAAe,CAAC,SAAS,CAAC,cAAc;;;;IAIxC,UAAU,MAAM,EAAE;QACd,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SAC7C;;;;;;;QAOD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YAC5D,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC;YACxE,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;SAC7C,CAAC,CAAC;QACH,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,gBAAgB,CAAC;KAC1D,CAAC;;;;;;;;IAQF,eAAe,CAAC,SAAS,CAAC,SAAS;;;;;;;IAOnC,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SAC7C;QACD,qBAAqB,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;QAChE,qBAAqB,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC/E,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACxC,qBAAqB,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAC3E,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAClD,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC;oBACpE,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC;oBACjC,UAAU,CAAC;aAClB;YACD,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SACjC,CAAC,CAAC;KACN,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,qBAAqB;;;IAG/C,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;QAClD,qBAAqB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;YACjB,OAAO;QACX,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YAC1B,qBAAqB,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;SACtC,CAAC,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YAC3D,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC9C,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;aACnE;SACJ,CAAC,CAAC;KACN,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,qBAAqB;;;IAG/C,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YAC3D,qBAAqB,GAAG,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC5D,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YACjC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SACjC,CAAC,CAAC;KACN,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,gBAAgB;;;IAG1C,YAAY,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC3D,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAE,YAAY,EAAE;QAC3D,GAAG;;;QAGH,YAAY;YACR,qBAAqB,UAAU,GAAG,EAAE,CAAC;YACrC,KAAK,qBAAqB,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACrD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACzB;YACD,OAAO,UAAU,CAAC;SACrB;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;IAKH,eAAe,CAAC,SAAS,CAAC,4BAA4B;;;;IAItD,UAAU,QAAQ,EAAE;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACxD,qBAAqB,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC1D,qBAAqB,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC7D,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE;gBAC5C,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC5C;SACJ,CAAC,CAAC;KACN,CAAC;;;;IAIF,eAAe,CAAC,SAAS,CAAC,cAAc;;;IAGxC,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,qBAAqB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/C,qBAAqB,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAChD,qBAAqB,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;QACjF,qBAAqB,cAAc,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,IAAI,EAAE;YAC9C,qBAAqB,aAAa,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAChE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBAC/C,qBAAqB,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;gBACjD,IAAI,KAAK,IAAID,UAAS,EAAE;oBACpB,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC3B;qBACI,IAAI,KAAK,IAAI,UAAU,EAAE;oBAC1B,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC5B;aACJ,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,EAAE;gBACV,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC;aACnD;YACD,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACtC,CAAC,CAAC;QACH,qBAAqB,QAAQ,GAAG,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;QAClG,qBAAqB,SAAS,GAAG,cAAc,CAAC,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;;QAErG,IAAI,OAAO,EAAE;YACT,qBAAqB,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC7C,qBAAqB,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YACxC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAClB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAClB,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;SAC/B;QACD,OAAO,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC1I,CAAC;IACF,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC,CAAC;AACL,AAiCA,IAAI,kBAAkB,kBAAkB,UAAU,MAAM,EAAE;IACtDE,SAAiB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;IAC9C,SAAS,kBAAkB,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,wBAAwB,EAAE;QACtH,IAAI,wBAAwB,KAAK,KAAK,CAAC,EAAE,EAAE,wBAAwB,GAAG,KAAK,CAAC,EAAE;QAC9E,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;QACtE,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACxB,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC5B,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;QACpC,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;QACtC,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;QAC1D,KAAK,CAAC,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;QAC7F,OAAO,KAAK,CAAC;KAChB;;;;IAID,kBAAkB,CAAC,SAAS,CAAC,iBAAiB;;;IAG9C,YAAY,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;;;;IAIlD,kBAAkB,CAAC,SAAS,CAAC,cAAc;;;IAG3C,YAAY;QACR,qBAAqB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAChD,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;QACpF,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,EAAE;YACxC,qBAAqB,YAAY,GAAG,EAAE,CAAC;YACvC,qBAAqB,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;YAClD,qBAAqB,WAAW,GAAG,KAAK,GAAG,SAAS,CAAC;;YAErD,qBAAqB,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YACxE,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/B,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACpC,qBAAqB,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YACxE,gBAAgB,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;YACtD,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;;;;;;;;;;;;;;;YAgBpC,qBAAqB,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAClD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC9C,qBAAqB,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC1D,qBAAqB,SAAS,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACjE,qBAAqB,cAAc,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAC;gBACnE,EAAE,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC;gBACvD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACzB;;YAED,QAAQ,GAAG,SAAS,CAAC;YACrB,KAAK,GAAG,CAAC,CAAC;YACV,MAAM,GAAG,EAAE,CAAC;YACZ,SAAS,GAAG,YAAY,CAAC;SAC5B;QACD,OAAO,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KACrI,CAAC;IACF,OAAO,kBAAkB,CAAC;CAC7B,CAAC,eAAe,CAAC,CAAC,CAAC;AACpB,AAcA;;;;;AAKA,SAAS,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE;IACxC,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,GAAG,CAAC,CAAC,EAAE;IACpD,qBAAqB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC;IAC5D,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;CAC3C;;;;;;AAMD,SAAS,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE;IACrC,qBAAqB,MAAM,GAAG,EAAE,CAAC;IACjC,qBAAqB,aAAa,CAAC;IACnC,KAAK,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;QAC3B,IAAI,KAAK,KAAK,GAAG,EAAE;YACf,aAAa,GAAG,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxD,aAAa,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;SACzE;aACI;YACD,UAAU,mBAAmB,KAAK,GAAG,KAAK,EAAE,MAAM,CAAC,CAAC;SACvD;KACJ,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;CACjB;;ACjrCD;;;;AAIA,AAIA,IAAI,SAAS,kBAAkB,YAAY;IACvC,SAAS,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,qBAAqB,GAAG,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACrE,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,qBAAqB,YAAY,GAAG,gCAAgC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;KAC5B;;;;;;;;;IASD,SAAS,CAAC,SAAS,CAAC,cAAc;;;;;;;;IAQlC,UAAU,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,OAAO,EAAE,eAAe,EAAE;QAC5E,qBAAqB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC,cAAc,CAAC,qBAAqB,cAAc,CAAC,CAAC;QACjI,qBAAqB,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,iBAAiB,CAAC,CAAC;QACzI,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE,CAAC;QACjE,qBAAqB,MAAM,GAAG,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QAClL,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,qBAAqB,YAAY,GAAG,8BAA8B,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SACjC;QACD,OAAO,MAAM,CAAC;KACjB,CAAC;IACF,OAAO,SAAS,CAAC;CACpB,EAAE,CAAC;;AChDJ;;;;;;;;;;;;;;;AAeA,IAIA,wBAAwB,kBAAkB,YAAY;IAClD,SAAS,wBAAwB,GAAG;KACnC;IACD,OAAO,wBAAwB,CAAC;CACnC,EAAE,CAAC,CAAC;AACL,AAuBA;;;AAGA,IAGA,4BAA4B,kBAAkB,YAAY;IACtD,SAAS,4BAA4B,GAAG;KACvC;;;;;;IAMD,4BAA4B,CAAC,SAAS,CAAC,qBAAqB;;;;;IAK5D,UAAU,YAAY,EAAE,MAAM,EAAE,EAAE,OAAO,YAAY,CAAC,EAAE,CAAC;;;;;;;;IAQzD,4BAA4B,CAAC,SAAS,CAAC,mBAAmB;;;;;;;IAO1D,UAAU,oBAAoB,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE;QAC/D,yBAAyB,KAAK,EAAE;KACnC,CAAC;IACF,OAAO,4BAA4B,CAAC;CACvC,EAAE,CAAC;;ACrFJ;;;;AAIA,AAGA,IAAI,4BAA4B,kBAAkB,UAAU,MAAM,EAAE;IAChEA,SAAiB,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;IACxD,SAAS,4BAA4B,GAAG;QACpC,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC;KACnE;;;;;;IAMD,4BAA4B,CAAC,SAAS,CAAC,qBAAqB;;;;;IAK5D,UAAU,YAAY,EAAE,MAAM,EAAE;QAC5B,OAAO,mBAAmB,CAAC,YAAY,CAAC,CAAC;KAC5C,CAAC;;;;;;;;IAQF,4BAA4B,CAAC,SAAS,CAAC,mBAAmB;;;;;;;IAO1D,UAAU,oBAAoB,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE;QAC/D,qBAAqB,IAAI,GAAG,EAAE,CAAC;QAC/B,qBAAqB,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;QACtD,IAAI,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;YAC1E,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC3B,IAAI,GAAG,IAAI,CAAC;aACf;iBACI;gBACD,qBAAqB,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAC/E,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;oBACvD,MAAM,CAAC,IAAI,CAAC,sCAAsC,GAAG,oBAAoB,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;iBAC5F;aACJ;SACJ;QACD,OAAO,MAAM,GAAG,IAAI,CAAC;KACxB,CAAC;IACF,OAAO,4BAA4B,CAAC;CACvC,CAAC,wBAAwB,CAAC,CAAC,CAAC;AAC7B,AACA,IAAqB,oBAAoB,GAAG,cAAc,CAAC,gUAAgU;KACtX,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;;;;AAKjB,SAAS,cAAc,CAAC,IAAI,EAAE;IAC1B,qBAAqB,GAAG,GAAG,EAAE,CAAC;IAC9B,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IACzD,OAAO,GAAG,CAAC;CACd;;ACpED;;;;;;;AAOA,AAAoD;AACpD,AA0BA;;;;;;;;;;;;;;;AAeA,AAAO,SAAS,2BAA2B,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE;IAChM,OAAO;QACH,IAAI,EAAE,CAAC;QACP,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,WAAW;QACxB,mBAAmB,EAAE,mBAAmB;QACxC,SAAS,EAAE,SAAS;QACpB,UAAU,EAAE,UAAU;QACtB,OAAO,EAAE,OAAO;QAChB,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE,eAAe;QAChC,aAAa,EAAE,aAAa;QAC5B,cAAc,EAAE,cAAc;QAC9B,MAAM,EAAE,MAAM;KACjB,CAAC;CACL;;ACjED;;;;AAIA,AAKA,IAAqB,YAAY,GAAG,EAAE,CAAC;AACvC,IAAI,0BAA0B,kBAAkB,YAAY;IACxD,SAAS,0BAA0B,CAAC,YAAY,EAAE,GAAG,EAAE,YAAY,EAAE;QACjE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KACpC;;;;;;IAMD,0BAA0B,CAAC,SAAS,CAAC,KAAK;;;;;IAK1C,UAAU,YAAY,EAAE,SAAS,EAAE;QAC/B,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;KAChF,CAAC;;;;;;;IAOF,0BAA0B,CAAC,SAAS,CAAC,WAAW;;;;;;IAMhD,UAAU,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE;QACjC,qBAAqB,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAChE,qBAAqB,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAChE,qBAAqB,YAAY,GAAG,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC;QAC3G,OAAO,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC;KAC/E,CAAC;;;;;;;;;;;;;IAaF,0BAA0B,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;IAY1C,UAAU,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE;QAC9H,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,qBAAqB,yBAAyB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,YAAY,CAAC;QAC7G,qBAAqB,sBAAsB,GAAG,cAAc,IAAI,cAAc,CAAC,MAAM,IAAI,YAAY,CAAC;QACtG,qBAAqB,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,sBAAsB,EAAE,MAAM,CAAC,CAAC;QACzG,qBAAqB,mBAAmB,GAAG,WAAW,IAAI,WAAW,CAAC,MAAM,IAAI,YAAY,CAAC;QAC7F,qBAAqB,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAChG,qBAAqB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjD,qBAAqB,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7C,qBAAqB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9C,qBAAqB,SAAS,GAAG,SAAS,KAAK,MAAM,CAAC;QACtD,qBAAqB,gBAAgB,GAAG,EAAE,MAAM,EAAEC,QAAgB,CAAC,EAAE,EAAE,yBAAyB,EAAE,mBAAmB,CAAC,EAAE,CAAC;QACzH,qBAAqB,SAAS,GAAG,uBAAuB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QAC9M,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,OAAO,2BAA2B,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;SACtL;QACD,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;YAC5B,qBAAqB,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC;YACtC,qBAAqB,QAAQ,GAAG,eAAe,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YACtE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5E,qBAAqB,SAAS,GAAG,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YACxE,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9E,IAAI,GAAG,KAAK,OAAO,EAAE;gBACjB,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAC5B;SACJ,CAAC,CAAC;QACH,qBAAqB,mBAAmB,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QACrF,OAAO,2BAA2B,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,eAAe,EAAE,SAAS,EAAE,mBAAmB,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;KACtM,CAAC;IACF,OAAO,0BAA0B,CAAC;CACrC,EAAE,CAAC,CAAC;AACL,AASA;;;;;;AAMA,SAAS,yBAAyB,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE;IAClE,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;CAC/E;AACD,IAAI,oBAAoB,kBAAkB,YAAY;IAClD,SAAS,oBAAoB,CAAC,MAAM,EAAE,aAAa,EAAE;QACjD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;;;;;;IAMD,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;;;IAK1C,UAAU,MAAM,EAAE,MAAM,EAAE;QACtB,qBAAqB,WAAW,GAAG,EAAE,CAAC;QACtC,qBAAqB,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;YACvC,qBAAqB,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YACzC,IAAI,KAAK,IAAI,IAAI,EAAE;gBACf,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aAC/B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;YACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC3B,qBAAqB,UAAU,qBAAqB,KAAK,CAAC,CAAC;gBAC3D,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;oBAC5C,qBAAqB,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;oBAC5C,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;wBAChB,GAAG,GAAG,iBAAiB,CAAC,GAAG,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;qBACxD;oBACD,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;iBAC3B,CAAC,CAAC;aACN;SACJ,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;KACtB,CAAC;IACF,OAAO,oBAAoB,CAAC;CAC/B,EAAE,CAAC;;AC/JJ;;;;AAIA,AACA;;;;;;AAMA,AAAO,SAAS,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;IACpC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;CAC1C;;;;AAID,IAGA,gBAAgB,kBAAkB,YAAY;IAC1C,SAAS,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE;QACjC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;YAC9B,qBAAqB,aAAa,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;YAC/E,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;SAC/E,CAAC,CAAC;QACH,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC5C,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAC7C,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;YACnC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;SAC3F,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACzE;IACD,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,EAAE;QACjE,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE;QAC/C,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;;IAMH,gBAAgB,CAAC,SAAS,CAAC,eAAe;;;;;IAK1C,UAAU,YAAY,EAAE,SAAS,EAAE;QAC/B,qBAAqB,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QACtH,OAAO,KAAK,IAAI,IAAI,CAAC;KACxB,CAAC;;;;;;;IAOF,gBAAgB,CAAC,SAAS,CAAC,WAAW;;;;;;IAMtC,UAAU,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE;QACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;KAC5E,CAAC;IACF,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC,CAAC;AACL,AAgBA;;;;;AAKA,SAAS,wBAAwB,CAAC,WAAW,EAAE,MAAM,EAAE;IACnD,qBAAqB,QAAQ,GAAG,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IACjF,qBAAqB,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,iBAAiB,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACtF,qBAAqB,UAAU,GAAG;QAC9B,IAAI,EAAE,CAAC;QACP,SAAS,EAAE,SAAS;QACpB,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,CAAC;QACb,QAAQ,EAAE,CAAC;KACd,CAAC;IACF,OAAO,IAAI,0BAA0B,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;CAC1E;;;;;;;AAOD,SAAS,iBAAiB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;IACxC,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;SACzB;KACJ;SACI,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC/B,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;KACzB;CACJ;;AC9HD;;;;AAIA,AAMA,IAAqB,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;AACzE,IAAI,uBAAuB,kBAAkB,YAAY;IACrD,SAAS,uBAAuB,CAAC,OAAO,EAAE,WAAW,EAAE;QACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;KACrB;;;;;;IAMD,uBAAuB,CAAC,SAAS,CAAC,QAAQ;;;;;IAK1C,UAAU,EAAE,EAAE,QAAQ,EAAE;QACpB,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,qBAAqB,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7E,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,6DAA6D,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACtG;aACI;YACD,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;SAC9B;KACJ,CAAC;;;;;;;IAOF,uBAAuB,CAAC,SAAS,CAAC,YAAY;;;;;;IAM9C,UAAU,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE;QAChC,qBAAqB,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;QACzC,qBAAqB,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QACjI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;KACtF,CAAC;;;;;;;IAOF,uBAAuB,CAAC,SAAS,CAAC,MAAM;;;;;;IAMxC,UAAU,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE;QAC5B,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;QACzC,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,qBAAqB,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAChD,qBAAqB,YAAY,CAAC;QAClC,qBAAqB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/C,IAAI,GAAG,EAAE;YACL,YAAY,GAAG,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,eAAe,EAAE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,MAAM,CAAC,CAAC;YACrJ,YAAY,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBACjC,qBAAqB,MAAM,GAAG,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC/E,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;aAChF,CAAC,CAAC;SACN;aACI;YACD,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;YACpF,YAAY,GAAG,EAAE,CAAC;SACrB;QACD,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,8DAA8D,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACvG;QACD,aAAa,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,OAAO,EAAE;YAC7C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;SAC1H,CAAC,CAAC;QACH,qBAAqB,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACzD,qBAAqB,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SAC5C,CAAC,CAAC;QACH,qBAAqB,MAAM,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QAC/B,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,OAAO,MAAM,CAAC;KACjB,CAAC;;;;;IAKF,uBAAuB,CAAC,SAAS,CAAC,OAAO;;;;IAIzC,UAAU,EAAE,EAAE;QACV,qBAAqB,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAClD,MAAM,CAAC,OAAO,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAC7B,qBAAqB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACjC;KACJ,CAAC;;;;;IAKF,uBAAuB,CAAC,SAAS,CAAC,UAAU;;;;IAI5C,UAAU,EAAE,EAAE;QACV,qBAAqB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,mDAAmD,GAAG,EAAE,CAAC,CAAC;SAC7E;QACD,OAAO,MAAM,CAAC;KACjB,CAAC;;;;;;;;IAQF,uBAAuB,CAAC,SAAS,CAAC,MAAM;;;;;;;IAOxC,UAAU,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE;;QAExC,qBAAqB,SAAS,GAAG,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QACzE,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QACpE,OAAO,YAAY,GAAG,CAAC;KAC1B,CAAC;;;;;;;;IAQF,uBAAuB,CAAC,SAAS,CAAC,OAAO;;;;;;;IAOzC,UAAU,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE;QAClC,IAAI,OAAO,IAAI,UAAU,EAAE;YACvB,IAAI,CAAC,QAAQ,CAAC,EAAE,oBAAoB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9C,OAAO;SACV;QACD,IAAI,OAAO,IAAI,QAAQ,EAAE;YACrB,qBAAqB,OAAO,sBAAsB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;YAClE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAClC,OAAO;SACV;QACD,qBAAqB,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAClD,QAAQ,OAAO;YACX,KAAK,MAAM;gBACP,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM;YACV,KAAK,SAAS;gBACV,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM;YACV,KAAK,QAAQ;gBACT,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM;YACV,KAAK,MAAM;gBACP,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM;YACV,KAAK,aAAa;gBACd,MAAM,CAAC,WAAW,CAAC,UAAU,mBAAmB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC3D,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACjB,MAAM;SACb;KACJ,CAAC;IACF,OAAO,uBAAuB,CAAC;CAClC,EAAE,CAAC;;AC/MJ;;;;AAIA,AAKA,IAAqB,gBAAgB,GAAG,mBAAmB,CAAC;AAC5D,IAAqB,eAAe,GAAG,oBAAoB,CAAC;AAC5D,IAAqB,kBAAkB,GAAG,qBAAqB,CAAC;AAChE,IAAqB,iBAAiB,GAAG,sBAAsB,CAAC;AAChE,IAAqB,cAAc,GAAG,kBAAkB,CAAC;AACzD,IAAqB,aAAa,GAAG,mBAAmB,CAAC;AACzD,IAAqB,kBAAkB,GAAG,EAAE,CAAC;AAC7C,IAAqB,kBAAkB,GAAG;IACtC,WAAW,EAAE,EAAE;IACf,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,KAAK;IACnB,oBAAoB,EAAE,KAAK;CAC9B,CAAC;AACF,IAAqB,0BAA0B,GAAG;IAC9C,WAAW,EAAE,EAAE;IACf,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,KAAK;IACnB,oBAAoB,EAAE,IAAI;CAC7B,CAAC;AACF,AAYA;;;AAGA,AAAsC;AACtC,AAgBO,IAAqB,YAAY,GAAG,cAAc,CAAC;;;;AAI1D,AAA2C;AAC3C,AAUA,IAAI,UAAU,kBAAkB,YAAY;IACxC,SAAS,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE;QACpC,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;QACjD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,qBAAqB,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACpE,qBAAqB,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;QAC5D,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACP,qBAAqB,OAAO,GAAG,OAAO,mBAAmB,KAAK,EAAE,CAAC;YACjE,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;YACxB,IAAI,CAAC,OAAO,qBAAqB,OAAO,CAAC,CAAC;SAC7C;aACI;YACD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACrB;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;SAC5B;KACJ;IACD,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,QAAQ,EAAE;QAClD,GAAG;;;QAGH,YAAY,EAAE,yBAAyB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE;QAC9D,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;IAKH,UAAU,CAAC,SAAS,CAAC,aAAa;;;;IAIlC,UAAU,OAAO,EAAE;QACf,qBAAqB,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;QAChD,IAAI,SAAS,EAAE;YACX,qBAAqB,WAAW,sBAAsB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC5E,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBAC3C,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;oBAC3B,WAAW,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;iBACvC;aACJ,CAAC,CAAC;SACN;KACJ,CAAC;IACF,OAAO,UAAU,CAAC;CACrB,EAAE,CAAC,CAAC;AACL,AASO,IAAqB,UAAU,GAAG,MAAM,CAAC;AAChD,AAAO,IAAqB,mBAAmB,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;AAC7E,AAAO,IAAqB,mBAAmB,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;AAC5E,IAAI,4BAA4B,kBAAkB,YAAY;IAC1D,SAAS,4BAA4B,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE;QAC5D,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,SAAS,GAAG,EAAE,CAAC;QACrC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KAC9C;;;;;;;;IAQD,4BAA4B,CAAC,SAAS,CAAC,MAAM;;;;;;;IAO7C,UAAU,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,oDAAoD,GAAG,KAAK,GAAG,qCAAqC,GAAG,IAAI,GAAG,mBAAmB,CAAC,CAAC;SACtJ;QACD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,8CAA8C,GAAG,IAAI,GAAG,6CAA6C,CAAC,CAAC;SAC1H;QACD,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,yCAAyC,GAAG,KAAK,GAAG,iCAAiC,GAAG,IAAI,GAAG,sBAAsB,CAAC,CAAC;SAC1I;QACD,qBAAqB,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QACtF,qBAAqB,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;QAC7E,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,qBAAqB,kBAAkB,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QACrG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC1C,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;YACrD,kBAAkB,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC;SAClD;QACD,OAAO,YAAY;;;;;;;YAOf,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY;gBACjC,qBAAqB,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,KAAK,IAAI,CAAC,EAAE;oBACZ,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC9B;gBACD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;oBACxB,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;iBACnC;aACJ,CAAC,CAAC;SACN,CAAC;KACL,CAAC;;;;;;IAMF,4BAA4B,CAAC,SAAS,CAAC,QAAQ;;;;;IAK/C,UAAU,IAAI,EAAE,GAAG,EAAE;QACjB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;;YAEtB,OAAO,KAAK,CAAC;SAChB;aACI;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YAC3B,OAAO,IAAI,CAAC;SACf;KACJ,CAAC;;;;;IAKF,4BAA4B,CAAC,SAAS,CAAC,WAAW;;;;IAIlD,UAAU,IAAI,EAAE;QACZ,qBAAqB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,IAAI,GAAG,6BAA6B,CAAC,CAAC;SAC/F;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;;;;;;;;IAQF,4BAA4B,CAAC,SAAS,CAAC,OAAO;;;;;;;IAO9C,UAAU,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,iBAAiB,EAAE;QACtD,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,iBAAiB,KAAK,KAAK,CAAC,EAAE,EAAE,iBAAiB,GAAG,IAAI,CAAC,EAAE;QAC/D,qBAAqB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC7D,qBAAqB,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAC3F,qBAAqB,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpF,IAAI,CAAC,kBAAkB,EAAE;YACrB,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,GAAG,EAAE,CAAC,CAAC;SACtE;QACD,qBAAqB,SAAS,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;QACjE,qBAAqB,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9D,qBAAqB,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE;YACrB,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAC5C;QACD,kBAAkB,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC;QAC1C,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS,GAAG,mBAAmB,CAAC;SACnC;aACI,IAAI,SAAS,KAAK,mBAAmB,EAAE;YACxC,OAAO,MAAM,CAAC;SACjB;QACD,qBAAqB,SAAS,GAAG,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC;;;;;;;QAO9D,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;;;YAGjD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC9C,qBAAqB,MAAM,GAAG,EAAE,CAAC;gBACjC,qBAAqB,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACnG,qBAAqB,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC7F,IAAI,MAAM,CAAC,MAAM,EAAE;oBACf,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBACpC;qBACI;oBACD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY;wBAChC,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;wBACnC,SAAS,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;qBAClC,CAAC,CAAC;iBACN;aACJ;YACD,OAAO;SACV;QACD,qBAAqB,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QACpG,gBAAgB,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;;;;;YAKvC,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;gBACtF,MAAM,CAAC,OAAO,EAAE,CAAC;aACpB;SACJ,CAAC,CAAC;QACH,qBAAqB,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC1F,qBAAqB,oBAAoB,GAAG,KAAK,CAAC;QAClD,IAAI,CAAC,UAAU,EAAE;YACb,IAAI,CAAC,iBAAiB;gBAClB,OAAO;YACX,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACxC,oBAAoB,GAAG,IAAI,CAAC;SAC/B;QACD,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,CAAC,CAAC;QAC7L,IAAI,CAAC,oBAAoB,EAAE;YACvB,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;SAC3E;QACD,MAAM,CAAC,MAAM,CAAC,YAAY;YACtB,qBAAqB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClC;YACD,qBAAqB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3E,IAAI,OAAO,EAAE;gBACT,qBAAqB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvD,IAAI,OAAO,IAAI,CAAC,EAAE;oBACd,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;iBAC9B;aACJ;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC;KACjB,CAAC;;;;;IAKF,4BAA4B,CAAC,SAAS,CAAC,UAAU;;;;IAIjD,UAAU,IAAI,EAAE;QACZ,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,OAAO,EAAE,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9F,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;YACzD,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SAC3G,CAAC,CAAC;KACN,CAAC;;;;;IAKF,4BAA4B,CAAC,SAAS,CAAC,iBAAiB;;;;IAIxD,UAAU,OAAO,EAAE;QACf,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,qBAAqB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACjF,IAAI,cAAc,EAAE;YAChB,cAAc,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YACvE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACjD;KACJ,CAAC;;;;;;;IAOF,4BAA4B,CAAC,SAAS,CAAC,8BAA8B;;;;;;IAMrE,UAAU,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,KAAK,CAAC,EAAE;;;;QAI5C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;;;YAGrF,IAAI,GAAG,CAAC,YAAY,CAAC;gBACjB,OAAO;YACX,qBAAqB,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;YAC9E,IAAI,UAAU,CAAC,IAAI,EAAE;gBACjB,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;aACrG;iBACI;gBACD,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;aAChC;SACJ,CAAC,CAAC;KACN,CAAC;;;;;;;;IAQF,4BAA4B,CAAC,SAAS,CAAC,qBAAqB;;;;;;;IAO5D,UAAU,OAAO,EAAE,OAAO,EAAE,oBAAoB,EAAE,iBAAiB,EAAE;QACjE,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/E,IAAI,aAAa,EAAE;YACf,qBAAqB,SAAS,GAAG,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAU,WAAW,EAAE;;;gBAGtD,IAAI,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;oBAC9B,qBAAqB,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;oBACjG,IAAI,MAAM,EAAE;wBACR,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC1B;iBACJ;aACJ,CAAC,CAAC;YACH,IAAI,SAAS,CAAC,MAAM,EAAE;gBAClB,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACnE,IAAI,oBAAoB,EAAE;oBACtB,mBAAmB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC1G;gBACD,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;KAChB,CAAC;;;;;IAKF,4BAA4B,CAAC,SAAS,CAAC,8BAA8B;;;;IAIrE,UAAU,OAAO,EAAE;QACf,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrE,IAAI,SAAS,EAAE;YACX,qBAAqB,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;YACnD,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;gBAClC,qBAAqB,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACjD,IAAI,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC;oBAClC,OAAO;gBACX,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACnC,qBAAqB,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC5D,qBAAqB,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC;gBAC7D,qBAAqB,aAAa,sBAAsB,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrG,qBAAqB,SAAS,GAAG,aAAa,CAAC,WAAW,CAAC,IAAI,mBAAmB,CAAC;gBACnF,qBAAqB,OAAO,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC1D,qBAAqB,MAAM,GAAG,IAAI,yBAAyB,CAAC,KAAK,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBAC5F,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBACnC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;oBACd,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,WAAW;oBACxB,UAAU,EAAE,UAAU;oBACtB,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,MAAM;oBACd,oBAAoB,EAAE,IAAI;iBAC7B,CAAC,CAAC;aACN,CAAC,CAAC;SACN;KACJ,CAAC;;;;;;IAMF,4BAA4B,CAAC,SAAS,CAAC,UAAU;;;;;IAKjD,UAAU,OAAO,EAAE,OAAO,EAAE;QACxB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;SAC/D;;QAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;YAClD,OAAO;;;QAGX,qBAAqB,iCAAiC,GAAG,KAAK,CAAC;QAC/D,IAAI,MAAM,CAAC,eAAe,EAAE;YACxB,qBAAqB,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;;;;;YAK/G,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;gBACzC,iCAAiC,GAAG,IAAI,CAAC;aAC5C;iBACI;gBACD,qBAAqB,QAAQ,GAAG,OAAO,CAAC;gBACxC,OAAO,QAAQ,GAAG,QAAQ,CAAC,UAAU,EAAE;oBACnC,qBAAqB,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACrE,IAAI,QAAQ,EAAE;wBACV,iCAAiC,GAAG,IAAI,CAAC;wBACzC,MAAM;qBACT;iBACJ;aACJ;SACJ;;;;;QAKD,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;;;QAG7C,IAAI,iCAAiC,EAAE;YACnC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACjE;aACI;;;YAGD,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5E,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC/C;KACJ,CAAC;;;;;;IAMF,4BAA4B,CAAC,SAAS,CAAC,UAAU;;;;;IAKjD,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;;;;;IAKvE,4BAA4B,CAAC,SAAS,CAAC,sBAAsB;;;;IAI7D,UAAU,WAAW,EAAE;QACnB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,YAAY,GAAG,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;YACjC,qBAAqB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC3C,IAAI,MAAM,CAAC,SAAS;gBAChB,OAAO;YACX,qBAAqB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC7C,qBAAqB,SAAS,GAAG,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtE,IAAI,SAAS,EAAE;gBACX,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;oBAClC,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE;wBACpC,qBAAqB,SAAS,GAAG,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC5H,mBAAmB,SAAS,GAAG,OAAO,CAAC,GAAG,WAAW,CAAC;wBACtD,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;qBAC9E;iBACJ,CAAC,CAAC;aACN;YACD,IAAI,MAAM,CAAC,gBAAgB,EAAE;gBACzB,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY;;;oBAGjC,MAAM,CAAC,OAAO,EAAE,CAAC;iBACpB,CAAC,CAAC;aACN;iBACI;gBACD,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC5B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,OAAO,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;;;YAGrC,qBAAqB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACpD,qBAAqB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACpD,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC;aAClB;YACD,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC9E,CAAC,CAAC;KACN,CAAC;;;;;IAKF,4BAA4B,CAAC,SAAS,CAAC,OAAO;;;;IAI9C,UAAU,OAAO,EAAE;QACf,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;KAClE,CAAC;;;;;IAKF,4BAA4B,CAAC,SAAS,CAAC,mBAAmB;;;;IAI1D,UAAU,OAAO,EAAE;QACf,qBAAqB,YAAY,GAAG,KAAK,CAAC;QAC1C,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACnC,YAAY,GAAG,IAAI,CAAC;QACxB,YAAY;YACR,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,YAAY,CAAC;QAC9G,OAAO,YAAY,CAAC;KACvB,CAAC;IACF,OAAO,4BAA4B,CAAC;CACvC,EAAE,CAAC,CAAC;AACL,AAmBA;;;AAGA,AAAsC;AACtC,AAQA,IAAI,yBAAyB,kBAAkB,YAAY;IACvD,SAAS,yBAAyB,CAAC,MAAM,EAAE,WAAW,EAAE;QACpD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,UAAU,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC;KAC5D;;;;;;;;IAQD,yBAAyB,CAAC,SAAS,CAAC,kBAAkB;;;;;;IAMtD,UAAU,OAAO,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC;IAC1E,MAAM,CAAC,cAAc,CAAC,yBAAyB,CAAC,SAAS,EAAE,eAAe,EAAE;QACxE,GAAG;;;QAGH,YAAY;YACR,qBAAqB,OAAO,GAAG,EAAE,CAAC;YAClC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;gBACtC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;oBACjC,IAAI,MAAM,CAAC,MAAM,EAAE;wBACf,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACxB;iBACJ,CAAC,CAAC;aACN,CAAC,CAAC;YACH,OAAO,OAAO,CAAC;SAClB;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;;IAMH,yBAAyB,CAAC,SAAS,CAAC,eAAe;;;;;IAKnD,UAAU,WAAW,EAAE,WAAW,EAAE;QAChC,qBAAqB,EAAE,GAAG,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAC3F,IAAI,WAAW,CAAC,UAAU,EAAE;YACxB,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;SAC/C;aACI;;;;YAID,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;;;;;;YAM1C,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;SACzC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;KAClD,CAAC;;;;;;IAMF,yBAAyB,CAAC,SAAS,CAAC,qBAAqB;;;;;IAKzD,UAAU,EAAE,EAAE,WAAW,EAAE;QACvB,qBAAqB,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5D,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,qBAAqB,KAAK,GAAG,KAAK,CAAC;YACnC,KAAK,qBAAqB,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC9C,qBAAqB,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC5D,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;oBACrE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACzC,KAAK,GAAG,IAAI,CAAC;oBACb,MAAM;iBACT;aACJ;YACD,IAAI,CAAC,KAAK,EAAE;gBACR,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;aACxC;SACJ;aACI;YACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,EAAE,CAAC;KACb,CAAC;;;;;;IAMF,yBAAyB,CAAC,SAAS,CAAC,QAAQ;;;;;IAK5C,UAAU,WAAW,EAAE,WAAW,EAAE;QAChC,qBAAqB,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,EAAE,EAAE;YACL,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;SACvD;QACD,OAAO,EAAE,CAAC;KACb,CAAC;;;;;;;IAOF,yBAAyB,CAAC,SAAS,CAAC,eAAe;;;;;;IAMnD,UAAU,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE;QAClC,qBAAqB,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YAClC,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;KACJ,CAAC;;;;;;IAMF,yBAAyB,CAAC,SAAS,CAAC,OAAO;;;;;IAK3C,UAAU,WAAW,EAAE,OAAO,EAAE;QAC5B,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW;YACZ,OAAO;QACX,qBAAqB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,CAAC,YAAY;YACxB,KAAK,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC3C,qBAAqB,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9D,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACzC;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;KAC9E,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,eAAe;;;;IAInD,UAAU,EAAE,EAAE,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAKpD,yBAAyB,CAAC,SAAS,CAAC,wBAAwB;;;;IAI5D,UAAU,OAAO,EAAE;;;;;;QAMf,qBAAqB,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5C,qBAAqB,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,aAAa,EAAE;YACf,qBAAqB,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACvD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnD,qBAAqB,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;gBAC/D,IAAI,IAAI,EAAE;oBACN,qBAAqB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBACrD,IAAI,EAAE,EAAE;wBACJ,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;qBACtB;iBACJ;aACJ;SACJ;QACD,OAAO,UAAU,CAAC;KACrB,CAAC;;;;;;;;IAQF,yBAAyB,CAAC,SAAS,CAAC,OAAO;;;;;;;IAO3C,UAAU,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;QACzC,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;KAChB,CAAC;;;;;;;;IAQF,yBAAyB,CAAC,SAAS,CAAC,UAAU;;;;;;;IAO9C,UAAU,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACvB,OAAO;;;QAGX,qBAAqB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACxE,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;YAClC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;SACjC;;;;QAID,IAAI,WAAW,EAAE;YACb,qBAAqB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;;;;;;;YAO5D,IAAI,EAAE,EAAE;gBACJ,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAClC;SACJ;;QAED,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SACrC;KACJ,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,mBAAmB;;;;IAIvD,UAAU,OAAO,EAAE,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;;;;;;IAMlE,yBAAyB,CAAC,SAAS,CAAC,qBAAqB;;;;;IAKzD,UAAU,OAAO,EAAE,KAAK,EAAE;QACtB,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAClC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAChC,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aACzC;SACJ;aACI,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACtC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;SAC5C;KACJ,CAAC;;;;;;;IAOF,yBAAyB,CAAC,SAAS,CAAC,UAAU;;;;;;IAM9C,UAAU,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;QACrC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC1C,OAAO;SACV;QACD,qBAAqB,EAAE,GAAG,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACjF,IAAI,EAAE,EAAE;YACJ,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACnC;aACI;YACD,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACnE;KACJ,CAAC;;;;;;;;IAQF,yBAAyB,CAAC,SAAS,CAAC,oBAAoB;;;;;;;IAOxD,UAAU,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE;QACnD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,OAAO,CAAC,YAAY,CAAC,GAAG;YACpB,WAAW,EAAE,WAAW;YACxB,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY;YAClD,oBAAoB,EAAE,KAAK;SAC9B,CAAC;KACL,CAAC;;;;;;;;;IASF,yBAAyB,CAAC,SAAS,CAAC,MAAM;;;;;;;;IAQ1C,UAAU,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE;QACnD,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;SACnF;QACD,OAAO,YAAY,GAAG,CAAC;KAC1B,CAAC;;;;;;;;IAQF,yBAAyB,CAAC,SAAS,CAAC,iBAAiB;;;;;;;IAOrD,UAAU,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE;QAC3D,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;KACvM,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,sBAAsB;;;;IAI1D,UAAU,gBAAgB,EAAE;QACxB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC/F,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,iCAAiC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QAClG,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC;YACtC,OAAO;QACX,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC5E,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;KACzG,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,iCAAiC;;;;IAIrE,UAAU,OAAO,EAAE;QACf,qBAAqB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;;;;gBAI9B,IAAI,MAAM,CAAC,MAAM,EAAE;oBACf,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC;iBAClC;qBACI;oBACD,MAAM,CAAC,OAAO,EAAE,CAAC;iBACpB;aACJ,CAAC,CAAC;SACN;QACD,qBAAqB,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,QAAQ,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,WAAW,EAAE,EAAE,OAAO,QAAQ,CAAC,WAAW,CAAC,GAAG,mBAAmB,CAAC,EAAE,CAAC,CAAC;SACjH;KACJ,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,qCAAqC;;;;IAIzE,UAAU,OAAO,EAAE;QACf,qBAAqB,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;SAClE;KACJ,CAAC;;;;IAIF,yBAAyB,CAAC,SAAS,CAAC,iBAAiB;;;IAGrD,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;YAClC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;gBACtB,OAAO,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;aACvF;iBACI;gBACD,OAAO,EAAE,CAAC;aACb;SACJ,CAAC,CAAC;KACN,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,gBAAgB;;;;IAIpD,UAAU,OAAO,EAAE;QACf,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACxE,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;;YAElC,OAAO,CAAC,YAAY,CAAC,GAAG,kBAAkB,CAAC;YAC3C,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBACrC,qBAAqB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACpE,IAAI,EAAE,EAAE;oBACJ,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;iBACjC;aACJ;YACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;SAC3D;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE;YACxD,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC9C;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACxE,KAAK,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC/C,CAAC,CAAC;KACN,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,KAAK;;;;IAIzC,UAAU,WAAW,EAAE;QACnB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE;QACjD,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,qBAAqB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1G,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAChC;QACD,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;YAC5D,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1E,qBAAqB,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC1D,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;aACjC;SACJ;QACD,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;aACzB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE;YACjE,qBAAqB,UAAU,GAAG,EAAE,CAAC;YACrC,IAAI;gBACA,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;aAC5D;oBACO;gBACJ,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACzD,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;iBACnB;aACJ;SACJ;aACI;YACD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1E,qBAAqB,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBAC9D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAClC;SACJ;QACD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;;;YAI3B,qBAAqB,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC;YACrD,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;YACxB,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;aAC5G;iBACI;gBACD,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;aACtD;SACJ;KACJ,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,WAAW;;;;IAI/C,UAAU,MAAM,EAAE;QACd,MAAM,IAAI,KAAK,CAAC,iFAAiF,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1H,CAAC;;;;;;IAMF,yBAAyB,CAAC,SAAS,CAAC,gBAAgB;;;;;IAKpD,UAAU,UAAU,EAAE,WAAW,EAAE;QAC/B,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,YAAY,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAChE,qBAAqB,cAAc,GAAG,EAAE,CAAC;QACzC,qBAAqB,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnD,qBAAqB,kBAAkB,GAAG,EAAE,CAAC;QAC7C,qBAAqB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjD,qBAAqB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrD,qBAAqB,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;QACtD,qBAAqB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACvC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9B,qBAAqB,oBAAoB,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YAC5F,KAAK,qBAAqB,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;gBACzE,mBAAmB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;aACtD;SACJ,CAAC,CAAC;QACH,qBAAqB,QAAQ,GAAG,WAAW,EAAE,CAAC;QAC9C,qBAAqB,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QAClF,qBAAqB,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;;;;QAIlG,qBAAqB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjD,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAC3B,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,IAAI,EAAE;YACxC,qBAAqB,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;YACvD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACrC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;SACxE,CAAC,CAAC;QACH,qBAAqB,aAAa,GAAG,EAAE,CAAC;QACxC,qBAAqB,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClD,qBAAqB,2BAA2B,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7D,KAAK,qBAAqB,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YAChF,qBAAqB,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;YAChE,qBAAqB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YACxE,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;gBAClC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC9B,IAAI,OAAO,CAAC,YAAY,EAAE;oBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;iBACjH;qBACI;oBACD,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;iBAC5C;aACJ;SACJ;QACD,qBAAqB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjD,qBAAqB,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACnG,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,IAAI,EAAE;YACxC,qBAAqB,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;YACvD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACrC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;SACxE,CAAC,CAAC;QACH,UAAU,CAAC,IAAI,CAAC,YAAY;YACxB,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,IAAI,EAAE;gBACxC,qBAAqB,SAAS,sBAAsB,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChF,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;aAC3E,CAAC,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,IAAI,EAAE;gBACxC,qBAAqB,SAAS,sBAAsB,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChF,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;aAC3E,CAAC,CAAC;YACH,aAAa,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;SAClF,CAAC,CAAC;QACH,qBAAqB,UAAU,GAAG,EAAE,CAAC;QACrC,qBAAqB,oBAAoB,GAAG,EAAE,CAAC;QAC/C,KAAK,qBAAqB,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;YAC7E,qBAAqB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YACnD,EAAE,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;gBAC5D,qBAAqB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC3C,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,qBAAqB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC7C,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;oBAC/D,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO;iBACV;gBACD,qBAAqB,cAAc,sBAAsB,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxF,qBAAqB,cAAc,sBAAsB,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxF,qBAAqB,WAAW,sBAAsB,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC;gBACrI,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE;oBACjD,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACvC,OAAO;iBACV;;;gBAGD,IAAI,KAAK,CAAC,oBAAoB,EAAE;oBAC5B,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;oBACrF,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;oBACnF,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5B,OAAO;iBACV;;;;;;gBAMD,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,uBAAuB,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3F,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;gBACpD,qBAAqB,KAAK,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC5F,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/B,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC/H,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;oBAC5D,qBAAqB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACpD,IAAI,KAAK,CAAC,MAAM,EAAE;wBACd,qBAAqB,QAAQ,sBAAsB,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;wBACtF,IAAI,CAAC,QAAQ,EAAE;4BACX,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;yBAC1D;wBACD,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;qBACjE;iBACJ,CAAC,CAAC;gBACH,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;oBAC7D,qBAAqB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACpD,qBAAqB,MAAM,sBAAsB,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrF,IAAI,CAAC,MAAM,EAAE;wBACT,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;qBACzD;oBACD,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC/D,CAAC,CAAC;aACN,CAAC,CAAC;SACN;QACD,IAAI,oBAAoB,CAAC,MAAM,EAAE;YAC7B,qBAAqB,QAAQ,GAAG,EAAE,CAAC;YACnC,oBAAoB,CAAC,OAAO,CAAC,UAAU,WAAW,EAAE;gBAChD,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,WAAW,GAAG,uBAAuB,CAAC,CAAC;gBACvE,EAAE,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;aACnG,CAAC,CAAC;YACH,UAAU,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YACnE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SAC9B;QACD,qBAAqB,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;;;;;QAKvD,qBAAqB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrD,kBAAkB,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;YACxC,qBAAqB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC7C,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC3B,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC1C,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;aACnG;SACJ,CAAC,CAAC;QACH,cAAc,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YACrC,qBAAqB,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAC9C,qBAAqB,eAAe,GAAG,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC/H,eAAe,CAAC,OAAO,CAAC,UAAU,UAAU,EAAE;gBAC1C,eAAe,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACrE,UAAU,CAAC,OAAO,EAAE,CAAC;aACxB,CAAC,CAAC;SACN,CAAC,CAAC;;;;;;;;QAQH,qBAAqB,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE;YACrE,OAAO,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;SAClF,CAAC,CAAC;;QAEH,qBAAqB,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/C,qBAAqB,oBAAoB,GAAG,qBAAqB,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,2BAA2B,EAAE,oBAAoB,EAAE,UAAU,CAAC,CAAC;QAC7J,oBAAoB,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACzC,IAAI,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,EAAE;gBACzE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3B;SACJ,CAAC,CAAC;;QAEH,qBAAqB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9C,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,IAAI,EAAE;YACxC,qBAAqB,CAAC,YAAY,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,mBAAmB,EAAEH,UAAS,CAAC,CAAC;SACrG,CAAC,CAAC;QACH,YAAY,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YACjC,qBAAqB,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpD,qBAAqB,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAClD,aAAa,CAAC,GAAG,CAAC,IAAI,oBAAoBG,QAAgB,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;SAC/E,CAAC,CAAC;QACH,qBAAqB,WAAW,GAAG,EAAE,CAAC;QACtC,qBAAqB,UAAU,GAAG,EAAE,CAAC;QACrC,qBAAqB,oCAAoC,GAAG,EAAE,CAAC;QAC/D,kBAAkB,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;YACxC,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;;;YAGpF,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC3B,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAClC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;oBACnF,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5B,OAAO;iBACV;;;;;;;gBAOD,qBAAqB,qBAAqB,GAAG,oCAAoC,CAAC;gBAClF,IAAI,mBAAmB,CAAC,IAAI,GAAG,CAAC,EAAE;oBAC9B,qBAAqB,GAAG,GAAG,OAAO,CAAC;oBACnC,qBAAqB,YAAY,GAAG,EAAE,CAAC;oBACvC,OAAO,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE;wBACzB,qBAAqB,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBACnE,IAAI,cAAc,EAAE;4BAChB,qBAAqB,GAAG,cAAc,CAAC;4BACvC,MAAM;yBACT;wBACD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;qBAC1B;oBACD,YAAY,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC9G;gBACD,qBAAqB,WAAW,GAAG,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;gBACjK,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;gBAClC,IAAI,qBAAqB,KAAK,oCAAoC,EAAE;oBAChE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC5B;qBACI;oBACD,qBAAqB,aAAa,GAAG,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;oBACvF,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE;wBACvC,MAAM,CAAC,YAAY,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;qBAC5D;oBACD,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC/B;aACJ;iBACI;gBACD,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;;;;gBAInF,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAClC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC/B;aACJ;SACJ,CAAC,CAAC;;QAEH,UAAU,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;;;YAGjC,qBAAqB,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC/E,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAC/C,qBAAqB,WAAW,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;gBAC1E,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;aACrC;SACJ,CAAC,CAAC;;;;QAIH,cAAc,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YACrC,IAAI,MAAM,CAAC,YAAY,EAAE;gBACrB,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aAChD;iBACI;gBACD,MAAM,CAAC,OAAO,EAAE,CAAC;aACpB;SACJ,CAAC,CAAC;;;;QAIH,KAAK,qBAAqB,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YAClE,qBAAqB,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;YAClD,qBAAqB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YACxE,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;;;;YAItC,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY;gBAC/B,SAAS;YACb,qBAAqB,OAAO,GAAG,EAAE,CAAC;;;;YAIlC,IAAI,eAAe,CAAC,IAAI,EAAE;gBACtB,qBAAqB,oBAAoB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACzE,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,EAAE;oBACrD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;iBACrD;gBACD,qBAAqB,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;gBACpG,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACnE,qBAAqB,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnF,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;wBACzC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;qBAC/C;iBACJ;aACJ;YACD,qBAAqB,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAC3F,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtB,6BAA6B,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;aAC/D;iBACI;gBACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAClC;SACJ;;QAED,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,WAAW,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YAClC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,YAAY;gBACtB,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,qBAAqB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC3D,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClC,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,EAAE,CAAC;SACjB,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;KACtB,CAAC;;;;;;IAMF,yBAAyB,CAAC,SAAS,CAAC,mBAAmB;;;;;IAKvD,UAAU,WAAW,EAAE,OAAO,EAAE;QAC5B,qBAAqB,YAAY,GAAG,KAAK,CAAC;QAC1C,qBAAqB,OAAO,qBAAqB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACxE,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa;YAChC,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;YAClC,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC;YACzC,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;YACjC,YAAY,GAAG,IAAI,CAAC;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC;KACzF,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,UAAU;;;;IAI9C,UAAU,QAAQ,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;;;;;IAKvD,yBAAyB,CAAC,SAAS,CAAC,wBAAwB;;;;IAI5D,UAAU,QAAQ,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;;;;;;;;;IAS3D,yBAAyB,CAAC,SAAS,CAAC,mBAAmB;;;;;;;;IAQvD,UAAU,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE;QACzE,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,gBAAgB,EAAE;YAClB,qBAAqB,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACvF,IAAI,qBAAqB,EAAE;gBACvB,OAAO,GAAG,qBAAqB,CAAC;aACnC;SACJ;aACI;YACD,qBAAqB,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACzE,IAAI,cAAc,EAAE;gBAChB,qBAAqB,oBAAoB,GAAG,CAAC,YAAY,IAAI,YAAY,IAAI,UAAU,CAAC;gBACxF,cAAc,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;oBACrC,IAAI,MAAM,CAAC,MAAM;wBACb,OAAO;oBACX,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW;wBAC1D,OAAO;oBACX,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACxB,CAAC,CAAC;aACN;SACJ;QACD,IAAI,WAAW,IAAI,WAAW,EAAE;YAC5B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,MAAM,EAAE;gBACvC,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;oBAChD,OAAO,KAAK,CAAC;gBACjB,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;oBAChD,OAAO,KAAK,CAAC;gBACjB,OAAO,IAAI,CAAC;aACf,CAAC,CAAC;SACN;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;;;;;;;IAOF,yBAAyB,CAAC,SAAS,CAAC,qBAAqB;;;;;;IAMzD,UAAU,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAE;QACvD,qBAAqB,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC3D,qBAAqB,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;QAGvD,qBAAqB,iBAAiB,GAAG,WAAW,CAAC,mBAAmB,GAAG,SAAS,GAAG,WAAW,CAAC;QACnG,qBAAqB,iBAAiB,GAAG,WAAW,CAAC,mBAAmB,GAAG,SAAS,GAAG,WAAW,CAAC;QACnG,IAAI,OAAO,GAAG,UAAU,mBAAmB,EAAE;YACzC,qBAAqB,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;YAC3D,qBAAqB,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;YAChE,qBAAqB,OAAO,GAAG,eAAe,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACnF,qBAAqB,eAAe,GAAG,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;YACxJ,eAAe,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;gBACtC,qBAAqB,UAAU,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;gBAC5E,IAAI,UAAU,CAAC,aAAa,EAAE;oBAC1B,UAAU,CAAC,aAAa,EAAE,CAAC;iBAC9B;gBACD,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACxB,CAAC,CAAC;SACN,CAAC;QACF,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YAC/D,IAAI,mBAAmB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SAChC;;;QAGD,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;KACpD,CAAC;;;;;;;;;;IAUF,yBAAyB,CAAC,SAAS,CAAC,eAAe;;;;;;;;;IASnD,UAAU,WAAW,EAAE,WAAW,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,EAAE;QACvG,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC3D,qBAAqB,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;;;QAGvD,qBAAqB,iBAAiB,GAAG,EAAE,CAAC;QAC5C,qBAAqB,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrD,qBAAqB,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAChD,qBAAqB,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,mBAAmB,EAAE;YAC1F,qBAAqB,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;YAC3D,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;YAEjC,qBAAqB,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;YACrD,IAAI,OAAO,IAAI,OAAO,CAAC,oBAAoB;gBACvC,OAAO,IAAI,mBAAmB,EAAE,CAAC;YACrC,qBAAqB,gBAAgB,GAAG,OAAO,KAAK,WAAW,CAAC;YAChE,qBAAqB,eAAe,GAAG,mBAAmB,CAAC,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,kBAAkB;iBAC/G,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;iBAChD,MAAM,CAAC,UAAU,CAAC,EAAE;;;;;gBAKrB,qBAAqB,EAAE,qBAAqB,CAAC,CAAC,CAAC;gBAC/C,OAAO,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,CAAC;aACtD,CAAC,CAAC;YACH,qBAAqB,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3D,qBAAqB,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC7D,qBAAqB,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,mBAAmB,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YACpJ,qBAAqB,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;;;YAGlG,IAAI,mBAAmB,CAAC,WAAW,IAAI,iBAAiB,EAAE;gBACtD,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aAC/B;YACD,IAAI,gBAAgB,EAAE;gBAClB,qBAAqB,aAAa,GAAG,IAAI,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBACtG,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACpC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACzC;YACD,OAAO,MAAM,CAAC;SACjB,CAAC,CAAC;QACH,iBAAiB,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YACxC,eAAe,CAAC,KAAK,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChF,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,kBAAkB,CAAC,KAAK,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;SACpH,CAAC,CAAC;QACH,mBAAmB,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,QAAQ,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC;QACtG,qBAAqB,MAAM,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;QACjE,MAAM,CAAC,SAAS,CAAC,YAAY;YACzB,mBAAmB,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,WAAW,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC;YACzG,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;SAChD,CAAC,CAAC;;;QAGH,cAAc,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,eAAe,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7G,OAAO,MAAM,CAAC;KACjB,CAAC;;;;;;;IAOF,yBAAyB,CAAC,SAAS,CAAC,YAAY;;;;;;IAMhD,UAAU,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE;QAC/C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;SAC5I;;;QAGD,OAAO,IAAI,mBAAmB,EAAE,CAAC;KACpC,CAAC;IACF,OAAO,yBAAyB,CAAC;CACpC,EAAE,CAAC,CAAC;AACL,AAuCA,IAAI,yBAAyB,kBAAkB,YAAY;IACvD,SAAS,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE;QAClE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACzC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB;;;;;IAKD,yBAAyB,CAAC,SAAS,CAAC,aAAa;;;;IAIjD,UAAU,MAAM,EAAE;QACd,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI,CAAC,mBAAmB;YACxB,OAAO;QACX,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;YACxD,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;SAC7H,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,mBAAmB,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;KAC5C,CAAC;;;;IAIF,yBAAyB,CAAC,SAAS,CAAC,aAAa;;;IAGjD,YAAY,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;;;;;IAKrC,yBAAyB,CAAC,SAAS,CAAC,gBAAgB;;;;IAIpD,UAAU,MAAM,EAAE;QACd,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,CAAC,qBAAqB,IAAI,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,CAAC,eAAe,EAAE;YACnB,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;SACtE;QACD,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QACtD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;KAC7D,CAAC;;;;;;IAMF,yBAAyB,CAAC,SAAS,CAAC,WAAW;;;;;IAK/C,UAAU,IAAI,EAAE,QAAQ,EAAE;QACtB,eAAe,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACnE,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,MAAM;;;;IAI1C,UAAU,EAAE,EAAE;QACV,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KAC3B,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,OAAO;;;;IAI3C,UAAU,EAAE,EAAE;QACV,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAC5B,CAAC;;;;;IAKF,yBAAyB,CAAC,SAAS,CAAC,SAAS;;;;IAI7C,UAAU,EAAE,EAAE;QACV,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC9B,CAAC;;;;IAIF,yBAAyB,CAAC,SAAS,CAAC,IAAI;;;IAGxC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;;;;IAIrC,yBAAyB,CAAC,SAAS,CAAC,UAAU;;;IAG9C,YAAY,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;;;;IAIxE,yBAAyB,CAAC,SAAS,CAAC,IAAI;;;IAGxC,YAAY,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;;;;IAIrD,yBAAyB,CAAC,SAAS,CAAC,KAAK;;;IAGzC,YAAY,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;;;;IAItD,yBAAyB,CAAC,SAAS,CAAC,OAAO;;;IAG3C,YAAY,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;;;;IAIxD,yBAAyB,CAAC,SAAS,CAAC,MAAM;;;IAG1C,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;;;;IAIvC,yBAAyB,CAAC,SAAS,CAAC,OAAO;;;IAG3C,YAAY;QACR,mBAAmB,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAC;;;;IAIF,yBAAyB,CAAC,SAAS,CAAC,KAAK;;;IAGzC,YAAY,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;;;;;IAKtD,yBAAyB,CAAC,SAAS,CAAC,WAAW;;;;IAI/C,UAAU,CAAC,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC/B;KACJ,CAAC;;;;IAIF,yBAAyB,CAAC,SAAS,CAAC,WAAW;;;IAG/C,YAAY,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;IACrE,MAAM,CAAC,cAAc,CAAC,yBAAyB,CAAC,SAAS,EAAE,WAAW,EAAE;QACpE,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QAC9C,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;;IAMH,yBAAyB,CAAC,SAAS,CAAC,eAAe;;;;IAInD,UAAU,SAAS,EAAE;QACjB,qBAAqB,CAAC,qBAAqB,IAAI,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,CAAC,eAAe,EAAE;YACnB,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SAChC;KACJ,CAAC;IACF,OAAO,yBAAyB,CAAC;CACpC,EAAE,CAAC,CAAC;AACL,AAuBA;;;;;;AAMA,SAAS,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IACzC,qBAAqB,aAAa,CAAC;IACnC,IAAI,GAAG,YAAY,GAAG,EAAE;QACpB,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,aAAa,EAAE;YACf,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtB,qBAAqB,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC1D,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClC;YACD,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC3B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACnB;SACJ;KACJ;SACI;QACD,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,IAAI,aAAa,EAAE;YACf,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtB,qBAAqB,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC1D,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClC;YACD,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC3B,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;aACnB;SACJ;KACJ;IACD,OAAO,aAAa,CAAC;CACxB;;;;;AAKD,SAAS,qBAAqB,CAAC,KAAK,EAAE;;;;IAIlC,OAAO,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;CACvC;;;;;AAKD,SAAS,aAAa,CAAC,IAAI,EAAE;IACzB,OAAO,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;CACzC;;;;;AAKD,SAAS,mBAAmB,CAAC,SAAS,EAAE;IACpC,OAAO,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,MAAM,CAAC;CACtD;;;;;;AAMD,SAAS,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE;IAClC,qBAAqB,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;IACtD,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC;IACvD,OAAO,QAAQ,CAAC;CACnB;;;;;;;;;AASD,SAAS,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE;IACvF,qBAAqB,SAAS,GAAG,EAAE,CAAC;IACpC,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvF,qBAAqB,cAAc,GAAG,EAAE,CAAC;IACzC,eAAe,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,OAAO,EAAE;QAC9C,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;YAC1B,qBAAqB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;;;YAG7F,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC7B,OAAO,CAAC,YAAY,CAAC,GAAG,0BAA0B,CAAC;gBACnD,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAChC;SACJ,CAAC,CAAC;QACH,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAClC,CAAC,CAAC;;;IAGH,qBAAqB,CAAC,GAAG,CAAC,CAAC;IAC3B,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvF,OAAO,cAAc,CAAC;CACzB;;;;;;AAMD,SAAS,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE;IAChC,qBAAqB,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;IACzC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACjE,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;QACjB,OAAO,OAAO,CAAC;IACnB,qBAAqB,SAAS,GAAG,CAAC,CAAC;IACnC,qBAAqB,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IAC9C,qBAAqB,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;;;;;IAK9C,SAAS,OAAO,CAAC,IAAI,EAAE;QACnB,IAAI,CAAC,IAAI;YACL,OAAO,SAAS,CAAC;QACrB,qBAAqB,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,IAAI;YACJ,OAAO,IAAI,CAAC;QAChB,qBAAqB,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAErB,IAAI,GAAG,MAAM,CAAC;SACjB;aACI,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAE1B,IAAI,GAAG,SAAS,CAAC;SACpB;aACI;;YAED,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;SAC1B;QACD,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;KACf;IACD,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;QAC1B,qBAAqB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,IAAI,KAAK,SAAS,EAAE;6BACH,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;SACrD;KACJ,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;CAClB;AACD,IAAqB,iBAAiB,GAAG,WAAW,CAAC;AACrD,AAcA;;;;;AAKA,SAAS,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE;IAClC,IAAI,OAAO,CAAC,SAAS,EAAE;QACnB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KACpC;SACI;QACD,qBAAqB,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;SAC7C;QACD,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;KAC7B;CACJ;;;;;;AAMD,SAAS,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE;IACrC,IAAI,OAAO,CAAC,SAAS,EAAE;QACnB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KACvC;SACI;QACD,qBAAqB,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC1D,IAAI,OAAO,EAAE;YACT,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;SAC7B;KACJ;CACJ;;;;;;;AAOD,SAAS,6BAA6B,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;IAC7D,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;CACjG;;;;;AAKD,SAAS,mBAAmB,CAAC,OAAO,EAAE;IAClC,qBAAqB,YAAY,GAAG,EAAE,CAAC;IACvC,yBAAyB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACjD,OAAO,YAAY,CAAC;CACvB;;;;;;AAMD,SAAS,yBAAyB,CAAC,OAAO,EAAE,YAAY,EAAE;IACtD,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtD,qBAAqB,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,MAAM,YAAYC,qBAAoB,EAAE;YACxC,yBAAyB,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;SAC3D;aACI;YACD,YAAY,CAAC,IAAI,mBAAmB,MAAM,EAAE,CAAC;SAChD;KACJ;CACJ;;;;;;AAMD,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IACrB,qBAAqB,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACzC,qBAAqB,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM;QACtB,OAAO,KAAK,CAAC;IACjB,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,qBAAqB,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;YAC9C,OAAO,KAAK,CAAC;KACpB;IACD,OAAO,IAAI,CAAC;CACf;;;;;;;AAOD,SAAS,sBAAsB,CAAC,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE;IAChF,qBAAqB,SAAS,GAAG,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACnE,IAAI,CAAC,SAAS;QACV,OAAO,KAAK,CAAC;IACjB,qBAAqB,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACjE,IAAI,QAAQ,EAAE;QACV,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,EAAE,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;KACzE;SACI;QACD,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;KAC/C;IACD,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC;CACf;;AChxED;;;;AAIA,AAKA,IAAI,eAAe,kBAAkB,YAAY;IAC7C,SAAS,eAAe,CAAC,OAAO,EAAE,UAAU,EAAE;QAC1C,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,UAAU,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC;QACzD,IAAI,CAAC,iBAAiB,GAAG,IAAI,yBAAyB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,UAAU,OAAO,EAAE,OAAO,EAAE;YACnE,OAAO,KAAK,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACpD,CAAC;KACL;;;;;;;;;IASD,eAAe,CAAC,SAAS,CAAC,eAAe;;;;;;;;IAQzC,UAAU,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;QAC7D,qBAAqB,QAAQ,GAAG,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC;QACzD,qBAAqB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE;YACV,qBAAqB,MAAM,GAAG,EAAE,CAAC;YACjC,qBAAqB,GAAG,qBAAqB,iBAAiB,CAAC,IAAI,CAAC,OAAO,oBAAoB,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;YACnH,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,GAAG,0DAA0D,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;aAC1I;YACD,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;SAC1C;QACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KACtE,CAAC;;;;;;IAMF,eAAe,CAAC,SAAS,CAAC,QAAQ;;;;;IAKlC,UAAU,WAAW,EAAE,WAAW,EAAE;QAChC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;KAC7D,CAAC;;;;;;IAMF,eAAe,CAAC,SAAS,CAAC,OAAO;;;;;IAKjC,UAAU,WAAW,EAAE,OAAO,EAAE;QAC5B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;KACxD,CAAC;;;;;;;;IAQF,eAAe,CAAC,SAAS,CAAC,QAAQ;;;;;;;IAOlC,UAAU,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE;QAClD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;KACjF,CAAC;;;;;;;IAOF,eAAe,CAAC,SAAS,CAAC,QAAQ;;;;;;IAMlC,UAAU,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;QACrC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KACpE,CAAC;;;;;;IAMF,eAAe,CAAC,SAAS,CAAC,iBAAiB;;;;;IAK3C,UAAU,OAAO,EAAE,OAAO,EAAE;QACxB,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAClE,CAAC;;;;;;;;IAQF,eAAe,CAAC,SAAS,CAAC,OAAO;;;;;;;IAOjC,UAAU,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC7C,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC3B,IAAI,EAAE,GAAG,oBAAoB,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACpE,qBAAqB,IAAI,qBAAqB,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SAC3D;aACI;YACD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SACzE;KACJ,CAAC;;;;;;;;;IASF,eAAe,CAAC,SAAS,CAAC,MAAM;;;;;;;;IAQhC,UAAU,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE;;QAE7D,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC5B,IAAI,EAAE,GAAG,oBAAoB,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACrE,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;SACrE;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KAC/F,CAAC;;;;;IAKF,eAAe,CAAC,SAAS,CAAC,KAAK;;;;IAI/B,UAAU,WAAW,EAAE;QACnB,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE;QACjD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KAC7C,CAAC;IACF,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE;QACxD,GAAG;;;QAGH,YAAY;YACR,OAAO,mBAAmB,IAAI,CAAC,iBAAiB,CAAC,OAAO;iBACnD,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;SAChE;QACD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;IAIH,eAAe,CAAC,SAAS,CAAC,iBAAiB;;;IAG3C,YAAY,EAAE,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC;IACnE,OAAO,eAAe,CAAC;CAC1B,EAAE,CAAC;;ACzMJ;;;;AAIA,AACA,IAAI,mBAAmB,kBAAkB,YAAY;IACjD,SAAS,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE;QACvE,IAAI,eAAe,KAAK,KAAK,CAAC,EAAE,EAAE,eAAe,GAAG,EAAE,CAAC,EAAE;QACzD,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,qBAAqB,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,IAAI,8BAA8B,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7D,eAAe,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;gBACtC,qBAAqB,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;aACtG,CAAC,CAAC;SACN;KACJ;;;;IAID,mBAAmB,CAAC,SAAS,CAAC,SAAS;;;IAGvC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;KACJ,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,IAAI;;;IAGlC,YAAY;QACR,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;KACpC,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,YAAY;;;IAG1C,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI,CAAC,YAAY;YACjB,OAAO;QACX,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,qBAAqB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7G,qBAAqB,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3E,IAAI,kBAAkB,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE;YAC/C,qBAAqB,kBAAkB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACvD,qBAAqB,mBAAmB,GAAG,EAAE,CAAC;YAC9C,kBAAkB,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBACvC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAC1C,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAClC;gBACD,kBAAkB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aACzD,CAAC,CAAC;YACH,IAAI,mBAAmB,CAAC,MAAM,EAAE;gBAC5B,qBAAqB,MAAM,GAAG,IAAI,CAAC;gBACnC,IAAI,OAAO,GAAG,YAAY;oBACtB,qBAAqB,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACvC,mBAAmB,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;wBACxC,EAAE,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBAClD,CAAC,CAAC;iBACN,CAAC;;gBAEF,KAAK,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACxD,OAAO,EAAE,CAAC;iBACb;aACJ;SACJ;QACD,mBAAmB,IAAI,GAAG,SAAS;YAC/B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACrE,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAC9E,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;KACxF,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,yBAAyB;;;IAGvD,YAAY;;QAER,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;aACI;YACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SAC1B;KACJ,CAAC;;;;;;;;;IASF,mBAAmB,CAAC,SAAS,CAAC,oBAAoB;;;;;;;IAOlD,UAAU,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;;;QAGnC,yBAAyB,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;KACpE,CAAC;;;;;IAKF,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;;IAIrC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAK7C,mBAAmB,CAAC,SAAS,CAAC,MAAM;;;;IAIpC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAK5C,mBAAmB,CAAC,SAAS,CAAC,SAAS;;;;IAIvC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;IAI/C,mBAAmB,CAAC,SAAS,CAAC,IAAI;;;IAGlC,YAAY;QACR,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;KACzB,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,KAAK;;;IAGnC,YAAY;QACR,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;KAC1B,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,MAAM;;;IAGpC,YAAY;QACR,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAC3B,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,KAAK;;;IAGnC,YAAY;QACR,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;KACzB,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,oBAAoB;;;IAGlD,YAAY;QACR,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;SAC3B;KACJ,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;IAGrC,YAAY;QACR,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,EAAE,CAAC;KACf,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,UAAU;;;IAGxC,YAAY,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;;;IAItC,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;IAGrC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SAC3B;KACJ,CAAC;;;;;IAKF,mBAAmB,CAAC,SAAS,CAAC,WAAW;;;;IAIzC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;;;;IAI7D,mBAAmB,CAAC,SAAS,CAAC,WAAW;;;IAGzC,YAAY,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC/D,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAE;QAC9D,GAAG;;;QAGH,YAAY,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE;QACpD,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;IAIH,mBAAmB,CAAC,SAAS,CAAC,aAAa;;;IAG3C,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,MAAM,GAAG,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBACrD,IAAI,IAAI,IAAI,QAAQ,EAAE;oBAClB,MAAM,CAAC,IAAI,CAAC;wBACR,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;iBACzF;aACJ,CAAC,CAAC;SACN;QACD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;KACjC,CAAC;;;;;;IAMF,mBAAmB,CAAC,SAAS,CAAC,eAAe;;;;IAI7C,UAAU,SAAS,EAAE;QACjB,qBAAqB,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACzF,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAChD,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KACtB,CAAC;IACF,OAAO,mBAAmB,CAAC;CAC9B,EAAE,CAAC,CAAC;AACL,AAyCA;;;;;AAKA,SAAS,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE;IAClC,OAAO,mBAAmB,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;CACtE;;ACtWD;;;;AAIA,AAEA,IAAI,mBAAmB,kBAAkB,YAAY;IACjD,SAAS,mBAAmB,GAAG;KAC9B;;;;;IAKD,mBAAmB,CAAC,SAAS,CAAC,qBAAqB;;;;IAInD,UAAU,IAAI,EAAE,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;;;;;;IAMxD,mBAAmB,CAAC,SAAS,CAAC,cAAc;;;;;IAK5C,UAAU,OAAO,EAAE,QAAQ,EAAE;QACzB,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC5C,CAAC;;;;;;IAMF,mBAAmB,CAAC,SAAS,CAAC,eAAe;;;;;IAK7C,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;;;;;;;IAO9D,mBAAmB,CAAC,SAAS,CAAC,KAAK;;;;;;IAMnC,UAAU,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAChC,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAChD,CAAC;;;;;;;IAOF,mBAAmB,CAAC,SAAS,CAAC,YAAY;;;;;;IAM1C,UAAU,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE;QACnC,yBAAyB,mBAAmB,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE;KACzF,CAAC;;;;;;;;;;IAUF,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;;;;;;;IASrC,UAAU,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE;QACpE,IAAI,eAAe,KAAK,KAAK,CAAC,EAAE,EAAE,eAAe,GAAG,EAAE,CAAC,EAAE;QACzD,qBAAqB,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU,CAAC;QAC7D,qBAAqB,aAAa,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;;QAGtF,IAAI,MAAM,EAAE;YACR,aAAa,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SACpC;QACD,qBAAqB,2BAA2B,qBAAqB,eAAe,CAAC,MAAM,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,YAAY,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;QAClK,OAAO,IAAI,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,2BAA2B,CAAC,CAAC;KAClG,CAAC;IACF,OAAO,mBAAmB,CAAC;CAC9B,EAAE,CAAC,CAAC;AACL,AACA;;;AAGA,AAAO,SAAS,qBAAqB,GAAG;IACpC,OAAO,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,mBAAmB,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,UAAU,CAAC;CACpH;;AC9GD;;;GAGG;;ACHH;;;;;;;;;;GAUG;;ACVH;;;;;;;;;;;;;;;GAeG;;ACfH;;;;;;GAMG;;;;"}