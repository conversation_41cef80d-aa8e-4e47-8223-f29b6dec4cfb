{"version": 3, "file": "te.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/te.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,YAAY,EAAE,MAAM,EAAE,WAAW;YACjC,UAAU,EAAE,QAAQ;SACrB;QACD,AADE;KAEH;IACD,AADE;IACA,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAC5F,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'అర్ధరాత్రి', 'ఉదయం', 'మధ్యాహ్నం',\n      'సాయంత్రం', 'రాత్రి'\n    ],\n    ,\n  ],\n  , ['00:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'], ['21:00', '06:00']]\n];\n"]}