{"version": 3, "file": "fromEvent.js", "sourceRoot": "", "sources": ["../../../src/add/observable/fromEvent.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,0BAA6C,4BAA4B,CAAC,CAAA;AAE1E,uBAAU,CAAC,SAAS,GAAG,qBAAe,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { fromEvent as staticFromEvent } from '../../observable/fromEvent';\n\nObservable.fromEvent = staticFromEvent;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let fromEvent: typeof staticFromEvent;\n  }\n}"]}