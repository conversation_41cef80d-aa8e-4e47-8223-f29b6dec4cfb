{"version": 3, "file": "cdk-stepper.umd.js", "sources": ["../../src/cdk/stepper/stepper-module.ts", "../../src/cdk/stepper/stepper-button.ts", "../../src/cdk/stepper/stepper.ts", "../../src/cdk/stepper/step-label.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkStepper, CdkStep} from './stepper';\nimport {CommonModule} from '@angular/common';\nimport {CdkStepLabel} from './step-label';\nimport {CdkStepperNext, CdkStepperPrevious} from './stepper-button';\nimport {BidiModule} from '@angular/cdk/bidi';\n\n@NgModule({\n  imports: [BidiModule, CommonModule],\n  exports: [CdkStep, CdkStepper, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n  declarations: [CdkStep, CdkStepper, CdkStepLabel, CdkStepperNext, CdkStepperPrevious]\n})\nexport class CdkStepperModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input} from '@angular/core';\nimport {CdkStepper} from './stepper';\n\n/** But<PERSON> that moves to the next step in a stepper workflow. */\n@Directive({\n  selector: 'button[cdkStepperNext]',\n  host: {\n    '(click)': '_stepper.next()',\n    '[type]': 'type',\n  }\n})\nexport class CdkStepperNext {\n  /** Type of the next button. Defaults to \"submit\" if not specified. */\n  @Input() type: string = 'submit';\n\n  constructor(public _stepper: CdkStepper) {}\n}\n\n/** But<PERSON> that moves to the previous step in a stepper workflow. */\n@Directive({\n  selector: 'button[cdkStepperPrevious]',\n  host: {\n    '(click)': '_stepper.previous()',\n    '[type]': 'type',\n  }\n})\nexport class CdkStepperPrevious {\n  /** Type of the previous button. Defaults to \"button\" if not specified. */\n  @Input() type: string = 'button';\n\n  constructor(public _stepper: CdkStepper) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ContentChildren,\n  EventEmitter,\n  Input,\n  Output,\n  QueryList,\n  Directive,\n  ElementRef,\n  Component,\n  ContentChild,\n  ViewChild,\n  TemplateRef,\n  ViewEncapsulation,\n  Optional,\n  Inject,\n  forwardRef,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  OnChanges,\n  OnDestroy\n} from '@angular/core';\nimport {\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  DOWN_ARROW,\n  UP_ARROW,\n  ENTER,\n  SPACE,\n  HOME,\n  END,\n} from '@angular/cdk/keycodes';\nimport {CdkStepLabel} from './step-label';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {AbstractControl} from '@angular/forms';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {Subject} from 'rxjs/Subject';\n\n/** Used to generate unique ID for each stepper component. */\nlet nextId = 0;\n\n/**\n * Position state of the content of each step in stepper that is used for transitioning\n * the content into correct position upon step selection change.\n */\nexport type StepContentPositionState = 'previous' | 'current' | 'next';\n\n/** Possible orientation of a stepper. */\nexport type StepperOrientation = 'horizontal' | 'vertical';\n\n/** Change event emitted on selection changes. */\nexport class StepperSelectionEvent {\n  /** Index of the step now selected. */\n  selectedIndex: number;\n\n  /** Index of the step previously selected. */\n  previouslySelectedIndex: number;\n\n  /** The step instance now selected. */\n  selectedStep: CdkStep;\n\n  /** The step instance previously selected. */\n  previouslySelectedStep: CdkStep;\n}\n\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-step',\n  exportAs: 'cdkStep',\n  templateUrl: 'step.html',\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class CdkStep implements OnChanges {\n  /** Template for step label if it exists. */\n  @ContentChild(CdkStepLabel) stepLabel: CdkStepLabel;\n\n  /** Template for step content. */\n  @ViewChild(TemplateRef) content: TemplateRef<any>;\n\n  /** The top level abstract control of the step. */\n  @Input() stepControl: AbstractControl;\n\n  /** Whether user has seen the expanded step content or not. */\n  interacted = false;\n\n  /** Label of the step. */\n  @Input() label: string;\n\n  /** Whether the user can return to this step once it has been marked as complted. */\n  @Input()\n  get editable(): boolean { return this._editable; }\n  set editable(value: boolean) {\n    this._editable = coerceBooleanProperty(value);\n  }\n  private _editable = true;\n\n  /** Whether the completion of step is optional. */\n  @Input()\n  get optional(): boolean { return this._optional; }\n  set optional(value: boolean) {\n    this._optional = coerceBooleanProperty(value);\n  }\n  private _optional = false;\n\n  /** Whether step is marked as completed. */\n  @Input()\n  get completed(): boolean {\n    return this._customCompleted == null ? this._defaultCompleted : this._customCompleted;\n  }\n  set completed(value: boolean) {\n    this._customCompleted = coerceBooleanProperty(value);\n  }\n  private _customCompleted: boolean | null = null;\n\n  private get _defaultCompleted() {\n    return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n  }\n\n  constructor(@Inject(forwardRef(() => CdkStepper)) private _stepper: CdkStepper) { }\n\n  /** Selects this step component. */\n  select(): void {\n    this._stepper.selected = this;\n  }\n\n  /** Resets the step to its initial state. Note that this includes resetting form data. */\n  reset(): void {\n    this.interacted = false;\n\n    if (this._customCompleted != null) {\n      this._customCompleted = false;\n    }\n\n    if (this.stepControl) {\n      this.stepControl.reset();\n    }\n  }\n\n  ngOnChanges() {\n    // Since basically all inputs of the MatStep get proxied through the view down to the\n    // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n    this._stepper._stateChanged();\n  }\n}\n\n@Directive({\n  selector: '[cdkStepper]',\n  exportAs: 'cdkStepper',\n})\nexport class CdkStepper implements OnDestroy {\n  /** Emits when the component is destroyed. */\n  protected _destroyed = new Subject<void>();\n\n  /** The list of step components that the stepper is holding. */\n  @ContentChildren(CdkStep) _steps: QueryList<CdkStep>;\n\n  /** The list of step headers of the steps in the stepper. */\n  _stepHeader: QueryList<ElementRef>;\n\n  /** Whether the validity of previous steps should be checked or not. */\n  @Input()\n  get linear(): boolean { return this._linear; }\n  set linear(value: boolean) { this._linear = coerceBooleanProperty(value); }\n  private _linear = false;\n\n  /** The index of the selected step. */\n  @Input()\n  get selectedIndex() { return this._selectedIndex; }\n  set selectedIndex(index: number) {\n    if (this._steps) {\n      // Ensure that the index can't be out of bounds.\n      if (index < 0 || index > this._steps.length - 1) {\n        throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n      }\n\n      if (this._anyControlsInvalidOrPending(index) || index < this._selectedIndex &&\n          !this._steps.toArray()[index].editable) {\n        // remove focus from clicked step header if the step is not able to be selected\n        this._stepHeader.toArray()[index].nativeElement.blur();\n      } else if (this._selectedIndex != index) {\n        this._emitStepperSelectionEvent(index);\n        this._focusIndex = this._selectedIndex;\n      }\n    } else {\n      this._selectedIndex = this._focusIndex = index;\n    }\n  }\n  private _selectedIndex = 0;\n\n  /** The step that is selected. */\n  @Input()\n  get selected(): CdkStep { return this._steps.toArray()[this.selectedIndex]; }\n  set selected(step: CdkStep) {\n    this.selectedIndex = this._steps.toArray().indexOf(step);\n  }\n\n  /** Event emitted when the selected step has changed. */\n  @Output() selectionChange: EventEmitter<StepperSelectionEvent>\n      = new EventEmitter<StepperSelectionEvent>();\n\n  /** The index of the step that the focus can be set. */\n  _focusIndex: number = 0;\n\n  /** Used to track unique ID for each stepper component. */\n  _groupId: number;\n\n  protected _orientation: StepperOrientation = 'horizontal';\n\n  constructor(\n    @Optional() private _dir: Directionality,\n    private _changeDetectorRef: ChangeDetectorRef) {\n    this._groupId = nextId++;\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Selects and focuses the next step in list. */\n  next(): void {\n    this.selectedIndex = Math.min(this._selectedIndex + 1, this._steps.length - 1);\n  }\n\n  /** Selects and focuses the previous step in list. */\n  previous(): void {\n    this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n  }\n\n  /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n  reset(): void {\n    this.selectedIndex = 0;\n    this._steps.forEach(step => step.reset());\n    this._stateChanged();\n  }\n\n  /** Returns a unique id for each step label element. */\n  _getStepLabelId(i: number): string {\n    return `cdk-step-label-${this._groupId}-${i}`;\n  }\n\n  /** Returns unique id for each step content element. */\n  _getStepContentId(i: number): string {\n    return `cdk-step-content-${this._groupId}-${i}`;\n  }\n\n  /** Marks the component to be change detected. */\n  _stateChanged() {\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Returns position state of the step with the given index. */\n  _getAnimationDirection(index: number): StepContentPositionState {\n    const position = index - this._selectedIndex;\n    if (position < 0) {\n      return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n    } else if (position > 0) {\n      return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n    }\n    return 'current';\n  }\n\n  /** Returns the type of icon to be displayed. */\n  _getIndicatorType(index: number): 'number' | 'edit' | 'done' {\n    const step = this._steps.toArray()[index];\n    if (!step.completed || this._selectedIndex == index) {\n      return 'number';\n    } else {\n      return step.editable ? 'edit' : 'done';\n    }\n  }\n\n  private _emitStepperSelectionEvent(newIndex: number): void {\n    const stepsArray = this._steps.toArray();\n    this.selectionChange.emit({\n      selectedIndex: newIndex,\n      previouslySelectedIndex: this._selectedIndex,\n      selectedStep: stepsArray[newIndex],\n      previouslySelectedStep: stepsArray[this._selectedIndex],\n    });\n    this._selectedIndex = newIndex;\n    this._stateChanged();\n  }\n\n  _onKeydown(event: KeyboardEvent) {\n    const keyCode = event.keyCode;\n\n    // Note that the left/right arrows work both in vertical and horizontal mode.\n    if (keyCode === RIGHT_ARROW) {\n      this._layoutDirection() === 'rtl' ? this._focusPreviousStep() : this._focusNextStep();\n      event.preventDefault();\n    }\n\n    if (keyCode === LEFT_ARROW) {\n      this._layoutDirection() === 'rtl' ? this._focusNextStep() : this._focusPreviousStep();\n      event.preventDefault();\n    }\n\n    // Note that the up/down arrows only work in vertical mode.\n    // See: https://www.w3.org/TR/wai-aria-practices-1.1/#tabpanel\n    if (this._orientation === 'vertical' && (keyCode === UP_ARROW || keyCode === DOWN_ARROW)) {\n      keyCode === UP_ARROW ? this._focusPreviousStep() : this._focusNextStep();\n      event.preventDefault();\n    }\n\n    if (keyCode === SPACE || keyCode === ENTER) {\n      this.selectedIndex = this._focusIndex;\n      event.preventDefault();\n    }\n\n    if (keyCode === HOME) {\n      this._focusStep(0);\n      event.preventDefault();\n    }\n\n    if (keyCode === END) {\n      this._focusStep(this._steps.length - 1);\n      event.preventDefault();\n    }\n  }\n\n  private _focusNextStep() {\n    this._focusStep((this._focusIndex + 1) % this._steps.length);\n  }\n\n  private _focusPreviousStep() {\n    this._focusStep((this._focusIndex + this._steps.length - 1) % this._steps.length);\n  }\n\n  private _focusStep(index: number) {\n    this._focusIndex = index;\n    this._stepHeader.toArray()[this._focusIndex].nativeElement.focus();\n  }\n\n  private _anyControlsInvalidOrPending(index: number): boolean {\n    const steps = this._steps.toArray();\n\n    steps[this._selectedIndex].interacted = true;\n\n    if (this._linear && index >= 0) {\n      return steps.slice(0, index).some(step => {\n        const control = step.stepControl;\n        const isIncomplete = control ? (control.invalid || control.pending) : !step.completed;\n        return isIncomplete && !step.optional;\n      });\n    }\n\n    return false;\n  }\n\n  private _layoutDirection(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, TemplateRef} from '@angular/core';\n\n@Directive({\n  selector: '[cdkStepLabel]',\n})\nexport class CdkStepLabel {\n  constructor(/** @docs-private */ public template: TemplateRef<any>) { }\n}\n"], "names": ["BidiModule", "CommonModule", "NgModule", "Input", "Directive", "Output", "END", "HOME", "SPACE", "ENTER", "LEFT_ARROW", "RIGHT_ARROW", "ChangeDetectionStrategy", "ViewEncapsulation", "Component", "coerceBooleanProperty", "TemplateRef"], "mappings": ";;;;;;;;;;;;;;;;;;AGQA,IAAA,YAAA,kBAAA,YAAA;IAME,SAAF,YAAA,CAA0C,QAA1C,EAAA;QAA0C,IAA1C,CAAA,QAAkD,GAAR,QAAQ,CAAlD;KAAyE;;QAJzE,EAAA,IAAA,EAACI,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,gBAAgB;iBAC3B,EAAD,EAAA;;;;QAJA,EAAA,IAAA,EAAmBY,yBAAW,GAA9B;;IARA,OAAA,YAAA,CAAA;CAaA,EAAA,CAAA,CAAA;;;;;;;;;;ADiCA,IAAI,MAAM,GAAG,CAAC,CAAC;;;;AAYf,IAAA,qBAAA,kBAAA,YAAA;;;IA1DA,OAAA,qBAAA,CAAA;CAsEA,EAAA,CAAC,CAAA;;IAyDC,SAAF,OAAA,CAA4D,QAA5D,EAAA;QAA4D,IAA5D,CAAA,QAAoE,GAAR,QAAQ,CAApE;;;;QAnCA,IAAA,CAAA,UAAA,GAAe,KAAK,CAApB;QAWA,IAAA,CAAA,SAAA,GAAsB,IAAI,CAA1B;QAQA,IAAA,CAAA,SAAA,GAAsB,KAAK,CAA3B;QAUA,IAAA,CAAA,gBAAA,GAA6C,IAAI,CAAjD;KAMqF;IA5BrF,MAAA,CAAA,cAAA,CAAM,OAAN,CAAA,SAAA,EAAA,UAAc,EAAd;;;;;QAAA,YAAA,EAA4B,OAAO,IAAI,CAAC,SAAS,CAAC,EAAlD;;;;;QACE,UAAa,KAAc,EAA7B;YACI,IAAI,CAAC,SAAS,GAAGD,2CAAqB,CAAC,KAAK,CAAC,CAAC;SAC/C;;;;IAKH,MAAA,CAAA,cAAA,CAAM,OAAN,CAAA,SAAA,EAAA,UAAc,EAAd;;;;;QAAA,YAAA,EAA4B,OAAO,IAAI,CAAC,SAAS,CAAC,EAAlD;;;;;QACE,UAAa,KAAc,EAA7B;YACI,IAAI,CAAC,SAAS,GAAGA,2CAAqB,CAAC,KAAK,CAAC,CAAC;SAC/C;;;;IAKH,MAAA,CAAA,cAAA,CAAM,OAAN,CAAA,SAAA,EAAA,WAAe,EAAf;;;;;;YACI,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC;;;;;;QAExF,UAAc,KAAc,EAA9B;YACI,IAAI,CAAC,gBAAgB,GAAGA,2CAAqB,CAAC,KAAK,CAAC,CAAC;SACtD;;;;IAGH,MAAA,CAAA,cAAA,CAAc,OAAd,CAAA,SAAA,EAAA,mBAA+B,EAA/B;;;;;YACI,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;;;;;;;;;;IAMxF,OAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;KAC/B,CAAH;;;;;;IAGE,OAAF,CAAA,SAAA,CAAA,KAAO;;;;IAAL,YAAF;QACI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;YACjC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;SAC/B;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;SAC1B;KACF,CAAH;;;;IAEE,OAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;;;QAGI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;KAC/B,CAAH;;QA/EA,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW,CAAX,QAAA,EAAA,UAAA;oBACE,QAAQ,EAAE,SAAS;oBACnB,QAAQ,EAAE,sDAAZ;oBACE,aAAF,EAAAD,+BAAA,CAAA,IAAA;oBACE,mBAAF,EAAA,KAAA;oBACE,eAAe,EAAjBD,qCAAA,CAAA,MAAA;iBACA,EAAA,EAAA;KACA,CAAA;;;;;IA+EA,OAAA,CAAA,cAAuB,GAAvB;;;QA3EA,aAAA,EAAA,CAAA,EAAA,IAAG,EAAHT,mBAAA,EAAA,EAAA;QAGA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;QAGA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;QAMA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;QAGA,WAAA,EAAA,CAAA,EAAA,IAAA,EAAAA,mBAAA,EAAA,EAAA;KAQA,CAAA;IAQA,OAAA,OAAA,CAAA;;AAlHA,IAAA,UAAA,kBAAA,YAAA;;;QAyNA,IAAA,CAAA,kBAAA,GAAA,kBAAA,CAAA;;;;;;QAzDA,IAAA,CAAA,cAAA,GAA6B,CAA7B,CAAA;;;;;;;;;;QAkDA,IAAA,CAAA,QAAA,GAAA,MAAA,EAAA,CAAA;KAKA;IAKA,MAAA,CAAA,cAAA,CAAA,UAAA,CAAA,SAAA,EAAA,QAAA,EAAA;QACA,GAAA;;;;;;;;;;;QAlDE,YAAF,EAAA,IAAA;;;;;;;;;;;;;;;gBAOQ,IAAI,KAAZ,GAAoB,CAAC,IAArB,KAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;;iBAEA;gBACA,IAAQ,IAAR,CAAA,4BAAA,CAAA,KAAA,CAAA,IAAA,KAAA,GAAA,IAAA,CAAA,cAAA;oBACA,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,EAAA,CAAA,KAAA,CAAA,CAAA,QAAA,EAAA;;oBAGU,IAAV,CAAe,WAAf,CAAA,OAAA,EAAA,CAAA,KAAA,CAAA,CAAA,aAAA,CAAA,IAAA,EAAA,CAAA;;qBAEA,IAAa,IAAb,CAAA,cAAgC,IAAhC,KAAyC,EAAzC;oBACA,IAAA,CAAA,0BAAA,CAAA,KAAA,CAAA,CAAA;oBAAY,IAAI,CAAC,WAAjB,GAAA,IAAA,CAAoC,cAApC,CAAA;iBACA;aACA;iBACO;gBACP,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,WAAA,GAAA,KAAA,CAAA;aAAA;SACA;QACA,UAAA,EAAA,IAAA;QACA,YAAA,EAAA,IAAA;;;;;;;;;;;;;;SAMA;QACA,UAAA,EAAA,IAAA;QACA,YAAA,EAAA,IAAA;;;;;;;;;;QAoBA,IAAA,CAAA,UAAA,CAAA,QAAA,EAAA,CAAA;KACA,CAAA;;;;;;;;;;;;KAKA,CAAA;;;;;;;;;;;;KAKA,CAAA;;;;;;;;;;;;QAKA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,EAAA,OAAA,IAAA,CAAA,KAAA,EAAA,CAAA,EAAA,CAAA,CAAA;QACI,IAAI,CAAC,aAAa,EAAtB,CAAyB;KACzB,CAAA;;;;;;;;;;;;;;KAKA,CAAA;;;;;;;;;;;;;;KAKA,CAAA;;;;;;;;;;;;KAKA,CAAA;;;;;;;;;;;;;;QAKA,IAAA,QAAA,GAAA,CAAA,EAAA;YACA,OAAA,IAAA,CAAA,gBAAqB,EAArB,KAAA,KAAiC,GAAjC,MAAA,GAAA,UAAA,CAAA;SACA;aACA,IAAA,QAAA,GAAA,CAAA,EAAA;YACA,OAAA,IAAA,CAAA,gBAAA,EAAA,KAAA,KAAA,GAAA,UAAA,GAAA,MAAA,CAAA;SAAA;QACA,OAAA,SAAkB,CAAlB;KACA,CAAA;;;;;;;;;;;;;;QAKA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,cAAA,IAAA,KAAA,EAAA;YACA,OAAA,QAAA,CAAA;SACA;aACA;YACA,OAAA,IAAA,CAAA,QAAA,GAAA,MAAA,GAAA,MAAA,CAAA;SAAA;KACA,CAAA;;;;;;;;;;;QAIA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA;YACA,aAAA,EAAA,QAAA;YACQ,uBAAR,EAAA,IAAA,CAAA,cAAA;YACM,YAAN,EAAA,UAAA,CAAA,QAAA,CAAA;YACM,sBAAN,EAAA,UAAA,CAAA,IAAA,CAAA,cAAA,CAAA;SACA,CAAA,CAAA;QACA,IAAM,CAAN,cAAA,GAAA,QAAA,CAAA;QACA,IAAA,CAAA,aAAA,EAAA,CAAA;KACA,CAAA;;;;;;;;;;;;QAKI,IAAJ,OAAA,KAAAQ,iCAAA,EAAA;;YAGQ,KAAR,CAAA,cAAA,EAAA,CAA+B;SAC/B;QACA,IAAM,OAAN,KAAAD,gCAA4B,EAA5B;YACA,IAAA,CAAA,gBAAA,EAAA,KAAA,KAAA,GAAA,IAAA,CAAA,cAAA,EAAA,GAAA,IAAA,CAAA,kBAAA,EAAA,CAAA;YAEQ,KAAR,CAAA,cAAA,EAA8B,CAAC;SAC/B;;;;;YAMQ,KAAK,CAAb,cAAA,EAA8B,CAA9B;SACA;QACA,IAAM,OAAN,KAAAF,2BAAA,IAAA,OAAA,KAAAC,2BAAA,EAAA;YACA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,WAAA,CAAA;YAEQ,KAAR,CAAA,cAAA,EAAA,CAAA;SACA;QACA,IAAM,OAAN,KAAAF,0BAAA,EAAA;YACA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;YAEQ,KAAR,CAAA,cAAA,EAAA,CAAA;SACA;QACA,IAAM,OAAN,KAAAD,yBAAA,EAAA;YACA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;YAEQ,KAAR,CAAA,cAAA,EAAA,CAAA;SACA;KACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAYA,IAAA,CAAA,WAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,aAAA,CAAA,KAAA,EAAA,CAAA;KACA,CAAA;;;;;;;;;;;QAIA,KAAA,CAAuC,IAAvC,CAAoD,cAApD,CAAA,CAAA,UAAA,GAAA,IAAA,CAAA;QACI,IAAJ,IAAA,CAAA,OAAA,IAAA,KAAA,IAAkB,CAAlB,EAAA;YAEA,OAAe,KAAf,CAAA,KAAA,CAAA,CAAA,EAA8B,KAA9B,CAAA,CAAA,IAAyC,CAAzC,UAAA,IAAA,EAAA;gBAEY,qBAAqB,OAAjC,GAAA,IAAA,CAAA,WAAA,CAAA;gBACA,qBAAA,YAAA,GAAA,OAAA,IAAA,OAAA,CAAA,OAAA,IAAA,OAAA,CAAA,OAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA;gBACQ,OAAR,YAAA,IAAA,CAAA,IAAqB,CAArB,QAAA,CAAA;aACA,CAAA,CAAA;SACA;QACA,OAAS,KAAT,CAAA;KACA,CAAA;;;;;;;;;;IAMA,UAAU,CAAC,UAAX,GAAwB;;;oBA9MxB,QAAA,EAAA,YAAA;iBACA,EAAA,EAAA;KACA,CAAA;;;;;KAlHA,CAAA,EAAA,CAAA;IAjBA,UAAA,CAAA,cAAA,GAAA;;;QA0IA,eAAA,EAAA,CAAA,EAAA,IAAA,EAAAH,mBAAA,EAAA,EAAA;QAMA,UAAA,EAAA,CAAA,EAAA,IAAG,EAAHA,mBAAA,EAAA,EAAA;QAMA,iBAAA,EAAA,CAAA,EAAA,IAAG,EAAHE,oBAAA,EAAA,EAAA;KAwBA,CAAA;IAOA,OAAA,UAAA,CAAA;CA9MA,EAAA,CAAA,CAAA;;;;;;;;;;;IDuBE,SAAF,cAAA,CAAqB,QAAoB,EAAzC;QAAqB,IAArB,CAAA,QAA6B,GAAR,QAAQ,CAAY;;;;QAFzC,IAAA,CAAA,IAAA,GAA0B,QAAQ,CAAlC;KAE6C;;QAX7C,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,wBAAwB;oBAClC,IAAI,EAAE;wBACJ,SAAS,EAAE,iBAAiB;wBAC5B,QAAQ,EAAE,MAAM;qBACjB;iBACF,EAAD,EAAA;;;;QATA,EAAA,IAAA,EAAQ,UAAU,GAAlB;;;QAYA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,mBAAK,EAAR,EAAA;;IArBA,OAAA,cAAA,CAAA;;;;;;IAsCE,SAAF,kBAAA,CAAqB,QAAoB,EAAzC;QAAqB,IAArB,CAAA,QAA6B,GAAR,QAAQ,CAAY;;;;QAFzC,IAAA,CAAA,IAAA,GAA0B,QAAQ,CAAlC;KAE6C;;QAX7C,EAAA,IAAA,EAACC,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,4BAA4B;oBACtC,IAAI,EAAE;wBACJ,SAAS,EAAE,qBAAqB;wBAChC,QAAQ,EAAE,MAAM;qBACjB;iBACF,EAAD,EAAA;;;;QAxBA,EAAA,IAAA,EAAQ,UAAU,GAAlB;;;QA2BA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,mBAAK,EAAR,EAAA;;IApCA,OAAA,kBAAA,CAAA;CAkCA,EAAA,CAAA,CAAA;;;;;;;AD1BA,IAAA,gBAAA,kBAAA,YAAA;;;;QAOA,EAAA,IAAA,EAACD,sBAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAACF,4BAAU,EAAEC,4BAAY,CAAC;oBACnC,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,kBAAkB,CAAC;oBAChF,YAAY,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,kBAAkB,CAAC;iBACtF,EAAD,EAAA;;;;IAnBA,OAAA,gBAAA,CAAA;CAoBA,EAAA,CAAA,CAAA;;;;;;;;;;;;"}