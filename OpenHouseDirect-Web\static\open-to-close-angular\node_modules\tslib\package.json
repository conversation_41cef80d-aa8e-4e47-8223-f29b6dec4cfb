{"_from": "tslib@^1.7.1", "_id": "tslib@1.14.1", "_inBundle": false, "_integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "_location": "/tslib", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tslib@^1.7.1", "name": "tslib", "escapedName": "tslib", "rawSpec": "^1.7.1", "saveSpec": null, "fetchSpec": "^1.7.1"}, "_requiredBy": ["/@angular/animations", "/@angular/cdk", "/@angular/common", "/@angular/compiler", "/@angular/core", "/@angular/forms", "/@angular/http", "/@angular/material", "/@angular/platform-browser", "/@angular/platform-browser-dynamic", "/@angular/router", "/@angular/service-worker", "/@ckeditor/ckeditor5-angular", "/@ng-select/ng-select", "/ng2-material-dropdown", "/ng2-meta", "/ngx-chips", "/tslint", "/tsutils"], "_resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "_shasum": "cf2d38bdc34a134bcaf1091c41f6619e2f672d00", "_spec": "tslib@^1.7.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\@angular\\animations", "author": {"name": "Microsoft Corp."}, "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Runtime library for TypeScript helper functions", "exports": {".": {"module": "./tslib.es6.js", "import": "./modules/index.js", "default": "./tslib.js"}, "./": "./"}, "homepage": "https://www.typescriptlang.org/", "jsnext:main": "tslib.es6.js", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "license": "0BSD", "main": "tslib.js", "module": "tslib.es6.js", "name": "tslib", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/tslib.git"}, "sideEffects": false, "typings": "tslib.d.ts", "version": "1.14.1"}