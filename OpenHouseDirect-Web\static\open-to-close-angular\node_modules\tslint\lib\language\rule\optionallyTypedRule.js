"use strict";
/**
 * @license
 * Copyright 2017 Palantir Technologies, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
var tslib_1 = require("tslib");
var abstractRule_1 = require("./abstractRule");
var OptionallyTypedRule = /** @class */ (function (_super) {
    tslib_1.__extends(OptionallyTypedRule, _super);
    function OptionallyTypedRule() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return OptionallyTypedRule;
}(abstractRule_1.AbstractRule));
exports.OptionallyTypedRule = OptionallyTypedRule;
