{"_from": "uuid@^3.0.0", "_id": "uuid@3.4.0", "_inBundle": false, "_integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==", "_location": "/uuid", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "uuid@^3.0.0", "name": "uuid", "escapedName": "uuid", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/node-gyp/request", "/node-sass/request", "/protractor/request", "/request", "/sockjs"], "_resolved": "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz", "_shasum": "b23e4358afa8a202fe7a100af1f5f883f02007ee", "_spec": "uuid@^3.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\request", "bin": {"uuid": "./bin/uuid"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bundleDependencies": false, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "description": "RFC4122 (v1, v4, and v5) UUIDs", "devDependencies": {"@commitlint/cli": "~8.2.0", "@commitlint/config-conventional": "~8.2.0", "eslint": "~6.4.0", "husky": "~3.0.5", "mocha": "6.2.0", "runmd": "1.2.1", "standard-version": "7.0.0"}, "homepage": "https://github.com/uuidjs/uuid#readme", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "name": "uuid", "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "scripts": {"lint": "eslint .", "md": "runmd --watch --output=README.md README_js.md", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version", "test": "npm run lint && mocha test/test.js"}, "version": "3.4.0"}