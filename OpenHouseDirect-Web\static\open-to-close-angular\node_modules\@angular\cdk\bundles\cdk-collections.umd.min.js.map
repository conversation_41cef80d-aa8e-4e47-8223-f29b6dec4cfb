{"version": 3, "file": "cdk-collections.umd.min.js", "sources": ["../../src/cdk/collections/selection.ts", "../../src/cdk/collections/unique-selection-dispatcher.ts", "../../src/cdk/collections/data-source.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Subject} from 'rxjs/Subject';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nexport class SelectionModel<T> {\n  /** Currently-selected values. */\n  private _selection: Set<T> = new Set();\n\n  /** Keeps track of the deselected options that haven't been emitted by the change event. */\n  private _deselectedToEmit: T[] = [];\n\n  /** Keeps track of the selected options that haven't been emitted by the change event. */\n  private _selectedToEmit: T[] = [];\n\n  /** Cache for the array value of the selected items. */\n  private _selected: T[] | null;\n\n  /** Selected values. */\n  get selected(): T[] {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n\n    return this._selected;\n  }\n\n  /** Event emitted when the value has changed. */\n  onChange: Subject<SelectionChange<T>> | null = this._emitChanges ? new Subject() : null;\n\n  constructor(\n    private _multiple = false,\n    initiallySelectedValues?: T[],\n    private _emitChanges = true) {\n\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n\n  /**\n   * Selects a value or an array of values.\n   */\n  select(...values: T[]): void {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    this._emitChangeEvent();\n  }\n\n  /**\n   * Deselects a value or an array of values.\n   */\n  deselect(...values: T[]): void {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    this._emitChangeEvent();\n  }\n\n  /**\n   * Toggles a value between selected and deselected.\n   */\n  toggle(value: T): void {\n    this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n\n  /**\n   * Clears all of the selected values.\n   */\n  clear(): void {\n    this._unmarkAll();\n    this._emitChangeEvent();\n  }\n\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value: T): boolean {\n    return this._selection.has(value);\n  }\n\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty(): boolean {\n    return this._selection.size === 0;\n  }\n\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue(): boolean {\n    return !this.isEmpty();\n  }\n\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate?: (a: T, b: T) => number): void {\n    if (this._multiple && this._selected) {\n      this._selected.sort(predicate);\n    }\n  }\n\n  /** Emits a change event and clears the records of selected and deselected values. */\n  private _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      const eventData = new SelectionChange<T>(this, this._selectedToEmit, this._deselectedToEmit);\n\n      if (this.onChange) {\n        this.onChange.next(eventData);\n      }\n\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n\n  /** Selects a value. */\n  private _markSelected(value: T) {\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n\n      this._selection.add(value);\n\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Deselects a value. */\n  private _unmarkSelected(value: T) {\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Clears out the selected values. */\n  private _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  private _verifyValueAssignment(values: T[]) {\n    if (values.length > 1 && !this._multiple) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n}\n\n/**\n * Event emitted when the value of a MatSelectionModel has changed.\n * @docs-private\n */\nexport class SelectionChange<T> {\n  constructor(\n    /** Model that dispatched the event. */\n    public source: SelectionModel<T>,\n    /** Options that were added to the model. */\n    public added?: T[],\n    /** Options that were removed from the model. */\n    public removed?: T[]) {}\n}\n\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n */\nexport function getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Optional, SkipSelf, OnDestroy} from '@angular/core';\n\n\n// Users of the Dispatcher never need to see this type, but TypeScript requires it to be exported.\nexport type UniqueSelectionDispatcherListener = (id: string, name: string) => void;\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\n@Injectable()\nexport class UniqueSelectionDispatcher implements OnDestroy {\n  private _listeners: UniqueSelectionDispatcherListener[] = [];\n\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id: string, name: string) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener: UniqueSelectionDispatcherListener): () => void {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter((registered: UniqueSelectionDispatcherListener) => {\n        return listener !== registered;\n      });\n    };\n  }\n\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n}\n\n/** @docs-private */\nexport function UNIQUE_SELECTION_DISPATCHER_PROVIDER_FACTORY(\n    parentDispatcher: UniqueSelectionDispatcher) {\n  return parentDispatcher || new UniqueSelectionDispatcher();\n}\n\n/** @docs-private */\nexport const UNIQUE_SELECTION_DISPATCHER_PROVIDER = {\n  // If there is already a dispatcher available, use that. Otherwise, provide a new one.\n  provide: UniqueSelectionDispatcher,\n  deps: [[new Optional(), new SkipSelf(), UniqueSelectionDispatcher]],\n  useFactory: UNIQUE_SELECTION_DISPATCHER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Observable} from 'rxjs/Observable';\nimport {CollectionViewer} from './collection-viewer';\n\nexport abstract class DataSource<T> {\n  /**\n   * Connects a collection viewer (such as a data-table) to this data source. Note that\n   * the stream provided will be accessed during change detection and should not directly change\n   * values that are bound in template views.\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   * @returns Observable that emits a new value when the data changes.\n   */\n  abstract connect(collectionViewer: CollectionViewer): Observable<T[]>;\n\n  /**\n   * Disconnects a collection viewer (such as a data-table) from this data source. Can be used\n   * to perform any clean-up or tear-down operations when a view is being destroyed.\n   *\n   * @param collectionViewer The component that exposes a view over the data provided by this\n   *     data source.\n   */\n  abstract disconnect(collectionViewer: CollectionViewer): void;\n}\n"], "names": ["getMultipleValuesInSingleSelectionError", "Error", "UNIQUE_SELECTION_DISPATCHER_PROVIDER_FACTORY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UniqueSelectionDispatcher", "DataSource", "SelectionModel", "_multiple", "initiallySelectedValues", "_emitChanges", "_this", "this", "_selection", "Set", "_deselectedToEmit", "_selectedToEmit", "onChange", "Subject", "length", "for<PERSON>ach", "value", "_markSelected", "Object", "defineProperty", "prototype", "_selected", "Array", "from", "values", "select", "_i", "arguments", "_verifyValueAssignment", "_emitChangeEvent", "deselect", "_unmarkSelected", "toggle", "isSelected", "clear", "_unmarkAll", "has", "isEmpty", "size", "hasValue", "sort", "predicate", "eventData", "SelectionChange", "next", "add", "push", "delete", "source", "added", "removed", "_listeners", "notify", "id", "name", "_a", "listener", "listen", "filter", "registered", "ngOnDestroy", "type", "Injectable", "UNIQUE_SELECTION_DISPATCHER_PROVIDER", "provide", "deps", "Optional", "SkipSelf", "useFactory"], "mappings": ";;;;;;;mWAqMA,SAAAA,KACE,MAAOC,OAAM,2EC7If,QAAAC,GACIC,GACF,MAAOA,IAAoB,GAAIC,GChDjC,GAAAC,GAAA,yBAXA,MAAAA,MFaAC,EAAA,WAyBE,QAAFA,GACYC,EACRC,EACQC,wCAHV,IAAFC,GAAAC,IACYA,MAAZJ,UAAYA,EAEAI,KAAZF,aAAYA,EA1BZE,KAAAC,WAA+B,GAAIC,KAGnCF,KAAAG,qBAGAH,KAAAI,mBAeAJ,KAAAK,SAAiDL,KAAKF,aAAe,GAAIQ,GAAAA,QAAY,KAO7ET,GAA2BA,EAAwBU,SACjDX,EACFC,EAAwBW,QAAQ,SAAAC,GAAS,MAAAV,GAAKW,cAAcD,KAE5DT,KAAKU,cAAcb,EAAwB,IAI7CG,KAAKI,gBAAgBG,OAAS,GAnDpC,MA2BEI,QAAFC,eAAMjB,EAANkB,UAAA,gBAAE,WAKE,MAJKb,MAAKc,YACRd,KAAKc,UAAYC,MAAMC,KAAKhB,KAAKC,WAAWgB,WAGvCjB,KAAKc,2CA0BdnB,EAAFkB,UAAAK,OAAE,WAAF,IAAS,GAATnB,GAAAC,KAAAiB,KAAAE,EAAA,EAASA,EAATC,UAAAb,OAASY,IAAAF,EAATE,GAAAC,UAAAD,EACInB,MAAKqB,uBAAuBJ,GAC5BA,EAAOT,QAAQ,SAAAC,GAAS,MAAAV,GAAKW,cAAcD,KAC3CT,KAAKsB,oBAMP3B,EAAFkB,UAAAU,SAAE,WAAF,IAAW,GAAXxB,GAAAC,KAAAiB,KAAAE,EAAA,EAAWA,EAAXC,UAAAb,OAAWY,IAAAF,EAAXE,GAAAC,UAAAD,EACInB,MAAKqB,uBAAuBJ,GAC5BA,EAAOT,QAAQ,SAAAC,GAAS,MAAAV,GAAKyB,gBAAgBf,KAC7CT,KAAKsB,oBAMP3B,EAAFkB,UAAAY,OAAE,SAAOhB,GACLT,KAAK0B,WAAWjB,GAAST,KAAKuB,SAASd,GAAST,KAAKkB,OAAOT,IAM9Dd,EAAFkB,UAAAc,MAAE,WACE3B,KAAK4B,aACL5B,KAAKsB,oBAMP3B,EAAFkB,UAAAa,WAAE,SAAWjB,GACT,MAAOT,MAAKC,WAAW4B,IAAIpB,IAM7Bd,EAAFkB,UAAAiB,QAAE,WACE,MAAgC,KAAzB9B,KAAKC,WAAW8B,MAMzBpC,EAAFkB,UAAAmB,SAAE,WACE,OAAQhC,KAAK8B,WAMfnC,EAAFkB,UAAAoB,KAAE,SAAKC,GACClC,KAAKJ,WAAaI,KAAKc,WACzBd,KAAKc,UAAUmB,KAAKC,IAKhBvC,EAAVkB,UAAAS,4BAII,GAFAtB,KAAKc,UAAY,KAEbd,KAAKI,gBAAgBG,QAAUP,KAAKG,kBAAkBI,OAAQ,CAChE,GAAM4B,GAAY,GAAIC,GAAmBpC,KAAMA,KAAKI,gBAAiBJ,KAAKG,kBAEtEH,MAAKK,UACPL,KAAKK,SAASgC,KAAKF,GAGrBnC,KAAKG,qBACLH,KAAKI,qBAKDT,EAAVkB,UAAAH,cAAA,SAAwBD,GACfT,KAAK0B,WAAWjB,KACdT,KAAKJ,WACRI,KAAK4B,aAGP5B,KAAKC,WAAWqC,IAAI7B,GAEhBT,KAAKF,cACPE,KAAKI,gBAAgBmC,KAAK9B,KAMxBd,EAAVkB,UAAAW,gBAAA,SAA0Bf,GAClBT,KAAK0B,WAAWjB,KAClBT,KAAKC,WAAWuC,OAAO/B,GAEnBT,KAAKF,cACPE,KAAKG,kBAAkBoC,KAAK9B,KAM1Bd,EAAVkB,UAAAe,gCACS5B,MAAK8B,WACR9B,KAAKC,WAAWO,QAAQ,SAAAC,GAAS,MAAAV,GAAKyB,gBAAgBf,MAQlDd,EAAVkB,UAAAQ,uBAAA,SAAiCJ,GAC7B,GAAIA,EAAOV,OAAS,IAAMP,KAAKJ,UAC7B,KAAMP,MA9KZM,KAuLAyC,EAAA,WACE,QAAFA,GAEWK,EAEAC,EAEAC,GAJA3C,KAAXyC,OAAWA,EAEAzC,KAAX0C,MAAWA,EAEA1C,KAAX2C,QAAWA,EA9LX,MAAAP,gCCyBApC,KAAA4C,cAzBA,MAgCEnD,GAAFoB,UAAAgC,OAAE,SAAOC,EAAYC,GACjB,IAAqB,GAAzB5B,GAAA,EAAyB6B,EAAAhD,KAAK4C,WAALzB,EAAzB6B,EAAAzC,OAAyBY,IAAzB,EACM8B,EADND,EAAA7B,IACe2B,EAAIC,KAQjBtD,EAAFoB,UAAAqC,OAAE,SAAOD,GAAP,GAAFlD,GAAAC,IAEI,OADAA,MAAK4C,WAAWL,KAAKU,GACd,WACLlD,EAAK6C,WAAa7C,EAAK6C,WAAWO,OAAO,SAACC,GACxC,MAAOH,KAAaG,MAK1B3D,EAAFoB,UAAAwC,YAAE,WACErD,KAAK4C,8BA7BTU,KAACC,EAAAA,mDAvBD9D,KA+Da+D,GAEXC,QAAShE,EACTiE,OAAQ,GAAIC,GAAAA,SAAY,GAAIC,GAAAA,SAAYnE,IACxCoE,WAAYtE"}