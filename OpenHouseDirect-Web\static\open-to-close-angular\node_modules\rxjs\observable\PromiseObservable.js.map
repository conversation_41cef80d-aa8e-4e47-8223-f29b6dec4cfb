{"version": 3, "file": "PromiseObservable.js", "sourceRoot": "", "sources": ["../../src/observable/PromiseObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,qBAAqB,cAAc,CAAC,CAAA;AAEpC,2BAA2B,eAAe,CAAC,CAAA;AAI3C;;;;GAIG;AACH;IAA0C,qCAAa;IAkCrD,2BAAoB,OAAuB,EAAU,SAAsB;QACzE,iBAAO,CAAC;QADU,YAAO,GAAP,OAAO,CAAgB;QAAU,cAAS,GAAT,SAAS,CAAa;IAE3E,CAAC;IAhCD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACI,wBAAM,GAAb,UAAiB,OAAuB,EAAE,SAAsB;QAC9D,MAAM,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAMD,oCAAoC,CAAC,sCAAU,GAAV,UAAW,UAAyB;QAApC,iBAwDpC;QAvDC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;YACtB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACnB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACvB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC5B,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,CAAC;YACH,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,OAAO,CAAC,IAAI,CACV,UAAC,KAAK;oBACJ,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,KAAI,CAAC,SAAS,GAAG,IAAI,CAAC;oBACtB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACvB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACxB,CAAC;gBACH,CAAC,EACD,UAAC,GAAG;oBACF,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC,CACF;qBACA,IAAI,CAAC,IAAI,EAAE,UAAA,GAAG;oBACb,kDAAkD;oBAClD,WAAI,CAAC,UAAU,CAAC,cAAQ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACnB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACvB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,sBAAU,EAAE,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,OAAO,CAAC,IAAI,CACV,UAAC,KAAK;oBACJ,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,KAAI,CAAC,SAAS,GAAG,IAAI,CAAC;oBACtB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,YAAK,EAAE,sBAAU,EAAE,CAAC,CAAC,CAAC;oBAC7E,CAAC;gBACH,CAAC,EACD,UAAC,GAAG;oBACF,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;wBACvB,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,QAAG,EAAE,sBAAU,EAAE,CAAC,CAAC,CAAC;oBAC5E,CAAC;gBACH,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,EAAE,UAAC,GAAG;oBACd,kDAAkD;oBAClD,WAAI,CAAC,UAAU,CAAC,cAAQ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;IACH,CAAC;IACH,wBAAC;AAAD,CAAC,AA/FD,CAA0C,uBAAU,GA+FnD;AA/FY,yBAAiB,oBA+F7B,CAAA;AAMD,sBAAyB,GAAuB;IACtC,qBAAK,EAAE,2BAAU,CAAS;IAClC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QACvB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,UAAU,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;AACH,CAAC;AAMD,uBAA0B,GAAwB;IACxC,iBAAG,EAAE,2BAAU,CAAS;IAChC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QACvB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;AACH,CAAC", "sourcesContent": ["import { root } from '../util/root';\nimport { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { TeardownLogic } from '../Subscription';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class PromiseObservable<T> extends Observable<T> {\n\n  public value: T;\n\n  /**\n   * Converts a Promise to an Observable.\n   *\n   * <span class=\"informal\">Returns an Observable that just emits the Promise's\n   * resolved value, then completes.</span>\n   *\n   * Converts an ES2015 Promise or a Promises/A+ spec compliant Promise to an\n   * Observable. If the Promise resolves with a value, the output Observable\n   * emits that resolved value as a `next`, and then completes. If the Promise\n   * is rejected, then the output Observable emits the corresponding Error.\n   *\n   * @example <caption>Convert the Promise returned by Fetch to an Observable</caption>\n   * var result = Rx.Observable.fromPromise(fetch('http://myserver.com/'));\n   * result.subscribe(x => console.log(x), e => console.error(e));\n   *\n   * @see {@link bindCallback}\n   * @see {@link from}\n   *\n   * @param {PromiseLike<T>} promise The promise to be converted.\n   * @param {Scheduler} [scheduler] An optional IScheduler to use for scheduling\n   * the delivery of the resolved value (or the rejection).\n   * @return {Observable<T>} An Observable which wraps the Promise.\n   * @static true\n   * @name fromPromise\n   * @owner Observable\n   */\n  static create<T>(promise: PromiseLike<T>, scheduler?: IScheduler): Observable<T> {\n    return new PromiseObservable(promise, scheduler);\n  }\n\n  constructor(private promise: PromiseLike<T>, private scheduler?: IScheduler) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n    const promise = this.promise;\n    const scheduler = this.scheduler;\n\n    if (scheduler == null) {\n      if (this._isScalar) {\n        if (!subscriber.closed) {\n          subscriber.next(this.value);\n          subscriber.complete();\n        }\n      } else {\n        promise.then(\n          (value) => {\n            this.value = value;\n            this._isScalar = true;\n            if (!subscriber.closed) {\n              subscriber.next(value);\n              subscriber.complete();\n            }\n          },\n          (err) => {\n            if (!subscriber.closed) {\n              subscriber.error(err);\n            }\n          }\n        )\n        .then(null, err => {\n          // escape the promise trap, throw unhandled errors\n          root.setTimeout(() => { throw err; });\n        });\n      }\n    } else {\n      if (this._isScalar) {\n        if (!subscriber.closed) {\n          return scheduler.schedule(dispatchNext, 0, { value: this.value, subscriber });\n        }\n      } else {\n        promise.then(\n          (value) => {\n            this.value = value;\n            this._isScalar = true;\n            if (!subscriber.closed) {\n              subscriber.add(scheduler.schedule(dispatchNext, 0, { value, subscriber }));\n            }\n          },\n          (err) => {\n            if (!subscriber.closed) {\n              subscriber.add(scheduler.schedule(dispatchError, 0, { err, subscriber }));\n            }\n          })\n          .then(null, (err) => {\n            // escape the promise trap, throw unhandled errors\n            root.setTimeout(() => { throw err; });\n          });\n      }\n    }\n  }\n}\n\ninterface DispatchNextArg<T> {\n  subscriber: Subscriber<T>;\n  value: T;\n}\nfunction dispatchNext<T>(arg: DispatchNextArg<T>) {\n  const { value, subscriber } = arg;\n  if (!subscriber.closed) {\n    subscriber.next(value);\n    subscriber.complete();\n  }\n}\n\ninterface DispatchErrorArg<T> {\n  subscriber: Subscriber<T>;\n  err: any;\n}\nfunction dispatchError<T>(arg: DispatchErrorArg<T>) {\n  const { err, subscriber } = arg;\n  if (!subscriber.closed) {\n    subscriber.error(err);\n  }\n}\n"]}