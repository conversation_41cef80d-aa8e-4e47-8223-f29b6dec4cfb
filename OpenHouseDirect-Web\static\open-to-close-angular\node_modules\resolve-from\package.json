{"_from": "resolve-from@^3.0.0", "_id": "resolve-from@3.0.0", "_inBundle": false, "_integrity": "sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw==", "_location": "/resolve-from", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resolve-from@^3.0.0", "name": "resolve-from", "escapedName": "resolve-from", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/import-fresh", "/import-from", "/resolve-cwd"], "_resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "_shasum": "b22c7af7d9d6881bc8b6e653335eebcb0a188748", "_spec": "resolve-from@^3.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\import-fresh", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Resolve the path of a module like `require.resolve()` but from a given path", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/resolve-from#readme", "keywords": ["require", "resolve", "path", "module", "from", "like", "import", "path"], "license": "MIT", "name": "resolve-from", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-from.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}