!function(t){"object"==typeof exports?module.exports=t():"function"==typeof define&&define.amd?define(t):"undefined"!=typeof window?window.when=t():"undefined"!=typeof global?global.when=t():"undefined"!=typeof self&&(self.when=t())}(function(){var t;return function n(t,e,r){function o(u,c){if(!e[u]){if(!t[u]){var f="function"==typeof require&&require;if(!c&&f)return f(u,!0);if(i)return i(u,!0);throw new Error("Cannot find module '"+u+"'")}var a=e[u]={exports:{}};t[u][0].call(a.exports,function(n){var e=t[u][1][n];return o(e?e:n)},a,a.exports,n,t,e,r)}return e[u].exports}for(var i="function"==typeof require&&require,u=0;u<r.length;u++)o(r[u]);return o}({1:[function(t,n,e){var r=n.exports=t("../when");r.callbacks=t("../callbacks"),r.cancelable=t("../cancelable"),r.delay=t("../delay"),r.fn=t("../function"),r.guard=t("../guard"),r.keys=t("../keys"),r.nodefn=r.node=t("../node"),r.parallel=t("../parallel"),r.pipeline=t("../pipeline"),r.poll=t("../poll"),r.sequence=t("../sequence"),r.timeout=t("../timeout")},{"../callbacks":2,"../cancelable":3,"../delay":4,"../function":5,"../guard":6,"../keys":7,"../node":26,"../parallel":27,"../pipeline":28,"../poll":29,"../sequence":30,"../timeout":31,"../when":32}],2:[function(n,e,r){!function(t){t(function(t){function n(t,n){return v(t,this,n||[])}function e(t,n,e,o){e.push(s(o.resolve,o),s(o.reject,o)),r(t,n,e,o)}function r(t,n,e,r){try{t.apply(n,e)}catch(o){r.reject(o)}}function o(t){return v(t,this,d.call(arguments,1))}function i(t){var n=arguments.length>1?d.call(arguments,1):[];return function(){return v(t,this,n.concat(d.call(arguments)))}}function u(t,n,e){return p(i,n,e,t)}function c(t,n){return function(){var e=this;return h.all(arguments).then(function(r){var o,i,u=h._defer();return"number"==typeof n.callback&&(o=f(r,n.callback)),"number"==typeof n.errback&&(i=f(r,n.errback)),o>i?(a(r,i,u._handler.reject,u._handler),a(r,o,u._handler.resolve,u._handler)):(a(r,o,u._handler.resolve,u._handler),a(r,i,u._handler.reject,u._handler)),t.apply(e,r),u})}}function f(t,n){return 0>n?t.length+n+2:n}function a(t,n,e,r){"number"==typeof n&&t.splice(n,0,s(e,r))}function s(t,n){return function(){arguments.length>1?t.call(n,d.call(arguments)):t.apply(n,arguments)}}var l=t("./when"),h=l.Promise,p=t("./lib/liftAll"),d=Array.prototype.slice,y=t("./lib/apply"),v=y(h,e);return{lift:i,liftAll:u,apply:n,call:o,promisify:c}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./lib/apply":11,"./lib/liftAll":23,"./when":32}],3:[function(n,e,r){!function(t){t(function(){return function(t,n){return t.cancel=function(){try{t.reject(n(t))}catch(e){t.reject(e)}return t.promise},t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],4:[function(n,e,r){!function(t){t(function(t){var n=t("./when");return function(t,e){return n(e).delay(t)}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./when":32}],5:[function(n,e,r){!function(t){t(function(t){function n(t,n){return f(t,this,null==n?[]:a.call(n))}function e(t){var n=arguments.length>1?a.call(arguments,1):[];return function(){return f(t,this,n.concat(a.call(arguments)))}}function r(t,n,r){return c(e,n,r,t)}function o(t){var n=a.call(arguments,1);return function(){var e=this,r=a.call(arguments),o=u.apply(e,[t].concat(r));return i.reduce(n,function(t,n){return n.call(e,t)},o)}}var i=t("./when"),u=i["try"],c=t("./lib/liftAll"),f=t("./lib/apply")(i.Promise),a=Array.prototype.slice;return{lift:e,liftAll:r,call:u,apply:n,compose:o}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./lib/apply":11,"./lib/liftAll":23,"./when":32}],6:[function(n,e,r){!function(t){t(function(t){function n(t,n){return function(){var e=o.call(arguments);return r(t()).withThis(this).then(function(t){return r(n.apply(this,e))["finally"](t)})}}function e(t){function n(){e=Math.max(e-1,0),o.length>0&&o.shift()(n)}var e=0,o=[];return function(){return r.promise(function(r){t>e?r(n):o.push(r),e+=1})}}var r=t("./when"),o=Array.prototype.slice;return n.n=e,n})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./when":32}],7:[function(n,e,r){!function(t){"use strict";t(function(t){function n(t){function n(t,n,e){this[t]=n,0===--f&&e.resolve(i)}for(var e,r=u._defer(),o=u._handler(r),i={},c=Object.keys(t),f=c.length,a=0;a<c.length;++a)e=c[a],u._handler(t[e]).fold(n,e,i,o);return 0===f&&o.resolve(i),r}function e(t,e){function r(t,n){return e(n,t)}return c(t).then(function(t){return n(Object.keys(t).reduce(function(n,e){return n[e]=c(t[e]).fold(r,e),n},{}))})}function r(t){var n=Object.keys(t),e={};if(0===n.length)return c(e);var r=u._defer(),f=u._handler(r),a=n.map(function(n){return t[n]});return i.settle(a).then(function(t){o(n,t,e,f)}),r}function o(t,n,e,r){for(var o=0;o<t.length;o++)e[t[o]]=n[o];r.resolve(e)}var i=t("./when"),u=i.Promise,c=i.resolve;return{all:i.lift(n),map:e,settle:r}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./when":32}],8:[function(n,e,r){!function(t){"use strict";t(function(t){var n=t("./makePromise"),e=t("./Scheduler"),r=t("./env").asap;return n({scheduler:new e(r)})})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./Scheduler":9,"./env":21,"./makePromise":24}],9:[function(n,e,r){!function(t){"use strict";t(function(){function t(t){this._async=t,this._running=!1,this._queue=this,this._queueLen=0,this._afterQueue={},this._afterQueueLen=0;var n=this;this.drain=function(){n._drain()}}return t.prototype.enqueue=function(t){this._queue[this._queueLen++]=t,this.run()},t.prototype.afterQueue=function(t){this._afterQueue[this._afterQueueLen++]=t,this.run()},t.prototype.run=function(){this._running||(this._running=!0,this._async(this.drain))},t.prototype._drain=function(){for(var t=0;t<this._queueLen;++t)this._queue[t].run(),this._queue[t]=void 0;for(this._queueLen=0,this._running=!1,t=0;t<this._afterQueueLen;++t)this._afterQueue[t].run(),this._afterQueue[t]=void 0;this._afterQueueLen=0},t})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],10:[function(n,e,r){!function(t){"use strict";t(function(){function t(n){Error.call(this),this.message=n,this.name=t.name,"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t)}return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],11:[function(n,e,r){!function(t){"use strict";t(function(){function t(t,e){function r(n,r,i){var u=t._defer(),c=i.length,f=new Array(c);return o({f:n,thisArg:r,args:i,params:f,i:c-1,call:e},u._handler),u}function o(n,r){if(n.i<0)return e(n.f,n.thisArg,n.params,r);var o=t._handler(n.args[n.i]);o.fold(i,n,void 0,r)}function i(t,n,e){t.params[t.i]=n,t.i-=1,o(t,e)}return arguments.length<2&&(e=n),r}function n(t,n,e,r){try{r.resolve(t.apply(n,e))}catch(o){r.reject(o)}}return t.tryCatchResolve=n,t})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],12:[function(n,e,r){!function(t){"use strict";t(function(t){var n=t("../state"),e=t("../apply");return function(t){function r(n){function e(t){s=null,this.resolve(t)}function r(t){this.resolved||(s.push(t),0===--a&&this.reject(s))}for(var o,i,u=t._defer(),c=u._handler,f=n.length>>>0,a=f,s=[],l=0;f>l;++l)if(i=n[l],void 0!==i||l in n){if(o=t._handler(i),o.state()>0){c.become(o),t._visitRemaining(n,l,o);break}o.visit(c,e,r)}else--a;return 0===a&&c.reject(new RangeError("any(): array must not be empty")),u}function o(n,e){function r(t){this.resolved||(s.push(t),0===--p&&(l=null,this.resolve(s)))}function o(t){this.resolved||(l.push(t),0===--i&&(s=null,this.reject(l)))}var i,u,c,f=t._defer(),a=f._handler,s=[],l=[],h=n.length>>>0,p=0;for(c=0;h>c;++c)u=n[c],(void 0!==u||c in n)&&++p;for(e=Math.max(e,0),i=p-e+1,p=Math.min(e,p),e>p?a.reject(new RangeError("some(): array must contain at least "+e+" item(s), but had "+p)):0===p&&a.resolve(s),c=0;h>c;++c)u=n[c],(void 0!==u||c in n)&&t._handler(u).visit(a,r,o,a.notify);return f}function i(n,e){return t._traverse(e,n)}function u(n,e){var r=b.call(n);return t._traverse(e,r).then(function(t){return c(r,t)})}function c(n,e){for(var r=e.length,o=new Array(r),i=0,u=0;r>i;++i)e[i]&&(o[u++]=t._handler(n[i]).value);return o.length=u,o}function f(t){return y(t.map(a))}function a(e){var r;return e instanceof t&&(r=e._handler.join()),r&&0===r.state()||!r?d(e).then(n.fulfilled,n.rejected):(r._unreport(),n.inspect(r))}function s(t,n){return arguments.length>2?v.call(t,h(n),arguments[2]):v.call(t,h(n))}function l(t,n){return arguments.length>2?m.call(t,h(n),arguments[2]):m.call(t,h(n))}function h(t){return function(n,e,r){return p(t,void 0,[n,e,r])}}var p=e(t),d=t.resolve,y=t.all,v=Array.prototype.reduce,m=Array.prototype.reduceRight,b=Array.prototype.slice;return t.any=r,t.some=o,t.settle=f,t.map=i,t.filter=u,t.reduce=s,t.reduceRight=l,t.prototype.spread=function(t){return this.then(y).then(function(n){return t.apply(this,n)})},t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"../apply":11,"../state":25}],13:[function(n,e,r){!function(t){"use strict";t(function(){function t(){throw new TypeError("catch predicate must be a function")}function n(t,n){return e(n)?t instanceof n:n(t)}function e(t){return t===Error||null!=t&&t.prototype instanceof Error}function r(t){return("object"==typeof t||"function"==typeof t)&&null!==t}function o(t){return t}return function(e){function i(t,e){return function(r){return n(r,e)?t.call(this,r):a(r)}}function u(t,n,e,o){var i=t.call(n);return r(i)?c(i,e,o):e(o)}function c(t,n,e){return f(t).then(function(){return n(e)})}var f=e.resolve,a=e.reject,s=e.prototype["catch"];return e.prototype.done=function(t,n){this._handler.visit(this._handler.receiver,t,n)},e.prototype["catch"]=e.prototype.otherwise=function(n){return arguments.length<2?s.call(this,n):"function"!=typeof n?this.ensure(t):s.call(this,i(arguments[1],n))},e.prototype["finally"]=e.prototype.ensure=function(t){return"function"!=typeof t?this:this.then(function(n){return u(t,this,o,n)},function(n){return u(t,this,a,n)})},e.prototype["else"]=e.prototype.orElse=function(t){return this.then(void 0,function(){return t})},e.prototype["yield"]=function(t){return this.then(function(){return t})},e.prototype.tap=function(t){return this.then(t)["yield"](this)},e}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],14:[function(n,e,r){!function(t){"use strict";t(function(){return function(t){return t.prototype.fold=function(n,e){var r=this._beget();return this._handler.fold(function(e,r,o){t._handler(e).fold(function(t,e,r){r.resolve(n.call(this,e,t))},r,this,o)},e,r._handler.receiver,r._handler),r},t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],15:[function(n,e,r){!function(t){"use strict";t(function(t){var n=t("../state").inspect;return function(t){return t.prototype.inspect=function(){return n(t._handler(this))},t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"../state":25}],16:[function(n,e,r){!function(t){"use strict";t(function(){return function(t){function n(t,n,r,o){return e(function(n){return[n,t(n)]},n,r,o)}function e(t,n,o,i){function u(i,u){return r(o(i)).then(function(){return e(t,n,o,u)})}return r(i).then(function(e){return r(n(e)).then(function(n){return n?e:r(t(e)).spread(u)})})}var r=t.resolve;return t.iterate=n,t.unfold=e,t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],17:[function(n,e,r){!function(t){"use strict";t(function(){return function(t){return t.prototype.progress=function(t){return this.then(void 0,void 0,t)},t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],18:[function(n,e,r){!function(t){"use strict";t(function(t){function n(t,n,r,o){return e.setTimer(function(){t(r,o,n)},n)}var e=t("../env"),r=t("../TimeoutError");return function(t){function o(t,e,r){n(i,t,e,r)}function i(t,n){n.resolve(t)}function u(t,n,e){var o="undefined"==typeof t?new r("timed out after "+e+"ms"):t;n.reject(o)}return t.prototype.delay=function(t){var n=this._beget();return this._handler.fold(o,t,void 0,n._handler),n},t.prototype.timeout=function(t,r){var o=this._beget(),i=o._handler,c=n(u,t,r,o._handler);return this._handler.visit(i,function(t){e.clearTimer(c),this.resolve(t)},function(t){e.clearTimer(c),this.reject(t)},i.notify),o},t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"../TimeoutError":10,"../env":21}],19:[function(n,e,r){!function(t){"use strict";t(function(t){function n(t){throw t}function e(){}var r=t("../env").setTimer,o=t("../format");return function(t){function i(t){t.handled||(p.push(t),s("Potentially unhandled rejection ["+t.id+"] "+o.formatError(t.value)))}function u(t){var n=p.indexOf(t);n>=0&&(p.splice(n,1),l("Handled previous rejection ["+t.id+"] "+o.formatObject(t.value)))}function c(t,n){h.push(t,n),null===d&&(d=r(f,0))}function f(){for(d=null;h.length>0;)h.shift()(h.shift())}var a,s=e,l=e;"undefined"!=typeof console&&(a=console,s="undefined"!=typeof a.error?function(t){a.error(t)}:function(t){a.log(t)},l="undefined"!=typeof a.info?function(t){a.info(t)}:function(t){a.log(t)}),t.onPotentiallyUnhandledRejection=function(t){c(i,t)},t.onPotentiallyUnhandledRejectionHandled=function(t){c(u,t)},t.onFatalRejection=function(t){c(n,t.value)};var h=[],p=[],d=null;return t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"../env":21,"../format":22}],20:[function(n,e,r){!function(t){"use strict";t(function(){return function(t){return t.prototype["with"]=t.prototype.withThis=function(t){var n=this._beget(),e=n._handler;return e.receiver=t,this._handler.chain(e,t),n},t}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],21:[function(n,e,r){!function(t){"use strict";t(function(t){function n(){return"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process)}function e(){return"undefined"!=typeof MutationObserver&&MutationObserver||"undefined"!=typeof WebKitMutationObserver&&WebKitMutationObserver}function r(t){function n(){var t=e;e=void 0,t()}var e,r=document.createTextNode(""),o=new t(n);o.observe(r,{characterData:!0});var i=0;return function(t){e=t,r.data=i^=1}}var o,i="undefined"!=typeof setTimeout&&setTimeout,u=function(t,n){return setTimeout(t,n)},c=function(t){return clearTimeout(t)},f=function(t){return i(t,0)};if(n())f=function(t){return process.nextTick(t)};else if(o=e())f=r(o);else if(!i){var a=t,s=a("vertx");u=function(t,n){return s.setTimer(n,t)},c=s.cancelTimer,f=s.runOnLoop||s.runOnContext}return{setTimer:u,clearTimer:c,asap:f}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{}],22:[function(n,e,r){!function(t){"use strict";t(function(){function t(t){var e="object"==typeof t&&null!==t&&(t.stack||t.message)?t.stack||t.message:n(t);return t instanceof Error?e:e+" (WARNING: non-Error used)"}function n(t){var n=String(t);return"[object Object]"===n&&"undefined"!=typeof JSON&&(n=e(t,n)),n}function e(t,n){try{return JSON.stringify(t)}catch(e){return n}}return{formatError:t,formatObject:n,tryStringify:e}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],23:[function(n,e,r){!function(t){"use strict";t(function(){function t(t,n,e){return t[e]=n,t}function n(t){return"function"==typeof t?t.bind():Object.create(t)}return function(e,r,o,i){return"undefined"==typeof r&&(r=t),Object.keys(i).reduce(function(t,n){var o=i[n];return"function"==typeof o?r(t,e(o),n):t},"undefined"==typeof o?n(i):o)}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],24:[function(n,e,r){!function(t){"use strict";t(function(){return function(t){function n(t,n){this._handler=t===g?n:e(t)}function e(t){function n(t){o.resolve(t)}function e(t){o.reject(t)}function r(t){o.notify(t)}var o=new w;try{t(n,e,r)}catch(i){e(i)}return o}function r(t){return L(t)?t:new n(g,new j(v(t)))}function o(t){return new n(g,new j(new k(t)))}function i(){return nt}function u(){return new n(g,new w)}function c(t,n){var e=new w(t.receiver,t.join().context);return new n(g,e)}function f(t){return s(K,null,t)}function a(t,n){return s(N,t,n)}function s(t,e,r){function o(n,o,u){u.resolved||l(r,i,n,t(e,o,n),u)}function i(t,n,e){s[t]=n,0===--a&&e.become(new E(s))}for(var u,c="function"==typeof e?o:i,f=new w,a=r.length>>>0,s=new Array(a),h=0;h<r.length&&!f.resolved;++h)u=r[h],void 0!==u||h in r?l(r,c,h,u,f):--a;return 0===a&&f.become(new E(s)),new n(g,f)}function l(t,n,e,r,o){if(S(r)){var i=m(r),u=i.state();0===u?i.fold(n,e,void 0,o):u>0?n(e,i.value,o):(o.become(i),h(t,e+1,i))}else n(e,r,o)}function h(t,n,e){for(var r=n;r<t.length;++r)p(v(t[r]),e)}function p(t,n){if(t!==n){var e=t.state();0===e?t.visit(t,void 0,t._unreport):0>e&&t._unreport()}}function d(t){return"object"!=typeof t||null===t?o(new TypeError("non-iterable passed to race()")):0===t.length?i():1===t.length?r(t[0]):y(t)}function y(t){var e,r,o,i=new w;for(e=0;e<t.length;++e)if(r=t[e],void 0!==r||e in t){if(o=v(r),0!==o.state()){i.become(o),h(t,e+1,o);break}o.visit(i,i.resolve,i.reject)}return new n(g,i)}function v(t){return L(t)?t._handler.join():S(t)?b(t):new E(t)}function m(t){return L(t)?t._handler.join():b(t)}function b(t){try{var n=t.then;return"function"==typeof n?new x(n,t):new E(t)}catch(e){return new k(e)}}function g(){}function _(){}function w(t,e){n.createContext(this,e),this.consumers=void 0,this.receiver=t,this.handler=void 0,this.resolved=!1}function j(t){this.handler=t}function x(t,n){w.call(this),V.enqueue(new O(t,n,this))}function E(t){n.createContext(this),this.value=t}function k(t){n.createContext(this),this.id=++$,this.value=t,this.handled=!1,this.reported=!1,this._report()}function A(t,n){this.rejection=t,this.context=n}function T(t){this.rejection=t}function C(){return new k(new TypeError("Promise cycle"))}function R(t,n){this.continuation=t,this.handler=n}function q(t,n){this.handler=n,this.value=t}function O(t,n,e){this._then=t,this.thenable=n,this.resolver=e}function P(t,n,e,r,o){try{t.call(n,e,r,o)}catch(i){r(i)}}function Q(t,n,e,r){this.f=t,this.z=n,this.c=e,this.to=r,this.resolver=Z,this.receiver=this}function L(t){return t instanceof n}function S(t){return("object"==typeof t||"function"==typeof t)&&null!==t}function M(t,e,r,o){return"function"!=typeof t?o.become(e):(n.enterContext(e),F(t,e.value,r,o),void n.exitContext())}function U(t,e,r,o,i){return"function"!=typeof t?i.become(r):(n.enterContext(r),W(t,e,r.value,o,i),void n.exitContext())}function H(t,e,r,o,i){return"function"!=typeof t?i.notify(e):(n.enterContext(r),z(t,e,o,i),void n.exitContext())}function N(t,n,e){try{return t(n,e)}catch(r){return o(r)}}function F(t,n,e,r){try{r.become(v(t.call(e,n)))}catch(o){r.become(new k(o))}}function W(t,n,e,r,o){try{t.call(r,n,e,o)}catch(i){o.become(new k(i))}}function z(t,n,e,r){try{r.notify(t.call(e,n))}catch(o){r.notify(o)}}function J(t,n){n.prototype=Y(t.prototype),n.prototype.constructor=n}function K(t,n){return n}function D(){}function G(){if("function"==typeof CustomEvent)try{var t=new CustomEvent("unhandledRejection");return t instanceof CustomEvent}catch(n){}return!1}function I(){if("undefined"!=typeof document&&"function"==typeof document.createEvent)try{var t=document.createEvent("CustomEvent");return t.initCustomEvent("eventType",!1,!0,{}),!0}catch(n){}return!1}function B(){return"undefined"!=typeof process&&null!==process&&"function"==typeof process.emit?function(t,n){return"unhandledRejection"===t?process.emit(t,n.value,n):process.emit(t,n)}:"undefined"!=typeof self&&G()?function(t,n){return function(e,r){var o=new n(e,{detail:{reason:r.value,key:r},bubbles:!1,cancelable:!0});return!t.dispatchEvent(o)}}(self,CustomEvent):"undefined"!=typeof self&&I()?function(t,n){return function(e,r){var o=n.createEvent("CustomEvent");return o.initCustomEvent(e,!1,!0,{reason:r.value,key:r}),!t.dispatchEvent(o)}}(self,document):D}var V=t.scheduler,X=B(),Y=Object.create||function(t){function n(){}return n.prototype=t,new n};n.resolve=r,n.reject=o,n.never=i,n._defer=u,n._handler=v,n.prototype.then=function(t,n,e){var r=this._handler,o=r.join().state();if("function"!=typeof t&&o>0||"function"!=typeof n&&0>o)return new this.constructor(g,r);var i=this._beget(),u=i._handler;return r.chain(u,r.receiver,t,n,e),i},n.prototype["catch"]=function(t){return this.then(void 0,t)},n.prototype._beget=function(){return c(this._handler,this.constructor)},n.all=f,n.race=d,n._traverse=a,n._visitRemaining=h,g.prototype.when=g.prototype.become=g.prototype.notify=g.prototype.fail=g.prototype._unreport=g.prototype._report=D,g.prototype._state=0,g.prototype.state=function(){return this._state},g.prototype.join=function(){for(var t=this;void 0!==t.handler;)t=t.handler;return t},g.prototype.chain=function(t,n,e,r,o){this.when({resolver:t,receiver:n,fulfilled:e,rejected:r,progress:o})},g.prototype.visit=function(t,n,e,r){this.chain(Z,t,n,e,r)},g.prototype.fold=function(t,n,e,r){this.when(new Q(t,n,e,r))},J(g,_),_.prototype.become=function(t){t.fail()};var Z=new _;J(g,w),w.prototype._state=0,w.prototype.resolve=function(t){this.become(v(t))},w.prototype.reject=function(t){this.resolved||this.become(new k(t))},w.prototype.join=function(){if(!this.resolved)return this;for(var t=this;void 0!==t.handler;)if(t=t.handler,t===this)return this.handler=C();return t},w.prototype.run=function(){var t=this.consumers,n=this.handler;this.handler=this.handler.join(),this.consumers=void 0;for(var e=0;e<t.length;++e)n.when(t[e])},w.prototype.become=function(t){this.resolved||(this.resolved=!0,this.handler=t,void 0!==this.consumers&&V.enqueue(this),void 0!==this.context&&t._report(this.context))},w.prototype.when=function(t){this.resolved?V.enqueue(new R(t,this.handler)):void 0===this.consumers?this.consumers=[t]:this.consumers.push(t)},w.prototype.notify=function(t){this.resolved||V.enqueue(new q(t,this))},w.prototype.fail=function(t){var n="undefined"==typeof t?this.context:t;this.resolved&&this.handler.join().fail(n)},w.prototype._report=function(t){this.resolved&&this.handler.join()._report(t)},w.prototype._unreport=function(){this.resolved&&this.handler.join()._unreport()},J(g,j),j.prototype.when=function(t){V.enqueue(new R(t,this))},j.prototype._report=function(t){this.join()._report(t)},j.prototype._unreport=function(){this.join()._unreport()},J(w,x),J(g,E),E.prototype._state=1,E.prototype.fold=function(t,n,e,r){U(t,n,this,e,r)},E.prototype.when=function(t){M(t.fulfilled,this,t.receiver,t.resolver)};var $=0;J(g,k),k.prototype._state=-1,k.prototype.fold=function(t,n,e,r){r.become(this)},k.prototype.when=function(t){"function"==typeof t.rejected&&this._unreport(),M(t.rejected,this,t.receiver,t.resolver)},k.prototype._report=function(t){V.afterQueue(new A(this,t))},k.prototype._unreport=function(){this.handled||(this.handled=!0,V.afterQueue(new T(this)))},k.prototype.fail=function(t){this.reported=!0,X("unhandledRejection",this),n.onFatalRejection(this,void 0===t?this.context:t)},A.prototype.run=function(){this.rejection.handled||this.rejection.reported||(this.rejection.reported=!0,X("unhandledRejection",this.rejection)||n.onPotentiallyUnhandledRejection(this.rejection,this.context))},T.prototype.run=function(){this.rejection.reported&&(X("rejectionHandled",this.rejection)||n.onPotentiallyUnhandledRejectionHandled(this.rejection))},n.createContext=n.enterContext=n.exitContext=n.onPotentiallyUnhandledRejection=n.onPotentiallyUnhandledRejectionHandled=n.onFatalRejection=D;var tt=new g,nt=new n(g,tt);return R.prototype.run=function(){this.handler.join().when(this.continuation)},q.prototype.run=function(){var t=this.handler.consumers;if(void 0!==t)for(var n,e=0;e<t.length;++e)n=t[e],H(n.progress,this.value,this.handler,n.receiver,n.resolver)},O.prototype.run=function(){function t(t){r.resolve(t)}function n(t){r.reject(t)}function e(t){r.notify(t)}var r=this.resolver;P(this._then,this.thenable,t,n,e)},Q.prototype.fulfilled=function(t){this.f.call(this.c,this.z,t,this.to)},Q.prototype.rejected=function(t){this.to.reject(t)},Q.prototype.progress=function(t){this.to.notify(t)},n}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],25:[function(n,e,r){!function(t){"use strict";t(function(){function t(){return{state:"pending"}}function n(t){return{state:"rejected",reason:t}}function e(t){return{state:"fulfilled",value:t}}function r(r){var o=r.state();return 0===o?t():o>0?e(r.value):n(r.value)}return{pending:t,fulfilled:e,rejected:n,inspect:r}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t()})},{}],26:[function(n,e,r){!function(t){t(function(t){function n(t,n){return p(t,this,n||[])}function e(t,n,e,r){var o=u(r);try{switch(e.length){case 2:t.call(n,e[0],e[1],o);break;case 1:t.call(n,e[0],o);break;case 0:t.call(n,o);break;default:e.push(o),t.apply(n,e)}}catch(i){r.reject(i)}}function r(t){return p(t,this,h.call(arguments,1))}function o(t){var n=arguments.length>1?h.call(arguments,1):[];return function(){var e,r=n.length,o=arguments.length,i=new Array(o+r);for(e=0;r>e;++e)i[e]=n[e];for(e=0;o>e;++e)i[e+r]=arguments[e];return p(t,this,i)}}function i(t,n,e){return s(o,n,e,t)}function u(t){return function(n,e){n?t.reject(n):arguments.length>2?t.resolve(h.call(arguments,1)):t.resolve(e)}}function c(t,n){function e(t){r(null,t)}function r(t,e){l(function(){n(t,e)},0)}return t=a(t),n&&t.then(e,r),t}function f(t){return function(n){return c(n,t)}}var a=t("./when"),s=t("./lib/liftAll"),l=t("./lib/env").setTimer,h=Array.prototype.slice,p=t("./lib/apply")(a.Promise,e);return{lift:o,liftAll:i,apply:n,call:r,createCallback:u,bindCallback:c,liftCallback:f}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./lib/apply":11,"./lib/env":21,"./lib/liftAll":23,"./when":32}],27:[function(n,e,r){!function(t){t(function(t){var n=t("./when"),e=n.Promise.all,r=Array.prototype.slice;return function(t){return e(r.call(arguments,1)).then(function(e){return n.map(t,function(t){return t.apply(void 0,e)})})}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./when":32}],28:[function(n,e,r){!function(t){t(function(t){var n=t("./when"),e=n.Promise.all,r=Array.prototype.slice;return function(t){var o=function(t,n){return o=function(t,n){return n(t)},n.apply(null,t)};return e(r.call(arguments,1)).then(function(e){return n.reduce(t,function(t,n){return o(t,n)},e)})}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./when":32}],29:[function(n,e,r){!function(t){"use strict";t(function(t){var n=t("./when"),e=n["try"],r=t("./cancelable");return function(t,o,i,u){function c(t){s.resolve(t)}function f(t){e(o).then(a,h),void 0!==t&&s.notify(t)}function a(){l||n(t(),function(t){n(i(t),function(n){return n?c(t):f(t)},function(){f(t)})},h)}var s,l,h;return l=!1,s=r(n.defer(),function(){l=!0}),h=s.reject,i=i||function(){return!1},"function"!=typeof o&&(o=function(t){return function(){return n().delay(t)}}(o)),u?f():a(),s.promise=Object.create(s.promise),s.promise.cancel=s.cancel,s.promise}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./cancelable":3,"./when":32}],30:[function(n,e,r){!function(t){t(function(t){var n=t("./when"),e=n.Promise.all,r=Array.prototype.slice;return function(t){function o(t){return i.push(t),i}var i=[];return e(r.call(arguments,1)).then(function(e){return n.reduce(t,function(t,r){return n(r.apply(void 0,e),o)},i)})}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./when":32}],31:[function(n,e,r){!function(t){t(function(t){var n=t("./when");return function(t,e){return n(e).timeout(t)}})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./when":32}],32:[function(n,e,r){!function(t){"use strict";t(function(t){function n(t,n,e,r){var o=x.resolve(t);return arguments.length<2?o:o.then(n,e,r)}function e(t){return new x(t)}function r(t){return function(){for(var n=0,e=arguments.length,r=new Array(e);e>n;++n)r[n]=arguments[n];return E(t,this,r)}}function o(t){for(var n=0,e=arguments.length-1,r=new Array(e);e>n;++n)r[n]=arguments[n+1];return E(t,this,r)}function i(){return new u}function u(){function t(t){r._handler.resolve(t)}function n(t){r._handler.reject(t)}function e(t){r._handler.notify(t)}var r=x._defer();this.promise=r,this.resolve=t,this.reject=n,this.notify=e,this.resolver={resolve:t,reject:n,notify:e}}function c(t){return t&&"function"==typeof t.then}function f(){return x.all(arguments)}function a(t){return n(t,x.all)}function s(t){return n(t,x.settle)}function l(t,e){return n(t,function(t){return x.map(t,e)})}function h(t,e){return n(t,function(t){return x.filter(t,e)})}var p=t("./lib/decorators/timed"),d=t("./lib/decorators/array"),y=t("./lib/decorators/flow"),v=t("./lib/decorators/fold"),m=t("./lib/decorators/inspect"),b=t("./lib/decorators/iterate"),g=t("./lib/decorators/progress"),_=t("./lib/decorators/with"),w=t("./lib/decorators/unhandledRejection"),j=t("./lib/TimeoutError"),x=[d,y,v,b,g,m,_,p,w].reduce(function(t,n){return n(t)},t("./lib/Promise")),E=t("./lib/apply")(x);return n.promise=e,n.resolve=x.resolve,n.reject=x.reject,n.lift=r,n["try"]=o,n.attempt=o,n.iterate=x.iterate,n.unfold=x.unfold,n.join=f,n.all=a,n.settle=s,n.any=r(x.any),n.some=r(x.some),n.race=r(x.race),n.map=l,n.filter=h,n.reduce=r(x.reduce),n.reduceRight=r(x.reduceRight),n.isPromiseLike=c,n.Promise=x,n.defer=i,n.TimeoutError=j,n})}("function"==typeof t&&t.amd?t:function(t){e.exports=t(n)})},{"./lib/Promise":8,"./lib/TimeoutError":10,"./lib/apply":11,"./lib/decorators/array":12,"./lib/decorators/flow":13,"./lib/decorators/fold":14,"./lib/decorators/inspect":15,"./lib/decorators/iterate":16,"./lib/decorators/progress":17,"./lib/decorators/timed":18,"./lib/decorators/unhandledRejection":19,"./lib/decorators/with":20}]},{},[1])(1)});
//# sourceMappingURL=dist/browser/when.min.js.map