{"version": 3, "file": "combineLatest.js", "sourceRoot": "", "sources": ["../../../src/add/observable/combineLatest.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,8BAAqD,gCAAgC,CAAC,CAAA;AAEtF,uBAAU,CAAC,aAAa,GAAG,6BAAmB,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { combineLatest as combineLatestStatic } from '../../observable/combineLatest';\n\nObservable.combineLatest = combineLatestStatic;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let combineLatest: typeof combineLatestStatic;\n  }\n}"]}