{"version": 3, "file": "sr-Latn-ME.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/sr-Latn-ME.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC;QACrD,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;QACtD,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;KAC1D;IACD;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC;QACrD,AADsD;KAEvD;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['ponoć', 'podne', 'jutro', 'po pod.', 'veče', 'noć'],\n    ['ponoć', 'podne', 'jutro', 'po pod.', 'veče', 'noću'],\n    ['ponoć', 'podne', 'ujutro', 'po podne', 'uveče', 'noću']\n  ],\n  [\n    ['ponoć', 'podne', 'jutro', 'popodne', 'veče', 'noć'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '06:00']\n  ]\n];\n"]}