{"version": 3, "file": "switchMap.js", "sourceRoot": "", "sources": ["../../src/operators/switchMap.ts"], "names": [], "mappings": ";;;;;;AAIA,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAM9D,mCAAmC;AAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8CG;AACH,mBACE,OAAwD,EACxD,cAA4F;IAE5F,MAAM,CAAC,mCAAmC,MAAqB;QAC7D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;IACrE,CAAC,CAAC;AACJ,CAAC;AAPe,iBAAS,YAOxB,CAAA;AAED;IACE,2BAAoB,OAAwD,EACxD,cAA4F;QAD5F,YAAO,GAAP,OAAO,CAAiD;QACxD,mBAAc,GAAd,cAAc,CAA8E;IAChH,CAAC;IAED,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IAClG,CAAC;IACH,wBAAC;AAAD,CAAC,AARD,IAQC;AAED;;;;GAIG;AACH;IAA2C,uCAAqB;IAI9D,6BAAY,WAA0B,EAClB,OAAwD,EACxD,cAA4F;QAC9G,kBAAM,WAAW,CAAC,CAAC;QAFD,YAAO,GAAP,OAAO,CAAiD;QACxD,mBAAc,GAAd,cAAc,CAA8E;QALxG,UAAK,GAAW,CAAC,CAAC;IAO1B,CAAC;IAES,mCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,MAA0B,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC;YACH,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACtC,CAAE;QAAA,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,CAAC;QACT,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAEO,uCAAS,GAAjB,UAAkB,MAA0B,EAAE,KAAQ,EAAE,KAAa;QACnE,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACjD,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACtB,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,GAAG,qCAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IACnF,CAAC;IAES,uCAAS,GAAnB;QACS,8CAAiB,CAAS;QACjC,EAAE,CAAC,CAAC,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;YACnD,gBAAK,CAAC,SAAS,WAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,oCAAoC,CAAC,0CAAY,GAAZ;QACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,4CAAc,GAAd,UAAe,QAAsB;QACnC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACnB,gBAAK,CAAC,SAAS,WAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,wCAAU,GAAV,UAAW,UAAa,EAAE,UAAa,EAC5B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACxC,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACtE,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,4CAAc,GAAtB,UAAuB,UAAa,EAAE,UAAa,EAAE,UAAkB,EAAE,UAAkB;QACzF,IAAI,MAAS,CAAC;QACd,IAAI,CAAC;YACH,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC/E,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,CAAC;QACT,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IACH,0BAAC;AAAD,CAAC,AArED,CAA2C,iCAAe,GAqEzD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Observable, ObservableInput } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OperatorFunction } from '../interfaces';\n\n/* tslint:disable:max-line-length */\nexport function switchMap<T, R>(project: (value: T, index: number) => ObservableInput<R>): OperatorFunction<T, R>;\nexport function switchMap<T, I, R>(project: (value: T, index: number) => ObservableInput<I>, resultSelector: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R): OperatorFunction<T, R>;\n/* tslint:enable:max-line-length */\n\n/**\n * Projects each source value to an Observable which is merged in the output\n * Observable, emitting values only from the most recently projected Observable.\n *\n * <span class=\"informal\">Maps each value to an Observable, then flattens all of\n * these inner Observables using {@link switch}.</span>\n *\n * <img src=\"./img/switchMap.png\" width=\"100%\">\n *\n * Returns an Observable that emits items based on applying a function that you\n * supply to each item emitted by the source Observable, where that function\n * returns an (so-called \"inner\") Observable. Each time it observes one of these\n * inner Observables, the output Observable begins emitting the items emitted by\n * that inner Observable. When a new inner Observable is emitted, `switchMap`\n * stops emitting items from the earlier-emitted inner Observable and begins\n * emitting items from the new one. It continues to behave like this for\n * subsequent inner Observables.\n *\n * @example <caption>Rerun an interval Observable on every click event</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var result = clicks.switchMap((ev) => Rx.Observable.interval(1000));\n * result.subscribe(x => console.log(x));\n *\n * @see {@link concatMap}\n * @see {@link exhaustMap}\n * @see {@link mergeMap}\n * @see {@link switch}\n * @see {@link switchMapTo}\n *\n * @param {function(value: T, ?index: number): ObservableInput} project A function\n * that, when applied to an item emitted by the source Observable, returns an\n * Observable.\n * @param {function(outerValue: T, innerValue: I, outerIndex: number, innerIndex: number): any} [resultSelector]\n * A function to produce the value on the output Observable based on the values\n * and the indices of the source (outer) emission and the inner Observable\n * emission. The arguments passed to this function are:\n * - `outerValue`: the value that came from the source\n * - `innerValue`: the value that came from the projected Observable\n * - `outerIndex`: the \"index\" of the value that came from the source\n * - `innerIndex`: the \"index\" of the value from the projected Observable\n * @return {Observable} An Observable that emits the result of applying the\n * projection function (and the optional `resultSelector`) to each item emitted\n * by the source Observable and taking only the values from the most recently\n * projected inner Observable.\n * @method switchMap\n * @owner Observable\n */\nexport function switchMap<T, I, R>(\n  project: (value: T, index: number) => ObservableInput<I>,\n  resultSelector?: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, I | R> {\n  return function switchMapOperatorFunction(source: Observable<T>): Observable<I | R> {\n    return source.lift(new SwitchMapOperator(project, resultSelector));\n  };\n}\n\nclass SwitchMapOperator<T, I, R> implements Operator<T, I> {\n  constructor(private project: (value: T, index: number) => ObservableInput<I>,\n              private resultSelector?: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R) {\n  }\n\n  call(subscriber: Subscriber<I>, source: any): any {\n    return source.subscribe(new SwitchMapSubscriber(subscriber, this.project, this.resultSelector));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass SwitchMapSubscriber<T, I, R> extends OuterSubscriber<T, I> {\n  private index: number = 0;\n  private innerSubscription: Subscription;\n\n  constructor(destination: Subscriber<I>,\n              private project: (value: T, index: number) => ObservableInput<I>,\n              private resultSelector?: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R) {\n    super(destination);\n  }\n\n  protected _next(value: T) {\n    let result: ObservableInput<I>;\n    const index = this.index++;\n    try {\n      result = this.project(value, index);\n    } catch (error) {\n      this.destination.error(error);\n      return;\n    }\n    this._innerSub(result, value, index);\n  }\n\n  private _innerSub(result: ObservableInput<I>, value: T, index: number) {\n    const innerSubscription = this.innerSubscription;\n    if (innerSubscription) {\n      innerSubscription.unsubscribe();\n    }\n    this.add(this.innerSubscription = subscribeToResult(this, result, value, index));\n  }\n\n  protected _complete(): void {\n    const {innerSubscription} = this;\n    if (!innerSubscription || innerSubscription.closed) {\n      super._complete();\n    }\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    this.innerSubscription = null;\n  }\n\n  notifyComplete(innerSub: Subscription): void {\n    this.remove(innerSub);\n    this.innerSubscription = null;\n    if (this.isStopped) {\n      super._complete();\n    }\n  }\n\n  notifyNext(outerValue: T, innerValue: I,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, I>): void {\n    if (this.resultSelector) {\n      this._tryNotifyNext(outerValue, innerValue, outerIndex, innerIndex);\n    } else {\n      this.destination.next(innerValue);\n    }\n  }\n\n  private _tryNotifyNext(outerValue: T, innerValue: I, outerIndex: number, innerIndex: number): void {\n    let result: R;\n    try {\n      result = this.resultSelector(outerValue, innerValue, outerIndex, innerIndex);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n    this.destination.next(result);\n  }\n}\n"]}