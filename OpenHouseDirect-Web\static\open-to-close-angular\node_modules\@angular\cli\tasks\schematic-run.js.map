{"version": 3, "file": "schematic-run.js", "sourceRoot": "/users/hansl/sources/hansl/angular-cli/", "sources": ["tasks/schematic-run.ts"], "names": [], "mappings": ";;AAAA,2DAQoC;AACpC,gEAA4E;AAC5E,4DAAkE;AAClE,2CAAwD;AACxD,6BAA6B;AAC7B,iCAA0B;AAC1B,6CAA6C;AAC7C,8CAAwE;AACxE,wDAAgG;AAGhG,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,eAAK,CAAC;AACrC,MAAM,IAAI,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AA2BrD,kBAAe,IAAI,CAAC,MAAM,CAAC;IACzB,GAAG,EAAE,UAAU,OAA4B;QACzC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE9F,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAEnB,MAAM,cAAc,GAAG,kBAAS,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACpE,MAAM,UAAU,GAAG,0BAAa,EAAE,CAAC;QACnC,UAAU,CAAC,oBAAoB,CAC7B,0BAAmB,CAAC,WAAW,EAC/B;YACE,aAAa,EAAE,UAAU;YACzB,cAAc,EAAE,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc;SACtE,CACF,CAAC;QACF,UAAU,CAAC,oBAAoB,CAC7B,0BAAmB,CAAC,qBAAqB,EACzC,EAAE,aAAa,EAAE,UAAU,EAAE,CAC9B,CAAC;QAEF,MAAM,UAAU,GAAG,0BAAa,CAAC,cAAc,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,yBAAY,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAE1D,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC;QAEpE,MAAM,cAAc,GAAG,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAC3D,MAAM,IAAI,qBAAQ,WAAW,EAAK,cAAc,CAAE,CAAC;QAEnD,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,sBAAS,EAAE,CAAC,CAAC,CAAC,IAAI,2BAAc,CAAC,IAAI,sBAAc,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9F,MAAM,IAAI,GAAG,OAAY,CAAC,IAAI,CAAC,CAAC;QAEhC,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,IAAI,2BAAc,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1D,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,MAAM,YAAY,GAAoB,EAAE,CAAC;QACzC,MAAM,aAAa,GAAa,EAAE,CAAC;QAEnC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAkB,EAAE,EAAE;YACnD,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;YACjF,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnB,KAAK,OAAO;oBACV,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,IAAI,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,CAAC;oBACxF,EAAE,CAAC,SAAS,CAAC,UAAU,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC;oBAC7C,KAAK,GAAG,IAAI,CAAC;oBACb,KAAK,CAAC;gBACR,KAAK,QAAQ;oBACX,YAAY,CAAC,IAAI,CAAC;wBAChB,KAAK,EAAE,MAAM;wBACb,OAAO,EAAE,QAAQ;wBACjB,OAAO,EAAE,GAAG,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,SAAS;qBACxD,CAAC,CAAC;oBACH,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC/B,KAAK,CAAC;gBACR,KAAK,QAAQ;oBACX,YAAY,CAAC,IAAI,CAAC;wBAChB,KAAK,EAAE,KAAK;wBACZ,OAAO,EAAE,QAAQ;wBACjB,OAAO,EAAE,GAAG,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,SAAS;qBACxD,CAAC,CAAC;oBACH,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC/B,KAAK,CAAC;gBACR,KAAK,QAAQ;oBACX,YAAY,CAAC,IAAI,CAAC;wBAChB,KAAK,EAAE,GAAG;wBACV,OAAO,EAAE,QAAQ;wBACjB,OAAO,EAAE,GAAG,SAAS,EAAE;qBACxB,CAAC,CAAC;oBACH,KAAK,CAAC;gBACR,KAAK,QAAQ;oBACX,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7E,YAAY,CAAC,IAAI,CAAC;wBAChB,KAAK,EAAE,MAAM;wBACb,OAAO,EAAE,QAAQ;wBACjB,OAAO,EAAE,GAAG,SAAS,OAAO,WAAW,EAAE;qBAC1C,CAAC,CAAC;oBACH,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBAC7B,KAAK,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,CACzC,eAAG,CAAC,CAAC,IAAU,EAAE,EAAE,CAAC,iBAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EACxC,qBAAS,CAAC,CAAC,IAAU,EAAE,EAAE;gBACvB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CACjC,0BAAc,EAAE,EAChB,kBAAM,CAAC,OAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,EACF,qBAAS,CAAC,CAAC,IAAU,EAAE,EAAE;gBACvB,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBACX,4BAA4B;oBAC5B,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC1F,CAAC;gBAED,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC;oBACzB,MAAM,CAAC,OAAY,CAAC,IAAI,CAAC,CAAC;gBAC5B,CAAC;gBACD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAC7B,0BAAc,EAAE,EAChB,kBAAM,CAAC,OAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,EACF,qBAAS,CAAC,GAAG,EAAE;gBACb,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBACjB,MAAM,CAAC,sBAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;gBACxC,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,MAAM,CAAC,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC;iBACF,SAAS,CAAC;gBACT,KAAK,CAAC,GAAG;oBACP,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC3C,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACtB,CAAC;gBACD,QAAQ;oBACN,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;wBAChB,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,kDAAkD,CAAC,CAAC,CAAC;oBAC3E,CAAC;oBACD,OAAO,CAAC,EAAC,aAAa,EAAC,CAAC,CAAC;gBAC3B,CAAC;aACF,CAAC,CAAC;QACP,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,MAAuB,EAAE,EAAE;YAChC,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;YAC3C,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC;gBACjD,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAE/D,EAAE,CAAC,CAAC,OAAO,IAAI,aAAa,CAAC,CAAC,CAAC;gBAC7B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBAC3C,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC;oBAC5B,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAC;gBAEH,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAClB,GAAG,EAAE,IAAI;oBACT,KAAK,EAAE,IAAI;oBACX,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,CAAC;4BACR,KAAK,EAAE,aAAa;iCACjB,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iCAC3C,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;yBACvD,CAAC;iBACH,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC;AAEH,qBAAqB,SAA4B,EAAE,OAAyB;IAC1E,MAAM,UAAU,GAAS,SAAS,CAAC,WAAY,CAAC,UAAU;QACxD,CAAC,CAAO,SAAS,CAAC,WAAY,CAAC,UAAU,CAAC,UAAU;QACpD,CAAC,CAAC,OAAO,CAAC;IAEZ,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACrC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpF,OAAO,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;YACpE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IACjC,CAAC;IAED,IAAI,cAAc,qBACb,OAAO,EACP,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAC3D,CAAC;IACF,cAAc,qBACT,cAAc,EACd,gBAAgB,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAC/D,CAAC;IAEF,MAAM,CAAC,cAAc,CAAC;AACxB,CAAC;AAED,sBAAsB,aAAqB,EAAE,UAAoB,EAAE,OAAY;IAC7E,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,GAAG,EAAE,EAAE;QACzC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;QACvF,MAAM,CAAC,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,MAAM,oBAAoB,GAAQ;IAChC,UAAU,EAAE,UAAU;IACtB,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,MAAM;CACf,CAAC;AAEF,MAAM,kBAAkB,GAAQ;IAC9B,SAAS,EAAE,SAAS;IACpB,QAAQ,EAAE,QAAQ;CACnB,CAAC;AAEF,0BAA0B,aAAqB,EAAE,UAAoB,EAAE,OAAY;IACjF,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,GAAG,EAAE,EAAE;QAEzC,EAAE,CAAC,CAAC,aAAa,KAAK,aAAa,IAAI,aAAa,KAAK,WAAW,CAAC,CAAC,CAAC;YACrE,EAAE,CAAC,CAAC,GAAG,KAAK,mBAAmB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChD,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAC9D,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,iBAAiB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrD,GAAG,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QACD,MAAM,CAAC,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,qBAAqB,aAAqB,EAAE,GAAW;IACrD,MAAM,QAAQ,GAAG,YAAY,aAAa,IAAI,GAAG,EAAE,CAAC;IACpD,MAAM,CAAC,kBAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACtC,CAAC"}