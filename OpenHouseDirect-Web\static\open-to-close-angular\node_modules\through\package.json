{"_from": "through@~2.3.4", "_id": "through@2.3.8", "_inBundle": false, "_integrity": "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==", "_location": "/through", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "through@~2.3.4", "name": "through", "escapedName": "through", "rawSpec": "~2.3.4", "saveSpec": null, "fetchSpec": "~2.3.4"}, "_requiredBy": ["/cssauron", "/envify"], "_resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "_shasum": "0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5", "_spec": "through@~2.3.4", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\envify", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "bugs": {"url": "https://github.com/dominictarr/through/issues"}, "bundleDependencies": false, "deprecated": false, "description": "simplified stream construction", "devDependencies": {"from": "~0.1.3", "stream-spec": "~0.3.5", "tape": "~2.3.2"}, "homepage": "https://github.com/dominictarr/through", "keywords": ["stream", "streams", "user-streams", "pipe"], "license": "MIT", "main": "index.js", "name": "through", "repository": {"type": "git", "url": "git+https://github.com/dominictarr/through.git"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "testling": {"browsers": ["ie/8..latest", "ff/15..latest", "chrome/20..latest", "safari/5.1..latest"], "files": "test/*.js"}, "version": "2.3.8"}