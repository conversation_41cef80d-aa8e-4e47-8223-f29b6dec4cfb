{"_from": "util-deprecate@~1.0.1", "_id": "util-deprecate@1.0.2", "_inBundle": false, "_integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "_location": "/util-deprecate", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "util-deprecate@~1.0.1", "name": "util-deprecate", "escapedName": "util-deprecate", "rawSpec": "~1.0.1", "saveSpec": null, "fetchSpec": "~1.0.1"}, "_requiredBy": ["/bl/readable-stream", "/readable-stream", "/spdy-transport/readable-stream"], "_resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "_shasum": "450d4dc9fa70de732762fbd2d4a28981419a0ccf", "_spec": "util-deprecate@~1.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\readable-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "browser": "browser.js", "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The Node.js `util.deprecate()` function with browser support", "homepage": "https://github.com/TooTallNate/util-deprecate", "keywords": ["util", "deprecate", "browserify", "browser", "node"], "license": "MIT", "main": "node.js", "name": "util-deprecate", "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.2"}