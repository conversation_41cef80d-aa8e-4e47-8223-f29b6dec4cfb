{"_from": "is-number@^3.0.0", "_id": "is-number@3.0.0", "_inBundle": false, "_integrity": "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==", "_location": "/watchpack-chokidar2/is-number", "_phantomChildren": {"is-buffer": "1.1.6"}, "_requested": {"type": "range", "registry": true, "raw": "is-number@^3.0.0", "name": "is-number", "escapedName": "is-number", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/watchpack-chokidar2/fill-range"], "_resolved": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "_shasum": "24fd6201a4782cf50561c810276afc7d12d71195", "_spec": "is-number@^3.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\watchpack-chokidar2\\node_modules\\fill-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON> Reagent", "url": "http://www.tunnckocore.tk"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"kind-of": "^3.0.2"}, "deprecated": false, "description": "Returns true if the value is a number. comprehensive tests.", "devDependencies": {"benchmarked": "^0.2.5", "chalk": "^1.1.3", "gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-number", "keywords": ["check", "coerce", "coercion", "integer", "is", "is-nan", "is-num", "is-number", "istype", "kind", "math", "nan", "num", "number", "numeric", "test", "type", "typeof", "value"], "license": "MIT", "main": "index.js", "name": "is-number", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["even", "is-even", "is-odd", "is-primitive", "kind-of", "odd"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb", "verb-generate-readme"]}, "version": "3.0.0"}