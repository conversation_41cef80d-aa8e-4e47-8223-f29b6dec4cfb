{"_from": "ret@~0.1.10", "_id": "ret@0.1.15", "_inBundle": false, "_integrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==", "_location": "/ret", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ret@~0.1.10", "name": "ret", "escapedName": "ret", "rawSpec": "~0.1.10", "saveSpec": null, "fetchSpec": "~0.1.10"}, "_requiredBy": ["/safe-regex"], "_resolved": "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz", "_shasum": "b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc", "_spec": "ret@~0.1.10", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\safe-regex", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/fent"}, "bugs": {"url": "https://github.com/fent/ret.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tokenizes a string that represents a regular expression.", "devDependencies": {"istanbul": "*", "vows": "*"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.12"}, "files": ["lib"], "homepage": "https://github.com/fent/ret.js#readme", "keywords": ["regex", "regexp", "regular expression", "parser", "tokenizer"], "license": "MIT", "main": "./lib/index.js", "name": "ret", "repository": {"type": "git", "url": "git://github.com/fent/ret.js.git"}, "scripts": {"test": "istanbul cover vows -- --spec test/*-test.js"}, "version": "0.1.15"}