/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@angular/core"),require("@angular/cdk/platform"),require("rxjs/Subject"),require("rxjs/operators/map"),require("rxjs/operators/startWith"),require("rxjs/operators/takeUntil"),require("@angular/cdk/coercion"),require("rxjs/observable/combineLatest"),require("rxjs/observable/fromEventPattern")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/platform","rxjs/Subject","rxjs/operators/map","rxjs/operators/startWith","rxjs/operators/takeUntil","@angular/cdk/coercion","rxjs/observable/combineLatest","rxjs/observable/fromEventPattern"],e):e((t.ng=t.ng||{},t.ng.cdk=t.ng.cdk||{},t.ng.cdk.layout=t.ng.cdk.layout||{}),t.ng.core,t.ng.cdk.platform,t.Rx,t.Rx.operators,t.Rx.operators,t.Rx.operators,t.ng.cdk.coercion,t.Rx.Observable,t.Rx.Observable)}(this,function(t,e,r,n,a,i,o,s,d,c){"use strict";function u(t){if(!m.has(t))try{var e=document.createElement("style");if(e.setAttribute("type","text/css"),!e.sheet){var r="@media "+t+" {.fx-query-test{ }}";e.appendChild(document.createTextNode(r))}document.getElementsByTagName("head")[0].appendChild(e),m.set(t,e)}catch(t){console.error(t)}}function p(t){return{matches:"all"===t||""===t,media:t,addListener:function(){},removeListener:function(){}}}var m=new Map,h=function(){function t(t){this.platform=t,this._matchMedia=this.platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):p}return t.prototype.matchMedia=function(t){return this.platform.WEBKIT&&u(t),this._matchMedia(t)},t.decorators=[{type:e.Injectable}],t.ctorParameters=function(){return[{type:r.Platform}]},t}(),x=function(){function t(t,e){this.mediaMatcher=t,this.zone=e,this._queries=new Map,this._destroySubject=new n.Subject}return t.prototype.ngOnDestroy=function(){this._destroySubject.next(),this._destroySubject.complete()},t.prototype.isMatched=function(t){var e=this;return s.coerceArray(t).some(function(t){return e._registerQuery(t).mql.matches})},t.prototype.observe=function(t){var e=this,r=s.coerceArray(t),n=r.map(function(t){return e._registerQuery(t).observable});return d.combineLatest(n,function(t,e){return{matches:!!(t&&t.matches||e&&e.matches)}})},t.prototype._registerQuery=function(t){var e=this;if(this._queries.has(t))return this._queries.get(t);var r=this.mediaMatcher.matchMedia(t),n=c.fromEventPattern(function(t){r.addListener(function(r){return e.zone.run(function(){return t(r)})})},function(t){r.removeListener(function(r){return e.zone.run(function(){return t(r)})})}).pipe(o.takeUntil(this._destroySubject),i.startWith(r),a.map(function(t){return{matches:t.matches}})),s={observable:n,mql:r};return this._queries.set(t,s),s},t.decorators=[{type:e.Injectable}],t.ctorParameters=function(){return[{type:h},{type:e.NgZone}]},t}(),l={XSmall:"(max-width: 599px)",Small:"(min-width: 600px) and (max-width: 959px)",Medium:"(min-width: 960px) and (max-width: 1279px)",Large:"(min-width: 1280px) and (max-width: 1919px)",XLarge:"(min-width: 1920px)",Handset:"(max-width: 599px) and (orientation: portrait), (max-width: 959px) and (orientation: landscape)",Tablet:"(min-width: 600px) and (max-width: 839px) and (orientation: portrait), (min-width: 960px) and (max-width: 1279px) and (orientation: landscape)",Web:"(min-width: 840px) and (orientation: portrait), (min-width: 1280px) and (orientation: landscape)",HandsetPortrait:"(max-width: 599px) and (orientation: portrait)",TabletPortrait:"(min-width: 600px) and (max-width: 839px) and (orientation: portrait)",WebPortrait:"(min-width: 840px) and (orientation: portrait)",HandsetLandscape:"(max-width: 959px) and (orientation: landscape)",TabletLandscape:"(min-width: 960px) and (max-width: 1279px) and (orientation: landscape)",WebLandscape:"(min-width: 1280px) and (orientation: landscape)"},f=function(){function t(){}return t.decorators=[{type:e.NgModule,args:[{providers:[x,h],imports:[r.PlatformModule]}]}],t.ctorParameters=function(){return[]},t}();t.LayoutModule=f,t.BreakpointObserver=x,t.Breakpoints=l,t.MediaMatcher=h,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-layout.umd.min.js.map
