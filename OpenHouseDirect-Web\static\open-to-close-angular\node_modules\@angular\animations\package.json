{"_from": "@angular/animations@^5.2.5", "_id": "@angular/animations@5.2.11", "_inBundle": false, "_integrity": "sha512-J7wKHkFn3wV28/Y1Qm4yjGXVCwXzj1JR5DRjGDTFnxTRacUFx7Nj0ApGhN0b2+V0NOvgxQOvEW415Y22kGoblw==", "_location": "/@angular/animations", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@angular/animations@^5.2.5", "name": "@angular/animations", "escapedName": "@angular%2fanimations", "scope": "@angular", "rawSpec": "^5.2.5", "saveSpec": null, "fetchSpec": "^5.2.5"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@angular/animations/-/animations-5.2.11.tgz", "_shasum": "2bd3fe9e72916ca28de9bfaaddf0cb936565a0b8", "_spec": "@angular/animations@^5.2.5", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.7.1"}, "deprecated": false, "description": "Angular - animations integration with web-animationss", "es2015": "./esm2015/animations.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/animations.umd.js", "module": "./esm5/animations.js", "name": "@angular/animations", "peerDependencies": {"@angular/core": "5.2.11"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "typings": "./animations.d.ts", "version": "5.2.11"}