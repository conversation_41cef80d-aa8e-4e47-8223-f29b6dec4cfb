{"version": 3, "file": "fromPromise.js", "sourceRoot": "", "sources": ["../../../src/add/observable/fromPromise.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,4BAAiD,8BAA8B,CAAC,CAAA;AAEhF,uBAAU,CAAC,WAAW,GAAG,yBAAiB,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { fromPromise as staticFromPromise } from '../../observable/fromPromise';\n\nObservable.fromPromise = staticFromPromise;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let fromPromise: typeof staticFromPromise;\n  }\n}"]}