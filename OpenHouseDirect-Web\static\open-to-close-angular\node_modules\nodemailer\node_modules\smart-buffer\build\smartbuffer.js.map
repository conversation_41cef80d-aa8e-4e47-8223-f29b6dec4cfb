{"version": 3, "file": "smartbuffer.js", "sourceRoot": "", "sources": ["../src/smartbuffer.ts"], "names": [], "mappings": ";AAaA,kDAAkD;AAClD,MAAM,wBAAwB,GAAG,IAAI,CAAC;AAEtC,mEAAmE;AACnE,MAAM,4BAA4B,GAAG,MAAM,CAAC;AAE5C;IASI;;;;;OAKG;IACH,YAAY,IAA4D,EAAE,IAAqB;QAbxF,WAAM,GAAW,CAAC,CAAC;QACnB,aAAQ,GAAmB,4BAA4B,CAAC;QAEvD,gBAAW,GAAW,CAAC,CAAC;QACxB,eAAU,GAAW,CAAC,CAAC;QAW3B,+BAA+B;QAC/B,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAE3B,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC9D,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACzC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;YAC9F,CAAC;QAEL,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAClC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;gBACzD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACzB,CAAC;YAAC,IAAI,CAAC,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,kGAAkG,CAAC,CAAC;YACxH,CAAC;QAEL,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEhD,sBAAsB;YACtB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAChB,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAClC,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACJ,MAAM,IAAK,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACzH,CAAC;YACL,CAAC;YAED,iCAAiC;YACjC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBACZ,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC7E,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9C,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACJ,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;gBAC9F,CAAC;YAEL,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC,CAAC;oBAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;oBACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;gBACnC,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACJ,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;YAAC,IAAI,CAAC,CAAC;gBACJ,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QAC3E,CAAC;QAAC,IAAI,CAAC,CAAC;YACJ,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;QAC7D,CAAC;QAED,qDAAqD;QACrD,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC3B,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACzB,CAAC;YAAC,IAAI,CAAC,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,kGAAkG,CAAC,CAAC;YACxH,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,QAAQ,CAAC,IAAY,EAAE,QAAyB;QAC1D,MAAM,CAAC,IAAI,IAAI,CAAC;YACZ,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,QAAQ;SACrB,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,UAAU,CAAC,IAAY,EAAE,QAAyB;QAC5D,MAAM,CAAC,IAAI,IAAI,CAAC;YACZ,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,QAAQ;SACrB,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,OAA2B;QACjD,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACK,eAAe,CAAC,SAAiB,EAAE,MAAe;QACtD,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC;QAE1D,mDAAmD;QACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;QAEzD,uFAAuF;QACvF,EAAE,CAAC,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClF,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE,SAAS,GAAG,SAAS,CAAC,CAAC;IAC3E,CAAC;IAGD;;;;OAIG;IACK,cAAc,CAAC,SAAiB;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAEnC,EAAE,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC;YACxB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACrB,IAAI,SAAS,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACxC,EAAE,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC;gBACxB,SAAS,GAAG,SAAS,CAAC;YAC1B,CAAC;YACD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAE1C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACK,eAAe,CAAC,IAAgC,EAAE,QAAgB;QACtE,0BAA0B;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEpD,8BAA8B;QAC9B,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC;QAE5B,MAAM,CAAC,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACK,gBAAgB,CAAC,IAAgD,EAAE,QAAgB,EAAE,KAAa,EAAE,MAAe;QACvH,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAEzE,0EAA0E;QAC1E,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEvC,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAEvC,gCAAgC;QAChC,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC;IACjC,CAAC;IAGD,kBAAkB;IAElB;;;;OAIG;IACH,QAAQ;QACJ,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACH,WAAW;QACP,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,WAAW;QACP,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,WAAW;QACP,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,WAAW;QACP,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;OAOG;IACH,SAAS,CAAC,KAAa,EAAE,MAAe;QACpC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACpE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,KAAa,EAAE,MAAe;QACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,KAAa,EAAE,MAAe;QACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,KAAa,EAAE,MAAe;QACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,KAAa,EAAE,MAAe;QACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED,oBAAoB;IAEpB;;;;OAIG;IACH,SAAS;QACL,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACH,YAAY;QACR,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,YAAY;QACR,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,YAAY;QACR,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,YAAY;QACR,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;OAOG;IACH,UAAU,CAAC,KAAa,EAAE,MAAe;QACrC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACrE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,aAAa,CAAC,KAAa,EAAE,MAAe;QACxC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,aAAa,CAAC,KAAa,EAAE,MAAe;QACxC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,aAAa,CAAC,KAAa,EAAE,MAAe;QACxC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,aAAa,CAAC,KAAa,EAAE,MAAe;QACxC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED,iBAAiB;IAGjB;;;;OAIG;IACH,WAAW;QACP,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACH,WAAW;QACP,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,KAAa,EAAE,MAAe;QACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,CAAC,KAAa,EAAE,MAAe;QACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAGD,wBAAwB;IAExB;;;;OAIG;IACH,YAAY;QACR,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,YAAY;QACR,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;OAOG;IACH,aAAa,CAAC,KAAa,EAAE,MAAe;QACxC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,aAAa,CAAC,KAAa,EAAE,MAAe;QACxC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAGD,UAAU;IAEV;;;;;;;OAOG;IACH,UAAU,CAAC,MAAe,EAAE,QAAyB;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QACnG,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhH,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC;QAC7B,MAAM,CAAC,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACH,WAAW,CAAC,KAAa,EAAE,IAA8B,EAAE,QAAyB;QAChF,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;QACjC,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEhC,mBAAmB;QACnB,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC3B,SAAS,GAAG,IAAI,CAAC;QAErB,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAClC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,WAAW,GAAG,IAAI,CAAC;YACvB,CAAC;YAAC,IAAI,CAAC,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,kGAAkG,CAAC,CAAC;YACxH,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,EAAE,CAAC,CAAC,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC/B,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC9B,WAAW,GAAG,QAAQ,CAAC;YAC3B,CAAC;YAAC,IAAI,CAAC,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,kGAAkG,CAAC,CAAC;YACxH,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAEzD,mDAAmD;QACnD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAE5C,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;QAE3D,0CAA0C;QAC1C,IAAI,CAAC,WAAW,IAAI,UAAU,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,YAAY,CAAC,QAAyB;QAElC,+DAA+D;QAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,6EAA6E;QAC7E,GAAG,CAAA,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;gBACxB,OAAO,GAAG,CAAC,CAAC;gBACZ,KAAK,CAAC;YACV,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAExD,wCAAwC;QACxC,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,CAAC,CAAC;QAE9B,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACH,aAAa,CAAC,KAAa,EAAE,MAAgC,EAAE,QAAyB;QACpF,eAAe;QACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACnG,CAAC;IAED,UAAU;IAEV;;;;;;OAMG;IACH,UAAU,CAAC,MAAe;QACtB,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC;QAEpE,oBAAoB;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEzD,wCAAwC;QACxC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;QAC3B,MAAM,CAAC,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,WAAW,CAAC,KAAa,EAAE,MAAe;QACtC,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAEzE,mDAAmD;QACnD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE9C,qBAAqB;QACrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAEjC,yCAAyC;QACzC,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,YAAY;QACR,+DAA+D;QAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,6EAA6E;QAC7E,GAAG,CAAA,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;gBACxB,OAAO,GAAG,CAAC,CAAC;gBACZ,KAAK,CAAC;YACV,CAAC;QACL,CAAC;QAED,aAAa;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAExD,wCAAwC;QACxC,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,CAAC,CAAC;QAC9B,MAAM,CAAC,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,KAAa,EAAE,MAAe;QACxC,eAAe;QACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAE5F,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK;QACD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,SAAS;QACL,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,MAAc;QACf,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,MAAc;QACjB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,QAAgB;QACnB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,QAAgB;QACnB,EAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACH,QAAQ;QACJ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,QAAQ,CAAC,QAAyB;QAC9B,MAAM,WAAW,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE5E,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;QAAC,IAAI,CAAC,CAAC;YACJ,MAAM,IAAI,KAAK,CAAC,kGAAkG,CAAC,CAAC;QACxH,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,OAA2B;QACnD,MAAM,WAAW,GAAwB,OAAQ,CAAC;QAElD,MAAM,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;IACnI,CAAC;CACJ;AAGD,iBAAS,WAAW,CAAC"}