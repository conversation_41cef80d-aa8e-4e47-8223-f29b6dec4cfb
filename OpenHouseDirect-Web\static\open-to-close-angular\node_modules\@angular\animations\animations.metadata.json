{"__symbolic": "module", "version": 4, "metadata": {"AnimationBuilder": {"__symbolic": "class", "members": {"build": [{"__symbolic": "method"}]}}, "AnimationFactory": {"__symbolic": "class", "members": {"create": [{"__symbolic": "method"}]}}, "AnimationEvent": {"__symbolic": "interface"}, "AUTO_STYLE": "*", "AnimateChildOptions": {"__symbolic": "interface"}, "AnimateTimings": {"__symbolic": "interface"}, "AnimationAnimateChildMetadata": {"__symbolic": "interface"}, "AnimationAnimateMetadata": {"__symbolic": "interface"}, "AnimationAnimateRefMetadata": {"__symbolic": "interface"}, "AnimationGroupMetadata": {"__symbolic": "interface"}, "AnimationKeyframesSequenceMetadata": {"__symbolic": "interface"}, "AnimationMetadata": {"__symbolic": "interface"}, "AnimationMetadataType": {"State": 0, "Transition": 1, "Sequence": 2, "Group": 3, "Animate": 4, "Keyframes": 5, "Style": 6, "Trigger": 7, "Reference": 8, "AnimateChild": 9, "AnimateRef": 10, "Query": 11, "Stagger": 12}, "AnimationOptions": {"__symbolic": "interface"}, "AnimationQueryMetadata": {"__symbolic": "interface"}, "AnimationQueryOptions": {"__symbolic": "interface"}, "AnimationReferenceMetadata": {"__symbolic": "interface"}, "AnimationSequenceMetadata": {"__symbolic": "interface"}, "AnimationStaggerMetadata": {"__symbolic": "interface"}, "AnimationStateMetadata": {"__symbolic": "interface"}, "AnimationStyleMetadata": {"__symbolic": "interface"}, "AnimationTransitionMetadata": {"__symbolic": "interface"}, "AnimationTriggerMetadata": {"__symbolic": "interface"}, "animate": {"__symbolic": "function", "parameters": ["timings", "styles"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Animate"}, "styles": {"__symbolic": "reference", "name": "styles"}, "timings": {"__symbolic": "reference", "name": "timings"}}}, "animateChild": {"__symbolic": "function", "parameters": ["options"], "defaults": [null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options": {"__symbolic": "reference", "name": "options"}}}, "animation": {"__symbolic": "function", "parameters": ["steps", "options"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Reference"}, "animation": {"__symbolic": "reference", "name": "steps"}, "options": {"__symbolic": "reference", "name": "options"}}}, "group": {"__symbolic": "function", "parameters": ["steps", "options"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Group"}, "steps": {"__symbolic": "reference", "name": "steps"}, "options": {"__symbolic": "reference", "name": "options"}}}, "keyframes": {"__symbolic": "function", "parameters": ["steps"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Keyframes"}, "steps": {"__symbolic": "reference", "name": "steps"}}}, "query": {"__symbolic": "function", "parameters": ["selector", "animation", "options"], "defaults": [null, null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Query"}, "selector": {"__symbolic": "reference", "name": "selector"}, "animation": {"__symbolic": "reference", "name": "animation"}, "options": {"__symbolic": "reference", "name": "options"}}}, "sequence": {"__symbolic": "function", "parameters": ["steps", "options"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Sequence"}, "steps": {"__symbolic": "reference", "name": "steps"}, "options": {"__symbolic": "reference", "name": "options"}}}, "stagger": {"__symbolic": "function", "parameters": ["timings", "animation"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Stagger"}, "timings": {"__symbolic": "reference", "name": "timings"}, "animation": {"__symbolic": "reference", "name": "animation"}}}, "state": {"__symbolic": "function", "parameters": ["name", "styles", "options"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "State"}, "name": {"__symbolic": "reference", "name": "name"}, "styles": {"__symbolic": "reference", "name": "styles"}, "options": {"__symbolic": "reference", "name": "options"}}}, "style": {"__symbolic": "function", "parameters": ["tokens"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Style"}, "styles": {"__symbolic": "reference", "name": "tokens"}, "offset": null}}, "transition": {"__symbolic": "function", "parameters": ["stateChangeExpr", "steps", "options"], "defaults": [null, null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "Transition"}, "expr": {"__symbolic": "reference", "name": "stateChangeExpr"}, "animation": {"__symbolic": "reference", "name": "steps"}, "options": {"__symbolic": "reference", "name": "options"}}}, "trigger": {"__symbolic": "function", "parameters": ["name", "definitions"], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "<PERSON><PERSON>"}, "name": {"__symbolic": "reference", "name": "name"}, "definitions": {"__symbolic": "reference", "name": "definitions"}, "options": {}}}, "useAnimation": {"__symbolic": "function", "parameters": ["animation", "options"], "defaults": [null, null], "value": {"type": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "AnimationMetadataType"}, "member": "AnimateRef"}, "animation": {"__symbolic": "reference", "name": "animation"}, "options": {"__symbolic": "reference", "name": "options"}}}, "ɵStyleData": {"__symbolic": "interface"}, "AnimationPlayer": {"__symbolic": "interface"}, "NoopAnimationPlayer": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor"}], "_onFinish": [{"__symbolic": "method"}], "onStart": [{"__symbolic": "method"}], "onDone": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "triggerMicrotask": [{"__symbolic": "method"}], "_onStart": [{"__symbolic": "method"}], "pause": [{"__symbolic": "method"}], "restart": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "setPosition": [{"__symbolic": "method"}], "getPosition": [{"__symbolic": "method"}], "triggerCallback": [{"__symbolic": "method"}]}}, "ɵPRE_STYLE": "!", "ɵAnimationGroupPlayer": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "reference", "name": "AnimationPlayer"}]}]}], "_onFinish": [{"__symbolic": "method"}], "init": [{"__symbolic": "method"}], "onStart": [{"__symbolic": "method"}], "_onStart": [{"__symbolic": "method"}], "onDone": [{"__symbolic": "method"}], "onDestroy": [{"__symbolic": "method"}], "hasStarted": [{"__symbolic": "method"}], "play": [{"__symbolic": "method"}], "pause": [{"__symbolic": "method"}], "restart": [{"__symbolic": "method"}], "finish": [{"__symbolic": "method"}], "destroy": [{"__symbolic": "method"}], "_onDestroy": [{"__symbolic": "method"}], "reset": [{"__symbolic": "method"}], "setPosition": [{"__symbolic": "method"}], "getPosition": [{"__symbolic": "method"}], "beforeDestroy": [{"__symbolic": "method"}], "triggerCallback": [{"__symbolic": "method"}]}}}, "origins": {"AnimationBuilder": "./src/animation_builder", "AnimationFactory": "./src/animation_builder", "AnimationEvent": "./src/animation_event", "AUTO_STYLE": "./src/animation_metadata", "AnimateChildOptions": "./src/animation_metadata", "AnimateTimings": "./src/animation_metadata", "AnimationAnimateChildMetadata": "./src/animation_metadata", "AnimationAnimateMetadata": "./src/animation_metadata", "AnimationAnimateRefMetadata": "./src/animation_metadata", "AnimationGroupMetadata": "./src/animation_metadata", "AnimationKeyframesSequenceMetadata": "./src/animation_metadata", "AnimationMetadata": "./src/animation_metadata", "AnimationMetadataType": "./src/animation_metadata", "AnimationOptions": "./src/animation_metadata", "AnimationQueryMetadata": "./src/animation_metadata", "AnimationQueryOptions": "./src/animation_metadata", "AnimationReferenceMetadata": "./src/animation_metadata", "AnimationSequenceMetadata": "./src/animation_metadata", "AnimationStaggerMetadata": "./src/animation_metadata", "AnimationStateMetadata": "./src/animation_metadata", "AnimationStyleMetadata": "./src/animation_metadata", "AnimationTransitionMetadata": "./src/animation_metadata", "AnimationTriggerMetadata": "./src/animation_metadata", "animate": "./src/animation_metadata", "animateChild": "./src/animation_metadata", "animation": "./src/animation_metadata", "group": "./src/animation_metadata", "keyframes": "./src/animation_metadata", "query": "./src/animation_metadata", "sequence": "./src/animation_metadata", "stagger": "./src/animation_metadata", "state": "./src/animation_metadata", "style": "./src/animation_metadata", "transition": "./src/animation_metadata", "trigger": "./src/animation_metadata", "useAnimation": "./src/animation_metadata", "ɵStyleData": "./src/animation_metadata", "AnimationPlayer": "./src/players/animation_player", "NoopAnimationPlayer": "./src/players/animation_player", "ɵPRE_STYLE": "./src/private_export", "ɵAnimationGroupPlayer": "./src/players/animation_group_player"}, "importAs": "@angular/animations"}