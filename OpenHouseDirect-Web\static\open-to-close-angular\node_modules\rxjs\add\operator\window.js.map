{"version": 3, "file": "window.js", "sourceRoot": "", "sources": ["../../../src/add/operator/window.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,uBAAuB,uBAAuB,CAAC,CAAA;AAE/C,uBAAU,CAAC,SAAS,CAAC,MAAM,GAAG,eAAM,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { window } from '../../operator/window';\n\nObservable.prototype.window = window;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    window: typeof window;\n  }\n}"]}