{"version": 3, "file": "EmptyObservable.js", "sourceRoot": "", "sources": ["../../src/observable/EmptyObservable.ts"], "names": [], "mappings": ";;;;;;AAEA,2BAA2B,eAAe,CAAC,CAAA;AAO3C;;;;GAIG;AACH;IAAwC,mCAAa;IAsDnD,yBAAoB,SAAsB;QACxC,iBAAO,CAAC;QADU,cAAS,GAAT,SAAS,CAAa;IAE1C,CAAC;IAtDD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA0CG;IACI,sBAAM,GAAb,UAAiB,SAAsB;QACrC,MAAM,CAAC,IAAI,eAAe,CAAI,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEM,wBAAQ,GAAf,UAAmB,GAAmB;QAC5B,+BAAU,CAAS;QAC3B,UAAU,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAMD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAAyB;QAEvE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,sBAAU,EAAE,CAAC,CAAC;QACzE,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AApED,CAAwC,uBAAU,GAoEjD;AApEY,uBAAe,kBAoE3B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\nimport { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { TeardownLogic } from '../Subscription';\n\nexport interface DispatchArg<T> {\n  subscriber: Subscriber<T>;\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class EmptyObservable<T> extends Observable<T> {\n\n  /**\n   * Creates an Observable that emits no items to the Observer and immediately\n   * emits a complete notification.\n   *\n   * <span class=\"informal\">Just emits 'complete', and nothing else.\n   * </span>\n   *\n   * <img src=\"./img/empty.png\" width=\"100%\">\n   *\n   * This static operator is useful for creating a simple Observable that only\n   * emits the complete notification. It can be used for composing with other\n   * Observables, such as in a {@link mergeMap}.\n   *\n   * @example <caption>Emit the number 7, then complete.</caption>\n   * var result = Rx.Observable.empty().startWith(7);\n   * result.subscribe(x => console.log(x));\n   *\n   * @example <caption>Map and flatten only odd numbers to the sequence 'a', 'b', 'c'</caption>\n   * var interval = Rx.Observable.interval(1000);\n   * var result = interval.mergeMap(x =>\n   *   x % 2 === 1 ? Rx.Observable.of('a', 'b', 'c') : Rx.Observable.empty()\n   * );\n   * result.subscribe(x => console.log(x));\n   *\n   * // Results in the following to the console:\n   * // x is equal to the count on the interval eg(0,1,2,3,...)\n   * // x will occur every 1000ms\n   * // if x % 2 is equal to 1 print abc\n   * // if x % 2 is not equal to 1 nothing will be output\n   *\n   * @see {@link create}\n   * @see {@link never}\n   * @see {@link of}\n   * @see {@link throw}\n   *\n   * @param {Scheduler} [scheduler] A {@link IScheduler} to use for scheduling\n   * the emission of the complete notification.\n   * @return {Observable} An \"empty\" Observable: emits only the complete\n   * notification.\n   * @static true\n   * @name empty\n   * @owner Observable\n   */\n  static create<T>(scheduler?: IScheduler): Observable<T> {\n    return new EmptyObservable<T>(scheduler);\n  }\n\n  static dispatch<T>(arg: DispatchArg<T>) {\n    const { subscriber } = arg;\n    subscriber.complete();\n  }\n\n  constructor(private scheduler?: IScheduler) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n\n    const scheduler = this.scheduler;\n\n    if (scheduler) {\n      return scheduler.schedule(EmptyObservable.dispatch, 0, { subscriber });\n    } else {\n      subscriber.complete();\n    }\n  }\n}\n"]}