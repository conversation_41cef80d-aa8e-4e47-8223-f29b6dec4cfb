{"uuid": "fdecfc06-a944-4897-9530-e08f62883fc1", "parent": "37d58de4-deea-4808-bb77-d27685bd1501", "pid": 93248, "argv": ["/usr/local/bin/node", "/Users/<USER>/dev/js/tar/test/parse.js"], "execArgv": ["-r", "/usr/local/lib/node_modules/tap/node_modules/esm/esm.js"], "cwd": "/Users/<USER>/dev/js/tar", "time": 1557878802931, "ppid": 93238, "root": "e52f8603-1293-44df-8bfa-ed740bdd2b77", "coverageFilename": "/Users/<USER>/dev/js/tar/.nyc_output/fdecfc06-a944-4897-9530-e08f62883fc1.json", "externalId": "test/parse.js", "files": ["/Users/<USER>/dev/js/tar/lib/pack.js", "/Users/<USER>/dev/js/tar/lib/entry-writer.js", "/Users/<USER>/dev/js/tar/lib/entry.js", "/Users/<USER>/dev/js/tar/lib/global-header-writer.js", "/Users/<USER>/dev/js/tar/lib/parse.js", "/Users/<USER>/dev/js/tar/lib/buffer-entry.js", "/Users/<USER>/dev/js/tar/lib/extended-header.js", "/Users/<USER>/dev/js/tar/lib/extract.js"]}