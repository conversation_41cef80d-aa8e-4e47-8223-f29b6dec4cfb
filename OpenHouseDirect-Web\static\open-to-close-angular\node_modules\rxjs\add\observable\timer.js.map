{"version": 3, "file": "timer.js", "sourceRoot": "", "sources": ["../../../src/add/observable/timer.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { timer as staticTimer } from '../../observable/timer';\n\nObservable.timer = staticTimer;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let timer: typeof staticTimer;\n  }\n}"]}