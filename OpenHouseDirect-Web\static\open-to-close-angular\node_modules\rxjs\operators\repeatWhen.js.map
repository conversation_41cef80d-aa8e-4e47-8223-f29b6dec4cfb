{"version": 3, "file": "repeatWhen.js", "sourceRoot": "", "sources": ["../../src/operators/repeatWhen.ts"], "names": [], "mappings": ";;;;;;AAGA,wBAAwB,YAAY,CAAC,CAAA;AAErC,yBAAyB,kBAAkB,CAAC,CAAA;AAC5C,4BAA4B,qBAAqB,CAAC,CAAA;AAElD,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAI9D;;;;;;;;;;;;;GAaG;AACH,oBAA8B,QAA6D;IACzF,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC,EAA7C,CAA6C,CAAC;AAClF,CAAC;AAFe,kBAAU,aAEzB,CAAA;AAED;IACE,4BAAsB,QAA6D;QAA7D,aAAQ,GAAR,QAAQ,CAAqD;IACnF,CAAC;IAED,iCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;IACvF,CAAC;IACH,yBAAC;AAAD,CAAC,AAPD,IAOC;AAED;;;;GAIG;AACH;IAAyC,wCAAqB;IAO5D,8BAAY,WAA0B,EAClB,QAA6D,EAC7D,MAAqB;QACvC,kBAAM,WAAW,CAAC,CAAC;QAFD,aAAQ,GAAR,QAAQ,CAAqD;QAC7D,WAAM,GAAN,MAAM,CAAe;QAJjC,8BAAyB,GAAY,IAAI,CAAC;IAMlD,CAAC;IAED,yCAAU,GAAV,UAAW,UAAa,EAAE,UAAa,EAC5B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACxC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,6CAAc,GAAd,UAAe,QAA+B;QAC5C,EAAE,CAAC,CAAC,IAAI,CAAC,yBAAyB,KAAK,KAAK,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,gBAAK,CAAC,QAAQ,WAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,uCAAQ,GAAR;QACE,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;QAEvC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACpB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;gBAClB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,CAAC;YACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjE,MAAM,CAAC,gBAAK,CAAC,QAAQ,WAAE,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,oCAAoC,CAAC,2CAAY,GAAZ;QACnC,IAAA,SAAmD,EAA3C,gCAAa,EAAE,4CAAmB,CAAU;QACpD,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YAClB,aAAa,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;QACD,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACxB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,oCAAoC,CAAC,qDAAsB,GAAtB;QACnC,IAAA,SAA4D,EAApD,gCAAa,EAAE,oBAAO,EAAE,4CAAmB,CAAU;QAC7D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,gBAAK,CAAC,sBAAsB,WAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IAEO,iDAAkB,GAA1B;QACE,IAAI,CAAC,aAAa,GAAG,IAAI,iBAAO,EAAE,CAAC;QACnC,IAAM,OAAO,GAAG,mBAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5D,EAAE,CAAC,CAAC,OAAO,KAAK,yBAAW,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,gBAAK,CAAC,QAAQ,WAAE,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,mBAAmB,GAAG,qCAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IACH,2BAAC;AAAD,CAAC,AA5ED,CAAyC,iCAAe,GA4EvD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { Subscription, TeardownLogic } from '../Subscription';\nimport { tryCatch } from '../util/tryCatch';\nimport { errorObject } from '../util/errorObject';\n\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\n\nimport { MonoTypeOperatorFunction } from '../interfaces';\n\n/**\n * Returns an Observable that mirrors the source Observable with the exception of a `complete`. If the source\n * Observable calls `complete`, this method will emit to the Observable returned from `notifier`. If that Observable\n * calls `complete` or `error`, then this method will call `complete` or `error` on the child subscription. Otherwise\n * this method will resubscribe to the source Observable.\n *\n * <img src=\"./img/repeatWhen.png\" width=\"100%\">\n *\n * @param {function(notifications: Observable): Observable} notifier - Receives an Observable of notifications with\n * which a user can `complete` or `error`, aborting the repetition.\n * @return {Observable} The source Observable modified with repeat logic.\n * @method repeatWhen\n * @owner Observable\n */\nexport function repeatWhen<T>(notifier: (notifications: Observable<any>) => Observable<any>): MonoTypeOperatorFunction<T> {\n  return (source: Observable<T>) => source.lift(new RepeatWhenOperator(notifier));\n}\n\nclass RepeatWhenOperator<T> implements Operator<T, T> {\n  constructor(protected notifier: (notifications: Observable<any>) => Observable<any>) {\n  }\n\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n    return source.subscribe(new RepeatWhenSubscriber(subscriber, this.notifier, source));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass RepeatWhenSubscriber<T, R> extends OuterSubscriber<T, R> {\n\n  private notifications: Subject<any>;\n  private retries: Observable<any>;\n  private retriesSubscription: Subscription;\n  private sourceIsBeingSubscribedTo: boolean = true;\n\n  constructor(destination: Subscriber<R>,\n              private notifier: (notifications: Observable<any>) => Observable<any>,\n              private source: Observable<T>) {\n    super(destination);\n  }\n\n  notifyNext(outerValue: T, innerValue: R,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, R>): void {\n    this.sourceIsBeingSubscribedTo = true;\n    this.source.subscribe(this);\n  }\n\n  notifyComplete(innerSub: InnerSubscriber<T, R>): void {\n    if (this.sourceIsBeingSubscribedTo === false) {\n      return super.complete();\n    }\n  }\n\n  complete() {\n    this.sourceIsBeingSubscribedTo = false;\n\n    if (!this.isStopped) {\n      if (!this.retries) {\n        this.subscribeToRetries();\n      }\n      if (!this.retriesSubscription || this.retriesSubscription.closed) {\n        return super.complete();\n      }\n\n      this._unsubscribeAndRecycle();\n      this.notifications.next();\n    }\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    const { notifications, retriesSubscription } = this;\n    if (notifications) {\n      notifications.unsubscribe();\n      this.notifications = null;\n    }\n    if (retriesSubscription) {\n      retriesSubscription.unsubscribe();\n      this.retriesSubscription = null;\n    }\n    this.retries = null;\n  }\n\n  /** @deprecated internal use only */ _unsubscribeAndRecycle(): Subscriber<T> {\n    const { notifications, retries, retriesSubscription } = this;\n    this.notifications = null;\n    this.retries = null;\n    this.retriesSubscription = null;\n    super._unsubscribeAndRecycle();\n    this.notifications = notifications;\n    this.retries = retries;\n    this.retriesSubscription = retriesSubscription;\n    return this;\n  }\n\n  private subscribeToRetries() {\n    this.notifications = new Subject();\n    const retries = tryCatch(this.notifier)(this.notifications);\n    if (retries === errorObject) {\n      return super.complete();\n    }\n    this.retries = retries;\n    this.retriesSubscription = subscribeToResult(this, retries);\n  }\n}\n"]}