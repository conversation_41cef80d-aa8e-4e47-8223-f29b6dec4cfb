{"version": 3, "file": "animations.js", "sources": ["../../../packages/animations/esm5/src/animation_builder.js", "../../../packages/animations/esm5/src/animation_metadata.js", "../../../packages/animations/esm5/src/util.js", "../../../packages/animations/esm5/src/players/animation_player.js", "../../../packages/animations/esm5/src/players/animation_group_player.js", "../../../packages/animations/esm5/src/private_export.js", "../../../packages/animations/esm5/src/animations.js", "../../../packages/animations/esm5/public_api.js", "../../../packages/animations/esm5/animations.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * AnimationBuilder is an injectable service that is available when the {\\@link\n * BrowserAnimationsModule BrowserAnimationsModule} or {\\@link NoopAnimationsModule\n * NoopAnimationsModule} modules are used within an application.\n *\n * The purpose if this service is to produce an animation sequence programmatically within an\n * angular component or directive.\n *\n * Programmatic animations are first built and then a player is created when the build animation is\n * attached to an element.\n *\n * ```ts\n * // remember to include the BrowserAnimationsModule module for this to work...\n * import {AnimationBuilder} from '\\@angular/animations';\n *\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first build the animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // then create a player from it\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * When an animation is built an instance of {\\@link AnimationFactory AnimationFactory} will be\n * returned. Using that an {\\@link AnimationPlayer AnimationPlayer} can be created which can then be\n * used to start the animation.\n *\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nvar /**\n * AnimationBuilder is an injectable service that is available when the {\\@link\n * BrowserAnimationsModule BrowserAnimationsModule} or {\\@link NoopAnimationsModule\n * NoopAnimationsModule} modules are used within an application.\n *\n * The purpose if this service is to produce an animation sequence programmatically within an\n * angular component or directive.\n *\n * Programmatic animations are first built and then a player is created when the build animation is\n * attached to an element.\n *\n * ```ts\n * // remember to include the BrowserAnimationsModule module for this to work...\n * import {AnimationBuilder} from '\\@angular/animations';\n *\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first build the animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // then create a player from it\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * When an animation is built an instance of {\\@link AnimationFactory AnimationFactory} will be\n * returned. Using that an {\\@link AnimationPlayer AnimationPlayer} can be created which can then be\n * used to start the animation.\n *\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nAnimationBuilder = /** @class */ (function () {\n    function AnimationBuilder() {\n    }\n    return AnimationBuilder;\n}());\n/**\n * AnimationBuilder is an injectable service that is available when the {\\@link\n * BrowserAnimationsModule BrowserAnimationsModule} or {\\@link NoopAnimationsModule\n * NoopAnimationsModule} modules are used within an application.\n *\n * The purpose if this service is to produce an animation sequence programmatically within an\n * angular component or directive.\n *\n * Programmatic animations are first built and then a player is created when the build animation is\n * attached to an element.\n *\n * ```ts\n * // remember to include the BrowserAnimationsModule module for this to work...\n * import {AnimationBuilder} from '\\@angular/animations';\n *\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first build the animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // then create a player from it\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * When an animation is built an instance of {\\@link AnimationFactory AnimationFactory} will be\n * returned. Using that an {\\@link AnimationPlayer AnimationPlayer} can be created which can then be\n * used to start the animation.\n *\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport { AnimationBuilder };\nfunction AnimationBuilder_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?} animation\n     * @return {?}\n     */\n    AnimationBuilder.prototype.build = function (animation) { };\n}\n/**\n * An instance of `AnimationFactory` is returned from {\\@link AnimationBuilder#build\n * AnimationBuilder.build}.\n *\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nvar /**\n * An instance of `AnimationFactory` is returned from {\\@link AnimationBuilder#build\n * AnimationBuilder.build}.\n *\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nAnimationFactory = /** @class */ (function () {\n    function AnimationFactory() {\n    }\n    return AnimationFactory;\n}());\n/**\n * An instance of `AnimationFactory` is returned from {\\@link AnimationBuilder#build\n * AnimationBuilder.build}.\n *\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nexport { AnimationFactory };\nfunction AnimationFactory_tsickle_Closure_declarations() {\n    /**\n     * @abstract\n     * @param {?} element\n     * @param {?=} options\n     * @return {?}\n     */\n    AnimationFactory.prototype.create = function (element, options) { };\n}\n//# sourceMappingURL=animation_builder.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @record\n */\nexport function ɵStyleData() { }\nfunction ɵStyleData_tsickle_Closure_declarations() {\n    /* TODO: handle strange member:\n    [key: string]: string|number;\n    */\n}\n/** @enum {number} */\nvar AnimationMetadataType = {\n    State: 0,\n    Transition: 1,\n    Sequence: 2,\n    Group: 3,\n    Animate: 4,\n    Keyframes: 5,\n    Style: 6,\n    Trigger: 7,\n    Reference: 8,\n    AnimateChild: 9,\n    AnimateRef: 10,\n    Query: 11,\n    Stagger: 12,\n};\nexport { AnimationMetadataType };\n/**\n * \\@experimental Animation support is experimental.\n */\nexport var /** @type {?} */ AUTO_STYLE = '*';\n/**\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationMetadata() { }\nfunction AnimationMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationMetadata.prototype.type;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link trigger trigger animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationTriggerMetadata() { }\nfunction AnimationTriggerMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTriggerMetadata.prototype.name;\n    /** @type {?} */\n    AnimationTriggerMetadata.prototype.definitions;\n    /** @type {?} */\n    AnimationTriggerMetadata.prototype.options;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link state state animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationStateMetadata() { }\nfunction AnimationStateMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationStateMetadata.prototype.name;\n    /** @type {?} */\n    AnimationStateMetadata.prototype.styles;\n    /** @type {?|undefined} */\n    AnimationStateMetadata.prototype.options;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link transition transition animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationTransitionMetadata() { }\nfunction AnimationTransitionMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationTransitionMetadata.prototype.expr;\n    /** @type {?} */\n    AnimationTransitionMetadata.prototype.animation;\n    /** @type {?} */\n    AnimationTransitionMetadata.prototype.options;\n}\n/**\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationReferenceMetadata() { }\nfunction AnimationReferenceMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationReferenceMetadata.prototype.animation;\n    /** @type {?} */\n    AnimationReferenceMetadata.prototype.options;\n}\n/**\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationQueryMetadata() { }\nfunction AnimationQueryMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationQueryMetadata.prototype.selector;\n    /** @type {?} */\n    AnimationQueryMetadata.prototype.animation;\n    /** @type {?} */\n    AnimationQueryMetadata.prototype.options;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link keyframes keyframes animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationKeyframesSequenceMetadata() { }\nfunction AnimationKeyframesSequenceMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationKeyframesSequenceMetadata.prototype.steps;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link style style animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationStyleMetadata() { }\nfunction AnimationStyleMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationStyleMetadata.prototype.styles;\n    /** @type {?} */\n    AnimationStyleMetadata.prototype.offset;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link animate animate animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationAnimateMetadata() { }\nfunction AnimationAnimateMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationAnimateMetadata.prototype.timings;\n    /** @type {?} */\n    AnimationAnimateMetadata.prototype.styles;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link animateChild animateChild animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationAnimateChildMetadata() { }\nfunction AnimationAnimateChildMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationAnimateChildMetadata.prototype.options;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link useAnimation useAnimation animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationAnimateRefMetadata() { }\nfunction AnimationAnimateRefMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationAnimateRefMetadata.prototype.animation;\n    /** @type {?} */\n    AnimationAnimateRefMetadata.prototype.options;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link sequence sequence animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationSequenceMetadata() { }\nfunction AnimationSequenceMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationSequenceMetadata.prototype.steps;\n    /** @type {?} */\n    AnimationSequenceMetadata.prototype.options;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link group group animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationGroupMetadata() { }\nfunction AnimationGroupMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationGroupMetadata.prototype.steps;\n    /** @type {?} */\n    AnimationGroupMetadata.prototype.options;\n}\n/**\n * Metadata representing the entry of animations. Instances of this interface are provided via the\n * animation DSL when the {\\@link stagger stagger animation function} is called.\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationStaggerMetadata() { }\nfunction AnimationStaggerMetadata_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationStaggerMetadata.prototype.timings;\n    /** @type {?} */\n    AnimationStaggerMetadata.prototype.animation;\n}\n/**\n * `trigger` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the\n * {\\@link Component#animations component animations metadata page} to gain a better\n * understanding of how animations in Angular are used.\n *\n * `trigger` Creates an animation trigger which will a list of {\\@link state state} and\n * {\\@link transition transition} entries that will be evaluated when the expression\n * bound to the trigger changes.\n *\n * Triggers are registered within the component annotation data under the\n * {\\@link Component#animations animations section}. An animation trigger can be placed on an element\n * within a template by referencing the name of the trigger followed by the expression value that\n * the\n * trigger is bound to (in the form of `[\\@triggerName]=\"expression\"`.\n *\n * Animation trigger bindings strigify values and then match the previous and current values against\n * any linked transitions. If a boolean value is provided into the trigger binding then it will both\n * be represented as `1` or `true` and `0` or `false` for a true and false boolean values\n * respectively.\n *\n * ### Usage\n *\n * `trigger` will create an animation trigger reference based on the provided `name` value. The\n * provided `animation` value is expected to be an array consisting of {\\@link state state} and\n * {\\@link transition transition} declarations.\n *\n * ```typescript\n * \\@Component({\n *   selector: 'my-component',\n *   templateUrl: 'my-component-tpl.html',\n *   animations: [\n *     trigger(\"myAnimationTrigger\", [\n *       state(...),\n *       state(...),\n *       transition(...),\n *       transition(...)\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"something\";\n * }\n * ```\n *\n * The template associated with this component will make use of the `myAnimationTrigger` animation\n * trigger by binding to an element within its template code.\n *\n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [\\@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * ## Disable Animations\n * A special animation control binding called `\\@.disabled` can be placed on an element which will\n * then disable animations for any inner animation triggers situated within the element as well as\n * any animations on the element itself.\n *\n * When true, the `\\@.disabled` binding will prevent all animations from rendering. The example\n * below shows how to use this feature:\n *\n * ```ts\n * \\@Component({\n *   selector: 'my-component',\n *   template: `\n *     <div [\\@.disabled]=\"isDisabled\">\n *       <div [\\@childAnimation]=\"exp\"></div>\n *     </div>\n *   `,\n *   animations: [\n *     trigger(\"childAnimation\", [\n *       // ...\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   isDisabled = true;\n *   exp = '...';\n * }\n * ```\n *\n * The `\\@childAnimation` trigger will not animate because `\\@.disabled` prevents it from happening\n * (when true).\n *\n * Note that `\\@.disabled` will only disable all animations (this means any animations running on\n * the same element will also be disabled).\n *\n * ### Disabling Animations Application-wide\n * When an area of the template is set to have animations disabled, **all** inner components will\n * also have their animations disabled as well. This means that all animations for an angular\n * application can be disabled by placing a host binding set on `\\@.disabled` on the topmost Angular\n * component.\n *\n * ```ts\n * import {Component, HostBinding} from '\\@angular/core';\n *\n * \\@Component({\n *   selector: 'app-component',\n *   templateUrl: 'app.component.html',\n * })\n * class AppComponent {\n *   \\@HostBinding('\\@.disabled')\n *   public animationsDisabled = true;\n * }\n * ```\n *\n * ### What about animations that us `query()` and `animateChild()`?\n * Despite inner animations being disabled, a parent animation can {\\@link query query} for inner\n * elements located in disabled areas of the template and still animate them as it sees fit. This is\n * also the case for when a sub animation is queried by a parent and then later animated using {\\@link\n * animateChild animateChild}.\n *\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} definitions\n * @return {?}\n */\nexport function trigger(name, definitions) {\n    return { type: 7 /* Trigger */, name: name, definitions: definitions, options: {} };\n}\n/**\n * `animate` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n *\n * `animate` specifies an animation step that will apply the provided `styles` data for a given\n * amount of time based on the provided `timing` expression value. Calls to `animate` are expected\n * to be used within {\\@link sequence an animation sequence}, {\\@link group group}, or {\\@link\n * transition transition}.\n *\n * ### Usage\n *\n * The `animate` function accepts two input parameters: `timing` and `styles`:\n *\n * - `timing` is a string based value that can be a combination of a duration with optional delay\n * and easing values. The format for the expression breaks down to `duration delay easing`\n * (therefore a value such as `1s 100ms ease-out` will be parse itself into `duration=1000,\n * delay=100, easing=ease-out`. If a numeric value is provided then that will be used as the\n * `duration` value in millisecond form.\n * - `styles` is the style input data which can either be a call to {\\@link style style} or {\\@link\n * keyframes keyframes}. If left empty then the styles from the destination state will be collected\n * and used (this is useful when describing an animation step that will complete an animation by\n * {\\@link transition#the-final-animate-call animating to the final state}).\n *\n * ```typescript\n * // various functions for specifying timing data\n * animate(500, style(...))\n * animate(\"1s\", style(...))\n * animate(\"100ms 0.5s\", style(...))\n * animate(\"5s ease\", style(...))\n * animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\", style(...))\n *\n * // either style() of keyframes() can be used\n * animate(500, style({ background: \"red\" }))\n * animate(500, keyframes([\n *   style({ background: \"blue\" })),\n *   style({ background: \"red\" }))\n * ])\n * ```\n *\n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n *\n * \\@experimental Animation support is experimental.\n * @param {?} timings\n * @param {?=} styles\n * @return {?}\n */\nexport function animate(timings, styles) {\n    if (styles === void 0) { styles = null; }\n    return { type: 4 /* Animate */, styles: styles, timings: timings };\n}\n/**\n * `group` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n *\n * `group` specifies a list of animation steps that are all run in parallel. Grouped animations are\n * useful when a series of styles must be animated/closed off at different starting/ending times.\n *\n * The `group` function can either be used within a {\\@link sequence sequence} or a {\\@link transition\n * transition} and it will only continue to the next instruction once all of the inner animation\n * steps have completed.\n *\n * ### Usage\n *\n * The `steps` data that is passed into the `group` animation function can either consist of {\\@link\n * style style} or {\\@link animate animate} function calls. Each call to `style()` or `animate()`\n * within a group will be executed instantly (use {\\@link keyframes keyframes} or a {\\@link\n * animate#usage animate() with a delay value} to offset styles to be applied at a later time).\n *\n * ```typescript\n * group([\n *   animate(\"1s\", { background: \"black\" }))\n *   animate(\"2s\", { color: \"white\" }))\n * ])\n * ```\n *\n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n *\n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function group(steps, options) {\n    if (options === void 0) { options = null; }\n    return { type: 3 /* Group */, steps: steps, options: options };\n}\n/**\n * `sequence` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n *\n * `sequence` Specifies a list of animation steps that are run one by one. (`sequence` is used by\n * default when an array is passed as animation data into {\\@link transition transition}.)\n *\n * The `sequence` function can either be used within a {\\@link group group} or a {\\@link transition\n * transition} and it will only continue to the next instruction once each of the inner animation\n * steps have completed.\n *\n * To perform animation styling in parallel with other animation steps then have a look at the\n * {\\@link group group} animation function.\n *\n * ### Usage\n *\n * The `steps` data that is passed into the `sequence` animation function can either consist of\n * {\\@link style style} or {\\@link animate animate} function calls. A call to `style()` will apply the\n * provided styling data immediately while a call to `animate()` will apply its styling data over a\n * given time depending on its timing data.\n *\n * ```typescript\n * sequence([\n *   style({ opacity: 0 })),\n *   animate(\"1s\", { opacity: 1 }))\n * ])\n * ```\n *\n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n *\n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function sequence(steps, options) {\n    if (options === void 0) { options = null; }\n    return { type: 2 /* Sequence */, steps: steps, options: options };\n}\n/**\n * `style` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n *\n * `style` declares a key/value object containing CSS properties/styles that can then be used for\n * {\\@link state animation states}, within an {\\@link sequence animation sequence}, or as styling data\n * for both {\\@link animate animate} and {\\@link keyframes keyframes}.\n *\n * ### Usage\n *\n * `style` takes in a key/value string map as data and expects one or more CSS property/value pairs\n * to be defined.\n *\n * ```typescript\n * // string values are used for css properties\n * style({ background: \"red\", color: \"blue\" })\n *\n * // numerical (pixel) values are also supported\n * style({ width: 100, height: 0 })\n * ```\n *\n * #### Auto-styles (using `*`)\n *\n * When an asterix (`*`) character is used as a value then it will be detected from the element\n * being animated and applied as animation data when the animation starts.\n *\n * This feature proves useful for a state depending on layout and/or environment factors; in such\n * cases the styles are calculated just before the animation starts.\n *\n * ```typescript\n * // the steps below will animate from 0 to the\n * // actual height of the element\n * style({ height: 0 }),\n * animate(\"1s\", style({ height: \"*\" }))\n * ```\n *\n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n *\n * \\@experimental Animation support is experimental.\n * @param {?} tokens\n * @return {?}\n */\nexport function style(tokens) {\n    return { type: 6 /* Style */, styles: tokens, offset: null };\n}\n/**\n * `state` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n *\n * `state` declares an animation state within the given trigger. When a state is active within a\n * component then its associated styles will persist on the element that the trigger is attached to\n * (even when the animation ends).\n *\n * To animate between states, have a look at the animation {\\@link transition transition} DSL\n * function. To register states to an animation trigger please have a look at the {\\@link trigger\n * trigger} function.\n *\n * #### The `void` state\n *\n * The `void` state value is a reserved word that angular uses to determine when the element is not\n * apart of the application anymore (e.g. when an `ngIf` evaluates to false then the state of the\n * associated element is void).\n *\n * #### The `*` (default) state\n *\n * The `*` state (when styled) is a fallback state that will be used if the state that is being\n * animated is not declared within the trigger.\n *\n * ### Usage\n *\n * `state` will declare an animation state with its associated styles\n * within the given trigger.\n *\n * - `stateNameExpr` can be one or more state names separated by commas.\n * - `styles` refers to the {\\@link style styling data} that will be persisted on the element once\n * the state has been reached.\n *\n * ```typescript\n * // \"void\" is a reserved name for a state and is used to represent\n * // the state in which an element is detached from from the application.\n * state(\"void\", style({ height: 0 }))\n *\n * // user-defined states\n * state(\"closed\", style({ height: 0 }))\n * state(\"open, visible\", style({ height: \"*\" }))\n * ```\n *\n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n *\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} styles\n * @param {?=} options\n * @return {?}\n */\nexport function state(name, styles, options) {\n    return { type: 0 /* State */, name: name, styles: styles, options: options };\n}\n/**\n * `keyframes` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n *\n * `keyframes` specifies a collection of {\\@link style style} entries each optionally characterized\n * by an `offset` value.\n *\n * ### Usage\n *\n * The `keyframes` animation function is designed to be used alongside the {\\@link animate animate}\n * animation function. Instead of applying animations from where they are currently to their\n * destination, keyframes can describe how each style entry is applied and at what point within the\n * animation arc (much like CSS Keyframe Animations do).\n *\n * For each `style()` entry an `offset` value can be set. Doing so allows to specifiy at what\n * percentage of the animate time the styles will be applied.\n *\n * ```typescript\n * // the provided offset values describe when each backgroundColor value is applied.\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\", offset: 0 }),\n *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n *   style({ backgroundColor: \"black\", offset: 1 })\n * ]))\n * ```\n *\n * Alternatively, if there are no `offset` values used within the style entries then the offsets\n * will be calculated automatically.\n *\n * ```typescript\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\" }) // offset = 0\n *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n *   style({ backgroundColor: \"black\" }) // offset = 1\n * ]))\n * ```\n *\n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n *\n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @return {?}\n */\nexport function keyframes(steps) {\n    return { type: 5 /* Keyframes */, steps: steps };\n}\n/**\n * `transition` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. If this information is new, please navigate to the {\\@link\n * Component#animations component animations metadata page} to gain a better understanding of\n * how animations in Angular are used.\n *\n * `transition` declares the {\\@link sequence sequence of animation steps} that will be run when the\n * provided `stateChangeExpr` value is satisfied. The `stateChangeExpr` consists of a `state1 =>\n * state2` which consists of two known states (use an asterix (`*`) to refer to a dynamic starting\n * and/or ending state).\n *\n * A function can also be provided as the `stateChangeExpr` argument for a transition and this\n * function will be executed each time a state change occurs. If the value returned within the\n * function is true then the associated animation will be run.\n *\n * Animation transitions are placed within an {\\@link trigger animation trigger}. For an transition\n * to animate to a state value and persist its styles then one or more {\\@link state animation\n * states} is expected to be defined.\n *\n * ### Usage\n *\n * An animation transition is kicked off the `stateChangeExpr` predicate evaluates to true based on\n * what the previous state is and what the current state has become. In other words, if a transition\n * is defined that matches the old/current state criteria then the associated animation will be\n * triggered.\n *\n * ```typescript\n * // all transition/state changes are defined within an animation trigger\n * trigger(\"myAnimationTrigger\", [\n *   // if a state is defined then its styles will be persisted when the\n *   // animation has fully completed itself\n *   state(\"on\", style({ background: \"green\" })),\n *   state(\"off\", style({ background: \"grey\" })),\n *\n *   // a transition animation that will be kicked off when the state value\n *   // bound to \"myAnimationTrigger\" changes from \"on\" to \"off\"\n *   transition(\"on => off\", animate(500)),\n *\n *   // it is also possible to do run the same animation for both directions\n *   transition(\"on <=> off\", animate(500)),\n *\n *   // or to define multiple states pairs separated by commas\n *   transition(\"on => off, off => void\", animate(500)),\n *\n *   // this is a catch-all state change for when an element is inserted into\n *   // the page and the destination state is unknown\n *   transition(\"void => *\", [\n *     style({ opacity: 0 }),\n *     animate(500)\n *   ]),\n *\n *   // this will capture a state change between any states\n *   transition(\"* => *\", animate(\"1s 0s\")),\n *\n *   // you can also go full out and include a function\n *   transition((fromState, toState) => {\n *     // when `true` then it will allow the animation below to be invoked\n *     return fromState == \"off\" && toState == \"on\";\n *   }, animate(\"1s 0s\"))\n * ])\n * ```\n *\n * The template associated with this component will make use of the `myAnimationTrigger` animation\n * trigger by binding to an element within its template code.\n *\n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [\\@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * #### The final `animate` call\n *\n * If the final step within the transition steps is a call to `animate()` that **only** uses a\n * timing value with **no style data** then it will be automatically used as the final animation arc\n * for the element to animate itself to the final state. This involves an automatic mix of\n * adding/removing CSS styles so that the element will be in the exact state it should be for the\n * applied state to be presented correctly.\n *\n * ```\n * // start off by hiding the element, but make sure that it animates properly to whatever state\n * // is currently active for \"myAnimationTrigger\"\n * transition(\"void => *\", [\n *   style({ opacity: 0 }),\n *   animate(500)\n * ])\n * ```\n *\n * ### Using :enter and :leave\n *\n * Given that enter (insertion) and leave (removal) animations are so common, the `transition`\n * function accepts both `:enter` and `:leave` values which are aliases for the `void => *` and `*\n * => void` state changes.\n *\n * ```\n * transition(\":enter\", [\n *   style({ opacity: 0 }),\n *   animate(500, style({ opacity: 1 }))\n * ]),\n * transition(\":leave\", [\n *   animate(500, style({ opacity: 0 }))\n * ])\n * ```\n *\n * ### Boolean values\n * if a trigger binding value is a boolean value then it can be matched using a transition\n * expression that compares `true` and `false` or `1` and `0`.\n *\n * ```\n * // in the template\n * <div [\\@openClose]=\"open ? true : false\">...</div>\n *\n * // in the component metadata\n * trigger('openClose', [\n *   state('true', style({ height: '*' })),\n *   state('false', style({ height: '0px' })),\n *   transition('false <=> true', animate(500))\n * ])\n * ```\n *\n * ### Using :increment and :decrement\n * In addition to the :enter and :leave transition aliases, the :increment and :decrement aliases\n * can be used to kick off a transition when a numeric value has increased or decreased in value.\n *\n * ```\n * import {group, animate, query, transition, style, trigger} from '\\@angular/animations';\n * import {Component} from '\\@angular/core';\n *\n * \\@Component({\n *   selector: 'banner-carousel-component',\n *   styles: [`\n *     .banner-container {\n *        position:relative;\n *        height:500px;\n *        overflow:hidden;\n *      }\n *     .banner-container > .banner {\n *        position:absolute;\n *        left:0;\n *        top:0;\n *        font-size:200px;\n *        line-height:500px;\n *        font-weight:bold;\n *        text-align:center;\n *        width:100%;\n *      }\n *   `],\n *   template: `\n *     <button (click)=\"previous()\">Previous</button>\n *     <button (click)=\"next()\">Next</button>\n *     <hr>\n *     <div [\\@bannerAnimation]=\"selectedIndex\" class=\"banner-container\">\n *       <div class=\"banner\" *ngFor=\"let banner of banners\"> {{ banner }} </div>\n *     </div>\n *   `,\n *   animations: [\n *     trigger('bannerAnimation', [\n *       transition(\":increment\", group([\n *         query(':enter', [\n *           style({ left: '100%' }),\n *           animate('0.5s ease-out', style('*'))\n *         ]),\n *         query(':leave', [\n *           animate('0.5s ease-out', style({ left: '-100%' }))\n *         ])\n *       ])),\n *       transition(\":decrement\", group([\n *         query(':enter', [\n *           style({ left: '-100%' }),\n *           animate('0.5s ease-out', style('*'))\n *         ]),\n *         query(':leave', [\n *           animate('0.5s ease-out', style({ left: '100%' }))\n *         ])\n *       ]))\n *     ])\n *   ]\n * })\n * class BannerCarouselComponent {\n *   allBanners: string[] = ['1', '2', '3', '4'];\n *   selectedIndex: number = 0;\n *\n *   get banners() {\n *      return [this.allBanners[this.selectedIndex]];\n *   }\n *\n *   previous() {\n *     this.selectedIndex = Math.max(this.selectedIndex - 1, 0);\n *   }\n *\n *   next() {\n *     this.selectedIndex = Math.min(this.selectedIndex + 1, this.allBanners.length - 1);\n *   }\n * }\n * ```\n *\n * {\\@example core/animation/ts/dsl/animation_example.ts region='Component'}\n *\n * \\@experimental Animation support is experimental.\n * @param {?} stateChangeExpr\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function transition(stateChangeExpr, steps, options) {\n    if (options === void 0) { options = null; }\n    return { type: 1 /* Transition */, expr: stateChangeExpr, animation: steps, options: options };\n}\n/**\n * `animation` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language.\n *\n * `var myAnimation = animation(...)` is designed to produce a reusable animation that can be later\n * invoked in another animation or sequence. Reusable animations are designed to make use of\n * animation parameters and the produced animation can be used via the `useAnimation` method.\n *\n * ```\n * var fadeAnimation = animation([\n *   style({ opacity: '{{ start }}' }),\n *   animate('{{ time }}',\n *     style({ opacity: '{{ end }}'}))\n * ], { params: { time: '1000ms', start: 0, end: 1 }});\n * ```\n *\n * If parameters are attached to an animation then they act as **default parameter values**. When an\n * animation is invoked via `useAnimation` then parameter values are allowed to be passed in\n * directly. If any of the passed in parameter values are missing then the default values will be\n * used.\n *\n * ```\n * useAnimation(fadeAnimation, {\n *   params: {\n *     time: '2s',\n *     start: 1,\n *     end: 0\n *   }\n * })\n * ```\n *\n * If one or more parameter values are missing before animated then an error will be thrown.\n *\n * \\@experimental Animation support is experimental.\n * @param {?} steps\n * @param {?=} options\n * @return {?}\n */\nexport function animation(steps, options) {\n    if (options === void 0) { options = null; }\n    return { type: 8 /* Reference */, animation: steps, options: options };\n}\n/**\n * `animateChild` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. It works by allowing a queried element to execute its own\n * animation within the animation sequence.\n *\n * Each time an animation is triggered in angular, the parent animation\n * will always get priority and any child animations will be blocked. In order\n * for a child animation to run, the parent animation must query each of the elements\n * containing child animations and then allow the animations to run using `animateChild`.\n *\n * The example HTML code below shows both parent and child elements that have animation\n * triggers that will execute at the same time.\n *\n * ```html\n * <!-- parent-child.component.html -->\n * <button (click)=\"exp =! exp\">Toggle</button>\n * <hr>\n *\n * <div [\\@parentAnimation]=\"exp\">\n *   <header>Hello</header>\n *   <div [\\@childAnimation]=\"exp\">\n *       one\n *   </div>\n *   <div [\\@childAnimation]=\"exp\">\n *       two\n *   </div>\n *   <div [\\@childAnimation]=\"exp\">\n *       three\n *   </div>\n * </div>\n * ```\n *\n * Now when the `exp` value changes to true, only the `parentAnimation` animation will animate\n * because it has priority. However, using `query` and `animateChild` each of the inner animations\n * can also fire:\n *\n * ```ts\n * // parent-child.component.ts\n * import {trigger, transition, animate, style, query, animateChild} from '\\@angular/animations';\n * \\@Component({\n *   selector: 'parent-child-component',\n *   animations: [\n *     trigger('parentAnimation', [\n *       transition('false => true', [\n *         query('header', [\n *           style({ opacity: 0 }),\n *           animate(500, style({ opacity: 1 }))\n *         ]),\n *         query('\\@childAnimation', [\n *           animateChild()\n *         ])\n *       ])\n *     ]),\n *     trigger('childAnimation', [\n *       transition('false => true', [\n *         style({ opacity: 0 }),\n *         animate(500, style({ opacity: 1 }))\n *       ])\n *     ])\n *   ]\n * })\n * class ParentChildCmp {\n *   exp: boolean = false;\n * }\n * ```\n *\n * In the animation code above, when the `parentAnimation` transition kicks off it first queries to\n * find the header element and fades it in. It then finds each of the sub elements that contain the\n * `\\@childAnimation` trigger and then allows for their animations to fire.\n *\n * This example can be further extended by using stagger:\n *\n * ```ts\n * query('\\@childAnimation', stagger(100, [\n *   animateChild()\n * ]))\n * ```\n *\n * Now each of the sub animations start off with respect to the `100ms` staggering step.\n *\n * ## The first frame of child animations\n * When sub animations are executed using `animateChild` the animation engine will always apply the\n * first frame of every sub animation immediately at the start of the animation sequence. This way\n * the parent animation does not need to set any initial styling data on the sub elements before the\n * sub animations kick off.\n *\n * In the example above the first frame of the `childAnimation`'s `false => true` transition\n * consists of a style of `opacity: 0`. This is applied immediately when the `parentAnimation`\n * animation transition sequence starts. Only then when the `\\@childAnimation` is queried and called\n * with `animateChild` will it then animate to its destination of `opacity: 1`.\n *\n * Note that this feature designed to be used alongside {\\@link query query()} and it will only work\n * with animations that are assigned using the Angular animation DSL (this means that CSS keyframes\n * and transitions are not handled by this API).\n *\n * \\@experimental Animation support is experimental.\n * @param {?=} options\n * @return {?}\n */\nexport function animateChild(options) {\n    if (options === void 0) { options = null; }\n    return { type: 9 /* AnimateChild */, options: options };\n}\n/**\n * `useAnimation` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. It is used to kick off a reusable animation that is created using {\\@link\n * animation animation()}.\n *\n * \\@experimental Animation support is experimental.\n * @param {?} animation\n * @param {?=} options\n * @return {?}\n */\nexport function useAnimation(animation, options) {\n    if (options === void 0) { options = null; }\n    return { type: 10 /* AnimateRef */, animation: animation, options: options };\n}\n/**\n * `query` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language.\n *\n * query() is used to find one or more inner elements within the current element that is\n * being animated within the sequence. The provided animation steps are applied\n * to the queried element (by default, an array is provided, then this will be\n * treated as an animation sequence).\n *\n * ### Usage\n *\n * query() is designed to collect mutiple elements and works internally by using\n * `element.querySelectorAll`. An additional options object can be provided which\n * can be used to limit the total amount of items to be collected.\n *\n * ```js\n * query('div', [\n *   animate(...),\n *   animate(...)\n * ], { limit: 1 })\n * ```\n *\n * query(), by default, will throw an error when zero items are found. If a query\n * has the `optional` flag set to true then this error will be ignored.\n *\n * ```js\n * query('.some-element-that-may-not-be-there', [\n *   animate(...),\n *   animate(...)\n * ], { optional: true })\n * ```\n *\n * ### Special Selector Values\n *\n * The selector value within a query can collect elements that contain angular-specific\n * characteristics\n * using special pseudo-selectors tokens.\n *\n * These include:\n *\n *  - Querying for newly inserted/removed elements using `query(\":enter\")`/`query(\":leave\")`\n *  - Querying all currently animating elements using `query(\":animating\")`\n *  - Querying elements that contain an animation trigger using `query(\"\\@triggerName\")`\n *  - Querying all elements that contain an animation triggers using `query(\"\\@*\")`\n *  - Including the current element into the animation sequence using `query(\":self\")`\n *\n *\n *  Each of these pseudo-selector tokens can be merged together into a combined query selector\n * string:\n *\n *  ```\n *  query(':self, .record:enter, .record:leave, \\@subTrigger', [...])\n *  ```\n *\n * ### Demo\n *\n * ```\n * \\@Component({\n *   selector: 'inner',\n *   template: `\n *     <div [\\@queryAnimation]=\"exp\">\n *       <h1>Title</h1>\n *       <div class=\"content\">\n *         Blah blah blah\n *       </div>\n *     </div>\n *   `,\n *   animations: [\n *    trigger('queryAnimation', [\n *      transition('* => goAnimate', [\n *        // hide the inner elements\n *        query('h1', style({ opacity: 0 })),\n *        query('.content', style({ opacity: 0 })),\n *\n *        // animate the inner elements in, one by one\n *        query('h1', animate(1000, style({ opacity: 1 })),\n *        query('.content', animate(1000, style({ opacity: 1 })),\n *      ])\n *    ])\n *  ]\n * })\n * class Cmp {\n *   exp = '';\n *\n *   goAnimate() {\n *     this.exp = 'goAnimate';\n *   }\n * }\n * ```\n *\n * \\@experimental Animation support is experimental.\n * @param {?} selector\n * @param {?} animation\n * @param {?=} options\n * @return {?}\n */\nexport function query(selector, animation, options) {\n    if (options === void 0) { options = null; }\n    return { type: 11 /* Query */, selector: selector, animation: animation, options: options };\n}\n/**\n * `stagger` is an animation-specific function that is designed to be used inside of Angular's\n * animation DSL language. It is designed to be used inside of an animation {\\@link query query()}\n * and works by issuing a timing gap between after each queried item is animated.\n *\n * ### Usage\n *\n * In the example below there is a container element that wraps a list of items stamped out\n * by an ngFor. The container element contains an animation trigger that will later be set\n * to query for each of the inner items.\n *\n * ```html\n * <!-- list.component.html -->\n * <button (click)=\"toggle()\">Show / Hide Items</button>\n * <hr />\n * <div [\\@listAnimation]=\"items.length\">\n *   <div *ngFor=\"let item of items\">\n *     {{ item }}\n *   </div>\n * </div>\n * ```\n *\n * The component code for this looks as such:\n *\n * ```ts\n * import {trigger, transition, style, animate, query, stagger} from '\\@angular/animations';\n * \\@Component({\n *   templateUrl: 'list.component.html',\n *   animations: [\n *     trigger('listAnimation', [\n *        //...\n *     ])\n *   ]\n * })\n * class ListComponent {\n *   items = [];\n *\n *   showItems() {\n *     this.items = [0,1,2,3,4];\n *   }\n *\n *   hideItems() {\n *     this.items = [];\n *   }\n *\n *   toggle() {\n *     this.items.length ? this.hideItems() : this.showItems();\n *   }\n * }\n * ```\n *\n * And now for the animation trigger code:\n *\n * ```ts\n * trigger('listAnimation', [\n *   transition('* => *', [ // each time the binding value changes\n *     query(':leave', [\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 0 }))\n *       ])\n *     ]),\n *     query(':enter', [\n *       style({ opacity: 0 }),\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 1 }))\n *       ])\n *     ])\n *   ])\n * ])\n * ```\n *\n * Now each time the items are added/removed then either the opacity\n * fade-in animation will run or each removed item will be faded out.\n * When either of these animations occur then a stagger effect will be\n * applied after each item's animation is started.\n *\n * \\@experimental Animation support is experimental.\n * @param {?} timings\n * @param {?} animation\n * @return {?}\n */\nexport function stagger(timings, animation) {\n    return { type: 12 /* Stagger */, timings: timings, animation: animation };\n}\n//# sourceMappingURL=animation_metadata.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @param {?} cb\n * @return {?}\n */\nexport function scheduleMicroTask(cb) {\n    Promise.resolve(null).then(cb);\n}\n//# sourceMappingURL=util.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { scheduleMicroTask } from '../util';\n/**\n * AnimationPlayer controls an animation sequence that was produced from a programmatic animation.\n * (see {\\@link AnimationBuilder AnimationBuilder} for more information on how to create programmatic\n * animations.)\n *\n * \\@experimental Animation support is experimental.\n * @record\n */\nexport function AnimationPlayer() { }\nfunction AnimationPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationPlayer.prototype.onDone;\n    /** @type {?} */\n    AnimationPlayer.prototype.onStart;\n    /** @type {?} */\n    AnimationPlayer.prototype.onDestroy;\n    /** @type {?} */\n    AnimationPlayer.prototype.init;\n    /** @type {?} */\n    AnimationPlayer.prototype.hasStarted;\n    /** @type {?} */\n    AnimationPlayer.prototype.play;\n    /** @type {?} */\n    AnimationPlayer.prototype.pause;\n    /** @type {?} */\n    AnimationPlayer.prototype.restart;\n    /** @type {?} */\n    AnimationPlayer.prototype.finish;\n    /** @type {?} */\n    AnimationPlayer.prototype.destroy;\n    /** @type {?} */\n    AnimationPlayer.prototype.reset;\n    /** @type {?} */\n    AnimationPlayer.prototype.setPosition;\n    /** @type {?} */\n    AnimationPlayer.prototype.getPosition;\n    /** @type {?} */\n    AnimationPlayer.prototype.parentPlayer;\n    /** @type {?} */\n    AnimationPlayer.prototype.totalTime;\n    /** @type {?|undefined} */\n    AnimationPlayer.prototype.beforeDestroy;\n    /** @type {?|undefined} */\n    AnimationPlayer.prototype.triggerCallback;\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nvar /**\n * \\@experimental Animation support is experimental.\n */\nNoopAnimationPlayer = /** @class */ (function () {\n    function NoopAnimationPlayer() {\n        this._onDoneFns = [];\n        this._onStartFns = [];\n        this._onDestroyFns = [];\n        this._started = false;\n        this._destroyed = false;\n        this._finished = false;\n        this.parentPlayer = null;\n        this.totalTime = 0;\n    }\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype._onFinish = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach(function (fn) { return fn(); });\n            this._onDoneFns = [];\n        }\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.onStart = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onStartFns.push(fn); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.onDone = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDoneFns.push(fn); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.onDestroy = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDestroyFns.push(fn); };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this._started; };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () { };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () {\n        if (!this.hasStarted()) {\n            this._onStart();\n            this.triggerMicrotask();\n        }\n        this._started = true;\n    };\n    /* @internal */\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.triggerMicrotask = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        scheduleMicroTask(function () { return _this._onFinish(); });\n    };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype._onStart = /**\n     * @return {?}\n     */\n    function () {\n        this._onStartFns.forEach(function (fn) { return fn(); });\n        this._onStartFns = [];\n    };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.pause = /**\n     * @return {?}\n     */\n    function () { };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.restart = /**\n     * @return {?}\n     */\n    function () { };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () { this._onFinish(); };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            if (!this.hasStarted()) {\n                this._onStart();\n            }\n            this.finish();\n            this._onDestroyFns.forEach(function (fn) { return fn(); });\n            this._onDestroyFns = [];\n        }\n    };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.reset = /**\n     * @return {?}\n     */\n    function () { };\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.setPosition = /**\n     * @param {?} p\n     * @return {?}\n     */\n    function (p) { };\n    /**\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.getPosition = /**\n     * @return {?}\n     */\n    function () { return 0; };\n    /* @internal */\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    NoopAnimationPlayer.prototype.triggerCallback = /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    function (phaseName) {\n        var /** @type {?} */ methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach(function (fn) { return fn(); });\n        methods.length = 0;\n    };\n    return NoopAnimationPlayer;\n}());\n/**\n * \\@experimental Animation support is experimental.\n */\nexport { NoopAnimationPlayer };\nfunction NoopAnimationPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    NoopAnimationPlayer.prototype._onDoneFns;\n    /** @type {?} */\n    NoopAnimationPlayer.prototype._onStartFns;\n    /** @type {?} */\n    NoopAnimationPlayer.prototype._onDestroyFns;\n    /** @type {?} */\n    NoopAnimationPlayer.prototype._started;\n    /** @type {?} */\n    NoopAnimationPlayer.prototype._destroyed;\n    /** @type {?} */\n    NoopAnimationPlayer.prototype._finished;\n    /** @type {?} */\n    NoopAnimationPlayer.prototype.parentPlayer;\n    /** @type {?} */\n    NoopAnimationPlayer.prototype.totalTime;\n}\n//# sourceMappingURL=animation_player.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { scheduleMicroTask } from '../util';\nvar AnimationGroupPlayer = /** @class */ (function () {\n    function AnimationGroupPlayer(_players) {\n        var _this = this;\n        this._onDoneFns = [];\n        this._onStartFns = [];\n        this._finished = false;\n        this._started = false;\n        this._destroyed = false;\n        this._onDestroyFns = [];\n        this.parentPlayer = null;\n        this.totalTime = 0;\n        this.players = _players;\n        var /** @type {?} */ doneCount = 0;\n        var /** @type {?} */ destroyCount = 0;\n        var /** @type {?} */ startCount = 0;\n        var /** @type {?} */ total = this.players.length;\n        if (total == 0) {\n            scheduleMicroTask(function () { return _this._onFinish(); });\n        }\n        else {\n            this.players.forEach(function (player) {\n                player.onDone(function () {\n                    if (++doneCount == total) {\n                        _this._onFinish();\n                    }\n                });\n                player.onDestroy(function () {\n                    if (++destroyCount == total) {\n                        _this._onDestroy();\n                    }\n                });\n                player.onStart(function () {\n                    if (++startCount == total) {\n                        _this._onStart();\n                    }\n                });\n            });\n        }\n        this.totalTime = this.players.reduce(function (time, player) { return Math.max(time, player.totalTime); }, 0);\n    }\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype._onFinish = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach(function (fn) { return fn(); });\n            this._onDoneFns = [];\n        }\n    };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () { this.players.forEach(function (player) { return player.init(); }); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.onStart = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onStartFns.push(fn); };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype._onStart = /**\n     * @return {?}\n     */\n    function () {\n        if (!this.hasStarted()) {\n            this._started = true;\n            this._onStartFns.forEach(function (fn) { return fn(); });\n            this._onStartFns = [];\n        }\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.onDone = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDoneFns.push(fn); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.onDestroy = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDestroyFns.push(fn); };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this._started; };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () {\n        if (!this.parentPlayer) {\n            this.init();\n        }\n        this._onStart();\n        this.players.forEach(function (player) { return player.play(); });\n    };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.pause = /**\n     * @return {?}\n     */\n    function () { this.players.forEach(function (player) { return player.pause(); }); };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.restart = /**\n     * @return {?}\n     */\n    function () { this.players.forEach(function (player) { return player.restart(); }); };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () {\n        this._onFinish();\n        this.players.forEach(function (player) { return player.finish(); });\n    };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () { this._onDestroy(); };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype._onDestroy = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._onFinish();\n            this.players.forEach(function (player) { return player.destroy(); });\n            this._onDestroyFns.forEach(function (fn) { return fn(); });\n            this._onDestroyFns = [];\n        }\n    };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.reset = /**\n     * @return {?}\n     */\n    function () {\n        this.players.forEach(function (player) { return player.reset(); });\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n    };\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.setPosition = /**\n     * @param {?} p\n     * @return {?}\n     */\n    function (p) {\n        var /** @type {?} */ timeAtPosition = p * this.totalTime;\n        this.players.forEach(function (player) {\n            var /** @type {?} */ position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n            player.setPosition(position);\n        });\n    };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.getPosition = /**\n     * @return {?}\n     */\n    function () {\n        var /** @type {?} */ min = 0;\n        this.players.forEach(function (player) {\n            var /** @type {?} */ p = player.getPosition();\n            min = Math.min(p, min);\n        });\n        return min;\n    };\n    /**\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.beforeDestroy = /**\n     * @return {?}\n     */\n    function () {\n        this.players.forEach(function (player) {\n            if (player.beforeDestroy) {\n                player.beforeDestroy();\n            }\n        });\n    };\n    /* @internal */\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    AnimationGroupPlayer.prototype.triggerCallback = /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    function (phaseName) {\n        var /** @type {?} */ methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach(function (fn) { return fn(); });\n        methods.length = 0;\n    };\n    return AnimationGroupPlayer;\n}());\nexport { AnimationGroupPlayer };\nfunction AnimationGroupPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    AnimationGroupPlayer.prototype._onDoneFns;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype._onStartFns;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype._finished;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype._started;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype._destroyed;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype._onDestroyFns;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype.parentPlayer;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype.totalTime;\n    /** @type {?} */\n    AnimationGroupPlayer.prototype.players;\n}\n//# sourceMappingURL=animation_group_player.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nexport { AnimationGroupPlayer as ɵAnimationGroupPlayer } from './players/animation_group_player';\nexport var /** @type {?} */ ɵPRE_STYLE = '!';\n//# sourceMappingURL=private_export.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport { AnimationBuilder, AnimationFactory } from './animation_builder';\nexport { AUTO_STYLE, animate, animateChild, animation, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation } from './animation_metadata';\nexport { NoopAnimationPlayer } from './players/animation_player';\nexport { ɵAnimationGroupPlayer, ɵPRE_STYLE } from './private_export';\n//# sourceMappingURL=animations.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport { AnimationBuilder, AnimationFactory, AUTO_STYLE, animate, animateChild, animation, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE } from './src/animations';\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { AnimationBuilder, AnimationFactory, AUTO_STYLE, animate, animateChild, animation, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE } from './public_api';\n//# sourceMappingURL=animations.js.map"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,IAwCA,gBAAgB,kBAAkB,YAAY;IAC1C,SAAS,gBAAgB,GAAG;KAC3B;IACD,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC,CAAC;AACL,AAiDA;;;;;;;AAOA,IAOA,gBAAgB,kBAAkB,YAAY;IAC1C,SAAS,gBAAgB,GAAG;KAC3B;IACD,OAAO,gBAAgB,CAAC;CAC3B,EAAE,CAAC;;AC5JJ;;;;;;;;;;;;AAYA,AAAgC;AAChC,AAsBA;;;AAGA,AAAO,IAAqB,UAAU,GAAG,GAAG,CAAC;;;;;AAK7C,AAAuC;AACvC,AAIA;;;;;;;AAOA,AAA8C;AAC9C,AAQA;;;;;;;AAOA,AAA4C;AAC5C,AAQA;;;;;;;AAOA,AAAiD;AACjD,AAQA;;;;AAIA,AAAgD;AAChD,AAMA;;;;AAIA,AAA4C;AAC5C,AAQA;;;;;;;AAOA,AAAwD;AACxD,AAIA;;;;;;;AAOA,AAA4C;AAC5C,AAMA;;;;;;;AAOA,AAA8C;AAC9C,AAMA;;;;;;;AAOA,AAAmD;AACnD,AAIA;;;;;;;AAOA,AAAiD;AACjD,AAMA;;;;;;;AAOA,AAA+C;AAC/C,AAMA;;;;;;;AAOA,AAA4C;AAC5C,AAMA;;;;;;;AAOA,AAA8C;AAC9C,AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA,AAAO,SAAS,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE;IACvC,OAAO,EAAE,IAAI,EAAE,CAAC,gBAAgB,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;CACvF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDD,AAAO,SAAS,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE;IACrC,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,EAAE;IACzC,OAAO,EAAE,IAAI,EAAE,CAAC,gBAAgB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CACtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCD,AAAO,SAAS,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE;IAClC,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;IAC3C,OAAO,EAAE,IAAI,EAAE,CAAC,cAAc,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CAClE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCD,AAAO,SAAS,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE;IACrC,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;IAC3C,OAAO,EAAE,IAAI,EAAE,CAAC,iBAAiB,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CACrE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CD,AAAO,SAAS,KAAK,CAAC,MAAM,EAAE;IAC1B,OAAO,EAAE,IAAI,EAAE,CAAC,cAAc,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;CAChE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDD,AAAO,SAAS,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;IACzC,OAAO,EAAE,IAAI,EAAE,CAAC,cAAc,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CAChF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDD,AAAO,SAAS,SAAS,CAAC,KAAK,EAAE;IAC7B,OAAO,EAAE,IAAI,EAAE,CAAC,kBAAkB,KAAK,EAAE,KAAK,EAAE,CAAC;CACpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4MD,AAAO,SAAS,UAAU,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;IACxD,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;IAC3C,OAAO,EAAE,IAAI,EAAE,CAAC,mBAAmB,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CAClG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCD,AAAO,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;IACtC,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;IAC3C,OAAO,EAAE,IAAI,EAAE,CAAC,kBAAkB,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CAC1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGD,AAAO,SAAS,YAAY,CAAC,OAAO,EAAE;IAClC,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;IAC3C,OAAO,EAAE,IAAI,EAAE,CAAC,qBAAqB,OAAO,EAAE,OAAO,EAAE,CAAC;CAC3D;;;;;;;;;;;AAWD,AAAO,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;IAC7C,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;IAC3C,OAAO,EAAE,IAAI,EAAE,EAAE,mBAAmB,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CAChF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGD,AAAO,SAAS,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;IAChD,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;IAC3C,OAAO,EAAE,IAAI,EAAE,EAAE,cAAc,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;CAC/F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFD,AAAO,SAAS,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE;IACxC,OAAO,EAAE,IAAI,EAAE,EAAE,gBAAgB,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;CAC7E;;AC5pCD;;;;;;;;;;;;;AAaA,AAAO,SAAS,iBAAiB,CAAC,EAAE,EAAE;IAClC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;CAClC;;ACfD;;;;AAIA,AACA;;;;;;;;AAQA,AAAqC;AACrC,AAoCA;;;AAGA,IAGA,mBAAmB,kBAAkB,YAAY;IAC7C,SAAS,mBAAmB,GAAG;QAC3B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;KACtB;;;;IAID,mBAAmB,CAAC,SAAS,CAAC,SAAS;;;IAGvC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;KACJ,CAAC;;;;;IAKF,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;;IAIrC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAK7C,mBAAmB,CAAC,SAAS,CAAC,MAAM;;;;IAIpC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAK5C,mBAAmB,CAAC,SAAS,CAAC,SAAS;;;;IAIvC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;IAI/C,mBAAmB,CAAC,SAAS,CAAC,UAAU;;;IAGxC,YAAY,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;;;IAItC,mBAAmB,CAAC,SAAS,CAAC,IAAI;;;IAGlC,YAAY,GAAG,CAAC;;;;IAIhB,mBAAmB,CAAC,SAAS,CAAC,IAAI;;;IAGlC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACpB,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACxB,CAAC;;;;;IAKF,mBAAmB,CAAC,SAAS,CAAC,gBAAgB;;;IAG9C,YAAY;QACR,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,iBAAiB,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;KAChE,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,QAAQ;;;IAGtC,YAAY;QACR,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;KACzB,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,KAAK;;;IAGnC,YAAY,GAAG,CAAC;;;;IAIhB,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;IAGrC,YAAY,GAAG,CAAC;;;;IAIhB,mBAAmB,CAAC,SAAS,CAAC,MAAM;;;IAGpC,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC;;;;IAIlC,mBAAmB,CAAC,SAAS,CAAC,OAAO;;;IAGrC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;gBACpB,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SAC3B;KACJ,CAAC;;;;IAIF,mBAAmB,CAAC,SAAS,CAAC,KAAK;;;IAGnC,YAAY,GAAG,CAAC;;;;;IAKhB,mBAAmB,CAAC,SAAS,CAAC,WAAW;;;;IAIzC,UAAU,CAAC,EAAE,GAAG,CAAC;;;;IAIjB,mBAAmB,CAAC,SAAS,CAAC,WAAW;;;IAGzC,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC;;;;;;IAM1B,mBAAmB,CAAC,SAAS,CAAC,eAAe;;;;IAI7C,UAAU,SAAS,EAAE;QACjB,qBAAqB,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACzF,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAChD,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KACtB,CAAC;IACF,OAAO,mBAAmB,CAAC;CAC9B,EAAE,CAAC;;ACvOJ;;;;;;;;;;;AAWA,AACA,IAAI,oBAAoB,kBAAkB,YAAY;IAClD,SAAS,oBAAoB,CAAC,QAAQ,EAAE;QACpC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;QACxB,qBAAqB,SAAS,GAAG,CAAC,CAAC;QACnC,qBAAqB,YAAY,GAAG,CAAC,CAAC;QACtC,qBAAqB,UAAU,GAAG,CAAC,CAAC;QACpC,qBAAqB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACjD,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,iBAAiB,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;SAChE;aACI;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;gBACnC,MAAM,CAAC,MAAM,CAAC,YAAY;oBACtB,IAAI,EAAE,SAAS,IAAI,KAAK,EAAE;wBACtB,KAAK,CAAC,SAAS,EAAE,CAAC;qBACrB;iBACJ,CAAC,CAAC;gBACH,MAAM,CAAC,SAAS,CAAC,YAAY;oBACzB,IAAI,EAAE,YAAY,IAAI,KAAK,EAAE;wBACzB,KAAK,CAAC,UAAU,EAAE,CAAC;qBACtB;iBACJ,CAAC,CAAC;gBACH,MAAM,CAAC,OAAO,CAAC,YAAY;oBACvB,IAAI,EAAE,UAAU,IAAI,KAAK,EAAE;wBACvB,KAAK,CAAC,QAAQ,EAAE,CAAC;qBACpB;iBACJ,CAAC,CAAC;aACN,CAAC,CAAC;SACN;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;KACjH;;;;IAID,oBAAoB,CAAC,SAAS,CAAC,SAAS;;;IAGxC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;KACJ,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;IAGnC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAKnF,oBAAoB,CAAC,SAAS,CAAC,OAAO;;;;IAItC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;IAI7C,oBAAoB,CAAC,SAAS,CAAC,QAAQ;;;IAGvC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;SACzB;KACJ,CAAC;;;;;IAKF,oBAAoB,CAAC,SAAS,CAAC,MAAM;;;;IAIrC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;;IAK5C,oBAAoB,CAAC,SAAS,CAAC,SAAS;;;;IAIxC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;IAI/C,oBAAoB,CAAC,SAAS,CAAC,UAAU;;;IAGzC,YAAY,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;;;;IAItC,oBAAoB,CAAC,SAAS,CAAC,IAAI;;;IAGnC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,IAAI,EAAE,CAAC;SACf;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;KACrE,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,KAAK;;;IAGpC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;IAIpF,oBAAoB,CAAC,SAAS,CAAC,OAAO;;;IAGtC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;;;IAItF,oBAAoB,CAAC,SAAS,CAAC,MAAM;;;IAGrC,YAAY;QACR,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;KACvE,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,OAAO;;;IAGtC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;;;;IAInC,oBAAoB,CAAC,SAAS,CAAC,UAAU;;;IAGzC,YAAY;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SAC3B;KACJ,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,KAAK;;;IAGpC,YAAY;QACR,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;KACzB,CAAC;;;;;IAKF,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;;IAI1C,UAAU,CAAC,EAAE;QACT,qBAAqB,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QACzD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YACnC,qBAAqB,QAAQ,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACtG,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SAChC,CAAC,CAAC;KACN,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,WAAW;;;IAG1C,YAAY;QACR,qBAAqB,GAAG,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YACnC,qBAAqB,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YAC9C,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;SAC1B,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;KACd,CAAC;;;;IAIF,oBAAoB,CAAC,SAAS,CAAC,aAAa;;;IAG5C,YAAY;QACR,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YACnC,IAAI,MAAM,CAAC,aAAa,EAAE;gBACtB,MAAM,CAAC,aAAa,EAAE,CAAC;aAC1B;SACJ,CAAC,CAAC;KACN,CAAC;;;;;;IAMF,oBAAoB,CAAC,SAAS,CAAC,eAAe;;;;IAI9C,UAAU,SAAS,EAAE;QACjB,qBAAqB,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACzF,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAChD,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KACtB,CAAC;IACF,OAAO,oBAAoB,CAAC;CAC/B,EAAE,CAAC;;ACvPJ;;;;AAIA,AACO,IAAqB,UAAU,GAAG,GAAG;;ACL5C;;;;;;;;;;GAUG;;ACVH;;;;;;;;;;;;;;;GAeG;;ACfH;;;;;;GAMG;;;;"}