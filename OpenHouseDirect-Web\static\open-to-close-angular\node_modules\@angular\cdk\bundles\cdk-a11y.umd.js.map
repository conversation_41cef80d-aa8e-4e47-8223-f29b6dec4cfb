{"version": 3, "file": "cdk-a11y.umd.js", "sources": ["../../src/cdk/a11y/a11y-module.ts", "../../src/cdk/a11y/fake-mousedown.ts", "../../src/cdk/a11y/focus-monitor/focus-monitor.ts", "../../src/cdk/a11y/live-announcer/live-announcer.ts", "../../src/cdk/a11y/key-manager/focus-key-manager.ts", "../../src/cdk/a11y/key-manager/activedescendant-key-manager.ts", "../../src/cdk/a11y/key-manager/list-key-manager.ts", "../../src/cdk/a11y/aria-describer/aria-describer.ts", "../../src/cdk/a11y/aria-describer/aria-reference.ts", "../../src/cdk/a11y/focus-trap/focus-trap.ts", "../../src/cdk/a11y/interactivity-checker/interactivity-checker.ts", "../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PlatformModule} from '@angular/cdk/platform';\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {ARIA_DESCRIBER_PROVIDER, AriaDescriber} from './aria-describer/aria-describer';\nimport {CdkMonitorFocus, FOCUS_MONITOR_PROVIDER} from './focus-monitor/focus-monitor';\nimport {\n  CdkTrapFocus,\n  FocusTrapDeprecatedDirective,\n  FocusTrapFactory,\n} from './focus-trap/focus-trap';\nimport {Interact<PERSON><PERSON>he<PERSON>} from './interactivity-checker/interactivity-checker';\nimport {LIVE_ANNOUNCER_PROVIDER} from './live-announcer/live-announcer';\n\n@NgModule({\n  imports: [CommonModule, PlatformModule],\n  declarations: [CdkTrapFocus, FocusTrapDeprecatedDirective, CdkMonitorFocus],\n  exports: [CdkTrapFocus, FocusTrapDeprecatedDirective, CdkMonitorFocus],\n  providers: [\n    InteractivityChecker,\n    FocusTrapFactory,\n    AriaDescriber,\n    LIVE_ANNOUNCER_PROVIDER,\n    ARIA_DESCRIBER_PROVIDER,\n    FOCUS_MONITOR_PROVIDER,\n  ]\n})\nexport class A11yModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Screenreaders will often fire fake mousedown events when a focusable element\n * is activated using the keyboard. We can typically distinguish between these faked\n * mousedown events and real mousedown events using the \"buttons\" property. While\n * real mousedowns will indicate the mouse button that was pressed (e.g. \"1\" for\n * the left mouse button), faked mousedowns will usually set the property value to 0.\n */\nexport function isFakeMousedownFromScreenReader(event: MouseEvent): boolean {\n  return event.buttons === 0;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Platform, supportsPassiveEventListeners} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injectable,\n  NgZone,\n  OnDestroy,\n  Optional,\n  Output,\n  Renderer2,\n  SkipSelf,\n} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {of as observableOf} from 'rxjs/observable/of';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\n\n\n// This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n// that a value of around 650ms seems appropriate.\nexport const TOUCH_BUFFER_MS = 650;\n\n\nexport type FocusOrigin = 'touch' | 'mouse' | 'keyboard' | 'program' | null;\n\n\ntype MonitoredElementInfo = {\n  unlisten: Function,\n  checkChildren: boolean,\n  subject: Subject<FocusOrigin>\n};\n\n\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\n@Injectable()\nexport class FocusMonitor implements OnDestroy {\n  /** The focus origin that the next focus event is a result of. */\n  private _origin: FocusOrigin = null;\n\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  private _lastFocusOrigin: FocusOrigin;\n\n  /** Whether the window has just been focused. */\n  private _windowFocused = false;\n\n  /** The target of the last touch event. */\n  private _lastTouchTarget: EventTarget | null;\n\n  /** The timeout id of the touch timeout, used to cancel timeout later. */\n  private _touchTimeoutId: number;\n\n  /** The timeout id of the window focus timeout. */\n  private _windowFocusTimeoutId: number;\n\n  /** The timeout id of the origin clearing timeout. */\n  private _originTimeoutId: number;\n\n  /** Map of elements being monitored to their info. */\n  private _elementInfo = new Map<HTMLElement, MonitoredElementInfo>();\n\n  /** A map of global objects to lists of current listeners. */\n  private _unregisterGlobalListeners = () => {};\n\n  /** The number of elements currently being monitored. */\n  private _monitoredElementCount = 0;\n\n  constructor(private _ngZone: NgZone, private _platform: Platform) {}\n\n  /**\n   * @docs-private\n   * @deprecated renderer param no longer needed.\n   * @deletion-target 6.0.0\n   */\n  monitor(element: HTMLElement, renderer: Renderer2, checkChildren: boolean):\n      Observable<FocusOrigin>;\n  /**\n   * Monitors focus on an element and applies appropriate CSS classes.\n   * @param element The element to monitor\n   * @param checkChildren Whether to count the element as focused when its children are focused.\n   * @returns An observable that emits when the focus state of the element changes.\n   *     When the element is blurred, null will be emitted.\n   */\n  monitor(element: HTMLElement, checkChildren?: boolean): Observable<FocusOrigin>;\n  monitor(\n      element: HTMLElement,\n      renderer?: Renderer2 | boolean,\n      checkChildren?: boolean): Observable<FocusOrigin> {\n    // TODO(mmalerba): clean up after deprecated signature is removed.\n    if (!(renderer instanceof Renderer2)) {\n      checkChildren = renderer;\n    }\n    checkChildren = !!checkChildren;\n\n    // Do nothing if we're not on the browser platform.\n    if (!this._platform.isBrowser) {\n      return observableOf(null);\n    }\n    // Check if we're already monitoring this element.\n    if (this._elementInfo.has(element)) {\n      let cachedInfo = this._elementInfo.get(element);\n      cachedInfo!.checkChildren = checkChildren;\n      return cachedInfo!.subject.asObservable();\n    }\n\n    // Create monitored element info.\n    let info: MonitoredElementInfo = {\n      unlisten: () => {},\n      checkChildren: checkChildren,\n      subject: new Subject<FocusOrigin>()\n    };\n    this._elementInfo.set(element, info);\n    this._incrementMonitoredElementCount();\n\n    // Start listening. We need to listen in capture phase since focus events don't bubble.\n    let focusListener = (event: FocusEvent) => this._onFocus(event, element);\n    let blurListener = (event: FocusEvent) => this._onBlur(event, element);\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('focus', focusListener, true);\n      element.addEventListener('blur', blurListener, true);\n    });\n\n    // Create an unlisten function for later.\n    info.unlisten = () => {\n      element.removeEventListener('focus', focusListener, true);\n      element.removeEventListener('blur', blurListener, true);\n    };\n\n    return info.subject.asObservable();\n  }\n\n  /**\n   * Stops monitoring an element and removes all focus classes.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: HTMLElement): void {\n    const elementInfo = this._elementInfo.get(element);\n\n    if (elementInfo) {\n      elementInfo.unlisten();\n      elementInfo.subject.complete();\n\n      this._setClasses(element);\n      this._elementInfo.delete(element);\n      this._decrementMonitoredElementCount();\n    }\n  }\n\n  /**\n   * Focuses the element via the specified focus origin.\n   * @param element The element to focus.\n   * @param origin The focus origin.\n   */\n  focusVia(element: HTMLElement, origin: FocusOrigin): void {\n    this._setOriginForCurrentEventQueue(origin);\n    element.focus();\n  }\n\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n\n  /** Register necessary event listeners on the document and window. */\n  private _registerGlobalListeners() {\n    // Do nothing if we're not on the browser platform.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    // On keydown record the origin and clear any touch event that may be in progress.\n    let documentKeydownListener = () => {\n      this._lastTouchTarget = null;\n      this._setOriginForCurrentEventQueue('keyboard');\n    };\n\n    // On mousedown record the origin only if there is not touch target, since a mousedown can\n    // happen as a result of a touch event.\n    let documentMousedownListener = () => {\n      if (!this._lastTouchTarget) {\n        this._setOriginForCurrentEventQueue('mouse');\n      }\n    };\n\n    // When the touchstart event fires the focus event is not yet in the event queue. This means\n    // we can't rely on the trick used above (setting timeout of 0ms). Instead we wait 650ms to\n    // see if a focus happens.\n    let documentTouchstartListener = (event: TouchEvent) => {\n      if (this._touchTimeoutId != null) {\n        clearTimeout(this._touchTimeoutId);\n      }\n      this._lastTouchTarget = event.target;\n      this._touchTimeoutId = setTimeout(() => this._lastTouchTarget = null, TOUCH_BUFFER_MS);\n    };\n\n    // Make a note of when the window regains focus, so we can restore the origin info for the\n    // focused element.\n    let windowFocusListener = () => {\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false, 0);\n    };\n\n    // Note: we listen to events in the capture phase so we can detect them even if the user stops\n    // propagation.\n    this._ngZone.runOutsideAngular(() => {\n      document.addEventListener('keydown', documentKeydownListener, true);\n      document.addEventListener('mousedown', documentMousedownListener, true);\n      document.addEventListener('touchstart', documentTouchstartListener,\n          supportsPassiveEventListeners() ? ({passive: true, capture: true} as any) : true);\n      window.addEventListener('focus', windowFocusListener);\n    });\n\n    this._unregisterGlobalListeners = () => {\n      document.removeEventListener('keydown', documentKeydownListener, true);\n      document.removeEventListener('mousedown', documentMousedownListener, true);\n      document.removeEventListener('touchstart', documentTouchstartListener,\n          supportsPassiveEventListeners() ? ({passive: true, capture: true} as any) : true);\n      window.removeEventListener('focus', windowFocusListener);\n\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._touchTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    };\n  }\n\n  private _toggleClass(element: Element, className: string, shouldSet: boolean) {\n    if (shouldSet) {\n      element.classList.add(className);\n    } else {\n      element.classList.remove(className);\n    }\n  }\n\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  private _setClasses(element: HTMLElement, origin?: FocusOrigin): void {\n    const elementInfo = this._elementInfo.get(element);\n\n    if (elementInfo) {\n      this._toggleClass(element, 'cdk-focused', !!origin);\n      this._toggleClass(element, 'cdk-touch-focused', origin === 'touch');\n      this._toggleClass(element, 'cdk-keyboard-focused', origin === 'keyboard');\n      this._toggleClass(element, 'cdk-mouse-focused', origin === 'mouse');\n      this._toggleClass(element, 'cdk-program-focused', origin === 'program');\n    }\n  }\n\n  /**\n   * Sets the origin and schedules an async function to clear it at the end of the event queue.\n   * @param origin The origin to set.\n   */\n  private _setOriginForCurrentEventQueue(origin: FocusOrigin): void {\n    this._origin = origin;\n    this._originTimeoutId = setTimeout(() => this._origin = null, 0);\n  }\n\n  /**\n   * Checks whether the given focus event was caused by a touchstart event.\n   * @param event The focus event to check.\n   * @returns Whether the event was caused by a touch.\n   */\n  private _wasCausedByTouch(event: FocusEvent): boolean {\n    // Note(mmalerba): This implementation is not quite perfect, there is a small edge case.\n    // Consider the following dom structure:\n    //\n    // <div #parent tabindex=\"0\" cdkFocusClasses>\n    //   <div #child (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // If the user touches the #child element and the #parent is programmatically focused as a\n    // result, this code will still consider it to have been caused by the touch event and will\n    // apply the cdk-touch-focused class rather than the cdk-program-focused class. This is a\n    // relatively small edge-case that can be worked around by using\n    // focusVia(parentEl, 'program') to focus the parent element.\n    //\n    // If we decide that we absolutely must handle this case correctly, we can do so by listening\n    // for the first focus event after the touchstart, and then the first blur event after that\n    // focus event. When that blur event fires we know that whatever follows is not a result of the\n    // touchstart.\n    let focusTarget = event.target;\n    return this._lastTouchTarget instanceof Node && focusTarget instanceof Node &&\n        (focusTarget === this._lastTouchTarget || focusTarget.contains(this._lastTouchTarget));\n  }\n\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  private _onFocus(event: FocusEvent, element: HTMLElement) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || (!elementInfo.checkChildren && element !== event.target)) {\n      return;\n    }\n\n    // If we couldn't detect a cause for the focus event, it's due to one of three reasons:\n    // 1) The window has just regained focus, in which case we want to restore the focused state of\n    //    the element from before the window blurred.\n    // 2) It was caused by a touch event, in which case we mark the origin as 'touch'.\n    // 3) The element was programmatically focused, in which case we should mark the origin as\n    //    'program'.\n    if (!this._origin) {\n      if (this._windowFocused && this._lastFocusOrigin) {\n        this._origin = this._lastFocusOrigin;\n      } else if (this._wasCausedByTouch(event)) {\n        this._origin = 'touch';\n      } else {\n        this._origin = 'program';\n      }\n    }\n\n    this._setClasses(element, this._origin);\n    elementInfo.subject.next(this._origin);\n    this._lastFocusOrigin = this._origin;\n    this._origin = null;\n  }\n\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event: FocusEvent, element: HTMLElement) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n\n    if (!elementInfo || (elementInfo.checkChildren && event.relatedTarget instanceof Node &&\n        element.contains(event.relatedTarget))) {\n      return;\n    }\n\n    this._setClasses(element);\n    elementInfo.subject.next(null);\n  }\n\n  private _incrementMonitoredElementCount() {\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount == 1) {\n      this._registerGlobalListeners();\n    }\n  }\n\n  private _decrementMonitoredElementCount() {\n    // Unregister global listeners when last element is unmonitored.\n    if (!--this._monitoredElementCount) {\n      this._unregisterGlobalListeners();\n      this._unregisterGlobalListeners = () => {};\n    }\n  }\n\n}\n\n\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\n@Directive({\n  selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n})\nexport class CdkMonitorFocus implements OnDestroy {\n  private _monitorSubscription: Subscription;\n  @Output() cdkFocusChange = new EventEmitter<FocusOrigin>();\n\n  constructor(private _elementRef: ElementRef, private _focusMonitor: FocusMonitor) {\n    this._monitorSubscription = this._focusMonitor.monitor(\n        this._elementRef.nativeElement,\n        this._elementRef.nativeElement.hasAttribute('cdkMonitorSubtreeFocus'))\n        .subscribe(origin => this.cdkFocusChange.emit(origin));\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef.nativeElement);\n    this._monitorSubscription.unsubscribe();\n  }\n}\n\n/** @docs-private */\nexport function FOCUS_MONITOR_PROVIDER_FACTORY(\n    parentDispatcher: FocusMonitor, ngZone: NgZone, platform: Platform) {\n  return parentDispatcher || new FocusMonitor(ngZone, platform);\n}\n\n/** @docs-private */\nexport const FOCUS_MONITOR_PROVIDER = {\n  // If there is already a FocusMonitor available, use that. Otherwise, provide a new one.\n  provide: FocusMonitor,\n  deps: [[new Optional(), new SkipSelf(), FocusMonitor], NgZone, Platform],\n  useFactory: FOCUS_MONITOR_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Injectable,\n  InjectionToken,\n  Optional,\n  Inject,\n  SkipSelf,\n  OnDestroy,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\n\n\nexport const LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken<HTMLElement>('liveAnnouncerElement');\n\n/** Possible politeness levels. */\nexport type AriaLivePoliteness = 'off' | 'polite' | 'assertive';\n\n@Injectable()\nexport class LiveAnnouncer implements OnD<PERSON>roy {\n  private _liveElement: Element;\n\n  constructor(\n      @Optional() @Inject(LIVE_ANNOUNCER_ELEMENT_TOKEN) elementToken: any,\n      @Inject(DOCUMENT) private _document: any) {\n\n    // We inject the live element as `any` because the constructor signature cannot reference\n    // browser globals (HTMLElement) on non-browser environments, since having a class decorator\n    // causes TypeScript to preserve the constructor signature types.\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n\n  /**\n   * Announces a message to screenreaders.\n   * @param message Message to be announced to the screenreader\n   * @param politeness The politeness of the announcer element\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, politeness: AriaLivePoliteness = 'polite'): Promise<void> {\n    this._liveElement.textContent = '';\n\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return new Promise(resolve => {\n      setTimeout(() => {\n        this._liveElement.textContent = message;\n        resolve();\n      }, 100);\n    });\n  }\n\n  ngOnDestroy() {\n    if (this._liveElement && this._liveElement.parentNode) {\n      this._liveElement.parentNode.removeChild(this._liveElement);\n    }\n  }\n\n  private _createLiveElement(): Element {\n    let liveEl = this._document.createElement('div');\n\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n\n    this._document.body.appendChild(liveEl);\n\n    return liveEl;\n  }\n\n}\n\n/** @docs-private */\nexport function LIVE_ANNOUNCER_PROVIDER_FACTORY(\n    parentDispatcher: LiveAnnouncer, liveElement: any, _document: any) {\n  return parentDispatcher || new LiveAnnouncer(liveElement, _document);\n}\n\n/** @docs-private */\nexport const LIVE_ANNOUNCER_PROVIDER = {\n  // If there is already a LiveAnnouncer available, use that. Otherwise, provide a new one.\n  provide: LiveAnnouncer,\n  deps: [\n    [new Optional(), new SkipSelf(), LiveAnnouncer],\n    [new Optional(), new Inject(LIVE_ANNOUNCER_ELEMENT_TOKEN)],\n    DOCUMENT,\n  ],\n  useFactory: LIVE_ANNOUNCER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\nimport {FocusOrigin} from '../focus-monitor/focus-monitor';\n\n/**\n * This is the interface for focusable items (used by the FocusKeyManager).\n * Each item must know how to focus itself, whether or not it is currently disabled\n * and be able to supply it's label.\n */\nexport interface FocusableOption extends ListKeyManagerOption {\n  /** Focuses the `FocusableOption`. */\n  focus(origin?: FocusOrigin): void;\n}\n\nexport class FocusKeyManager<T> extends ListKeyManager<FocusableOption & T> {\n  private _origin: FocusOrigin = 'program';\n\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin: FocusOrigin): this {\n    this._origin = origin;\n    return this;\n  }\n\n  /**\n   * This method sets the active item to the item at the specified index.\n   * It also adds focuses the newly active item.\n   */\n  setActiveItem(index: number): void {\n    super.setActiveItem(index);\n\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\n\n/**\n * This is the interface for highlightable items (used by the ActiveDescendantKeyManager).\n * Each item must know how to style itself as active or inactive and whether or not it is\n * currently disabled.\n */\nexport interface Highlightable extends ListKeyManagerOption {\n  /** Applies the styles for an active item to this item. */\n  setActiveStyles(): void;\n\n  /** Applies the styles for an inactive item to this item. */\n  setInactiveStyles(): void;\n}\n\nexport class ActiveDescendantKeyManager<T> extends ListKeyManager<Highlightable & T> {\n\n  /**\n   * This method sets the active item to the item at the specified index.\n   * It also adds active styles to the newly active item and removes active\n   * styles from the previously active item.\n   */\n  setActiveItem(index: number): void {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {QueryList} from '@angular/core';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {\n  UP_ARROW,\n  DOWN_ARROW,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  TAB,\n  A,\n  Z,\n  ZERO,\n  NINE,\n} from '@angular/cdk/keycodes';\nimport {debounceTime} from 'rxjs/operators/debounceTime';\nimport {filter} from 'rxjs/operators/filter';\nimport {map} from 'rxjs/operators/map';\nimport {tap} from 'rxjs/operators/tap';\n\n/** This interface is for items that can be passed to a ListKeyManager. */\nexport interface ListKeyManagerOption {\n  /** Whether the option is disabled. */\n  disabled?: boolean;\n\n  /** Gets the label for this option. */\n  getLabel?(): string;\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nexport class ListKeyManager<T extends ListKeyManagerOption> {\n  private _activeItemIndex = -1;\n  private _activeItem: T;\n  private _wrap = false;\n  private _letterKeyStream = new Subject<string>();\n  private _typeaheadSubscription = Subscription.EMPTY;\n  private _vertical = true;\n  private _horizontal: 'ltr' | 'rtl' | null;\n\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  private _skipPredicateFn = (item: T) => item.disabled;\n\n  // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n  private _pressedLetters: string[] = [];\n\n  constructor(private _items: QueryList<T>) {\n    _items.changes.subscribe((newItems: QueryList<T>) => {\n      if (this._activeItem) {\n        const itemArray = newItems.toArray();\n        const newIndex = itemArray.indexOf(this._activeItem);\n\n        if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n          this._activeItemIndex = newIndex;\n        }\n      }\n    });\n  }\n\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  tabOut: Subject<void> = new Subject<void>();\n\n  /** Stream that emits whenever the active item of the list manager changes. */\n  change = new Subject<number>();\n\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate: (item: T) => boolean): this {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n\n  /**\n   * Turns on wrapping mode, which ensures that the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   */\n  withWrap(): this {\n    this._wrap = true;\n    return this;\n  }\n\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled: boolean = true): this {\n    this._vertical = enabled;\n    return this;\n  }\n\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction: 'ltr' | 'rtl' | null): this {\n    this._horizontal = direction;\n    return this;\n  }\n\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval: number = 200): this {\n    if (this._items.length && this._items.some(item => typeof item.getLabel !== 'function')) {\n      throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n\n    this._typeaheadSubscription.unsubscribe();\n\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._typeaheadSubscription = this._letterKeyStream.pipe(\n      tap(keyCode => this._pressedLetters.push(keyCode)),\n      debounceTime(debounceInterval),\n      filter(() => this._pressedLetters.length > 0),\n      map(() => this._pressedLetters.join(''))\n    ).subscribe(inputString => {\n      const items = this._items.toArray();\n\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < items.length + 1; i++) {\n        const index = (this._activeItemIndex + i) % items.length;\n        const item = items[index];\n\n        if (!this._skipPredicateFn(item) &&\n            item.getLabel!().toUpperCase().trim().indexOf(inputString) === 0) {\n\n          this.setActiveItem(index);\n          break;\n        }\n      }\n\n      this._pressedLetters = [];\n    });\n\n    return this;\n  }\n\n  /**\n   * Sets the active item to the item at the index specified.\n   * @param index The index of the item to be set as active.\n   */\n  setActiveItem(index: number): void {\n    const previousIndex = this._activeItemIndex;\n\n    this._activeItemIndex = index;\n    this._activeItem = this._items.toArray()[index];\n\n    if (this._activeItemIndex !== previousIndex) {\n      this.change.next(index);\n    }\n  }\n\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n\n      case DOWN_ARROW:\n        if (this._vertical) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case UP_ARROW:\n        if (this._vertical) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case RIGHT_ARROW:\n        if (this._horizontal === 'ltr') {\n          this.setNextItemActive();\n          break;\n        } else if (this._horizontal === 'rtl') {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case LEFT_ARROW:\n        if (this._horizontal === 'ltr') {\n          this.setPreviousItemActive();\n          break;\n        } else if (this._horizontal === 'rtl') {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      default:\n        // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n        // otherwise fall back to resolving alphanumeric characters via the keyCode.\n        if (event.key && event.key.length === 1) {\n          this._letterKeyStream.next(event.key.toLocaleUpperCase());\n        } else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n          this._letterKeyStream.next(String.fromCharCode(keyCode));\n        }\n\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n\n    this._pressedLetters = [];\n    event.preventDefault();\n  }\n\n  /** Index of the currently active item. */\n  get activeItemIndex(): number | null {\n    return this._activeItemIndex;\n  }\n\n  /** The active item. */\n  get activeItem(): T | null {\n    return this._activeItem;\n  }\n\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive(): void {\n    this._setActiveItemByIndex(0, 1);\n  }\n\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive(): void {\n    this._setActiveItemByIndex(this._items.length - 1, -1);\n  }\n\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive(): void {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive(): void {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive()\n                                            : this._setActiveItemByDelta(-1);\n  }\n\n  /**\n   * Allows setting of the activeItemIndex without any other effects.\n   * @param index The new activeItemIndex.\n   */\n  updateActiveItemIndex(index: number) {\n    this._activeItemIndex = index;\n  }\n\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  private _setActiveItemByDelta(delta: -1 | 1, items = this._items.toArray()): void {\n    this._wrap ? this._setActiveInWrapMode(delta, items)\n               : this._setActiveInDefaultMode(delta, items);\n  }\n\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  private _setActiveInWrapMode(delta: -1 | 1, items: T[]): void {\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + (delta * i) + items.length) % items.length;\n      const item = items[index];\n\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  private _setActiveInDefaultMode(delta: -1 | 1, items: T[]): void {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta, items);\n  }\n\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  private _setActiveItemByIndex(index: number, fallbackDelta: -1 | 1,\n                                items = this._items.toArray()): void {\n    if (!items[index]) {\n      return;\n    }\n\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n\n      if (!items[index]) {\n        return;\n      }\n    }\n\n    this.setActiveItem(index);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Inject, InjectionToken, Optional, SkipSelf} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId} from './aria-reference';\n\n/**\n * Interface used to register message elements and keep a count of how many registrations have\n * the same message and the reference to the message element used for the `aria-describedby`.\n */\nexport interface RegisteredMessage {\n  /** The element containing the message. */\n  messageElement: Element;\n\n  /** The number of elements that reference this message element via `aria-describedby`. */\n  referenceCount: number;\n}\n\n/** ID used for the body container where all messages are appended. */\nexport const MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n\n/** ID prefix used for each created message element. */\nexport const CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n\n/** Attribute given to each host element that is described by a message element. */\nexport const CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n\n/** Global map of all registered message elements that have been placed into the document. */\nconst messageRegistry = new Map<string, RegisteredMessage>();\n\n/** Container for all registered messages. */\nlet messagesContainer: HTMLElement | null = null;\n\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n * @docs-private\n */\n@Injectable()\nexport class AriaDescriber {\n  private _document: Document;\n\n  constructor(@Inject(DOCUMENT) _document: any) {\n    this._document = _document;\n  }\n\n  /**\n   * Adds to the host element an aria-describedby reference to a hidden element that contains\n   * the message. If the same message has already been registered, then it will reuse the created\n   * message element.\n   */\n  describe(hostElement: Element, message: string) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    if (!messageRegistry.has(message)) {\n      this._createMessageElement(message);\n    }\n\n    if (!this._isElementDescribedByMessage(hostElement, message)) {\n      this._addMessageReference(hostElement, message);\n    }\n  }\n\n  /** Removes the host element's aria-describedby reference to the message element. */\n  removeDescription(hostElement: Element, message: string) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    if (this._isElementDescribedByMessage(hostElement, message)) {\n      this._removeMessageReference(hostElement, message);\n    }\n\n    const registeredMessage = messageRegistry.get(message);\n    if (registeredMessage && registeredMessage.referenceCount === 0) {\n      this._deleteMessageElement(message);\n    }\n\n    if (messagesContainer && messagesContainer.childNodes.length === 0) {\n      this._deleteMessagesContainer();\n    }\n  }\n\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements =\n        this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}]`);\n\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n\n    if (messagesContainer) {\n      this._deleteMessagesContainer();\n    }\n\n    messageRegistry.clear();\n  }\n\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  private _createMessageElement(message: string) {\n    const messageElement = this._document.createElement('div');\n    messageElement.setAttribute('id', `${CDK_DESCRIBEDBY_ID_PREFIX}-${nextId++}`);\n    messageElement.appendChild(this._document.createTextNode(message)!);\n\n    if (!messagesContainer) { this._createMessagesContainer(); }\n    messagesContainer!.appendChild(messageElement);\n\n    messageRegistry.set(message, {messageElement, referenceCount: 0});\n  }\n\n  /** Deletes the message element from the global messages container. */\n  private _deleteMessageElement(message: string) {\n    const registeredMessage = messageRegistry.get(message);\n    const messageElement = registeredMessage && registeredMessage.messageElement;\n    if (messagesContainer && messageElement) {\n      messagesContainer.removeChild(messageElement);\n    }\n    messageRegistry.delete(message);\n  }\n\n  /** Creates the global container for all aria-describedby messages. */\n  private _createMessagesContainer() {\n    messagesContainer = this._document.createElement('div');\n    messagesContainer.setAttribute('id', MESSAGES_CONTAINER_ID);\n    messagesContainer.setAttribute('aria-hidden', 'true');\n    messagesContainer.style.display = 'none';\n    this._document.body.appendChild(messagesContainer);\n  }\n\n  /** Deletes the global messages container. */\n  private _deleteMessagesContainer() {\n    if (messagesContainer && messagesContainer.parentNode) {\n      messagesContainer.parentNode.removeChild(messagesContainer);\n      messagesContainer = null;\n    }\n  }\n\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  private _removeCdkDescribedByReferenceIds(element: Element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby')\n        .filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  private _addMessageReference(element: Element, message: string) {\n    const registeredMessage = messageRegistry.get(message)!;\n\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, '');\n\n    registeredMessage.referenceCount++;\n  }\n\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  private _removeMessageReference(element: Element, message: string) {\n    const registeredMessage = messageRegistry.get(message)!;\n    registeredMessage.referenceCount--;\n\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n\n  /** Returns true if the element has been described by the provided message ID. */\n  private _isElementDescribedByMessage(element: Element, message: string): boolean {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = messageRegistry.get(message);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n\n  /** Determines whether a message can be described on a particular element. */\n  private _canBeDescribed(element: Element, message: string): boolean {\n    return element.nodeType === this._document.ELEMENT_NODE && message != null &&\n        !!`${message}`.trim();\n  }\n\n}\n\n/** @docs-private */\nexport function ARIA_DESCRIBER_PROVIDER_FACTORY(parentDispatcher: AriaDescriber, _document: any) {\n  return parentDispatcher || new AriaDescriber(_document);\n}\n\n/** @docs-private */\nexport const ARIA_DESCRIBER_PROVIDER = {\n  // If there is already an AriaDescriber available, use that. Otherwise, provide a new one.\n  provide: AriaDescriber,\n  deps: [\n    [new Optional(), new SkipSelf(), AriaDescriber],\n    DOCUMENT as InjectionToken<any>\n  ],\n  useFactory: ARIA_DESCRIBER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** IDs are deliminated by an empty space, as per the spec. */\nconst ID_DELIMINATOR = ' ';\n\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function addAriaReferencedId(el: Element, attr: string, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  if (ids.some(existingId => existingId.trim() == id.trim())) { return; }\n  ids.push(id.trim());\n\n  el.setAttribute(attr, ids.join(ID_DELIMINATOR));\n}\n\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function removeAriaReferencedId(el: Element, attr: string, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  const filteredIds = ids.filter(val => val != id.trim());\n\n  el.setAttribute(attr, filteredIds.join(ID_DELIMINATOR));\n}\n\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function getAriaReferenceIds(el: Element, attr: string): string[] {\n  // Get string array of all individual ids (whitespace deliminated) in the attribute value\n  return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  Input,\n  NgZone,\n  OnDestroy,\n  AfterContentInit,\n  Injectable,\n  Inject,\n} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {take} from 'rxjs/operators/take';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {DOCUMENT} from '@angular/common';\n\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause to two to misalign.\n */\nexport class FocusTrap {\n  private _startAnchor: HTMLElement | null;\n  private _endAnchor: HTMLElement | null;\n\n  /** Whether the focus trap is active. */\n  get enabled(): boolean { return this._enabled; }\n  set enabled(val: boolean) {\n    this._enabled = val;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._startAnchor.tabIndex = this._endAnchor.tabIndex = this._enabled ? 0 : -1;\n    }\n  }\n  private _enabled: boolean = true;\n\n  constructor(\n    private _element: HTMLElement,\n    private _checker: InteractivityChecker,\n    private _ngZone: NgZone,\n    private _document: Document,\n    deferAnchors = false) {\n\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    if (this._startAnchor && this._startAnchor.parentNode) {\n      this._startAnchor.parentNode.removeChild(this._startAnchor);\n    }\n\n    if (this._endAnchor && this._endAnchor.parentNode) {\n      this._endAnchor.parentNode.removeChild(this._endAnchor);\n    }\n\n    this._startAnchor = this._endAnchor = null;\n  }\n\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   */\n  attachAnchors(): void {\n    if (!this._startAnchor) {\n      this._startAnchor = this._createAnchor();\n    }\n\n    if (!this._endAnchor) {\n      this._endAnchor = this._createAnchor();\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      this._startAnchor!.addEventListener('focus', () => {\n        this.focusLastTabbableElement();\n      });\n\n      this._endAnchor!.addEventListener('focus', () => {\n        this.focusFirstTabbableElement();\n      });\n\n      if (this._element.parentNode) {\n        this._element.parentNode.insertBefore(this._startAnchor!, this._element);\n        this._element.parentNode.insertBefore(this._endAnchor!, this._element.nextSibling);\n      }\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then either focuses the first element that the\n   * user specified, or the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusInitialElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement()));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusFirstTabbableElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement()));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusLastTabbableElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement()));\n    });\n  }\n\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  private _getRegionBoundary(bound: 'start' | 'end'): HTMLElement | null {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    let markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` +\n                                                 `[cdkFocusRegion${bound}], ` +\n                                                 `[cdk-focus-${bound}]`) as NodeListOf<HTMLElement>;\n\n    for (let i = 0; i < markers.length; i++) {\n      if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}',` +\n                     ` use 'cdkFocusRegion${bound}' instead.`, markers[i]);\n      } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}',` +\n                     ` use 'cdkFocusRegion${bound}' instead.`, markers[i]);\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ?\n        markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusInitialElement(): boolean {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` +\n                                                          `[cdkFocusInitial]`) as HTMLElement;\n\n    if (this._element.hasAttribute(`cdk-focus-initial`)) {\n      console.warn(`Found use of deprecated attribute 'cdk-focus-initial',` +\n                    ` use 'cdkFocusInitial' instead.`, this._element);\n    }\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n      return true;\n    }\n\n    return this.focusFirstTabbableElement();\n  }\n\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusFirstTabbableElement(): boolean {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusLastTabbableElement(): boolean {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  private _getFirstTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in DOM order. Note that IE doesn't have `children` for SVG so we fall\n    // back to `childNodes` which includes text nodes, comments etc.\n    let children = root.children || root.childNodes;\n\n    for (let i = 0; i < children.length; i++) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n        this._getFirstTabbableElement(children[i] as HTMLElement) :\n        null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  private _getLastTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in reverse DOM order.\n    let children = root.children || root.childNodes;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n        this._getLastTabbableElement(children[i] as HTMLElement) :\n        null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Creates an anchor element. */\n  private _createAnchor(): HTMLElement {\n    const anchor = this._document.createElement('div');\n    anchor.tabIndex = this._enabled ? 0 : -1;\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    return anchor;\n  }\n\n  /** Executes a function when the zone is stable. */\n  private _executeOnStable(fn: () => any): void {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.asObservable().pipe(take(1)).subscribe(fn);\n    }\n  }\n}\n\n\n/** Factory that allows easy instantiation of focus traps. */\n@Injectable()\nexport class FocusTrapFactory {\n  private _document: Document;\n\n  constructor(\n      private _checker: InteractivityChecker,\n      private _ngZone: NgZone,\n      @Inject(DOCUMENT) _document: any) {\n\n    this._document = _document;\n  }\n\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element: HTMLElement, deferCaptureElements: boolean = false): FocusTrap {\n    return new FocusTrap(\n        element, this._checker, this._ngZone, this._document, deferCaptureElements);\n  }\n}\n\n\n/**\n * Directive for trapping focus within a region.\n * @docs-private\n * @deprecated\n * @deletion-target 6.0.0\n */\n@Directive({\n  selector: 'cdk-focus-trap',\n})\nexport class FocusTrapDeprecatedDirective implements OnDestroy, AfterContentInit {\n  focusTrap: FocusTrap;\n\n  /** Whether the focus trap is active. */\n  @Input()\n  get disabled(): boolean { return !this.focusTrap.enabled; }\n  set disabled(val: boolean) {\n    this.focusTrap.enabled = !coerceBooleanProperty(val);\n  }\n\n  constructor(private _elementRef: ElementRef, private _focusTrapFactory: FocusTrapFactory) {\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy();\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n  }\n}\n\n\n/** Directive for trapping focus within a region. */\n@Directive({\n  selector: '[cdkTrapFocus]',\n  exportAs: 'cdkTrapFocus',\n})\nexport class CdkTrapFocus implements OnDestroy, AfterContentInit {\n  private _document: Document;\n\n  /** Underlying FocusTrap instance. */\n  focusTrap: FocusTrap;\n\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  private _previouslyFocusedElement: HTMLElement | null = null;\n\n  /** Whether the focus trap is active. */\n  @Input('cdkTrapFocus')\n  get enabled(): boolean { return this.focusTrap.enabled; }\n  set enabled(value: boolean) { this.focusTrap.enabled = coerceBooleanProperty(value); }\n\n  /**\n   * Whether the directive should automatially move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  @Input('cdkTrapFocusAutoCapture')\n  get autoCapture(): boolean { return this._autoCapture; }\n  set autoCapture(value: boolean) { this._autoCapture = coerceBooleanProperty(value); }\n  private _autoCapture: boolean;\n\n  constructor(\n      private _elementRef: ElementRef,\n      private _focusTrapFactory: FocusTrapFactory,\n      @Inject(DOCUMENT) _document: any) {\n\n    this._document = _document;\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy();\n\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n\n    if (this.autoCapture) {\n      this._previouslyFocusedElement = this._document.activeElement as HTMLElement;\n      this.focusTrap.focusInitialElementWhenReady();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\n\n\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\n@Injectable()\nexport class InteractivityChecker {\n\n  constructor(private _platform: Platform) {}\n\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element: HTMLElement): boolean {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element: HTMLElement): boolean {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element: HTMLElement): boolean {\n    // Nothing is tabbable on the the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n\n    const frameElement = getFrameElement(getWindow(element));\n\n    if (frameElement) {\n      const frameType = frameElement && frameElement.nodeName.toLowerCase();\n\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n\n      // Webkit and Blink consider anything inside of an <object> element as non-tabbable.\n      if ((this._platform.BLINK || this._platform.WEBKIT) && frameType === 'object') {\n        return false;\n      }\n\n      // Webkit and Blink disable tabbing to an element inside of an invisible frame.\n      if ((this._platform.BLINK || this._platform.WEBKIT) && !this.isVisible(frameElement)) {\n        return false;\n      }\n\n    }\n\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'iframe') {\n      // The frames may be tabbable depending on content, but it's not possibly to reliably\n      // investigate the content of the frames.\n      return false;\n    }\n\n    if (nodeName === 'audio') {\n      if (!element.hasAttribute('controls')) {\n        // By default an <audio> element without the controls enabled is not tabbable.\n        return false;\n      } else if (this._platform.BLINK) {\n        // In Blink <audio controls> elements are always tabbable.\n        return true;\n      }\n    }\n\n    if (nodeName === 'video') {\n      if (!element.hasAttribute('controls') && this._platform.TRIDENT) {\n        // In Trident a <video> element without the controls enabled is not tabbable.\n        return false;\n      } else if (this._platform.BLINK || this._platform.FIREFOX) {\n        // In Chrome and Firefox <video controls> elements are always tabbable.\n        return true;\n      }\n    }\n\n    if (nodeName === 'object' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      // In all Blink and WebKit based browsers <object> elements are never tabbable.\n      return false;\n    }\n\n    // In iOS the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n\n    return element.tabIndex >= 0;\n  }\n\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element: HTMLElement): boolean {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && this.isVisible(element);\n  }\n\n}\n\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window: Window) {\n  try {\n    return window.frameElement as HTMLElement;\n  } catch (e) {\n    return null;\n  }\n}\n\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element: HTMLElement): boolean {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight ||\n      (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n\n/** Gets whether an element's  */\nfunction isNativeFormElement(element: Node) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' ||\n      nodeName === 'select' ||\n      nodeName === 'button' ||\n      nodeName === 'textarea';\n}\n\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element: HTMLElement): boolean {\n  return isInputElement(element) && element.type == 'hidden';\n}\n\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element: HTMLElement): boolean {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n\n/** Gets whether an element is an input element. */\nfunction isInputElement(element: HTMLElement): element is HTMLInputElement {\n  return element.nodeName.toLowerCase() == 'input';\n}\n\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element: HTMLElement): element is HTMLAnchorElement {\n  return element.nodeName.toLowerCase() == 'a';\n}\n\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element: HTMLElement): boolean {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex');\n\n  // IE11 parses tabindex=\"\" as the value \"-32768\"\n  if (tabIndex == '-32768') {\n    return false;\n  }\n\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element: HTMLElement): number | null {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element: HTMLElement): boolean {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && (element as HTMLInputElement).type;\n\n  return inputType === 'text'\n      || inputType === 'password'\n      || nodeName === 'select'\n      || nodeName === 'textarea';\n}\n\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element: HTMLElement): boolean {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return isNativeFormElement(element) ||\n      isAnchorWithHref(element) ||\n      element.hasAttribute('contenteditable') ||\n      hasValidTabIndex(element);\n}\n\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node: HTMLElement): Window {\n  return node.ownerDocument.defaultView || window;\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n"], "names": ["CommonModule", "PlatformModule", "NgModule", "Optional", "SkipSelf", "NgZone", "Platform", "Output", "ElementRef", "Directive", "EventEmitter", "Injectable", "supportsPassiveEventListeners", "Subject", "observableOf", "Renderer2", "DOCUMENT", "Inject", "InjectionToken", "tslib_1.__extends", "A", "Z", "ZERO", "NINE", "LEFT_ARROW", "RIGHT_ARROW", "UP_ARROW", "DOWN_ARROW", "TAB", "tap", "debounceTime", "filter", "map", "Subscription", "Input", "coerceBooleanProperty", "take"], "mappings": ";;;;;;;;;;;;;AWAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF,AAED,AAAO,AACH,AAIA,AACH,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO,AAIN,AAED,AAAO,AAGN,AAAC;;;;;;;;;;;;ID9IA,SAAF,oBAAA,CAAsB,SAAmB,EAAzC;QAAsB,IAAtB,CAAA,SAA+B,GAAT,SAAS,CAAU;KAAI;;;;;;;;;;;;;IAQ3C,oBAAF,CAAA,SAAA,CAAA,UAAY;;;;;;IAAV,UAAW,OAAoB,EAAjC;;;QAGI,OAAO,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;KACzC,CAAH;;;;;;;;;;;;;;;;;;IAUE,oBAAF,CAAA,SAAA,CAAA,SAAW;;;;;;;;;IAAT,UAAU,OAAoB,EAAhC;QACI,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC;KACnF,CAAH;;;;;;;;;;;;;;;IASE,oBAAF,CAAA,SAAA,CAAA,UAAY;;;;;;;IAAV,UAAW,OAAoB,EAAjC;;QAEI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,qBAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAEzD,IAAI,YAAY,EAAE;YAChB,qBAAM,SAAS,GAAG,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;;YAGtE,IAAI,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzC,OAAO,KAAK,CAAC;aACd;;YAGD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,KAAK,QAAQ,EAAE;gBAC7E,OAAO,KAAK,CAAC;aACd;;YAGD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;gBACpF,OAAO,KAAK,CAAC;aACd;SAEF;QAED,qBAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC9C,qBAAI,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;YAC3C,OAAO,aAAa,KAAK,CAAC,CAAC,CAAC;SAC7B;QAED,IAAI,QAAQ,KAAK,QAAQ,EAAE;;;YAGzB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,QAAQ,KAAK,OAAO,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;;gBAErC,OAAO,KAAK,CAAC;aACd;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;gBAE/B,OAAO,IAAI,CAAC;aACb;SACF;QAED,IAAI,QAAQ,KAAK,OAAO,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;;gBAE/D,OAAO,KAAK,CAAC;aACd;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;;gBAEzD,OAAO,IAAI,CAAC;aACb;SACF;QAED,IAAI,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;;YAE5E,OAAO,KAAK,CAAC;SACd;;QAGD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE;YACrF,OAAO,KAAK,CAAC;SACd;QAED,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;KAC9B,CAAH;;;;;;;;;;;;;IAQE,oBAAF,CAAA,SAAA,CAAA,WAAa;;;;;;IAAX,UAAY,OAAoB,EAAlC;;;QAGI,OAAO,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;KAChG,CAAH;;QAxHA,EAAA,IAAA,EAACW,wBAAU,EAAX;;;;QAXA,EAAA,IAAA,EAAQL,8BAAQ,GAAhB;;IATA,OAAA,oBAAA,CAAA;;;;;;;;;AAqJA,SAAA,eAAA,CAAyB,MAAc,EAAvC;IACE,IAAI;QACF,yBAAO,MAAM,CAAC,YAA2B,EAAC;KAC3C;IAAC,wBAAO,CAAC,EAAE;QACV,OAAO,IAAI,CAAC;KACb;CACF;;;;;;AAGD,SAAA,WAAA,CAAqB,OAAoB,EAAzC;;;IAGE,OAAO,CAAC,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,YAAY;SAChD,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;CACxF;;;;;;AAGD,SAAA,mBAAA,CAA6B,OAAa,EAA1C;IACE,qBAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC9C,OAAO,QAAQ,KAAK,OAAO;QACvB,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,UAAU,CAAC;CAC7B;;;;;;AAGD,SAAA,aAAA,CAAuB,OAAoB,EAA3C;IACE,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC;CAC5D;;;;;;AAGD,SAAA,gBAAA,CAA0B,OAAoB,EAA9C;IACE,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;CACjE;;;;;;AAGD,SAAA,cAAA,CAAwB,OAAoB,EAA5C;IACE,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,OAAO,CAAC;CAClD;;;;;;AAGD,SAAA,eAAA,CAAyB,OAAoB,EAA7C;IACE,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC;CAC9C;;;;;;AAGD,SAAA,gBAAA,CAA0B,OAAoB,EAA9C;IACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;QACvE,OAAO,KAAK,CAAC;KACd;IAED,qBAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;;IAGhD,IAAI,QAAQ,IAAI,QAAQ,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;CACvD;;;;;;;AAMD,SAAA,gBAAA,CAA0B,OAAoB,EAA9C;IACE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE;QAC9B,OAAO,IAAI,CAAC;KACb;;IAGD,qBAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;IAEtE,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;CACxC;;;;;;AAGD,SAAA,wBAAA,CAAkC,OAAoB,EAAtD;IACE,qBAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC9C,qBAAI,SAAS,GAAG,QAAQ,KAAK,OAAO,IAAI,mBAAC,OAA2B,GAAE,IAAI,CAAC;IAE3E,OAAO,SAAS,KAAK,MAAM;WACpB,SAAS,KAAK,UAAU;WACxB,QAAQ,KAAK,QAAQ;WACrB,QAAQ,KAAK,UAAU,CAAC;CAChC;;;;;;;AAMD,SAAA,sBAAA,CAAgC,OAAoB,EAApD;;IAEE,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,KAAK,CAAC;KACd;IAED,OAAO,mBAAmB,CAAC,OAAO,CAAC;QAC/B,gBAAgB,CAAC,OAAO,CAAC;QACzB,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC;QACvC,gBAAgB,CAAC,OAAO,CAAC,CAAC;CAC/B;;;;;;AAGD,SAAA,SAAA,CAAmB,IAAiB,EAApC;IACE,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,MAAM,CAAC;CACjD;;;;;;;;;;;;;;ADhOD,IAAA,SAAA,kBAAA,YAAA;IAeE,SAAF,SAAA,CACY,QADZ,EAEY,QAFZ,EAGY,OAHZ,EAIY,SAJZ,EAKI,YAAoB,EALxB;QAKI,IAAJ,YAAA,KAAA,KAAA,CAAA,EAAI,EAAA,YAAJ,GAAA,KAAwB,CAAxB,EAAA;QAJY,IAAZ,CAAA,QAAoB,GAAR,QAAQ,CAApB;QACY,IAAZ,CAAA,QAAoB,GAAR,QAAQ,CAApB;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QACY,IAAZ,CAAA,SAAqB,GAAT,SAAS,CAArB;QANA,IAAA,CAAA,QAAA,GAA8B,IAAI,CAAlC;QASI,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;IApBD,MAAF,CAAA,cAAA,CAAM,SAAN,CAAA,SAAA,EAAA,SAAa,EAAb;;;;;;QAAE,YAAF,EAA2B,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;;;;;QAChD,UAAY,GAAY,EAA1B;YACI,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;YAEpB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;gBACxC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aAChF;SACF;;;KAPH,CAAA,CAAkD;;;;;;IAuBhD,SAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7D;QAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;YACjD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC5C,CAAH;;;;;;;;;;IAME,SAAF,CAAA,SAAA,CAAA,aAAe;;;;;IAAb,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAuBG;QAtBC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;SAC1C;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;SACxC;QAED,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAnC;6BACA,EAAM,KAAI,CAAC,YAAY,GAAE,gBAAgB,CAAC,OAAO,EAAE,YAAnD;gBACQ,KAAI,CAAC,wBAAwB,EAAE,CAAC;aACjC,CAAP,CAAA;YAEA,EAAM,KAAI,CAAC,UAAU,GAAE,gBAAgB,CAAC,OAAO,EAAE,YAAjD;gBACQ,KAAI,CAAC,yBAAyB,EAAE,CAAC;aAClC,CAAP,CAAA;YAEM,IAAI,KAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC5B,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,oBAAC,KAAI,CAAC,YAAY,IAAG,KAAI,CAAC,QAAQ,CAAC,CAAC;gBACzE,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,oBAAC,KAAI,CAAC,UAAU,IAAG,KAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;aACpF;SACF,CAAC,CAAC;KACJ,CAAH;;;;;;;;;;;;;IAQE,SAAF,CAAA,SAAA,CAAA,4BAA8B;;;;;;IAA5B,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAIG;QAHC,OAAO,IAAI,OAAO,CAAU,UAAA,OAAO,EAAvC;YACM,KAAI,CAAC,gBAAgB,CAAC,YAA5B,EAAkC,OAAA,OAAO,CAAC,KAAI,CAAC,mBAAmB,EAAE,CAAC,CAArE,EAAqE,CAAC,CAAC;SAClE,CAAC,CAAC;KACJ,CAAH;;;;;;;;;;;;;IAQE,SAAF,CAAA,SAAA,CAAA,kCAAoC;;;;;;IAAlC,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAIG;QAHC,OAAO,IAAI,OAAO,CAAU,UAAA,OAAO,EAAvC;YACM,KAAI,CAAC,gBAAgB,CAAC,YAA5B,EAAkC,OAAA,OAAO,CAAC,KAAI,CAAC,yBAAyB,EAAE,CAAC,CAA3E,EAA2E,CAAC,CAAC;SACxE,CAAC,CAAC;KACJ,CAAH;;;;;;;;;;;;;IAQE,SAAF,CAAA,SAAA,CAAA,iCAAmC;;;;;;IAAjC,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAIG;QAHC,OAAO,IAAI,OAAO,CAAU,UAAA,OAAO,EAAvC;YACM,KAAI,CAAC,gBAAgB,CAAC,YAA5B,EAAkC,OAAA,OAAO,CAAC,KAAI,CAAC,wBAAwB,EAAE,CAAC,CAA1E,EAA0E,CAAC,CAAC;SACvE,CAAC,CAAC;KACJ,CAAH;;;;;;IAOU,SAAV,CAAA,SAAA,CAAA,kBAA4B;;;;;IAA5B,UAA6B,KAAsB,EAAnD;;QAEI,qBAAI,OAAO,qBAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,oBAAjD,GAAsE,KAAK,GAA3E,KAAgF;aAC/B,iBAAjD,GAAmE,KAAK,GAAxE,KAA6E,CAAA;aAC5B,aAAjD,GAA+D,KAAK,GAApE,GAAuE,CAAA,CAA4B,CAAA,CAAC;QAEhG,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,YAAlC,GAA+C,KAAO,CAAC,EAAE;gBACjD,OAAO,CAAC,IAAI,CAAC,+CAArB,GAAqE,KAAK,GAA1E,IAA8E;qBACzD,sBAArB,GAA4C,KAAK,GAAjD,YAA6D,CAAA,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACpE;iBAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,mBAAzC,GAA6D,KAAO,CAAC,EAAE;gBAC/D,OAAO,CAAC,IAAI,CAAC,sDAArB,GAA4E,KAAK,GAAjF,IAAqF;qBAChE,sBAArB,GAA4C,KAAK,GAAjD,YAA6D,CAAA,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACpE;SACF;QAED,IAAI,KAAK,IAAI,OAAO,EAAE;YACpB,OAAO,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnF;QACD,OAAO,OAAO,CAAC,MAAM;YACjB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;;;;;;;;;IAOhF,SAAF,CAAA,SAAA,CAAA,mBAAqB;;;;IAAnB,YAAF;;QAEI,qBAAM,iBAAiB,qBAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,uBAAuB;YACvB,mBAAmB,CAAgB,CAAA,CAAC;QAE1F,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;YACnD,OAAO,CAAC,IAAI,CAAC,wDAAwD;gBACvD,iCAAiC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjE;QAED,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC;KACzC,CAAH;;;;;;;;;IAME,SAAF,CAAA,SAAA,CAAA,yBAA2B;;;;IAAzB,YAAF;QACI,qBAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;SAC3B;QAED,OAAO,CAAC,CAAC,iBAAiB,CAAC;KAC5B,CAAH;;;;;;;;;IAME,SAAF,CAAA,SAAA,CAAA,wBAA0B;;;;IAAxB,YAAF;QACI,qBAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAEzD,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;SAC3B;QAED,OAAO,CAAC,CAAC,iBAAiB,CAAC;KAC5B,CAAH;;;;;;IAGU,SAAV,CAAA,SAAA,CAAA,wBAAkC;;;;;IAAlC,UAAmC,IAAiB,EAApD;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC;SACb;;;QAID,qBAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC;QAEhD,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,qBAAI,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY;gBACtE,IAAI,CAAC,wBAAwB,mBAAC,QAAQ,CAAC,CAAC,CAAgB,EAAC;gBACzD,IAAI,CAAC;YAEP,IAAI,aAAa,EAAE;gBACjB,OAAO,aAAa,CAAC;aACtB;SACF;QAED,OAAO,IAAI,CAAC;;;;;;;IAIN,SAAV,CAAA,SAAA,CAAA,uBAAiC;;;;;IAAjC,UAAkC,IAAiB,EAAnD;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC;SACb;;QAGD,qBAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC;QAEhD,KAAK,qBAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7C,qBAAI,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY;gBACtE,IAAI,CAAC,uBAAuB,mBAAC,QAAQ,CAAC,CAAC,CAAgB,EAAC;gBACxD,IAAI,CAAC;YAEP,IAAI,aAAa,EAAE;gBACjB,OAAO,aAAa,CAAC;aACtB;SACF;QAED,OAAO,IAAI,CAAC;;;;;;IAIN,SAAV,CAAA,SAAA,CAAA,aAAuB;;;;;QACnB,qBAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC5C,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAC9C,OAAO,MAAM,CAAC;;;;;;;IAIR,SAAV,CAAA,SAAA,CAAA,gBAA0B;;;;;IAA1B,UAA2B,EAAa,EAAxC;QACI,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,EAAE,EAAE,CAAC;SACN;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC8B,wBAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;SAClE;;IAlRL,OAAA,SAAA,CAAA;CAoRA,EAAA,CAAC,CAAA;;;;;IAQC,SAAF,gBAAA,CACc,QADd,EAEc,OAFd,EAGwB,SAHxB,EAAA;QACc,IAAd,CAAA,QAAsB,GAAR,QAAQ,CAAtB;QACc,IAAd,CAAA,OAAqB,GAAP,OAAO,CAArB;QAGI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;;;;;;;;;;;;;;;IASD,gBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;;;IAAN,UAAO,OAAoB,EAAE,oBAAqC,EAApE;QAA+B,IAA/B,oBAAA,KAAA,KAAA,CAAA,EAA+B,EAAA,oBAA/B,GAAA,KAAoE,CAApE,EAAA;QACI,OAAO,IAAI,SAAS,CAChB,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;KACjF,CAAH;;QAtBA,EAAA,IAAA,EAACzB,wBAAU,EAAX;;;;QApQA,EAAA,IAAA,EAAQ,oBAAoB,GAA5B;QARA,EAAA,IAAA,EAAEN,oBAAM,GAAR;QAmRA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAOY,oBAAM,EAAb,IAAA,EAAA,CAAcD,wBAAQ,EAAtB,EAAA,EAAA,EAAA;;IA/RA,OAAA,gBAAA,CAAA;;;;;;;;;IAqUE,SAAF,4BAAA,CAAsB,WAAuB,EAAU,iBAAmC,EAA1F;QAAsB,IAAtB,CAAA,WAAiC,GAAX,WAAW,CAAY;QAAU,IAAvD,CAAA,iBAAwE,GAAjB,iBAAiB,CAAkB;QACtF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;KACtF;IAPH,MAAA,CAAA,cAAA,CAAM,4BAAN,CAAA,SAAA,EAAA,UAAc,EAAd;;;;;QAAA,YAAA,EAA4B,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAA3D;;;;;QACE,UAAa,GAAY,EAA3B;YACI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAACmB,2CAAqB,CAAC,GAAG,CAAC,CAAC;SACtD;;;;;;;IAMD,4BAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAH;;;;IAEE,4BAAF,CAAA,SAAA,CAAA,kBAAoB;;;IAAlB,YAAF;QACI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;KAChC,CAAH;;QAvBA,EAAA,IAAA,EAAC1B,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,gBAAgB;iBAC3B,EAAD,EAAA;;;;QAhTA,EAAA,IAAA,EAAED,wBAAU,GAAZ;QA+QA,EAAA,IAAA,EAAa,gBAAgB,GAA7B;;;QAsCA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAG0B,mBAAK,EAAR,EAAA;;IA/TA,OAAA,4BAAA,CAAA;;;;;;IA+WE,SAAF,YAAA,CACc,WADd,EAEc,iBAFd,EAGwB,SAHxB,EAAA;QACc,IAAd,CAAA,WAAyB,GAAX,WAAW,CAAzB;QACc,IAAd,CAAA,iBAA+B,GAAjB,iBAAiB,CAA/B;;;;QAlBA,IAAA,CAAA,yBAAA,GAA0D,IAAI,CAA9D;QAqBI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;KACtF;IAnBH,MAAA,CAAA,cAAA,CAAM,YAAN,CAAA,SAAA,EAAA,SAAa,EAAb;;;;;QAAA,YAAA,EAA2B,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAzD;;;;;QACE,UAAY,KAAc,EAA5B,EAAgC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAGC,2CAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;;;;IAOxF,MAAA,CAAA,cAAA,CAAM,YAAN,CAAA,SAAA,EAAA,aAAiB,EAAjB;;;;;;QAAA,YAAA,EAA+B,OAAO,IAAI,CAAC,YAAY,CAAC,EAAxD;;;;;QACE,UAAgB,KAAc,EAAhC,EAAoC,IAAI,CAAC,YAAY,GAAGA,2CAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;;;;;;;IAYrF,YAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;;;QAIzB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACvC;KACF,CAAH;;;;IAEE,YAAF,CAAA,SAAA,CAAA,kBAAoB;;;IAAlB,YAAF;QACI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;QAE/B,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,yBAAyB,qBAAG,IAAI,CAAC,SAAS,CAAC,aAA4B,CAAA,CAAC;YAC7E,IAAI,CAAC,SAAS,CAAC,4BAA4B,EAAE,CAAC;SAC/C;KACF,CAAH;;QAtDA,EAAA,IAAA,EAAC1B,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,gBAAgB;oBAC1B,QAAQ,EAAE,cAAc;iBACzB,EAAD,EAAA;;;;QA7UA,EAAA,IAAA,EAAED,wBAAU,GAAZ;QA+QA,EAAA,IAAA,EAAa,gBAAgB,GAA7B;QAyFA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAOS,oBAAM,EAAb,IAAA,EAAA,CAAcD,wBAAQ,EAAtB,EAAA,EAAA,EAAA;;;QAhBA,SAAA,EAAA,CAAA,EAAA,IAAA,EAAGkB,mBAAK,EAAR,IAAA,EAAA,CAAS,cAAc,EAAvB,EAAA,EAAA;QAQA,aAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,yBAAyB,EAAlC,EAAA,EAAA;;IA1WA,OAAA,YAAA,CAAA;CAwVA,EAAA,CAAA,CAAA;;;;;;;;;;AD/UA,IAAM,cAAc,GAAG,GAAG,CAAC;;;;;;;;;AAM3B,SAAA,mBAAA,CAAoC,EAAW,EAAE,IAAY,EAAE,EAAU,EAAzE;IACE,qBAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC1C,IAAI,GAAG,CAAC,IAAI,CAAC,UAAA,UAAU,EAAzB,EAA6B,OAAA,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,CAA3D,EAA2D,CAAC,EAAE;QAAE,OAAO;KAAE;IACvE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAEpB,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;CACjD;;;;;;;;;AAMD,SAAA,sBAAA,CAAuC,EAAW,EAAE,IAAY,EAAE,EAAU,EAA5E;IACE,qBAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC1C,qBAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,UAAA,GAAG,EAApC,EAAwC,OAAA,GAAG,IAAI,EAAE,CAAC,IAAI,EAAE,CAAxD,EAAwD,CAAC,CAAC;IAExD,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;CACzD;;;;;;;;AAMD,SAAA,mBAAA,CAAoC,EAAW,EAAE,IAAY,EAA7D;;IAEE,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;CAC1D;;;;;;;;;;;;;;;;ADhBD,IAAa,qBAAqB,GAAG,mCAAmC,CAAC;;;;AAGzE,IAAa,yBAAyB,GAAG,yBAAyB,CAAC;;;;AAGnE,IAAa,8BAA8B,GAAG,sBAAsB,CAAC;;;;AAGrE,IAAI,MAAM,GAAG,CAAC,CAAC;;;;AAGf,IAAM,eAAe,GAAG,IAAI,GAAG,EAA6B,CAAC;;;;AAG7D,IAAI,iBAAiB,GAAuB,IAAI,CAAC;;;;;;;;IAY/C,SAAF,aAAA,CAAgC,SAAhC,EAAA;QACI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;;;;;;;;;;;;;;IAOD,aAAF,CAAA,SAAA,CAAA,QAAU;;;;;;;;IAAR,UAAS,WAAoB,EAAE,OAAe,EAAhD;QACI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC/C,OAAO;SACR;QAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACjC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC5D,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACjD;KACF,CAAH;;;;;;;;IAGE,aAAF,CAAA,SAAA,CAAA,iBAAmB;;;;;;IAAjB,UAAkB,WAAoB,EAAE,OAAe,EAAzD;QACI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC/C,OAAO;SACR;QAED,IAAI,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC3D,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACpD;QAED,qBAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,KAAK,CAAC,EAAE;YAC/D,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;SACrC;QAED,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAClE,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACjC;KACF,CAAH;;;;;;IAGE,aAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,YAAF;QACI,qBAAM,iBAAiB,GACnB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,GADxC,GAC4C,8BAA8B,GAD1E,GAC6E,CAAC,CAAC;QAE3E,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,IAAI,CAAC,iCAAiC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,8BAA8B,CAAC,CAAC;SACtE;QAED,IAAI,iBAAiB,EAAE;YACrB,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACjC;QAED,eAAe,CAAC,KAAK,EAAE,CAAC;KACzB,CAAH;;;;;;;IAMU,aAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;;IAA/B,UAAgC,OAAe,EAA/C;QACI,qBAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3D,cAAc,CAAC,YAAY,CAAC,IAAI,EAAK,yBAAyB,GAAlE,GAAA,GAAsE,MAAM,EAAI,CAAC,CAAC;QAC9E,cAAc,CAAC,WAAW,oBAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,GAAE,CAAC;QAEpE,IAAI,CAAC,iBAAiB,EAAE;YAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC;SAAE;QAChE,EAAI,iBAAiB,GAAE,WAAW,CAAC,cAAc,CAAjD,CAAA;QAEI,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,EAAC,cAAc,EAAhD,cAAgD,EAAE,cAAc,EAAE,CAAC,EAAC,CAAC,CAAC;;;;;;;IAI5D,aAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;IAA/B,UAAgC,OAAe,EAA/C;QACI,qBAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,qBAAM,cAAc,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC;QAC7E,IAAI,iBAAiB,IAAI,cAAc,EAAE;YACvC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;SAC/C;QACD,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;;;;;;IAI1B,aAAV,CAAA,SAAA,CAAA,wBAAkC;;;;;QAC9B,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACxD,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;QAC5D,iBAAiB,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACtD,iBAAiB,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;;;;;;IAI7C,aAAV,CAAA,SAAA,CAAA,wBAAkC;;;;;QAC9B,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,UAAU,EAAE;YACrD,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;YAC5D,iBAAiB,GAAG,IAAI,CAAC;SAC1B;;;;;;;IAIK,aAAV,CAAA,SAAA,CAAA,iCAA2C;;;;;IAA3C,UAA4C,OAAgB,EAA5D;;QAEI,qBAAM,oBAAoB,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC;aACxE,MAAM,CAAC,UAAA,EAAE,EAAlB,EAAsB,OAAA,EAAE,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAhE,EAAgE,CAAC,CAAC;QAC9D,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;;;;;;;IAOnE,aAAV,CAAA,SAAA,CAAA,oBAA8B;;;;;;;IAA9B,UAA+B,OAAgB,EAAE,OAAe,EAAhE;QACI,qBAAM,iBAAiB,sBAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAC,CAAC;;;QAIxD,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACtF,OAAO,CAAC,YAAY,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAAC;QAEzD,iBAAiB,CAAC,cAAc,EAAE,CAAC;;;;;;;;;IAO7B,aAAV,CAAA,SAAA,CAAA,uBAAiC;;;;;;;IAAjC,UAAkC,OAAgB,EAAE,OAAe,EAAnE;QACI,qBAAM,iBAAiB,sBAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAC,CAAC;QACxD,iBAAiB,CAAC,cAAc,EAAE,CAAC;QAEnC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACzF,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC,CAAC;;;;;;;;IAIlD,aAAV,CAAA,SAAA,CAAA,4BAAsC;;;;;;IAAtC,UAAuC,OAAgB,EAAE,OAAe,EAAxE;QACI,qBAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACtE,qBAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,qBAAM,SAAS,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;QAE3E,OAAO,CAAC,CAAC,SAAS,IAAI,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;;;;;;;;IAItD,aAAV,CAAA,SAAA,CAAA,eAAyB;;;;;;IAAzB,UAA0B,OAAgB,EAAE,OAAe,EAA3D;QACI,OAAO,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,OAAO,IAAI,IAAI;YACtE,CAAC,CAAC,CAAA,EAAV,GAAa,OAAS,EAAC,IAAI,EAAE,CAAC;;;QAzJ9B,EAAA,IAAA,EAACvB,wBAAU,EAAX;;;;QAIA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAeM,oBAAM,EAArB,IAAA,EAAA,CAAsBD,wBAAQ,EAA9B,EAAA,EAAA,EAAA;;IApDA,OAAA,aAAA,CAAA;;;;;;;;AA+MA,SAAA,+BAAA,CAAgD,gBAA+B,EAAE,SAAc,EAA/F;IACE,OAAO,gBAAgB,IAAI,IAAI,aAAa,CAAC,SAAS,CAAC,CAAC;CACzD;;;;AAGD,IAAa,uBAAuB,GAAG;;IAErC,OAAO,EAAE,aAAa;IACtB,IAAI,EAAE;QACJ,CAAC,IAAIb,sBAAQ,EAAE,EAAE,IAAIC,sBAAQ,EAAE,EAAE,aAAa,CAAC;0BAC/CY,wBAA+B;KAChC;IACD,UAAU,EAAE,+BAA+B;CAC5C,CAAC;;;;;;;;;;;;;;;;ADpLF,IAAA,cAAA,kBAAA,YAAA;IAkBE,SAAF,cAAA,CAAsB,MAAoB,EAA1C;QAAE,IAAF,KAAA,GAAA,IAAA,CAWG;QAXmB,IAAtB,CAAA,MAA4B,GAAN,MAAM,CAAc;QAjB1C,IAAA,CAAA,gBAAA,GAA6B,CAAC,CAAC,CAA/B;QAEA,IAAA,CAAA,KAAA,GAAkB,KAAK,CAAvB;QACA,IAAA,CAAA,gBAAA,GAA6B,IAAIH,oBAAO,EAAU,CAAlD;QACA,IAAA,CAAA,sBAAA,GAAmCoB,8BAAY,CAAC,KAAK,CAArD;QACA,IAAA,CAAA,SAAA,GAAsB,IAAI,CAA1B;;;;;QAOA,IAAA,CAAA,gBAAA,GAA6B,UAAC,IAAO,EAArC,EAA0C,OAAA,IAAI,CAAC,QAAQ,CAAvD,EAAuD,CAAvD;QAGA,IAAA,CAAA,eAAA,GAAsC,EAAE,CAAxC;;;;;QAmBA,IAAA,CAAA,MAAA,GAA0B,IAAIpB,oBAAO,EAAQ,CAA7C;;;;QAGA,IAAA,CAAA,MAAA,GAAW,IAAIA,oBAAO,EAAU,CAAhC;QAnBI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,UAAC,QAAsB,EAApD;YACM,IAAI,KAAI,CAAC,WAAW,EAAE;gBACpB,qBAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrC,qBAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,KAAI,CAAC,WAAW,CAAC,CAAC;gBAErD,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,KAAK,KAAI,CAAC,gBAAgB,EAAE;oBACvD,KAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;iBAClC;aACF;SACF,CAAC,CAAC;KACJ;;;;;;;;;;;;IAgBD,cAAF,CAAA,SAAA,CAAA,aAAe;;;;;;IAAb,UAAc,SAA+B,EAA/C;QACI,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,cAAF,CAAA,SAAA,CAAA,QAAU;;;;;IAAR,YAAF;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,cAAF,CAAA,SAAA,CAAA,uBAAyB;;;;;IAAvB,UAAwB,OAAuB,EAAjD;QAA0B,IAA1B,OAAA,KAAA,KAAA,CAAA,EAA0B,EAAA,OAA1B,GAAA,IAAiD,CAAjD,EAAA;QACI,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QACzB,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;;IAOE,cAAF,CAAA,SAAA,CAAA,yBAA2B;;;;;;IAAzB,UAA0B,SAA+B,EAA3D;QACI,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC7B,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,cAAF,CAAA,SAAA,CAAA,aAAe;;;;;IAAb,UAAc,gBAA8B,EAA9C;QAAE,IAAF,KAAA,GAAA,IAAA,CAoCG;QApCa,IAAhB,gBAAA,KAAA,KAAA,CAAA,EAAgB,EAAA,gBAAhB,GAAA,GAA8C,CAA9C,EAAA;QACI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,IAAI,EAAnD,EAAuD,OAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAA1F,EAA0F,CAAC,EAAE;YACvF,MAAM,KAAK,CAAC,8EAA8E,CAAC,CAAC;SAC7F;QAED,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;;;;QAK1C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CACtDgB,sBAAG,CAAC,UAAA,OAAO,EADjB,EACqB,OAAA,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CADvD,EACuD,CAAC,EAClDC,wCAAY,CAAC,gBAAgB,CAAC,EAC9BC,4BAAM,CAAC,YAHb,EAGmB,OAAA,KAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAHlD,EAGkD,CAAC,EAC7CC,sBAAG,CAAC,YAJV,EAIgB,OAAA,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAJ7C,EAI6C,CAAC,CACzC,CAAC,SAAS,CAAC,UAAA,WAAW,EAL3B;YAMM,qBAAM,KAAK,GAAG,KAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;;;YAIpC,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzC,qBAAM,KAAK,GAAG,CAAC,KAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;gBACzD,qBAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;gBAE1B,IAAI,CAAC,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAxC,EACY,IAAI,CAAC,QAAQ,IADzB,CAC6B,WAAW,EADxC,CAC2C,IAAI,EAD/C,CACkD,OAAO,CAAC,WAAW,CADrE,KAC2E,CAAC,EAAE;oBAEpE,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAC1B,MAAM;iBACP;aACF;YAED,KAAI,CAAC,eAAe,GAAG,EAAE,CAAC;SAC3B,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,cAAF,CAAA,SAAA,CAAA,aAAe;;;;;IAAb,UAAc,KAAa,EAA7B;QACI,qBAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,gBAAgB,KAAK,aAAa,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;KACF,CAAH;;;;;;;;;;IAME,cAAF,CAAA,SAAA,CAAA,SAAW;;;;;IAAT,UAAU,KAAoB,EAAhC;QACI,qBAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAE9B,QAAQ,OAAO;YACb,KAAKJ,yBAAG;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACnB,OAAO;YAET,KAAKD,gCAAU;gBACb,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH,KAAKD,8BAAQ;gBACX,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH,KAAKD,iCAAW;gBACd,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBACrC,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH,KAAKD,gCAAU;gBACb,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBAC9B,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,MAAM;iBACP;qBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;oBACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;iBACP;qBAAM;oBACL,OAAO;iBACR;YAEH;;;gBAGE,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;iBAC3D;qBAAM,IAAI,CAAC,OAAO,IAAIJ,uBAAC,IAAI,OAAO,IAAIC,uBAAC,MAAM,OAAO,IAAIC,0BAAI,IAAI,OAAO,IAAIC,0BAAI,CAAC,EAAE;oBACjF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC1D;;;gBAID,OAAO;SACV;QAED,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;KACxB,CAAH;IAGE,MAAF,CAAA,cAAA,CAAM,cAAN,CAAA,SAAA,EAAA,iBAAqB,EAArB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAC9B;;;KAAH,CAAA,CAAG;IAGD,MAAF,CAAA,cAAA,CAAM,cAAN,CAAA,SAAA,EAAA,YAAgB,EAAhB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,WAAW,CAAC;SACzB;;;KAAH,CAAA,CAAG;;;;;;IAGD,cAAF,CAAA,SAAA,CAAA,kBAAoB;;;;IAAlB,YAAF;QACI,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC,CAAH;;;;;;IAGE,cAAF,CAAA,SAAA,CAAA,iBAAmB;;;;IAAjB,YAAF;QACI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACxD,CAAH;;;;;;IAGE,cAAF,CAAA,SAAA,CAAA,iBAAmB;;;;IAAjB,YAAF;QACI,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;KACvF,CAAH;;;;;;IAGE,cAAF,CAAA,SAAA,CAAA,qBAAuB;;;;IAArB,YAAF;QACI,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE;cACxB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1E,CAAH;;;;;;;;;;IAME,cAAF,CAAA,SAAA,CAAA,qBAAuB;;;;;IAArB,UAAsB,KAAa,EAArC;QACI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC/B,CAAH;;;;;;;;;IAOU,cAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;;;;IAA/B,UAAgC,KAAa,EAAE,KAA6B,EAA5E;QAA+C,IAA/C,KAAA,KAAA,KAAA,CAAA,EAA+C,EAAA,KAA/C,GAAuD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAA5E,EAAA;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC;cACvC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;;;;;;;;;IAQlD,cAAV,CAAA,SAAA,CAAA,oBAA8B;;;;;;;;IAA9B,UAA+B,KAAa,EAAE,KAAU,EAAxD;QACI,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,qBAAM,KAAK,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;YAClF,qBAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,OAAO;aACR;SACF;;;;;;;;;;IAQK,cAAV,CAAA,SAAA,CAAA,uBAAiC;;;;;;;;IAAjC,UAAkC,KAAa,EAAE,KAAU,EAA3D;QACI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;;;;;;;;;;;IAQlE,cAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;;;;;IAA/B,UAAgC,KAAa,EAAE,aAAqB,EACpC,KAA6B,EAD7D;QACgC,IAAhC,KAAA,KAAA,KAAA,CAAA,EAAgC,EAAA,KAAhC,GAAwC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAA7D,EAAA;QACI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACjB,OAAO;SACR;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1C,KAAK,IAAI,aAAa,CAAC;YAEvB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACjB,OAAO;aACR;SACF;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;IAjV9B,OAAA,cAAA,CAAA;CAmVA,EAAA,CAAC,CAAA;;;;;;;;;;;;;;AD5TD,IAAA,0BAAA,kBAAA,UAAA,MAAA,EAAA;IAAmDJ,SAAnD,CAAA,0BAAA,EAAA,MAAA,CAAA,CAAoF;;;;;;;;;;;;;;;;IAOlF,0BAAF,CAAA,SAAA,CAAA,aAAe;;;;;;;IAAb,UAAc,KAAa,EAA7B;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;SACrC;QACD,MAAJ,CAAA,SAAA,CAAU,aAAa,CAAvB,IAAA,CAAA,IAAA,EAAwB,KAAK,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;SACnC;KACF,CAAH;IAtCA,OAAA,0BAAA,CAAA;CAuBA,CAAmD,cAAc,CAAjE,CAiBC,CAAA;;;;;;;;;;;;;;ADnBD,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;IAAwCA,SAAxC,CAAA,eAAA,EAAA,MAAA,CAAA,CAA2E;;;QAC3E,KAAA,CAAA,OAAA,GAAiC,SAAS,CAA1C;;;;;;;;;;;;IAME,eAAF,CAAA,SAAA,CAAA,cAAgB;;;;;IAAd,UAAe,MAAmB,EAApC;QACI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;IAME,eAAF,CAAA,SAAA,CAAA,aAAe;;;;;;IAAb,UAAc,KAAa,EAA7B;QACI,MAAJ,CAAA,SAAA,CAAU,aAAa,CAAvB,IAAA,CAAA,IAAA,EAAwB,KAAK,CAAC,CAAC;QAE3B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrC;KACF,CAAH;IA3CA,OAAA,eAAA,CAAA;CAqBA,CAAwC,cAAc,CAAtD,CAuBC,CAAA;;;;;;;ADpCD,IAWa,4BAA4B,GAAG,IAAID,4BAAc,CAAc,sBAAsB,CAAC,CAAC;;IASlG,SAAF,aAAA,CACwD,YADxD,EAEgC,SAFhC,EAAA;QAEgC,IAAhC,CAAA,SAAyC,GAAT,SAAS,CAAzC;;;;QAKI,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC/D;;;;;;;;;;;;;IAQD,aAAF,CAAA,SAAA,CAAA,QAAU;;;;;;IAAR,UAAS,OAAe,EAAE,UAAyC,EAArE;QAAE,IAAF,KAAA,GAAA,IAAA,CAiBG;QAjByB,IAA5B,UAAA,KAAA,KAAA,CAAA,EAA4B,EAAA,UAA5B,GAAA,QAAqE,CAArE,EAAA;QACI,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,EAAE,CAAC;;QAGnC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;;;;;;QAOxD,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO,EAA9B;YACM,UAAU,CAAC,YAAjB;gBACQ,KAAI,CAAC,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;gBACxC,OAAO,EAAE,CAAC;aACX,EAAE,GAAG,CAAC,CAAC;SACT,CAAC,CAAC;KACJ,CAAH;;;;IAEE,aAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7D;KACF,CAAH;;;;IAEU,aAAV,CAAA,SAAA,CAAA,kBAA4B;;;;QACxB,qBAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEjD,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC5C,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAC3C,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAExC,OAAO,MAAM,CAAC;;;QAtDlB,EAAA,IAAA,EAACP,wBAAU,EAAX;;;;QAKA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAOR,sBAAQ,EAAf,EAAA,EAAA,IAAA,EAAmBc,oBAAM,EAAzB,IAAA,EAAA,CAA0B,4BAA4B,EAAtD,EAAA,EAAA,EAAA;QACA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAOA,oBAAM,EAAb,IAAA,EAAA,CAAcD,wBAAQ,EAAtB,EAAA,EAAA,EAAA;;IA9BA,OAAA,aAAA,CAAA;;;;;;;;;AAoFA,SAAA,+BAAA,CACI,gBAA+B,EAAE,WAAgB,EAAE,SAAc,EADrE;IAEE,OAAO,gBAAgB,IAAI,IAAI,aAAa,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;CACtE;;;;AAGD,IAAa,uBAAuB,GAAG;;IAErC,OAAO,EAAE,aAAa;IACtB,IAAI,EAAE;QACJ,CAAC,IAAIb,sBAAQ,EAAE,EAAE,IAAIC,sBAAQ,EAAE,EAAE,aAAa,CAAC;QAC/C,CAAC,IAAID,sBAAQ,EAAE,EAAE,IAAIc,oBAAM,CAAC,4BAA4B,CAAC,CAAC;QAC1DD,wBAAQ;KACT;IACD,UAAU,EAAE,+BAA+B;CAC5C,CAAC;;;;;;;;ADvEF,IAAa,eAAe,GAAG,GAAG,CAAC;;;;;IA8CjC,SAAF,YAAA,CAAsB,OAAe,EAAU,SAAmB,EAAlE;QAAsB,IAAtB,CAAA,OAA6B,GAAP,OAAO,CAAQ;QAAU,IAA/C,CAAA,SAAwD,GAAT,SAAS,CAAU;;;;QA7BlE,IAAA,CAAA,OAAA,GAAiC,IAAI,CAArC;;;;QAMA,IAAA,CAAA,cAAA,GAA2B,KAAK,CAAhC;;;;QAeA,IAAA,CAAA,YAAA,GAAyB,IAAI,GAAG,EAAqC,CAArE;;;;QAGA,IAAA,CAAA,0BAAA,GAAuC,YAAvC,GAA+C,CAA/C;;;;QAGA,IAAA,CAAA,sBAAA,GAAmC,CAAC,CAApC;KAEsE;;;;;;;IAiBpE,YAAF,CAAA,SAAA,CAAA,OAAS;;;;;;IAAP,UACI,OAAoB,EACpB,QAA8B,EAC9B,aAAuB,EAH7B;QAAE,IAAF,KAAA,GAAA,IAAA,CA6CG;;QAxCC,IAAI,EAAE,QAAQ,YAAYD,uBAAS,CAAC,EAAE;YACpC,aAAa,GAAG,QAAQ,CAAC;SAC1B;QACD,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC;;QAGhC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAOD,qBAAY,CAAC,IAAI,CAAC,CAAC;SAC3B;;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAClC,qBAAI,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtD,EAAM,UAAU,GAAE,aAAa,GAAG,aAAa,CAA/C;YACM,wBAAN,EAAa,UAAU,GAAE,OAAO,CAAC,YAAY,EAA7C,CAAgD;SAC3C;;QAGD,qBAAI,IAAI,GAAyB;YAC/B,QAAQ,EAAE,YAAhB,GAAwB;YAClB,aAAa,EAAE,aAAa;YAC5B,OAAO,EAAE,IAAID,oBAAO,EAAe;SACpC,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,+BAA+B,EAAE,CAAC;;QAGvC,qBAAI,aAAa,GAAG,UAAC,KAAiB,EAA1C,EAA+C,OAAA,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAA5E,EAA4E,CAAC;QACzE,qBAAI,YAAY,GAAG,UAAC,KAAiB,EAAzC,EAA8C,OAAA,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAA1E,EAA0E,CAAC;QACvE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAnC;YACM,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YACvD,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;SACtD,CAAC,CAAC;;QAGH,IAAI,CAAC,QAAQ,GAAG,YAApB;YACM,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YAC1D,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;SACzD,CAAC;QAEF,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;KACpC,CAAH;;;;;;;;;;IAME,YAAF,CAAA,SAAA,CAAA,cAAgB;;;;;IAAd,UAAe,OAAoB,EAArC;QACI,qBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAE/B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,+BAA+B,EAAE,CAAC;SACxC;KACF,CAAH;;;;;;;;;;;;IAOE,YAAF,CAAA,SAAA,CAAA,QAAU;;;;;;IAAR,UAAS,OAAoB,EAAE,MAAmB,EAApD;QACI,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,CAAC,KAAK,EAAE,CAAC;KACjB,CAAH;;;;IAEE,YAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAEG;QADC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,OAAO,EAA7C,EAAkD,OAAA,KAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAA9E,EAA8E,CAAC,CAAC;KAC7E,CAAH;;;;;IAGU,YAAV,CAAA,SAAA,CAAA,wBAAkC;;;;;;;QAE9B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO;SACR;;QAGD,qBAAI,uBAAuB,GAAG,YAAlC;YACM,KAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,KAAI,CAAC,8BAA8B,CAAC,UAAU,CAAC,CAAC;SACjD,CAAC;;;QAIF,qBAAI,yBAAyB,GAAG,YAApC;YACM,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;gBAC1B,KAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;aAC9C;SACF,CAAC;;;;QAKF,qBAAI,0BAA0B,GAAG,UAAC,KAAiB,EAAvD;YACM,IAAI,KAAI,CAAC,eAAe,IAAI,IAAI,EAAE;gBAChC,YAAY,CAAC,KAAI,CAAC,eAAe,CAAC,CAAC;aACpC;YACD,KAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAC;YACrC,KAAI,CAAC,eAAe,GAAG,UAAU,CAAC,YAAxC,EAA8C,OAAA,KAAI,CAAC,gBAAgB,GAAG,IAAI,CAA1E,EAA0E,EAAE,eAAe,CAAC,CAAC;SACxF,CAAC;;;QAIF,qBAAI,mBAAmB,GAAG,YAA9B;YACM,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,KAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,YAA9C,EAAoD,OAAA,KAAI,CAAC,cAAc,GAAG,KAAK,CAA/E,EAA+E,EAAE,CAAC,CAAC,CAAC;SAC/E,CAAC;;;QAIF,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAnC;YACM,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;YACpE,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,yBAAyB,EAAE,IAAI,CAAC,CAAC;YACxE,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,0BAA0B,EAC9DD,mDAA6B,EAAE,sBAAI,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAQ,KAAI,IAAI,CAAC,CAAC;YACtF,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;SACvD,CAAC,CAAC;QAEH,IAAI,CAAC,0BAA0B,GAAG,YAAtC;YACM,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;YACvE,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAC3E,QAAQ,CAAC,mBAAmB,CAAC,YAAY,EAAE,0BAA0B,EACjEA,mDAA6B,EAAE,sBAAI,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAQ,KAAI,IAAI,CAAC,CAAC;YACtF,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;;YAGzD,YAAY,CAAC,KAAI,CAAC,qBAAqB,CAAC,CAAC;YACzC,YAAY,CAAC,KAAI,CAAC,eAAe,CAAC,CAAC;YACnC,YAAY,CAAC,KAAI,CAAC,gBAAgB,CAAC,CAAC;SACrC,CAAC;;;;;;;;IAGI,YAAV,CAAA,SAAA,CAAA,YAAsB;;;;;;IAAtB,UAAuB,OAAgB,EAAE,SAAiB,EAAE,SAAkB,EAA9E;QACI,IAAI,SAAS,EAAE;YACb,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SAClC;aAAM;YACL,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACrC;;;;;;;;IAQK,YAAV,CAAA,SAAA,CAAA,WAAqB;;;;;;IAArB,UAAsB,OAAoB,EAAE,MAAoB,EAAhE;QACI,qBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sBAAsB,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC;YAC1E,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,qBAAqB,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC;SACzE;;;;;;;IAOK,YAAV,CAAA,SAAA,CAAA,8BAAwC;;;;;IAAxC,UAAyC,MAAmB,EAA5D;;QACI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,YAAvC,EAA6C,OAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAhE,EAAgE,EAAE,CAAC,CAAC,CAAC;;;;;;;IAQ3D,YAAV,CAAA,SAAA,CAAA,iBAA2B;;;;;IAA3B,UAA4B,KAAiB,EAA7C;;;;;;;;;;;;;;;;;;QAkBI,qBAAI,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO,IAAI,CAAC,gBAAgB,YAAY,IAAI,IAAI,WAAW,YAAY,IAAI;aACtE,WAAW,KAAK,IAAI,CAAC,gBAAgB,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;;;;;;;IAQrF,YAAV,CAAA,SAAA,CAAA,QAAkB;;;;;;IAAlB,UAAmB,KAAiB,EAAE,OAAoB,EAA1D;;;;;;;QAQI,qBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,WAAW,KAAK,CAAC,WAAW,CAAC,aAAa,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE;YAC5E,OAAO;SACR;;;;;;;QAQD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAChD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;aACtC;iBAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;gBACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;aACxB;iBAAM;gBACL,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;aAC1B;SACF;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;;;;;;;;;;;;IAQtB,YAAF,CAAA,SAAA,CAAA,OAAS;;;;;;IAAP,UAAQ,KAAiB,EAAE,OAAoB,EAAjD;;;QAGI,qBAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,YAAY,IAAI;YACjF,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE;YAC1C,OAAO;SACR;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC1B,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAChC,CAAH;;;;IAEU,YAAV,CAAA,SAAA,CAAA,+BAAyC;;;;;QAErC,IAAI,EAAE,IAAI,CAAC,sBAAsB,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACjC;;;;;IAGK,YAAV,CAAA,SAAA,CAAA,+BAAyC;;;;;QAErC,IAAI,CAAC,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,0BAA0B,GAAG,YAAxC,GAAgD,CAAC;SAC5C;;;QAnUL,EAAA,IAAA,EAACD,wBAAU,EAAX;;;;QA7BA,EAAA,IAAA,EAAEN,oBAAM,GAAR;QANA,EAAA,IAAA,EAAQC,8BAAQ,GAAhB;;IAPA,OAAA,YAAA,CAAA;;;;;;;;;;;;IAmYE,SAAF,eAAA,CAAsB,WAAuB,EAAU,aAA2B,EAAlF;QAAE,IAAF,KAAA,GAAA,IAAA,CAKG;QALmB,IAAtB,CAAA,WAAiC,GAAX,WAAW,CAAY;QAAU,IAAvD,CAAA,aAAoE,GAAb,aAAa,CAAc;QAFlF,IAAA,CAAA,cAAA,GAA6B,IAAII,0BAAY,EAAe,CAA5D;QAGI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAClD,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;aACrE,SAAS,CAAC,UAAA,MAAM,EAAzB,EAA6B,OAAA,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAA7D,EAA6D,CAAC,CAAC;KAC5D;;;;IAED,eAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;KACzC,CAAH;;QAjBA,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,oDAAoD;iBAC/D,EAAD,EAAA;;;;QApXA,EAAA,IAAA,EAAED,wBAAU,GAAZ;QAiCA,EAAA,IAAA,EAAa,YAAY,GAAzB;;;QAsVA,gBAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,oBAAM,EAAT,EAAA;;IAjYA,OAAA,eAAA,CAAA;;;;;;;;;AAiZA,SAAA,8BAAA,CACI,gBAA8B,EAAE,MAAc,EAAE,QAAkB,EADtE;IAEE,OAAO,gBAAgB,IAAI,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;CAC/D;;;;AAGD,IAAa,sBAAsB,GAAG;;IAEpC,OAAO,EAAE,YAAY;IACrB,IAAI,EAAE,CAAC,CAAC,IAAIJ,sBAAQ,EAAE,EAAE,IAAIC,sBAAQ,EAAE,EAAE,YAAY,CAAC,EAAEC,oBAAM,EAAEC,8BAAQ,CAAC;IACxE,UAAU,EAAE,8BAA8B;CAC3C,CAAC;;;;;;;;;;;;;;;;AD7YF,SAAA,+BAAA,CAAgD,KAAiB,EAAjE;IACE,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,CAAC;CAC5B;;;;;;;ADTD,IAAA,UAAA,kBAAA,YAAA;;;;QAaA,EAAA,IAAA,EAACJ,sBAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAACF,4BAAY,EAAEC,oCAAc,CAAC;oBACvC,YAAY,EAAE,CAAC,YAAY,EAAE,4BAA4B,EAAE,eAAe,CAAC;oBAC3E,OAAO,EAAE,CAAC,YAAY,EAAE,4BAA4B,EAAE,eAAe,CAAC;oBACtE,SAAS,EAAE;wBACT,oBAAoB;wBACpB,gBAAgB;wBAChB,aAAa;wBACb,uBAAuB;wBACvB,uBAAuB;wBACvB,sBAAsB;qBACvB;iBACF,EAAD,EAAA;;;;IAjCA,OAAA,UAAA,CAAA;CAkCA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}