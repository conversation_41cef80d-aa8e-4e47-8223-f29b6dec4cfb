{"_from": "npmlog@^4.0.0", "_id": "npmlog@4.1.2", "_inBundle": false, "_integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "_location": "/npmlog", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "npmlog@^4.0.0", "name": "npmlog", "escapedName": "npmlog", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/node-gyp", "/node-sass"], "_resolved": "https://registry.npmjs.org/npmlog/-/npmlog-4.1.2.tgz", "_shasum": "08a7f2a8bf734604779a9efa4ad5cc717abb954b", "_spec": "npmlog@^4.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\node-sass", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "bundleDependencies": false, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}, "deprecated": "This package is no longer supported.", "description": "logger for npm", "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.3"}, "files": ["log.js"], "homepage": "https://github.com/npm/npmlog#readme", "license": "ISC", "main": "log.js", "name": "npmlog", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "scripts": {"test": "standard && tap test/*.js"}, "version": "4.1.2"}