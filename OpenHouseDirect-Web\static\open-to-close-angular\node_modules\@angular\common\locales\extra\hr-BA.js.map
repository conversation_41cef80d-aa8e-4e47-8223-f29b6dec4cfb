{"version": 3, "file": "hr-BA.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/hr-BA.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,AAAD;QAC3D,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,CAAC;KACjE;IACD;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;QAC1D,AAD2D;KAE5D;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['ponoć', 'podne', 'ujutro', 'popodne', 'navečer', 'noću'], ,\n    ['ponoć', 'podne', 'ujutro', 'poslije podne', 'navečer', 'noću']\n  ],\n  [\n    ['ponoć', 'podne', 'ujutro', 'popodne', 'navečer', 'noću'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['04:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '04:00']\n  ]\n];\n"]}