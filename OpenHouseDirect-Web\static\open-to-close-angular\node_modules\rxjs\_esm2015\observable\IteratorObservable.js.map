{"version": 3, "file": "IteratorObservable.js", "sourceRoot": "", "sources": ["../../src/observable/IteratorObservable.ts"], "names": [], "mappings": "OAAO,EAAE,IAAI,EAAE,MAAM,cAAc;OAE5B,EAAE,UAAU,EAAE,MAAM,eAAe;OACnC,EAAE,QAAQ,IAAI,eAAe,EAAE,MAAM,oBAAoB;AAIhE;;;;GAIG;AACH,wCAA2C,UAAU;IAmCnD,YAAY,QAAa,EAAU,SAAsB;QACvD,OAAO,CAAC;QADyB,cAAS,GAAT,SAAS,CAAa;QAGvD,EAAE,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAxCD,OAAO,MAAM,CAAI,QAAa,EAAE,SAAsB;QACpD,MAAM,CAAC,IAAI,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED,OAAO,QAAQ,CAAC,KAAU;QAExB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;QAExD,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACb,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,CAAC;QACT,CAAC;QAED,IAAI,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC7B,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YAChB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QAExB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC;gBAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC;YACD,MAAM,CAAC;QACT,CAAC;QAEM,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAYD,oCAAoC,CAAC,UAAU,CAAC,UAAyB;QAEvE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAErC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACxD,KAAK,EAAE,QAAQ,EAAE,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC;gBACF,IAAI,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC7B,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACtB,KAAK,CAAC;gBACR,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChC,CAAC;gBACD,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBACtB,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC;wBAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACpB,CAAC;oBACD,KAAK,CAAC;gBACR,CAAC;YACH,CAAC,QAAQ,IAAI,EAAE;QACjB,CAAC;IACH,CAAC;AACH,CAAC;AAED;IACE,YAAoB,GAAW,EACX,GAAG,GAAW,CAAC,EACf,GAAG,GAAW,GAAG,CAAC,MAAM;QAFxB,QAAG,GAAH,GAAG,CAAQ;QACX,QAAG,GAAH,GAAG,CAAY;QACf,QAAG,GAAH,GAAG,CAAqB;IAC5C,CAAC;IACD,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtC,IAAI;QACF,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG;YACzB,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SACrC,GAAG;YACA,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;IACE,YAAoB,GAAe,EACf,GAAG,GAAW,CAAC,EACf,GAAG,GAAW,QAAQ,CAAC,GAAG,CAAC;QAF3B,QAAG,GAAH,GAAG,CAAY;QACf,QAAG,GAAH,GAAG,CAAY;QACf,QAAG,GAAH,GAAG,CAAwB;IAC/C,CAAC;IACD,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACpC,IAAI;QACF,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG;YACzB,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SAC9B,GAAG;YACA,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,qBAAqB,GAAQ;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC,eAAe,CAAC,CAAC;IAC/B,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC;QAClC,MAAM,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC;IAChD,CAAC;IACD,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;AAChC,CAAC;AAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAE3C,kBAAkB,CAAM;IACtB,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IACpB,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACb,MAAM,CAAC,CAAC,CAAC;IACb,CAAC;IACD,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,CAAC,GAAG,CAAC;IACf,CAAC;IACD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACX,MAAM,CAAC,CAAC,CAAC;IACb,CAAC;IACD,EAAE,CAAC,CAAC,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC;QACvB,MAAM,CAAC,cAAc,CAAC;IAC1B,CAAC;IACD,MAAM,CAAC,GAAG,CAAC;AACb,CAAC;AAED,wBAAwB,KAAU;IAChC,MAAM,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3D,CAAC;AAED,cAAc,KAAU;IACtB,IAAI,aAAa,GAAG,CAAC,KAAK,CAAC;IAC3B,EAAE,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,aAAa,CAAC;IACvB,CAAC;IACD,EAAE,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC"}