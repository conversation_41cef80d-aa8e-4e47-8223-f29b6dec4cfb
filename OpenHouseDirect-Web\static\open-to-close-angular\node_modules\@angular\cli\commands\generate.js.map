{"version": 3, "file": "generate.js", "sourceRoot": "/users/hansl/sources/hansl/angular-cli/", "sources": ["commands/generate.ts"], "names": [], "mappings": ";;AAAA,iCAA0B;AAC1B,MAAM,WAAW,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AACtD,6CAAsC;AACtC,6CAA6C;AAC7C,+CAAyD;AACzD,8CAAwC;AAExC,wDAGiC;AACjC,0EAAyF;AACzF,sDAA0D;AAC1D,6BAA6B;AAG7B,MAAM,OAAO,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC3D,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE5C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,eAAK,CAAC;AAC/B,MAAM,cAAc,GAAG,SAAS,CAAC;AAGjC,kBAAe,OAAO,CAAC,MAAM,CAAC;IAC5B,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE,uDAAuD;IACpE,OAAO,EAAE,CAAC,GAAG,CAAC;IAEd,gBAAgB,EAAE;QAChB;YACE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,yCAAyC;SACvD;QACD;YACE,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,8BAA8B;SAC5C;QACD;YACE,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,4BAA4B;SAC1C;QACD;YACE,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,WAAW,EAAE,+BAA+B;SAC7C;QACD;YACE,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,WAAW,EAAE,yCAAyC;SACvD;KACF;IAED,gBAAgB,EAAE;QAChB,aAAa;KACd;IAED,iBAAiB,CAAC,OAAiB,EAAE,aAAuC;QAC1E,IAAI,cAAc,GAAW,kBAAS,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC;QAClF,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAChC,CAAC;QACD,IAAI,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/B,EAAE,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,cAAc,EAAE,aAAa,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAChE,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACzB,EAAE,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC7B,cAAc,GAAG,aAAa,CAAC,UAAU,CAAC;YAC5C,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAClD,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;gBAClC,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC;YACjD,CAAC;QACH,CAAC;QAED,MAAM,CAAC,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;IACzC,CAAC;IAED,SAAS,EAAE,UAAS,OAAiB;QAEnC,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC;QACT,CAAC;QAED,MAAM,CAAC,cAAc,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACxE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACnB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,qBAAO,CAAA;;;;OAI5C,CAAC,CAAC,CAAC;QACN,CAAC;QAED,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,WAAW,CAAC,YAAY,CAAC,gCAAgC,EACvD,qBAAqB,aAAa,IAAI,OAAO,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC;QAC/F,CAAC;QAED,MAAM,uBAAuB,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC,OAAO,CAAC;QAElF,MAAM,cAAc,GAAG,IAAI,uBAAuB,CAAC;YACjD,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC;YACtB,aAAa;YACb,cAAc;SACf,CAAC;aACD,IAAI,CAAC,CAAC,gBAA6C,EAAE,EAAE;YACtD,IAAI,gBAAgB,GAAa,EAAE,CAAC;YAEpC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACrB,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1E,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;oBACf,gBAAgB,GAAG,CAAC,GAAG,gBAAgB,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,gBAAgB,GAAG,CAAC,GAAG,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YACrD,CAAC;YAED,EAAE,CAAC,CAAC,cAAc,KAAK,qBAAqB,IAAI,aAAa,KAAK,WAAW,CAAC,CAAC,CAAC;gBAC9E,gBAAgB,GAAG,CAAC,GAAG,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YACrD,CAAC;YAED,IAAI,CAAC,eAAe,CAAC;gBACnB,gBAAgB,EAAE,gBAAgB;gBAClC,gBAAgB,EAAE,gBAAgB,IAAI,EAAE;aACzC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,GAAG,EAAE,UAAU,cAAmB,EAAE,OAAiB;QACnD,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,mEAAmE,CAAC;QAC5E,CAAC;QAED,IAAI,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YACf,cAAc,CAAC,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACtF,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,UAAU,GAAG,EAAE,CAAC;QAClB,CAAC;QAED,MAAM,SAAS,GAAG,4BAAgB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACvD,MAAM,kBAAkB,GAAuB;YAC7C,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,UAAU;YACtB,SAAS,EAAE,SAAS;YACpB,MAAM,EAAE,cAAc,CAAC,MAAM;SAC9B,CAAC;QACF,MAAM,UAAU,GAAG,uCAAiB,CAAC,kBAAkB,CAAC,CAAC;QACzD,cAAc,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;QAC7E,MAAM,IAAI,GAAG,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;QAC7C,cAAc,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACzE,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;gBACjC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACxC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC;QAEzB,cAAc,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;QAClE,cAAc,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,KAAK,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAClE,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;gBAC7B,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACzC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;QAE1B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9B,MAAM,CAAC,cAAc,EAAE,aAAa,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAExF,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,EAAE,CAAC,CAAC,cAAc,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;gBACxC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;YAC3C,CAAC;YAED,EAAE,CAAC,CAAC,aAAa,KAAK,WAAW,IAAI,aAAa,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC3D,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC;oBAC1C,cAAc,CAAC,QAAQ,GAAG,kBAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC,OAAO,CAAC;QACnE,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC;YAC5C,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;QAEH,EAAE,CAAC,CAAC,cAAc,KAAK,qBAAqB,IAAI,aAAa,KAAK,WAAW,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5F,cAAc,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,cAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACjD,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CACpC,kBAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC;aACzC,SAAS,CAAC,KAAK,CAAC,EAAE;YACjB,IAAI,KAAK,GAAG,CAAC,CAAM,EAAE,EAAE,CAAC,eAAQ,CAAC,GAAG,CAAC,eAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAC5B,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpB,KAAK,MAAM;oBACT,KAAK,GAAG,eAAQ,CAAC,KAAK,CAAC;oBACvB,KAAK,CAAC;gBACR,KAAK,MAAM;oBACT,KAAK,GAAG,eAAQ,CAAC,MAAM,CAAC;oBACxB,KAAK,CAAC;gBACR,KAAK,OAAO;oBACV,KAAK,GAAG,eAAQ,CAAC,GAAG,CAAC;oBACrB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;oBACxB,KAAK,CAAC;gBACR,KAAK,OAAO;oBACV,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,eAAQ,CAAC,IAAI,CAAC,eAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;oBACxB,KAAK,CAAC;YACV,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEL,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC;YACxB,WAAW,EAAE,cAAc;YAC3B,UAAU,EAAE,GAAG;YACf,cAAc;YACd,aAAa;YACb,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC;aACD,IAAI,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,iBAAiB,EAAE,UAAU,QAAa,EAAE,OAAY;QACtD,MAAM,UAAU,GAAG,0BAAa,EAAE,CAAC;QACnC,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAClD,MAAM,UAAU,GAAG,0BAAa,CAAC,cAAc,CAAC,CAAC;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACjC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YAClB,MAAM,0BAA0B,GAAG,OAAO,CAAC,oCAAoC,CAAC,CAAC,OAAO,CAAC;YACzF,MAAM,iBAAiB,GAAG,IAAI,0BAA0B,CAAC;gBACvD,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC;gBAC3B,aAAa;gBACb,cAAc;gBACd,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;aACzE,CAAC;iBACD,IAAI,CAAC,CAAC,MAAgB,EAAE,EAAE;gBACzB,MAAM,CAAC;oBACL,IAAI,CAAC,eAAe,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC9E,GAAG,MAAM;iBACV,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,cAAc,GAAa,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACvE,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;YAC3C,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBACrC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,aAAa,EAAE,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}