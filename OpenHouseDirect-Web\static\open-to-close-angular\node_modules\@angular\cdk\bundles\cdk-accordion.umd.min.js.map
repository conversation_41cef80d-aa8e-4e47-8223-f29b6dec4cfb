{"version": 3, "file": "cdk-accordion.umd.min.js", "sources": ["../../src/cdk/accordion/accordion.ts", "../../src/cdk/accordion/accordion-item.ts", "../../src/cdk/accordion/accordion-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, Input} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\n\n/** Used to generate unique ID for each accordion. */\nlet nextId = 0;\n\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\n@Directive({\n  selector: 'cdk-accordion, [cdkAccordion]',\n  exportAs: 'cdkAccordion',\n})\nexport class CdkAccordion {\n  /** A readonly id value to use for unique selection coordination. */\n  readonly id = `cdk-accordion-${nextId++}`;\n\n  /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n  @Input()\n  get multi(): boolean { return this._multi; }\n  set multi(multi: boolean) { this._multi = coerceBooleanProperty(multi); }\n  private _multi: boolean = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Output,\n  Directive,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Optional,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport {UniqueSelectionDispatcher} from '@angular/cdk/collections';\nimport {CdkAccordion} from './accordion';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\n\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\n@Directive({\n  selector: 'cdk-accordion-item',\n  exportAs: 'cdkAccordionItem',\n})\nexport class CdkAccordionItem implements OnDestroy {\n  /** Event emitted every time the AccordionItem is closed. */\n  @Output() closed: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted every time the AccordionItem is opened. */\n  @Output() opened: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted when the AccordionItem is destroyed. */\n  @Output() destroyed: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Emits whenever the expanded state of the accordion changes.\n   * Primarily used to facilitate two-way binding.\n   * @docs-private\n   */\n  @Output() expandedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** The unique AccordionItem id. */\n  readonly id: string = `cdk-accordion-child-${nextId++}`;\n\n  /** Whether the AccordionItem is expanded. */\n  @Input()\n  get expanded(): any { return this._expanded; }\n  set expanded(expanded: any) {\n    expanded = coerceBooleanProperty(expanded);\n\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _expanded = false;\n\n  /** Whether the AccordionItem is disabled. */\n  @Input()\n  get disabled() { return this._disabled; }\n  set disabled(disabled: any) { this._disabled = coerceBooleanProperty(disabled); }\n  private _disabled: boolean = false;\n\n  /** Unregister function for _expansionDispatcher. */\n  private _removeUniqueSelectionListener: () => void = () => {};\n\n  constructor(@Optional() public accordion: CdkAccordion,\n              private _changeDetectorRef: ChangeDetectorRef,\n              protected _expansionDispatcher: UniqueSelectionDispatcher) {\n    this._removeUniqueSelectionListener =\n      _expansionDispatcher.listen((id: string, accordionId: string) => {\n        if (this.accordion && !this.accordion.multi &&\n            this.accordion.id === accordionId && this.id !== id) {\n          this.expanded = false;\n        }\n      });\n  }\n\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.destroyed.emit();\n    this._removeUniqueSelectionListener();\n  }\n\n  /** Toggles the expanded state of the accordion item. */\n  toggle(): void {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to false. */\n  close(): void {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to true. */\n  open(): void {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {UNIQUE_SELECTION_DISPATCHER_PROVIDER} from '@angular/cdk/collections';\nimport {CdkAccordion} from './accordion';\nimport {CdkAccordionItem} from './accordion-item';\n\n@NgModule({\n  exports: [CdkAccordion, CdkAccordionItem],\n  declarations: [CdkAccordion, CdkAccordionItem],\n  providers: [UNIQUE_SELECTION_DISPATCHER_PROVIDER],\n})\nexport class CdkAccordionModule {}\n"], "names": ["nextId", "this", "id", "_multi", "Object", "defineProperty", "CdkAccordion", "prototype", "multi", "coerceBooleanProperty", "type", "Directive", "args", "selector", "exportAs", "Input", "CdkAccordionItem", "accordion", "_changeDetectorRef", "_expansionDispatcher", "_this", "closed", "EventEmitter", "opened", "destroyed", "expandedChange", "_expanded", "_disabled", "_removeUniqueSelectionListener", "listen", "accordionId", "expanded", "emit", "notify", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "ngOnDestroy", "toggle", "close", "open", "decorators", "Optional", "ChangeDetectorRef", "UniqueSelectionDispatcher", "Output", "CdkAccordionModule", "NgModule", "exports", "declarations", "providers", "UNIQUE_SELECTION_DISPATCHER_PROVIDER"], "mappings": ";;;;;;;odAYA,IAAIA,GAAS,4BAWbC,KAAAC,GAAgB,iBAAiBF,IAMjCC,KAAAE,QAA4B,EA7B5B,MA2BAC,QAAAC,eAAMC,EAANC,UAAA,aAAA,WAAyB,MAAON,MAAKE,YACnC,SAAUK,GAAkBP,KAAKE,OAASM,EAAAA,sBAAsBD,mDAXlEE,KAACC,EAAAA,UAADC,OACEC,SAAU,gCACVC,SAAU,2EAOZN,QAAAE,KAAGK,EAAAA,SA1BHT,KCsBIN,EAAS,eAmEX,QAAFgB,GAAiCC,EACXC,EACEC,GAFtB,GAAFC,GAAAnB,IAAiCA,MAAjCgB,UAAiCA,EACXhB,KAAtBiB,mBAAsBA,EACEjB,KAAxBkB,qBAAwBA,EAzDxBlB,KAAAoB,OAAyC,GAAIC,GAAAA,aAE7CrB,KAAAsB,OAAyC,GAAID,GAAAA,aAE7CrB,KAAAuB,UAA4C,GAAIF,GAAAA,aAOhDrB,KAAAwB,eAAoD,GAAIH,GAAAA,aAGxDrB,KAAAC,GAAwB,uBAAuBF,IA8B/CC,KAAAyB,WAAsB,EAMtBzB,KAAA0B,WAA+B,EAG/B1B,KAAA2B,+BAAuD,aAKnD3B,KAAK2B,+BACHT,EAAqBU,OAAO,SAAC3B,EAAY4B,GACnCV,EAAKH,YAAcG,EAAKH,UAAUT,OAClCY,EAAKH,UAAUf,KAAO4B,GAAeV,EAAKlB,KAAOA,IACnDkB,EAAKW,UAAW,KAhG1B,MAoDA3B,QAAAC,eAAMW,EAANT,UAAA,gBAAA,WAAwB,MAAON,MAAKyB,eAClC,SAAaK,GAIX,GAHAA,EAAWtB,EAAAA,sBAAsBsB,GAG7B9B,KAAKyB,YAAcK,EAAU,CAI/B,GAHA9B,KAAKyB,UAAYK,EACjB9B,KAAKwB,eAAeO,KAAKD,GAErBA,EAAU,CACZ9B,KAAKsB,OAAOS,MAKZ,IAAMF,GAAc7B,KAAKgB,UAAYhB,KAAKgB,UAAUf,GAAKD,KAAKC,EAC9DD,MAAKkB,qBAAqBc,OAAOhC,KAAKC,GAAI4B,OAE1C7B,MAAKoB,OAAOW,MAKd/B,MAAKiB,mBAAmBgB,iDAO9B9B,OAAAC,eAAMW,EAANT,UAAA,gBAAA,WAAmB,MAAON,MAAK0B,eAC7B,SAAaQ,GAAiBlC,KAAK0B,UAAYlB,EAAAA,sBAAsB0B,oCAmBrEnB,EAAFT,UAAA6B,YAAE,WACEnC,KAAKuB,UAAUQ,OACf/B,KAAK2B,kCAIPZ,EAAFT,UAAA8B,OAAE,WACOpC,KAAKkC,WACRlC,KAAK8B,UAAY9B,KAAK8B,WAK1Bf,EAAFT,UAAA+B,MAAE,WACOrC,KAAKkC,WACRlC,KAAK8B,UAAW,IAKpBf,EAAFT,UAAAgC,KAAE,WACOtC,KAAKkC,WACRlC,KAAK8B,UAAW,mBAhGtBrB,KAACC,EAAAA,UAADC,OACEC,SAAU,qBACVC,SAAU,2DAZZJ,KAAQJ,EAARkC,aAAA9B,KAuEe+B,EAAAA,aA1Ef/B,KAAEgC,EAAAA,oBAEFhC,KAAQiC,EAAAA,+CAiBRtB,SAAAX,KAAGkC,EAAAA,SAEHrB,SAAAb,KAAGkC,EAAAA,SAEHpB,YAAAd,KAAGkC,EAAAA,SAOHnB,iBAAAf,KAAGkC,EAAAA,SAMHb,WAAArB,KAAGK,EAAAA,QA8BHoB,WAAAzB,KAAGK,EAAAA,SAjFHC,KCQA6B,EAAA,yBARA,sBAaAnC,KAACoC,EAAAA,SAADlC,OACEmC,SAAUzC,EAAcU,GACxBgC,cAAe1C,EAAcU,GAC7BiC,WAAYC,EAAAA,gFAhBdL"}