{"version": 3, "file": "uz-Latn.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/uz-Latn.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;QACzE,AAD0E;KAE3E;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['yarim tun', 'tush payti', 'ertalab', 'kunduzi', 'kechqurun', 'kechasi'],\n    ,\n  ],\n  ,\n  [\n    '00:00', '12:00', ['06:00', '11:00'], ['11:00', '18:00'], ['18:00', '22:00'],\n    ['22:00', '06:00']\n  ]\n];\n"]}