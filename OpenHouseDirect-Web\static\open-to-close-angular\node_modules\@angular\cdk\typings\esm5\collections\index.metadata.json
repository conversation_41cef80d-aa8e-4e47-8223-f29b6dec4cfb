{"__symbolic": "module", "version": 4, "metadata": {"CollectionViewer": {"__symbolic": "interface"}, "DataSource": {"__symbolic": "class", "arity": 1, "members": {"connect": [{"__symbolic": "method"}], "disconnect": [{"__symbolic": "method"}]}}, "SelectionModel": {"__symbolic": "class", "arity": 1, "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [null, {"__symbolic": "reference", "name": "Array", "arguments": [{"__symbolic": "error", "message": "Could not resolve type", "line": 40, "character": 30, "context": {"typeName": "T"}, "module": "./selection"}]}, null]}], "select": [{"__symbolic": "method"}], "deselect": [{"__symbolic": "method"}], "toggle": [{"__symbolic": "method"}], "clear": [{"__symbolic": "method"}], "isSelected": [{"__symbolic": "method"}], "isEmpty": [{"__symbolic": "method"}], "hasValue": [{"__symbolic": "method"}], "sort": [{"__symbolic": "method"}], "_emitChangeEvent": [{"__symbolic": "method"}], "_markSelected": [{"__symbolic": "method"}], "_unmarkSelected": [{"__symbolic": "method"}], "_unmarkAll": [{"__symbolic": "method"}], "_verifyValueAssignment": [{"__symbolic": "method"}]}}, "SelectionChange": {"__symbolic": "interface"}, "getMultipleValuesInSingleSelectionError": {"__symbolic": "function", "parameters": [], "value": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Error"}, "arguments": ["Cannot pass multiple values into SelectionModel with single-value mode."]}}, "ɵa": {"__symbolic": "function", "parameters": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "UniqueSelectionDispatcher"}}}}, "UniqueSelectionDispatcher": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 23, "character": 1}}], "members": {"notify": [{"__symbolic": "method"}], "listen": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "UniqueSelectionDispatcherListener": {"__symbolic": "interface"}, "UNIQUE_SELECTION_DISPATCHER_PROVIDER": {"provide": {"__symbolic": "reference", "name": "UniqueSelectionDispatcher"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 66, "character": 14}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 66, "character": 30}}, {"__symbolic": "reference", "name": "UniqueSelectionDispatcher"}]], "useFactory": {"__symbolic": "reference", "name": "ɵa"}}}, "origins": {"CollectionViewer": "./collection-viewer", "DataSource": "./data-source", "SelectionModel": "./selection", "SelectionChange": "./selection", "getMultipleValuesInSingleSelectionError": "./selection", "ɵa": "./unique-selection-dispatcher", "UniqueSelectionDispatcher": "./unique-selection-dispatcher", "UniqueSelectionDispatcherListener": "./unique-selection-dispatcher", "UNIQUE_SELECTION_DISPATCHER_PROVIDER": "./unique-selection-dispatcher"}, "importAs": "@angular/cdk/collections"}