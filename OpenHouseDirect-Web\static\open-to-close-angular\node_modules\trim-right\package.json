{"_from": "trim-right@^1.0.1", "_id": "trim-right@1.0.1", "_inBundle": false, "_integrity": "sha512-WZGXGstmCWgeevgTL54hrCuw1dyMQIzWy7ZfqRJfSmJZBwklI15egmQytFP6bPidmw3M8d5yEowl1niq4vmqZw==", "_location": "/trim-right", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "trim-right@^1.0.1", "name": "trim-right", "escapedName": "trim-right", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/babel-generator"], "_resolved": "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz", "_shasum": "cb2e1203067e0c8de1f614094b9fe45704ea6003", "_spec": "trim-right@^1.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\babel-generator", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/trim-right/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Similar to String#trim() but removes only whitespace on the right", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/trim-right#readme", "keywords": ["trim", "right", "string", "str", "util", "utils", "utility", "whitespace", "space", "remove", "delete"], "license": "MIT", "name": "trim-right", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/trim-right.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.1"}