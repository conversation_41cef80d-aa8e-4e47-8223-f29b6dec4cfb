{"_from": "object-is@^1.1.5", "_id": "object-is@1.1.6", "_inBundle": false, "_integrity": "sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==", "_location": "/object-is", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object-is@^1.1.5", "name": "object-is", "escapedName": "object-is", "rawSpec": "^1.1.5", "saveSpec": null, "fetchSpec": "^1.1.5"}, "_requiredBy": ["/deep-equal"], "_resolved": "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz", "_shasum": "1a6a53aed2dd8f7e6775ff870bea58545956ab07", "_spec": "object-is@^1.1.5", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\deep-equal", "author": {"name": "<PERSON>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "deprecated": false, "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "devDependencies": {"@es-shims/api": "^2.4.2", "@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "functions-have-names": "^1.2.3", "has-symbols": "^1.0.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "license": "MIT", "main": "index.js", "name": "object-is", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.6"}