{"_from": "ts-node@~4.1.0", "_id": "ts-node@4.1.0", "_inBundle": false, "_integrity": "sha512-xcZH12oVg9PShKhy3UHyDmuDLV3y7iKwX25aMVPt1SIXSuAfWkFiGPEkg+th8R4YKW/QCxDoW7lJdb15lx6QWg==", "_location": "/ts-node", "_phantomChildren": {"ansi-styles": "3.2.1", "buffer-from": "1.1.2", "escape-string-regexp": "1.0.5"}, "_requested": {"type": "range", "registry": true, "raw": "ts-node@~4.1.0", "name": "ts-node", "escapedName": "ts-node", "rawSpec": "~4.1.0", "saveSpec": null, "fetchSpec": "~4.1.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/ts-node/-/ts-node-4.1.0.tgz", "_shasum": "36d9529c7b90bb993306c408cd07f7743de20712", "_spec": "ts-node@~4.1.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bin": {"ts-node": "dist/bin.js", "_ts-node": "dist/_bin.js"}, "bugs": {"url": "https://github.com/TypeStrong/ts-node/issues"}, "bundleDependencies": false, "dependencies": {"arrify": "^1.0.0", "chalk": "^2.3.0", "diff": "^3.1.0", "make-error": "^1.1.1", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.5.0", "tsconfig": "^7.0.0", "v8flags": "^3.0.0", "yn": "^2.0.0"}, "deprecated": false, "description": "TypeScript execution environment and REPL for node", "devDependencies": {"@types/arrify": "^1.0.1", "@types/chai": "^4.0.4", "@types/diff": "^3.2.1", "@types/minimist": "^1.2.0", "@types/mkdirp": "^0.5.0", "@types/mocha": "^2.2.42", "@types/node": "^8.0.27", "@types/proxyquire": "^1.3.28", "@types/react": "^16.0.2", "@types/semver": "^5.3.34", "@types/source-map-support": "^0.4.0", "@types/v8flags": "github:types/npm-v8flags#de224ae1cd5fd7dbb4e7158a6cc7a29e5315930d", "@types/yn": "github:types/npm-yn#ca75f6c82940fae6a06fb41d2d37a6aa9b4ea8e9", "chai": "^4.0.1", "istanbul": "^0.4.0", "mocha": "^4.0.0", "ntypescript": "^1.201507091536.1", "proxyquire": "^1.7.2", "react": "^16.0.0", "rimraf": "^2.5.4", "semver": "^5.1.0", "tslint": "^5.0.0", "tslint-config-standard": "^7.0.0", "typescript": "^2.4.1"}, "engines": {"node": ">=4.2.0"}, "files": ["dist/", "register/", "LICENSE"], "homepage": "https://github.com/TypeStrong/ts-node", "keywords": ["typescript", "node", "runtime", "environment", "ts", "compiler"], "license": "MIT", "main": "dist/index.js", "name": "ts-node", "repository": {"type": "git", "url": "git://github.com/TypeStrong/ts-node.git"}, "scripts": {"build": "npm run clean && npm run tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "tslint \"src/**/*.ts\" --project tsconfig.json --type-check", "prepublish": "npm run build", "test": "npm run build && npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- \"dist/**/*.spec.js\" -R spec --bail", "test-spec": "mocha dist/**/*.spec.js -R spec --bail", "tsc": "tsc"}, "types": "dist/index.d.ts", "version": "4.1.0"}