{"version": 3, "file": "PairsObservable.js", "sourceRoot": "", "sources": ["../../src/observable/PairsObservable.ts"], "names": [], "mappings": ";;;;;;AAEA,2BAA2B,eAAe,CAAC,CAAA;AAY3C,kBAAoD,KAAsB;IACjE,mBAAG,EAAE,iBAAI,EAAE,qBAAM,EAAE,mBAAK,EAAE,6BAAU,CAAU;IAErD,EAAE,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC;QACrB,UAAU,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,CAAC;IACT,CAAC;IAED,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IACxB,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEjC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;IAExB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAED;;;;GAIG;AACH;IAAwC,mCAA6B;IAsCnE,yBAAoB,GAAW,EAAU,SAAsB;QAC7D,iBAAO,CAAC;QADU,QAAG,GAAH,GAAG,CAAQ;QAAU,cAAS,GAAT,SAAS,CAAa;QAE7D,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAtCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACI,sBAAM,GAAb,UAAiB,GAAW,EAAE,SAAsB;QAClD,MAAM,CAAC,IAAI,eAAe,CAAI,GAAG,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAOD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAAyC;QACvF,IAAA,SAA8B,EAAvB,cAAI,EAAE,wBAAS,CAAS;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACrC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,UAAI,EAAE,cAAM,EAAE,KAAK,EAAE,CAAC,EAAE,sBAAU;aAClD,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;gBACtC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACtB,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AA3DD,CAAwC,uBAAU,GA2DjD;AA3DY,uBAAe,kBA2D3B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\r\nimport { Action } from '../scheduler/Action';\r\nimport { Observable } from '../Observable';\r\nimport { Subscriber } from '../Subscriber';\r\nimport { TeardownLogic } from '../Subscription';\r\n\r\ninterface PairsContext<T> {\r\n  obj: Object;\r\n  keys: Array<string>;\r\n  length: number;\r\n  index: number;\r\n  subscriber: Subscriber<Array<string | T>>;\r\n}\r\n\r\nfunction dispatch<T>(this: Action<PairsContext<T>>, state: PairsContext<T>) {\r\n  const {obj, keys, length, index, subscriber} = state;\r\n\r\n  if (index === length) {\r\n    subscriber.complete();\r\n    return;\r\n  }\r\n\r\n  const key = keys[index];\r\n  subscriber.next([key, obj[key]]);\r\n\r\n  state.index = index + 1;\r\n\r\n  this.schedule(state);\r\n}\r\n\r\n/**\r\n * We need this JSDoc comment for affecting ESDoc.\r\n * @extends {Ignored}\r\n * @hide true\r\n */\r\nexport class PairsObservable<T> extends Observable<Array<string | T>> {\r\n  private keys: Array<string>;\r\n\r\n  /**\r\n   * Convert an object into an observable sequence of [key, value] pairs\r\n   * using an optional IScheduler to enumerate the object.\r\n   *\r\n   * @example <caption>Converts a javascript object to an Observable</caption>\r\n   * var obj = {\r\n   *   foo: 42,\r\n   *   bar: 56,\r\n   *   baz: 78\r\n   * };\r\n   *\r\n   * var source = Rx.Observable.pairs(obj);\r\n   *\r\n   * var subscription = source.subscribe(\r\n   *   function (x) {\r\n   *     console.log('Next: %s', x);\r\n   *   },\r\n   *   function (err) {\r\n   *     console.log('Error: %s', err);\r\n   *   },\r\n   *   function () {\r\n   *     console.log('Completed');\r\n   *   });\r\n   *\r\n   * @param {Object} obj The object to inspect and turn into an\r\n   * Observable sequence.\r\n   * @param {Scheduler} [scheduler] An optional IScheduler to run the\r\n   * enumeration of the input sequence on.\r\n   * @returns {(Observable<Array<string | T>>)} An observable sequence of\r\n   * [key, value] pairs from the object.\r\n   */\r\n  static create<T>(obj: Object, scheduler?: IScheduler): Observable<Array<string | T>> {\r\n    return new PairsObservable<T>(obj, scheduler);\r\n  }\r\n\r\n  constructor(private obj: Object, private scheduler?: IScheduler) {\r\n    super();\r\n    this.keys = Object.keys(obj);\r\n  }\r\n\r\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<Array<string | T>>): TeardownLogic {\r\n    const {keys, scheduler} = this;\r\n    const length = keys.length;\r\n\r\n    if (scheduler) {\r\n      return scheduler.schedule(dispatch, 0, {\r\n        obj: this.obj, keys, length, index: 0, subscriber\r\n      });\r\n    } else {\r\n      for (let idx = 0; idx < length; idx++) {\r\n        const key = keys[idx];\r\n        subscriber.next([key, this.obj[key]]);\r\n      }\r\n      subscriber.complete();\r\n    }\r\n  }\r\n}\r\n"]}