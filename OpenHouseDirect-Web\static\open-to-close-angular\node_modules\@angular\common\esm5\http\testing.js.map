{"version": 3, "file": "testing.js", "sources": ["../../../../packages/common/esm5/http/testing/src/api.js", "../../../../packages/common/esm5/http/testing/src/request.js", "../../../../packages/common/esm5/http/testing/src/backend.js", "../../../../packages/common/esm5/http/testing/src/module.js", "../../../../packages/common/esm5/http/testing/public_api.js", "../../../../packages/common/esm5/http/testing/testing.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Defines a matcher for requests based on URL, method, or both.\n *\n * \\@stable\n * @record\n */\nexport function RequestMatch() { }\nfunction RequestMatch_tsickle_Closure_declarations() {\n    /** @type {?|undefined} */\n    RequestMatch.prototype.method;\n    /** @type {?|undefined} */\n    RequestMatch.prototype.url;\n}\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * \\@stable\n * @abstract\n */\nvar /**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * \\@stable\n * @abstract\n */\nHttpTestingController = /** @class */ (function () {\n    function HttpTestingController() {\n    }\n    return HttpTestingController;\n}());\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * \\@stable\n * @abstract\n */\nexport { HttpTestingController };\nfunction HttpTestingController_tsickle_Closure_declarations() {\n    /**\n     * Search for requests that match the given parameter, without any expectations.\n     * @abstract\n     * @param {?} match\n     * @return {?}\n     */\n    HttpTestingController.prototype.match = function (match) { };\n    /**\n     * Expect that a single request has been made which matches the given URL, and return its\n     * mock.\n     *\n     * If no such request has been made, or more than one such request has been made, fail with an\n     * error message including the given request description, if any.\n     * @abstract\n     * @param {?} url\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectOne = function (url, description) { };\n    /**\n     * Expect that a single request has been made which matches the given parameters, and return\n     * its mock.\n     *\n     * If no such request has been made, or more than one such request has been made, fail with an\n     * error message including the given request description, if any.\n     * @abstract\n     * @param {?} params\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectOne = function (params, description) { };\n    /**\n     * Expect that a single request has been made which matches the given predicate function, and\n     * return its mock.\n     *\n     * If no such request has been made, or more than one such request has been made, fail with an\n     * error message including the given request description, if any.\n     * @abstract\n     * @param {?} matchFn\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectOne = function (matchFn, description) { };\n    /**\n     * Expect that a single request has been made which matches the given condition, and return\n     * its mock.\n     *\n     * If no such request has been made, or more than one such request has been made, fail with an\n     * error message including the given request description, if any.\n     * @abstract\n     * @param {?} match\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectOne = function (match, description) { };\n    /**\n     * Expect that no requests have been made which match the given URL.\n     *\n     * If a matching request has been made, fail with an error message including the given request\n     * description, if any.\n     * @abstract\n     * @param {?} url\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectNone = function (url, description) { };\n    /**\n     * Expect that no requests have been made which match the given parameters.\n     *\n     * If a matching request has been made, fail with an error message including the given request\n     * description, if any.\n     * @abstract\n     * @param {?} params\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectNone = function (params, description) { };\n    /**\n     * Expect that no requests have been made which match the given predicate function.\n     *\n     * If a matching request has been made, fail with an error message including the given request\n     * description, if any.\n     * @abstract\n     * @param {?} matchFn\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectNone = function (matchFn, description) { };\n    /**\n     * Expect that no requests have been made which match the given condition.\n     *\n     * If a matching request has been made, fail with an error message including the given request\n     * description, if any.\n     * @abstract\n     * @param {?} match\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpTestingController.prototype.expectNone = function (match, description) { };\n    /**\n     * Verify that no unmatched requests are outstanding.\n     *\n     * If any requests are outstanding, fail with an error message indicating which requests were not\n     * handled.\n     *\n     * If `ignoreCancelled` is not set (the default), `verify()` will also fail if cancelled requests\n     * were not explicitly matched.\n     * @abstract\n     * @param {?=} opts\n     * @return {?}\n     */\n    HttpTestingController.prototype.verify = function (opts) { };\n}\n//# sourceMappingURL=api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { HttpErrorResponse, HttpHeaders, HttpResponse } from '@angular/common/http';\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * \\@stable\n */\nvar /**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * \\@stable\n */\nTestRequest = /** @class */ (function () {\n    function TestRequest(request, observer) {\n        this.request = request;\n        this.observer = observer;\n        /**\n         * \\@internal set by `HttpClientTestingBackend`\n         */\n        this._cancelled = false;\n    }\n    Object.defineProperty(TestRequest.prototype, \"cancelled\", {\n        /**\n         * Whether the request was cancelled after it was sent.\n         */\n        get: /**\n         * Whether the request was cancelled after it was sent.\n         * @return {?}\n         */\n        function () { return this._cancelled; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * Resolve the request by returning a body plus additional HTTP information (such as response\n     * headers) if provided.\n     *\n     * Both successful and unsuccessful responses can be delivered via `flush()`.\n     */\n    /**\n     * Resolve the request by returning a body plus additional HTTP information (such as response\n     * headers) if provided.\n     *\n     * Both successful and unsuccessful responses can be delivered via `flush()`.\n     * @param {?} body\n     * @param {?=} opts\n     * @return {?}\n     */\n    TestRequest.prototype.flush = /**\n     * Resolve the request by returning a body plus additional HTTP information (such as response\n     * headers) if provided.\n     *\n     * Both successful and unsuccessful responses can be delivered via `flush()`.\n     * @param {?} body\n     * @param {?=} opts\n     * @return {?}\n     */\n    function (body, opts) {\n        if (opts === void 0) { opts = {}; }\n        if (this.cancelled) {\n            throw new Error(\"Cannot flush a cancelled request.\");\n        }\n        var /** @type {?} */ url = this.request.urlWithParams;\n        var /** @type {?} */ headers = (opts.headers instanceof HttpHeaders) ? opts.headers : new HttpHeaders(opts.headers);\n        body = _maybeConvertBody(this.request.responseType, body);\n        var /** @type {?} */ statusText = opts.statusText;\n        var /** @type {?} */ status = opts.status !== undefined ? opts.status : 200;\n        if (opts.status === undefined) {\n            if (body === null) {\n                status = 204;\n                statusText = statusText || 'No Content';\n            }\n            else {\n                statusText = statusText || 'OK';\n            }\n        }\n        if (statusText === undefined) {\n            throw new Error('statusText is required when setting a custom status.');\n        }\n        if (status >= 200 && status < 300) {\n            this.observer.next(new HttpResponse({ body: body, headers: headers, status: status, statusText: statusText, url: url }));\n            this.observer.complete();\n        }\n        else {\n            this.observer.error(new HttpErrorResponse({ error: body, headers: headers, status: status, statusText: statusText, url: url }));\n        }\n    };\n    /**\n     * Resolve the request by returning an `ErrorEvent` (e.g. simulating a network failure).\n     */\n    /**\n     * Resolve the request by returning an `ErrorEvent` (e.g. simulating a network failure).\n     * @param {?} error\n     * @param {?=} opts\n     * @return {?}\n     */\n    TestRequest.prototype.error = /**\n     * Resolve the request by returning an `ErrorEvent` (e.g. simulating a network failure).\n     * @param {?} error\n     * @param {?=} opts\n     * @return {?}\n     */\n    function (error, opts) {\n        if (opts === void 0) { opts = {}; }\n        if (this.cancelled) {\n            throw new Error(\"Cannot return an error for a cancelled request.\");\n        }\n        if (opts.status && opts.status >= 200 && opts.status < 300) {\n            throw new Error(\"error() called with a successful status.\");\n        }\n        var /** @type {?} */ headers = (opts.headers instanceof HttpHeaders) ? opts.headers : new HttpHeaders(opts.headers);\n        this.observer.error(new HttpErrorResponse({\n            error: error,\n            headers: headers,\n            status: opts.status || 0,\n            statusText: opts.statusText || '',\n            url: this.request.urlWithParams,\n        }));\n    };\n    /**\n     * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n     * request.\n     */\n    /**\n     * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n     * request.\n     * @param {?} event\n     * @return {?}\n     */\n    TestRequest.prototype.event = /**\n     * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n     * request.\n     * @param {?} event\n     * @return {?}\n     */\n    function (event) {\n        if (this.cancelled) {\n            throw new Error(\"Cannot send events to a cancelled request.\");\n        }\n        this.observer.next(event);\n    };\n    return TestRequest;\n}());\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * \\@stable\n */\nexport { TestRequest };\nfunction TestRequest_tsickle_Closure_declarations() {\n    /**\n     * \\@internal set by `HttpClientTestingBackend`\n     * @type {?}\n     */\n    TestRequest.prototype._cancelled;\n    /** @type {?} */\n    TestRequest.prototype.request;\n    /** @type {?} */\n    TestRequest.prototype.observer;\n}\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n * @param {?} body\n * @return {?}\n */\nfunction _toArrayBufferBody(body) {\n    if (typeof ArrayBuffer === 'undefined') {\n        throw new Error('ArrayBuffer responses are not supported on this platform.');\n    }\n    if (body instanceof ArrayBuffer) {\n        return body;\n    }\n    throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to a Blob.\n * @param {?} body\n * @return {?}\n */\nfunction _toBlob(body) {\n    if (typeof Blob === 'undefined') {\n        throw new Error('Blob responses are not supported on this platform.');\n    }\n    if (body instanceof Blob) {\n        return body;\n    }\n    if (ArrayBuffer && body instanceof ArrayBuffer) {\n        return new Blob([body]);\n    }\n    throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to JSON data.\n * @param {?} body\n * @param {?=} format\n * @return {?}\n */\nfunction _toJsonBody(body, format) {\n    if (format === void 0) { format = 'JSON'; }\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error(\"Automatic conversion to \" + format + \" is not supported for ArrayBuffers.\");\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error(\"Automatic conversion to \" + format + \" is not supported for Blobs.\");\n    }\n    if (typeof body === 'string' || typeof body === 'number' || typeof body === 'object' ||\n        Array.isArray(body)) {\n        return body;\n    }\n    throw new Error(\"Automatic conversion to \" + format + \" is not supported for response type.\");\n}\n/**\n * Helper function to convert a response body to a string.\n * @param {?} body\n * @return {?}\n */\nfunction _toTextBody(body) {\n    if (typeof body === 'string') {\n        return body;\n    }\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error('Automatic conversion to text is not supported for Blobs.');\n    }\n    return JSON.stringify(_toJsonBody(body, 'text'));\n}\n/**\n * Convert a response body to the requested type.\n * @param {?} responseType\n * @param {?} body\n * @return {?}\n */\nfunction _maybeConvertBody(responseType, body) {\n    if (body === null) {\n        return null;\n    }\n    switch (responseType) {\n        case 'arraybuffer':\n            return _toArrayBufferBody(body);\n        case 'blob':\n            return _toBlob(body);\n        case 'json':\n            return _toJsonBody(body);\n        case 'text':\n            return _toTextBody(body);\n        default:\n            throw new Error(\"Unsupported responseType: \" + responseType);\n    }\n}\n//# sourceMappingURL=request.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { HttpEventType } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs/Observable';\nimport { TestRequest } from './request';\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n * \\@stable\n */\nvar HttpClientTestingBackend = /** @class */ (function () {\n    function HttpClientTestingBackend() {\n        /**\n         * List of pending requests which have not yet been expected.\n         */\n        this.open = [];\n    }\n    /**\n     * Handle an incoming request by queueing it in the list of open requests.\n     */\n    /**\n     * Handle an incoming request by queueing it in the list of open requests.\n     * @param {?} req\n     * @return {?}\n     */\n    HttpClientTestingBackend.prototype.handle = /**\n     * Handle an incoming request by queueing it in the list of open requests.\n     * @param {?} req\n     * @return {?}\n     */\n    function (req) {\n        var _this = this;\n        return new Observable(function (observer) {\n            var /** @type {?} */ testReq = new TestRequest(req, observer);\n            _this.open.push(testReq);\n            observer.next(/** @type {?} */ ({ type: HttpEventType.Sent }));\n            return function () { testReq._cancelled = true; };\n        });\n    };\n    /**\n     * Helper function to search for requests in the list of open requests.\n     * @param {?} match\n     * @return {?}\n     */\n    HttpClientTestingBackend.prototype._match = /**\n     * Helper function to search for requests in the list of open requests.\n     * @param {?} match\n     * @return {?}\n     */\n    function (match) {\n        if (typeof match === 'string') {\n            return this.open.filter(function (testReq) { return testReq.request.urlWithParams === match; });\n        }\n        else if (typeof match === 'function') {\n            return this.open.filter(function (testReq) { return match(testReq.request); });\n        }\n        else {\n            return this.open.filter(function (testReq) {\n                return (!match.method || testReq.request.method === match.method.toUpperCase()) &&\n                    (!match.url || testReq.request.urlWithParams === match.url);\n            });\n        }\n    };\n    /**\n     * Search for requests in the list of open requests, and return all that match\n     * without asserting anything about the number of matches.\n     */\n    /**\n     * Search for requests in the list of open requests, and return all that match\n     * without asserting anything about the number of matches.\n     * @param {?} match\n     * @return {?}\n     */\n    HttpClientTestingBackend.prototype.match = /**\n     * Search for requests in the list of open requests, and return all that match\n     * without asserting anything about the number of matches.\n     * @param {?} match\n     * @return {?}\n     */\n    function (match) {\n        var _this = this;\n        var /** @type {?} */ results = this._match(match);\n        results.forEach(function (result) {\n            var /** @type {?} */ index = _this.open.indexOf(result);\n            if (index !== -1) {\n                _this.open.splice(index, 1);\n            }\n        });\n        return results;\n    };\n    /**\n     * Expect that a single outstanding request matches the given matcher, and return\n     * it.\n     *\n     * Requests returned through this API will no longer be in the list of open requests,\n     * and thus will not match twice.\n     */\n    /**\n     * Expect that a single outstanding request matches the given matcher, and return\n     * it.\n     *\n     * Requests returned through this API will no longer be in the list of open requests,\n     * and thus will not match twice.\n     * @param {?} match\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpClientTestingBackend.prototype.expectOne = /**\n     * Expect that a single outstanding request matches the given matcher, and return\n     * it.\n     *\n     * Requests returned through this API will no longer be in the list of open requests,\n     * and thus will not match twice.\n     * @param {?} match\n     * @param {?=} description\n     * @return {?}\n     */\n    function (match, description) {\n        description = description || this.descriptionFromMatcher(match);\n        var /** @type {?} */ matches = this.match(match);\n        if (matches.length > 1) {\n            throw new Error(\"Expected one matching request for criteria \\\"\" + description + \"\\\", found \" + matches.length + \" requests.\");\n        }\n        if (matches.length === 0) {\n            throw new Error(\"Expected one matching request for criteria \\\"\" + description + \"\\\", found none.\");\n        }\n        return matches[0];\n    };\n    /**\n     * Expect that no outstanding requests match the given matcher, and throw an error\n     * if any do.\n     */\n    /**\n     * Expect that no outstanding requests match the given matcher, and throw an error\n     * if any do.\n     * @param {?} match\n     * @param {?=} description\n     * @return {?}\n     */\n    HttpClientTestingBackend.prototype.expectNone = /**\n     * Expect that no outstanding requests match the given matcher, and throw an error\n     * if any do.\n     * @param {?} match\n     * @param {?=} description\n     * @return {?}\n     */\n    function (match, description) {\n        description = description || this.descriptionFromMatcher(match);\n        var /** @type {?} */ matches = this.match(match);\n        if (matches.length > 0) {\n            throw new Error(\"Expected zero matching requests for criteria \\\"\" + description + \"\\\", found \" + matches.length + \".\");\n        }\n    };\n    /**\n     * Validate that there are no outstanding requests.\n     */\n    /**\n     * Validate that there are no outstanding requests.\n     * @param {?=} opts\n     * @return {?}\n     */\n    HttpClientTestingBackend.prototype.verify = /**\n     * Validate that there are no outstanding requests.\n     * @param {?=} opts\n     * @return {?}\n     */\n    function (opts) {\n        if (opts === void 0) { opts = {}; }\n        var /** @type {?} */ open = this.open;\n        // It's possible that some requests may be cancelled, and this is expected.\n        // The user can ask to ignore open requests which have been cancelled.\n        if (opts.ignoreCancelled) {\n            open = open.filter(function (testReq) { return !testReq.cancelled; });\n        }\n        if (open.length > 0) {\n            // Show the methods and URLs of open requests in the error, for convenience.\n            var /** @type {?} */ requests = open.map(function (testReq) {\n                var /** @type {?} */ url = testReq.request.urlWithParams.split('?')[0];\n                var /** @type {?} */ method = testReq.request.method;\n                return method + \" \" + url;\n            })\n                .join(', ');\n            throw new Error(\"Expected no open requests, found \" + open.length + \": \" + requests);\n        }\n    };\n    /**\n     * @param {?} matcher\n     * @return {?}\n     */\n    HttpClientTestingBackend.prototype.descriptionFromMatcher = /**\n     * @param {?} matcher\n     * @return {?}\n     */\n    function (matcher) {\n        if (typeof matcher === 'string') {\n            return \"Match URL: \" + matcher;\n        }\n        else if (typeof matcher === 'object') {\n            var /** @type {?} */ method = matcher.method || '(any)';\n            var /** @type {?} */ url = matcher.url || '(any)';\n            return \"Match method: \" + method + \", URL: \" + url;\n        }\n        else {\n            return \"Match by function: \" + matcher.name;\n        }\n    };\n    HttpClientTestingBackend.decorators = [\n        { type: Injectable },\n    ];\n    /** @nocollapse */\n    HttpClientTestingBackend.ctorParameters = function () { return []; };\n    return HttpClientTestingBackend;\n}());\nexport { HttpClientTestingBackend };\nfunction HttpClientTestingBackend_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpClientTestingBackend.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpClientTestingBackend.ctorParameters;\n    /**\n     * List of pending requests which have not yet been expected.\n     * @type {?}\n     */\n    HttpClientTestingBackend.prototype.open;\n}\n//# sourceMappingURL=backend.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { HttpBackend, HttpClientModule } from '@angular/common/http';\nimport { NgModule } from '@angular/core';\nimport { HttpTestingController } from './api';\nimport { HttpClientTestingBackend } from './backend';\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * \\@stable\n */\nvar HttpClientTestingModule = /** @class */ (function () {\n    function HttpClientTestingModule() {\n    }\n    HttpClientTestingModule.decorators = [\n        { type: NgModule, args: [{\n                    imports: [\n                        HttpClientModule,\n                    ],\n                    providers: [\n                        HttpClientTestingBackend,\n                        { provide: HttpBackend, useExisting: HttpClientTestingBackend },\n                        { provide: HttpTestingController, useExisting: HttpClientTestingBackend },\n                    ],\n                },] },\n    ];\n    /** @nocollapse */\n    HttpClientTestingModule.ctorParameters = function () { return []; };\n    return HttpClientTestingModule;\n}());\nexport { HttpClientTestingModule };\nfunction HttpClientTestingModule_tsickle_Closure_declarations() {\n    /** @type {!Array<{type: !Function, args: (undefined|!Array<?>)}>} */\n    HttpClientTestingModule.decorators;\n    /**\n     * @nocollapse\n     * @type {function(): !Array<(null|{type: ?, decorators: (undefined|!Array<{type: !Function, args: (undefined|!Array<?>)}>)})>}\n     */\n    HttpClientTestingModule.ctorParameters;\n}\n//# sourceMappingURL=module.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nexport { HttpTestingController } from './src/api';\nexport { HttpClientTestingModule } from './src/module';\nexport { TestRequest } from './src/request';\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { HttpTestingController, HttpClientTestingModule, TestRequest } from './public_api';\nexport { HttpClientTestingBackend as ɵa } from './src/backend';\n//# sourceMappingURL=testing.js.map"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;;;AAiBA,AAAkC;AAClC,AAMA;;;;;;;AAOA,IAOA,qBAAqB,kBAAkB,YAAY;IAC/C,SAAS,qBAAqB,GAAG;KAChC;IACD,OAAO,qBAAqB,CAAC;CAChC,EAAE,CAAC;;AC1CJ;;;;;;;;;;;AAWA,AACA;;;;;;;;AAQA,IAQA,WAAW,kBAAkB,YAAY;IACrC,SAAS,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE;QACpC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;;;;QAIzB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;KAC3B;IACD,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE;;;;QAItD,GAAG;;;;QAIH,YAAY,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE;QACvC,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACrB,CAAC,CAAC;;;;;;;;;;;;;;;;IAgBH,WAAW,CAAC,SAAS,CAAC,KAAK;;;;;;;;;IAS3B,UAAU,IAAI,EAAE,IAAI,EAAE;QAClB,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACxD;QACD,qBAAqB,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;QACtD,qBAAqB,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,YAAY,WAAW,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpH,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC1D,qBAAqB,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QAClD,qBAAqB,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;QAC5E,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC3B,IAAI,IAAI,KAAK,IAAI,EAAE;gBACf,MAAM,GAAG,GAAG,CAAC;gBACb,UAAU,GAAG,UAAU,IAAI,YAAY,CAAC;aAC3C;iBACI;gBACD,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC;aACnC;SACJ;QACD,IAAI,UAAU,KAAK,SAAS,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;SAC3E;QACD,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE;YAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACzH,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;SAC5B;aACI;YACD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;SACnI;KACJ,CAAC;;;;;;;;;;IAUF,WAAW,CAAC,SAAS,CAAC,KAAK;;;;;;IAM3B,UAAU,KAAK,EAAE,IAAI,EAAE;QACnB,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACtE;QACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;YACxD,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC/D;QACD,qBAAqB,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,YAAY,WAAW,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC;YACtC,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;YACxB,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,EAAE;YACjC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;SAClC,CAAC,CAAC,CAAC;KACP,CAAC;;;;;;;;;;;IAWF,WAAW,CAAC,SAAS,CAAC,KAAK;;;;;;IAM3B,UAAU,KAAK,EAAE;QACb,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7B,CAAC;IACF,OAAO,WAAW,CAAC;CACtB,EAAE,CAAC,CAAC;AACL,AAoBA;;;;;AAKA,SAAS,kBAAkB,CAAC,IAAI,EAAE;IAC9B,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACpC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;KAChF;IACD,IAAI,IAAI,YAAY,WAAW,EAAE;QAC7B,OAAO,IAAI,CAAC;KACf;IACD,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;CAC9F;;;;;;AAMD,SAAS,OAAO,CAAC,IAAI,EAAE;IACnB,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;KACzE;IACD,IAAI,IAAI,YAAY,IAAI,EAAE;QACtB,OAAO,IAAI,CAAC;KACf;IACD,IAAI,WAAW,IAAI,IAAI,YAAY,WAAW,EAAE;QAC5C,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KAC3B;IACD,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;CACvF;;;;;;;AAOD,SAAS,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE;IAC/B,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC,EAAE;IAC3C,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,IAAI,YAAY,WAAW,EAAE;QACnE,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,MAAM,GAAG,qCAAqC,CAAC,CAAC;KAChG;IACD,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;QACrD,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,MAAM,GAAG,8BAA8B,CAAC,CAAC;KACzF;IACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ;QAChF,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACrB,OAAO,IAAI,CAAC;KACf;IACD,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,MAAM,GAAG,sCAAsC,CAAC,CAAC;CACjG;;;;;;AAMD,SAAS,WAAW,CAAC,IAAI,EAAE;IACvB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC1B,OAAO,IAAI,CAAC;KACf;IACD,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,IAAI,YAAY,WAAW,EAAE;QACnE,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;KACtF;IACD,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;QACrD,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;KAC/E;IACD,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;CACpD;;;;;;;AAOD,SAAS,iBAAiB,CAAC,YAAY,EAAE,IAAI,EAAE;IAC3C,IAAI,IAAI,KAAK,IAAI,EAAE;QACf,OAAO,IAAI,CAAC;KACf;IACD,QAAQ,YAAY;QAChB,KAAK,aAAa;YACd,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACpC,KAAK,MAAM;YACP,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,KAAK,MAAM;YACP,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7B,KAAK,MAAM;YACP,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7B;YACI,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,YAAY,CAAC,CAAC;KACpE;CACJ;;AC7QD;;;;;;;;;;;AAWA,AAIA;;;;;;;;;;;AAWA,IAAI,wBAAwB,kBAAkB,YAAY;IACtD,SAAS,wBAAwB,GAAG;;;;QAIhC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;KAClB;;;;;;;;;IASD,wBAAwB,CAAC,SAAS,CAAC,MAAM;;;;;IAKzC,UAAU,GAAG,EAAE;QACX,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,UAAU,CAAC,UAAU,QAAQ,EAAE;YACtC,qBAAqB,OAAO,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC9D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,QAAQ,CAAC,IAAI,mBAAmB,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC;YAC/D,OAAO,YAAY,EAAE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC;SACrD,CAAC,CAAC;KACN,CAAC;;;;;;IAMF,wBAAwB,CAAC,SAAS,CAAC,MAAM;;;;;IAKzC,UAAU,KAAK,EAAE;QACb,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;SACnG;aACI,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAClC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;SAClF;aACI;YACD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE;gBACvC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE;qBACzE,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC;aACnE,CAAC,CAAC;SACN;KACJ,CAAC;;;;;;;;;;;IAWF,wBAAwB,CAAC,SAAS,CAAC,KAAK;;;;;;IAMxC,UAAU,KAAK,EAAE;QACb,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,qBAAqB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAClD,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;YAC9B,qBAAqB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACd,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC/B;SACJ,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;KAClB,CAAC;;;;;;;;;;;;;;;;;;IAkBF,wBAAwB,CAAC,SAAS,CAAC,SAAS;;;;;;;;;;IAU5C,UAAU,KAAK,EAAE,WAAW,EAAE;QAC1B,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAChE,qBAAqB,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,+CAA+C,GAAG,WAAW,GAAG,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;SACjI;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,+CAA+C,GAAG,WAAW,GAAG,iBAAiB,CAAC,CAAC;SACtG;QACD,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;KACrB,CAAC;;;;;;;;;;;;IAYF,wBAAwB,CAAC,SAAS,CAAC,UAAU;;;;;;;IAO7C,UAAU,KAAK,EAAE,WAAW,EAAE;QAC1B,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAChE,qBAAqB,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,iDAAiD,GAAG,WAAW,GAAG,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;SAC1H;KACJ,CAAC;;;;;;;;;IASF,wBAAwB,CAAC,SAAS,CAAC,MAAM;;;;;IAKzC,UAAU,IAAI,EAAE;QACZ,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE;QACnC,qBAAqB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;;;QAGtC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;SACzE;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;;YAEjB,qBAAqB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;gBACxD,qBAAqB,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvE,qBAAqB,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBACrD,OAAO,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;aAC7B,CAAC;iBACG,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC;SACxF;KACJ,CAAC;;;;;IAKF,wBAAwB,CAAC,SAAS,CAAC,sBAAsB;;;;IAIzD,UAAU,OAAO,EAAE;QACf,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC7B,OAAO,aAAa,GAAG,OAAO,CAAC;SAClC;aACI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAClC,qBAAqB,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC;YACxD,qBAAqB,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC;YAClD,OAAO,gBAAgB,GAAG,MAAM,GAAG,SAAS,GAAG,GAAG,CAAC;SACtD;aACI;YACD,OAAO,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC;SAC/C;KACJ,CAAC;IACF,wBAAwB,CAAC,UAAU,GAAG;QAClC,EAAE,IAAI,EAAE,UAAU,EAAE;KACvB,CAAC;;IAEF,wBAAwB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACrE,OAAO,wBAAwB,CAAC;CACnC,EAAE,CAAC;;ACpOJ;;;;;;;;;;;AAWA,AAIA;;;;;;;AAOA,IAAI,uBAAuB,kBAAkB,YAAY;IACrD,SAAS,uBAAuB,GAAG;KAClC;IACD,uBAAuB,CAAC,UAAU,GAAG;QACjC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBACb,OAAO,EAAE;wBACL,gBAAgB;qBACnB;oBACD,SAAS,EAAE;wBACP,wBAAwB;wBACxB,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,wBAAwB,EAAE;wBAC/D,EAAE,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,wBAAwB,EAAE;qBAC5E;iBACJ,EAAE,EAAE;KAChB,CAAC;;IAEF,uBAAuB,CAAC,cAAc,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACpE,OAAO,uBAAuB,CAAC;CAClC,EAAE,CAAC;;ACxCJ;;;;;;;;;;GAUG;;ACVH;;;;;;GAMG;;;;"}