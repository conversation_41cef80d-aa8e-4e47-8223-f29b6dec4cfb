{"version": 3, "file": "cdk-a11y.umd.min.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../src/cdk/a11y/interactivity-checker/interactivity-checker.ts", "../../src/cdk/a11y/aria-describer/aria-reference.ts", "../../src/cdk/a11y/aria-describer/aria-describer.ts", "../../src/cdk/a11y/live-announcer/live-announcer.ts", "../../src/cdk/a11y/focus-monitor/focus-monitor.ts", "../../src/cdk/a11y/fake-mousedown.ts", "../../src/cdk/a11y/focus-trap/focus-trap.ts", "../../src/cdk/a11y/key-manager/list-key-manager.ts", "../../src/cdk/a11y/key-manager/activedescendant-key-manager.ts", "../../src/cdk/a11y/key-manager/focus-key-manager.ts", "../../src/cdk/a11y/a11y-module.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\n\n\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\n@Injectable()\nexport class InteractivityChecker {\n\n  constructor(private _platform: Platform) {}\n\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element: HTMLElement): boolean {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element: HTMLElement): boolean {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element: HTMLElement): boolean {\n    // Nothing is tabbable on the the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n\n    const frameElement = getFrameElement(getWindow(element));\n\n    if (frameElement) {\n      const frameType = frameElement && frameElement.nodeName.toLowerCase();\n\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n\n      // Webkit and Blink consider anything inside of an <object> element as non-tabbable.\n      if ((this._platform.BLINK || this._platform.WEBKIT) && frameType === 'object') {\n        return false;\n      }\n\n      // Webkit and Blink disable tabbing to an element inside of an invisible frame.\n      if ((this._platform.BLINK || this._platform.WEBKIT) && !this.isVisible(frameElement)) {\n        return false;\n      }\n\n    }\n\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'iframe') {\n      // The frames may be tabbable depending on content, but it's not possibly to reliably\n      // investigate the content of the frames.\n      return false;\n    }\n\n    if (nodeName === 'audio') {\n      if (!element.hasAttribute('controls')) {\n        // By default an <audio> element without the controls enabled is not tabbable.\n        return false;\n      } else if (this._platform.BLINK) {\n        // In Blink <audio controls> elements are always tabbable.\n        return true;\n      }\n    }\n\n    if (nodeName === 'video') {\n      if (!element.hasAttribute('controls') && this._platform.TRIDENT) {\n        // In Trident a <video> element without the controls enabled is not tabbable.\n        return false;\n      } else if (this._platform.BLINK || this._platform.FIREFOX) {\n        // In Chrome and Firefox <video controls> elements are always tabbable.\n        return true;\n      }\n    }\n\n    if (nodeName === 'object' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      // In all Blink and WebKit based browsers <object> elements are never tabbable.\n      return false;\n    }\n\n    // In iOS the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n\n    return element.tabIndex >= 0;\n  }\n\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element: HTMLElement): boolean {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && this.isVisible(element);\n  }\n\n}\n\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window: Window) {\n  try {\n    return window.frameElement as HTMLElement;\n  } catch (e) {\n    return null;\n  }\n}\n\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element: HTMLElement): boolean {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight ||\n      (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n\n/** Gets whether an element's  */\nfunction isNativeFormElement(element: Node) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' ||\n      nodeName === 'select' ||\n      nodeName === 'button' ||\n      nodeName === 'textarea';\n}\n\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element: HTMLElement): boolean {\n  return isInputElement(element) && element.type == 'hidden';\n}\n\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element: HTMLElement): boolean {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n\n/** Gets whether an element is an input element. */\nfunction isInputElement(element: HTMLElement): element is HTMLInputElement {\n  return element.nodeName.toLowerCase() == 'input';\n}\n\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element: HTMLElement): element is HTMLAnchorElement {\n  return element.nodeName.toLowerCase() == 'a';\n}\n\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element: HTMLElement): boolean {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex');\n\n  // IE11 parses tabindex=\"\" as the value \"-32768\"\n  if (tabIndex == '-32768') {\n    return false;\n  }\n\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element: HTMLElement): number | null {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element: HTMLElement): boolean {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && (element as HTMLInputElement).type;\n\n  return inputType === 'text'\n      || inputType === 'password'\n      || nodeName === 'select'\n      || nodeName === 'textarea';\n}\n\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element: HTMLElement): boolean {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return isNativeFormElement(element) ||\n      isAnchorWithHref(element) ||\n      element.hasAttribute('contenteditable') ||\n      hasValidTabIndex(element);\n}\n\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node: HTMLElement): Window {\n  return node.ownerDocument.defaultView || window;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** IDs are deliminated by an empty space, as per the spec. */\nconst ID_DELIMINATOR = ' ';\n\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function addAriaReferencedId(el: Element, attr: string, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  if (ids.some(existingId => existingId.trim() == id.trim())) { return; }\n  ids.push(id.trim());\n\n  el.setAttribute(attr, ids.join(ID_DELIMINATOR));\n}\n\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function removeAriaReferencedId(el: Element, attr: string, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  const filteredIds = ids.filter(val => val != id.trim());\n\n  el.setAttribute(attr, filteredIds.join(ID_DELIMINATOR));\n}\n\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function getAriaReferenceIds(el: Element, attr: string): string[] {\n  // Get string array of all individual ids (whitespace deliminated) in the attribute value\n  return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Inject, InjectionToken, Optional, SkipSelf} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId} from './aria-reference';\n\n/**\n * Interface used to register message elements and keep a count of how many registrations have\n * the same message and the reference to the message element used for the `aria-describedby`.\n */\nexport interface RegisteredMessage {\n  /** The element containing the message. */\n  messageElement: Element;\n\n  /** The number of elements that reference this message element via `aria-describedby`. */\n  referenceCount: number;\n}\n\n/** ID used for the body container where all messages are appended. */\nexport const MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n\n/** ID prefix used for each created message element. */\nexport const CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n\n/** Attribute given to each host element that is described by a message element. */\nexport const CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n\n/** Global map of all registered message elements that have been placed into the document. */\nconst messageRegistry = new Map<string, RegisteredMessage>();\n\n/** Container for all registered messages. */\nlet messagesContainer: HTMLElement | null = null;\n\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n * @docs-private\n */\n@Injectable()\nexport class AriaDescriber {\n  private _document: Document;\n\n  constructor(@Inject(DOCUMENT) _document: any) {\n    this._document = _document;\n  }\n\n  /**\n   * Adds to the host element an aria-describedby reference to a hidden element that contains\n   * the message. If the same message has already been registered, then it will reuse the created\n   * message element.\n   */\n  describe(hostElement: Element, message: string) {\n    if (hostElement.nodeType !== this._document.ELEMENT_NODE || !message.trim()) {\n      return;\n    }\n\n    if (!messageRegistry.has(message)) {\n      this._createMessageElement(message);\n    }\n\n    if (!this._isElementDescribedByMessage(hostElement, message)) {\n      this._addMessageReference(hostElement, message);\n    }\n  }\n\n  /** Removes the host element's aria-describedby reference to the message element. */\n  removeDescription(hostElement: Element, message: string) {\n    if (hostElement.nodeType !== this._document.ELEMENT_NODE || !message.trim()) {\n      return;\n    }\n\n    if (this._isElementDescribedByMessage(hostElement, message)) {\n      this._removeMessageReference(hostElement, message);\n    }\n\n    const registeredMessage = messageRegistry.get(message);\n    if (registeredMessage && registeredMessage.referenceCount === 0) {\n      this._deleteMessageElement(message);\n    }\n\n    if (messagesContainer && messagesContainer.childNodes.length === 0) {\n      this._deleteMessagesContainer();\n    }\n  }\n\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements =\n        this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}]`);\n\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n\n    if (messagesContainer) {\n      this._deleteMessagesContainer();\n    }\n\n    messageRegistry.clear();\n  }\n\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  private _createMessageElement(message: string) {\n    const messageElement = this._document.createElement('div');\n    messageElement.setAttribute('id', `${CDK_DESCRIBEDBY_ID_PREFIX}-${nextId++}`);\n    messageElement.appendChild(this._document.createTextNode(message)!);\n\n    if (!messagesContainer) { this._createMessagesContainer(); }\n    messagesContainer!.appendChild(messageElement);\n\n    messageRegistry.set(message, {messageElement, referenceCount: 0});\n  }\n\n  /** Deletes the message element from the global messages container. */\n  private _deleteMessageElement(message: string) {\n    const registeredMessage = messageRegistry.get(message);\n    const messageElement = registeredMessage && registeredMessage.messageElement;\n    if (messagesContainer && messageElement) {\n      messagesContainer.removeChild(messageElement);\n    }\n    messageRegistry.delete(message);\n  }\n\n  /** Creates the global container for all aria-describedby messages. */\n  private _createMessagesContainer() {\n    messagesContainer = this._document.createElement('div');\n    messagesContainer.setAttribute('id', MESSAGES_CONTAINER_ID);\n    messagesContainer.setAttribute('aria-hidden', 'true');\n    messagesContainer.style.display = 'none';\n    this._document.body.appendChild(messagesContainer);\n  }\n\n  /** Deletes the global messages container. */\n  private _deleteMessagesContainer() {\n    if (messagesContainer && messagesContainer.parentNode) {\n      messagesContainer.parentNode.removeChild(messagesContainer);\n      messagesContainer = null;\n    }\n  }\n\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  private _removeCdkDescribedByReferenceIds(element: Element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby')\n        .filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  private _addMessageReference(element: Element, message: string) {\n    const registeredMessage = messageRegistry.get(message)!;\n\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, '');\n\n    registeredMessage.referenceCount++;\n  }\n\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  private _removeMessageReference(element: Element, message: string) {\n    const registeredMessage = messageRegistry.get(message)!;\n    registeredMessage.referenceCount--;\n\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n\n  /** Returns true if the element has been described by the provided message ID. */\n  private _isElementDescribedByMessage(element: Element, message: string): boolean {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = messageRegistry.get(message);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n\n}\n\n/** @docs-private */\nexport function ARIA_DESCRIBER_PROVIDER_FACTORY(parentDispatcher: AriaDescriber, _document: any) {\n  return parentDispatcher || new AriaDescriber(_document);\n}\n\n/** @docs-private */\nexport const ARIA_DESCRIBER_PROVIDER = {\n  // If there is already an AriaDescriber available, use that. Otherwise, provide a new one.\n  provide: AriaDescriber,\n  deps: [\n    [new Optional(), new SkipSelf(), AriaDescriber],\n    DOCUMENT as InjectionToken<any>\n  ],\n  useFactory: ARIA_DESCRIBER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Injectable,\n  InjectionToken,\n  Optional,\n  Inject,\n  SkipSelf,\n  OnDestroy,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\n\n\nexport const LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken<HTMLElement>('liveAnnouncerElement');\n\n/** Possible politeness levels. */\nexport type AriaLivePoliteness = 'off' | 'polite' | 'assertive';\n\n@Injectable()\nexport class LiveAnnouncer implements OnD<PERSON>roy {\n  private _liveElement: Element;\n\n  constructor(\n      @Optional() @Inject(LIVE_ANNOUNCER_ELEMENT_TOKEN) elementToken: any,\n      @Inject(DOCUMENT) private _document: any) {\n\n    // We inject the live element as `any` because the constructor signature cannot reference\n    // browser globals (HTMLElement) on non-browser environments, since having a class decorator\n    // causes TypeScript to preserve the constructor signature types.\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n\n  /**\n   * Announces a message to screenreaders.\n   * @param message Message to be announced to the screenreader\n   * @param politeness The politeness of the announcer element\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, politeness: AriaLivePoliteness = 'polite'): Promise<void> {\n    this._liveElement.textContent = '';\n\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return new Promise(resolve => {\n      setTimeout(() => {\n        this._liveElement.textContent = message;\n        resolve();\n      }, 100);\n    });\n  }\n\n  ngOnDestroy() {\n    if (this._liveElement && this._liveElement.parentNode) {\n      this._liveElement.parentNode.removeChild(this._liveElement);\n    }\n  }\n\n  private _createLiveElement(): Element {\n    let liveEl = this._document.createElement('div');\n\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n\n    this._document.body.appendChild(liveEl);\n\n    return liveEl;\n  }\n\n}\n\n/** @docs-private */\nexport function LIVE_ANNOUNCER_PROVIDER_FACTORY(\n    parentDispatcher: LiveAnnouncer, liveElement: any, _document: any) {\n  return parentDispatcher || new LiveAnnouncer(liveElement, _document);\n}\n\n/** @docs-private */\nexport const LIVE_ANNOUNCER_PROVIDER = {\n  // If there is already a LiveAnnouncer available, use that. Otherwise, provide a new one.\n  provide: LiveAnnouncer,\n  deps: [\n    [new Optional(), new SkipSelf(), LiveAnnouncer],\n    [new Optional(), new Inject(LIVE_ANNOUNCER_ELEMENT_TOKEN)],\n    DOCUMENT,\n  ],\n  useFactory: LIVE_ANNOUNCER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Platform, supportsPassiveEventListeners} from '@angular/cdk/platform';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Injectable,\n  NgZone,\n  OnDestroy,\n  Optional,\n  Output,\n  Renderer2,\n  SkipSelf,\n} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {of as observableOf} from 'rxjs/observable/of';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\n\n\n// This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n// that a value of around 650ms seems appropriate.\nexport const TOUCH_BUFFER_MS = 650;\n\n\nexport type FocusOrigin = 'touch' | 'mouse' | 'keyboard' | 'program' | null;\n\n\ntype MonitoredElementInfo = {\n  unlisten: Function,\n  checkChildren: boolean,\n  subject: Subject<FocusOrigin>\n};\n\n\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\n@Injectable()\nexport class FocusMonitor implements OnDestroy {\n  /** The focus origin that the next focus event is a result of. */\n  private _origin: FocusOrigin = null;\n\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  private _lastFocusOrigin: FocusOrigin;\n\n  /** Whether the window has just been focused. */\n  private _windowFocused = false;\n\n  /** The target of the last touch event. */\n  private _lastTouchTarget: EventTarget | null;\n\n  /** The timeout id of the touch timeout, used to cancel timeout later. */\n  private _touchTimeoutId: number;\n\n  /** The timeout id of the window focus timeout. */\n  private _windowFocusTimeoutId: number;\n\n  /** The timeout id of the origin clearing timeout. */\n  private _originTimeoutId: number;\n\n  /** Map of elements being monitored to their info. */\n  private _elementInfo = new Map<HTMLElement, MonitoredElementInfo>();\n\n  /** A map of global objects to lists of current listeners. */\n  private _unregisterGlobalListeners = () => {};\n\n  /** The number of elements currently being monitored. */\n  private _monitoredElementCount = 0;\n\n  constructor(private _ngZone: NgZone, private _platform: Platform) {}\n\n  /**\n   * @docs-private\n   * @deprecated renderer param no longer needed.\n   * @deletion-target 6.0.0\n   */\n  monitor(element: HTMLElement, renderer: Renderer2, checkChildren: boolean):\n      Observable<FocusOrigin>;\n  /**\n   * Monitors focus on an element and applies appropriate CSS classes.\n   * @param element The element to monitor\n   * @param checkChildren Whether to count the element as focused when its children are focused.\n   * @returns An observable that emits when the focus state of the element changes.\n   *     When the element is blurred, null will be emitted.\n   */\n  monitor(element: HTMLElement, checkChildren?: boolean): Observable<FocusOrigin>;\n  monitor(\n      element: HTMLElement,\n      renderer?: Renderer2 | boolean,\n      checkChildren?: boolean): Observable<FocusOrigin> {\n    // TODO(mmalerba): clean up after deprecated signature is removed.\n    if (!(renderer instanceof Renderer2)) {\n      checkChildren = renderer;\n    }\n    checkChildren = !!checkChildren;\n\n    // Do nothing if we're not on the browser platform.\n    if (!this._platform.isBrowser) {\n      return observableOf(null);\n    }\n    // Check if we're already monitoring this element.\n    if (this._elementInfo.has(element)) {\n      let cachedInfo = this._elementInfo.get(element);\n      cachedInfo!.checkChildren = checkChildren;\n      return cachedInfo!.subject.asObservable();\n    }\n\n    // Create monitored element info.\n    let info: MonitoredElementInfo = {\n      unlisten: () => {},\n      checkChildren: checkChildren,\n      subject: new Subject<FocusOrigin>()\n    };\n    this._elementInfo.set(element, info);\n    this._incrementMonitoredElementCount();\n\n    // Start listening. We need to listen in capture phase since focus events don't bubble.\n    let focusListener = (event: FocusEvent) => this._onFocus(event, element);\n    let blurListener = (event: FocusEvent) => this._onBlur(event, element);\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('focus', focusListener, true);\n      element.addEventListener('blur', blurListener, true);\n    });\n\n    // Create an unlisten function for later.\n    info.unlisten = () => {\n      element.removeEventListener('focus', focusListener, true);\n      element.removeEventListener('blur', blurListener, true);\n    };\n\n    return info.subject.asObservable();\n  }\n\n  /**\n   * Stops monitoring an element and removes all focus classes.\n   * @param element The element to stop monitoring.\n   */\n  stopMonitoring(element: HTMLElement): void {\n    const elementInfo = this._elementInfo.get(element);\n\n    if (elementInfo) {\n      elementInfo.unlisten();\n      elementInfo.subject.complete();\n\n      this._setClasses(element);\n      this._elementInfo.delete(element);\n      this._decrementMonitoredElementCount();\n    }\n  }\n\n  /**\n   * Focuses the element via the specified focus origin.\n   * @param element The element to focus.\n   * @param origin The focus origin.\n   */\n  focusVia(element: HTMLElement, origin: FocusOrigin): void {\n    this._setOriginForCurrentEventQueue(origin);\n    element.focus();\n  }\n\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n\n  /** Register necessary event listeners on the document and window. */\n  private _registerGlobalListeners() {\n    // Do nothing if we're not on the browser platform.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    // On keydown record the origin and clear any touch event that may be in progress.\n    let documentKeydownListener = () => {\n      this._lastTouchTarget = null;\n      this._setOriginForCurrentEventQueue('keyboard');\n    };\n\n    // On mousedown record the origin only if there is not touch target, since a mousedown can\n    // happen as a result of a touch event.\n    let documentMousedownListener = () => {\n      if (!this._lastTouchTarget) {\n        this._setOriginForCurrentEventQueue('mouse');\n      }\n    };\n\n    // When the touchstart event fires the focus event is not yet in the event queue. This means\n    // we can't rely on the trick used above (setting timeout of 0ms). Instead we wait 650ms to\n    // see if a focus happens.\n    let documentTouchstartListener = (event: TouchEvent) => {\n      if (this._touchTimeoutId != null) {\n        clearTimeout(this._touchTimeoutId);\n      }\n      this._lastTouchTarget = event.target;\n      this._touchTimeoutId = setTimeout(() => this._lastTouchTarget = null, TOUCH_BUFFER_MS);\n    };\n\n    // Make a note of when the window regains focus, so we can restore the origin info for the\n    // focused element.\n    let windowFocusListener = () => {\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false, 0);\n    };\n\n    // Note: we listen to events in the capture phase so we can detect them even if the user stops\n    // propagation.\n    this._ngZone.runOutsideAngular(() => {\n      document.addEventListener('keydown', documentKeydownListener, true);\n      document.addEventListener('mousedown', documentMousedownListener, true);\n      document.addEventListener('touchstart', documentTouchstartListener,\n          supportsPassiveEventListeners() ? ({passive: true, capture: true} as any) : true);\n      window.addEventListener('focus', windowFocusListener);\n    });\n\n    this._unregisterGlobalListeners = () => {\n      document.removeEventListener('keydown', documentKeydownListener, true);\n      document.removeEventListener('mousedown', documentMousedownListener, true);\n      document.removeEventListener('touchstart', documentTouchstartListener,\n          supportsPassiveEventListeners() ? ({passive: true, capture: true} as any) : true);\n      window.removeEventListener('focus', windowFocusListener);\n\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._touchTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    };\n  }\n\n  private _toggleClass(element: Element, className: string, shouldSet: boolean) {\n    if (shouldSet) {\n      element.classList.add(className);\n    } else {\n      element.classList.remove(className);\n    }\n  }\n\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  private _setClasses(element: HTMLElement, origin?: FocusOrigin): void {\n    const elementInfo = this._elementInfo.get(element);\n\n    if (elementInfo) {\n      this._toggleClass(element, 'cdk-focused', !!origin);\n      this._toggleClass(element, 'cdk-touch-focused', origin === 'touch');\n      this._toggleClass(element, 'cdk-keyboard-focused', origin === 'keyboard');\n      this._toggleClass(element, 'cdk-mouse-focused', origin === 'mouse');\n      this._toggleClass(element, 'cdk-program-focused', origin === 'program');\n    }\n  }\n\n  /**\n   * Sets the origin and schedules an async function to clear it at the end of the event queue.\n   * @param origin The origin to set.\n   */\n  private _setOriginForCurrentEventQueue(origin: FocusOrigin): void {\n    this._origin = origin;\n    this._originTimeoutId = setTimeout(() => this._origin = null, 0);\n  }\n\n  /**\n   * Checks whether the given focus event was caused by a touchstart event.\n   * @param event The focus event to check.\n   * @returns Whether the event was caused by a touch.\n   */\n  private _wasCausedByTouch(event: FocusEvent): boolean {\n    // Note(mmalerba): This implementation is not quite perfect, there is a small edge case.\n    // Consider the following dom structure:\n    //\n    // <div #parent tabindex=\"0\" cdkFocusClasses>\n    //   <div #child (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // If the user touches the #child element and the #parent is programmatically focused as a\n    // result, this code will still consider it to have been caused by the touch event and will\n    // apply the cdk-touch-focused class rather than the cdk-program-focused class. This is a\n    // relatively small edge-case that can be worked around by using\n    // focusVia(parentEl, 'program') to focus the parent element.\n    //\n    // If we decide that we absolutely must handle this case correctly, we can do so by listening\n    // for the first focus event after the touchstart, and then the first blur event after that\n    // focus event. When that blur event fires we know that whatever follows is not a result of the\n    // touchstart.\n    let focusTarget = event.target;\n    return this._lastTouchTarget instanceof Node && focusTarget instanceof Node &&\n        (focusTarget === this._lastTouchTarget || focusTarget.contains(this._lastTouchTarget));\n  }\n\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  private _onFocus(event: FocusEvent, element: HTMLElement) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || (!elementInfo.checkChildren && element !== event.target)) {\n      return;\n    }\n\n    // If we couldn't detect a cause for the focus event, it's due to one of three reasons:\n    // 1) The window has just regained focus, in which case we want to restore the focused state of\n    //    the element from before the window blurred.\n    // 2) It was caused by a touch event, in which case we mark the origin as 'touch'.\n    // 3) The element was programmatically focused, in which case we should mark the origin as\n    //    'program'.\n    if (!this._origin) {\n      if (this._windowFocused && this._lastFocusOrigin) {\n        this._origin = this._lastFocusOrigin;\n      } else if (this._wasCausedByTouch(event)) {\n        this._origin = 'touch';\n      } else {\n        this._origin = 'program';\n      }\n    }\n\n    this._setClasses(element, this._origin);\n    elementInfo.subject.next(this._origin);\n    this._lastFocusOrigin = this._origin;\n    this._origin = null;\n  }\n\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event: FocusEvent, element: HTMLElement) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n\n    if (!elementInfo || (elementInfo.checkChildren && event.relatedTarget instanceof Node &&\n        element.contains(event.relatedTarget))) {\n      return;\n    }\n\n    this._setClasses(element);\n    elementInfo.subject.next(null);\n  }\n\n  private _incrementMonitoredElementCount() {\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount == 1) {\n      this._registerGlobalListeners();\n    }\n  }\n\n  private _decrementMonitoredElementCount() {\n    // Unregister global listeners when last element is unmonitored.\n    if (!--this._monitoredElementCount) {\n      this._unregisterGlobalListeners();\n      this._unregisterGlobalListeners = () => {};\n    }\n  }\n\n}\n\n\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\n@Directive({\n  selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n})\nexport class CdkMonitorFocus implements OnDestroy {\n  private _monitorSubscription: Subscription;\n  @Output() cdkFocusChange = new EventEmitter<FocusOrigin>();\n\n  constructor(private _elementRef: ElementRef, private _focusMonitor: FocusMonitor) {\n    this._monitorSubscription = this._focusMonitor.monitor(\n        this._elementRef.nativeElement,\n        this._elementRef.nativeElement.hasAttribute('cdkMonitorSubtreeFocus'))\n        .subscribe(origin => this.cdkFocusChange.emit(origin));\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef.nativeElement);\n    this._monitorSubscription.unsubscribe();\n  }\n}\n\n/** @docs-private */\nexport function FOCUS_MONITOR_PROVIDER_FACTORY(\n    parentDispatcher: FocusMonitor, ngZone: NgZone, platform: Platform) {\n  return parentDispatcher || new FocusMonitor(ngZone, platform);\n}\n\n/** @docs-private */\nexport const FOCUS_MONITOR_PROVIDER = {\n  // If there is already a FocusMonitor available, use that. Otherwise, provide a new one.\n  provide: FocusMonitor,\n  deps: [[new Optional(), new SkipSelf(), FocusMonitor], NgZone, Platform],\n  useFactory: FOCUS_MONITOR_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Screenreaders will often fire fake mousedown events when a focusable element\n * is activated using the keyboard. We can typically distinguish between these faked\n * mousedown events and real mousedown events using the \"buttons\" property. While\n * real mousedowns will indicate the mouse button that was pressed (e.g. \"1\" for\n * the left mouse button), faked mousedowns will usually set the property value to 0.\n */\nexport function isFakeMousedownFromScreenReader(event: MouseEvent): boolean {\n  return event.buttons === 0;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  Input,\n  NgZone,\n  OnDestroy,\n  AfterContentInit,\n  Injectable,\n  Inject,\n} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {take} from 'rxjs/operators/take';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {DOCUMENT} from '@angular/common';\n\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause to two to misalign.\n */\nexport class FocusTrap {\n  private _startAnchor: HTMLElement | null;\n  private _endAnchor: HTMLElement | null;\n\n  /** Whether the focus trap is active. */\n  get enabled(): boolean { return this._enabled; }\n  set enabled(val: boolean) {\n    this._enabled = val;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._startAnchor.tabIndex = this._endAnchor.tabIndex = this._enabled ? 0 : -1;\n    }\n  }\n  private _enabled: boolean = true;\n\n  constructor(\n    private _element: HTMLElement,\n    private _checker: InteractivityChecker,\n    private _ngZone: NgZone,\n    private _document: Document,\n    deferAnchors = false) {\n\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    if (this._startAnchor && this._startAnchor.parentNode) {\n      this._startAnchor.parentNode.removeChild(this._startAnchor);\n    }\n\n    if (this._endAnchor && this._endAnchor.parentNode) {\n      this._endAnchor.parentNode.removeChild(this._endAnchor);\n    }\n\n    this._startAnchor = this._endAnchor = null;\n  }\n\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   */\n  attachAnchors(): void {\n    if (!this._startAnchor) {\n      this._startAnchor = this._createAnchor();\n    }\n\n    if (!this._endAnchor) {\n      this._endAnchor = this._createAnchor();\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      this._startAnchor!.addEventListener('focus', () => {\n        this.focusLastTabbableElement();\n      });\n\n      this._endAnchor!.addEventListener('focus', () => {\n        this.focusFirstTabbableElement();\n      });\n\n      if (this._element.parentNode) {\n        this._element.parentNode.insertBefore(this._startAnchor!, this._element);\n        this._element.parentNode.insertBefore(this._endAnchor!, this._element.nextSibling);\n      }\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then either focuses the first element that the\n   * user specified, or the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusInitialElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement()));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusFirstTabbableElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement()));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfuly.\n   */\n  focusLastTabbableElementWhenReady(): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement()));\n    });\n  }\n\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  private _getRegionBoundary(bound: 'start' | 'end'): HTMLElement | null {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    let markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` +\n                                                 `[cdkFocusRegion${bound}], ` +\n                                                 `[cdk-focus-${bound}]`) as NodeListOf<HTMLElement>;\n\n    for (let i = 0; i < markers.length; i++) {\n      if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}',` +\n                     ` use 'cdkFocusRegion${bound}' instead.`, markers[i]);\n      } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}',` +\n                     ` use 'cdkFocusRegion${bound}' instead.`, markers[i]);\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ?\n        markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusInitialElement(): boolean {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` +\n                                                          `[cdkFocusInitial]`) as HTMLElement;\n\n    if (this._element.hasAttribute(`cdk-focus-initial`)) {\n      console.warn(`Found use of deprecated attribute 'cdk-focus-initial',` +\n                    ` use 'cdkFocusInitial' instead.`, this._element);\n    }\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n      return true;\n    }\n\n    return this.focusFirstTabbableElement();\n  }\n\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusFirstTabbableElement(): boolean {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfuly.\n   */\n  focusLastTabbableElement(): boolean {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n\n    return !!redirectToElement;\n  }\n\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  private _getFirstTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in DOM order. Note that IE doesn't have `children` for SVG so we fall\n    // back to `childNodes` which includes text nodes, comments etc.\n    let children = root.children || root.childNodes;\n\n    for (let i = 0; i < children.length; i++) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n        this._getFirstTabbableElement(children[i] as HTMLElement) :\n        null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  private _getLastTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in reverse DOM order.\n    let children = root.children || root.childNodes;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n        this._getLastTabbableElement(children[i] as HTMLElement) :\n        null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Creates an anchor element. */\n  private _createAnchor(): HTMLElement {\n    const anchor = this._document.createElement('div');\n    anchor.tabIndex = this._enabled ? 0 : -1;\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    return anchor;\n  }\n\n  /** Executes a function when the zone is stable. */\n  private _executeOnStable(fn: () => any): void {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.asObservable().pipe(take(1)).subscribe(fn);\n    }\n  }\n}\n\n\n/** Factory that allows easy instantiation of focus traps. */\n@Injectable()\nexport class FocusTrapFactory {\n  private _document: Document;\n\n  constructor(\n      private _checker: InteractivityChecker,\n      private _ngZone: NgZone,\n      @Inject(DOCUMENT) _document: any) {\n\n    this._document = _document;\n  }\n\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element: HTMLElement, deferCaptureElements: boolean = false): FocusTrap {\n    return new FocusTrap(\n        element, this._checker, this._ngZone, this._document, deferCaptureElements);\n  }\n}\n\n\n/**\n * Directive for trapping focus within a region.\n * @docs-private\n * @deprecated\n * @deletion-target 6.0.0\n */\n@Directive({\n  selector: 'cdk-focus-trap',\n})\nexport class FocusTrapDeprecatedDirective implements OnDestroy, AfterContentInit {\n  focusTrap: FocusTrap;\n\n  /** Whether the focus trap is active. */\n  @Input()\n  get disabled(): boolean { return !this.focusTrap.enabled; }\n  set disabled(val: boolean) {\n    this.focusTrap.enabled = !coerceBooleanProperty(val);\n  }\n\n  constructor(private _elementRef: ElementRef, private _focusTrapFactory: FocusTrapFactory) {\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy();\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n  }\n}\n\n\n/** Directive for trapping focus within a region. */\n@Directive({\n  selector: '[cdkTrapFocus]',\n  exportAs: 'cdkTrapFocus',\n})\nexport class CdkTrapFocus implements OnDestroy, AfterContentInit {\n  private _document: Document;\n\n  /** Underlying FocusTrap instance. */\n  focusTrap: FocusTrap;\n\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  private _previouslyFocusedElement: HTMLElement | null = null;\n\n  /** Whether the focus trap is active. */\n  @Input('cdkTrapFocus')\n  get enabled(): boolean { return this.focusTrap.enabled; }\n  set enabled(value: boolean) { this.focusTrap.enabled = coerceBooleanProperty(value); }\n\n  /**\n   * Whether the directive should automatially move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  @Input('cdkTrapFocusAutoCapture')\n  get autoCapture(): boolean { return this._autoCapture; }\n  set autoCapture(value: boolean) { this._autoCapture = coerceBooleanProperty(value); }\n  private _autoCapture: boolean;\n\n  constructor(\n      private _elementRef: ElementRef,\n      private _focusTrapFactory: FocusTrapFactory,\n      @Inject(DOCUMENT) _document: any) {\n\n    this._document = _document;\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy();\n\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n\n    if (this.autoCapture) {\n      this._previouslyFocusedElement = this._document.activeElement as HTMLElement;\n      this.focusTrap.focusInitialElementWhenReady();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {QueryList} from '@angular/core';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {\n  UP_ARROW,\n  DOWN_ARROW,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  TAB,\n  A,\n  Z,\n  ZERO,\n  NINE,\n} from '@angular/cdk/keycodes';\nimport {debounceTime} from 'rxjs/operators/debounceTime';\nimport {filter} from 'rxjs/operators/filter';\nimport {map} from 'rxjs/operators/map';\nimport {tap} from 'rxjs/operators/tap';\n\n/** This interface is for items that can be passed to a ListKeyManager. */\nexport interface ListKeyManagerOption {\n  /** Whether the option is disabled. */\n  disabled?: boolean;\n\n  /** Gets the label for this option. */\n  getLabel?(): string;\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nexport class ListKeyManager<T extends ListKeyManagerOption> {\n  private _activeItemIndex = -1;\n  private _activeItem: T;\n  private _wrap = false;\n  private _letterKeyStream = new Subject<string>();\n  private _typeaheadSubscription = Subscription.EMPTY;\n  private _vertical = true;\n  private _horizontal: 'ltr' | 'rtl' | null;\n\n  // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n  private _pressedLetters: string[] = [];\n\n  constructor(private _items: QueryList<T>) {\n    _items.changes.subscribe((newItems: QueryList<T>) => {\n      if (this._activeItem) {\n        const itemArray = newItems.toArray();\n        const newIndex = itemArray.indexOf(this._activeItem);\n\n        if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n          this._activeItemIndex = newIndex;\n        }\n      }\n    });\n  }\n\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  tabOut: Subject<void> = new Subject<void>();\n\n  /** Stream that emits whenever the active item of the list manager changes. */\n  change = new Subject<number>();\n\n  /**\n   * Turns on wrapping mode, which ensures that the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   */\n  withWrap(): this {\n    this._wrap = true;\n    return this;\n  }\n\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled: boolean = true): this {\n    this._vertical = enabled;\n    return this;\n  }\n\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction: 'ltr' | 'rtl' | null): this {\n    this._horizontal = direction;\n    return this;\n  }\n\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval: number = 200): this {\n    if (this._items.length && this._items.some(item => typeof item.getLabel !== 'function')) {\n      throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n\n    this._typeaheadSubscription.unsubscribe();\n\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._typeaheadSubscription = this._letterKeyStream.pipe(\n      tap(keyCode => this._pressedLetters.push(keyCode)),\n      debounceTime(debounceInterval),\n      filter(() => this._pressedLetters.length > 0),\n      map(() => this._pressedLetters.join(''))\n    ).subscribe(inputString => {\n      const items = this._items.toArray();\n\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < items.length + 1; i++) {\n        const index = (this._activeItemIndex + i) % items.length;\n        const item = items[index];\n\n        if (!item.disabled && item.getLabel!().toUpperCase().trim().indexOf(inputString) === 0) {\n          this.setActiveItem(index);\n          break;\n        }\n      }\n\n      this._pressedLetters = [];\n    });\n\n    return this;\n  }\n\n  /**\n   * Sets the active item to the item at the index specified.\n   * @param index The index of the item to be set as active.\n   */\n  setActiveItem(index: number): void {\n    const previousIndex = this._activeItemIndex;\n\n    this._activeItemIndex = index;\n    this._activeItem = this._items.toArray()[index];\n\n    if (this._activeItemIndex !== previousIndex) {\n      this.change.next(index);\n    }\n  }\n\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n\n      case DOWN_ARROW:\n        if (this._vertical) {\n          this.setNextItemActive();\n          break;\n        }\n\n      case UP_ARROW:\n        if (this._vertical) {\n          this.setPreviousItemActive();\n          break;\n        }\n\n      case RIGHT_ARROW:\n        if (this._horizontal === 'ltr') {\n          this.setNextItemActive();\n          break;\n        } else if (this._horizontal === 'rtl') {\n          this.setPreviousItemActive();\n          break;\n        }\n\n      case LEFT_ARROW:\n        if (this._horizontal === 'ltr') {\n          this.setPreviousItemActive();\n          break;\n        } else if (this._horizontal === 'rtl') {\n          this.setNextItemActive();\n          break;\n        }\n\n      default:\n        // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n        // otherwise fall back to resolving alphanumeric characters via the keyCode.\n        if (event.key && event.key.length === 1) {\n          this._letterKeyStream.next(event.key.toLocaleUpperCase());\n        } else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n          this._letterKeyStream.next(String.fromCharCode(keyCode));\n        }\n\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n\n    this._pressedLetters = [];\n    event.preventDefault();\n  }\n\n  /** Index of the currently active item. */\n  get activeItemIndex(): number | null {\n    return this._activeItemIndex;\n  }\n\n  /** The active item. */\n  get activeItem(): T | null {\n    return this._activeItem;\n  }\n\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive(): void {\n    this._setActiveItemByIndex(0, 1);\n  }\n\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive(): void {\n    this._setActiveItemByIndex(this._items.length - 1, -1);\n  }\n\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive(): void {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive(): void {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive()\n                                            : this._setActiveItemByDelta(-1);\n  }\n\n  /**\n   * Allows setting of the activeItemIndex without any other effects.\n   * @param index The new activeItemIndex.\n   */\n  updateActiveItemIndex(index: number) {\n    this._activeItemIndex = index;\n  }\n\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  private _setActiveItemByDelta(delta: number, items = this._items.toArray()): void {\n    this._wrap ? this._setActiveInWrapMode(delta, items)\n               : this._setActiveInDefaultMode(delta, items);\n  }\n\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  private _setActiveInWrapMode(delta: number, items: T[]): void {\n    // when active item would leave menu, wrap to beginning or end\n    this._activeItemIndex =\n      (this._activeItemIndex + delta + items.length) % items.length;\n\n    // skip all disabled menu items recursively until an enabled one is reached\n    if (items[this._activeItemIndex].disabled) {\n      this._setActiveInWrapMode(delta, items);\n    } else {\n      this.setActiveItem(this._activeItemIndex);\n    }\n  }\n\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  private _setActiveInDefaultMode(delta: number, items: T[]): void {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta, items);\n  }\n\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  private _setActiveItemByIndex(index: number, fallbackDelta: number,\n                                  items = this._items.toArray()): void {\n    if (!items[index]) { return; }\n\n    while (items[index].disabled) {\n      index += fallbackDelta;\n      if (!items[index]) { return; }\n    }\n\n    this.setActiveItem(index);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\n\n/**\n * This is the interface for highlightable items (used by the ActiveDescendantKeyManager).\n * Each item must know how to style itself as active or inactive and whether or not it is\n * currently disabled.\n */\nexport interface Highlightable extends ListKeyManagerOption {\n  /** Applies the styles for an active item to this item. */\n  setActiveStyles(): void;\n\n  /** Applies the styles for an inactive item to this item. */\n  setInactiveStyles(): void;\n}\n\nexport class ActiveDescendantKeyManager<T> extends ListKeyManager<Highlightable & T> {\n\n  /**\n   * This method sets the active item to the item at the specified index.\n   * It also adds active styles to the newly active item and removes active\n   * styles from the previously active item.\n   */\n  setActiveItem(index: number): void {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\nimport {FocusOrigin} from '../focus-monitor/focus-monitor';\n\n/**\n * This is the interface for focusable items (used by the FocusKeyManager).\n * Each item must know how to focus itself, whether or not it is currently disabled\n * and be able to supply it's label.\n */\nexport interface FocusableOption extends ListKeyManagerOption {\n  /** Focuses the `FocusableOption`. */\n  focus(origin?: FocusOrigin): void;\n}\n\nexport class FocusKeyManager<T> extends ListKeyManager<FocusableOption & T> {\n  private _origin: FocusOrigin = 'program';\n\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin: FocusOrigin): this {\n    this._origin = origin;\n    return this;\n  }\n\n  /**\n   * This method sets the active item to the item at the specified index.\n   * It also adds focuses the newly active item.\n   */\n  setActiveItem(index: number): void {\n    super.setActiveItem(index);\n\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PlatformModule} from '@angular/cdk/platform';\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {ARIA_DESCRIBER_PROVIDER, AriaDescriber} from './aria-describer/aria-describer';\nimport {CdkMonitorFocus, FOCUS_MONITOR_PROVIDER} from './focus-monitor/focus-monitor';\nimport {\n  CdkTrapFocus,\n  FocusTrapDeprecatedDirective,\n  FocusTrapFactory,\n} from './focus-trap/focus-trap';\nimport {Interact<PERSON><PERSON>he<PERSON>} from './interactivity-checker/interactivity-checker';\nimport {LIVE_ANNOUNCER_PROVIDER} from './live-announcer/live-announcer';\n\n@NgModule({\n  imports: [CommonModule, PlatformModule],\n  declarations: [CdkTrapFocus, FocusTrapDeprecatedDirective, CdkMonitorFocus],\n  exports: [CdkTrapFocus, FocusTrapDeprecatedDirective, CdkMonitorFocus],\n  providers: [\n    InteractivityChecker,\n    FocusTrapFactory,\n    AriaDescriber,\n    LIVE_ANNOUNCER_PROVIDER,\n    ARIA_DESCRIBER_PROVIDER,\n    FOCUS_MONITOR_PROVIDER,\n  ]\n})\nexport class A11yModule {}\n"], "names": ["__extends", "d", "b", "__", "this", "constructor", "extendStatics", "prototype", "Object", "create", "getFrameElement", "window", "e", "hasGeometry", "element", "offsetWidth", "offsetHeight", "getClientRects", "length", "isNativeFormElement", "nodeName", "toLowerCase", "isHiddenInput", "isInputElement", "type", "isAnchorWithHref", "isAnchorElement", "hasAttribute", "hasValidTabIndex", "undefined", "tabIndex", "getAttribute", "isNaN", "parseInt", "getTabIndexValue", "isPotentiallyTabbableIOS", "inputType", "isPotentiallyFocusable", "getWindow", "node", "ownerDocument", "defaultView", "addAriaReferencedId", "el", "attr", "id", "ids", "getAriaReferenceIds", "some", "existingId", "trim", "push", "setAttribute", "join", "ID_DELIMINATOR", "removeAriaReferencedId", "filteredIds", "filter", "val", "match", "ARIA_DESCRIBER_PROVIDER_FACTORY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_document", "AriaDescriber", "LIVE_ANNOUNCER_PROVIDER_FACTORY", "liveElement", "LiveAnnouncer", "FOCUS_MONITOR_PROVIDER_FACTORY", "ngZone", "platform", "FocusMonitor", "isFakeMousedownFromScreenReader", "event", "buttons", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "InteractivityChecker", "_platform", "isDisabled", "isVisible", "getComputedStyle", "visibility", "isTabbable", "<PERSON><PERSON><PERSON><PERSON>", "frameElement", "frameType", "BLINK", "WEBKIT", "tabIndexValue", "TRIDENT", "FIREFOX", "IOS", "isFocusable", "Injectable", "Platform", "FocusTrap", "_element", "_checker", "_ngZone", "deferAnchors", "_enabled", "attachAnchors", "defineProperty", "_startAnchor", "_endAnchor", "destroy", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_this", "_createAnchor", "runOutsideAngular", "addEventListener", "focusLastTabbableElement", "focusFirstTabbableElement", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "querySelectorAll", "i", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus", "root", "children", "childNodes", "tabbable<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "anchor", "createElement", "classList", "add", "fn", "isStable", "onStable", "asObservable", "pipe", "take", "subscribe", "FocusTrapFactory", "deferCaptureElements", "NgZone", "decorators", "Inject", "args", "DOCUMENT", "FocusTrapDeprecatedDirective", "_elementRef", "_focusTrapFactory", "focusTrap", "nativeElement", "enabled", "coerceBooleanProperty", "ngOnDestroy", "ngAfterContentInit", "Directive", "selector", "ElementRef", "disabled", "Input", "CdkTrapFocus", "_previouslyFocusedElement", "value", "_autoCapture", "autoCapture", "exportAs", "nextId", "messageRegistry", "Map", "messagesContainer", "describe", "hostElement", "message", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_removeMessageReference", "registeredMessage", "get", "referenceCount", "_deleteMessageElement", "_deleteMessagesContainer", "describedE<PERSON>s", "_removeCdkDescribedByReferenceIds", "removeAttribute", "clear", "messageElement", "CDK_DESCRIBEDBY_ID_PREFIX", "append<PERSON><PERSON><PERSON>", "createTextNode", "_createMessagesContainer", "set", "delete", "style", "display", "body", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "ARIA_DESCRIBER_PROVIDER", "provide", "deps", "Optional", "SkipSelf", "useFactory", "ListKeyManager", "_items", "_activeItemIndex", "_wrap", "_letterKeyStream", "Subject", "_typeaheadSubscription", "Subscription", "EMPTY", "_vertical", "_pressedLetters", "tabOut", "change", "changes", "newItems", "_activeItem", "itemArray", "toArray", "newIndex", "withWrap", "withVerticalOrientation", "withHorizontalOrientation", "direction", "_horizontal", "withTypeAhead", "debounceInterval", "item", "get<PERSON><PERSON><PERSON>", "Error", "unsubscribe", "tap", "keyCode", "debounceTime", "map", "inputString", "items", "index", "toUpperCase", "setActiveItem", "previousIndex", "next", "onKeydown", "TAB", "DOWN_ARROW", "setNextItemActive", "UP_ARROW", "setPreviousItemActive", "RIGHT_ARROW", "LEFT_ARROW", "key", "toLocaleUpperCase", "A", "Z", "ZERO", "NINE", "String", "fromCharCode", "preventDefault", "setFirstItemActive", "_setActiveItemByIndex", "setLastItemActive", "_setActiveItemByDelta", "updateActiveItemIndex", "delta", "_setActiveInWrapMode", "_setActiveInDefaultMode", "fallback<PERSON><PERSON><PERSON>", "ActiveDescendantKeyManager", "_super", "tslib_1.__extends", "activeItem", "setInactiveStyles", "call", "setActiveStyles", "FocusKeyManager", "_origin", "setFocusOrigin", "origin", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "InjectionToken", "elementToken", "_liveElement", "_createLiveElement", "announce", "politeness", "textContent", "setTimeout", "liveEl", "LIVE_ANNOUNCER_PROVIDER", "_windowFocused", "_elementInfo", "_unregisterGlobalListeners", "_monitoredElementCount", "monitor", "renderer", "check<PERSON><PERSON><PERSON><PERSON>", "Renderer2", "observableOf", "cachedInfo", "subject", "info", "unlisten", "_incrementMonitoredElementCount", "focusListener", "_onFocus", "blurListener", "_onBlur", "removeEventListener", "stopMonitoring", "elementInfo", "complete", "_setClasses", "_decrementMonitoredElementCount", "focusVia", "_setOriginForCurrentEventQueue", "for<PERSON>ach", "_info", "_registerGlobalListeners", "documentKeydownListener", "_lastTouch<PERSON>arget", "documentMousedownListener", "documentTouchstartListener", "_touchTimeoutId", "clearTimeout", "target", "windowFocusListener", "_windowFocusTimeoutId", "document", "supportsPassiveEventListeners", "passive", "capture", "_originTimeoutId", "_toggleClass", "className", "shouldSet", "remove", "_wasCausedByTouch", "focusTarget", "Node", "contains", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relatedTarget", "CdkMonitorFocus", "_focusMonitor", "cdkFocusChange", "EventEmitter", "_monitorSubscription", "emit", "Output", "FOCUS_MONITOR_PROVIDER", "A11yModule", "NgModule", "imports", "CommonModule", "PlatformModule", "declarations", "exports", "providers"], "mappings": ";;;;;;;kmCAoBA,SAAgBA,GAAUC,EAAGC,GAEzB,QAASC,KAAOC,KAAKC,YAAcJ,EADnCK,EAAcL,EAAGC,GAEjBD,EAAEM,UAAkB,OAANL,EAAaM,OAAOC,OAAOP,IAAMC,EAAGI,UAAYL,EAAEK,UAAW,GAAIJ,IC8HnF,QAAAO,GAAyBC,GACvB,IACE,MAAOA,GAAkC,aACzC,MAAOC,GACP,MAAO,OAKX,QAAAC,GAAqBC,GAGnB,SAAUA,EAAQC,aAAeD,EAAQE,cACF,kBAA3BF,GAAQG,gBAAiCH,EAAQG,iBAAiBC,QAIhF,QAAAC,GAA6BL,GAC3B,GAAIM,GAAWN,EAAQM,SAASC,aAChC,OAAoB,UAAbD,GACU,WAAbA,GACa,WAAbA,GACa,aAAbA,EAIN,QAAAE,GAAuBR,GACrB,MAAOS,GAAeT,IAA4B,UAAhBA,EAAQU,KAI5C,QAAAC,GAA0BX,GACxB,MAAOY,GAAgBZ,IAAYA,EAAQa,aAAa,QAI1D,QAAAJ,GAAwBT,GACtB,MAAyC,SAAlCA,EAAQM,SAASC,cAI1B,QAAAK,GAAyBZ,GACvB,MAAyC,KAAlCA,EAAQM,SAASC,cAI1B,QAAAO,GAA0Bd,GACxB,IAAKA,EAAQa,aAAa,iBAAoCE,KAArBf,EAAQgB,SAC/C,OAAO,CAGT,IAAIA,GAAWhB,EAAQiB,aAAa,WAGpC,OAAgB,UAAZD,MAIMA,GAAaE,MAAMC,SAASH,EAAU,MAOlD,QAAAI,GAA0BpB,GACxB,IAAKc,EAAiBd,GACpB,MAAO,KAIT,IAAMgB,GAAWG,SAASnB,EAAQiB,aAAa,aAAe,GAAI,GAElE,OAAOC,OAAMF,IAAa,EAAIA,EAIhC,QAAAK,GAAkCrB,GAChC,GAAIM,GAAWN,EAAQM,SAASC,cAC5Be,EAAyB,UAAbhB,GAAwB,EAA8BI,IAEtE,OAAqB,SAAdY,GACc,aAAdA,GACa,WAAbhB,GACa,aAAbA,EAOT,QAAAiB,GAAgCvB,GAE9B,OAAIQ,EAAcR,KAIXK,EAAoBL,IACvBW,EAAiBX,IACjBA,EAAQa,aAAa,oBACrBC,EAAiBd,IAIvB,QAAAwB,GAAmBC,GACjB,MAAOA,GAAKC,cAAcC,aAAe9B,OC/O3C,QAAA+B,GAAoCC,EAAaC,EAAcC,GAC7D,GAAMC,GAAMC,EAAoBJ,EAAIC,EAChCE,GAAIE,KAAK,SAAAC,GAAc,MAAAA,GAAWC,QAAUL,EAAGK,WACnDJ,EAAIK,KAAKN,EAAGK,QAEZP,EAAGS,aAAaR,EAAME,EAAIO,KAAKC,KAOjC,QAAAC,GAAuCZ,EAAaC,EAAcC,GAChE,GAAMC,GAAMC,EAAoBJ,EAAIC,GAC9BY,EAAcV,EAAIW,OAAO,SAAAC,GAAO,MAAAA,IAAOb,EAAGK,QAEhDP,GAAGS,aAAaR,EAAMY,EAAYH,KAAKC,IAOzC,QAAAP,GAAoCJ,EAAaC,GAE/C,OAAQD,EAAGZ,aAAaa,IAAS,IAAIe,MAAM,YCiK7C,QAAAC,GAAgDC,EAAiCC,GAC/E,MAAOD,IAAoB,GAAIE,GAAcD,GCtH/C,QAAAE,GACIH,EAAiCI,EAAkBH,GACrD,MAAOD,IAAoB,GAAIK,GAAcD,EAAaH,GC2T5D,QAAAK,GACIN,EAAgCO,EAAgBC,GAClD,MAAOR,IAAoB,GAAIS,GAAaF,EAAQC,GCpYtD,QAAAE,GAAgDC,GAC9C,MAAyB,KAAlBA,EAAMC,QNAf,GAAInE,GAAgBE,OAAOkE,iBACpBC,uBAA2BC,QAAS,SAAU3E,EAAGC,GAAKD,EAAE0E,UAAYzE,IACvE,SAAUD,EAAGC,GAAK,IAAK,GAAI2E,KAAK3E,GAAOA,EAAE4E,eAAeD,KAAI5E,EAAE4E,GAAK3E,EAAE2E,kBCKvE,QAAFE,GAAsBC,GAAA5E,KAAtB4E,UAAsBA,EAvBtB,MA+BED,GAAFxE,UAAA0E,WAAE,SAAWnE,GAGT,MAAOA,GAAQa,aAAa,aAW9BoD,EAAFxE,UAAA2E,UAAE,SAAUpE,GACR,MAAOD,GAAYC,IAAqD,YAAzCqE,iBAAiBrE,GAASsE,YAU3DL,EAAFxE,UAAA8E,WAAE,SAAWvE,GAET,IAAKV,KAAK4E,UAAUM,UAClB,OAAO,CAGT,IAAMC,GAAe7E,EAAgB4B,EAAUxB,GAE/C,IAAIyE,EAAc,CAChB,GAAMC,GAAYD,GAAgBA,EAAanE,SAASC,aAGxD,KAAwC,IAApCa,EAAiBqD,GACnB,OAAO,CAIT,KAAKnF,KAAK4E,UAAUS,OAASrF,KAAK4E,UAAUU,SAAyB,WAAdF,EACrD,OAAO,CAIT,KAAKpF,KAAK4E,UAAUS,OAASrF,KAAK4E,UAAUU,UAAYtF,KAAK8E,UAAUK,GACrE,OAAO,EAKX,GAAInE,GAAWN,EAAQM,SAASC,cAC5BsE,EAAgBzD,EAAiBpB,EAErC,IAAIA,EAAQa,aAAa,mBACvB,OAA0B,IAAnBgE,CAGT,IAAiB,WAAbvE,EAGF,OAAO,CAGT,IAAiB,UAAbA,EAAsB,CACxB,IAAKN,EAAQa,aAAa,YAExB,OAAO,CACF,IAAIvB,KAAK4E,UAAUS,MAExB,OAAO,EAIX,GAAiB,UAAbrE,EAAsB,CACxB,IAAKN,EAAQa,aAAa,aAAevB,KAAK4E,UAAUY,QAEtD,OAAO,CACF,IAAIxF,KAAK4E,UAAUS,OAASrF,KAAK4E,UAAUa,QAEhD,OAAO,EAIX,OAAiB,WAAbzE,IAA0BhB,KAAK4E,UAAUS,QAASrF,KAAK4E,UAAUU,YAMjEtF,KAAK4E,UAAUU,QAAUtF,KAAK4E,UAAUc,MAAQ3D,EAAyBrB,KAItEA,EAAQgB,UAAY,IAS7BiD,EAAFxE,UAAAwF,YAAE,SAAYjF,GAGV,MAAOuB,GAAuBvB,KAAaV,KAAK6E,WAAWnE,IAAYV,KAAK8E,UAAUpE,mBAvH1FU,KAACwE,EAAAA,iDAXDxE,KAAQyE,EAAAA,YATRlB,KM+BAmB,EAAA,WAeE,QAAFA,GACYC,EACAC,EACAC,EACAvC,EACRwC,OAAJ,KAAAA,IAAIA,GAAJ,GAJYlG,KAAZ+F,SAAYA,EACA/F,KAAZgG,SAAYA,EACAhG,KAAZiG,QAAYA,EACAjG,KAAZ0D,UAAYA,EANZ1D,KAAAmG,UAA8B,EASrBD,GACHlG,KAAKoG,gBAtDX,MAoCEhG,QAAFiG,eAAMP,EAAN3F,UAAA,eAAE,WAAyB,MAAOH,MAAKmG,cACrC,SAAY7C,GACVtD,KAAKmG,SAAW7C,EAEZtD,KAAKsG,cAAgBtG,KAAKuG,aAC5BvG,KAAKsG,aAAa5E,SAAW1B,KAAKuG,WAAW7E,SAAW1B,KAAKmG,SAAW,GAAK,oCAkBjFL,EAAF3F,UAAAqG,QAAE,WACMxG,KAAKsG,cAAgBtG,KAAKsG,aAAaG,YACzCzG,KAAKsG,aAAaG,WAAWC,YAAY1G,KAAKsG,cAG5CtG,KAAKuG,YAAcvG,KAAKuG,WAAWE,YACrCzG,KAAKuG,WAAWE,WAAWC,YAAY1G,KAAKuG,YAG9CvG,KAAKsG,aAAetG,KAAKuG,WAAa,MAOxCT,EAAF3F,UAAAiG,cAAE,WAAA,GAAFO,GAAA3G,IACSA,MAAKsG,eACRtG,KAAKsG,aAAetG,KAAK4G,iBAGtB5G,KAAKuG,aACRvG,KAAKuG,WAAavG,KAAK4G,iBAGzB5G,KAAKiG,QAAQY,kBAAkB,WAC7BF,EAAiB,aAAEG,iBAAiB,QAAS,WAC3CH,EAAKI,6BAGPJ,EAAe,WAAEG,iBAAiB,QAAS,WACzCH,EAAKK,8BAGHL,EAAKZ,SAASU,aAChBE,EAAKZ,SAASU,WAAWQ,aAAaN,EAAiB,aAAGA,EAAKZ,UAC/DY,EAAKZ,SAASU,WAAWQ,aAAaN,EAAe,WAAGA,EAAKZ,SAASmB,iBAW5EpB,EAAF3F,UAAAgH,6BAAE,WAAA,GAAFR,GAAA3G,IACI,OAAO,IAAIoH,SAAiB,SAAAC,GAC1BV,EAAKW,iBAAiB,WAAM,MAAAD,GAAQV,EAAKY,4BAU7CzB,EAAF3F,UAAAqH,mCAAE,WAAA,GAAFb,GAAA3G,IACI,OAAO,IAAIoH,SAAiB,SAAAC,GAC1BV,EAAKW,iBAAiB,WAAM,MAAAD,GAAQV,EAAKK,kCAU7ClB,EAAF3F,UAAAsH,kCAAE,WAAA,GAAFd,GAAA3G,IACI,OAAO,IAAIoH,SAAiB,SAAAC,GAC1BV,EAAKW,iBAAiB,WAAM,MAAAD,GAAQV,EAAKI,iCASrCjB,EAAV3F,UAAAuH,mBAAA,SAA6BC,GAMzB,IAAK,GAJDC,GAAU5H,KAAK+F,SAAS8B,iBAAiB,qBAAqBF,EAAtE,qBACmEA,EAAnE,iBAC+DA,EAA/D,KAEaG,EAAI,EAAGA,EAAIF,EAAQ9G,OAAQgH,IAC9BF,EAAQE,GAAGvG,aAAa,aAAaoG,GACvCI,QAAQC,KAAK,gDAAgDL,EAArE,yBAC4CA,EAA5C,aAA+DC,EAAQE,IACtDF,EAAQE,GAAGvG,aAAa,oBAAoBoG,IACrDI,QAAQC,KAAK,uDAAuDL,EAA5E,yBAC4CA,EAA5C,aAA+DC,EAAQE,GAInE,OAAa,SAATH,EACKC,EAAQ9G,OAAS8G,EAAQ,GAAK5H,KAAKiI,yBAAyBjI,KAAK+F,UAEnE6B,EAAQ9G,OACX8G,EAAQA,EAAQ9G,OAAS,GAAKd,KAAKkI,wBAAwBlI,KAAK+F,WAOtED,EAAF3F,UAAAoH,oBAAE,WAEE,GAAMY,GAAoBnI,KAAK+F,SAASqC,cAAc,yCAQtD,OALIpI,MAAK+F,SAASxE,aAAa,sBAC7BwG,QAAQC,KAAK,wFACoChI,KAAK+F,UAGpDoC,GACFA,EAAkBE,SACX,GAGFrI,KAAKgH,6BAOdlB,EAAF3F,UAAA6G,0BAAE,WACE,GAAMmB,GAAoBnI,KAAK0H,mBAAmB,QAMlD,OAJIS,IACFA,EAAkBE,UAGXF,GAOXrC,EAAF3F,UAAA4G,yBAAE,WACE,GAAMoB,GAAoBnI,KAAK0H,mBAAmB,MAMlD,OAJIS,IACFA,EAAkBE,UAGXF,GAIHrC,EAAV3F,UAAA8H,yBAAA,SAAmCK,GAC/B,GAAItI,KAAKgG,SAASL,YAAY2C,IAAStI,KAAKgG,SAASf,WAAWqD,GAC9D,MAAOA,EAOT,KAAK,GAFDC,GAAWD,EAAKC,UAAYD,EAAKE,WAE5BV,EAAI,EAAGA,EAAIS,EAASzH,OAAQgH,IAAK,CACxC,GAAIW,GAAgBF,EAAST,GAAGY,WAAa1I,KAAK0D,UAAUiF,aAC1D3I,KAAKiI,yBAAyBM,EAAST,IACvC,IAEF,IAAIW,EACF,MAAOA,GAIX,MAAO,OAID3C,EAAV3F,UAAA+H,wBAAA,SAAkCI,GAC9B,GAAItI,KAAKgG,SAASL,YAAY2C,IAAStI,KAAKgG,SAASf,WAAWqD,GAC9D,MAAOA,EAMT,KAAK,GAFDC,GAAWD,EAAKC,UAAYD,EAAKE,WAE5BV,EAAIS,EAASzH,OAAS,EAAGgH,GAAK,EAAGA,IAAK,CAC7C,GAAIW,GAAgBF,EAAST,GAAGY,WAAa1I,KAAK0D,UAAUiF,aAC1D3I,KAAKkI,wBAAwBK,EAAST,IACtC,IAEF,IAAIW,EACF,MAAOA,GAIX,MAAO,OAID3C,EAAV3F,UAAAyG,yBACI,GAAMgC,GAAS5I,KAAK0D,UAAUmF,cAAc,MAI5C,OAHAD,GAAOlH,SAAW1B,KAAKmG,SAAW,GAAK,EACvCyC,EAAOE,UAAUC,IAAI,uBACrBH,EAAOE,UAAUC,IAAI,yBACdH,GAID9C,EAAV3F,UAAAmH,iBAAA,SAA2B0B,GACnBhJ,KAAKiG,QAAQgD,SACfD,IAEAhJ,KAAKiG,QAAQiD,SAASC,eAAeC,KAAKC,EAAAA,KAAK,IAAIC,UAAUN,IAjRnElD,kBA4RE,QAAFyD,GACcvD,EACAC,EACUvC,GAFV1D,KAAdgG,SAAcA,EACAhG,KAAdiG,QAAcA,EAGVjG,KAAK0D,UAAYA,EAjSrB,MA2SE6F,GAAFpJ,UAAAE,OAAE,SAAOK,EAAsB8I,GAC3B,WADJ,KAAAA,IAA+BA,GAA/B,GACW,GAAI1D,GACPpF,EAASV,KAAKgG,SAAUhG,KAAKiG,QAASjG,KAAK0D,UAAW8F,mBArB9DpI,KAACwE,EAAAA,iDApQDxE,KAAQuD,IARRvD,KAAEqI,EAAAA,SAmRFrI,SAAAK,GAAAiI,aAAAtI,KAAOuI,EAAAA,OAAPC,MAAcC,EAAAA,eA/RdN,kBAqUE,QAAFO,GAAsBC,EAAiCC,GAAjChK,KAAtB+J,YAAsBA,EAAiC/J,KAAvDgK,kBAAuDA,EACnDhK,KAAKiK,UAAYjK,KAAKgK,kBAAkB3J,OAAOL,KAAK+J,YAAYG,eAAe,GAtUnF,MAgUA9J,QAAAiG,eAAMyD,EAAN3J,UAAA,gBAAA,WAA4B,OAAQH,KAAKiK,UAAUE,aACjD,SAAa7G,GACXtD,KAAKiK,UAAUE,SAAWC,EAAAA,sBAAsB9G,oCAOlDwG,EAAF3J,UAAAkK,YAAE,WACErK,KAAKiK,UAAUzD,WAGjBsD,EAAF3J,UAAAmK,mBAAE,WACEtK,KAAKiK,UAAU7D,gCAtBnBhF,KAACmJ,EAAAA,UAADX,OACEY,SAAU,yDA/SZpJ,KAAEqJ,EAAAA,aA+QFrJ,KAAamI,uBAsCbmB,WAAAtJ,KAAGuJ,EAAAA,SA/THb,kBA+WE,QAAFc,GACcb,EACAC,EACUtG,GAFV1D,KAAd+J,YAAcA,EACA/J,KAAdgK,kBAAcA,EAlBdhK,KAAA6K,0BAA0D,KAqBtD7K,KAAK0D,UAAYA,EACjB1D,KAAKiK,UAAYjK,KAAKgK,kBAAkB3J,OAAOL,KAAK+J,YAAYG,eAAe,GArXnF,MAmWA9J,QAAAiG,eAAMuE,EAANzK,UAAA,eAAA,WAA2B,MAAOH,MAAKiK,UAAUE,aAC/C,SAAYW,GAAkB9K,KAAKiK,UAAUE,QAAUC,EAAAA,sBAAsBU,oCAO/E1K,OAAAiG,eAAMuE,EAANzK,UAAA,mBAAA,WAA+B,MAAOH,MAAK+K,kBACzC,SAAgBD,GAAkB9K,KAAK+K,aAAeX,EAAAA,sBAAsBU,oCAY5EF,EAAFzK,UAAAkK,YAAE,WACErK,KAAKiK,UAAUzD,UAIXxG,KAAK6K,4BACP7K,KAAK6K,0BAA0BxC,QAC/BrI,KAAK6K,0BAA4B,OAIrCD,EAAFzK,UAAAmK,mBAAE,WACEtK,KAAKiK,UAAU7D,gBAEXpG,KAAKgL,cACPhL,KAAK6K,0BAA4B7K,KAAK0D,UAAsC,cAC5E1D,KAAKiK,UAAU9C,gDApDrB/F,KAACmJ,EAAAA,UAADX,OACEY,SAAU,iBACVS,SAAU,uDA5UZ7J,KAAEqJ,EAAAA,aA+QFrJ,KAAamI,IAyFbnI,SAAAK,GAAAiI,aAAAtI,KAAOuI,EAAAA,OAAPC,MAAcC,EAAAA,iCAhBdM,UAAA/I,KAAGuJ,EAAAA,MAAHf,MAAS,kBAQToB,cAAA5J,KAAGuJ,EAAAA,MAAHf,MAAS,8BA1WTgB,KLSM1H,EAAiB,ICyBnBgI,EAAS,EAGPC,EAAkB,GAAIC,KAGxBC,EAAwC,kBAY1C,QAAF1H,GAAgCD,GAC5B1D,KAAK0D,UAAYA,EArDrB,MA6DEC,GAAFxD,UAAAmL,SAAE,SAASC,EAAsBC,GACzBD,EAAY7C,WAAa1I,KAAK0D,UAAUiF,cAAiB6C,EAAQ1I,SAIhEqI,EAAgBM,IAAID,IACvBxL,KAAK0L,sBAAsBF,GAGxBxL,KAAK2L,6BAA6BJ,EAAaC,IAClDxL,KAAK4L,qBAAqBL,EAAaC,KAK3C7H,EAAFxD,UAAA0L,kBAAE,SAAkBN,EAAsBC,GACtC,GAAID,EAAY7C,WAAa1I,KAAK0D,UAAUiF,cAAiB6C,EAAQ1I,OAArE,CAII9C,KAAK2L,6BAA6BJ,EAAaC,IACjDxL,KAAK8L,wBAAwBP,EAAaC,EAG5C,IAAMO,GAAoBZ,EAAgBa,IAAIR,EAC1CO,IAA0D,IAArCA,EAAkBE,gBACzCjM,KAAKkM,sBAAsBV,GAGzBH,GAA6D,IAAxCA,EAAkB7C,WAAW1H,QACpDd,KAAKmM,6BAKTxI,EAAFxD,UAAAkK,YAAE,WAIE,IAAK,GAHC+B,GACFpM,KAAK0D,UAAUmE,iBAAiB,0BAE3BC,EAAI,EAAGA,EAAIsE,EAAkBtL,OAAQgH,IAC5C9H,KAAKqM,kCAAkCD,EAAkBtE,IACzDsE,EAAkBtE,GAAGwE,gBAvEmB,uBA0EtCjB,IACFrL,KAAKmM,2BAGPhB,EAAgBoB,SAOV5I,EAAVxD,UAAAuL,sBAAA,SAAgCF,GAC5B,GAAMgB,GAAiBxM,KAAK0D,UAAUmF,cAAc,MACpD2D,GAAexJ,aAAa,KAASyJ,2BAA6BvB,KAClEsB,EAAeE,YAAY1M,KAAK0D,UAAUiJ,eAAenB,IAEpDH,GAAqBrL,KAAK4M,2BACnC,EAAuBF,YAAYF,GAE/BrB,EAAgB0B,IAAIrB,GAAUgB,eAAlCA,EAAkDP,eAAgB,KAIxDtI,EAAVxD,UAAA+L,sBAAA,SAAgCV,GAC5B,GAAMO,GAAoBZ,EAAgBa,IAAIR,GACxCgB,EAAiBT,GAAqBA,EAAkBS,cAC1DnB,IAAqBmB,GACvBnB,EAAkB3E,YAAY8F,GAEhCrB,EAAgB2B,OAAOtB,IAIjB7H,EAAVxD,UAAAyM,oCACIvB,EAAoBrL,KAAK0D,UAAUmF,cAAc,OACjDwC,EAAkBrI,aAAa,KAnHE,qCAoHjCqI,EAAkBrI,aAAa,cAAe,QAC9CqI,EAAkB0B,MAAMC,QAAU,OAClChN,KAAK0D,UAAUuJ,KAAKP,YAAYrB,IAI1B1H,EAAVxD,UAAAgM,oCACQd,GAAqBA,EAAkB5E,aACzC4E,EAAkB5E,WAAWC,YAAY2E,GACzCA,EAAoB,OAKhB1H,EAAVxD,UAAAkM,kCAAA,SAA4C3L,GAExC,GAAMwM,GAAuBvK,EAAoBjC,EAAS,oBACrD2C,OAAO,SAAAZ,GAAM,MAAyC,IAAzCA,EAAG0K,QAlIgB,4BAmIrCzM,GAAQsC,aAAa,mBAAoBkK,EAAqBjK,KAAK,OAO7DU,EAAVxD,UAAAyL,qBAAA,SAA+BlL,EAAkB8K,GAC7C,GAAMO,GAAoBZ,EAAgBa,IAAIR,EAI9ClJ,GAAoB5B,EAAS,mBAAoBqL,EAAkBS,eAAe/J,IAClF/B,EAAQsC,aA7IkC,uBA6IW,IAErD+I,EAAkBE,kBAOZtI,EAAVxD,UAAA2L,wBAAA,SAAkCpL,EAAkB8K,GAChD,GAAMO,GAAoBZ,EAAgBa,IAAIR,EAC9CO,GAAkBE,iBAElB9I,EAAuBzC,EAAS,mBAAoBqL,EAAkBS,eAAe/J,IACrF/B,EAAQ4L,gBA3JkC,yBA+JpC3I,EAAVxD,UAAAwL,6BAAA,SAAuCjL,EAAkB8K,GACrD,GAAM4B,GAAezK,EAAoBjC,EAAS,oBAC5CqL,EAAoBZ,EAAgBa,IAAIR,GACxC6B,EAAYtB,GAAqBA,EAAkBS,eAAe/J,EAExE,SAAS4K,IAAiD,GAApCD,EAAaD,QAAQE,mBAnJ/CjM,KAACwE,EAAAA,iDAIDxE,SAAAK,GAAAiI,aAAAtI,KAAeuI,EAAAA,OAAfC,MAAsBC,EAAAA,eApDtBlG,KA8Ma2J,GAEXC,QAAS5J,EACT6J,OACG,GAAIC,GAAAA,SAAY,GAAIC,GAAAA,SAAY/J,GACjCkG,EAA+B,UAEjC8D,WAAYnK,GK7KdoK,EAAA,WAYE,QAAFA,GAAsBC,GAApB,GAAFlH,GAAA3G,IAAsBA,MAAtB6N,OAAsBA,EAXtB7N,KAAA8N,kBAA8B,EAE9B9N,KAAA+N,OAAkB,EAClB/N,KAAAgO,iBAA6B,GAAIC,GAAAA,QACjCjO,KAAAkO,uBAAmCC,EAAAA,aAAaC,MAChDpO,KAAAqO,WAAsB,EAItBrO,KAAAsO,mBAmBAtO,KAAAuO,OAA0B,GAAIN,GAAAA,QAG9BjO,KAAAwO,OAAW,GAAIP,GAAAA,QAnBXJ,EAAOY,QAAQnF,UAAU,SAACoF,GACxB,GAAI/H,EAAKgI,YAAa,CACpB,GAAMC,GAAYF,EAASG,UACrBC,EAAWF,EAAUzB,QAAQxG,EAAKgI,YAEpCG,IAAY,GAAKA,IAAanI,EAAKmH,mBACrCnH,EAAKmH,iBAAmBgB,MA3DlC,MA8EElB,GAAFzN,UAAA4O,SAAE,WAEE,MADA/O,MAAK+N,OAAQ,EACN/N,MAOT4N,EAAFzN,UAAA6O,wBAAE,SAAwB7E,GAEtB,WAFJ,KAAAA,IAA0BA,GAA1B,GACInK,KAAKqO,UAAYlE,EACVnK,MAQT4N,EAAFzN,UAAA8O,0BAAE,SAA0BC,GAExB,MADAlP,MAAKmP,YAAcD,EACZlP,MAOT4N,EAAFzN,UAAAiP,cAAE,SAAcC,GAAd,GAAF1I,GAAA3G,IACI,QADJ,KAAAqP,IAAgBA,EAAhB,KACQrP,KAAK6N,OAAO/M,QAAUd,KAAK6N,OAAOjL,KAAK,SAAA0M,GAAQ,MAAyB,kBAAlBA,GAAKC,WAC7D,KAAMC,OAAM,+EA+Bd,OA5BAxP,MAAKkO,uBAAuBuB,cAK5BzP,KAAKkO,uBAAyBlO,KAAKgO,iBAAiB5E,KAClDsG,EAAAA,IAAI,SAAAC,GAAW,MAAAhJ,GAAK2H,gBAAgBvL,KAAK4M,KACzCC,EAAAA,aAAaP,GACbhM,EAAAA,OAAO,WAAM,MAAAsD,GAAK2H,gBAAgBxN,OAAS,IAC3C+O,EAAAA,IAAI,WAAM,MAAAlJ,GAAK2H,gBAAgBrL,KAAK,OACpCqG,UAAU,SAAAwG,GAKV,IAAK,GAJCC,GAAQpJ,EAAKkH,OAAOgB,UAIjB/G,EAAI,EAAGA,EAAIiI,EAAMjP,OAAS,EAAGgH,IAAK,CACzC,GAAMkI,IAASrJ,EAAKmH,iBAAmBhG,GAAKiI,EAAMjP,OAC5CwO,EAAOS,EAAMC,EAEnB,KAAKV,EAAK5E,UAA2E,IAA/D4E,EAAa,WAAIW,cAAcnN,OAAOqK,QAAQ2C,GAAoB,CACtFnJ,EAAKuJ,cAAcF,EACnB,QAIJrJ,EAAK2H,qBAGAtO,MAOT4N,EAAFzN,UAAA+P,cAAE,SAAcF,GACZ,GAAMG,GAAgBnQ,KAAK8N,gBAE3B9N,MAAK8N,iBAAmBkC,EACxBhQ,KAAK2O,YAAc3O,KAAK6N,OAAOgB,UAAUmB,GAErChQ,KAAK8N,mBAAqBqC,GAC5BnQ,KAAKwO,OAAO4B,KAAKJ,IAQrBpC,EAAFzN,UAAAkQ,UAAE,SAAUjM,GACR,GAAMuL,GAAUvL,EAAMuL,OAEtB,QAAQA,GACN,IAAKW,GAAAA,IAEH,WADAtQ,MAAKuO,OAAO6B,MAGd,KAAKG,GAAAA,WACH,GAAIvQ,KAAKqO,UAAW,CAClBrO,KAAKwQ,mBACL,OAGJ,IAAKC,GAAAA,SACH,GAAIzQ,KAAKqO,UAAW,CAClBrO,KAAK0Q,uBACL,OAGJ,IAAKC,GAAAA,YACH,GAAyB,QAArB3Q,KAAKmP,YAAuB,CAC9BnP,KAAKwQ,mBACL,OACK,GAAyB,QAArBxQ,KAAKmP,YAAuB,CACrCnP,KAAK0Q,uBACL,OAGJ,IAAKE,GAAAA,WACH,GAAyB,QAArB5Q,KAAKmP,YAAuB,CAC9BnP,KAAK0Q,uBACL,OACK,GAAyB,QAArB1Q,KAAKmP,YAAuB,CACrCnP,KAAKwQ,mBACL,OAGJ,QAWE,YARIpM,EAAMyM,KAA4B,IAArBzM,EAAMyM,IAAI/P,OACzBd,KAAKgO,iBAAiBoC,KAAKhM,EAAMyM,IAAIC,sBAC3BnB,GAAWoB,EAAAA,GAAKpB,GAAWqB,EAAAA,GAAOrB,GAAWsB,EAAAA,MAAQtB,GAAWuB,EAAAA,OAC1ElR,KAAKgO,iBAAiBoC,KAAKe,OAAOC,aAAazB,KAQrD3P,KAAKsO,mBACLlK,EAAMiN,kBAIRjR,OAAFiG,eAAMuH,EAANzN,UAAA,uBAAE,WACE,MAAOH,MAAK8N,kDAId1N,OAAFiG,eAAMuH,EAANzN,UAAA,kBAAE,WACE,MAAOH,MAAK2O,6CAIdf,EAAFzN,UAAAmR,mBAAE,WACEtR,KAAKuR,sBAAsB,EAAG,IAIhC3D,EAAFzN,UAAAqR,kBAAE,WACExR,KAAKuR,sBAAsBvR,KAAK6N,OAAO/M,OAAS,GAAI,IAItD8M,EAAFzN,UAAAqQ,kBAAE,WACExQ,KAAK8N,iBAAmB,EAAI9N,KAAKsR,qBAAuBtR,KAAKyR,sBAAsB,IAIrF7D,EAAFzN,UAAAuQ,sBAAE,WACE1Q,KAAK8N,iBAAmB,GAAK9N,KAAK+N,MAAQ/N,KAAKwR,oBACLxR,KAAKyR,uBAAuB,IAOxE7D,EAAFzN,UAAAuR,sBAAE,SAAsB1B,GACpBhQ,KAAK8N,iBAAmBkC,GAQlBpC,EAAVzN,UAAAsR,sBAAA,SAAgCE,EAAe5B,OAA/C,KAAAA,IAA+CA,EAAQ/P,KAAK6N,OAAOgB,WAC/D7O,KAAK+N,MAAQ/N,KAAK4R,qBAAqBD,EAAO5B,GACjC/P,KAAK6R,wBAAwBF,EAAO5B,IAQ3CnC,EAAVzN,UAAAyR,qBAAA,SAA+BD,EAAe5B,GAE1C/P,KAAK8N,kBACF9N,KAAK8N,iBAAmB6D,EAAQ5B,EAAMjP,QAAUiP,EAAMjP,OAGrDiP,EAAM/P,KAAK8N,kBAAkBpD,SAC/B1K,KAAK4R,qBAAqBD,EAAO5B,GAEjC/P,KAAKkQ,cAAclQ,KAAK8N,mBASpBF,EAAVzN,UAAA0R,wBAAA,SAAkCF,EAAe5B,GAC7C/P,KAAKuR,sBAAsBvR,KAAK8N,iBAAmB6D,EAAOA,EAAO5B,IAQ3DnC,EAAVzN,UAAAoR,sBAAA,SAAgCvB,EAAe8B,EACb/B,GAC9B,OADJ,KAAAA,IAAkCA,EAAQ/P,KAAK6N,OAAOgB,WAC7CkB,EAAMC,GAAX,CAEA,KAAOD,EAAMC,GAAOtF,UAElB,GADAsF,GAAS8B,GACJ/B,EAAMC,GAAU,MAGvBhQ,MAAKkQ,cAAcF,KAnTvBpC,KCuBAmE,EAAA,SAAAC,+DAvBA,MAuBmDC,GAAnDF,EAAAC,GAOED,EAAF5R,UAAA+P,cAAE,SAAcF,GACRhQ,KAAKkS,YACPlS,KAAKkS,WAAWC,oBAElBH,EAAJ7R,UAAU+P,cAAVkC,KAAApS,KAAwBgQ,GAChBhQ,KAAKkS,YACPlS,KAAKkS,WAAWG,mBApCtBN,GAuBmDnE,GCFnD0E,EAAA,SAAAN,oEACArL,GAAA4L,QAAiC,YAtBjC,MAqBwCN,GAAxCK,EAAAN,GAOEM,EAAFnS,UAAAqS,eAAE,SAAeC,GAEb,MADAzS,MAAKuS,QAAUE,EACRzS,MAOTsS,EAAFnS,UAAA+P,cAAE,SAAcF,GACZgC,EAAJ7R,UAAU+P,cAAVkC,KAAApS,KAAwBgQ,GAEhBhQ,KAAKkS,YACPlS,KAAKkS,WAAW7J,MAAMrI,KAAKuS,UAzCjCD,GAqBwC1E,GNF3B8E,EAA+B,GAAIC,GAAAA,eAA4B,qCAS1E,QAAF7O,GACwD8O,EACxBlP,GAAA1D,KAAhC0D,UAAgCA,EAK5B1D,KAAK6S,aAAeD,GAAgB5S,KAAK8S,qBAnC7C,MA4CEhP,GAAF3D,UAAA4S,SAAE,SAASvH,EAAiBwH,GAA1B,GAAFrM,GAAA3G,IAWI,YAXJ,KAAAgT,IAA4BA,EAA5B,UACIhT,KAAK6S,aAAaI,YAAc,GAGhCjT,KAAK6S,aAAa7P,aAAa,YAAagQ,GAOrC,GAAI5L,SAAQ,SAAAC,GACjB6L,WAAW,WACTvM,EAAKkM,aAAaI,YAAczH,EAChCnE,KACC,QAIPvD,EAAF3D,UAAAkK,YAAE,WACMrK,KAAK6S,cAAgB7S,KAAK6S,aAAapM,YACzCzG,KAAK6S,aAAapM,WAAWC,YAAY1G,KAAK6S,eAI1C/O,EAAV3D,UAAA2S,8BACI,GAAIK,GAASnT,KAAK0D,UAAUmF,cAAc,MAQ1C,OANAsK,GAAOrK,UAAUC,IAAI,uBACrBoK,EAAOnQ,aAAa,cAAe,QACnCmQ,EAAOnQ,aAAa,YAAa,UAEjChD,KAAK0D,UAAUuJ,KAAKP,YAAYyG,GAEzBA,kBAtDX/R,KAACwE,EAAAA,iDAKDxE,SAAAK,GAAAiI,aAAAtI,KAAOqM,EAAAA,WAAPrM,KAAmBuI,EAAAA,OAAnBC,MAA0B8I,OAC1BtR,SAAAK,GAAAiI,aAAAtI,KAAOuI,EAAAA,OAAPC,MAAcC,EAAAA,eA9Bd/F,KA0FasP,GAEX7F,QAASzJ,EACT0J,OACG,GAAIC,GAAAA,SAAY,GAAIC,GAAAA,SAAY5J,IAChC,GAAI2J,GAAAA,SAAY,GAAI9D,GAAAA,OAAO+I,IAC5B7I,EAAAA,UAEF8D,WAAY/J,gBCxBZ,QAAFM,GAAsB+B,EAAyBrB,GAAzB5E,KAAtBiG,QAAsBA,EAAyBjG,KAA/C4E,UAA+CA,EA7B/C5E,KAAAuS,QAAiC,KAMjCvS,KAAAqT,gBAA2B,EAe3BrT,KAAAsT,aAAyB,GAAIlI,KAG7BpL,KAAAuT,2BAAuC,aAGvCvT,KAAAwT,uBAAmC,EAxEnC,MA2FEtP,GAAF/D,UAAAsT,QAAE,SACI/S,EACAgT,EACAC,GAHJ,GAAFhN,GAAA3G,IAWI,IANM0T,YAAoBE,GAAAA,YACxBD,EAAgBD,GAElBC,IAAkBA,GAGb3T,KAAK4E,UAAUM,UAClB,MAAO2O,GAAAA,GAAa,KAGtB,IAAI7T,KAAKsT,aAAa7H,IAAI/K,GAAU,CAClC,GAAIoT,GAAa9T,KAAKsT,aAAatH,IAAItL,EAEvC,OADN,GAAkBiT,cAAgBA,EAClC,EAAyBI,QAAQ5K,eAI7B,GAAI6K,IACFC,SAAU,aACVN,cAAeA,EACfI,QAAS,GAAI9F,GAAAA,QAEfjO,MAAKsT,aAAazG,IAAInM,EAASsT,GAC/BhU,KAAKkU,iCAGL,IAAIC,GAAgB,SAAC/P,GAAsB,MAAAuC,GAAKyN,SAAShQ,EAAO1D,IAC5D2T,EAAe,SAACjQ,GAAsB,MAAAuC,GAAK2N,QAAQlQ,EAAO1D,GAY9D,OAXAV,MAAKiG,QAAQY,kBAAkB,WAC7BnG,EAAQoG,iBAAiB,QAASqN,GAAe,GACjDzT,EAAQoG,iBAAiB,OAAQuN,GAAc,KAIjDL,EAAKC,SAAW,WACdvT,EAAQ6T,oBAAoB,QAASJ,GAAe,GACpDzT,EAAQ6T,oBAAoB,OAAQF,GAAc,IAG7CL,EAAKD,QAAQ5K,gBAOtBjF,EAAF/D,UAAAqU,eAAE,SAAe9T,GACb,GAAM+T,GAAczU,KAAKsT,aAAatH,IAAItL,EAEtC+T,KACFA,EAAYR,WACZQ,EAAYV,QAAQW,WAEpB1U,KAAK2U,YAAYjU,GACjBV,KAAKsT,aAAaxG,OAAOpM,GACzBV,KAAK4U,oCAST1Q,EAAF/D,UAAA0U,SAAE,SAASnU,EAAsB+R,GAC7BzS,KAAK8U,+BAA+BrC,GACpC/R,EAAQ2H,SAGVnE,EAAF/D,UAAAkK,YAAE,WAAA,GAAF1D,GAAA3G,IACIA,MAAKsT,aAAayB,QAAQ,SAACC,EAAOtU,GAAY,MAAAiG,GAAK6N,eAAe9T,MAI5DwD,EAAV/D,UAAA8U,8CAEI,IAAKjV,KAAK4E,UAAUM,UAApB,CAKA,GAAIgQ,GAA0B,WAC5BvO,EAAKwO,iBAAmB,KACxBxO,EAAKmO,+BAA+B,aAKlCM,EAA4B,WACzBzO,EAAKwO,kBACRxO,EAAKmO,+BAA+B,UAOpCO,EAA6B,SAACjR,GACJ,MAAxBuC,EAAK2O,iBACPC,aAAa5O,EAAK2O,iBAEpB3O,EAAKwO,iBAAmB/Q,EAAMoR,OAC9B7O,EAAK2O,gBAAkBpC,WAAW,WAAM,MAAAvM,GAAKwO,iBAAmB,MA1KvC,MA+KvBM,EAAsB,WACxB9O,EAAK0M,gBAAiB,EACtB1M,EAAK+O,sBAAwBxC,WAAW,WAAM,MAAAvM,GAAK0M,gBAAiB,GAAO,GAK7ErT,MAAKiG,QAAQY,kBAAkB,WAC7B8O,SAAS7O,iBAAiB,UAAWoO,GAAyB,GAC9DS,SAAS7O,iBAAiB,YAAasO,GAA2B,GAClEO,SAAS7O,iBAAiB,aAAcuO,GACpCO,EAAAA,kCAAoCC,SAAS,EAAMC,SAAS,IAChEvV,OAAOuG,iBAAiB,QAAS2O,KAGnCzV,KAAKuT,2BAA6B,WAChCoC,SAASpB,oBAAoB,UAAWW,GAAyB,GACjES,SAASpB,oBAAoB,YAAaa,GAA2B,GACrEO,SAASpB,oBAAoB,aAAcc,GACvCO,EAAAA,kCAAoCC,SAAS,EAAMC,SAAS,IAChEvV,OAAOgU,oBAAoB,QAASkB,GAGpCF,aAAa5O,EAAK+O,uBAClBH,aAAa5O,EAAK2O,iBAClBC,aAAa5O,EAAKoP,qBAId7R,EAAV/D,UAAA6V,aAAA,SAAuBtV,EAAkBuV,EAAmBC,GACpDA,EACFxV,EAAQoI,UAAUC,IAAIkN,GAEtBvV,EAAQoI,UAAUqN,OAAOF,IASrB/R,EAAV/D,UAAAwU,YAAA,SAAsBjU,EAAsB+R,GACpBzS,KAAKsT,aAAatH,IAAItL,KAGxCV,KAAKgW,aAAatV,EAAS,gBAAiB+R,GAC5CzS,KAAKgW,aAAatV,EAAS,oBAAgC,UAAX+R,GAChDzS,KAAKgW,aAAatV,EAAS,uBAAmC,aAAX+R,GACnDzS,KAAKgW,aAAatV,EAAS,oBAAgC,UAAX+R,GAChDzS,KAAKgW,aAAatV,EAAS,sBAAkC,YAAX+R,KAQ9CvO,EAAV/D,UAAA2U,+BAAA,SAAyCrC,aACrCzS,MAAKuS,QAAUE,EACfzS,KAAK+V,iBAAmB7C,WAAW,WAAM,MAAAvM,GAAK4L,QAAU,MAAM,IAQxDrO,EAAV/D,UAAAiW,kBAAA,SAA4BhS,GAkBxB,GAAIiS,GAAcjS,EAAMoR,MACxB,OAAOxV,MAAKmV,2BAA4BmB,OAAQD,YAAuBC,QAClED,IAAgBrW,KAAKmV,kBAAoBkB,EAAYE,SAASvW,KAAKmV,oBAQlEjR,EAAV/D,UAAAiU,SAAA,SAAmBhQ,EAAmB1D,GAQlC,GAAM+T,GAAczU,KAAKsT,aAAatH,IAAItL,EACrC+T,KAAiBA,EAAYd,eAAiBjT,IAAY0D,EAAMoR,UAUhExV,KAAKuS,UACJvS,KAAKqT,gBAAkBrT,KAAKwW,iBAC9BxW,KAAKuS,QAAUvS,KAAKwW,iBACXxW,KAAKoW,kBAAkBhS,GAChCpE,KAAKuS,QAAU,QAEfvS,KAAKuS,QAAU,WAInBvS,KAAK2U,YAAYjU,EAASV,KAAKuS,SAC/BkC,EAAYV,QAAQ3D,KAAKpQ,KAAKuS,SAC9BvS,KAAKwW,iBAAmBxW,KAAKuS,QAC7BvS,KAAKuS,QAAU,OAQjBrO,EAAF/D,UAAAmU,QAAE,SAAQlQ,EAAmB1D,GAGzB,GAAM+T,GAAczU,KAAKsT,aAAatH,IAAItL,IAErC+T,GAAgBA,EAAYd,eAAiBvP,EAAMqS,wBAAyBH,OAC7E5V,EAAQ6V,SAASnS,EAAMqS,iBAI3BzW,KAAK2U,YAAYjU,GACjB+T,EAAYV,QAAQ3D,KAAK,QAGnBlM,EAAV/D,UAAA+T,2CAEyC,KAA/BlU,KAAKwT,wBACTxT,KAAKiV,4BAID/Q,EAAV/D,UAAAyU,6CAEW5U,KAAKwT,yBACVxT,KAAKuT,6BACLvT,KAAKuT,2BAA6B,8BAlUxCnS,KAACwE,EAAAA,iDA7BDxE,KAAEqI,EAAAA,SANFrI,KAAQyE,EAAAA,YAPR3B,kBAmYE,QAAFwS,GAAsB3M,EAAiC4M,GAArD,GAAFhQ,GAAA3G,IAAsBA,MAAtB+J,YAAsBA,EAAiC/J,KAAvD2W,cAAuDA,EAFvD3W,KAAA4W,eAA6B,GAAIC,GAAAA,aAG7B7W,KAAK8W,qBAAuB9W,KAAK2W,cAAclD,QAC3CzT,KAAK+J,YAAYG,cACjBlK,KAAK+J,YAAYG,cAAc3I,aAAa,2BAC3C+H,UAAU,SAAAmJ,GAAU,MAAA9L,GAAKiQ,eAAeG,KAAKtE,KAvYtD,MA0YEiE,GAAFvW,UAAAkK,YAAE,WACErK,KAAK2W,cAAcnC,eAAexU,KAAK+J,YAAYG,eACnDlK,KAAK8W,qBAAqBrH,8BAhB9BrO,KAACmJ,EAAAA,UAADX,OACEY,SAAU,6FAnXZpJ,KAAEqJ,EAAAA,aAiCFrJ,KAAa8C,uBAsVb0S,iBAAAxV,KAAG4V,EAAAA,UAjYHN,KAuZaO,IAEX1J,QAASrJ,EACTsJ,OAAQ,GAAIC,GAAAA,SAAY,GAAIC,GAAAA,SAAYxJ,GAAeuF,EAAAA,OAAQ5D,EAAAA,UAC/D8H,WAAY5J,GMnZdmT,GAAA,yBARA,sBAqBA9V,KAAC+V,EAAAA,SAADvN,OACEwN,SAAUC,EAAAA,aAAcC,EAAAA,gBACxBC,cAAe3M,EAAcd,EAA8B4M,GAC3Dc,SAAU5M,EAAcd,EAA8B4M,GACtDe,WACE9S,EACA4E,EACA5F,EACAyP,EACA9F,EACA2J,8CA/BJC,oDRyBqC,gEAGI,2DAGK,kaEHf"}