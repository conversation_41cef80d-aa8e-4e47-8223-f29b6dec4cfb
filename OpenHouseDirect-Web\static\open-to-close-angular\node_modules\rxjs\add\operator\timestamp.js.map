{"version": 3, "file": "timestamp.js", "sourceRoot": "", "sources": ["../../../src/add/operator/timestamp.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,0BAA0B,0BAA0B,CAAC,CAAA;AAErD,uBAAU,CAAC,SAAS,CAAC,SAAS,GAAG,qBAAS,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\r\nimport { timestamp } from '../../operator/timestamp';\r\n\r\nObservable.prototype.timestamp = timestamp;\r\n\r\ndeclare module '../../Observable' {\r\n  interface Observable<T> {\r\n    timestamp: typeof timestamp;\r\n  }\r\n}"]}