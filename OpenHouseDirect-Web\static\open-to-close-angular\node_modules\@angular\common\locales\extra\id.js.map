{"version": 3, "file": "id.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/id.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;QACjE,AADkE;KAEnE;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['tengah malam', 'tengah hari', 'pagi', 'siang', 'sore', 'malam'],\n    ,\n  ],\n  ,\n  [\n    '00:00', '12:00', ['00:00', '10:00'], ['10:00', '15:00'], ['15:00', '18:00'],\n    ['18:00', '24:00']\n  ]\n];\n"]}