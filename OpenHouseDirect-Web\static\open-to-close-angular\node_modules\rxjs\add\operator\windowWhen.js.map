{"version": 3, "file": "windowWhen.js", "sourceRoot": "", "sources": ["../../../src/add/operator/windowWhen.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,2BAA2B,2BAA2B,CAAC,CAAA;AAEvD,uBAAU,CAAC,SAAS,CAAC,UAAU,GAAG,uBAAU,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { windowWhen } from '../../operator/windowWhen';\n\nObservable.prototype.windowWhen = windowWhen;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    windowWhen: typeof windowWhen;\n  }\n}"]}