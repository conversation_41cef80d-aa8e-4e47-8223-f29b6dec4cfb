{"version": 3, "file": "portal.es5.js", "sources": ["../../../src/cdk/portal/index.ts", "../../../src/cdk/portal/public-api.ts", "../../../src/cdk/portal/portal-injector.ts", "../../../src/cdk/portal/portal-directives.ts", "../../../src/cdk/portal/dom-portal-outlet.ts", "../../../src/cdk/portal/portal.ts", "../../../src/cdk/portal/portal-errors.ts"], "sourcesContent": ["/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport * from './portal';\nexport * from './dom-portal-outlet';\nexport * from './portal-directives';\nexport * from './portal-injector';\n\nexport {DomPortalOutlet as DomPortalHost} from './dom-portal-outlet';\nexport {\n  CdkPortalOutlet as PortalHostDirective,\n  CdkPortal as TemplatePortalDirective,\n} from './portal-directives';\nexport {PortalOutlet as PortalHost, BasePortalOutlet as BasePortalHost} from './portal';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injector} from '@angular/core';\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n */\nexport class PortalInjector implements Injector {\n  constructor(\n    private _parentInjector: Injector,\n    private _customTokens: WeakMap<any, any>) { }\n\n  get(token: any, notFoundValue?: any): any {\n    const value = this._customTokens.get(token);\n\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n\n    return this._parentInjector.get<any>(token, notFoundValue);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  NgModule,\n  ComponentRef,\n  Directive,\n  EmbeddedViewRef,\n  TemplateRef,\n  ComponentFactoryResolver,\n  ViewContainerRef,\n  OnDestroy,\n  OnInit,\n  Input,\n  EventEmitter,\n  Output,\n} from '@angular/core';\nimport {Portal, TemplatePortal, ComponentPortal, BasePortalOutlet} from './portal';\n\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\n@Directive({\n  selector: '[cdk-portal], [cdkPortal], [portal]',\n  exportAs: 'cdkPortal',\n})\nexport class CdkPortal extends TemplatePortal {\n  constructor(templateRef: TemplateRef<any>, viewContainerRef: ViewContainerRef) {\n    super(templateRef, viewContainerRef);\n  }\n}\n\n/**\n * Possible attached references to the CdkPortalOutlet.\n */\nexport type CdkPortalOutletAttachedRef = ComponentRef<any> | EmbeddedViewRef<any> | null;\n\n\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\n@Directive({\n  selector: '[cdkPortalOutlet], [cdkPortalHost], [portalHost]',\n  exportAs: 'cdkPortalOutlet, cdkPortalHost',\n  inputs: ['portal: cdkPortalOutlet']\n})\nexport class CdkPortalOutlet extends BasePortalOutlet implements OnInit, OnDestroy {\n  /** Whether the portal component is initialized. */\n  private _isInitialized = false;\n\n  /** Reference to the currently-attached component/view ref. */\n  private _attachedRef: CdkPortalOutletAttachedRef;\n\n  constructor(\n      private _componentFactoryResolver: ComponentFactoryResolver,\n      private _viewContainerRef: ViewContainerRef) {\n    super();\n  }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('portalHost')\n  get _deprecatedPortal() { return this.portal; }\n  set _deprecatedPortal(v) { this.portal = v; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('cdkPortalHost')\n  get _deprecatedPortalHost() { return this.portal; }\n  set _deprecatedPortalHost(v) { this.portal = v; }\n\n  /** Portal associated with the Portal outlet. */\n  get portal(): Portal<any> | null {\n    return this._attachedPortal;\n  }\n\n  set portal(portal: Portal<any> | null) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n\n    if (this.hasAttached()) {\n      super.detach();\n    }\n\n    if (portal) {\n      super.attach(portal);\n    }\n\n    this._attachedPortal = portal;\n  }\n\n  @Output('attached') attached: EventEmitter<CdkPortalOutletAttachedRef> =\n      new EventEmitter<CdkPortalOutletAttachedRef>();\n\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef(): CdkPortalOutletAttachedRef {\n    return this._attachedRef;\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedPortal = null;\n    this._attachedRef = null;\n  }\n\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    portal.setAttachedHost(this);\n\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ?\n        portal.viewContainerRef :\n        this._viewContainerRef;\n\n    const componentFactory =\n        this._componentFactoryResolver.resolveComponentFactory(portal.component);\n    const ref = viewContainerRef.createComponent(\n        componentFactory, viewContainerRef.length,\n        portal.injector || viewContainerRef.parentInjector);\n\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n\n    return ref;\n  }\n\n  /**\n   * Attach the given TemplatePortal to this PortlHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context);\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n\n    return viewRef;\n  }\n}\n\n\n@NgModule({\n  exports: [CdkPortal, CdkPortalOutlet],\n  declarations: [CdkPortal, CdkPortalOutlet],\n})\nexport class PortalModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentFactoryResolver,\n  ComponentRef,\n  EmbeddedViewRef,\n  ApplicationRef,\n  Injector,\n} from '@angular/core';\nimport {BasePortalOutlet, ComponentPortal, TemplatePortal} from './portal';\n\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nexport class DomPortalOutlet extends BasePortalOutlet {\n  constructor(\n      /** Element into which the content is projected. */\n      public outletElement: Element,\n      private _componentFactoryResolver: ComponentFactoryResolver,\n      private _appRef: ApplicationRef,\n      private _defaultInjector: Injector) {\n    super();\n  }\n\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    let componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n    let componentRef: ComponentRef<T>;\n\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(\n          componentFactory,\n          portal.viewContainerRef.length,\n          portal.injector || portal.viewContainerRef.parentInjector);\n\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      componentRef = componentFactory.create(portal.injector || this._defaultInjector);\n      this._appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        this._appRef.detachView(componentRef.hostView);\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n\n    return componentRef;\n  }\n\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context);\n    viewRef.detectChanges();\n\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n\n    this.setDisposeFn((() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    }));\n\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose(): void {\n    super.dispose();\n    if (this.outletElement.parentNode != null) {\n      this.outletElement.parentNode.removeChild(this.outletElement);\n    }\n  }\n\n  /** Gets the root HTMLElement for an instantiated component. */\n  private _getComponentRootNode(componentRef: ComponentRef<any>): HTMLElement {\n    return (componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n    TemplateRef,\n    ViewContainerRef,\n    ElementRef,\n    ComponentRef,\n    EmbeddedViewRef,\n    Injector\n} from '@angular/core';\nimport {\n    throwNullPortalOutletError,\n    throwPortalAlreadyAttachedError,\n    throwNoPortalAttachedError,\n    throwNullPortalError,\n    throwPortalOutletAlreadyDisposedError,\n    throwUnknownPortalTypeError\n} from './portal-errors';\n\n/** Interface that can be used to generically type a class. */\nexport interface ComponentType<T> {\n  new (...args: any[]): T;\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nexport abstract class Portal<T> {\n  private _attachedHost: PortalOutlet | null;\n\n  /** Attach this portal to a host. */\n  attach(host: PortalOutlet): T {\n    if (host == null) {\n      throwNullPortalOutletError();\n    }\n\n    if (host.hasAttached()) {\n      throwPortalAlreadyAttachedError();\n    }\n\n    this._attachedHost = host;\n    return <T> host.attach(this);\n  }\n\n  /** Detach this portal from its host */\n  detach(): void {\n    let host = this._attachedHost;\n\n    if (host == null) {\n      throwNoPortalAttachedError();\n    } else {\n      this._attachedHost = null;\n      host.detach();\n    }\n  }\n\n  /** Whether this portal is attached to a host. */\n  get isAttached(): boolean {\n    return this._attachedHost != null;\n  }\n\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host: PortalOutlet | null) {\n    this._attachedHost = host;\n  }\n}\n\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nexport class ComponentPortal<T> extends Portal<ComponentRef<T>> {\n  /** The type of the component that will be instantiated for attachment. */\n  component: ComponentType<T>;\n\n  /**\n   * [Optional] Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef?: ViewContainerRef | null;\n\n  /** [Optional] Injector used for the instantiation of the component. */\n  injector?: Injector | null;\n\n  constructor(\n      component: ComponentType<T>,\n      viewContainerRef?: ViewContainerRef | null,\n      injector?: Injector | null) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n  }\n}\n\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nexport class TemplatePortal<C = any> extends Portal<C> {\n  /** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef: TemplateRef<C>;\n\n  /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef: ViewContainerRef;\n\n  /** Contextual data to be passed in to the embedded view. */\n  context: C | undefined;\n\n  constructor(template: TemplateRef<C>, viewContainerRef: ViewContainerRef, context?: C) {\n    super();\n    this.templateRef = template;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n  }\n\n  get origin(): ElementRef {\n    return this.templateRef.elementRef;\n  }\n\n  /**\n   * Attach the the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host: PortalOutlet, context: C | undefined = this.context): C {\n    this.context = context;\n    return super.attach(host);\n  }\n\n  detach(): void {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n\n\n/** A `PortalOutlet` is an space that can contain a single `Portal`. */\nexport interface PortalOutlet {\n  /** Attaches a portal to this outlet. */\n  attach(portal: Portal<any>): any;\n\n  /** Detaches the currently attached portal from this outlet. */\n  detach(): any;\n\n  /** Performs cleanup before the outlet is destroyed. */\n  dispose(): void;\n\n  /** Whether there is currently a portal attached to this outlet. */\n  hasAttached(): boolean;\n}\n\n\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nexport abstract class BasePortalOutlet implements PortalOutlet {\n  /** The portal currently attached to the host. */\n  protected _attachedPortal: Portal<any> | null;\n\n  /** A function that will permanently dispose this host. */\n  private _disposeFn: (() => void) | null;\n\n  /** Whether this host has already been permanently disposed. */\n  private _isDisposed: boolean = false;\n\n  /** Whether this host has an attached portal. */\n  hasAttached(): boolean {\n    return !!this._attachedPortal;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /** Attaches a portal. */\n  attach(portal: Portal<any>): any {\n    if (!portal) {\n      throwNullPortalError();\n    }\n\n    if (this.hasAttached()) {\n      throwPortalAlreadyAttachedError();\n    }\n\n    if (this._isDisposed) {\n      throwPortalOutletAlreadyDisposedError();\n    }\n\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n    }\n\n    throwUnknownPortalTypeError();\n  }\n\n  abstract attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n\n  abstract attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C>;\n\n  /** Detaches a previously attached portal. */\n  detach(): void {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n\n    this._invokeDisposeFn();\n  }\n\n  /** Permanently dispose of this portal host. */\n  dispose(): void {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n\n  /** @docs-private */\n  setDisposeFn(fn: () => void) {\n    this._disposeFn = fn;\n  }\n\n  private _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nexport function throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nexport function throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nexport function throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nexport function throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n              'a ComponentPortal or a TemplatePortal.');\n}\n\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nexport function throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nexport function throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n"], "names": ["tslib_1.__extends"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AMYA,AAAA,SAAA,oBAAA,GAAA;IACE,MAAM,KAAK,CAAC,iCAAiC,CAAC,CAAC;CAChD;;;;;;AAMD,AAAA,SAAA,+BAAA,GAAA;IACE,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;CACnD;;;;;;AAMD,AAAA,SAAA,qCAAA,GAAA;IACE,MAAM,KAAK,CAAC,6CAA6C,CAAC,CAAC;CAC5D;;;;;;AAMD,AAAA,SAAA,2BAAA,GAAA;IACE,MAAM,KAAK,CAAC,+EAA+E;QAC/E,wCAAwC,CAAC,CAAC;CACvD;;;;;;AAMD,AAAA,SAAA,0BAAA,GAAA;IACE,MAAM,KAAK,CAAC,sDAAsD,CAAC,CAAC;CACrE;;;;;;AAMD,AAAA,SAAA,0BAAA,GAAA;IACE,MAAM,KAAK,CAAC,8DAA8D,CAAC,CAAC;CAC7E;;;;;;;ADvCD;;;;;;;;;;AAkBA,IAAA,MAAA,kBAAA,YAAA;;;;;;;;;IAIE,MAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,IAAkB,EAA3B;QACI,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,0BAA0B,EAAE,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,+BAA+B,EAAE,CAAC;SACnC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,yBAAW,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;KAC9B,CAAH;;;;;;IAGE,MAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,qBAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;QAE9B,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,0BAA0B,EAAE,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;KACF,CAAH;IAGE,MAAF,CAAA,cAAA,CAAM,MAAN,CAAA,SAAA,EAAA,YAAgB,EAAhB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;SACnC;;;KAAH,CAAA,CAAG;;;;;;;;;;;IAMD,MAAF,CAAA,SAAA,CAAA,eAAiB;;;;;;IAAf,UAAgB,IAAyB,EAA3C;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B,CAAH;IA1EA,OAAA,MAAA,CAAA;CA2EA,EAAA,CAAC,CAAA;;;;AAMD,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;IAAwCA,SAAxC,CAAA,eAAA,EAAA,MAAA,CAAA,CAA+D;IAc7D,SAAF,eAAA,CACM,SAA2B,EAC3B,gBAA0C,EAC1C,QAA0B,EAHhC;QAAE,IAAF,KAAA,GAII,MAJJ,CAAA,IAAA,CAAA,IAAA,CAIW,IAJX,IAAA,CAQG;QAHC,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;;KAC1B;IAvGH,OAAA,eAAA,CAAA;CAiFA,CAAwC,MAAM,CAA9C,CAuBC,CAAA;;;;AAKD,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;IAA6CA,SAA7C,CAAA,cAAA,EAAA,MAAA,CAAA,CAAsD;IAUpD,SAAF,cAAA,CAAc,QAAwB,EAAE,gBAAkC,EAAE,OAAW,EAAvF;QAAE,IAAF,KAAA,GACI,MADJ,CAAA,IAAA,CAAA,IAAA,CACW,IADX,IAAA,CAKG;QAHC,KAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC5B,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;KACxB;IAED,MAAF,CAAA,cAAA,CAAM,cAAN,CAAA,SAAA,EAAA,QAAY,EAAZ;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;SACpC;;;KAAH,CAAA,CAAG;;;;;;;;;;;;;;IAOD,cAAF,CAAA,SAAA,CAAA,MAAQ;;;;;;;;IAAN,UAAO,IAAkB,EAAE,OAAqC,EAAlE;QAA6B,IAA7B,OAAA,KAAA,KAAA,CAAA,EAA6B,EAAA,OAA7B,GAAsD,IAAI,CAAC,OAAO,CAAlE,EAAA;QACI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,MAAX,CAAA,SAAA,CAAiB,MAAM,CAAvB,IAAA,CAAA,IAAA,EAAwB,IAAI,CAAC,CAAC;KAC3B,CAAH;;;;IAEE,cAAF,CAAA,SAAA,CAAA,MAAQ;;;IAAN,YAAF;QACI,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,OAAO,MAAX,CAAA,SAAA,CAAiB,MAAM,CAAvB,IAAA,CAAA,IAAA,CAAyB,CAAC;KACvB,CAAH;IA/IA,OAAA,cAAA,CAAA;CA6GA,CAA6C,MAAM,CAAnD,CAmCC,CAAA;;;;;;;;;;;AAuBD,IAAA,gBAAA,kBAAA,YAAA;;;;;QAQA,IAAA,CAAA,WAAA,GAAiC,KAAK,CAAtC;;;;;;;IAGE,gBAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,YAAF;QACI,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;KAC/B,CAAH;;;;;;;IAOE,gBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,MAAmB,EAA5B;QACI,IAAI,CAAC,MAAM,EAAE;YACX,oBAAoB,EAAE,CAAC;SACxB;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,+BAA+B,EAAE,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,qCAAqC,EAAE,CAAC;SACzC;QAED,IAAI,MAAM,YAAY,eAAe,EAAE;YACrC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;YAC9B,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;SAC3C;aAAM,IAAI,MAAM,YAAY,cAAc,EAAE;YAC3C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;YAC9B,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SAC1C;QAED,2BAA2B,EAAE,CAAC;KAC/B,CAAH;;;;;;IAOE,gBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB,CAAH;;;;;;IAGE,gBAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB,CAAH;;;;;;;IAGE,gBAAF,CAAA,SAAA,CAAA,YAAc;;;;;IAAZ,UAAa,EAAc,EAA7B;QACI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB,CAAH;;;;IAEU,gBAAV,CAAA,SAAA,CAAA,gBAA0B;;;;QACtB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;;IApPL,OAAA,gBAAA,CAAA;CAsPA,EAAA,CAAC,CAAA;;;;;;;ADvOD;;;;AAOA,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;IAAqCA,SAArC,CAAA,eAAA,EAAA,MAAA,CAAA,CAAqD;IACnD,SAAF,eAAA,CAEa,aAFb,EAGc,yBAHd,EAIc,OAJd,EAKc,gBALd,EAAA;QAAE,IAAF,KAAA,GAMI,MANJ,CAAA,IAAA,CAAA,IAAA,CAMW,IANX,IAAA,CAOG;QALU,KAAb,CAAA,aAA0B,GAAb,aAAa,CAA1B;QACc,KAAd,CAAA,yBAAuC,GAAzB,yBAAyB,CAAvC;QACc,KAAd,CAAA,OAAqB,GAAP,OAAO,CAArB;QACc,KAAd,CAAA,gBAA8B,GAAhB,gBAAgB,CAA9B;;KAEG;;;;;;;;;;;;IAOD,eAAF,CAAA,SAAA,CAAA,qBAAuB;;;;;;IAArB,UAAyB,MAA0B,EAArD;QAAE,IAAF,KAAA,GAAA,IAAA,CA4BG;QA3BC,qBAAI,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChG,qBAAI,YAA6B,CAAC;;;;;QAMlC,IAAI,MAAM,CAAC,gBAAgB,EAAE;YAC3B,YAAY,GAAG,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAClD,gBAAgB,EAChB,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAC9B,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAE/D,IAAI,CAAC,YAAY,CAAC,YAAxB,EAA8B,OAAA,YAAY,CAAC,OAAO,EAAE,CAApD,EAAoD,CAAC,CAAC;SACjD;aAAM;YACL,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACjF,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,YAAY,CAAC,YAAxB;gBACQ,KAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAC/C,YAAY,CAAC,OAAO,EAAE,CAAC;aACxB,CAAC,CAAC;SACJ;;;QAGD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC,CAAC;QAEzE,OAAO,YAAY,CAAC;KACrB,CAAH;;;;;;;;;;;;IAOE,eAAF,CAAA,SAAA,CAAA,oBAAsB;;;;;;IAApB,UAAwB,MAAyB,EAAnD;QAAE,IAAF,KAAA,GAAA,IAAA,CAoBG;QAnBC,qBAAI,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAC5C,qBAAI,OAAO,GAAG,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACnF,OAAO,CAAC,aAAa,EAAE,CAAC;;;;;QAMxB,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ,EAAtC,EAA0C,OAAA,KAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAlF,EAAkF,CAAC,CAAC;QAEhF,IAAI,CAAC,YAAY,EAAE,YAAvB;YACM,qBAAI,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACF,EAAE,CAAC;;QAGJ,OAAO,OAAO,CAAC;KAChB,CAAH;;;;;;;;IAKE,eAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,MAAJ,CAAA,SAAA,CAAU,OAAO,CAAjB,IAAA,CAAA,IAAA,CAAmB,CAAC;QAChB,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,IAAI,EAAE;YACzC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC/D;KACF,CAAH;;;;;;IAGU,eAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;IAA/B,UAAgC,YAA+B,EAA/D;QACI,yBAAO,mBAAC,YAAY,CAAC,QAAgC,GAAE,SAAS,CAAC,CAAC,CAAgB,EAAC;;IA1GvF,OAAA,eAAA,CAAA;CAsBA,CAAqC,gBAAgB,CAArD,CAsFC,CAAA;;;;;;;ADpGD,AAcA;;;;;IAW+BA,SAA/B,CAAA,SAAA,EAAA,MAAA,CAAA,CAA6C;IAC3C,SAAF,SAAA,CAAc,WAA6B,EAAE,gBAAkC,EAA/E;QACA,OAAI,MAAJ,CAAA,IAAA,CAAA,IAAA,EAAU,WAAW,EAAE,gBAAgB,CAAC,IAAxC,IAAA,CAAA;KACG;;QAPH,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,qCAAqC;oBAC/C,QAAQ,EAAE,WAAW;iBACtB,EAAD,EAAA;;;;QAnBA,EAAA,IAAA,EAAE,WAAW,GAAb;QAEA,EAAA,IAAA,EAAE,gBAAgB,GAAlB;;IAfA,OAAA,SAAA,CAAA;CAiCA,CAA+B,cAAc,CAA7C,CAAA,CAAA;AAAA;;;;;;;;IAwBqCA,SAArC,CAAA,eAAA,EAAA,MAAA,CAAA,CAAqD;IAOnD,SAAF,eAAA,CACc,yBADd,EAEc,iBAFd,EAAA;QAAE,IAAF,KAAA,GAGI,MAHJ,CAAA,IAAA,CAAA,IAAA,CAGW,IAHX,IAAA,CAIG;QAHW,KAAd,CAAA,yBAAuC,GAAzB,yBAAyB,CAAvC;QACc,KAAd,CAAA,iBAA+B,GAAjB,iBAAiB,CAA/B;;;;QAPA,KAAA,CAAA,cAAA,GAA2B,KAAK,CAAhC;QAqDA,KAAA,CAAA,QAAA,GAAM,IAAI,YAAY,EAA8B,CAApD;;KA5CG;IAOH,MAAA,CAAA,cAAA,CAAM,eAAN,CAAA,SAAA,EAAA,mBAAuB,EAAvB;;;;;;QAAA,YAAA,EAA4B,OAAO,IAAI,CAAC,MAAM,CAAC,EAA/C;;;;;QACE,UAAsB,CAAC,EAAzB,EAA6B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;;;;IAO/C,MAAA,CAAA,cAAA,CAAM,eAAN,CAAA,SAAA,EAAA,uBAA2B,EAA3B;;;;;;QAAA,YAAA,EAAgC,OAAO,IAAI,CAAC,MAAM,CAAC,EAAnD;;;;;QACE,UAA0B,CAAC,EAA7B,EAAiC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;;;;IAGjD,MAAF,CAAA,cAAA,CAAM,eAAN,CAAA,SAAA,EAAA,QAAY,EAAZ;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;;;;;QAED,UAAW,MAA0B,EAAvC;;;;;YAKI,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACzD,OAAO;aACR;YAED,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBACtB,MAAN,CAAA,SAAA,CAAY,MAAM,CAAlB,IAAA,CAAA,IAAA,CAAoB,CAAC;aAChB;YAED,IAAI,MAAM,EAAE;gBACV,MAAN,CAAA,SAAA,CAAY,MAAM,CAAlB,IAAA,CAAA,IAAA,EAAmB,MAAM,CAAC,CAAC;aACtB;YAED,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;SAC/B;;;KApBH,CAAA,CAAG;IA0BD,MAAF,CAAA,cAAA,CAAM,eAAN,CAAA,SAAA,EAAA,aAAiB,EAAjB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,YAAY,CAAC;SAC1B;;;KAAH,CAAA,CAAG;;;;IAED,eAAF,CAAA,SAAA,CAAA,QAAU;;;IAAR,YAAF;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC5B,CAAH;;;;IAEE,eAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,MAAJ,CAAA,SAAA,CAAU,OAAO,CAAjB,IAAA,CAAA,IAAA,CAAmB,CAAC;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC1B,CAAH;;;;;;;;;;;;;;IAQE,eAAF,CAAA,SAAA,CAAA,qBAAuB;;;;;;;IAArB,UAAyB,MAA0B,EAArD;QACI,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;;;QAI7B,qBAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,IAAI;YACpD,MAAM,CAAC,gBAAgB;YACvB,IAAI,CAAC,iBAAiB,CAAC;QAE3B,qBAAM,gBAAgB,GAClB,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7E,qBAAM,GAAG,GAAG,gBAAgB,CAAC,eAAe,CACxC,gBAAgB,EAAE,gBAAgB,CAAC,MAAM,EACzC,MAAM,CAAC,QAAQ,IAAI,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAExD,MAAJ,CAAA,SAAA,CAAU,YAAY,CAAtB,IAAA,CAAA,IAAA,EAAuB,YAAvB,EAA6B,OAAA,GAAG,CAAC,OAAO,EAAE,CAA1C,EAA0C,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAExB,OAAO,GAAG,CAAC;KACZ,CAAH;;;;;;;;;;;;IAOE,eAAF,CAAA,SAAA,CAAA,oBAAsB;;;;;;IAApB,UAAwB,MAAyB,EAAnD;QAAE,IAAF,KAAA,GAAA,IAAA,CAUG;QATC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC7B,qBAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9F,MAAJ,CAAA,SAAA,CAAU,YAAY,CAAtB,IAAA,CAAA,IAAA,EAAuB,YAAvB,EAA6B,OAAA,KAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAA3D,EAA2D,CAAC,CAAC;QAEzD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE5B,OAAO,OAAO,CAAC;KAChB,CAAH;;QAzHA,EAAA,IAAA,EAAC,SAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,kDAAkD;oBAC5D,QAAQ,EAAE,gCAAgC;oBAC1C,MAAM,EAAE,CAAC,yBAAyB,CAAC;iBACpC,EAAD,EAAA;;;;QA1CA,EAAA,IAAA,EAAE,wBAAwB,GAA1B;QACA,EAAA,IAAA,EAAE,gBAAgB,GAAlB;;;QA2DA,mBAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,IAAA,EAAA,CAAS,YAAY,EAArB,EAAA,EAAA;QAQA,uBAAA,EAAA,CAAA,EAAA,IAAA,EAAG,KAAK,EAAR,IAAA,EAAA,CAAS,eAAe,EAAxB,EAAA,EAAA;QA6BA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAG,MAAM,EAAT,IAAA,EAAA,CAAU,UAAU,EAApB,EAAA,EAAA;;IA/GA,OAAA,eAAA,CAAA;CAyDA,CAAqC,gBAAgB,CAArD,CAAA,CAAA;AAAA;;;;QAwHA,EAAA,IAAA,EAAC,QAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;oBACrC,YAAY,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;iBAC3C,EAAD,EAAA;;;;IApLA,OAAA,YAAA,CAAA;KAqLA;;;;;;;;;;;;ADtKA,IAAA,cAAA,kBAAA,YAAA;IACE,SAAF,cAAA,CACY,eADZ,EAEY,aAFZ,EAAA;QACY,IAAZ,CAAA,eAA2B,GAAf,eAAe,CAA3B;QACY,IAAZ,CAAA,aAAyB,GAAb,aAAa,CAAzB;KAAiD;;;;;;IAE/C,cAAF,CAAA,SAAA,CAAA,GAAK;;;;;IAAH,UAAI,KAAU,EAAE,aAAmB,EAArC;QACI,qBAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAE5C,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAChC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAM,KAAK,EAAE,aAAa,CAAC,CAAC;KAC5D,CAAH;IA5BA,OAAA,cAAA,CAAA;CA6BA,EAAA,CAAC,CAAA;;;;;GDrBD,AACA,AACA,AACA,AAEA,AACA,AAIA,AAAwF;;;;;;;;GDdxF,AAA6B;;"}