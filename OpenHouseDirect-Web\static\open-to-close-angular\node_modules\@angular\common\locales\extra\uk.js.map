{"version": 3, "file": "uk.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/uk.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;QACjD,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;KAC5D;IACD;QACE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;QACvD,AADwD;KAEzD;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['північ', 'п', 'ранку', 'дня', 'вечора', 'ночі'],\n    ['опівночі', 'пополудні', 'ранку', 'дня', 'вечора', 'ночі'],\n  ],\n  [\n    ['північ', 'полудень', 'ранок', 'день', 'вечір', 'ніч'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['04:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'],\n    ['00:00', '04:00']\n  ]\n];\n"]}