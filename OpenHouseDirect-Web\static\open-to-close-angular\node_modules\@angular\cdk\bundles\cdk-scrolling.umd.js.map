{"version": 3, "file": "cdk-scrolling.umd.js", "sources": ["../../src/cdk/scrolling/scrolling-module.ts", "../../src/cdk/scrolling/viewport-ruler.ts", "../../src/cdk/scrolling/scrollable.ts", "../../src/cdk/scrolling/scroll-dispatcher.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {SCROLL_DISPATCHER_PROVIDER} from './scroll-dispatcher';\nimport {CdkScrollable} from  './scrollable';\nimport {PlatformModule} from '@angular/cdk/platform';\n\n@NgModule({\n  imports: [PlatformModule],\n  exports: [CdkScrollable],\n  declarations: [CdkScrollable],\n  providers: [SCROLL_DISPATCHER_PROVIDER],\n})\nexport class ScrollDispatchModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Optional, SkipSelf, NgZone, OnDestroy} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\nimport {Observable} from 'rxjs/Observable';\nimport {fromEvent} from 'rxjs/observable/fromEvent';\nimport {merge} from 'rxjs/observable/merge';\nimport {auditTime} from 'rxjs/operators/auditTime';\nimport {Subscription} from 'rxjs/Subscription';\nimport {of as observableOf} from 'rxjs/observable/of';\n\n/** Time in ms to throttle the resize events by default. */\nexport const DEFAULT_RESIZE_TIME = 20;\n\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\n@Injectable()\nexport class ViewportRuler implements OnDestroy {\n  /** Cached viewport dimensions. */\n  private _viewportSize: {width: number; height: number};\n\n  /** Stream of viewport change events. */\n  private _change: Observable<Event>;\n\n  /** Subscription to streams that invalidate the cached viewport dimensions. */\n  private _invalidateCache: Subscription;\n\n  constructor(platform: Platform, ngZone: NgZone) {\n    this._change = platform.isBrowser ? ngZone.runOutsideAngular(() => {\n      return merge<Event>(fromEvent(window, 'resize'), fromEvent(window, 'orientationchange'));\n    }) : observableOf();\n\n    this._invalidateCache = this.change().subscribe(() => this._updateViewportSize());\n  }\n\n  ngOnDestroy() {\n    this._invalidateCache.unsubscribe();\n  }\n\n  /** Returns the viewport's width and height. */\n  getViewportSize(): Readonly<{width: number, height: number}> {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n\n    return {width: this._viewportSize.width, height: this._viewportSize.height};\n  }\n\n  /** Gets a ClientRect for the viewport's bounds. */\n  getViewportRect(): ClientRect {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {width, height} = this.getViewportSize();\n\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width,\n    };\n  }\n\n  /** Gets the (top, left) scroll position of the viewport. */\n  getViewportScrollPosition() {\n    // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n    const documentRect = document.documentElement.getBoundingClientRect();\n\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY ||\n                 document.documentElement.scrollTop || 0;\n\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX ||\n                  document.documentElement.scrollLeft || 0;\n\n    return {top, left};\n  }\n\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * @param throttle Time in milliseconds to throttle the stream.\n   */\n  change(throttleTime: number = DEFAULT_RESIZE_TIME): Observable<Event> {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n\n  /** Updates the cached viewport size. */\n  private _updateViewportSize() {\n    this._viewportSize = {width: window.innerWidth, height: window.innerHeight};\n  }\n}\n\n/** @docs-private */\nexport function VIEWPORT_RULER_PROVIDER_FACTORY(parentRuler: ViewportRuler,\n                                                platform: Platform,\n                                                ngZone: NgZone) {\n  return parentRuler || new ViewportRuler(platform, ngZone);\n}\n\n/** @docs-private */\nexport const VIEWPORT_RULER_PROVIDER = {\n  // If there is already a ViewportRuler available, use that. Otherwise, provide a new one.\n  provide: ViewportRuler,\n  deps: [[new Optional(), new SkipSelf(), ViewportRuler], Platform, NgZone],\n  useFactory: VIEWPORT_RULER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive, ElementRef, OnInit, OnDestroy, NgZone} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {Subject} from 'rxjs/Subject';\nimport {ScrollDispatcher} from './scroll-dispatcher';\n\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\n@Directive({\n  selector: '[cdk-scrollable], [cdkScrollable]'\n})\nexport class CdkScrollable implements OnInit, OnDestroy {\n  private _elementScrolled: Subject<Event> = new Subject();\n  private _scrollListener = (event: Event) => this._elementScrolled.next(event);\n\n  constructor(private _elementRef: ElementRef,\n              private _scroll: ScrollDispatcher,\n              private _ngZone: NgZone) {}\n\n  ngOnInit() {\n    this._ngZone.runOutsideAngular(() => {\n      this.getElementRef().nativeElement.addEventListener('scroll', this._scrollListener);\n    });\n\n    this._scroll.register(this);\n  }\n\n  ngOnDestroy() {\n    this._scroll.deregister(this);\n\n    if (this._scrollListener) {\n      this.getElementRef().nativeElement.removeEventListener('scroll', this._scrollListener);\n    }\n  }\n\n  /**\n   * Returns observable that emits when a scroll event is fired on the host element.\n   */\n  elementScrolled(): Observable<any> {\n    return this._elementScrolled.asObservable();\n  }\n\n  getElementRef(): ElementRef {\n    return this._elementRef;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementRef, Injectable, NgZone, Optional, SkipSelf, OnDestroy} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Observable} from 'rxjs/Observable';\nimport {of as observableOf} from 'rxjs/observable/of';\nimport {fromEvent} from 'rxjs/observable/fromEvent';\nimport {auditTime} from 'rxjs/operators/auditTime';\nimport {filter} from 'rxjs/operators/filter';\nimport {CdkScrollable} from './scrollable';\n\n\n/** Time in ms to throttle the scrolling events by default. */\nexport const DEFAULT_SCROLL_TIME = 20;\n\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\n@Injectable()\nexport class ScrollDispatcher implements OnDestroy {\n  constructor(private _ngZone: NgZone, private _platform: Platform) { }\n\n  /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n  private _scrolled = new Subject<CdkScrollable|void>();\n\n  /** Keeps track of the global `scroll` and `resize` subscriptions. */\n  _globalSubscription: Subscription | null = null;\n\n  /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n  private _scrolledCount = 0;\n\n  /**\n   * Map of all the scrollable references that are registered with the service and their\n   * scroll event subscriptions.\n   */\n  scrollContainers: Map<CdkScrollable, Subscription> = new Map();\n\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n  register(scrollable: CdkScrollable): void {\n    const scrollSubscription = scrollable.elementScrolled()\n        .subscribe(() => this._scrolled.next(scrollable));\n\n    this.scrollContainers.set(scrollable, scrollSubscription);\n  }\n\n  /**\n   * Deregisters a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n  deregister(scrollable: CdkScrollable): void {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n  scrolled(auditTimeInMs: number = DEFAULT_SCROLL_TIME): Observable<CdkScrollable|void> {\n    return this._platform.isBrowser ? Observable.create(observer => {\n      if (!this._globalSubscription) {\n        this._addGlobalListener();\n      }\n\n      // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n      const subscription = auditTimeInMs > 0 ?\n        this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) :\n        this._scrolled.subscribe(observer);\n\n      this._scrolledCount++;\n\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n\n        if (!this._scrolledCount) {\n          this._removeGlobalListener();\n        }\n      };\n    }) : observableOf<void>();\n  }\n\n  ngOnDestroy() {\n    this._removeGlobalListener();\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n  }\n\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n  ancestorScrolled(elementRef: ElementRef, auditTimeInMs?: number): Observable<CdkScrollable|void> {\n    const ancestors = this.getAncestorScrollContainers(elementRef);\n\n    return this.scrolled(auditTimeInMs).pipe(filter(target => {\n      return !target || ancestors.indexOf(target) > -1;\n    }));\n  }\n\n  /** Returns all registered Scrollables that contain the provided element. */\n  getAncestorScrollContainers(elementRef: ElementRef): CdkScrollable[] {\n    const scrollingContainers: CdkScrollable[] = [];\n\n    this.scrollContainers.forEach((_subscription: Subscription, scrollable: CdkScrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n\n    return scrollingContainers;\n  }\n\n  /** Returns true if the element is contained within the provided Scrollable. */\n  private _scrollableContainsElement(scrollable: CdkScrollable, elementRef: ElementRef): boolean {\n    let element = elementRef.nativeElement;\n    let scrollableElement = scrollable.getElementRef().nativeElement;\n\n    // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n    do {\n      if (element == scrollableElement) { return true; }\n    } while (element = element.parentElement);\n\n    return false;\n  }\n\n  /** Sets up the global scroll listeners. */\n  private _addGlobalListener() {\n    this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n      return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n    });\n  }\n\n  /** Cleans up the global scroll listener. */\n  private _removeGlobalListener() {\n    if (this._globalSubscription) {\n      this._globalSubscription.unsubscribe();\n      this._globalSubscription = null;\n    }\n  }\n}\n\n/** @docs-private */\nexport function SCROLL_DISPATCHER_PROVIDER_FACTORY(\n    parentDispatcher: ScrollDispatcher, ngZone: NgZone, platform: Platform) {\n  return parentDispatcher || new ScrollDispatcher(ngZone, platform);\n}\n\n/** @docs-private */\nexport const SCROLL_DISPATCHER_PROVIDER = {\n  // If there is already a ScrollDispatcher available, use that. Otherwise, provide a new one.\n  provide: ScrollDispatcher,\n  deps: [[new Optional(), new SkipSelf(), ScrollDispatcher], NgZone, Platform],\n  useFactory: SCROLL_DISPATCHER_PROVIDER_FACTORY\n};\n"], "names": ["PlatformModule", "NgModule", "Optional", "SkipSelf", "Platform", "NgZone", "Injectable", "auditTime", "observableOf", "merge", "fromEvent", "ElementRef", "Directive", "Subject", "filter", "Observable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AGqBA,IAAa,mBAAmB,GAAG,EAAE,CAAC;;;;;;IAQpC,SAAF,gBAAA,CAAsB,OAAe,EAAU,SAAmB,EAAlE;QAAsB,IAAtB,CAAA,OAA6B,GAAP,OAAO,CAAQ;QAAU,IAA/C,CAAA,SAAwD,GAAT,SAAS,CAAU;;;;QAGlE,IAAA,CAAA,SAAA,GAAsB,IAAIa,oBAAO,EAAsB,CAAvD;;;;QAGA,IAAA,CAAA,mBAAA,GAA6C,IAAI,CAAjD;;;;QAGA,IAAA,CAAA,cAAA,GAA2B,CAAC,CAA5B;;;;;QAMA,IAAA,CAAA,gBAAA,GAAuD,IAAI,GAAG,EAAE,CAAhE;KAfuE;;;;;;;;;;;;IAsBrE,gBAAF,CAAA,SAAA,CAAA,QAAU;;;;;;IAAR,UAAS,UAAyB,EAApC;QAAE,IAAF,KAAA,GAAA,IAAA,CAKG;QAJC,qBAAM,kBAAkB,GAAG,UAAU,CAAC,eAAe,EAAE;aAClD,SAAS,CAAC,YAAnB,EAAyB,OAAA,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAxD,EAAwD,CAAC,CAAC;QAEtD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;KAC3D,CAAH;;;;;;;;;;IAME,gBAAF,CAAA,SAAA,CAAA,UAAY;;;;;IAAV,UAAW,UAAyB,EAAtC;QACI,qBAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAElE,IAAI,mBAAmB,EAAE;YACvB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SAC1C;KACF,CAAH;;;;;;;;;;;;;;;;;;;;;;;IAYE,gBAAF,CAAA,SAAA,CAAA,QAAU;;;;;;;;;;;;IAAR,UAAS,aAA2C,EAAtD;QAAE,IAAF,KAAA,GAAA,IAAA,CAuBG;QAvBQ,IAAX,aAAA,KAAA,KAAA,CAAA,EAAW,EAAA,aAAX,GAAA,mBAAsD,CAAtD,EAAA;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,GAAGE,0BAAU,CAAC,MAAM,CAAC,UAAA,QAAQ,EAAhE;YACM,IAAI,CAAC,KAAI,CAAC,mBAAmB,EAAE;gBAC7B,KAAI,CAAC,kBAAkB,EAAE,CAAC;aAC3B;;;YAID,qBAAM,YAAY,GAAG,aAAa,GAAG,CAAC;gBACpC,KAAI,CAAC,SAAS,CAAC,IAAI,CAACR,kCAAS,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACjE,KAAI,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAErC,KAAI,CAAC,cAAc,EAAE,CAAC;YAEtB,OAAO,YAAb;gBACQ,YAAY,CAAC,WAAW,EAAE,CAAC;gBAC3B,KAAI,CAAC,cAAc,EAAE,CAAC;gBAEtB,IAAI,CAAC,KAAI,CAAC,cAAc,EAAE;oBACxB,KAAI,CAAC,qBAAqB,EAAE,CAAC;iBAC9B;aACF,CAAC;SACH,CAAC,GAAGC,qBAAY,EAAQ,CAAC;KAC3B,CAAH;;;;IAEE,gBAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAGG;QAFC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,SAAS,EAA/C,EAAoD,OAAA,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAA9E,EAA8E,CAAC,CAAC;KAC7E,CAAH;;;;;;;;;;;;;;IAQE,gBAAF,CAAA,SAAA,CAAA,gBAAkB;;;;;;;IAAhB,UAAiB,UAAsB,EAAE,aAAsB,EAAjE;QACI,qBAAM,SAAS,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAE/D,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAACM,4BAAM,CAAC,UAAA,MAAM,EAA1D;YACM,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;SAClD,CAAC,CAAC,CAAC;KACL,CAAH;;;;;;;IAGE,gBAAF,CAAA,SAAA,CAAA,2BAA6B;;;;;IAA3B,UAA4B,UAAsB,EAApD;QAAE,IAAF,KAAA,GAAA,IAAA,CAUG;QATC,qBAAM,mBAAmB,GAAoB,EAAE,CAAC;QAEhD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAC,aAA2B,EAAE,UAAyB,EAAzF;YACM,IAAI,KAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;gBAC3D,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACtC;SACF,CAAC,CAAC;QAEH,OAAO,mBAAmB,CAAC;KAC5B,CAAH;;;;;;;IAGU,gBAAV,CAAA,SAAA,CAAA,0BAAoC;;;;;;IAApC,UAAqC,UAAyB,EAAE,UAAsB,EAAtF;QACI,qBAAI,OAAO,GAAG,UAAU,CAAC,aAAa,CAAC;QACvC,qBAAI,iBAAiB,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC;;;QAIjE,GAAG;YACD,IAAI,OAAO,IAAI,iBAAiB,EAAE;gBAAE,OAAO,IAAI,CAAC;aAAE;SACnD,QAAQ,OAAO,GAAG,OAAO,CAAC,aAAa,EAAE;QAE1C,OAAO,KAAK,CAAC;;;;;;IAIP,gBAAV,CAAA,SAAA,CAAA,kBAA4B;;;;;;QACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAA9D;YACM,OAAOJ,mCAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,SAAS,CAAC,YAA5D,EAAkE,OAAA,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAvF,EAAuF,CAAC,CAAC;SACpF,CAAC,CAAC;;;;;;IAIG,gBAAV,CAAA,SAAA,CAAA,qBAA+B;;;;;QAC3B,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;;;QAzIL,EAAA,IAAA,EAACJ,wBAAU,EAAX;;;;QAnBA,EAAA,IAAA,EAAgCD,oBAAM,GAAtC;QACA,EAAA,IAAA,EAAQD,8BAAQ,GAAhB;;IATA,OAAA,gBAAA,CAAA;;;;;;;;;AAyKA,SAAA,kCAAA,CACI,gBAAkC,EAAE,MAAc,EAAE,QAAkB,EAD1E;IAEE,OAAO,gBAAgB,IAAI,IAAI,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;CACnE;;;;AAGD,IAAa,0BAA0B,GAAG;;IAExC,OAAO,EAAE,gBAAgB;IACzB,IAAI,EAAE,CAAC,CAAC,IAAIF,sBAAQ,EAAE,EAAE,IAAIC,sBAAQ,EAAE,EAAE,gBAAgB,CAAC,EAAEE,oBAAM,EAAED,8BAAQ,CAAC;IAC5E,UAAU,EAAE,kCAAkC;CAC/C,CAAC;;;;;;;;;;;;;ID1JA,SAAF,aAAA,CAAsB,WAAuB,EACvB,OADtB,EAEsB,OAFtB,EAAA;QAAE,IAAF,KAAA,GAAA,IAAA,CAEyC;QAFnB,IAAtB,CAAA,WAAiC,GAAX,WAAW,CAAY;QACvB,IAAtB,CAAA,OAA6B,GAAP,OAAO,CAA7B;QACsB,IAAtB,CAAA,OAA6B,GAAP,OAAO,CAA7B;QALA,IAAA,CAAA,gBAAA,GAA6C,IAAIS,oBAAO,EAAE,CAA1D;QACA,IAAA,CAAA,eAAA,GAA4B,UAAC,KAAY,EAAzC,EAA8C,OAAA,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAA/E,EAA+E,CAA/E;KAIyC;;;;IAEvC,aAAF,CAAA,SAAA,CAAA,QAAU;;;IAAR,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAMG;QALC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAnC;YACM,KAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAI,CAAC,eAAe,CAAC,CAAC;SACrF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KAC7B,CAAH;;;;IAEE,aAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SACxF;KACF,CAAH;;;;;;;;IAKE,aAAF,CAAA,SAAA,CAAA,eAAiB;;;;IAAf,YAAF;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;KAC7C,CAAH;;;;IAEE,aAAF,CAAA,SAAA,CAAA,aAAe;;;IAAb,YAAF;QACI,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB,CAAH;;QApCA,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,mCAAmC;iBAC9C,EAAD,EAAA;;;;QAbA,EAAA,IAAA,EAAmBD,wBAAU,GAA7B;QAGA,EAAA,IAAA,EAAQ,gBAAgB,GAAxB;QAHA,EAAA,IAAA,EAAkDN,oBAAM,GAAxD;;IARA,OAAA,aAAA,CAAA;CAsBA,EAAA,CAAA,CAAA;;;;;;;;;;ADJA,IAAa,mBAAmB,GAAG,EAAE,CAAC;;;;;;IAiBpC,SAAF,aAAA,CAAc,QAAkB,EAAE,MAAc,EAAhD;QAAE,IAAF,KAAA,GAAA,IAAA,CAMG;QALC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,iBAAiB,CAAC,YAAjE;YACM,OAAOI,2BAAK,CAAQC,mCAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAEA,mCAAS,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC,CAAC;SAC1F,CAAC,GAAGF,qBAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,YAApD,EAA0D,OAAA,KAAI,CAAC,mBAAmB,EAAE,CAApF,EAAoF,CAAC,CAAC;KACnF;;;;IAED,aAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;KACrC,CAAH;;;;;;IAGE,aAAF,CAAA,SAAA,CAAA,eAAiB;;;;IAAf,YAAF;QACI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5B;QAED,OAAO,EAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAC,CAAC;KAC7E,CAAH;;;;;;IAGE,aAAF,CAAA,SAAA,CAAA,eAAiB;;;;IAAf,YAAF;;;;;;;;;;QAUI,qBAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACxD,IAAJ,EAAA,GAAA,IAAA,CAAA,eAAA,EAAA,EAAW,KAAX,GAAA,EAAA,CAAA,KAAgB,EAAE,MAAlB,GAAA,EAAA,CAAA,MAAwB,CAA2B;QAE/C,OAAO;YACL,GAAG,EAAE,cAAc,CAAC,GAAG;YACvB,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,MAAM,EAAE,cAAc,CAAC,GAAG,GAAG,MAAM;YACnC,KAAK,EAAE,cAAc,CAAC,IAAI,GAAG,KAAK;YAClC,MAAM,EAAZ,MAAY;YACN,KAAK,EAAX,KAAW;SACN,CAAC;KACH,CAAH;;;;;;IAGE,aAAF,CAAA,SAAA,CAAA,yBAA2B;;;;IAAzB,YAAF;;;;;;;QAOI,qBAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;QAEtE,qBAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,OAAO;YAC7D,QAAQ,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,CAAC;QAErD,qBAAM,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,OAAO;YAC/D,QAAQ,CAAC,eAAe,CAAC,UAAU,IAAI,CAAC,CAAC;QAEvD,OAAO,EAAC,GAAG,EAAf,GAAe,EAAE,IAAI,EAArB,IAAqB,EAAC,CAAC;KACpB,CAAH;;;;;;;;;;IAME,aAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,YAA0C,EAAnD;QAAS,IAAT,YAAA,KAAA,KAAA,CAAA,EAAS,EAAA,YAAT,GAAA,mBAAmD,CAAnD,EAAA;QACI,OAAO,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAACD,kCAAS,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;KACrF,CAAH;;;;;IAGU,aAAV,CAAA,SAAA,CAAA,mBAA6B;;;;;QACzB,IAAI,CAAC,aAAa,GAAG,EAAC,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,WAAW,EAAC,CAAC;;;QArFhF,EAAA,IAAA,EAACD,wBAAU,EAAX;;;;QAfA,EAAA,IAAA,EAAQF,8BAAQ,GAAhB;QADA,EAAA,IAAA,EAAwCC,oBAAM,GAA9C;;IARA,OAAA,aAAA,CAAA;;;;;;;;;AAkHA,SAAA,+BAAA,CAAgD,WAA0B,EAC1B,QAAkB,EAClB,MAAc,EAF9D;IAGE,OAAO,WAAW,IAAI,IAAI,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;CAC3D;;;;AAGD,IAAa,uBAAuB,GAAG;;IAErC,OAAO,EAAE,aAAa;IACtB,IAAI,EAAE,CAAC,CAAC,IAAIH,sBAAQ,EAAE,EAAE,IAAIC,sBAAQ,EAAE,EAAE,aAAa,CAAC,EAAEC,8BAAQ,EAAEC,oBAAM,CAAC;IACzE,UAAU,EAAE,+BAA+B;CAC5C,CAAC;;;;;;;ADtHF,IAAA,oBAAA,kBAAA,YAAA;;;;QAKA,EAAA,IAAA,EAACJ,sBAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAACD,oCAAc,CAAC;oBACzB,OAAO,EAAE,CAAC,aAAa,CAAC;oBACxB,YAAY,EAAE,CAAC,aAAa,CAAC;oBAC7B,SAAS,EAAE,CAAC,0BAA0B,CAAC;iBACxC,EAAD,EAAA;;;;IAlBA,OAAA,oBAAA,CAAA;CAmBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;"}