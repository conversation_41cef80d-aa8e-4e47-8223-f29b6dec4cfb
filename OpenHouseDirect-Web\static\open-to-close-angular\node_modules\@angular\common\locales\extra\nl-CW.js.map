{"version": 3, "file": "nl-CW.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/nl-CW.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;QACtE,AADuE;KAExE;IACD;QACE,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;QACtD,AADuD;KAExD;IACD,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAC1F,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['middernacht', '‘s ochtends', '‘s middags', '‘s avonds', '‘s nachts'],\n    ,\n  ],\n  [\n    ['middernacht', 'ochtend', 'middag', 'avond', 'nacht'],\n    ,\n  ],\n  ['00:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'], ['00:00', '06:00']]\n];\n"]}