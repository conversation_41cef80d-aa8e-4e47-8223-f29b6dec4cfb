{"_from": "fill-range@^4.0.0", "_id": "fill-range@4.0.0", "_inBundle": false, "_integrity": "sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ==", "_location": "/watchpack-chokidar2/fill-range", "_phantomChildren": {"is-extendable": "0.1.1"}, "_requested": {"type": "range", "registry": true, "raw": "fill-range@^4.0.0", "name": "fill-range", "escapedName": "fill-range", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/watchpack-chokidar2/braces"], "_resolved": "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz", "_shasum": "d544811d428f98eb06a63dc402d2403c328c38f7", "_spec": "fill-range@^4.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\watchpack-chokidar2\\node_modules\\braces", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/fill-range/issues"}, "bundleDependencies": false, "contributors": [{"email": "<EMAIL>", "url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "edo.rivai.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "paulmillr.com"}], "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "deprecated": false, "description": "Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`", "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^1.0.0", "gulp-format-md": "^0.1.12", "minimist": "^1.2.0", "mocha": "^3.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/fill-range", "keywords": ["alpha", "alphabetical", "array", "bash", "brace", "expand", "expansion", "fill", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sh"], "license": "MIT", "main": "index.js", "name": "fill-range", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/fill-range.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["braces", "expand-range", "micromatch", "to-regex-range"]}, "toc": true, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "4.0.0"}