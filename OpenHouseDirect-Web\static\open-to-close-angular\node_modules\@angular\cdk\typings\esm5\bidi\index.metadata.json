{"__symbolic": "module", "version": 4, "metadata": {"Directionality": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 35, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 43, "character": 15}}, {"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 43, "character": 27}, "arguments": [{"__symbolic": "reference", "name": "DIR_DOCUMENT"}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}]}}, "DIR_DOCUMENT": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken", "line": 29, "character": 32}, "arguments": ["cdk-dir-doc"]}, "Direction": {"__symbolic": "interface"}, "Dir": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 25, "character": 1}, "arguments": [{"selector": "[dir]", "providers": [{"provide": {"__symbolic": "reference", "name": "Directionality"}, "useExisting": {"__symbolic": "reference", "name": "<PERSON><PERSON>"}}], "host": {"[dir]": "dir"}, "exportAs": "dir"}]}], "members": {"change": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 38, "character": 3}, "arguments": ["<PERSON><PERSON><PERSON><PERSON>"]}]}], "dir": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 41, "character": 3}}]}], "ngAfterContentInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}]}}, "BidiModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 14, "character": 1}, "arguments": [{"exports": [{"__symbolic": "reference", "name": "<PERSON><PERSON>"}], "declarations": [{"__symbolic": "reference", "name": "<PERSON><PERSON>"}], "providers": [{"provide": {"__symbolic": "reference", "name": "DIR_DOCUMENT"}, "useExisting": {"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 18, "character": 41}}, {"__symbolic": "reference", "name": "Directionality"}]}]}], "members": {}}}, "origins": {"Directionality": "./directionality", "DIR_DOCUMENT": "./directionality", "Direction": "./directionality", "Dir": "./dir", "BidiModule": "./bidi-module"}, "importAs": "@angular/cdk/bidi"}