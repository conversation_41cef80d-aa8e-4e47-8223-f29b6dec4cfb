{"_from": "object-visit@^1.0.0", "_id": "object-visit@1.0.1", "_inBundle": false, "_integrity": "sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA==", "_location": "/object-visit", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object-visit@^1.0.0", "name": "object-visit", "escapedName": "object-visit", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/collection-visit", "/map-visit"], "_resolved": "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz", "_shasum": "f79c4493af0c5377b59fe39d395e41042dd045bb", "_spec": "object-visit@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\collection-visit", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/object-visit/issues"}, "bundleDependencies": false, "dependencies": {"isobject": "^3.0.0"}, "deprecated": false, "description": "Call a specified method on each value in the given object.", "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "mocha": "^3.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/object-visit", "keywords": ["context", "function", "helper", "key", "method", "object", "value", "visit", "visitor"], "license": "MIT", "main": "index.js", "name": "object-visit", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/object-visit.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["base-methods", "collection-visit", "define-property", "map-visit"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "1.0.1"}