{"version": 3, "file": "kk.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/kk.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,OAAO;YACpD,OAAO;SACR;QACD;YACE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB;YAChD,OAAO,EAAE,OAAO;SACjB;KACF;IACD;QACE;YACE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK;SAC5D;QACD,AADE;KAEH;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'түнгі', 'түскі', 'таңғы', 'түстен кейінгі', 'кешкі',\n      'түнгі'\n    ],\n    [\n      'түн жарымы', 'түскі', 'таңғы', 'түстен кейінгі',\n      'кешкі', 'түнгі'\n    ],\n  ],\n  [\n    [\n      'түн жарымы', 'талтүс', 'таң', 'түстен кейін', 'кеш', 'түн'\n    ],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '21:00'],\n    ['21:00', '06:00']\n  ]\n];\n"]}