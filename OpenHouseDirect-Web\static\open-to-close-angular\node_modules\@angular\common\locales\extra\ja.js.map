{"version": 3, "file": "ja.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ja.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;QACxC,AADyC;KAE1C;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['真夜中', '正午', '朝', '昼', '夕方', '夜', '夜中'],\n    ,\n  ],\n  ,\n  [\n    '00:00', '12:00', ['04:00', '12:00'], ['12:00', '16:00'], ['16:00', '19:00'],\n    ['19:00', '23:00'], ['23:00', '04:00']\n  ]\n];\n"]}