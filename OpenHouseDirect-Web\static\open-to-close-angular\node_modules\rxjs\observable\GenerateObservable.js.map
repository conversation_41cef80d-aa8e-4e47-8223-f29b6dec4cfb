{"version": 3, "file": "GenerateObservable.js", "sourceRoot": "", "sources": ["../../src/observable/GenerateObservable.ts"], "names": [], "mappings": ";;;;;;AAEA,2BAA2B,eAAgB,CAAC,CAAA;AAG5C,4BAA4B,qBAAqB,CAAC,CAAA;AAElD,IAAM,YAAY,GAAG,UAAI,KAAQ,IAAK,OAAA,KAAK,EAAL,CAAK,CAAC;AA4C5C;;;;GAIG;AACH;IAA8C,sCAAa;IACzD,4BAAoB,YAAe,EACf,SAA2B,EAC3B,OAAuB,EACvB,cAAgC,EAChC,SAAsB;QACtC,iBAAO,CAAC;QALQ,iBAAY,GAAZ,YAAY,CAAG;QACf,cAAS,GAAT,SAAS,CAAkB;QAC3B,YAAO,GAAP,OAAO,CAAgB;QACvB,mBAAc,GAAd,cAAc,CAAkB;QAChC,cAAS,GAAT,SAAS,CAAa;IAE1C,CAAC;IA4GM,yBAAM,GAAb,UAAoB,qBAAgD,EAChD,SAA4B,EAC5B,OAAwB,EACxB,0BAA4D,EAC5D,SAAsB;QACxC,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,kBAAkB,CACH,qBAAsB,CAAC,YAAY,EACnC,qBAAsB,CAAC,SAAS,EAChC,qBAAsB,CAAC,OAAO,EAC9B,qBAAsB,CAAC,cAAc,IAAI,YAAgC,EACzE,qBAAsB,CAAC,SAAS,CAAC,CAAC;QAC9D,CAAC;QAED,EAAE,CAAC,CAAC,0BAA0B,KAAK,SAAS,IAAI,yBAAW,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;YACxF,MAAM,CAAC,IAAI,kBAAkB,CACxB,qBAAqB,EACxB,SAAS,EACT,OAAO,EACP,YAAgC,EACpB,0BAA0B,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,CAAC,IAAI,kBAAkB,CACxB,qBAAqB,EACxB,SAAS,EACT,OAAO,EACW,0BAA0B,EAChC,SAAS,CAAC,CAAC;IAC3B,CAAC;IAED,oCAAoC,CAAC,uCAAU,GAAV,UAAW,UAA2B;QACzE,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAC9B,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAuB,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACnF,sBAAU;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,YAAK,EAAE,CAAC,CAAC;QACb,CAAC;QACD,IAAA,SAAmD,EAA3C,wBAAS,EAAE,kCAAc,EAAE,oBAAO,CAAU;QACpD,GAAG,CAAC;YACF,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBACd,IAAI,eAAe,SAAS,CAAC;gBAC7B,IAAI,CAAC;oBACH,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;gBACrC,CAAE;gBAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACb,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACtB,MAAM,CAAC;gBACT,CAAC;gBACD,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;oBACrB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACtB,KAAK,CAAC;gBACR,CAAC;YACH,CAAC;YACD,IAAI,KAAK,SAAG,CAAC;YACb,IAAI,CAAC;gBACH,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;YAChC,CAAE;YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACb,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtB,MAAM,CAAC;YACT,CAAC;YACD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtB,KAAK,CAAC;YACR,CAAC;YACD,IAAI,CAAC;gBACH,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YACzB,CAAE;YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACb,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtB,MAAM,CAAC;YACT,CAAC;QACH,CAAC,QAAQ,IAAI,EAAE;IACjB,CAAC;IAEc,2BAAQ,GAAvB,UAA8B,KAA2B;QAC/C,iCAAU,EAAE,2BAAS,CAAW;QACxC,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QACD,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC;gBACH,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAE;YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACb,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtB,MAAM,CAAC;YACT,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QAC3B,CAAC;QACD,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,IAAI,eAAe,SAAS,CAAC;YAC7B,IAAI,CAAC;gBACH,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAE;YAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACb,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtB,MAAM,CAAC;YACT,CAAC;YACD,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;gBACrB,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACtB,MAAM,CAAC;YACT,CAAC;YACD,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtB,MAAM,CAAC;YACT,CAAC;QACH,CAAC;QACD,IAAI,KAAQ,CAAC;QACb,IAAI,CAAC;YACH,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QACD,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QACD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QACD,MAAM,CAAqC,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;IACH,yBAAC;AAAD,CAAC,AA9OD,CAA8C,uBAAU,GA8OvD;AA9OY,0BAAkB,qBA8O9B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\nimport { Action } from '../scheduler/Action';\nimport { Observable } from '../Observable' ;\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { isScheduler } from '../util/isScheduler';\n\nconst selfSelector = <T>(value: T) => value;\n\nexport type ConditionFunc<S> = (state: S) => boolean;\nexport type IterateFunc<S> = (state: S) => S;\nexport type ResultFunc<S, T> = (state: S) => T;\n\ninterface SchedulerState<T, S> {\n  needIterate?: boolean;\n  state: S;\n  subscriber: Subscriber<T>;\n  condition?: ConditionFunc<S>;\n  iterate: IterateFunc<S>;\n  resultSelector: ResultFunc<S, T>;\n}\n\nexport interface GenerateBaseOptions<S> {\n  /**\n   * Initial state.\n  */\n  initialState: S;\n  /**\n   * Condition function that accepts state and returns boolean.\n   * When it returns false, the generator stops.\n   * If not specified, a generator never stops.\n  */\n  condition?: ConditionFunc<S>;\n  /**\n   * Iterate function that accepts state and returns new state.\n   */\n  iterate: IterateFunc<S>;\n  /**\n   * IScheduler to use for generation process.\n   * By default, a generator starts immediately.\n  */\n  scheduler?: IScheduler;\n}\n\nexport interface GenerateOptions<T, S> extends GenerateBaseOptions<S> {\n  /**\n   * Result selection function that accepts state and returns a value to emit.\n   */\n  resultSelector: ResultFunc<S, T>;\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class GenerateObservable<T, S> extends Observable<T> {\n  constructor(private initialState: S,\n              private condition: ConditionFunc<S>,\n              private iterate: IterateFunc<S>,\n              private resultSelector: ResultFunc<S, T>,\n              private scheduler?: IScheduler) {\n      super();\n  }\n\n  /**\n   * Generates an observable sequence by running a state-driven loop\n   * producing the sequence's elements, using the specified scheduler\n   * to send out observer messages.\n   *\n   * <img src=\"./img/generate.png\" width=\"100%\">\n   *\n   * @example <caption>Produces sequence of 0, 1, 2, ... 9, then completes.</caption>\n   * var res = Rx.Observable.generate(0, x => x < 10, x => x + 1, x => x);\n   *\n   * @example <caption>Using asap scheduler, produces sequence of 2, 3, 5, then completes.</caption>\n   * var res = Rx.Observable.generate(1, x => x < 5, x => x * 2, x => x + 1, Rx.Scheduler.asap);\n   *\n   * @see {@link from}\n   * @see {@link create}\n   *\n   * @param {S} initialState Initial state.\n   * @param {function (state: S): boolean} condition Condition to terminate generation (upon returning false).\n   * @param {function (state: S): S} iterate Iteration step function.\n   * @param {function (state: S): T} resultSelector Selector function for results produced in the sequence.\n   * @param {Scheduler} [scheduler] A {@link IScheduler} on which to run the generator loop. If not provided, defaults to emit immediately.\n   * @returns {Observable<T>} The generated sequence.\n   */\n  static create<T, S>(initialState: S,\n                      condition: ConditionFunc<S>,\n                      iterate: IterateFunc<S>,\n                      resultSelector: ResultFunc<S, T>,\n                      scheduler?: IScheduler): Observable<T>\n\n  /**\n   * Generates an observable sequence by running a state-driven loop\n   * producing the sequence's elements, using the specified scheduler\n   * to send out observer messages.\n   * The overload uses state as an emitted value.\n   *\n   * <img src=\"./img/generate.png\" width=\"100%\">\n   *\n   * @example <caption>Produces sequence of 0, 1, 2, ... 9, then completes.</caption>\n   * var res = Rx.Observable.generate(0, x => x < 10, x => x + 1);\n   *\n   * @example <caption>Using asap scheduler, produces sequence of 1, 2, 4, then completes.</caption>\n   * var res = Rx.Observable.generate(1, x => x < 5, x => x * 2, Rx.Scheduler.asap);\n   *\n   * @see {@link from}\n   * @see {@link create}\n   *\n   * @param {S} initialState Initial state.\n   * @param {function (state: S): boolean} condition Condition to terminate generation (upon returning false).\n   * @param {function (state: S): S} iterate Iteration step function.\n   * @param {Scheduler} [scheduler] A {@link IScheduler} on which to run the generator loop. If not provided, defaults to emit immediately.\n   * @returns {Observable<S>} The generated sequence.\n   */\n  static create<S>(initialState: S,\n                   condition: ConditionFunc<S>,\n                   iterate: IterateFunc<S>,\n                   scheduler?: IScheduler): Observable<S>\n\n  /**\n   * Generates an observable sequence by running a state-driven loop\n   * producing the sequence's elements, using the specified scheduler\n   * to send out observer messages.\n   * The overload accepts options object that might contain initial state, iterate,\n   * condition and scheduler.\n   *\n   * <img src=\"./img/generate.png\" width=\"100%\">\n   *\n   * @example <caption>Produces sequence of 0, 1, 2, ... 9, then completes.</caption>\n   * var res = Rx.Observable.generate({\n   *   initialState: 0,\n   *   condition: x => x < 10,\n   *   iterate: x => x + 1\n   * });\n   *\n   * @see {@link from}\n   * @see {@link create}\n   *\n   * @param {GenerateBaseOptions<S>} options Object that must contain initialState, iterate and might contain condition and scheduler.\n   * @returns {Observable<S>} The generated sequence.\n   */\n  static create<S>(options: GenerateBaseOptions<S>): Observable<S>\n\n  /**\n   * Generates an observable sequence by running a state-driven loop\n   * producing the sequence's elements, using the specified scheduler\n   * to send out observer messages.\n   * The overload accepts options object that might contain initial state, iterate,\n   * condition, result selector and scheduler.\n   *\n   * <img src=\"./img/generate.png\" width=\"100%\">\n   *\n   * @example <caption>Produces sequence of 0, 1, 2, ... 9, then completes.</caption>\n   * var res = Rx.Observable.generate({\n   *   initialState: 0,\n   *   condition: x => x < 10,\n   *   iterate: x => x + 1,\n   *   resultSelector: x => x\n   * });\n   *\n   * @see {@link from}\n   * @see {@link create}\n   *\n   * @param {GenerateOptions<T, S>} options Object that must contain initialState, iterate, resultSelector and might contain condition and scheduler.\n   * @returns {Observable<T>} The generated sequence.\n   */\n  static create<T, S>(options: GenerateOptions<T, S>): Observable<T>\n\n  static create<T, S>(initialStateOrOptions: S | GenerateOptions<T, S>,\n                      condition?: ConditionFunc<S>,\n                      iterate?: IterateFunc<S>,\n                      resultSelectorOrObservable?: (ResultFunc<S, T>) | IScheduler,\n                      scheduler?: IScheduler): Observable<T> {\n    if (arguments.length == 1) {\n      return new GenerateObservable<T, S>(\n        (<GenerateOptions<T, S>>initialStateOrOptions).initialState,\n        (<GenerateOptions<T, S>>initialStateOrOptions).condition,\n        (<GenerateOptions<T, S>>initialStateOrOptions).iterate,\n        (<GenerateOptions<T, S>>initialStateOrOptions).resultSelector || selfSelector as ResultFunc<S, T>,\n        (<GenerateOptions<T, S>>initialStateOrOptions).scheduler);\n    }\n\n    if (resultSelectorOrObservable === undefined || isScheduler(resultSelectorOrObservable)) {\n      return new GenerateObservable<T, S>(\n        <S>initialStateOrOptions,\n        condition,\n        iterate,\n        selfSelector as ResultFunc<S, T>,\n        <IScheduler>resultSelectorOrObservable);\n    }\n\n    return new GenerateObservable<T, S>(\n      <S>initialStateOrOptions,\n      condition,\n      iterate,\n      <ResultFunc<S, T>>resultSelectorOrObservable,\n      <IScheduler>scheduler);\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<any>): Subscription | Function | void {\n    let state = this.initialState;\n    if (this.scheduler) {\n      return this.scheduler.schedule<SchedulerState<T, S>>(GenerateObservable.dispatch, 0, {\n        subscriber,\n        iterate: this.iterate,\n        condition: this.condition,\n        resultSelector: this.resultSelector,\n        state });\n    }\n    const { condition, resultSelector, iterate } = this;\n    do {\n      if (condition) {\n        let conditionResult: boolean;\n        try {\n          conditionResult = condition(state);\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n        if (!conditionResult) {\n          subscriber.complete();\n          break;\n        }\n      }\n      let value: T;\n      try {\n        value = resultSelector(state);\n      } catch (err) {\n        subscriber.error(err);\n        return;\n      }\n      subscriber.next(value);\n      if (subscriber.closed) {\n        break;\n      }\n      try {\n        state = iterate(state);\n      } catch (err) {\n        subscriber.error(err);\n        return;\n      }\n    } while (true);\n  }\n\n  private static dispatch<T, S>(state: SchedulerState<T, S>): Subscription | void {\n    const { subscriber, condition } = state;\n    if (subscriber.closed) {\n      return;\n    }\n    if (state.needIterate) {\n      try {\n        state.state = state.iterate(state.state);\n      } catch (err) {\n        subscriber.error(err);\n        return;\n      }\n    } else {\n      state.needIterate = true;\n    }\n    if (condition) {\n      let conditionResult: boolean;\n      try {\n        conditionResult = condition(state.state);\n      } catch (err) {\n        subscriber.error(err);\n        return;\n      }\n      if (!conditionResult) {\n        subscriber.complete();\n        return;\n      }\n      if (subscriber.closed) {\n        return;\n      }\n    }\n    let value: T;\n    try {\n      value = state.resultSelector(state.state);\n    } catch (err) {\n      subscriber.error(err);\n      return;\n    }\n    if (subscriber.closed) {\n      return;\n    }\n    subscriber.next(value);\n    if (subscriber.closed) {\n      return;\n    }\n    return (<Action<SchedulerState<T, S>>><any>this).schedule(state);\n  }\n}\n"]}