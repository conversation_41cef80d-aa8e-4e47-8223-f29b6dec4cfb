{"version": 3, "file": "testing.js", "sources": ["../../../../packages/animations/browser/src/render/shared.js", "../../../../packages/animations/browser/src/util.js", "../../../../packages/animations/browser/testing/src/mock_animation_driver.js", "../../../../packages/animations/browser/testing/src/testing.js", "../../../../packages/animations/browser/testing/public_api.js", "../../../../packages/animations/browser/testing/testing.js"], "sourcesContent": ["/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, NoopAnimationPlayer, ɵAnimationGroupPlayer, ɵPRE_STYLE as PRE_STYLE } from '@angular/animations';\n/**\n * @param {?} players\n * @return {?}\n */\nexport function optimizeGroupPlayer(players) {\n    switch (players.length) {\n        case 0:\n            return new NoopAnimationPlayer();\n        case 1:\n            return players[0];\n        default:\n            return new ɵAnimationGroupPlayer(players);\n    }\n}\n/**\n * @param {?} driver\n * @param {?} normalizer\n * @param {?} element\n * @param {?} keyframes\n * @param {?=} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nexport function normalizeKeyframes(driver, normalizer, element, keyframes, preStyles = {}, postStyles = {}) {\n    const /** @type {?} */ errors = [];\n    const /** @type {?} */ normalizedKeyframes = [];\n    let /** @type {?} */ previousOffset = -1;\n    let /** @type {?} */ previousKeyframe = null;\n    keyframes.forEach(kf => {\n        const /** @type {?} */ offset = /** @type {?} */ (kf['offset']);\n        const /** @type {?} */ isSameOffset = offset == previousOffset;\n        const /** @type {?} */ normalizedKeyframe = (isSameOffset && previousKeyframe) || {};\n        Object.keys(kf).forEach(prop => {\n            let /** @type {?} */ normalizedProp = prop;\n            let /** @type {?} */ normalizedValue = kf[prop];\n            if (prop !== 'offset') {\n                normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n                switch (normalizedValue) {\n                    case PRE_STYLE:\n                        normalizedValue = preStyles[prop];\n                        break;\n                    case AUTO_STYLE:\n                        normalizedValue = postStyles[prop];\n                        break;\n                    default:\n                        normalizedValue =\n                            normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n                        break;\n                }\n            }\n            normalizedKeyframe[normalizedProp] = normalizedValue;\n        });\n        if (!isSameOffset) {\n            normalizedKeyframes.push(normalizedKeyframe);\n        }\n        previousKeyframe = normalizedKeyframe;\n        previousOffset = offset;\n    });\n    if (errors.length) {\n        const /** @type {?} */ LINE_START = '\\n - ';\n        throw new Error(`Unable to animate due to the following errors:${LINE_START}${errors.join(LINE_START)}`);\n    }\n    return normalizedKeyframes;\n}\n/**\n * @param {?} player\n * @param {?} eventName\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nexport function listenOnPlayer(player, eventName, event, callback) {\n    switch (eventName) {\n        case 'start':\n            player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player.totalTime)));\n            break;\n        case 'done':\n            player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player.totalTime)));\n            break;\n        case 'destroy':\n            player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)));\n            break;\n    }\n}\n/**\n * @param {?} e\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function copyAnimationEvent(e, phaseName, totalTime) {\n    const /** @type {?} */ event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime);\n    const /** @type {?} */ data = (/** @type {?} */ (e))['_data'];\n    if (data != null) {\n        (/** @type {?} */ (event))['_data'] = data;\n    }\n    return event;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nexport function makeAnimationEvent(element, triggerName, fromState, toState, phaseName = '', totalTime = 0) {\n    return { element, triggerName, fromState, toState, phaseName, totalTime };\n}\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} defaultValue\n * @return {?}\n */\nexport function getOrSetAsInMap(map, key, defaultValue) {\n    let /** @type {?} */ value;\n    if (map instanceof Map) {\n        value = map.get(key);\n        if (!value) {\n            map.set(key, value = defaultValue);\n        }\n    }\n    else {\n        value = map[key];\n        if (!value) {\n            value = map[key] = defaultValue;\n        }\n    }\n    return value;\n}\n/**\n * @param {?} command\n * @return {?}\n */\nexport function parseTimelineCommand(command) {\n    const /** @type {?} */ separatorPos = command.indexOf(':');\n    const /** @type {?} */ id = command.substring(1, separatorPos);\n    const /** @type {?} */ action = command.substr(separatorPos + 1);\n    return [id, action];\n}\nlet /** @type {?} */ _contains = (elm1, elm2) => false;\nconst ɵ0 = _contains;\nlet /** @type {?} */ _matches = (element, selector) => false;\nconst ɵ1 = _matches;\nlet /** @type {?} */ _query = (element, selector, multi) => {\n    return [];\n};\nconst ɵ2 = _query;\nif (typeof Element != 'undefined') {\n    // this is well supported in all browsers\n    _contains = (elm1, elm2) => { return /** @type {?} */ (elm1.contains(elm2)); };\n    if (Element.prototype.matches) {\n        _matches = (element, selector) => element.matches(selector);\n    }\n    else {\n        const /** @type {?} */ proto = /** @type {?} */ (Element.prototype);\n        const /** @type {?} */ fn = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n            proto.oMatchesSelector || proto.webkitMatchesSelector;\n        if (fn) {\n            _matches = (element, selector) => fn.apply(element, [selector]);\n        }\n    }\n    _query = (element, selector, multi) => {\n        let /** @type {?} */ results = [];\n        if (multi) {\n            results.push(...element.querySelectorAll(selector));\n        }\n        else {\n            const /** @type {?} */ elm = element.querySelector(selector);\n            if (elm) {\n                results.push(elm);\n            }\n        }\n        return results;\n    };\n}\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction containsVendorPrefix(prop) {\n    // Webkit is the only real popular vendor prefix nowadays\n    // cc: http://shouldiprefix.com/\n    return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nlet /** @type {?} */ _CACHED_BODY = null;\nlet /** @type {?} */ _IS_WEBKIT = false;\n/**\n * @param {?} prop\n * @return {?}\n */\nexport function validateStyleProperty(prop) {\n    if (!_CACHED_BODY) {\n        _CACHED_BODY = getBodyNode() || {};\n        _IS_WEBKIT = /** @type {?} */ ((_CACHED_BODY)).style ? ('WebkitAppearance' in /** @type {?} */ ((_CACHED_BODY)).style) : false;\n    }\n    let /** @type {?} */ result = true;\n    if (/** @type {?} */ ((_CACHED_BODY)).style && !containsVendorPrefix(prop)) {\n        result = prop in /** @type {?} */ ((_CACHED_BODY)).style;\n        if (!result && _IS_WEBKIT) {\n            const /** @type {?} */ camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n            result = camelProp in /** @type {?} */ ((_CACHED_BODY)).style;\n        }\n    }\n    return result;\n}\n/**\n * @return {?}\n */\nexport function getBodyNode() {\n    if (typeof document != 'undefined') {\n        return document.body;\n    }\n    return null;\n}\nexport const /** @type {?} */ matchesElement = _matches;\nexport const /** @type {?} */ containsElement = _contains;\nexport const /** @type {?} */ invokeQuery = _query;\nexport { ɵ0, ɵ1, ɵ2 };\n//# sourceMappingURL=shared.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { sequence } from '@angular/animations';\nexport const /** @type {?} */ ONE_SECOND = 1000;\nexport const /** @type {?} */ SUBSTITUTION_EXPR_START = '{{';\nexport const /** @type {?} */ SUBSTITUTION_EXPR_END = '}}';\nexport const /** @type {?} */ ENTER_CLASSNAME = 'ng-enter';\nexport const /** @type {?} */ LEAVE_CLASSNAME = 'ng-leave';\nexport const /** @type {?} */ ENTER_SELECTOR = '.ng-enter';\nexport const /** @type {?} */ LEAVE_SELECTOR = '.ng-leave';\nexport const /** @type {?} */ NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const /** @type {?} */ NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const /** @type {?} */ NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const /** @type {?} */ NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nexport function resolveTimingValue(value) {\n    if (typeof value == 'number')\n        return value;\n    const /** @type {?} */ matches = (/** @type {?} */ (value)).match(/^(-?[\\.\\d]+)(m?s)/);\n    if (!matches || matches.length < 2)\n        return 0;\n    return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value, unit) {\n    switch (unit) {\n        case 's':\n            return value * ONE_SECOND;\n        default:\n            // ms or something else\n            return value;\n    }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nexport function resolveTiming(timings, errors, allowNegativeValues) {\n    return timings.hasOwnProperty('duration') ? /** @type {?} */ (timings) :\n        parseTimeExpression(/** @type {?} */ (timings), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n    const /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n    let /** @type {?} */ duration;\n    let /** @type {?} */ delay = 0;\n    let /** @type {?} */ easing = '';\n    if (typeof exp === 'string') {\n        const /** @type {?} */ matches = exp.match(regex);\n        if (matches === null) {\n            errors.push(`The provided timing value \"${exp}\" is invalid.`);\n            return { duration: 0, delay: 0, easing: '' };\n        }\n        duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n        const /** @type {?} */ delayMatch = matches[3];\n        if (delayMatch != null) {\n            delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n        }\n        const /** @type {?} */ easingVal = matches[5];\n        if (easingVal) {\n            easing = easingVal;\n        }\n    }\n    else {\n        duration = /** @type {?} */ (exp);\n    }\n    if (!allowNegativeValues) {\n        let /** @type {?} */ containsErrors = false;\n        let /** @type {?} */ startIndex = errors.length;\n        if (duration < 0) {\n            errors.push(`Duration values below 0 are not allowed for this animation step.`);\n            containsErrors = true;\n        }\n        if (delay < 0) {\n            errors.push(`Delay values below 0 are not allowed for this animation step.`);\n            containsErrors = true;\n        }\n        if (containsErrors) {\n            errors.splice(startIndex, 0, `The provided timing value \"${exp}\" is invalid.`);\n        }\n    }\n    return { duration, delay, easing };\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nexport function copyObj(obj, destination = {}) {\n    Object.keys(obj).forEach(prop => { destination[prop] = obj[prop]; });\n    return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nexport function normalizeStyles(styles) {\n    const /** @type {?} */ normalizedStyles = {};\n    if (Array.isArray(styles)) {\n        styles.forEach(data => copyStyles(data, false, normalizedStyles));\n    }\n    else {\n        copyStyles(styles, false, normalizedStyles);\n    }\n    return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nexport function copyStyles(styles, readPrototype, destination = {}) {\n    if (readPrototype) {\n        // we make use of a for-in loop so that the\n        // prototypically inherited properties are\n        // revealed from the backFill map\n        for (let /** @type {?} */ prop in styles) {\n            destination[prop] = styles[prop];\n        }\n    }\n    else {\n        copyObj(styles, destination);\n    }\n    return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function setStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(prop => {\n            const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = styles[prop];\n        });\n    }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nexport function eraseStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(prop => {\n            const /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = '';\n        });\n    }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nexport function normalizeAnimationEntry(steps) {\n    if (Array.isArray(steps)) {\n        if (steps.length == 1)\n            return steps[0];\n        return sequence(steps);\n    }\n    return /** @type {?} */ (steps);\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nexport function validateStyleParams(value, options, errors) {\n    const /** @type {?} */ params = options.params || {};\n    const /** @type {?} */ matches = extractStyleParams(value);\n    if (matches.length) {\n        matches.forEach(varName => {\n            if (!params.hasOwnProperty(varName)) {\n                errors.push(`Unable to resolve the local animation param ${varName} in the given list of values`);\n            }\n        });\n    }\n}\nconst /** @type {?} */ PARAM_REGEX = new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\n/**\n * @param {?} value\n * @return {?}\n */\nexport function extractStyleParams(value) {\n    let /** @type {?} */ params = [];\n    if (typeof value === 'string') {\n        const /** @type {?} */ val = value.toString();\n        let /** @type {?} */ match;\n        while (match = PARAM_REGEX.exec(val)) {\n            params.push(/** @type {?} */ (match[1]));\n        }\n        PARAM_REGEX.lastIndex = 0;\n    }\n    return params;\n}\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nexport function interpolateParams(value, params, errors) {\n    const /** @type {?} */ original = value.toString();\n    const /** @type {?} */ str = original.replace(PARAM_REGEX, (_, varName) => {\n        let /** @type {?} */ localVal = params[varName];\n        // this means that the value was never overidden by the data passed in by the user\n        if (!params.hasOwnProperty(varName)) {\n            errors.push(`Please provide a value for the animation param ${varName}`);\n            localVal = '';\n        }\n        return localVal.toString();\n    });\n    // we do this to assert that numeric values stay as they are\n    return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nexport function iteratorToArray(iterator) {\n    const /** @type {?} */ arr = [];\n    let /** @type {?} */ item = iterator.next();\n    while (!item.done) {\n        arr.push(item.value);\n        item = iterator.next();\n    }\n    return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\nexport function mergeAnimationOptions(source, destination) {\n    if (source.params) {\n        const /** @type {?} */ p0 = source.params;\n        if (!destination.params) {\n            destination.params = {};\n        }\n        const /** @type {?} */ p1 = destination.params;\n        Object.keys(p0).forEach(param => {\n            if (!p1.hasOwnProperty(param)) {\n                p1[param] = p0[param];\n            }\n        });\n    }\n    return destination;\n}\nconst /** @type {?} */ DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nexport function dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nexport function allowPreviousPlayerStylesMerge(duration, delay) {\n    return duration === 0 || delay === 0;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nexport function visitDslNode(visitor, node, context) {\n    switch (node.type) {\n        case 7 /* Trigger */:\n            return visitor.visitTrigger(node, context);\n        case 0 /* State */:\n            return visitor.visitState(node, context);\n        case 1 /* Transition */:\n            return visitor.visitTransition(node, context);\n        case 2 /* Sequence */:\n            return visitor.visitSequence(node, context);\n        case 3 /* Group */:\n            return visitor.visitGroup(node, context);\n        case 4 /* Animate */:\n            return visitor.visitAnimate(node, context);\n        case 5 /* Keyframes */:\n            return visitor.visitKeyframes(node, context);\n        case 6 /* Style */:\n            return visitor.visitStyle(node, context);\n        case 8 /* Reference */:\n            return visitor.visitReference(node, context);\n        case 9 /* AnimateChild */:\n            return visitor.visitAnimateChild(node, context);\n        case 10 /* AnimateRef */:\n            return visitor.visitAnimateRef(node, context);\n        case 11 /* Query */:\n            return visitor.visitQuery(node, context);\n        case 12 /* Stagger */:\n            return visitor.visitStagger(node, context);\n        default:\n            throw new Error(`Unable to resolve animation metadata node #${node.type}`);\n    }\n}\n//# sourceMappingURL=util.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nimport { AUTO_STYLE, NoopAnimationPlayer } from '@angular/animations';\nimport { containsElement, invokeQuery, matchesElement, validateStyleProperty } from '../../src/render/shared';\nimport { allowPreviousPlayerStylesMerge } from '../../src/util';\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class MockAnimationDriver {\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    validateStyleProperty(prop) { return validateStyleProperty(prop); }\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    matchesElement(element, selector) {\n        return matchesElement(element, selector);\n    }\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    containsElement(elm1, elm2) { return containsElement(elm1, elm2); }\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    query(element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    }\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    computeStyle(element, prop, defaultValue) {\n        return defaultValue || '';\n    }\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n        const /** @type {?} */ player = new MockAnimationPlayer(element, keyframes, duration, delay, easing, previousPlayers);\n        MockAnimationDriver.log.push(/** @type {?} */ (player));\n        return player;\n    }\n}\nMockAnimationDriver.log = [];\nfunction MockAnimationDriver_tsickle_Closure_declarations() {\n    /** @type {?} */\n    MockAnimationDriver.log;\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nexport class MockAnimationPlayer extends NoopAnimationPlayer {\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?} previousPlayers\n     */\n    constructor(element, keyframes, duration, delay, easing, previousPlayers) {\n        super();\n        this.element = element;\n        this.keyframes = keyframes;\n        this.duration = duration;\n        this.delay = delay;\n        this.easing = easing;\n        this.previousPlayers = previousPlayers;\n        this.__finished = false;\n        this.__started = false;\n        this.previousStyles = {};\n        this._onInitFns = [];\n        this.currentSnapshot = {};\n        if (allowPreviousPlayerStylesMerge(duration, delay)) {\n            previousPlayers.forEach(player => {\n                if (player instanceof MockAnimationPlayer) {\n                    const /** @type {?} */ styles = player.currentSnapshot;\n                    Object.keys(styles).forEach(prop => this.previousStyles[prop] = styles[prop]);\n                }\n            });\n        }\n        this.totalTime = delay + duration;\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    onInit(fn) { this._onInitFns.push(fn); }\n    /**\n     * @return {?}\n     */\n    init() {\n        super.init();\n        this._onInitFns.forEach(fn => fn());\n        this._onInitFns = [];\n    }\n    /**\n     * @return {?}\n     */\n    finish() {\n        super.finish();\n        this.__finished = true;\n    }\n    /**\n     * @return {?}\n     */\n    destroy() {\n        super.destroy();\n        this.__finished = true;\n    }\n    /**\n     * @return {?}\n     */\n    triggerMicrotask() { }\n    /**\n     * @return {?}\n     */\n    play() {\n        super.play();\n        this.__started = true;\n    }\n    /**\n     * @return {?}\n     */\n    hasStarted() { return this.__started; }\n    /**\n     * @return {?}\n     */\n    beforeDestroy() {\n        const /** @type {?} */ captures = {};\n        Object.keys(this.previousStyles).forEach(prop => {\n            captures[prop] = this.previousStyles[prop];\n        });\n        if (this.hasStarted()) {\n            // when assembling the captured styles, it's important that\n            // we build the keyframe styles in the following order:\n            // {other styles within keyframes, ... previousStyles }\n            this.keyframes.forEach(kf => {\n                Object.keys(kf).forEach(prop => {\n                    if (prop != 'offset') {\n                        captures[prop] = this.__finished ? kf[prop] : AUTO_STYLE;\n                    }\n                });\n            });\n        }\n        this.currentSnapshot = captures;\n    }\n}\nfunction MockAnimationPlayer_tsickle_Closure_declarations() {\n    /** @type {?} */\n    MockAnimationPlayer.prototype.__finished;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.__started;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.previousStyles;\n    /** @type {?} */\n    MockAnimationPlayer.prototype._onInitFns;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.currentSnapshot;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.element;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.keyframes;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.duration;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.delay;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.easing;\n    /** @type {?} */\n    MockAnimationPlayer.prototype.previousPlayers;\n}\n//# sourceMappingURL=mock_animation_driver.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nexport { MockAnimationDriver, MockAnimationPlayer } from './mock_animation_driver';\n//# sourceMappingURL=testing.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport { MockAnimationDriver, MockAnimationPlayer } from './src/testing';\n//# sourceMappingURL=public_api.js.map", "/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Generated bundle index. Do not edit.\n */\nexport { MockAnimationDriver, MockAnimationPlayer } from './public_api';\n//# sourceMappingURL=testing.js.map"], "names": [], "mappings": ";;;;;;;AAAA;;;;AAIA,AACA;;;;AAIA,AASC;;;;;;;;;;AAUD,AAwCC;;;;;;;;AAQD,AAYC;;;;;;;AAOD,AAOC;;;;;;;;;;AAUD,AAEC;;;;;;;AAOD,AAeC;;;;;AAKD,AAKC;AACD,IAAqB,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,KAAK,CAAC;AACvD,AACA,IAAqB,QAAQ,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK,KAAK,CAAC;AAC7D,AACA,IAAqB,MAAM,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,KAAK;IACxD,OAAO,EAAE,CAAC;CACb,CAAC;AACF,AACA,IAAI,OAAO,OAAO,IAAI,WAAW,EAAE;;IAE/B,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,EAAE,yBAAyB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAC/E,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC3B,QAAQ,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC/D;SACI;QACD,uBAAuB,KAAK,qBAAqB,OAAO,CAAC,SAAS,CAAC,CAAC;QACpE,uBAAuB,EAAE,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,iBAAiB;YACpG,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,qBAAqB,CAAC;QAC1D,IAAI,EAAE,EAAE;YACJ,QAAQ,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;SACnE;KACJ;IACD,MAAM,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,KAAK;QACnC,qBAAqB,OAAO,GAAG,EAAE,CAAC;QAClC,IAAI,KAAK,EAAE;YACP,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACvD;aACI;YACD,uBAAuB,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC7D,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrB;SACJ;QACD,OAAO,OAAO,CAAC;KAClB,CAAC;CACL;;;;;AAKD,SAAS,oBAAoB,CAAC,IAAI,EAAE;;;IAGhC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC;CAC1C;AACD,IAAqB,YAAY,GAAG,IAAI,CAAC;AACzC,IAAqB,UAAU,GAAG,KAAK,CAAC;;;;;AAKxC,AAAO,SAAS,qBAAqB,CAAC,IAAI,EAAE;IACxC,IAAI,CAAC,YAAY,EAAE;QACf,YAAY,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC;QACnC,UAAU,oBAAoB,EAAE,YAAY,GAAG,KAAK,IAAI,kBAAkB,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,KAAK,CAAC;KAClI;IACD,qBAAqB,MAAM,GAAG,IAAI,CAAC;IACnC,qBAAqB,EAAE,YAAY,GAAG,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;QACxE,MAAM,GAAG,IAAI,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;QACzD,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;YACvB,uBAAuB,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5F,MAAM,GAAG,SAAS,qBAAqB,EAAE,YAAY,GAAG,KAAK,CAAC;SACjE;KACJ;IACD,OAAO,MAAM,CAAC;CACjB;;;;AAID,AAAO,SAAS,WAAW,GAAG;IAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;QAChC,OAAO,QAAQ,CAAC,IAAI,CAAC;KACxB;IACD,OAAO,IAAI,CAAC;CACf;AACD,AAAO,MAAuB,cAAc,GAAG,QAAQ,CAAC;AACxD,AAAO,MAAuB,eAAe,GAAG,SAAS,CAAC;AAC1D,AAAO,MAAuB,WAAW,GAAG,MAAM;;AChOlD;;;;AAIA,AACgD;AAChD,AAA6D;AAC7D,AAA2D;AAC3D,AAA2D;AAC3D,AAA2D;AAC3D,AAA2D;AAC3D,AAA2D;AAC3D,AAAkE;AAClE,AAAkE;AAClE,AAAsE;AACtE,AAAsE;;;;;AAKtE,AAOC;AACD,AAcA;;;;;;AAMA,AAGC;AACD,AA+CA;;;;;AAKA,AAGC;;;;;AAKD,AASC;;;;;;;AAOD,AAaC;;;;;;AAMD,AAOC;;;;;;AAMD,AAOC;;;;;AAKD,AAOC;;;;;;;AAOD,AAUC;AACD,AACA;;;;AAIA,AAWC;;;;;;;AAOD,AAaC;;;;;AAKD,AAQC;;;;;;AAMD,AAcC;AACD,AACA;;;;AAIA,AAEC;;;;;;AAMD,AAAO,SAAS,8BAA8B,CAAC,QAAQ,EAAE,KAAK,EAAE;IAC5D,OAAO,QAAQ,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;CACxC;;;;;;GAME;;AChSH;;;;AAIA,AAGA;;;AAGA,AAAO,MAAM,mBAAmB,CAAC;;;;;IAK7B,qBAAqB,CAAC,IAAI,EAAE,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;;;;;;IAMnE,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE;QAC9B,OAAO,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC5C;;;;;;IAMD,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;;;;;;;IAOnE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC5B,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAChD;;;;;;;IAOD,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE;QACtC,OAAO,YAAY,IAAI,EAAE,CAAC;KAC7B;;;;;;;;;;IAUD,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,GAAG,EAAE,EAAE;QACvE,uBAAuB,MAAM,GAAG,IAAI,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;QACtH,mBAAmB,CAAC,GAAG,CAAC,IAAI,mBAAmB,MAAM,EAAE,CAAC;QACxD,OAAO,MAAM,CAAC;KACjB;CACJ;AACD,mBAAmB,CAAC,GAAG,GAAG,EAAE,CAAC;AAC7B,AAIA;;;AAGA,AAAO,MAAM,mBAAmB,SAAS,mBAAmB,CAAC;;;;;;;;;IASzD,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE;QACtE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;YACjD,eAAe,CAAC,OAAO,CAAC,MAAM,IAAI;gBAC9B,IAAI,MAAM,YAAY,mBAAmB,EAAE;oBACvC,uBAAuB,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;oBACvD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;iBACjF;aACJ,CAAC,CAAC;SACN;QACD,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,QAAQ,CAAC;KACrC;;;;;IAKD,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;;;;IAIxC,IAAI,GAAG;QACH,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACxB;;;;IAID,MAAM,GAAG;QACL,KAAK,CAAC,MAAM,EAAE,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B;;;;IAID,OAAO,GAAG;QACN,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B;;;;IAID,gBAAgB,GAAG,GAAG;;;;IAItB,IAAI,GAAG;QACH,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB;;;;IAID,UAAU,GAAG,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;;;;IAIvC,aAAa,GAAG;QACZ,uBAAuB,QAAQ,GAAG,EAAE,CAAC;QACrC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;YAC7C,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC9C,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;YAInB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI;gBACzB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;oBAC5B,IAAI,IAAI,IAAI,QAAQ,EAAE;wBAClB,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;qBAC5D;iBACJ,CAAC,CAAC;aACN,CAAC,CAAC;SACN;QACD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;KACnC;CACJ;;ACvKD;;;GAGG;;ACHH;;;;;;;;;;;;;;;GAeG;;ACfH;;;;;;GAMG;;;;"}