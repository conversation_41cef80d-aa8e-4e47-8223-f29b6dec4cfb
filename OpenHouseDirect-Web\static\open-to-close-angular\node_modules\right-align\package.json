{"_from": "right-align@^0.1.1", "_id": "right-align@0.1.3", "_inBundle": false, "_integrity": "sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg==", "_location": "/right-align", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "right-align@^0.1.1", "name": "right-align", "escapedName": "right-align", "rawSpec": "^0.1.1", "saveSpec": null, "fetchSpec": "^0.1.1"}, "_requiredBy": ["/webpack/cliui"], "_resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "_shasum": "61339b722fe6a3515689210d24e14c96148613ef", "_spec": "right-align@^0.1.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\webpack\\node_modules\\cliui", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/right-align/issues"}, "bundleDependencies": false, "dependencies": {"align-text": "^0.1.1"}, "deprecated": false, "description": "Right-align the text in a string.", "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/right-align", "keywords": ["align", "align-center", "center", "center-align", "right", "right-align", "text", "typography"], "license": "MIT", "main": "index.js", "name": "right-align", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/right-align.git"}, "scripts": {"test": "mocha"}, "version": "0.1.3"}