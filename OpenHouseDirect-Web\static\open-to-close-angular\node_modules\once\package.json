{"_from": "once@^1.3.0", "_id": "once@1.4.0", "_inBundle": false, "_integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "_location": "/once", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "once@^1.3.0", "name": "once", "escapedName": "once", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/end-of-stream", "/glob", "/globule/glob", "/inflight", "/istanbul-api", "/pump"], "_resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "_shasum": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "_spec": "once@^1.3.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/once/issues"}, "bundleDependencies": false, "dependencies": {"wrappy": "1"}, "deprecated": false, "description": "Run a function exactly one time", "devDependencies": {"tap": "^7.0.1"}, "directories": {"test": "test"}, "files": ["once.js"], "homepage": "https://github.com/isaacs/once#readme", "keywords": ["once", "function", "one", "single"], "license": "ISC", "main": "once.js", "name": "once", "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "scripts": {"test": "tap test/*.js"}, "version": "1.4.0"}