{"version": 3, "file": "he.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/he.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;YACpD,YAAY;SACb;QACD;YACE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM;YACtD,YAAY;SACb;QACD;YACE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO;YAC1D,YAAY;SACb;KACF;IACD;QACE;YACE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM;YAChD,YAAY;SACb;QACD,AADE;QAEF;YACE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM;YACtD,YAAY;SACb;KACF;IACD;QACE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QACvF,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'חצות', 'בבוקר', 'בצהריים', 'אחה״צ', 'בערב', 'בלילה',\n      'ל<PERSON>נו<PERSON> בוקר'\n    ],\n    [\n      'חצות', 'בוקר', 'צהריים', 'אחר הצהריים', 'ערב', 'לילה',\n      'לפנות בוקר'\n    ],\n    [\n      'חצות', 'בבוקר', 'בצהריים', 'אחר הצהריים', 'בערב', 'בלילה',\n      'ל<PERSON><PERSON>ו<PERSON> בוקר'\n    ]\n  ],\n  [\n    [\n      'חצות', 'בוקר', 'צהריים', 'אחה״צ', 'ערב', 'לילה',\n      'לפנות בוקר'\n    ],\n    ,\n    [\n      'חצות', 'בוקר', 'צהריים', 'אחר הצהריים', 'ערב', 'לילה',\n      'לפנות בוקר'\n    ]\n  ],\n  [\n    '00:00', ['06:00', '12:00'], ['12:00', '16:00'], ['16:00', '18:00'], ['18:00', '22:00'],\n    ['22:00', '03:00'], ['03:00', '06:00']\n  ]\n];\n"]}