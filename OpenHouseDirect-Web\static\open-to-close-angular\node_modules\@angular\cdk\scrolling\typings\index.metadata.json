{"__symbolic": "module", "version": 4, "metadata": {"DEFAULT_SCROLL_TIME": 20, "ScrollDispatcher": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 27, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 29, "character": 31}, {"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 29, "character": 58}]}], "register": [{"__symbolic": "method"}], "deregister": [{"__symbolic": "method"}], "scrolled": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "ancestorScrolled": [{"__symbolic": "method"}], "getAncestorScrollContainers": [{"__symbolic": "method"}], "_scrollableContainsElement": [{"__symbolic": "method"}], "_addGlobalListener": [{"__symbolic": "method"}], "_removeGlobalListener": [{"__symbolic": "method"}]}}, "SCROLL_DISPATCHER_PROVIDER_FACTORY": {"__symbolic": "function", "parameters": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngZone", "platform"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "arguments": [{"__symbolic": "reference", "name": "ngZone"}, {"__symbolic": "reference", "name": "platform"}]}}}, "SCROLL_DISPATCHER_PROVIDER": {"provide": {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 178, "character": 14}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 178, "character": 30}}, {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 29, "character": 31}, {"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 29, "character": 58}], "useFactory": {"__symbolic": "reference", "name": "SCROLL_DISPATCHER_PROVIDER_FACTORY"}}, "CdkScrollable": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 19, "character": 1}, "arguments": [{"selector": "[cdk-scrollable], [cdkScrollable]"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 26, "character": 35}, {"__symbolic": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 28, "character": 31}]}], "ngOnInit": [{"__symbolic": "method"}], "ngOnDestroy": [{"__symbolic": "method"}], "elementScrolled": [{"__symbolic": "method"}], "getElementRef": [{"__symbolic": "method"}]}}, "DEFAULT_RESIZE_TIME": 20, "ViewportRuler": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 24, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 35, "character": 24}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 35, "character": 42}]}], "ngOnDestroy": [{"__symbolic": "method"}], "getViewportSize": [{"__symbolic": "method"}], "getViewportRect": [{"__symbolic": "method"}], "getViewportScrollPosition": [{"__symbolic": "method"}], "change": [{"__symbolic": "method"}], "_updateViewportSize": [{"__symbolic": "method"}]}}, "VIEWPORT_RULER_PROVIDER_FACTORY": {"__symbolic": "function", "parameters": ["parentRuler", "platform", "ngZone"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "parentRuler"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "ViewportRuler"}, "arguments": [{"__symbolic": "reference", "name": "platform"}, {"__symbolic": "reference", "name": "ngZone"}]}}}, "VIEWPORT_RULER_PROVIDER": {"provide": {"__symbolic": "reference", "name": "ViewportRuler"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 124, "character": 14}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 124, "character": 30}}, {"__symbolic": "reference", "name": "ViewportRuler"}], {"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "Platform", "line": 35, "character": 24}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 35, "character": 42}], "useFactory": {"__symbolic": "reference", "name": "VIEWPORT_RULER_PROVIDER_FACTORY"}}, "ScrollDispatchModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 13, "character": 1}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/cdk/platform", "name": "PlatformModule", "line": 14, "character": 12}], "exports": [{"__symbolic": "reference", "name": "CdkScrollable"}], "declarations": [{"__symbolic": "reference", "name": "CdkScrollable"}], "providers": [{"__symbolic": "reference", "name": "SCROLL_DISPATCHER_PROVIDER"}]}]}], "members": {}}}, "origins": {"DEFAULT_SCROLL_TIME": "./scroll-dispatcher", "ScrollDispatcher": "./scroll-dispatcher", "SCROLL_DISPATCHER_PROVIDER_FACTORY": "./scroll-dispatcher", "SCROLL_DISPATCHER_PROVIDER": "./scroll-dispatcher", "CdkScrollable": "./scrollable", "DEFAULT_RESIZE_TIME": "./viewport-ruler", "ViewportRuler": "./viewport-ruler", "VIEWPORT_RULER_PROVIDER_FACTORY": "./viewport-ruler", "VIEWPORT_RULER_PROVIDER": "./viewport-ruler", "ScrollDispatchModule": "./scrolling-module"}, "importAs": "@angular/cdk/scrolling"}