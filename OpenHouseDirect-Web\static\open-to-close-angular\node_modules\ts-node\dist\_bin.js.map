{"version": 3, "file": "_bin.js", "sourceRoot": "", "sources": ["../src/_bin.ts"], "names": [], "mappings": ";;AAAA,6BAAoC;AACpC,6BAAyC;AACzC,6BAA8B;AAC9B,+BAAiC;AACjC,+BAAiC;AACjC,mCAAqC;AACrC,+BAAyB;AACzB,6BAAgC;AAChC,yBAA2B;AAC3B,iCAA4F;AAmB5F,IAAM,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAA;AACjH,IAAM,QAAQ,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;AAE1D,IAAM,OAAO,GAAgC;IAC3C,IAAI,EAAE,CAAC,GAAG,CAAC;IACX,OAAO,EAAE,CAAC,GAAG,CAAC;IACd,IAAI,EAAE,CAAC,GAAG,CAAC;IACX,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,OAAO,EAAE,CAAC,GAAG,CAAC;IACd,QAAQ,EAAE,CAAC,GAAG,CAAC;IACf,OAAO,EAAE,CAAC,GAAG,CAAC;IACd,SAAS,EAAE,CAAC,YAAY,CAAC;IACzB,cAAc,EAAE,CAAC,iBAAiB,CAAC;IACnC,cAAc,EAAE,CAAC,GAAG,EAAE,iBAAiB,CAAC;IACxC,eAAe,EAAE,CAAC,GAAG,EAAE,kBAAkB,CAAC;CAC3C,CAAA;AAED,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;AAE9B,oBAAqB,GAAW;IAC9B,IAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IAGpC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAA;IACb,CAAC;IAED,GAAG,CAAC,CAAe,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ;QAAtB,IAAM,IAAI,iBAAA;QACb,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;YAClB,MAAM,CAAC,IAAI,CAAA;QACb,CAAC;QAED,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QAE3B,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACV,GAAG,CAAC,CAAgB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK;gBAApB,IAAM,KAAK,cAAA;gBACd,EAAE,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAA;gBACb,CAAC;aACF;QACH,CAAC;KACF;IAED,MAAM,CAAC,KAAK,CAAA;AACd,CAAC;AAGD,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7C,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC3B,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAEhC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvC,QAAQ,CAAA;IACV,CAAC;IAED,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAEnB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,EAAE,CAAA;QACL,CAAC;QAED,QAAQ,CAAA;IACV,CAAC;IAED,IAAI,GAAG,CAAC,CAAA;IACR,KAAK,CAAA;AACP,CAAC;AAED,IAAM,IAAI,GAAG,QAAQ,CAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;IACvD,MAAM,EAAE,OAAO;IACf,OAAO,EAAE,QAAQ;IACjB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE;QACP,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;KAChB;CACF,CAAC,CAAA;AAEF,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACd,OAAO,CAAC,GAAG,CAAC,m2BAgBb,CAAC,CAAA;IAEA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC;AAED,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;AACzB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;AAC7D,IAAM,YAAY,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;AAClE,IAAM,MAAM,GAAG,YAAY,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;AAC3D,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,KAAK,SAAS,CAAA;AAG1C,IAAM,OAAO,GAAG,gBAAQ,CAAC;IACvB,SAAS,EAAE,IAAI,CAAC,SAAS;IACzB,KAAK,EAAE,IAAI,CAAC,KAAK;IACjB,cAAc,EAAE,IAAI,CAAC,cAAc;IACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;IACvB,OAAO,EAAE,IAAI,CAAC,OAAO;IACrB,MAAM,EAAE,IAAI,CAAC,MAAM;IACnB,cAAc,EAAE,IAAI,CAAC,cAAc;IACnC,eAAe,EAAE,aAAK,CAAC,IAAI,CAAC,eAAe,CAAC;IAC5C,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAO;IACvC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAU;CACjD,CAAC,CAAA;AAGF,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACjB,OAAO,CAAC,GAAG,CAAC,cAAY,eAAS,CAAC,CAAA;IAClC,OAAO,CAAC,GAAG,CAAC,UAAQ,OAAO,CAAC,OAAS,CAAC,CAAA;IACtC,OAAO,CAAC,GAAG,CAAC,iBAAe,OAAO,CAAC,EAAE,CAAC,OAAS,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,WAAS,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAG,CAAC,CAAA;IACxD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC;AAGA,MAAc,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;AAKrD,IAAM,aAAa,GAAG,WAAW,CAAA;AACjC,IAAM,SAAS,GAAG,WAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;AAC1C,IAAM,aAAa,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;AAGrE,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;IACjB,WAAW,CAAC,IAAc,EAAE,SAAS,CAAC,CAAA;AACxC,CAAC;AAAC,IAAI,CAAC,CAAC;IACN,EAAE,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/B,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACrC,IAAI,CAAC,CAAC,CAAC,GAAG,cAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAC/B,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACpC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QACpC,MAAM,CAAC,OAAO,EAAE,CAAA;IAClB,CAAC;IAAC,IAAI,CAAC,CAAC;QAEN,EAAE,CAAC,CAAE,OAAO,CAAC,KAAa,CAAC,KAAK,CAAC,CAAC,CAAC;YACjC,SAAS,EAAE,CAAA;QACb,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,MAAI,GAAG,EAAE,CAAA;YACb,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAa,IAAK,OAAA,MAAI,IAAI,KAAK,EAAb,CAAa,CAAC,CAAA;YAC1D,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,cAAM,OAAA,WAAW,CAAC,MAAI,EAAE,SAAS,CAAC,EAA5B,CAA4B,CAAC,CAAA;QAC7D,CAAC;IACH,CAAC;AACH,CAAC;AAKD,qBAAsB,IAAY,EAAE,SAAkB;IACpD,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,CAAA;IACxC,MAAM,CAAC,QAAQ,GAAG,aAAa,CAAA;IAC/B,MAAM,CAAC,KAAK,GAAI,MAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAEnD;IAAC,MAAc,CAAC,UAAU,GAAG,aAAa,CAC1C;IAAC,MAAc,CAAC,SAAS,GAAG,GAAG,CAC/B;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CACxC;IAAC,MAAc,CAAC,MAAM,GAAG,MAAM,CAC/B;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAEtD,IAAI,MAAW,CAAA;IAEf,IAAI,CAAC;QACH,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC9B,CAAC;IAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACf,EAAE,CAAC,CAAC,KAAK,YAAY,eAAO,CAAC,CAAC,CAAC;YAC7B,OAAO,CAAC,KAAK,CAAC,kBAAU,CAAC,KAAK,CAAC,CAAC,CAAA;YAChC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC;QAED,MAAM,KAAK,CAAA;IACb,CAAC;IAED,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAO,CAAC,MAAM,CAAC,CAAC,CAAA;IACpE,CAAC;AACH,CAAC;AAKD,eAAgB,KAAa,EAAE,OAAY;IACzC,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAA;IACjC,IAAM,YAAY,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACvC,IAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;IAC9B,IAAI,MAAc,CAAA;IAElB,IAAI,CAAC;QACH,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,CAAA;IAClE,CAAC;IAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACb,IAAI,EAAE,CAAA;QAEN,MAAM,GAAG,CAAA;IACX,CAAC;IAGD,IAAM,OAAO,GAAG,gBAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAEvD,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QACjB,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,IAAI,CAAC,CAAC;QACN,aAAa,CAAC,MAAM,GAAG,MAAM,CAAA;IAC/B,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,MAAM;QACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAC3E,CAAC,EAAE,SAAS,CAAC,CAAA;AACf,CAAC;AAKD,cAAe,IAAY,EAAE,QAAgB,EAAE,OAAY;IACzD,IAAM,MAAM,GAAG,IAAI,WAAM,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IAEvD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;AACxC,CAAC;AAKD;IACE,IAAM,IAAI,GAAG,YAAK,CAAC;QACjB,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,KAAK;KACjB,CAAC,CAAA;IAGF,IAAM,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC,CAAA;IAEhC;QACE,SAAS,EAAE,CAAA;QAGX,IAAI,CAAC,0BAA0B,EAAE,aAAa,EAAG,IAAY,CAAC,OAAO,CAAC,CAAA;IACxE,CAAC;IAED,KAAK,EAAE,CAAA;IACP,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAEvB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;QACzB,IAAI,EAAE,2CAA2C;QACjD,MAAM,EAAE,UAAU,UAAkB;YAClC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;gBAChB,IAAI,CAAC,aAAa,EAAE,CAAA;gBACpB,MAAM,CAAA;YACR,CAAC;YAED,IAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,CAAA;YAC7B,IAAA,oFAAmG,EAAjG,cAAI,EAAE,oBAAO,CAAoF;YAEzG,IAAI,EAAE,CAAA;YAEN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAI,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAK,OAAO,CAAC,CAAC,CAAI,OAAO,OAAI,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC,CAAA;YAChF,IAAI,CAAC,aAAa,EAAE,CAAA;QACtB,CAAC;KACF,CAAC,CAAA;AACJ,CAAC;AAKD,kBAAmB,IAAY,EAAE,OAAY,EAAE,SAAiB,EAAE,QAA4C;IAC5G,IAAI,GAAQ,CAAA;IACZ,IAAI,MAAW,CAAA;IAGf,EAAE,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;QACtB,QAAQ,EAAE,CAAA;QACV,MAAM,CAAA;IACR,CAAC;IAED,IAAI,CAAC;QACH,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC/B,CAAC;IAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACf,EAAE,CAAC,CAAC,KAAK,YAAY,eAAO,CAAC,CAAC,CAAC;YAE7B,EAAE,CAAC,CAAC,kBAAW,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxC,GAAG,GAAG,IAAI,kBAAW,CAAC,KAAK,CAAC,CAAA;YAC9B,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,GAAG,GAAG,kBAAU,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,GAAG,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;AACvB,CAAC;AAKD,oBAAqB,KAAa;IAChC,IAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAA;IACrC,IAAM,WAAW,GAAG,aAAa,CAAC,OAAO,CAAA;IACzC,IAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAA;IACvC,IAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAA;IAGrC,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC9G,aAAa,CAAC,KAAK,GAAM,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAK,CAAA;IAChE,CAAC;IAED,aAAa,CAAC,KAAK,IAAI,KAAK,CAAA;IAC5B,aAAa,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,CAAA;IACvC,aAAa,CAAC,OAAO,EAAE,CAAA;IAEvB,MAAM,CAAC;QACL,aAAa,CAAC,KAAK,GAAG,SAAS,CAAA;QAC/B,aAAa,CAAC,MAAM,GAAG,UAAU,CAAA;QACjC,aAAa,CAAC,OAAO,GAAG,WAAW,CAAA;QACnC,aAAa,CAAC,KAAK,GAAG,SAAS,CAAA;IACjC,CAAC,CAAA;AACH,CAAC;AAKD,mBAAoB,KAAa;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAA;IAEb,GAAG,CAAC,CAAe,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK;QAAnB,IAAM,IAAI,cAAA;QACb,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,CAAA;QACT,CAAC;KACF;IAED,MAAM,CAAC,KAAK,CAAA;AACd,CAAC;AAKD,qBAAsB,IAAY;IAChC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,eAAO,CAAC,IAAI,CAAC,CAAA;AACjE,CAAC;AAKD,wBAAyB,IAAY;IACnC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,kBAAU,CAAC,IAAI,CAAC,CAAA;AAC/C,CAAC;AAED,IAAM,cAAc,GAAa;IAC/B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;CACL,CAAA;AAKD,uBAAwB,KAAc;IACpC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAnC,CAAmC,CAAC,CAAA;AAC1E,CAAC", "sourcesContent": ["import { join, resolve } from 'path'\nimport { start, Recoverable } from 'repl'\nimport { inspect } from 'util'\nimport arrify = require('arrify')\nimport Module = require('module')\nimport minimist = require('minimist')\nimport chalk from 'chalk'\nimport { diffLines } from 'diff'\nimport { <PERSON>ript } from 'vm'\nimport { register, VERSION, getFile, fileExists, TSError, parse, printError } from './index'\n\ninterface Argv {\n  eval?: string\n  print?: string\n  typeCheck?: boolean\n  cache?: boolean\n  cacheDirectory?: string\n  version?: boolean\n  help?: boolean\n  compiler?: string\n  project?: string\n  require?: string | string[]\n  ignore?: boolean | string | string[]\n  ignoreWarnings?: string | string[]\n  compilerOptions?: any\n  _: string[]\n}\n\nconst strings = ['eval', 'print', 'compiler', 'project', 'ignoreWarnings', 'require', 'cacheDirectory', 'ignore']\nconst booleans = ['help', 'typeCheck', 'version', 'cache']\n\nconst aliases: { [key: string]: string[] } = {\n  help: ['h'],\n  version: ['v'],\n  eval: ['e'],\n  print: ['p'],\n  project: ['P'],\n  compiler: ['C'],\n  require: ['r'],\n  typeCheck: ['type-check'],\n  cacheDirectory: ['cache-directory'],\n  ignoreWarnings: ['I', 'ignore-warnings'],\n  compilerOptions: ['O', 'compiler-options']\n}\n\nlet stop = process.argv.length\n\nfunction isFlagOnly (arg: string) {\n  const name = arg.replace(/^--?/, '')\n\n  // The value is part of this argument.\n  if (/=/.test(name) || /^--no-/.test(arg)) {\n    return true\n  }\n\n  for (const bool of booleans) {\n    if (name === bool) {\n      return true\n    }\n\n    const alias = aliases[bool]\n\n    if (alias) {\n      for (const other of alias) {\n        if (other === name) {\n          return true\n        }\n      }\n    }\n  }\n\n  return false\n}\n\n// Hack around known subarg issue with `stopEarly`.\nfor (let i = 2; i < process.argv.length; i++) {\n  const arg = process.argv[i]\n  const next = process.argv[i + 1]\n\n  if (/^\\[/.test(arg) || /\\]$/.test(arg)) {\n    continue\n  }\n\n  if (/^-/.test(arg)) {\n    // Skip next argument.\n    if (!isFlagOnly(arg) && !/^-/.test(next)) {\n      i++\n    }\n\n    continue\n  }\n\n  stop = i\n  break\n}\n\nconst argv = minimist<Argv>(process.argv.slice(2, stop), {\n  string: strings,\n  boolean: booleans,\n  alias: aliases,\n  default: {\n    cache: null,\n    typeCheck: null\n  }\n})\n\nif (argv.help) {\n  console.log(`\nUsage: ts-node [options] [ -e script | script.ts ] [arguments]\n\nOptions:\n\n  -e, --eval [code]              Evaluate code\n  -p, --print [code]             Evaluate code and print result\n  -r, --require [path]           Require a node module for execution\n  -C, --compiler [name]          Specify a custom TypeScript compiler\n  -I, --ignoreWarnings [code]    Ignore TypeScript warnings by diagnostic code\n  -P, --project [path]           Path to TypeScript project (or \\`false\\`)\n  -O, --compilerOptions [opts]   JSON object to merge with compiler options\n  -F, --fast                     Run TypeScript compilation in transpile mode\n  --ignore [regexp], --no-ignore Set the ignore check (default: \\`/node_modules/\\`)\n  --no-cache                     Disable the TypeScript cache\n  --cache-directory              Configure the TypeScript cache directory\n`)\n\n  process.exit(0)\n}\n\nconst cwd = process.cwd()\nconst code = argv.eval === undefined ? argv.print : argv.eval\nconst isEvalScript = typeof argv.eval === 'string' || !!argv.print // Minimist struggles with empty strings.\nconst isEval = isEvalScript || stop === process.argv.length\nconst isPrinted = argv.print !== undefined\n\n// Register the TypeScript compiler instance.\nconst service = register({\n  typeCheck: argv.typeCheck,\n  cache: argv.cache,\n  cacheDirectory: argv.cacheDirectory,\n  compiler: argv.compiler,\n  project: argv.project,\n  ignore: argv.ignore,\n  ignoreWarnings: argv.ignoreWarnings,\n  compilerOptions: parse(argv.compilerOptions),\n  getFile: isEval ? getFileEval : getFile,\n  fileExists: isEval ? fileExistsEval : fileExists\n})\n\n// Output project information.\nif (argv.version) {\n  console.log(`ts-node v${VERSION}`)\n  console.log(`node ${process.version}`)\n  console.log(`typescript v${service.ts.version}`)\n  console.log(`cache ${JSON.stringify(service.cachedir)}`)\n  process.exit(0)\n}\n\n// Require specified modules before start-up.\n(Module as any)._preloadModules(arrify(argv.require))\n\n/**\n * Eval helpers.\n */\nconst EVAL_FILENAME = `[eval].ts`\nconst EVAL_PATH = join(cwd, EVAL_FILENAME)\nconst EVAL_INSTANCE = { input: '', output: '', version: 0, lines: 0 }\n\n// Execute the main contents (either eval, script or piped).\nif (isEvalScript) {\n  evalAndExit(code as string, isPrinted)\n} else {\n  if (stop < process.argv.length) {\n    const args = process.argv.slice(stop)\n    args[0] = resolve(cwd, args[0])\n    process.argv = ['node'].concat(args)\n    process.execArgv.unshift(__filename)\n    Module.runMain()\n  } else {\n    // Piping of execution _only_ occurs when no other script is specified.\n    if ((process.stdin as any).isTTY) {\n      startRepl()\n    } else {\n      let code = ''\n      process.stdin.on('data', (chunk: Buffer) => code += chunk)\n      process.stdin.on('end', () => evalAndExit(code, isPrinted))\n    }\n  }\n}\n\n/**\n * Evaluate a script.\n */\nfunction evalAndExit (code: string, isPrinted: boolean) {\n  const module = new Module(EVAL_FILENAME)\n  module.filename = EVAL_FILENAME\n  module.paths = (Module as any)._nodeModulePaths(cwd)\n\n  ;(global as any).__filename = EVAL_FILENAME\n  ;(global as any).__dirname = cwd\n  ;(global as any).exports = module.exports\n  ;(global as any).module = module\n  ;(global as any).require = module.require.bind(module)\n\n  let result: any\n\n  try {\n    result = _eval(code, global)\n  } catch (error) {\n    if (error instanceof TSError) {\n      console.error(printError(error))\n      process.exit(1)\n    }\n\n    throw error\n  }\n\n  if (isPrinted) {\n    console.log(typeof result === 'string' ? result : inspect(result))\n  }\n}\n\n/**\n * Evaluate the code snippet.\n */\nfunction _eval (input: string, context: any) {\n  const lines = EVAL_INSTANCE.lines\n  const isCompletion = !/\\n$/.test(input)\n  const undo = appendEval(input)\n  let output: string\n\n  try {\n    output = service.compile(EVAL_INSTANCE.input, EVAL_PATH, -lines)\n  } catch (err) {\n    undo()\n\n    throw err\n  }\n\n  // Use `diff` to check for new JavaScript to execute.\n  const changes = diffLines(EVAL_INSTANCE.output, output)\n\n  if (isCompletion) {\n    undo()\n  } else {\n    EVAL_INSTANCE.output = output\n  }\n\n  return changes.reduce((result, change) => {\n    return change.added ? exec(change.value, EVAL_FILENAME, context) : result\n  }, undefined)\n}\n\n/**\n * Execute some code.\n */\nfunction exec (code: string, filename: string, context: any) {\n  const script = new Script(code, { filename: filename })\n\n  return script.runInNewContext(context)\n}\n\n/**\n * Start a CLI REPL.\n */\nfunction startRepl () {\n  const repl = start({\n    prompt: '> ',\n    input: process.stdin,\n    output: process.stdout,\n    eval: replEval,\n    useGlobal: false\n  })\n\n  // Bookmark the point where we should reset the REPL state.\n  const resetEval = appendEval('')\n\n  function reset () {\n    resetEval()\n\n    // Hard fix for TypeScript forcing `Object.defineProperty(exports, ...)`.\n    exec('exports = module.exports', EVAL_FILENAME, (repl as any).context)\n  }\n\n  reset()\n  repl.on('reset', reset)\n\n  repl.defineCommand('type', {\n    help: 'Check the type of a TypeScript identifier',\n    action: function (identifier: string) {\n      if (!identifier) {\n        repl.displayPrompt()\n        return\n      }\n\n      const undo = appendEval(identifier)\n      const { name, comment } = service.getTypeInfo(EVAL_INSTANCE.input, EVAL_PATH, EVAL_INSTANCE.input.length)\n\n      undo()\n\n      repl.outputStream.write(`${chalk.bold(name)}\\n${comment ? `${comment}\\n` : ''}`)\n      repl.displayPrompt()\n    }\n  })\n}\n\n/**\n * Eval code from the REPL.\n */\nfunction replEval (code: string, context: any, _filename: string, callback: (err?: Error, result?: any) => any) {\n  let err: any\n  let result: any\n\n  // TODO: Figure out how to handle completion here.\n  if (code === '.scope') {\n    callback()\n    return\n  }\n\n  try {\n    result = _eval(code, context)\n  } catch (error) {\n    if (error instanceof TSError) {\n      // Support recoverable compilations using >= node 6.\n      if (Recoverable && isRecoverable(error)) {\n        err = new Recoverable(error)\n      } else {\n        err = printError(error)\n      }\n    } else {\n      err = error\n    }\n  }\n\n  callback(err, result)\n}\n\n/**\n * Append to the eval instance and return an undo function.\n */\nfunction appendEval (input: string) {\n  const undoInput = EVAL_INSTANCE.input\n  const undoVersion = EVAL_INSTANCE.version\n  const undoOutput = EVAL_INSTANCE.output\n  const undoLines = EVAL_INSTANCE.lines\n\n  // Handle ASI issues with TypeScript re-evaluation.\n  if (undoInput.charAt(undoInput.length - 1) === '\\n' && /^\\s*[\\[\\(\\`]/.test(input) && !/;\\s*$/.test(undoInput)) {\n    EVAL_INSTANCE.input = `${EVAL_INSTANCE.input.slice(0, -1)};\\n`\n  }\n\n  EVAL_INSTANCE.input += input\n  EVAL_INSTANCE.lines += lineCount(input)\n  EVAL_INSTANCE.version++\n\n  return function () {\n    EVAL_INSTANCE.input = undoInput\n    EVAL_INSTANCE.output = undoOutput\n    EVAL_INSTANCE.version = undoVersion\n    EVAL_INSTANCE.lines = undoLines\n  }\n}\n\n/**\n * Count the number of lines.\n */\nfunction lineCount (value: string) {\n  let count = 0\n\n  for (const char of value) {\n    if (char === '\\n') {\n      count++\n    }\n  }\n\n  return count\n}\n\n/**\n * Get the file text, checking for eval first.\n */\nfunction getFileEval (path: string) {\n  return path === EVAL_PATH ? EVAL_INSTANCE.input : getFile(path)\n}\n\n/**\n * Get whether the file exists.\n */\nfunction fileExistsEval (path: string) {\n  return path === EVAL_PATH || fileExists(path)\n}\n\nconst RECOVERY_CODES: number[] = [\n  1003, // \"Identifier expected.\"\n  1005, // \"')' expected.\"\n  1109, // \"Expression expected.\"\n  1126, // \"Unexpected end of text.\"\n  1160, // \"Unterminated template literal.\"\n  1161 // \"Unterminated regular expression literal.\"\n]\n\n/**\n * Check if a function can recover gracefully.\n */\nfunction isRecoverable (error: TSError) {\n  return error.diagnostics.every(x => RECOVERY_CODES.indexOf(x.code) > -1)\n}\n"]}