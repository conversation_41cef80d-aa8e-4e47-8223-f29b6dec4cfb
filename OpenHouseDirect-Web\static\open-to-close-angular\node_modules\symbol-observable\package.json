{"_from": "symbol-observable@1.0.1", "_id": "symbol-observable@1.0.1", "_inBundle": false, "_integrity": "sha512-Kb3PrPYz4HanVF1LVGuAdW6LoVgIwjUYJGzFe7NDrBLCN4lsV/5J0MFurV+ygS4bRVwrCEt2c7MQ1R2a72oJDw==", "_location": "/symbol-observable", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "symbol-observable@1.0.1", "name": "symbol-observable", "escapedName": "symbol-observable", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/rxjs"], "_resolved": "https://registry.npmjs.org/symbol-observable/-/symbol-observable-1.0.1.tgz", "_shasum": "8340fc4702c3122df5d22288f88283f513d3fdd4", "_spec": "symbol-observable@1.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\rxjs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/blesh/symbol-observable/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Symbol.observable ponyfill", "devDependencies": {"babel-cli": "^6.9.0", "babel-preset-es2015": "^6.9.0", "chai": "^3.5.0", "mocha": "^2.4.5", "typescript": "^1.8.10"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "ponyfill.js", "index.d.ts", "es/index.js", "es/ponyfill/js", "lib/index.js", "lib/ponyfill.js"], "homepage": "https://github.com/blesh/symbol-observable#readme", "jsnext:main": "es/index.js", "keywords": ["symbol", "observable", "observables", "ponyfill", "polyfill", "shim"], "license": "MIT", "name": "symbol-observable", "repository": {"type": "git", "url": "git+https://github.com/blesh/symbol-observable.git"}, "scripts": {"build": "babel es --out-dir lib", "prepublish": "npm test", "test": "npm run build && mocha && tsc ./ts-test/test.ts && node ./ts-test/test.js"}, "typings": "index.d.ts", "version": "1.0.1"}