# symbol-observable [![Build Status](https://travis-ci.org/blesh/symbol-observable.svg?branch=master)](https://travis-ci.org/blesh/symbol-observable)

> [Symbol.observable](https://github.com/zenparsing/es-observable) ponyfill


## Install

```
$ npm install --save symbol-observable
```


## Usage

```js
const symbolObservable = require('symbol-observable');

console.log(symbolObservable);
//=> Symbol(observable)
```


## Related

- [is-observable](https://github.com/sindresorhus/is-observable) - Check if a value is an Observable
- [observable-to-promise](https://github.com/sindresorhus/observable-to-promise) - Convert an Observable to a Promise


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
