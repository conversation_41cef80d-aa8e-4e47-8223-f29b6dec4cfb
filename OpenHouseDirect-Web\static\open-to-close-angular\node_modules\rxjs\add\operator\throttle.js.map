{"version": 3, "file": "throttle.js", "sourceRoot": "", "sources": ["../../../src/add/operator/throttle.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,yBAAyB,yBAAyB,CAAC,CAAA;AAEnD,uBAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,mBAAQ,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { throttle } from '../../operator/throttle';\n\nObservable.prototype.throttle = throttle;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    throttle: typeof throttle;\n  }\n}"]}