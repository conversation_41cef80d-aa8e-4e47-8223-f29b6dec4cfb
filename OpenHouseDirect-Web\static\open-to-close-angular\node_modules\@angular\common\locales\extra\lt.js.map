{"version": 3, "file": "lt.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/lt.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;QACnE,AADoE;KAErE;IACD;QACE,CAAC,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;QACrE,AADsE;KAEvE;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['vidurnaktis', 'perpiet', 'rytas', 'popietė', 'vakaras', 'naktis'],\n    ,\n  ],\n  [\n    ['vidurnaktis', 'vidurdienis', 'rytas', 'diena', 'vakaras', 'naktis'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'],\n    ['00:00', '06:00']\n  ]\n];\n"]}