# time-stamp [![NPM version](https://img.shields.io/npm/v/time-stamp.svg?style=flat)](https://www.npmjs.com/package/time-stamp) [![NPM monthly downloads](https://img.shields.io/npm/dm/time-stamp.svg?style=flat)](https://npmjs.org/package/time-stamp) [![NPM total downloads](https://img.shields.io/npm/dt/time-stamp.svg?style=flat)](https://npmjs.org/package/time-stamp) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/time-stamp.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/time-stamp)

> Get a formatted timestamp.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

- [Install](#install)
- [Usage](#usage)
- [Customizing the timestamp](#customizing-the-timestamp)
- [Release history](#release-history)
  * [v2.0.0](#v200)
- [About](#about)

_(TOC generated by [verb](https://github.com/verbose/verb) using [markdown-toc](https://github.com/jonschlinkert/markdown-toc))_

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save time-stamp
```

## Usage

```js
const timestamp = require('time-stamp');

console.log(timestamp());
//=> 2018-10-26

console.log(timestamp.utc());
//=> 2018-10-26
```

## Customizing the timestamp

You may also pass a string to format the generated timestamp.

```js
console.log(timestamp('YYYYMMDD'));
//=> 20181026

console.log(timestamp.utc('YYYYMMDD'));
//=> 20181026
```

**Supported patterns**

* `YYYY`: full year (ex: **2018**)
* `MM`: month (ex: **04**)
* `DD`: day (ex: **01**)
* `HH`: hours (ex: **12**)
* `mm`: minutes (ex: **59**)
* `ss`: seconds (ex: **09**)
* `ms`: milliseconds (ex: **532**)

**Usage Examples**

```js
console.log(timestamp('YYYYMMDD'));
//=> 20181026
console.log(timestamp.utc('YYYYMMDD'));
//=> 20181026

console.log(timestamp('YYYYMMDD:ss'));
//=> 20181026:24
console.log(timestamp.utc('YYYYMMDD:ss'));
//=> 20181026:24

console.log(timestamp('YYYY/MM/DD:mm:ss'));
//=> 2018/10/26:46:24
console.log(timestamp.utc('YYYY/MM/DD:mm:ss'));
//=> 2018/10/26:46:24

console.log(timestamp('YYYY:MM:DD'));
//=> 2018:10:26
console.log(timestamp.utc('YYYY:MM:DD'));
//=> 2018:10:26

console.log(timestamp('[YYYY:MM:DD]'));
//=> [2018:10:26]
console.log(timestamp.utc('[YYYY:MM:DD]'));
//=> [2018:10:26]

console.log(timestamp('YYYY/MM/DD'));
//=> 2018/10/26
console.log(timestamp.utc('YYYY/MM/DD'));
//=> 2018/10/26

console.log(timestamp('YYYY:MM'));
//=> 2018:10
console.log(timestamp.utc('YYYY:MM'));
//=> 2018:10

console.log(timestamp('YYYY'));
//=> 2018
console.log(timestamp.utc('YYYY'));
//=> 2018

console.log(timestamp('MM'));
//=> 10
console.log(timestamp.utc('MM'));
//=> 10

console.log(timestamp('DD'));
//=> 26
console.log(timestamp.utc('DD'));
//=> 26

console.log(timestamp('HH'));
//=> 00
console.log(timestamp.utc('HH'));
//=> 04

console.log(timestamp('mm'));
//=> 46
console.log(timestamp.utc('mm'));
//=> 46

console.log(timestamp('ss'));
//=> 24
console.log(timestamp.utc('ss'));
//=> 24

console.log(timestamp('ms'));
//=> 186
console.log(timestamp.utc('ms'));
//=> 186
```

## Release history

### v2.0.0

**Breaking changes**

Default pattern was changed from `YYYY:MM:DD` to `YYYY-MM-DD`. See [issues/3](../../issues) for more details.

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [days](https://www.npmjs.com/package/days): Days of the week. | [homepage](https://github.com/jonschlinkert/days "Days of the week.")
* [iso-week](https://www.npmjs.com/package/iso-week): Get the ISO week of the year. | [homepage](https://github.com/jonschlinkert/iso-week "Get the ISO week of the year.")
* [month](https://www.npmjs.com/package/month): Get the name or number of the current month or any month of the year. | [homepage](https://github.com/datetime/month "Get the name or number of the current month or any month of the year.")
* [months](https://www.npmjs.com/package/months): Months of the year. | [homepage](https://github.com/datetime/months "Months of the year.")
* [o-clock](https://www.npmjs.com/package/o-clock): Simple javascript utility for displaying the time in 12-hour clock format. | [homepage](https://github.com/jonschlinkert/o-clock "Simple javascript utility for displaying the time in 12-hour clock format.")
* [seconds](https://www.npmjs.com/package/seconds): Get the number of seconds for a minute, hour, day and week. | [homepage](https://github.com/jonschlinkert/seconds "Get the number of seconds for a minute, hour, day and week.")
* [week](https://www.npmjs.com/package/week): Get the current week number. | [homepage](https://github.com/datetime/week "Get the current week number.")
* [weekday](https://www.npmjs.com/package/weekday): Get the name and number of the current weekday. Or get the name of the… [more](https://github.com/datetime/weekday) | [homepage](https://github.com/datetime/weekday "Get the name and number of the current weekday. Or get the name of the weekday for a given number.")
* [year](https://www.npmjs.com/package/year): Simple utility to get the current year with 2 or 4 digits. | [homepage](https://github.com/jonschlinkert/year "Simple utility to get the current year with 2 or 4 digits.")

### Contributors

| **Commits** | **Contributor** |  
| --- | --- |  
| 31 | [jonschlinkert](https://github.com/jonschlinkert) |  
| 7  | [doowb](https://github.com/doowb) |  
| 1  | [evocateur](https://github.com/evocateur) |  
| 1  | [mendenhallmagic](https://github.com/mendenhallmagic) |  
| 1  | [mvanroon](https://github.com/mvanroon) |  
| 1  | [leesei](https://github.com/leesei) |  
| 1  | [sleagon](https://github.com/sleagon) |  

### Author

**Jon Schlinkert**

* [GitHub Profile](https://github.com/jonschlinkert)
* [Twitter Profile](https://twitter.com/jonschlinkert)
* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on October 26, 2018._