/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/cdk/keycodes"),require("@angular/cdk/coercion"),require("@angular/forms"),require("@angular/cdk/bidi"),require("rxjs/Subject"),require("@angular/common")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/keycodes","@angular/cdk/coercion","@angular/forms","@angular/cdk/bidi","rxjs/Subject","@angular/common"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.stepper=e.ng.cdk.stepper||{}),e.ng.core,e.ng.cdk.keycodes,e.ng.cdk.coercion,e.ng.forms,e.ng.cdk.bidi,e.Rx,e.ng.common)}(this,function(e,t,n,o,r,i,s,p){"use strict";var c=function(){function e(e){this.template=e}return e.decorators=[{type:t.Directive,args:[{selector:"[cdkStepLabel]"}]}],e.ctorParameters=function(){return[{type:t.TemplateRef}]},e}(),u=0,a=function(){function e(){}return e}(),d=function(){function e(e){this._stepper=e,this.interacted=!1,this._editable=!0,this._optional=!1,this._customCompleted=null}return Object.defineProperty(e.prototype,"editable",{get:function(){return this._editable},set:function(e){this._editable=o.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"optional",{get:function(){return this._optional},set:function(e){this._optional=o.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"completed",{get:function(){return null==this._customCompleted?this._defaultCompleted:this._customCompleted},set:function(e){this._customCompleted=o.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_defaultCompleted",{get:function(){return this.stepControl?this.stepControl.valid&&this.interacted:this.interacted},enumerable:!0,configurable:!0}),e.prototype.select=function(){this._stepper.selected=this},e.prototype.reset=function(){this.interacted=!1,null!=this._customCompleted&&(this._customCompleted=!1),this.stepControl&&this.stepControl.reset()},e.prototype.ngOnChanges=function(){this._stepper._stateChanged()},e.decorators=[{type:t.Component,args:[{selector:"cdk-step",exportAs:"cdkStep",template:"<ng-template><ng-content></ng-content></ng-template>",encapsulation:t.ViewEncapsulation.None,preserveWhitespaces:!1,changeDetection:t.ChangeDetectionStrategy.OnPush}]}],e.ctorParameters=function(){return[{type:l,decorators:[{type:t.Inject,args:[t.forwardRef(function(){return l})]}]}]},e.propDecorators={stepLabel:[{type:t.ContentChild,args:[c]}],content:[{type:t.ViewChild,args:[t.TemplateRef]}],stepControl:[{type:t.Input}],label:[{type:t.Input}],editable:[{type:t.Input}],optional:[{type:t.Input}],completed:[{type:t.Input}]},e}(),l=function(){function e(e,n){this._dir=e,this._changeDetectorRef=n,this._destroyed=new s.Subject,this._linear=!1,this._selectedIndex=0,this.selectionChange=new t.EventEmitter,this._focusIndex=0,this._orientation="horizontal",this._groupId=u++}return Object.defineProperty(e.prototype,"linear",{get:function(){return this._linear},set:function(e){this._linear=o.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"selectedIndex",{get:function(){return this._selectedIndex},set:function(e){if(this._steps){if(e<0||e>this._steps.length-1)throw Error("cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.");this._anyControlsInvalidOrPending(e)||e<this._selectedIndex&&!this._steps.toArray()[e].editable?this._stepHeader.toArray()[e].nativeElement.blur():this._selectedIndex!=e&&(this._emitStepperSelectionEvent(e),this._focusIndex=this._selectedIndex)}else this._selectedIndex=this._focusIndex=e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"selected",{get:function(){return this._steps.toArray()[this.selectedIndex]},set:function(e){this.selectedIndex=this._steps.toArray().indexOf(e)},enumerable:!0,configurable:!0}),e.prototype.ngOnDestroy=function(){this._destroyed.next(),this._destroyed.complete()},e.prototype.next=function(){this.selectedIndex=Math.min(this._selectedIndex+1,this._steps.length-1)},e.prototype.previous=function(){this.selectedIndex=Math.max(this._selectedIndex-1,0)},e.prototype.reset=function(){this.selectedIndex=0,this._steps.forEach(function(e){return e.reset()}),this._stateChanged()},e.prototype._getStepLabelId=function(e){return"cdk-step-label-"+this._groupId+"-"+e},e.prototype._getStepContentId=function(e){return"cdk-step-content-"+this._groupId+"-"+e},e.prototype._stateChanged=function(){this._changeDetectorRef.markForCheck()},e.prototype._getAnimationDirection=function(e){var t=e-this._selectedIndex;return t<0?"rtl"===this._layoutDirection()?"next":"previous":t>0?"rtl"===this._layoutDirection()?"previous":"next":"current"},e.prototype._getIndicatorType=function(e){var t=this._steps.toArray()[e];return t.completed&&this._selectedIndex!=e?t.editable?"edit":"done":"number"},e.prototype._emitStepperSelectionEvent=function(e){var t=this._steps.toArray();this.selectionChange.emit({selectedIndex:e,previouslySelectedIndex:this._selectedIndex,selectedStep:t[e],previouslySelectedStep:t[this._selectedIndex]}),this._selectedIndex=e,this._stateChanged()},e.prototype._onKeydown=function(e){var t=e.keyCode;t===n.RIGHT_ARROW&&("rtl"===this._layoutDirection()?this._focusPreviousStep():this._focusNextStep(),e.preventDefault()),t===n.LEFT_ARROW&&("rtl"===this._layoutDirection()?this._focusNextStep():this._focusPreviousStep(),e.preventDefault()),"vertical"!==this._orientation||t!==n.UP_ARROW&&t!==n.DOWN_ARROW||(t===n.UP_ARROW?this._focusPreviousStep():this._focusNextStep(),e.preventDefault()),t!==n.SPACE&&t!==n.ENTER||(this.selectedIndex=this._focusIndex,e.preventDefault()),t===n.HOME&&(this._focusStep(0),e.preventDefault()),t===n.END&&(this._focusStep(this._steps.length-1),e.preventDefault())},e.prototype._focusNextStep=function(){this._focusStep((this._focusIndex+1)%this._steps.length)},e.prototype._focusPreviousStep=function(){this._focusStep((this._focusIndex+this._steps.length-1)%this._steps.length)},e.prototype._focusStep=function(e){this._focusIndex=e,this._stepHeader.toArray()[this._focusIndex].nativeElement.focus()},e.prototype._anyControlsInvalidOrPending=function(e){var t=this._steps.toArray();return t[this._selectedIndex].interacted=!0,!!(this._linear&&e>=0)&&t.slice(0,e).some(function(e){var t=e.stepControl;return(t?t.invalid||t.pending||!e.interacted:!e.completed)&&!e.optional})},e.prototype._layoutDirection=function(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"},e.decorators=[{type:t.Directive,args:[{selector:"[cdkStepper]",exportAs:"cdkStepper"}]}],e.ctorParameters=function(){return[{type:i.Directionality,decorators:[{type:t.Optional}]},{type:t.ChangeDetectorRef}]},e.propDecorators={_steps:[{type:t.ContentChildren,args:[d]}],linear:[{type:t.Input}],selectedIndex:[{type:t.Input}],selected:[{type:t.Input}],selectionChange:[{type:t.Output}]},e}(),h=function(){function e(e){this._stepper=e,this.type="submit"}return e.decorators=[{type:t.Directive,args:[{selector:"button[cdkStepperNext]",host:{"(click)":"_stepper.next()","[type]":"type"}}]}],e.ctorParameters=function(){return[{type:l}]},e.propDecorators={type:[{type:t.Input}]},e}(),f=function(){function e(e){this._stepper=e,this.type="button"}return e.decorators=[{type:t.Directive,args:[{selector:"button[cdkStepperPrevious]",host:{"(click)":"_stepper.previous()","[type]":"type"}}]}],e.ctorParameters=function(){return[{type:l}]},e.propDecorators={type:[{type:t.Input}]},e}(),_=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{imports:[i.BidiModule,p.CommonModule],exports:[d,l,c,h,f],declarations:[d,l,c,h,f]}]}],e.ctorParameters=function(){return[]},e}();e.StepperSelectionEvent=a,e.CdkStep=d,e.CdkStepper=l,e.CdkStepLabel=c,e.CdkStepperNext=h,e.CdkStepperPrevious=f,e.CdkStepperModule=_,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-stepper.umd.min.js.map
