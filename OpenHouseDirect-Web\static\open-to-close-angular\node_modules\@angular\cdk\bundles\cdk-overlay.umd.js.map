{"version": 3, "file": "cdk-overlay.umd.js", "sources": ["../../src/cdk/overlay/fullscreen-overlay-container.ts", "../../src/cdk/overlay/overlay-module.ts", "../../src/cdk/overlay/overlay-directives.ts", "../../src/cdk/overlay/overlay.ts", "../../src/cdk/overlay/overlay-container.ts", "../../src/cdk/overlay/keyboard/overlay-keyboard-dispatcher.ts", "../../src/cdk/overlay/position/overlay-position-builder.ts", "../../src/cdk/overlay/position/global-position-strategy.ts", "../../src/cdk/overlay/position/connected-position-strategy.ts", "../../src/cdk/overlay/overlay-ref.ts", "../../src/cdk/overlay/scroll/scroll-strategy-options.ts", "../../src/cdk/overlay/scroll/reposition-scroll-strategy.ts", "../../src/cdk/overlay/position/scroll-clip.ts", "../../src/cdk/overlay/scroll/block-scroll-strategy.ts", "../../src/cdk/overlay/scroll/close-scroll-strategy.ts", "../../src/cdk/overlay/scroll/scroll-strategy.ts", "../../src/cdk/overlay/position/connected-position.ts", "../../src/cdk/overlay/overlay-config.ts", "../../src/cdk/overlay/scroll/noop-scroll-strategy.ts", "../../node_modules/tslib/tslib.es6.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {OverlayContainer} from './overlay-container';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\n@Injectable()\nexport class FullscreenOverlayContainer extends OverlayContainer {\n  protected _createContainer(): void {\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n\n  private _adjustParentForFullscreenChange(): void {\n    if (!this._containerElement) {\n      return;\n    }\n    let fullscreenElement = this.getFullscreenElement();\n    let parent = fullscreenElement || document.body;\n    parent.appendChild(this._containerElement);\n  }\n\n  private _addFullscreenChangeListener(fn: () => void) {\n    if (document.fullscreenEnabled) {\n      document.addEventListener('fullscreenchange', fn);\n    } else if (document.webkitFullscreenEnabled) {\n      document.addEventListener('webkitfullscreenchange', fn);\n    } else if ((document as any).mozFullScreenEnabled) {\n      document.addEventListener('mozfullscreenchange', fn);\n    } else if ((document as any).msFullscreenEnabled) {\n      document.addEventListener('MSFullscreenChange', fn);\n    }\n  }\n\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement(): Element {\n    return document.fullscreenElement ||\n        document.webkitFullscreenElement ||\n        (document as any).mozFullScreenElement ||\n        (document as any).msFullscreenElement ||\n        null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BidiModule} from '@angular/cdk/bidi';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {ScrollDispatchModule, VIEWPORT_RULER_PROVIDER} from '@angular/cdk/scrolling';\nimport {NgModule, Provider} from '@angular/core';\nimport {Overlay} from './overlay';\nimport {OVERLAY_CONTAINER_PROVIDER} from './overlay-container';\nimport {\n  CdkConnectedOverlay,\n  CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER,\n  CdkOverlayOrigin,\n} from './overlay-directives';\nimport {OverlayPositionBuilder} from './position/overlay-position-builder';\nimport {OVERLAY_KEYBOARD_DISPATCHER_PROVIDER} from './keyboard/overlay-keyboard-dispatcher';\nimport {ScrollStrategyOptions} from './scroll/scroll-strategy-options';\n\nexport const OVERLAY_PROVIDERS: Provider[] = [\n  Overlay,\n  OverlayPositionBuilder,\n  OVERLAY_KEYBOARD_DISPATCHER_PROVIDER,\n  VIEWPORT_RULER_PROVIDER,\n  OVERLAY_CONTAINER_PROVIDER,\n  CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER,\n];\n\n@NgModule({\n  imports: [BidiModule, PortalModule, ScrollDispatchModule],\n  exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollDispatchModule],\n  declarations: [CdkConnectedOverlay, CdkOverlayOrigin],\n  providers: [OVERLAY_PROVIDERS, ScrollStrategyOptions],\n})\nexport class OverlayModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {ESCAPE} from '@angular/cdk/keycodes';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Optional,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Overlay} from './overlay';\nimport {OverlayConfig} from './overlay-config';\nimport {OverlayRef} from './overlay-ref';\nimport {\n  ConnectedOverlayPositionChange,\n  ConnectionPositionPair,\n} from './position/connected-position';\nimport {ConnectedPositionStrategy} from './position/connected-position-strategy';\nimport {RepositionScrollStrategy, ScrollStrategy} from './scroll/index';\n\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n  new ConnectionPositionPair(\n      {originX: 'start', originY: 'bottom'},\n      {overlayX: 'start', overlayY: 'top'}),\n  new ConnectionPositionPair(\n      {originX: 'start', originY: 'top'},\n      {overlayX: 'start', overlayY: 'bottom'}),\n  new ConnectionPositionPair(\n    {originX: 'end', originY: 'top'},\n    {overlayX: 'end', overlayY: 'bottom'}),\n  new ConnectionPositionPair(\n    {originX: 'end', originY: 'bottom'},\n    {overlayX: 'end', overlayY: 'top'}),\n];\n\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY =\n    new InjectionToken<() => ScrollStrategy>('cdk-connected-overlay-scroll-strategy');\n\n/** @docs-private */\nexport function CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay: Overlay):\n    () => RepositionScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/** @docs-private */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n  exportAs: 'cdkOverlayOrigin',\n})\nexport class CdkOverlayOrigin {\n  constructor(\n      /** Reference to the element on which the directive is applied. */\n      public elementRef: ElementRef) { }\n}\n\n\n/**\n * Directive to facilitate declarative creation of an Overlay using a ConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n  exportAs: 'cdkConnectedOverlay'\n})\nexport class CdkConnectedOverlay implements OnDestroy, OnChanges {\n  private _overlayRef: OverlayRef;\n  private _templatePortal: TemplatePortal;\n  private _hasBackdrop = false;\n  private _lockPosition = false;\n  private _backdropSubscription = Subscription.EMPTY;\n  private _offsetX: number = 0;\n  private _offsetY: number = 0;\n  private _position: ConnectedPositionStrategy;\n\n  /** Origin for the connected overlay. */\n  @Input('cdkConnectedOverlayOrigin') origin: CdkOverlayOrigin;\n\n  /** Registered connected position pairs. */\n  @Input('cdkConnectedOverlayPositions') positions: ConnectionPositionPair[];\n\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  @Input('cdkConnectedOverlayOffsetX')\n  get offsetX(): number { return this._offsetX; }\n  set offsetX(offsetX: number) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._position.withOffsetX(offsetX);\n    }\n  }\n\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  @Input('cdkConnectedOverlayOffsetY')\n  get offsetY() { return this._offsetY; }\n  set offsetY(offsetY: number) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._position.withOffsetY(offsetY);\n    }\n  }\n\n  /** The width of the overlay panel. */\n  @Input('cdkConnectedOverlayWidth') width: number | string;\n\n  /** The height of the overlay panel. */\n  @Input('cdkConnectedOverlayHeight') height: number | string;\n\n  /** The min width of the overlay panel. */\n  @Input('cdkConnectedOverlayMinWidth') minWidth: number | string;\n\n  /** The min height of the overlay panel. */\n  @Input('cdkConnectedOverlayMinHeight') minHeight: number | string;\n\n  /** The custom class to be set on the backdrop element. */\n  @Input('cdkConnectedOverlayBackdropClass') backdropClass: string;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  @Input('cdkConnectedOverlayScrollStrategy') scrollStrategy: ScrollStrategy =\n      this._scrollStrategy();\n\n  /** Whether the overlay is open. */\n  @Input('cdkConnectedOverlayOpen') open: boolean = false;\n\n  /** Whether or not the overlay should attach a backdrop. */\n  @Input('cdkConnectedOverlayHasBackdrop')\n  get hasBackdrop() { return this._hasBackdrop; }\n  set hasBackdrop(value: any) { this._hasBackdrop = coerceBooleanProperty(value); }\n\n  /** Whether or not the overlay should be locked when scrolling. */\n  @Input('cdkConnectedOverlayLockPosition')\n  get lockPosition() { return this._lockPosition; }\n  set lockPosition(value: any) { this._lockPosition = coerceBooleanProperty(value); }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('origin')\n  get _deprecatedOrigin(): CdkOverlayOrigin { return this.origin; }\n  set _deprecatedOrigin(_origin: CdkOverlayOrigin) { this.origin = _origin; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('positions')\n  get _deprecatedPositions(): ConnectionPositionPair[] { return this.positions; }\n  set _deprecatedPositions(_positions: ConnectionPositionPair[]) { this.positions = _positions; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('offsetX')\n  get _deprecatedOffsetX(): number { return this.offsetX; }\n  set _deprecatedOffsetX(_offsetX: number) { this.offsetX = _offsetX; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('offsetY')\n  get _deprecatedOffsetY(): number { return this.offsetY; }\n  set _deprecatedOffsetY(_offsetY: number) { this.offsetY = _offsetY; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('width')\n  get _deprecatedWidth(): number | string { return this.width; }\n  set _deprecatedWidth(_width: number | string) { this.width = _width; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('height')\n  get _deprecatedHeight(): number | string { return this.height; }\n  set _deprecatedHeight(_height: number | string) { this.height = _height; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('minWidth')\n  get _deprecatedMinWidth(): number | string { return this.minWidth; }\n  set _deprecatedMinWidth(_minWidth: number | string) { this.minWidth = _minWidth; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('minHeight')\n  get _deprecatedMinHeight(): number | string { return this.minHeight; }\n  set _deprecatedMinHeight(_minHeight: number | string) { this.minHeight = _minHeight; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('backdropClass')\n  get _deprecatedBackdropClass(): string { return this.backdropClass; }\n  set _deprecatedBackdropClass(_backdropClass: string) { this.backdropClass = _backdropClass; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('scrollStrategy')\n  get _deprecatedScrollStrategy(): ScrollStrategy { return this.scrollStrategy; }\n  set _deprecatedScrollStrategy(_scrollStrategy: ScrollStrategy) {\n    this.scrollStrategy = _scrollStrategy;\n  }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('open')\n  get _deprecatedOpen(): boolean { return this.open; }\n  set _deprecatedOpen(_open: boolean) { this.open = _open; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('hasBackdrop')\n  get _deprecatedHasBackdrop() { return this.hasBackdrop; }\n  set _deprecatedHasBackdrop(_hasBackdrop: any) { this.hasBackdrop = _hasBackdrop; }\n\n  /** Event emitted when the backdrop is clicked. */\n  @Output() backdropClick = new EventEmitter<MouseEvent>();\n\n  /** Event emitted when the position has changed. */\n  @Output() positionChange = new EventEmitter<ConnectedOverlayPositionChange>();\n\n  /** Event emitted when the overlay has been attached. */\n  @Output() attach = new EventEmitter<void>();\n\n  /** Event emitted when the overlay has been detached. */\n  @Output() detach = new EventEmitter<void>();\n\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n\n  constructor(\n      private _overlay: Overlay,\n      templateRef: TemplateRef<any>,\n      viewContainerRef: ViewContainerRef,\n      @Inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY) private _scrollStrategy,\n      @Optional() private _dir: Directionality) {\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n  }\n\n  /** The associated overlay reference. */\n  get overlayRef(): OverlayRef {\n    return this._overlayRef;\n  }\n\n  /** The element's layout direction. */\n  get dir(): Direction {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n\n  ngOnDestroy() {\n    this._destroyOverlay();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._position) {\n      if (changes['positions'] || changes['_deprecatedPositions']) {\n        this._position.withPositions(this.positions);\n      }\n\n      if (changes['lockPosition']) {\n        this._position.withLockedPosition(this.lockPosition);\n      }\n\n      if (changes['origin'] || changes['_deprecatedOrigin']) {\n        this._position.setOrigin(this.origin.elementRef);\n\n        if (this.open) {\n          this._position.apply();\n        }\n      }\n    }\n\n    if (changes['open'] || changes['_deprecatedOpen']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n\n  /** Creates an overlay */\n  private _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n\n    this._overlayRef = this._overlay.create(this._buildConfig());\n  }\n\n  /** Builds the overlay config based on the directive's inputs */\n  private _buildConfig(): OverlayConfig {\n    const positionStrategy = this._position = this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop\n    });\n\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n\n    return overlayConfig;\n  }\n\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  private _createPositionStrategy(): ConnectedPositionStrategy {\n    const primaryPosition = this.positions[0];\n    const originPoint = {originX: primaryPosition.originX, originY: primaryPosition.originY};\n    const overlayPoint = {overlayX: primaryPosition.overlayX, overlayY: primaryPosition.overlayY};\n    const strategy = this._overlay.position()\n      .connectedTo(this.origin.elementRef, originPoint, overlayPoint)\n      .withOffsetX(this.offsetX)\n      .withOffsetY(this.offsetY)\n      .withLockedPosition(this.lockPosition);\n\n    for (let i = 1; i < this.positions.length; i++) {\n      strategy.withFallbackPosition(\n          {originX: this.positions[i].originX, originY: this.positions[i].originY},\n          {overlayX: this.positions[i].overlayX, overlayY: this.positions[i].overlayY}\n      );\n    }\n\n    strategy.onPositionChange.subscribe(pos => this.positionChange.emit(pos));\n\n    return strategy;\n  }\n\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n  private _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n\n      this._overlayRef!.keydownEvents().subscribe((event: KeyboardEvent) => {\n        if (event.keyCode === ESCAPE) {\n          this._detachOverlay();\n        }\n      });\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight,\n      });\n    }\n\n    this._position.withDirection(this.dir);\n    this._overlayRef.setDirection(this.dir);\n\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n      this.attach.emit();\n    }\n\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    }\n  }\n\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n  private _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n      this.detach.emit();\n    }\n\n    this._backdropSubscription.unsubscribe();\n  }\n\n  /** Destroys the overlay created by this directive. */\n  private _destroyOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n\n    this._backdropSubscription.unsubscribe();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentFactoryResolver,\n  Injectable,\n  ApplicationRef,\n  Injector,\n  NgZone,\n  Inject,\n} from '@angular/core';\nimport {DomPortalOutlet} from '@angular/cdk/portal';\nimport {OverlayConfig} from './overlay-config';\nimport {OverlayRef} from './overlay-ref';\nimport {OverlayPositionBuilder} from './position/overlay-position-builder';\nimport {OverlayKeyboardDispatcher} from './keyboard/overlay-keyboard-dispatcher';\nimport {OverlayContainer} from './overlay-container';\nimport {ScrollStrategyOptions} from './scroll/index';\nimport {DOCUMENT} from '@angular/common';\n\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\n@Injectable()\nexport class Overlay {\n  constructor(\n              /** Scrolling strategies that can be used when creating an overlay. */\n              public scrollStrategies: ScrollStrategyOptions,\n              private _overlayContainer: OverlayContainer,\n              private _componentFactoryResolver: ComponentFactoryResolver,\n              private _positionBuilder: OverlayPositionBuilder,\n              private _keyboardDispatcher: OverlayKeyboardDispatcher,\n              private _appRef: ApplicationRef,\n              private _injector: Injector,\n              private _ngZone: NgZone,\n              @Inject(DOCUMENT) private _document: any) { }\n\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config?: OverlayConfig): OverlayRef {\n    const pane = this._createPaneElement();\n    const portalOutlet = this._createPortalOutlet(pane);\n\n    return new OverlayRef(\n      portalOutlet,\n      pane,\n      new OverlayConfig(config),\n      this._ngZone,\n      this._keyboardDispatcher,\n      this._document\n    );\n  }\n\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position(): OverlayPositionBuilder {\n    return this._positionBuilder;\n  }\n\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  private _createPaneElement(): HTMLElement {\n    const pane = this._document.createElement('div');\n\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    this._overlayContainer.getContainerElement().appendChild(pane);\n\n    return pane;\n  }\n\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  private _createPortalOutlet(pane: HTMLElement): DomPortalOutlet {\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector);\n  }\n\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, InjectionToken, Inject, Optional, SkipSelf, OnDestroy} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\n\n\n/** Container inside which all overlays will render. */\n@Injectable()\nexport class OverlayContainer implements OnDestroy {\n  protected _containerElement: HTMLElement;\n\n  constructor(@Inject(DOCUMENT) private _document: any) {}\n\n  ngOnDestroy() {\n    if (this._containerElement && this._containerElement.parentNode) {\n      this._containerElement.parentNode.removeChild(this._containerElement);\n    }\n  }\n\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time  it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement(): HTMLElement {\n    if (!this._containerElement) { this._createContainer(); }\n    return this._containerElement;\n  }\n\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  protected _createContainer(): void {\n    const container = this._document.createElement('div');\n\n    container.classList.add('cdk-overlay-container');\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n}\n\n/** @docs-private */\nexport function OVERLAY_CONTAINER_PROVIDER_FACTORY(parentContainer: OverlayContainer,\n  _document: any) {\n  return parentContainer || new OverlayContainer(_document);\n}\n\n/** @docs-private */\nexport const OVERLAY_CONTAINER_PROVIDER = {\n  // If there is already an OverlayContainer available, use that. Otherwise, provide a new one.\n  provide: OverlayContainer,\n  deps: [\n    [new Optional(), new SkipSelf(), OverlayContainer],\n    DOCUMENT as InjectionToken<any> // We need to use the InjectionToken somewhere to keep TS happy\n  ],\n  useFactory: OVERLAY_CONTAINER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Inject, InjectionToken, Optional, SkipSelf, OnDestroy} from '@angular/core';\nimport {OverlayRef} from '../overlay-ref';\nimport {Subscription} from 'rxjs/Subscription';\nimport {filter} from 'rxjs/operators/filter';\nimport {fromEvent} from 'rxjs/observable/fromEvent';\nimport {DOCUMENT} from '@angular/common';\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable()\nexport class OverlayKeyboardDispatcher implements OnDestroy {\n\n  /** Currently attached overlays in the order they were attached. */\n  _attachedOverlays: OverlayRef[] = [];\n\n  private _keydownEventSubscription: Subscription | null;\n\n  constructor(@Inject(DOCUMENT) private _document: any) {}\n\n  ngOnDestroy() {\n    this._unsubscribeFromKeydownEvents();\n  }\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef: OverlayRef): void {\n    // Lazily start dispatcher once first overlay is added\n    if (!this._keydownEventSubscription) {\n      this._subscribeToKeydownEvents();\n    }\n\n    this._attachedOverlays.push(overlayRef);\n  }\n\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef: OverlayRef): void {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this._unsubscribeFromKeydownEvents();\n    }\n  }\n\n  /**\n   * Subscribe to keydown events that land on the body and dispatch those\n   * events to the appropriate overlay.\n   */\n  private _subscribeToKeydownEvents(): void {\n    const bodyKeydownEvents = fromEvent<KeyboardEvent>(this._document.body, 'keydown', true);\n\n    this._keydownEventSubscription = bodyKeydownEvents.pipe(\n      filter(() => !!this._attachedOverlays.length)\n    ).subscribe(event => {\n      // Dispatch keydown event to the correct overlay.\n      this._selectOverlayFromEvent(event)._keydownEvents.next(event);\n    });\n  }\n\n  /** Removes the global keydown subscription. */\n  private _unsubscribeFromKeydownEvents(): void {\n    if (this._keydownEventSubscription) {\n      this._keydownEventSubscription.unsubscribe();\n      this._keydownEventSubscription = null;\n    }\n  }\n\n  /** Select the appropriate overlay from a keydown event. */\n  private _selectOverlayFromEvent(event: KeyboardEvent): OverlayRef {\n    // Check if any overlays contain the event\n    const targetedOverlay = this._attachedOverlays.find(overlay => {\n      return overlay.overlayElement === event.target ||\n          overlay.overlayElement.contains(event.target as HTMLElement);\n    });\n\n    // Use the overlay if it exists, otherwise choose the most recently attached one\n    return targetedOverlay || this._attachedOverlays[this._attachedOverlays.length - 1];\n  }\n\n}\n\n/** @docs-private */\nexport function OVERLAY_KEYBOARD_DISPATCHER_PROVIDER_FACTORY(\n    dispatcher: OverlayKeyboardDispatcher, _document: any) {\n  return dispatcher || new OverlayKeyboardDispatcher(_document);\n}\n\n/** @docs-private */\nexport const OVERLAY_KEYBOARD_DISPATCHER_PROVIDER = {\n  // If there is already an OverlayKeyboardDispatcher available, use that.\n  // Otherwise, provide a new one.\n  provide: OverlayKeyboardDispatcher,\n  deps: [\n    [new Optional(), new SkipSelf(), OverlayKeyboardDispatcher],\n\n    // Coerce to `InjectionToken` so that the `deps` match the \"shape\"\n    // of the type expected by Angular\n    DOCUMENT as InjectionToken<any>\n  ],\n  useFactory: OVERLAY_KEYBOARD_DISPATCHER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementRef, Injectable, Inject} from '@angular/core';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {ConnectedPositionStrategy} from './connected-position-strategy';\nimport {GlobalPositionStrategy} from './global-position-strategy';\nimport {OverlayConnectionPosition, OriginConnectionPosition} from './connected-position';\nimport {DOCUMENT} from '@angular/common';\n\n\n/** Builder for overlay position strategy. */\n@Injectable()\nexport class OverlayPositionBuilder {\n  constructor(private _viewportRuler: ViewportRuler,\n              @Inject(DOCUMENT) private _document: any) { }\n\n  /**\n   * Creates a global position strategy.\n   */\n  global(): GlobalPositionStrategy {\n    return new GlobalPositionStrategy(this._document);\n  }\n\n  /**\n   * Creates a relative position strategy.\n   * @param elementRef\n   * @param originPos\n   * @param overlayPos\n   */\n  connectedTo(\n      elementRef: ElementRef,\n      originPos: OriginConnectionPosition,\n      overlayPos: OverlayConnectionPosition): ConnectedPositionStrategy {\n\n    return new ConnectedPositionStrategy(originPos, overlayPos, elementRef,\n        this._viewportRuler, this._document);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position-strategy';\nimport {OverlayRef} from '../overlay-ref';\n\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nexport class GlobalPositionStrategy implements PositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  private _overlayRef: OverlayRef;\n\n  private _cssPosition = 'static';\n  private _topOffset = '';\n  private _bottomOffset = '';\n  private _leftOffset = '';\n  private _rightOffset = '';\n  private _alignItems = '';\n  private _justifyContent = '';\n  private _width = '';\n  private _height = '';\n\n  /** A lazily-created wrapper for the overlay element that is used as a flex container. */\n  private _wrapper: HTMLElement | null = null;\n\n  constructor(private _document: any) {}\n\n  attach(overlayRef: OverlayRef): void {\n    const config = overlayRef.getConfig();\n\n    this._overlayRef = overlayRef;\n\n    if (this._width && !config.width) {\n      overlayRef.updateSize({width: this._width});\n    }\n\n    if (this._height && !config.height) {\n      overlayRef.updateSize({height: this._height});\n    }\n  }\n\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value: string = ''): this {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value: string = ''): this {\n    this._rightOffset = '';\n    this._leftOffset = value;\n    this._justifyContent = 'flex-start';\n    return this;\n  }\n\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value: string = ''): this {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value: string = ''): this {\n    this._leftOffset = '';\n    this._rightOffset = value;\n    this._justifyContent = 'flex-end';\n    return this;\n  }\n\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @deletion-target 7.0.0\n   */\n  width(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({width: value});\n    } else {\n      this._width = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @deletion-target 7.0.0\n   */\n  height(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({height: value});\n    } else {\n      this._height = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset: string = ''): this {\n    this.left(offset);\n    this._justifyContent = 'center';\n    return this;\n  }\n\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset: string = ''): this {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   *\n   * @returns Resolved when the styles have been applied.\n   */\n  apply(): void {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef.hasAttached()) {\n      return;\n    }\n\n    const element = this._overlayRef.overlayElement;\n\n    if (!this._wrapper && element.parentNode) {\n      this._wrapper = this._document.createElement('div');\n      this._wrapper!.classList.add('cdk-global-overlay-wrapper');\n      element.parentNode.insertBefore(this._wrapper!, element);\n      this._wrapper!.appendChild(element);\n    }\n\n    const styles = element.style;\n    const parentStyles = (element.parentNode as HTMLElement).style;\n    const config = this._overlayRef.getConfig();\n\n    styles.position = this._cssPosition;\n    styles.marginLeft = config.width === '100%' ? '0' : this._leftOffset;\n    styles.marginTop = config.height === '100%' ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = this._rightOffset;\n\n    parentStyles.justifyContent = config.width === '100%' ? 'flex-start' : this._justifyContent;\n    parentStyles.alignItems = config.height === '100%' ? 'flex-start' : this._alignItems;\n  }\n\n  /** Removes the wrapper element from the DOM. */\n  dispose(): void {\n    if (this._wrapper && this._wrapper.parentNode) {\n      this._wrapper.parentNode.removeChild(this._wrapper);\n      this._wrapper = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position-strategy';\nimport {ElementRef} from '@angular/core';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {\n  ConnectionPositionPair,\n  OriginConnectionPosition,\n  OverlayConnectionPosition,\n  ConnectedOverlayPositionChange,\n  ScrollingVisibility,\n} from './connected-position';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Observable} from 'rxjs/Observable';\nimport {CdkScrollable} from '@angular/cdk/scrolling';\nimport {isElementScrolledOutsideView, isElementClippedByScrolling} from './scroll-clip';\nimport {OverlayRef} from '../overlay-ref';\n\n\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nexport class ConnectedPositionStrategy implements PositionStrategy {\n  /** Layout direction of the position strategy. */\n  private _dir = 'ltr';\n\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  private _offsetX: number = 0;\n\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  private _offsetY: number = 0;\n\n  /** The Scrollable containers used to check scrollable view properties on position change. */\n  private scrollables: CdkScrollable[] = [];\n\n  /** Subscription to viewport resize events. */\n  private _resizeSubscription = Subscription.EMPTY;\n\n  /** Whether the we're dealing with an RTL context */\n  get _isRtl() {\n    return this._dir === 'rtl';\n  }\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  _preferredPositions: ConnectionPositionPair[] = [];\n\n  /** The origin element against which the overlay will be positioned. */\n  private _origin: HTMLElement;\n\n  /** The overlay pane element. */\n  private _pane: HTMLElement;\n\n  /** The last position to have been calculated as the best fit position. */\n  private _lastConnectedPosition: ConnectionPositionPair;\n\n  /** Whether the position strategy is applied currently. */\n  private _applied = false;\n\n  /** Whether the overlay position is locked. */\n  private _positionLocked = false;\n\n  private _onPositionChange = new Subject<ConnectedOverlayPositionChange>();\n\n  /** Emits an event when the connection point changes. */\n  get onPositionChange(): Observable<ConnectedOverlayPositionChange> {\n    return this._onPositionChange.asObservable();\n  }\n\n  constructor(\n      originPos: OriginConnectionPosition,\n      overlayPos: OverlayConnectionPosition,\n      private _connectedTo: ElementRef,\n      private _viewportRuler: ViewportRuler,\n      private _document: any) {\n    this._origin = this._connectedTo.nativeElement;\n    this.withFallbackPosition(originPos, overlayPos);\n  }\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions(): ConnectionPositionPair[] {\n    return this._preferredPositions;\n  }\n\n  /** Attach this position strategy to an overlay. */\n  attach(overlayRef: OverlayRef): void {\n    this._pane = overlayRef.overlayElement;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => this.apply());\n  }\n\n  /** Disposes all resources used by the position strategy. */\n  dispose() {\n    this._applied = false;\n    this._resizeSubscription.unsubscribe();\n    this._onPositionChange.complete();\n  }\n\n  /** @docs-private */\n  detach() {\n    this._applied = false;\n    this._resizeSubscription.unsubscribe();\n  }\n\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin fits on-screen.\n   * @docs-private\n   */\n  apply(): void {\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the  old position, in order to\n    // prevent the overlay from jumping around.\n    if (this._applied && this._positionLocked && this._lastConnectedPosition) {\n      this.recalculateLastPosition();\n      return;\n    }\n\n    this._applied = true;\n\n    // We need the bounding rects for the origin and the overlay to determine how to position\n    // the overlay relative to the origin.\n    const element = this._pane;\n    const originRect = this._origin.getBoundingClientRect();\n    const overlayRect = element.getBoundingClientRect();\n\n    // We use the viewport size to determine whether a position would go off-screen.\n    const viewportSize = this._viewportRuler.getViewportSize();\n\n    // Fallback point if none of the fallbacks fit into the viewport.\n    let fallbackPoint: OverlayPoint | undefined;\n    let fallbackPosition: ConnectionPositionPair | undefined;\n\n    // We want to place the overlay in the first of the preferred positions such that the\n    // overlay fits on-screen.\n    for (let pos of this._preferredPositions) {\n      // Get the (x, y) point of connection on the origin, and then use that to get the\n      // (top, left) coordinate for the overlay at `pos`.\n      let originPoint = this._getOriginConnectionPoint(originRect, pos);\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, viewportSize, pos);\n\n      // If the overlay in the calculated position fits on-screen, put it there and we're done.\n      if (overlayPoint.fitsInViewport) {\n        this._setElementPosition(element, overlayRect, overlayPoint, pos);\n\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastConnectedPosition = pos;\n\n        return;\n      } else if (!fallbackPoint || fallbackPoint.visibleArea < overlayPoint.visibleArea) {\n        fallbackPoint = overlayPoint;\n        fallbackPosition = pos;\n      }\n    }\n\n    // If none of the preferred positions were in the viewport, take the one\n    // with the largest visible area.\n    this._setElementPosition(element, overlayRect, fallbackPoint!, fallbackPosition!);\n  }\n\n  /**\n   * Re-positions the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  recalculateLastPosition(): void {\n    // If the overlay has never been positioned before, do nothing.\n    if (!this._lastConnectedPosition) {\n      return;\n    }\n\n    const originRect = this._origin.getBoundingClientRect();\n    const overlayRect = this._pane.getBoundingClientRect();\n    const viewportSize = this._viewportRuler.getViewportSize();\n    const lastPosition = this._lastConnectedPosition || this._preferredPositions[0];\n\n    let originPoint = this._getOriginConnectionPoint(originRect, lastPosition);\n    let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, viewportSize, lastPosition);\n    this._setElementPosition(this._pane, overlayRect, overlayPoint, lastPosition);\n  }\n\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables: CdkScrollable[]) {\n    this.scrollables = scrollables;\n  }\n\n  /**\n   * Adds a new preferred fallback position.\n   * @param originPos\n   * @param overlayPos\n   */\n  withFallbackPosition(\n      originPos: OriginConnectionPosition,\n      overlayPos: OverlayConnectionPosition,\n      offsetX?: number,\n      offsetY?: number): this {\n\n    const position = new ConnectionPositionPair(originPos, overlayPos, offsetX, offsetY);\n    this._preferredPositions.push(position);\n    return this;\n  }\n\n  /**\n   * Sets the layout direction so the overlay's position can be adjusted to match.\n   * @param dir New layout direction.\n   */\n  withDirection(dir: 'ltr' | 'rtl'): this {\n    this._dir = dir;\n    return this;\n  }\n\n  /**\n   * Sets an offset for the overlay's connection point on the x-axis\n   * @param offset New offset in the X axis.\n   */\n  withOffsetX(offset: number): this {\n    this._offsetX = offset;\n    return this;\n  }\n\n  /**\n   * Sets an offset for the overlay's connection point on the y-axis\n   * @param  offset New offset in the Y axis.\n   */\n  withOffsetY(offset: number): this {\n    this._offsetY = offset;\n    return this;\n  }\n\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked: boolean): this {\n    this._positionLocked = isLocked;\n    return this;\n  }\n\n  /**\n   * Overwrites the current set of positions with an array of new ones.\n   * @param positions Position pairs to be set on the strategy.\n   */\n  withPositions(positions: ConnectionPositionPair[]): this {\n    this._preferredPositions = positions.slice();\n    return this;\n  }\n\n  /**\n   * Sets the origin element, relative to which to position the overlay.\n   * @param origin Reference to the new origin element.\n   */\n  setOrigin(origin: ElementRef): this {\n    this._origin = origin.nativeElement;\n    return this;\n  }\n\n  /**\n   * Gets the horizontal (x) \"start\" dimension based on whether the overlay is in an RTL context.\n   * @param rect\n   */\n  private _getStartX(rect: ClientRect): number {\n    return this._isRtl ? rect.right : rect.left;\n  }\n\n  /**\n   * Gets the horizontal (x) \"end\" dimension based on whether the overlay is in an RTL context.\n   * @param rect\n   */\n  private _getEndX(rect: ClientRect): number {\n    return this._isRtl ? rect.left : rect.right;\n  }\n\n\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   * @param originRect\n   * @param pos\n   */\n  private _getOriginConnectionPoint(originRect: ClientRect, pos: ConnectionPositionPair): Point {\n    const originStartX = this._getStartX(originRect);\n    const originEndX = this._getEndX(originRect);\n\n    let x: number;\n    if (pos.originX == 'center') {\n      x = originStartX + (originRect.width / 2);\n    } else {\n      x = pos.originX == 'start' ? originStartX : originEndX;\n    }\n\n    let y: number;\n    if (pos.originY == 'center') {\n      y = originRect.top + (originRect.height / 2);\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n\n    return {x, y};\n  }\n\n\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected, as well as how much of the element\n   * would be inside the viewport at that position.\n   */\n  private _getOverlayPoint(\n      originPoint: Point,\n      overlayRect: ClientRect,\n      viewportSize: {width: number; height: number},\n      pos: ConnectionPositionPair): OverlayPoint {\n    // Calculate the (overlayStartX, overlayStartY), the start of the potential overlay position\n    // relative to the origin point.\n    let overlayStartX: number;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl ? 0 : -overlayRect.width;\n    }\n\n    let overlayStartY: number;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n\n    // The (x, y) offsets of the overlay based on the current position.\n    let offsetX = typeof pos.offsetX === 'undefined' ? this._offsetX : pos.offsetX;\n    let offsetY = typeof pos.offsetY === 'undefined' ? this._offsetY : pos.offsetY;\n\n    // The (x, y) coordinates of the overlay.\n    let x = originPoint.x + overlayStartX + offsetX;\n    let y = originPoint.y + overlayStartY + offsetY;\n\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = (x + overlayRect.width) - viewportSize.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = (y + overlayRect.height) - viewportSize.height;\n\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlayRect.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlayRect.height, topOverflow, bottomOverflow);\n\n    // The area of the element that's within the viewport.\n    let visibleArea = visibleWidth * visibleHeight;\n    let fitsInViewport = (overlayRect.width * overlayRect.height) === visibleArea;\n\n    return {x, y, fitsInViewport, visibleArea};\n  }\n\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  private _getScrollVisibility(overlay: HTMLElement): ScrollingVisibility {\n    const originBounds = this._origin.getBoundingClientRect();\n    const overlayBounds = overlay.getBoundingClientRect();\n    const scrollContainerBounds =\n        this.scrollables.map(s => s.getElementRef().nativeElement.getBoundingClientRect());\n\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n    };\n  }\n\n  /** Physically positions the overlay element to the given coordinate. */\n  private _setElementPosition(\n      element: HTMLElement,\n      overlayRect: ClientRect,\n      overlayPoint: Point,\n      pos: ConnectionPositionPair) {\n\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear above\n    // or below the origin and the direction in which the element will expand.\n    let verticalStyleProperty = pos.overlayY === 'bottom' ? 'bottom' : 'top';\n\n    // When using `bottom`, we adjust the y position such that it is the distance\n    // from the bottom of the viewport rather than the top.\n    let y = verticalStyleProperty === 'top' ?\n        overlayPoint.y :\n        this._document.documentElement.clientHeight - (overlayPoint.y + overlayRect.height);\n\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty: string;\n    if (this._dir === 'rtl') {\n      horizontalStyleProperty = pos.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = pos.overlayX === 'end' ? 'right' : 'left';\n    }\n\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    let x = horizontalStyleProperty === 'left' ?\n      overlayPoint.x :\n      this._document.documentElement.clientWidth - (overlayPoint.x + overlayRect.width);\n\n\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    ['top', 'bottom', 'left', 'right'].forEach(p => element.style[p] = null);\n\n    element.style[verticalStyleProperty] = `${y}px`;\n    element.style[horizontalStyleProperty] = `${x}px`;\n\n    // Notify that the position has been changed along with its change properties.\n    const scrollableViewProperties = this._getScrollVisibility(element);\n    const positionChange = new ConnectedOverlayPositionChange(pos, scrollableViewProperties);\n    this._onPositionChange.next(positionChange);\n  }\n\n  /**\n   * Subtracts the amount that an element is overflowing on an axis from it's length.\n   */\n  private _subtractOverflows(length: number, ...overflows: number[]): number {\n    return overflows.reduce((currentValue: number, currentOverflow: number) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n}\n\n/** A simple (x, y) coordinate. */\ninterface Point {\n  x: number;\n  y: number;\n}\n\n/**\n * Expands the simple (x, y) coordinate by adding info about whether the\n * element would fit inside the viewport at that position, as well as\n * how much of the element would be visible.\n */\ninterface OverlayPoint extends Point {\n  visibleArea: number;\n  fitsInViewport: boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction} from '@angular/cdk/bidi';\nimport {ComponentPortal, Portal, PortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {ComponentRef, EmbeddedViewRef, NgZone} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {take} from 'rxjs/operators/take';\nimport {Subject} from 'rxjs/Subject';\nimport {OverlayKeyboardDispatcher} from './keyboard/overlay-keyboard-dispatcher';\nimport {OverlayConfig} from './overlay-config';\n\n\n/** An object where all of its properties cannot be written. */\nexport type ImmutableObject<T> = {\n  readonly [P in keyof T]: T[P];\n};\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nexport class OverlayRef implements PortalOutlet {\n  private _backdropElement: HTMLElement | null = null;\n  private _backdropClick: Subject<MouseEvent> = new Subject();\n  private _attachments = new Subject<void>();\n  private _detachments = new Subject<void>();\n\n  /** Stream of keydown events dispatched to this overlay. */\n  _keydownEvents = new Subject<KeyboardEvent>();\n\n  constructor(\n      private _portalOutlet: PortalOutlet,\n      private _pane: HTMLElement,\n      private _config: ImmutableObject<OverlayConfig>,\n      private _ngZone: NgZone,\n      private _keyboardDispatcher: OverlayKeyboardDispatcher,\n      private _document: Document) {\n\n    if (_config.scrollStrategy) {\n      _config.scrollStrategy.attach(this);\n    }\n  }\n\n  /** The overlay's HTML element */\n  get overlayElement(): HTMLElement {\n    return this._pane;\n  }\n\n  /** The overlay's backdrop HTML element. */\n  get backdropElement(): HTMLElement | null {\n    return this._backdropElement;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal: Portal<any>): any {\n    let attachResult = this._portalOutlet.attach(portal);\n\n    if (this._config.positionStrategy) {\n      this._config.positionStrategy.attach(this);\n    }\n\n    // Update the pane element with the given configuration.\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n\n    if (this._config.scrollStrategy) {\n      this._config.scrollStrategy.enable();\n    }\n\n    // Update the position once the zone is stable so that the overlay will be fully rendered\n    // before attempting to position it, as the position may depend on the size of the rendered\n    // content.\n    this._ngZone.onStable.asObservable().pipe(take(1)).subscribe(() => {\n      // The overlay could've been detached before the zone has stabilized.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    });\n\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n\n    if (this._config.panelClass) {\n      // We can't do a spread here, because IE doesn't support setting multiple classes.\n      if (Array.isArray(this._config.panelClass)) {\n        this._config.panelClass.forEach(cls => this._pane.classList.add(cls));\n      } else {\n        this._pane.classList.add(this._config.panelClass);\n      }\n    }\n\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n\n    return attachResult;\n  }\n\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach(): any {\n    if (!this.hasAttached()) {\n      return;\n    }\n\n    this.detachBackdrop();\n\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n\n    if (this._config.positionStrategy && this._config.positionStrategy.detach) {\n      this._config.positionStrategy.detach();\n    }\n\n    if (this._config.scrollStrategy) {\n      this._config.scrollStrategy.disable();\n    }\n\n    const detachmentResult = this._portalOutlet.detach();\n\n    // Only emit after everything is detached.\n    this._detachments.next();\n\n    // Remove this overlay from keyboard dispatcher tracking\n    this._keyboardDispatcher.remove(this);\n\n    return detachmentResult;\n  }\n\n  /** Cleans up the overlay from the DOM. */\n  dispose(): void {\n    const isAttached = this.hasAttached();\n\n    if (this._config.positionStrategy) {\n      this._config.positionStrategy.dispose();\n    }\n\n    if (this._config.scrollStrategy) {\n      this._config.scrollStrategy.disable();\n    }\n\n    this.detachBackdrop();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n\n    if (isAttached) {\n      this._detachments.next();\n    }\n\n    this._detachments.complete();\n  }\n\n  /** Whether the overlay has attached content. */\n  hasAttached(): boolean {\n    return this._portalOutlet.hasAttached();\n  }\n\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick(): Observable<MouseEvent> {\n    return this._backdropClick.asObservable();\n  }\n\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments(): Observable<void> {\n    return this._attachments.asObservable();\n  }\n\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments(): Observable<void> {\n    return this._detachments.asObservable();\n  }\n\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents(): Observable<KeyboardEvent> {\n    return this._keydownEvents.asObservable();\n  }\n\n  /** Gets the the current overlay configuration, which is immutable. */\n  getConfig(): OverlayConfig {\n    return this._config;\n  }\n\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._config.positionStrategy) {\n      this._config.positionStrategy.apply();\n    }\n  }\n\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig: OverlaySizeConfig) {\n    this._config = {...this._config, ...sizeConfig};\n    this._updateElementSize();\n  }\n\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir: Direction) {\n    this._config = {...this._config, direction: dir};\n    this._updateElementDirection();\n  }\n\n  /** Updates the text direction of the overlay panel. */\n  private _updateElementDirection() {\n    this._pane.setAttribute('dir', this._config.direction!);\n  }\n\n  /** Updates the size of the overlay element based on the overlay config. */\n  private _updateElementSize() {\n    if (this._config.width || this._config.width === 0) {\n      this._pane.style.width = formatCssUnit(this._config.width);\n    }\n\n    if (this._config.height || this._config.height === 0) {\n      this._pane.style.height = formatCssUnit(this._config.height);\n    }\n\n    if (this._config.minWidth || this._config.minWidth === 0) {\n      this._pane.style.minWidth = formatCssUnit(this._config.minWidth);\n    }\n\n    if (this._config.minHeight || this._config.minHeight === 0) {\n      this._pane.style.minHeight = formatCssUnit(this._config.minHeight);\n    }\n\n    if (this._config.maxWidth || this._config.maxWidth === 0) {\n      this._pane.style.maxWidth = formatCssUnit(this._config.maxWidth);\n    }\n\n    if (this._config.maxHeight || this._config.maxHeight === 0) {\n      this._pane.style.maxHeight = formatCssUnit(this._config.maxHeight);\n    }\n  }\n\n  /** Toggles the pointer events for the overlay pane element. */\n  private _togglePointerEvents(enablePointer: boolean) {\n    this._pane.style.pointerEvents = enablePointer ? 'auto' : 'none';\n  }\n\n  /** Attaches a backdrop for this overlay. */\n  private _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n\n    this._backdropElement = this._document.createElement('div');\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n\n    if (this._config.backdropClass) {\n      this._backdropElement.classList.add(this._config.backdropClass);\n    }\n\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._pane.parentElement!.insertBefore(this._backdropElement, this._pane);\n\n    // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n    this._backdropElement.addEventListener('click',\n        (event: MouseEvent) => this._backdropClick.next(event));\n\n    // Add class to fade-in the backdrop after one frame.\n    if (typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  private _updateStackingOrder() {\n    if (this._pane.nextSibling) {\n      this._pane.parentNode!.appendChild(this._pane);\n    }\n  }\n\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop(): void {\n    let backdropToDetach = this._backdropElement;\n\n    if (backdropToDetach) {\n      let finishDetach = () => {\n        // It may not be attached to anything in certain cases (e.g. unit tests).\n        if (backdropToDetach && backdropToDetach.parentNode) {\n          backdropToDetach.parentNode.removeChild(backdropToDetach);\n        }\n\n        // It is possible that a new portal has been attached to this overlay since we started\n        // removing the backdrop. If that is the case, only clear the backdrop reference if it\n        // is still the same instance that we started to remove.\n        if (this._backdropElement == backdropToDetach) {\n          this._backdropElement = null;\n        }\n      };\n\n      backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n\n      if (this._config.backdropClass) {\n        backdropToDetach.classList.remove(this._config.backdropClass);\n      }\n\n      backdropToDetach.addEventListener('transitionend', finishDetach);\n\n      // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n      // In this case we make it unclickable and we try to remove it after a delay.\n      backdropToDetach.style.pointerEvents = 'none';\n\n      // Run this outside the Angular zone because there's nothing that Angular cares about.\n      // If it were to run inside the Angular zone, every test that used Overlay would have to be\n      // either async or fakeAsync.\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(finishDetach, 500);\n      });\n    }\n  }\n}\n\nfunction formatCssUnit(value: number | string) {\n  return typeof value === 'string' ? value as string : `${value}px`;\n}\n\n\n/** Size properties for an overlay. */\nexport interface OverlaySizeConfig {\n  width?: number | string;\n  height?: number | string;\n  minWidth?: number | string;\n  minHeight?: number | string;\n  maxWidth?: number | string;\n  maxHeight?: number | string;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Injectable, NgZone, Inject} from '@angular/core';\nimport {CloseScrollStrategy, CloseScrollStrategyConfig} from './close-scroll-strategy';\nimport {NoopScrollStrategy} from './noop-scroll-strategy';\nimport {BlockScrollStrategy} from './block-scroll-strategy';\nimport {ScrollDispatcher} from '@angular/cdk/scrolling';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  RepositionScrollStrategy,\n  RepositionScrollStrategyConfig,\n} from './reposition-scroll-strategy';\n\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\n@Injectable()\nexport class ScrollStrategyOptions {\n  private _document: Document;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewportRuler: ViewportRuler,\n    private _ngZone: NgZone,\n    @Inject(DOCUMENT) document: any) {\n      this._document = document;\n    }\n\n  /** Do nothing on scroll. */\n  noop = () => new NoopScrollStrategy();\n\n  /**\n   * Close the overlay as soon as the user scrolls.\n   * @param config Configuration to be used inside the scroll strategy.\n   */\n  close = (config?: CloseScrollStrategyConfig) => new CloseScrollStrategy(this._scrollDispatcher,\n      this._ngZone, this._viewportRuler, config)\n\n  /** Block scrolling. */\n  block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n\n  /**\n   * Update the overlay's position on scroll.\n   * @param config Configuration to be used inside the scroll strategy.\n   * Allows debouncing the reposition calls.\n   */\n  reposition = (config?: RepositionScrollStrategyConfig) => new RepositionScrollStrategy(\n      this._scrollDispatcher, this._viewportRuler, this._ngZone, config)\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgZone} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {OverlayRef} from '../overlay-ref';\nimport {ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\nimport {isElementScrolledOutsideView} from '../position/scroll-clip';\n\n/**\n * Config options for the RepositionScrollStrategy.\n */\nexport interface RepositionScrollStrategyConfig {\n  /** Time in milliseconds to throttle the scroll events. */\n  scrollThrottle?: number;\n\n  /** Whether to close the overlay once the user has scrolled away completely. */\n  autoClose?: boolean;\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nexport class RepositionScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription|null = null;\n  private _overlayRef: OverlayRef;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewportRuler: ViewportRuler,\n    private _ngZone: NgZone,\n    private _config?: RepositionScrollStrategyConfig) { }\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {width, height} = this._viewportRuler.getViewportSize();\n\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{width, height, bottom: height, right: width, top: 0, left: 0}];\n\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// TODO(jelbourn): move this to live with the rest of the scrolling code\n// TODO(jelbourn): someday replace this with IntersectionObservers\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nexport function isElementScrolledOutsideView(element: ClientRect, scrollContainers: ClientRect[]) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n\n\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nexport function isElementClippedByScrolling(element: ClientRect, scrollContainers: ClientRect[]) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\n\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nexport class BlockScrollStrategy implements ScrollStrategy {\n  private _previousHTMLStyles = { top: '', left: '' };\n  private _previousScrollPosition: { top: number, left: number };\n  private _isEnabled = false;\n  private _document: Document;\n\n  constructor(private _viewportRuler: ViewportRuler, document: any) {\n    this._document = document;\n  }\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach() { }\n\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = `${-this._previousScrollPosition.left}px`;\n      root.style.top = `${-this._previousScrollPosition.top}px`;\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const previousHtmlScrollBehavior = html.style['scrollBehavior'] || '';\n      const previousBodyScrollBehavior = body.style['scrollBehavior'] || '';\n\n      this._isEnabled = false;\n\n      html.style.left = this._previousHTMLStyles.left;\n      html.style.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      html.style['scrollBehavior'] = body.style['scrollBehavior'] = 'auto';\n\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n\n      html.style['scrollBehavior'] = previousHtmlScrollBehavior;\n      body.style['scrollBehavior'] = previousBodyScrollBehavior;\n    }\n  }\n\n  private _canBeEnabled(): boolean {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {NgZone} from '@angular/core';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {OverlayRef} from '../overlay-ref';\nimport {Subscription} from 'rxjs/Subscription';\nimport {ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\n\n/**\n * Config options for the CloseScrollStrategy.\n */\nexport interface CloseScrollStrategyConfig {\n  /** Amount of pixels the user has to scroll before the overlay is closed. */\n  threshold?: number;\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nexport class CloseScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription|null = null;\n  private _overlayRef: OverlayRef;\n  private _initialScrollPosition: number;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _ngZone: Ng<PERSON>one,\n    private _viewportRuler: ViewportRuler,\n    private _config?: CloseScrollStrategyConfig) {}\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n\n    const stream = this._scrollDispatcher.scrolled(0);\n\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config!.threshold!) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n\n  /** Detaches the overlay ref and disables the scroll strategy. */\n  private _detach = () => {\n    this.disable();\n\n    if (this._overlayRef.hasAttached()) {\n      this._ngZone.run(() => this._overlayRef.detach());\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OverlayRef} from '../overlay-ref';\n\n/**\n * Describes a strategy that will be used by an overlay to handle scroll events while it is open.\n */\nexport interface ScrollStrategy {\n  /** Enable this scroll strategy (called when the attached overlay is attached to a portal). */\n  enable: () => void;\n\n  /** Disable this scroll strategy (called when the attached overlay is detached from a portal). */\n  disable: () => void;\n\n  /** Attaches this `ScrollStrategy` to an overlay. */\n  attach: (overlayRef: OverlayRef) => void;\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nexport function getMatScrollStrategyAlreadyAttachedError(): Error {\n  return Error(`Scroll strategy has already been attached.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Horizontal dimension of a connection point on the perimeter of the origin or overlay element. */\nimport {Optional} from '@angular/core';\nexport type HorizontalConnectionPos = 'start' | 'center' | 'end';\n\n/** Vertical dimension of a connection point on the perimeter of the origin or overlay element. */\nexport type VerticalConnectionPos = 'top' | 'center' | 'bottom';\n\n\n/** A connection point on the origin element. */\nexport interface OriginConnectionPosition {\n  originX: HorizontalConnectionPos;\n  originY: VerticalConnectionPos;\n}\n\n/** A connection point on the overlay element. */\nexport interface OverlayConnectionPosition {\n  overlayX: HorizontalConnectionPos;\n  overlayY: VerticalConnectionPos;\n}\n\n/** The points of the origin element and the overlay element to connect. */\nexport class ConnectionPositionPair {\n  /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n  originX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n  originY: VerticalConnectionPos;\n  /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n  overlayX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n  overlayY: VerticalConnectionPos;\n\n  constructor(\n    origin: OriginConnectionPosition,\n    overlay: OverlayConnectionPosition,\n    public offsetX?: number,\n    public offsetY?: number) {\n\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nexport class ScrollingVisibility {\n  isOriginClipped: boolean;\n  isOriginOutsideView: boolean;\n  isOverlayClipped: boolean;\n  isOverlayOutsideView: boolean;\n}\n\n/** The change event emitted by the strategy when a fallback position is used. */\nexport class ConnectedOverlayPositionChange {\n  constructor(\n      /** The position used as a result of this change. */\n      public connectionPair: ConnectionPositionPair,\n      /** @docs-private */\n      @Optional() public scrollableViewProperties: ScrollingVisibility) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position/position-strategy';\nimport {Direction} from '@angular/cdk/bidi';\nimport {ScrollStrategy} from './scroll/scroll-strategy';\nimport {NoopScrollStrategy} from './scroll/noop-scroll-strategy';\n\n\n/** Initial configuration used when creating an overlay. */\nexport class OverlayConfig {\n  /** Strategy with which to position the overlay. */\n  positionStrategy?: PositionStrategy;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy?: ScrollStrategy = new NoopScrollStrategy();\n\n  /** Custom class to add to the overlay pane. */\n  panelClass?: string | string[] = '';\n\n  /** Whether the overlay has a backdrop. */\n  hasBackdrop?: boolean = false;\n\n  /** Custom class to add to the backdrop */\n  backdropClass?: string = 'cdk-overlay-dark-backdrop';\n\n  /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n  width?: number | string;\n\n  /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n  height?: number | string;\n\n  /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  minWidth?: number | string;\n\n  /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  minHeight?: number | string;\n\n  /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxWidth?: number | string;\n\n  /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxHeight?: number | string;\n\n  /** The direction of the text in the overlay panel. */\n  direction?: Direction = 'ltr';\n\n  constructor(config?: OverlayConfig) {\n    if (config) {\n      Object.keys(config)\n        .filter(key => typeof config[key] !== 'undefined')\n        .forEach(key => this[key] = config[key]);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\n\n/** Scroll strategy that doesn't do anything. */\nexport class NoopScrollStrategy implements ScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() { }\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() { }\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() { }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n"], "names": ["Injectable", "tslib_1.__extends", "ScrollDispatchModule", "BidiModule", "PortalModule", "NgModule", "VIEWPORT_RULER_PROVIDER", "Output", "Input", "Directionality", "Optional", "Inject", "ViewContainerRef", "TemplateRef", "Directive", "ESCAPE", "coerceBooleanProperty", "TemplatePortal", "EventEmitter", "Subscription", "ElementRef", "InjectionToken", "DOCUMENT", "NgZone", "Injector", "ApplicationRef", "ComponentFactoryResolver", "DomPortalOutlet", "SkipSelf", "filter", "fromEvent", "ViewportRuler", "Subject", "tslib_1.__assign", "take", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AmBAA;;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;KACpC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAE/E,AAAO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAC5B,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;CACxF;;AAED,AAAO,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;IACxD,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACjD,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACjB,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAChF;IACD,OAAO,CAAC,CAAC;CACZ,CAAA,AAED,AAAO,AAQN,AAED,AAAO,AAKN,AAED,AAAO,AAEN,AAED,AAAO,AAEN,AAED,AAAO,AAON,AAED,AAAO,AA0BN,AAED,AAAO,AAEN,AAED,AAAO,AASN,AAED,AAAO,AAeN,AAED,AAAO,AAIN,AAED,AAAO,AAEN,AAED,AAAO,AAUN,AAED,AAAO,AAIN,AAED,AAAO,AAIN,AAED,AAAO,AAGN,AAAC;;;;;;;;;;AD1JF,IAAA,kBAAA,kBAAA,YAAA;;;;;;;;IAEE,kBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF,GAAc,CAAd;;;;;;IAEE,kBAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF,GAAe,CAAf;;;;;;IAEE,kBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF,GAAc,CAAd;IAjBA,OAAA,kBAAA,CAAA;CAkBA,EAAA,CAAC,CAAA;;;;;;;;;;ADHD,IAAA,aAAA,kBAAA,YAAA;IAqCE,SAAF,aAAA,CAAc,MAAsB,EAApC;QAAE,IAAF,KAAA,GAAA,IAAA,CAMG;;;;QAtCH,IAAA,CAAA,cAAA,GAAoC,IAAI,kBAAkB,EAAE,CAA5D;;;;QAGA,IAAA,CAAA,UAAA,GAAmC,EAAE,CAArC;;;;QAGA,IAAA,CAAA,WAAA,GAA0B,KAAK,CAA/B;;;;QAGA,IAAA,CAAA,aAAA,GAA2B,2BAA2B,CAAtD;;;;QAqBA,IAAA,CAAA,SAAA,GAA0B,KAAK,CAA/B;QAGI,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;iBAChB,MAAM,CAAC,UAAA,GAAG,EAAnB,EAAuB,OAAA,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW,CAAzD,EAAyD,CAAC;iBACjD,OAAO,CAAC,UAAA,GAAG,EAApB,EAAwB,OAAA,KAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAA/C,EAA+C,CAAC,CAAC;SAC5C;KACF;IA1DH,OAAA,aAAA,CAAA;CA2DA,EAAA,CAAC,CAAA;;;;;;;;;;;;;;;;;;;;AD9BD,IAAA,sBAAA,kBAAA,YAAA;IAUE,SAAF,sBAAA,CACI,MAAgC,EAChC,OAAkC,EAC3B,OAHX,EAIW,OAJX,EAAA;QAGW,IAAX,CAAA,OAAkB,GAAP,OAAO,CAAlB;QACW,IAAX,CAAA,OAAkB,GAAP,OAAO,CAAlB;QAEI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;KAClC;IAjDH,OAAA,sBAAA,CAAA;CAkDA,EAAA,CAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BD,IAAA,mBAAA,kBAAA,YAAA;;;IA7EA,OAAA,mBAAA,CAAA;CAkFA,EAAA,CAAC,CAAA;;;;;IAIC,SAAF,8BAAA,CAEa,cAFb;QAIyB,wBAAzB,EAAA;QAFa,IAAb,CAAA,cAA2B,GAAd,cAAc,CAA3B;QAEyB,IAAzB,CAAA,wBAAiD,GAAxB,wBAAwB,CAAjD;KAA0E;;;QA7D1E,EAAA,IAAA,EAAa,sBAAsB,GAAnC;QAgDA,EAAA,IAAA,EAAa,mBAAmB,EAAhC,UAAA,EAAA,CAAA,EAAA,IAAA,EAaOU,sBAAQ,EAbf,EAAA,EAAA;;IA7EA,OAAA,8BAAA,CAAA;CAqFA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;AD1DA,SAAA,wCAAA,GAAA;IACE,OAAO,KAAK,CAAC,4CAA4C,CAAC,CAAC;CAC5D;;;;;;;;;;;;;;ADLD,IAAA,mBAAA,kBAAA,YAAA;IAKE,SAAF,mBAAA,CACY,iBADZ,EAEY,OAFZ,EAGY,cAHZ,EAIY,OAJZ,EAAA;QAAE,IAAF,KAAA,GAAA,IAAA,CAImD;QAHvC,IAAZ,CAAA,iBAA6B,GAAjB,iBAAiB,CAA7B;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QACY,IAAZ,CAAA,cAA0B,GAAd,cAAc,CAA1B;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QARA,IAAA,CAAA,mBAAA,GAAmD,IAAI,CAAvD;;;;QAqDA,IAAA,CAAA,OAAA,GAAoB,YAApB;YACI,KAAI,CAAC,OAAO,EAAE,CAAC;YAEf,IAAI,KAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;gBAClC,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAvB,EAA6B,OAAA,KAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAtD,EAAsD,CAAC,CAAC;aACnD;SACF,CAAH;KAnDmD;;;;;;;IAGjD,mBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,UAAsB,EAA/B;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,MAAM,wCAAwC,EAAE,CAAC;SAClD;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;KAC/B,CAAH;;;;;;IAGE,mBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAsBG;QArBC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,OAAO;SACR;QAED,qBAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE;YACxE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC,GAAG,CAAC;YAElF,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,YAAlD;gBACQ,qBAAM,cAAc,GAAG,KAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC,GAAG,CAAC;gBAE3E,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,KAAI,CAAC,sBAAsB,CAAC,uCAAlE,EAAqE,KAAI,CAAC,OAAO,GAAE,SAAS,EAAC,EAAE;oBACrF,KAAI,CAAC,OAAO,EAAE,CAAC;iBAChB;qBAAM;oBACL,KAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;iBACnC;aACF,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3D;KACF,CAAH;;;;;;IAGE,mBAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;KACF,CAAH;IA3EA,OAAA,mBAAA,CAAA;CAqFA,EAAA,CAAC,CAAA;;;;;;;;;;ADvED,IAAA,mBAAA,kBAAA,YAAA;IAME,SAAF,mBAAA,CAAsB,cAA6B,EAAE,QAAa,EAAlE;QAAsB,IAAtB,CAAA,cAAoC,GAAd,cAAc,CAAe;QALnD,IAAA,CAAA,mBAAA,GAAgC,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAArD;QAEA,IAAA,CAAA,UAAA,GAAuB,KAAK,CAA5B;QAII,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;;;;;;IAGD,mBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF,GAAc,CAAd;;;;;;IAGE,mBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;YACxB,qBAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;YAE5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,CAAC;;YAG/E,IAAI,CAAC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;YACtD,IAAI,CAAC,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;;;YAIpD,IAAI,CAAC,KAAK,CAAC,IAAI,GAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,GAA7D,IAAiE,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,GAAG,GAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,GAA3D,IAA+D,CAAC;YAC1D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;KACF,CAAH;;;;;;IAGE,mBAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,qBAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;YAC5C,qBAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACjC,qBAAM,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YACtE,qBAAM,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YAEtE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAExB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;;;YAIhD,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC;YAErE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;YAEnF,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,0BAA0B,CAAC;YAC1D,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,0BAA0B,CAAC;SAC3D;KACF,CAAH;;;;IAEU,mBAAV,CAAA,SAAA,CAAA,aAAuB;;;;;;;QAInB,qBAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QAE5C,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YACxE,OAAO,KAAK,CAAC;SACd;QAED,qBAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACjC,qBAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;;IApFpF,OAAA,mBAAA,CAAA;CAsFA,EAAA,CAAC,CAAA;;;;;;;;;;;;;;;;ADpED,SAAA,4BAAA,CAA6C,OAAmB,EAAE,gBAA8B,EAAhG;IACE,OAAO,gBAAgB,CAAC,IAAI,CAAC,UAAA,eAAe,EAA9C;QACI,qBAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC;QAC1D,qBAAM,YAAY,GAAG,OAAO,CAAC,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC;QAC1D,qBAAM,WAAW,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC;QACzD,qBAAM,YAAY,GAAG,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC;QAE1D,OAAO,YAAY,IAAI,YAAY,IAAI,WAAW,IAAI,YAAY,CAAC;KACpE,CAAC,CAAC;CACJ;;;;;;;;AAUD,SAAA,2BAAA,CAA4C,OAAmB,EAAE,gBAA8B,EAA/F;IACE,OAAO,gBAAgB,CAAC,IAAI,CAAC,UAAA,mBAAmB,EAAlD;QACI,qBAAM,YAAY,GAAG,OAAO,CAAC,GAAG,GAAG,mBAAmB,CAAC,GAAG,CAAC;QAC3D,qBAAM,YAAY,GAAG,OAAO,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACjE,qBAAM,WAAW,GAAG,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;QAC5D,qBAAM,YAAY,GAAG,OAAO,CAAC,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC;QAE/D,OAAO,YAAY,IAAI,YAAY,IAAI,WAAW,IAAI,YAAY,CAAC;KACpE,CAAC,CAAC;CACJ;;;;;;;;;;;;;;;ADjBD,IAAA,wBAAA,kBAAA,YAAA;IAIE,SAAF,wBAAA,CACY,iBADZ,EAEY,cAFZ,EAGY,OAHZ,EAIY,OAJZ,EAAA;QACY,IAAZ,CAAA,iBAA6B,GAAjB,iBAAiB,CAA7B;QACY,IAAZ,CAAA,cAA0B,GAAd,cAAc,CAA1B;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;QAPA,IAAA,CAAA,mBAAA,GAAmD,IAAI,CAAvD;KAOyD;;;;;;;IAGvD,wBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,UAAsB,EAA/B;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,MAAM,wCAAwC,EAAE,CAAC;SAClD;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;KAC/B,CAAH;;;;;;IAGE,wBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAuBG;QAtBC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,qBAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC;YAEhE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,YAArF;gBACQ,KAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;;gBAGlC,IAAI,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,OAAO,CAAC,SAAS,EAAE;oBAC1C,qBAAM,WAAW,GAAG,KAAI,CAAC,WAAW,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC;oBAC5E,IAAV,EAAA,GAAA,KAAA,CAAA,cAAA,CAAA,eAAA,EAAA,EAAiB,KAAjB,GAAA,EAAA,CAAA,KAAsB,EAAE,MAAxB,GAAA,EAAA,CAAA,MAA8B,CAA0C;;;oBAI9D,qBAAM,WAAW,GAAG,CAAC,EAAC,KAAK,EAArC,KAAqC,EAAE,MAAM,EAA7C,MAA6C,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAC,CAAC,CAAC;oBAErF,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;wBAC1D,KAAI,CAAC,OAAO,EAAE,CAAC;wBACf,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAA7B,EAAmC,OAAA,KAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAA5D,EAA4D,CAAC,CAAC;qBACnD;iBACF;aACF,CAAC,CAAC;SACJ;KACF,CAAH;;;;;;IAGE,wBAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;KACF,CAAH;IAhFA,OAAA,wBAAA,CAAA;CAiFA,EAAA,CAAC,CAAA;;;;;;;;;;;;;IDnDC,SAAF,qBAAA,CACY,iBADZ,EAEY,cAFZ,EAGY,OAHZ,EAIsB,QAJtB,EAAA;QAAE,IAAF,KAAA,GAAA,IAAA,CAMK;QALO,IAAZ,CAAA,iBAA6B,GAAjB,iBAAiB,CAA7B;QACY,IAAZ,CAAA,cAA0B,GAAd,cAAc,CAA1B;QACY,IAAZ,CAAA,OAAmB,GAAP,OAAO,CAAnB;;;;QAMA,IAAA,CAAA,IAAA,GAAS,YAAT,EAAe,OAAA,IAAI,kBAAkB,EAAE,CAAvC,EAAuC,CAAvC;;;;;QAMA,IAAA,CAAA,KAAA,GAAU,UAAC,MAAkC,EAA7C;YAAkD,OAAA,IAAI,mBAAmB,CAAC,KAAI,CAAC,iBAAiB,EAC1F,KAAI,CAAC,OAAO,EAAE,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CADhD;SACgD,CADhD;;;;QAIA,IAAA,CAAA,KAAA,GAAU,YAAV,EAAgB,OAAA,IAAI,mBAAmB,CAAC,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,SAAS,CAAC,CAA5E,EAA4E,CAA5E;;;;;;QAOA,IAAA,CAAA,UAAA,GAAe,UAAC,MAAuC,EAAvD;YAA4D,OAAA,IAAI,wBAAwB,CAClF,KAAI,CAAC,iBAAiB,EAAE,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CADxE;SACwE,CADxE;QArBM,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;;QAVL,EAAA,IAAA,EAACV,wBAAU,EAAX;;;;QAfA,EAAA,IAAA,EAAQmC,uCAAgB,GAAxB;QACA,EAAA,IAAA,EAAQJ,oCAAa,GAArB;QALA,EAAA,IAAA,EAAoBR,oBAAM,GAA1B;QA2BA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAKZ,oBAAM,EAAX,IAAA,EAAA,CAAYW,wBAAQ,EAApB,EAAA,EAAA,EAAA;;IAlCA,OAAA,qBAAA,CAAA;CA2BA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;ADAA,IAAA,UAAA,kBAAA,YAAA;IASE,SAAF,UAAA,CACc,aADd,EAEc,KAFd,EAGc,OAHd,EAIc,OAJd,EAKc,mBALd,EAMc,SANd,EAAA;QACc,IAAd,CAAA,aAA2B,GAAb,aAAa,CAA3B;QACc,IAAd,CAAA,KAAmB,GAAL,KAAK,CAAnB;QACc,IAAd,CAAA,OAAqB,GAAP,OAAO,CAArB;QACc,IAAd,CAAA,OAAqB,GAAP,OAAO,CAArB;QACc,IAAd,CAAA,mBAAiC,GAAnB,mBAAmB,CAAjC;QACc,IAAd,CAAA,SAAuB,GAAT,SAAS,CAAvB;QAdA,IAAA,CAAA,gBAAA,GAAiD,IAAI,CAArD;QACA,IAAA,CAAA,cAAA,GAAgD,IAAIU,oBAAO,EAAE,CAA7D;QACA,IAAA,CAAA,YAAA,GAAyB,IAAIA,oBAAO,EAAQ,CAA5C;QACA,IAAA,CAAA,YAAA,GAAyB,IAAIA,oBAAO,EAAQ,CAA5C;;;;QAGA,IAAA,CAAA,cAAA,GAAmB,IAAIA,oBAAO,EAAiB,CAA/C;QAUI,IAAI,OAAO,CAAC,cAAc,EAAE;YAC1B,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACrC;KACF;IAGD,MAAF,CAAA,cAAA,CAAM,UAAN,CAAA,SAAA,EAAA,gBAAoB,EAApB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;;;KAAH,CAAA,CAAG;IAGD,MAAF,CAAA,cAAA,CAAM,UAAN,CAAA,SAAA,EAAA,iBAAqB,EAArB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAC9B;;;KAAH,CAAA,CAAG;;;;;;;;;;;;;;;IAaD,UAAF,CAAA,SAAA,CAAA,MAAQ;;;;;;;IAAN,UAAO,MAAmB,EAA5B;QAAE,IAAF,KAAA,GAAA,IAAA,CAiDG;QAhDC,qBAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAErD,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC5C;;QAGD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;SACtC;;;;QAKD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,IAAI,CAACE,wBAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,YAAjE;;YAEM,IAAI,KAAI,CAAC,WAAW,EAAE,EAAE;gBACtB,KAAI,CAAC,cAAc,EAAE,CAAC;aACvB;SACF,CAAC,CAAC;;QAGH,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;;YAE3B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC1C,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,GAAG,EAA3C,EAA+C,OAAA,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAA5E,EAA4E,CAAC,CAAC;aACvE;iBAAM;gBACL,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;aACnD;SACF;;QAGD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;;QAGzB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEnC,OAAO,YAAY,CAAC;KACrB,CAAH;;;;;;;;;IAME,UAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB,OAAO;SACR;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;;;;QAKtB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE;YACzE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SACvC;QAED,qBAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;;QAGrD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;;QAGzB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEtC,OAAO,gBAAgB,CAAC;KACzB,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,qBAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SACvC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;QAE/B,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC1B;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;KAC9B,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,YAAF;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;KACzC,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,aAAe;;;;IAAb,YAAF;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;KAC3C,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,YAAF;QACI,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;KACzC,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,YAAF;QACI,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;KACzC,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,aAAe;;;;IAAb,YAAF;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;KAC3C,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,SAAW;;;;IAAT,YAAF;QACI,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB,CAAH;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,cAAgB;;;;IAAd,YAAF;QACI,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SACvC;KACF,CAAH;;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,UAAY;;;;;IAAV,UAAW,UAA6B,EAA1C;QACI,IAAI,CAAC,OAAO,GAAhBD,QAAA,CAAA,EAAA,EAAuB,IAAI,CAAC,OAAO,EAAK,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B,CAAH;;;;;;;IAGE,UAAF,CAAA,SAAA,CAAA,YAAc;;;;;IAAZ,UAAa,GAAc,EAA7B;QACI,IAAI,CAAC,OAAO,GAAhBA,QAAA,CAAA,EAAA,EAAuB,IAAI,CAAC,OAAO,EAAnC,EAAqC,SAAS,EAAE,GAAG,EAAnD,CAAoD,CAAC;QACjD,IAAI,CAAC,uBAAuB,EAAE,CAAC;KAChC,CAAH;;;;;IAGU,UAAV,CAAA,SAAA,CAAA,uBAAiC;;;;;QAC7B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,qBAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAE,CAAC;;;;;;IAIlD,UAAV,CAAA,SAAA,CAAA,kBAA4B;;;;;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;YAClD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC5D;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC9D;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YACxD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAClE;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC1D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACpE;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YACxD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAClE;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC1D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACpE;;;;;;;IAIK,UAAV,CAAA,SAAA,CAAA,oBAA8B;;;;;IAA9B,UAA+B,aAAsB,EAArD;QACI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;;;;;;IAI3D,UAAV,CAAA,SAAA,CAAA,eAAyB;;;;;;QACrB,qBAAM,YAAY,GAAG,8BAA8B,CAAC;QAEpD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAE5D,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC9B,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACjE;;;;QAID,IAAI,CAAC,KAAK,CAAC,aAAa,GAAE,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAA5E,CAAA;;;QAII,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAC1C,UAAC,KAAiB,EAD1B,EAC+B,OAAA,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAD9D,EAC8D,CAAC,CAAC;;QAG5D,IAAI,OAAO,qBAAqB,KAAK,WAAW,EAAE;YAChD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAArC;gBACQ,qBAAqB,CAAC,YAA9B;oBACU,IAAI,KAAI,CAAC,gBAAgB,EAAE;wBACzB,KAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;qBACnD;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;SACnD;;;;;;;;;;IAUK,UAAV,CAAA,SAAA,CAAA,oBAA8B;;;;;;;;;QAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;6BAChC,EAAM,IAAI,CAAC,KAAK,CAAC,UAAU,GAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAnD,CAAA;SACK;;;;;;;IAIH,UAAF,CAAA,SAAA,CAAA,cAAgB;;;;IAAd,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAqCG;QApCC,qBAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE7C,IAAI,gBAAgB,EAAE;YACpB,qBAAI,cAAY,GAAG,YAAzB;;gBAEQ,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,UAAU,EAAE;oBACnD,gBAAgB,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;iBAC3D;;;;gBAKD,IAAI,KAAI,CAAC,gBAAgB,IAAI,gBAAgB,EAAE;oBAC7C,KAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;iBAC9B;aACF,CAAC;YAEF,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;gBAC9B,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;aAC/D;YAED,gBAAgB,CAAC,gBAAgB,CAAC,eAAe,EAAE,cAAY,CAAC,CAAC;;;YAIjE,gBAAgB,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;;;;YAK9C,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAArC;gBACQ,UAAU,CAAC,cAAY,EAAE,GAAG,CAAC,CAAC;aAC/B,CAAC,CAAC;SACJ;KACF,CAAH;IAjWA,OAAA,UAAA,CAAA;CAkWA,EAAA,CAAC,CAAA;;;;;AAED,SAAA,aAAA,CAAuB,KAAsB,EAA7C;IACE,OAAO,OAAO,KAAK,KAAK,QAAQ,qBAAG,KAAe,IAAM,KAAK,GAA/D,IAAmE,CAAC;CACnE;;;;;;;;;;;;;;;;;;ADpUD,IAAA,yBAAA,kBAAA,YAAA;IA8CE,SAAF,yBAAA,CACM,SAAmC,EACnC,UAAqC,EAC7B,YAHd,EAIc,cAJd,EAKc,SALd,EAAA;QAGc,IAAd,CAAA,YAA0B,GAAZ,YAAY,CAA1B;QACc,IAAd,CAAA,cAA4B,GAAd,cAAc,CAA5B;QACc,IAAd,CAAA,SAAuB,GAAT,SAAS,CAAvB;;;;QAjDA,IAAA,CAAA,IAAA,GAAiB,KAAK,CAAtB;;;;QAGA,IAAA,CAAA,QAAA,GAA6B,CAAC,CAA9B;;;;QAGA,IAAA,CAAA,QAAA,GAA6B,CAAC,CAA9B;;;;QAGA,IAAA,CAAA,WAAA,GAAyC,EAAE,CAA3C;;;;QAGA,IAAA,CAAA,mBAAA,GAAgCd,8BAAY,CAAC,KAAK,CAAlD;;;;QAQA,IAAA,CAAA,mBAAA,GAAkD,EAAE,CAApD;;;;QAYA,IAAA,CAAA,QAAA,GAAqB,KAAK,CAA1B;;;;QAGA,IAAA,CAAA,eAAA,GAA4B,KAAK,CAAjC;QAEA,IAAA,CAAA,iBAAA,GAA8B,IAAIa,oBAAO,EAAkC,CAA3E;QAaI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QAC/C,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;KAClD;IArCD,MAAF,CAAA,cAAA,CAAM,yBAAN,CAAA,SAAA,EAAA,QAAY,EAAZ;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC;SAC5B;;;KAAH,CAAA,CAAG;IAuBD,MAAF,CAAA,cAAA,CAAM,yBAAN,CAAA,SAAA,EAAA,kBAAsB,EAAtB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;SAC9C;;;KAAH,CAAA,CAAG;IAaD,MAAF,CAAA,cAAA,CAAM,yBAAN,CAAA,SAAA,EAAA,WAAe,EAAf;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,mBAAmB,CAAC;SACjC;;;KAAH,CAAA,CAAG;;;;;;;IAGD,yBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,UAAsB,EAA/B;QAAE,IAAF,KAAA,GAAA,IAAA,CAIG;QAHC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC;QACvC,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,YAAtE,EAA4E,OAAA,KAAI,CAAC,KAAK,EAAE,CAAxF,EAAwF,CAAC,CAAC;KACvF,CAAH;;;;;;IAGE,yBAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;KACnC,CAAH;;;;;;IAGE,yBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;KACxC,CAAH;;;;;;;;;;;;IAOE,yBAAF,CAAA,SAAA,CAAA,KAAO;;;;;;IAAL,YAAF;;;;QAII,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,sBAAsB,EAAE;YACxE,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;;;QAIrB,qBAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;QAC3B,qBAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACxD,qBAAM,WAAW,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;;QAGpD,qBAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;;QAG3D,qBAAI,aAAuC,CAAC;QAC5C,qBAAI,gBAAoD,CAAC;;;QAIzD,KAAgB,IAApB,EAAA,GAAA,CAA4C,EAAxB,EAApB,GAAoB,IAAI,CAAC,mBAAmB,EAAxB,EAApB,GAAA,EAAA,CAAA,MAA4C,EAAxB,EAApB,EAA4C,EAA5C;YAAS,IAAI,GAAG,GAAhB,EAAA,CAAA,EAAA,CAAgB,CAAhB;;;YAGM,qBAAI,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAClE,qBAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;;YAGtF,IAAI,YAAY,CAAC,cAAc,EAAE;gBAC/B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;;gBAGlE,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;gBAElC,OAAO;aACR;iBAAM,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW,EAAE;gBACjF,aAAa,GAAG,YAAY,CAAC;gBAC7B,gBAAgB,GAAG,GAAG,CAAC;aACxB;SACF;;;QAID,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,WAAW,qBAAE,aAAa,uBAAG,gBAAgB,GAAE,CAAC;KACnF,CAAH;;;;;;;;;;;;IAOE,yBAAF,CAAA,SAAA,CAAA,uBAAyB;;;;;;IAAvB,YAAF;;QAEI,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAChC,OAAO;SACR;QAED,qBAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACxD,qBAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;QACvD,qBAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;QAC3D,qBAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAEhF,qBAAI,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAC3E,qBAAI,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QAC/F,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;KAC/E,CAAH;;;;;;;;;;;;;IAOE,yBAAF,CAAA,SAAA,CAAA,wBAA0B;;;;;;;IAAxB,UAAyB,WAA4B,EAAvD;QACI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAChC,CAAH;;;;;;;;;;;;;;IAOE,yBAAF,CAAA,SAAA,CAAA,oBAAsB;;;;;;;;IAApB,UACI,SAAmC,EACnC,UAAqC,EACrC,OAAgB,EAChB,OAAgB,EAJtB;QAMI,qBAAM,QAAQ,GAAG,IAAI,sBAAsB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACrF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,yBAAF,CAAA,SAAA,CAAA,aAAe;;;;;IAAb,UAAc,GAAkB,EAAlC;QACI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,yBAAF,CAAA,SAAA,CAAA,WAAa;;;;;IAAX,UAAY,MAAc,EAA5B;QACI,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;QACvB,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,yBAAF,CAAA,SAAA,CAAA,WAAa;;;;;IAAX,UAAY,MAAc,EAA5B;QACI,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;QACvB,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;;;;IAQE,yBAAF,CAAA,SAAA,CAAA,kBAAoB;;;;;;;IAAlB,UAAmB,QAAiB,EAAtC;QACI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,yBAAF,CAAA,SAAA,CAAA,aAAe;;;;;IAAb,UAAc,SAAmC,EAAnD;QACI,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,yBAAF,CAAA,SAAA,CAAA,SAAW;;;;;IAAT,UAAU,MAAkB,EAA9B;QACI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC;QACpC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;IAMU,yBAAV,CAAA,SAAA,CAAA,UAAoB;;;;;IAApB,UAAqB,IAAgB,EAArC;QACI,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;;;;;;;IAOtC,yBAAV,CAAA,SAAA,CAAA,QAAkB;;;;;IAAlB,UAAmB,IAAgB,EAAnC;QACI,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;;;;;;;;IAStC,yBAAV,CAAA,SAAA,CAAA,yBAAmC;;;;;;IAAnC,UAAoC,UAAsB,EAAE,GAA2B,EAAvF;QACI,qBAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QACjD,qBAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE7C,qBAAI,CAAS,CAAC;QACd,IAAI,GAAG,CAAC,OAAO,IAAI,QAAQ,EAAE;YAC3B,CAAC,GAAG,YAAY,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;SAC3C;aAAM;YACL,CAAC,GAAG,GAAG,CAAC,OAAO,IAAI,OAAO,GAAG,YAAY,GAAG,UAAU,CAAC;SACxD;QAED,qBAAI,CAAS,CAAC;QACd,IAAI,GAAG,CAAC,OAAO,IAAI,QAAQ,EAAE;YAC3B,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAC9C;aAAM;YACL,CAAC,GAAG,GAAG,CAAC,OAAO,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC;SAC/D;QAED,OAAO,EAAC,CAAC,EAAb,CAAa,EAAE,CAAC,EAAhB,CAAgB,EAAC,CAAC;;;;;;;;;;;;IASR,yBAAV,CAAA,SAAA,CAAA,gBAA0B;;;;;;;;;;IAC1B,UAAM,WAAkB,EAClB,WAAuB,EACvB,YAA6C,EAC7C,GAA2B,EAHjC;;;QAMI,qBAAI,aAAqB,CAAC;QAC1B,IAAI,GAAG,CAAC,QAAQ,IAAI,QAAQ,EAAE;YAC5B,aAAa,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;SACxC;aAAM,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,EAAE;YACnC,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;SACtD;aAAM;YACL,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC;SACtD;QAED,qBAAI,aAAqB,CAAC;QAC1B,IAAI,GAAG,CAAC,QAAQ,IAAI,QAAQ,EAAE;YAC5B,aAAa,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;SACzC;aAAM;YACL,aAAa,GAAG,GAAG,CAAC,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC;SACjE;;QAGD,qBAAI,OAAO,GAAG,OAAO,GAAG,CAAC,OAAO,KAAK,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC;QAC/E,qBAAI,OAAO,GAAG,OAAO,GAAG,CAAC,OAAO,KAAK,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC;;QAG/E,qBAAI,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,aAAa,GAAG,OAAO,CAAC;QAChD,qBAAI,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,aAAa,GAAG,OAAO,CAAC;;QAGhD,qBAAI,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,qBAAI,aAAa,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC;QACjE,qBAAI,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,qBAAI,cAAc,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;;QAGpE,qBAAI,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAC3F,qBAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;;QAG7F,qBAAI,WAAW,GAAG,YAAY,GAAG,aAAa,CAAC;QAC/C,qBAAI,cAAc,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,MAAM,WAAW,CAAC;QAE9E,OAAO,EAAC,CAAC,EAAb,CAAa,EAAE,CAAC,EAAhB,CAAgB,EAAE,cAAc,EAAhC,cAAgC,EAAE,WAAW,EAA7C,WAA6C,EAAC,CAAC;;;;;;;;IAOrC,yBAAV,CAAA,SAAA,CAAA,oBAA8B;;;;;;IAA9B,UAA+B,OAAoB,EAAnD;QACI,qBAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC1D,qBAAM,aAAa,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACtD,qBAAM,qBAAqB,GACvB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,EAD9B,EACkC,OAAA,CAAC,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CADzF,EACyF,CAAC,CAAC;QAEvF,OAAO;YACL,eAAe,EAAE,2BAA2B,CAAC,YAAY,EAAE,qBAAqB,CAAC;YACjF,mBAAmB,EAAE,4BAA4B,CAAC,YAAY,EAAE,qBAAqB,CAAC;YACtF,gBAAgB,EAAE,2BAA2B,CAAC,aAAa,EAAE,qBAAqB,CAAC;YACnF,oBAAoB,EAAE,4BAA4B,CAAC,aAAa,EAAE,qBAAqB,CAAC;SACzF,CAAC;;;;;;;;;;IAII,yBAAV,CAAA,SAAA,CAAA,mBAA6B;;;;;;;;IAC7B,UAAM,OAAoB,EACpB,WAAuB,EACvB,YAAmB,EACnB,GAA2B,EAHjC;;;QAOI,qBAAI,qBAAqB,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAC;;;QAIzE,qBAAI,CAAC,GAAG,qBAAqB,KAAK,KAAK;YACnC,YAAY,CAAC,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,YAAY,IAAI,YAAY,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;;;;;QAMxF,qBAAI,uBAA+B,CAAC;QACpC,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;YACvB,uBAAuB,GAAG,GAAG,CAAC,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;SACrE;aAAM;YACL,uBAAuB,GAAG,GAAG,CAAC,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;SACrE;;;QAID,qBAAI,CAAC,GAAG,uBAAuB,KAAK,MAAM;YACxC,YAAY,CAAC,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,WAAW,IAAI,YAAY,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;;;QAKpF,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC,EAAhD,EAAoD,OAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAA3E,EAA2E,CAAC,CAAC;QAEzE,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,GAAM,CAAC,GAA/C,IAAmD,CAAC;QAChD,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAM,CAAC,GAAjD,IAAqD,CAAC;;QAGlD,qBAAM,wBAAwB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACpE,qBAAM,cAAc,GAAG,IAAI,8BAA8B,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;QACzF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;;;;;;;IAMtC,yBAAV,CAAA,SAAA,CAAA,kBAA4B;;;;;;IAA5B,UAA6B,MAAc,EAA3C;QAA6C,IAA7C,SAAA,GAAA,EAAA,CAAmE;QAAnE,KAA6C,IAA7C,EAAA,GAAA,CAAmE,EAAtB,EAA7C,GAAA,SAAA,CAAA,MAAmE,EAAtB,EAA7C,EAAmE,EAAnE;YAA6C,SAA7C,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAmE;;QAC/D,OAAO,SAAS,CAAC,MAAM,CAAC,UAAC,YAAoB,EAAE,eAAuB,EAA1E;YACM,OAAO,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;SACpD,EAAE,MAAM,CAAC,CAAC;;IA1bf,OAAA,yBAAA,CAAA;CA4bA,EAAA,CAAC,CAAA;;;;;;;;;;;;;AD1aD,IAAA,sBAAA,kBAAA,YAAA;IAiBE,SAAF,sBAAA,CAAsB,SAAc,EAApC;QAAsB,IAAtB,CAAA,SAA+B,GAAT,SAAS,CAAK;QAbpC,IAAA,CAAA,YAAA,GAAyB,QAAQ,CAAjC;QACA,IAAA,CAAA,UAAA,GAAuB,EAAE,CAAzB;QACA,IAAA,CAAA,aAAA,GAA0B,EAAE,CAA5B;QACA,IAAA,CAAA,WAAA,GAAwB,EAAE,CAA1B;QACA,IAAA,CAAA,YAAA,GAAyB,EAAE,CAA3B;QACA,IAAA,CAAA,WAAA,GAAwB,EAAE,CAA1B;QACA,IAAA,CAAA,eAAA,GAA4B,EAAE,CAA9B;QACA,IAAA,CAAA,MAAA,GAAmB,EAAE,CAArB;QACA,IAAA,CAAA,OAAA,GAAoB,EAAE,CAAtB;;;;QAGA,IAAA,CAAA,QAAA,GAAyC,IAAI,CAA7C;KAEwC;;;;;IAEtC,sBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,UAAO,UAAsB,EAA/B;QACI,qBAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;QAEtC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAChC,UAAU,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAC,CAAC,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAClC,UAAU,CAAC,UAAU,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;SAC/C;KACF,CAAH;;;;;;;;;;IAME,sBAAF,CAAA,SAAA,CAAA,GAAK;;;;;IAAH,UAAI,KAAkB,EAAxB;QAAM,IAAN,KAAA,KAAA,KAAA,CAAA,EAAM,EAAA,KAAN,GAAA,EAAwB,CAAxB,EAAA;QACI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;QAChC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,sBAAF,CAAA,SAAA,CAAA,IAAM;;;;;IAAJ,UAAK,KAAkB,EAAzB;QAAO,IAAP,KAAA,KAAA,KAAA,CAAA,EAAO,EAAA,KAAP,GAAA,EAAyB,CAAzB,EAAA;QACI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,YAAY,CAAC;QACpC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,sBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,KAAkB,EAA3B;QAAS,IAAT,KAAA,KAAA,KAAA,CAAA,EAAS,EAAA,KAAT,GAAA,EAA2B,CAA3B,EAAA;QACI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;IAME,sBAAF,CAAA,SAAA,CAAA,KAAO;;;;;IAAL,UAAM,KAAkB,EAA1B;QAAQ,IAAR,KAAA,KAAA,KAAA,CAAA,EAAQ,EAAA,KAAR,GAAA,EAA0B,CAA1B,EAAA;QACI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;;;;IAQE,sBAAF,CAAA,SAAA,CAAA,KAAO;;;;;;;IAAL,UAAM,KAAkB,EAA1B;QAAQ,IAAR,KAAA,KAAA,KAAA,CAAA,EAAQ,EAAA,KAAR,GAAA,EAA0B,CAA1B,EAAA;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC,CAAC;SAC7C;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACrB;QAED,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;;;;IAQE,sBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;;;IAAN,UAAO,KAAkB,EAA3B;QAAS,IAAT,KAAA,KAAA,KAAA,CAAA,EAAS,EAAA,KAAT,GAAA,EAA2B,CAA3B,EAAA;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAC,MAAM,EAAE,KAAK,EAAC,CAAC,CAAC;SAC9C;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACtB;QAED,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;;;;IAQE,sBAAF,CAAA,SAAA,CAAA,kBAAoB;;;;;;;IAAlB,UAAmB,MAAmB,EAAxC;QAAqB,IAArB,MAAA,KAAA,KAAA,CAAA,EAAqB,EAAA,MAArB,GAAA,EAAwC,CAAxC,EAAA;QACI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;;;;IAQE,sBAAF,CAAA,SAAA,CAAA,gBAAkB;;;;;;;IAAhB,UAAiB,MAAmB,EAAtC;QAAmB,IAAnB,MAAA,KAAA,KAAA,CAAA,EAAmB,EAAA,MAAnB,GAAA,EAAsC,CAAtC,EAAA;QACI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC5B,OAAO,IAAI,CAAC;KACb,CAAH;;;;;;;;;;;;;IAQE,sBAAF,CAAA,SAAA,CAAA,KAAO;;;;;;IAAL,YAAF;;;;QAII,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;YACnC,OAAO;SACR;QAED,qBAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;QAEhD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE;YACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1D,EAAM,IAAI,CAAC,QAAQ,GAAE,SAAS,CAAC,GAAG,CAAC,4BAA4B,CAA/D,CAAA;YACM,OAAO,CAAC,UAAU,CAAC,YAAY,oBAAC,IAAI,CAAC,QAAQ,IAAG,OAAO,CAAC,CAAC;YAC/D,EAAM,IAAI,CAAC,QAAQ,GAAE,WAAW,CAAC,OAAO,CAAxC,CAAA;SACK;QAED,qBAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;QAC7B,qBAAM,YAAY,GAAG,mBAAC,OAAO,CAAC,UAAyB,GAAE,KAAK,CAAC;QAC/D,qBAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QAE5C,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;QACpC,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;QACrE,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QACpE,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QAEvC,YAAY,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,KAAK,MAAM,GAAG,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC;QAC5F,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,KAAK,MAAM,GAAG,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;KACtF,CAAH;;;;;;IAGE,sBAAF,CAAA,SAAA,CAAA,OAAS;;;;IAAP,YAAF;QACI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC7C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;KACF,CAAH;IAlMA,OAAA,sBAAA,CAAA;CAmMA,EAAA,CAAC,CAAA;;;;;;;;;;;IDhLC,SAAF,sBAAA,CAAsB,cAA6B,EACX,SADxC,EAAA;QAAsB,IAAtB,CAAA,cAAoC,GAAd,cAAc,CAAe;QACX,IAAxC,CAAA,SAAiD,GAAT,SAAS,CAAjD;KAA2D;;;;;;;;IAKzD,sBAAF,CAAA,SAAA,CAAA,MAAQ;;;;IAAN,YAAF;QACI,OAAO,IAAI,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACnD,CAAH;;;;;;;;;;;;;;IAQE,sBAAF,CAAA,SAAA,CAAA,WAAa;;;;;;;IAAX,UACI,UAAsB,EACtB,SAAmC,EACnC,UAAqC,EAH3C;QAKI,OAAO,IAAI,yBAAyB,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAClE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;KAC1C,CAAH;;QAzBA,EAAA,IAAA,EAAChC,wBAAU,EAAX;;;;QARA,EAAA,IAAA,EAAQ+B,oCAAa,GAArB;QAWA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAepB,oBAAM,EAArB,IAAA,EAAA,CAAsBW,wBAAQ,EAA9B,EAAA,EAAA,EAAA;;IApBA,OAAA,sBAAA,CAAA;CAkBA,EAAA,CAAA,CAAA;;;;;;;;;;;;;IDUE,SAAF,yBAAA,CAAwC,SAAxC,EAAA;QAAwC,IAAxC,CAAA,SAAiD,GAAT,SAAS,CAAjD;;;;QAJA,IAAA,CAAA,iBAAA,GAAoC,EAAE,CAAtC;KAI0D;;;;IAExD,yBAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,6BAA6B,EAAE,CAAC;KACtC,CAAH;;;;;;;IAGE,yBAAF,CAAA,SAAA,CAAA,GAAK;;;;;IAAH,UAAI,UAAsB,EAA5B;;QAEI,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACnC,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACzC,CAAH;;;;;;;IAGE,yBAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,UAAsB,EAA/B;QACI,qBAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAEzD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACzC;;QAGD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,IAAI,CAAC,6BAA6B,EAAE,CAAC;SACtC;KACF,CAAH;;;;;;IAMU,yBAAV,CAAA,SAAA,CAAA,yBAAmC;;;;;;;QAC/B,qBAAM,iBAAiB,GAAGQ,mCAAS,CAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAEzF,IAAI,CAAC,yBAAyB,GAAG,iBAAiB,CAAC,IAAI,CACrDD,4BAAM,CAAC,YADb,EACmB,OAAA,CAAC,CAAC,KAAI,CAAC,iBAAiB,CAAC,MAAM,CADlD,EACkD,CAAC,CAC9C,CAAC,SAAS,CAAC,UAAA,KAAK,EAFrB;;;YAIM,KAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAChE,CAAC,CAAC;;;;;;IAIG,yBAAV,CAAA,SAAA,CAAA,6BAAuC;;;;;QACnC,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACvC;;;;;;;IAIK,yBAAV,CAAA,SAAA,CAAA,uBAAiC;;;;;IAAjC,UAAkC,KAAoB,EAAtD;;QAEI,qBAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAA,OAAO,EAA/D;YACM,OAAO,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,MAAM;gBAC1C,OAAO,CAAC,cAAc,CAAC,QAAQ,mBAAC,KAAK,CAAC,MAAqB,EAAC,CAAC;SAClE,CAAC,CAAC;;QAGH,OAAO,eAAe,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;;QAtExF,EAAA,IAAA,EAAC7B,wBAAU,EAAX;;;;QAQA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAeW,oBAAM,EAArB,IAAA,EAAA,CAAsBW,wBAAQ,EAA9B,EAAA,EAAA,EAAA;;IA5BA,OAAA,yBAAA,CAAA;;;;;;;;AAgGA,SAAA,4CAAA,CACI,UAAqC,EAAE,SAAc,EADzD;IAEE,OAAO,UAAU,IAAI,IAAI,yBAAyB,CAAC,SAAS,CAAC,CAAC;CAC/D;;;;AAGD,IAAa,oCAAoC,GAAG;;;IAGlD,OAAO,EAAE,yBAAyB;IAClC,IAAI,EAAE;QACJ,CAAC,IAAIZ,sBAAQ,EAAE,EAAE,IAAIkB,sBAAQ,EAAE,EAAE,yBAAyB,CAAC;;;;QAI3DN,wBAA+B;KAChC;IACD,UAAU,EAAE,4CAA4C;CACzD,CAAC;;;;;;;;;;;IDjGA,SAAF,gBAAA,CAAwC,SAAxC,EAAA;QAAwC,IAAxC,CAAA,SAAiD,GAAT,SAAS,CAAjD;KAA0D;;;;IAExD,gBAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;YAC/D,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACvE;KACF,CAAH;;;;;;;;;;;;;IAQE,gBAAF,CAAA,SAAA,CAAA,mBAAqB;;;;;;IAAnB,YAAF;QACI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAAE;QACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC;KAC/B,CAAH;;;;;;;;;;IAMY,gBAAZ,CAAA,SAAA,CAAA,gBAA4B;;;;;IAA1B,YAAF;QACI,qBAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEtD,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;KACpC,CAAH;;QAjCA,EAAA,IAAA,EAACtB,wBAAU,EAAX;;;;QAIA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAeW,oBAAM,EAArB,IAAA,EAAA,CAAsBW,wBAAQ,EAA9B,EAAA,EAAA,EAAA;;IAjBA,OAAA,gBAAA,CAAA;;;;;;;;AAkDA,SAAA,kCAAA,CAAmD,eAAiC,EAClF,SAAc,EADhB;IAEE,OAAO,eAAe,IAAI,IAAI,gBAAgB,CAAC,SAAS,CAAC,CAAC;CAC3D;;;;AAGD,IAAa,0BAA0B,GAAG;;IAExC,OAAO,EAAE,gBAAgB;IACzB,IAAI,EAAE;QACJ,CAAC,IAAIZ,sBAAQ,EAAE,EAAE,IAAIkB,sBAAQ,EAAE,EAAE,gBAAgB,CAAC;0BAClDN,wBAA+B;;KAChC;IACD,UAAU,EAAE,kCAAkC;CAC/C,CAAC;;;;;;;;;;ADrCF,IAAI,YAAY,GAAG,CAAC,CAAC;;;;;;;;;;IAYnB,SAAF,OAAA,CAEqB,gBAFrB,EAGsB,iBAHtB,EAIsB,yBAJtB,EAKsB,gBALtB,EAMsB,mBANtB,EAOsB,OAPtB,EAQsB,SARtB,EASsB,OATtB,EAUwC,SAVxC,EAAA;QAEqB,IAArB,CAAA,gBAAqC,GAAhB,gBAAgB,CAArC;QACsB,IAAtB,CAAA,iBAAuC,GAAjB,iBAAiB,CAAvC;QACsB,IAAtB,CAAA,yBAA+C,GAAzB,yBAAyB,CAA/C;QACsB,IAAtB,CAAA,gBAAsC,GAAhB,gBAAgB,CAAtC;QACsB,IAAtB,CAAA,mBAAyC,GAAnB,mBAAmB,CAAzC;QACsB,IAAtB,CAAA,OAA6B,GAAP,OAAO,CAA7B;QACsB,IAAtB,CAAA,SAA+B,GAAT,SAAS,CAA/B;QACsB,IAAtB,CAAA,OAA6B,GAAP,OAAO,CAA7B;QACwC,IAAxC,CAAA,SAAiD,GAAT,SAAS,CAAjD;KAA2D;;;;;;;;;;;IAOzD,OAAF,CAAA,SAAA,CAAA,MAAQ;;;;;IAAN,UAAO,MAAsB,EAA/B;QACI,qBAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACvC,qBAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAEpD,OAAO,IAAI,UAAU,CACnB,YAAY,EACZ,IAAI,EACJ,IAAI,aAAa,CAAC,MAAM,CAAC,EACzB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,SAAS,CACf,CAAC;KACH,CAAH;;;;;;;;;;;IAOE,OAAF,CAAA,SAAA,CAAA,QAAU;;;;;IAAR,YAAF;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B,CAAH;;;;;IAMU,OAAV,CAAA,SAAA,CAAA,kBAA4B;;;;;QACxB,qBAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEjD,IAAI,CAAC,EAAE,GAAG,cAAd,GAA6B,YAAY,EAAI,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACvC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE/D,OAAO,IAAI,CAAC;;;;;;;IAQN,OAAV,CAAA,SAAA,CAAA,mBAA6B;;;;;IAA7B,UAA8B,IAAiB,EAA/C;QACI,OAAO,IAAIK,mCAAe,CAAC,IAAI,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;;QA9DnG,EAAA,IAAA,EAAC3B,wBAAU,EAAX;;;;QAfA,EAAA,IAAA,EAAQ,qBAAqB,GAA7B;QADA,EAAA,IAAA,EAAQ,gBAAgB,GAAxB;QAZA,EAAA,IAAA,EAAE0B,sCAAwB,GAA1B;QAUA,EAAA,IAAA,EAAQ,sBAAsB,GAA9B;QACA,EAAA,IAAA,EAAQ,yBAAyB,GAAjC;QATA,EAAA,IAAA,EAAED,4BAAc,GAAhB;QACA,EAAA,IAAA,EAAED,sBAAQ,GAAV;QACA,EAAA,IAAA,EAAED,oBAAM,GAAR;QAoCA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAeZ,oBAAM,EAArB,IAAA,EAAA,CAAsBW,wBAAQ,EAA9B,EAAA,EAAA,EAAA;;IAjDA,OAAA,OAAA,CAAA;CAsCA,EAAA,CAAA,CAAA;;;;;;;;;;ADEA,IAAM,mBAAmB,GAAG;IAC1B,IAAI,sBAAsB,CACtB,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC,EACrC,EAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;IACzC,IAAI,sBAAsB,CACtB,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAC,EAClC,EAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC;IAC5C,IAAI,sBAAsB,CACxB,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAC,EAChC,EAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC;IACxC,IAAI,sBAAsB,CACxB,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAC,EACnC,EAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;CACtC,CAAC;;;;AAGF,IAAa,qCAAqC,GAC9C,IAAID,4BAAc,CAAuB,uCAAuC,CAAC,CAAC;;;;;;AAGtF,SAAA,sDAAA,CAAuE,OAAgB,EAAvF;IAEE,OAAO,YAAT,EAAe,OAAA,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAApD,EAAoD,CAAC;CACpD;;;;AAGD,IAAa,8CAA8C,GAAG;IAC5D,OAAO,EAAE,qCAAqC;IAC9C,IAAI,EAAE,CAAC,OAAO,CAAC;IACf,UAAU,EAAE,sDAAsD;CACnE,CAAC;;;;;;IAYA,SAAF,gBAAA,CAEa,UAFb,EAAA;QAEa,IAAb,CAAA,UAAuB,GAAV,UAAU,CAAvB;KAAwC;;QAPxC,EAAA,IAAA,EAACP,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,4DAA4D;oBACtE,QAAQ,EAAE,kBAAkB;iBAC7B,EAAD,EAAA;;;;QAlEA,EAAA,IAAA,EAAEM,wBAAU,GAAZ;;IAdA,OAAA,gBAAA,CAAA;;;;;;;IAmRE,SAAF,mBAAA,CACc,QADd,EAEM,WAA6B,EAC7B,gBAAkC,EACqB,eAJ7D,EAK0B,IAL1B,EAAA;QACc,IAAd,CAAA,QAAsB,GAAR,QAAQ,CAAtB;QAG6D,IAA7D,CAAA,eAA4E,GAAf,eAAe,CAA5E;QAC0B,IAA1B,CAAA,IAA8B,GAAJ,IAAI,CAA9B;QAtLA,IAAA,CAAA,YAAA,GAAyB,KAAK,CAA9B;QACA,IAAA,CAAA,aAAA,GAA0B,KAAK,CAA/B;QACA,IAAA,CAAA,qBAAA,GAAkCD,8BAAY,CAAC,KAAK,CAApD;QACA,IAAA,CAAA,QAAA,GAA6B,CAAC,CAA9B;QACA,IAAA,CAAA,QAAA,GAA6B,CAAC,CAA9B;;;;QA8CA,IAAA,CAAA,cAAA,GAAM,IAAI,CAAC,eAAe,EAAE,CAA5B;;;;QAGA,IAAA,CAAA,IAAA,GAAoD,KAAK,CAAzD;;;;QA+GA,IAAA,CAAA,aAAA,GAA4B,IAAID,0BAAY,EAAc,CAA1D;;;;QAGA,IAAA,CAAA,cAAA,GAA6B,IAAIA,0BAAY,EAAkC,CAA/E;;;;QAGA,IAAA,CAAA,MAAA,GAAqB,IAAIA,0BAAY,EAAQ,CAA7C;;;;QAGA,IAAA,CAAA,MAAA,GAAqB,IAAIA,0BAAY,EAAQ,CAA7C;QAUI,IAAI,CAAC,eAAe,GAAG,IAAID,kCAAc,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;KAC1E;IAzKH,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,SAAa,EAAb;;;;;QAAA,YAAA,EAA0B,OAAO,IAAI,CAAC,QAAQ,CAAC,EAA/C;;;;;QACE,UAAY,OAAe,EAA7B;YACI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;aACrC;SACF;;;;IAIH,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,SAAa,EAAb;;;;;QAAA,YAAA,EAAkB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAvC;;;;;QACE,UAAY,OAAe,EAA7B;YACI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;aACrC;SACF;;;;IA0BH,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,aAAiB,EAAjB;;;;;QAAA,YAAA,EAAsB,OAAO,IAAI,CAAC,YAAY,CAAC,EAA/C;;;;;QACE,UAAgB,KAAU,EAA5B,EAAgC,IAAI,CAAC,YAAY,GAAGD,2CAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;;;;IAInF,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,cAAkB,EAAlB;;;;;QAAA,YAAA,EAAuB,OAAO,IAAI,CAAC,aAAa,CAAC,EAAjD;;;;;QACE,UAAiB,KAAU,EAA7B,EAAiC,IAAI,CAAC,aAAa,GAAGA,2CAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;;;;IAOrF,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,mBAAuB,EAAvB;;;;;;QAAA,YAAA,EAA8C,OAAO,IAAI,CAAC,MAAM,CAAC,EAAjE;;;;;QACE,UAAsB,OAAyB,EAAjD,EAAqD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,EAAE;;;;IAO7E,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,sBAA0B,EAA1B;;;;;;QAAA,YAAA,EAAyD,OAAO,IAAI,CAAC,SAAS,CAAC,EAA/E;;;;;QACE,UAAyB,UAAoC,EAA/D,EAAmE,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,EAAE;;;;IAOjG,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,oBAAwB,EAAxB;;;;;;QAAA,YAAA,EAAqC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAzD;;;;;QACE,UAAuB,QAAgB,EAAzC,EAA6C,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,EAAE;;;;IAOvE,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,oBAAwB,EAAxB;;;;;;QAAA,YAAA,EAAqC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAzD;;;;;QACE,UAAuB,QAAgB,EAAzC,EAA6C,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,EAAE;;;;IAOvE,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,kBAAsB,EAAtB;;;;;;QAAA,YAAA,EAA4C,OAAO,IAAI,CAAC,KAAK,CAAC,EAA9D;;;;;QACE,UAAqB,MAAuB,EAA9C,EAAkD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE;;;;IAOxE,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,mBAAuB,EAAvB;;;;;;QAAA,YAAA,EAA6C,OAAO,IAAI,CAAC,MAAM,CAAC,EAAhE;;;;;QACE,UAAsB,OAAwB,EAAhD,EAAoD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,EAAE;;;;IAO5E,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,qBAAyB,EAAzB;;;;;;QAAA,YAAA,EAA+C,OAAO,IAAI,CAAC,QAAQ,CAAC,EAApE;;;;;QACE,UAAwB,SAA0B,EAApD,EAAwD,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,EAAE;;;;IAOpF,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,sBAA0B,EAA1B;;;;;;QAAA,YAAA,EAAgD,OAAO,IAAI,CAAC,SAAS,CAAC,EAAtE;;;;;QACE,UAAyB,UAA2B,EAAtD,EAA0D,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,EAAE;;;;IAOxF,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,0BAA8B,EAA9B;;;;;;QAAA,YAAA,EAA2C,OAAO,IAAI,CAAC,aAAa,CAAC,EAArE;;;;;QACE,UAA6B,cAAsB,EAArD,EAAyD,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,EAAE;;;;IAO/F,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,2BAA+B,EAA/B;;;;;;QAAA,YAAA,EAAoD,OAAO,IAAI,CAAC,cAAc,CAAC,EAA/E;;;;;QACE,UAA8B,eAA+B,EAA/D;YACI,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;SACvC;;;;IAOH,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,iBAAqB,EAArB;;;;;;QAAA,YAAA,EAAmC,OAAO,IAAI,CAAC,IAAI,CAAC,EAApD;;;;;QACE,UAAoB,KAAc,EAApC,EAAwC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;;;;IAO5D,MAAA,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,wBAA4B,EAA5B;;;;;;QAAA,YAAA,EAAiC,OAAO,IAAI,CAAC,WAAW,CAAC,EAAzD;;;;;QACE,UAA2B,YAAiB,EAA9C,EAAkD,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE;;;;IA0BlF,MAAF,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,YAAgB,EAAhB;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,WAAW,CAAC;SACzB;;;KAAH,CAAA,CAAG;IAGD,MAAF,CAAA,cAAA,CAAM,mBAAN,CAAA,SAAA,EAAA,KAAS,EAAT;;;;;;QAAE,YAAF;YACI,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SAC5C;;;KAAH,CAAA,CAAG;;;;IAED,mBAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,eAAe,EAAE,CAAC;KACxB,CAAH;;;;;IAEE,mBAAF,CAAA,SAAA,CAAA,WAAa;;;;IAAX,UAAY,OAAsB,EAApC;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,sBAAsB,CAAC,EAAE;gBAC3D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9C;YAED,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE;gBAC3B,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACtD;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE;gBACrD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBAEjD,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;iBACxB;aACF;SACF;QAED,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACjD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;SAC3D;KACF,CAAH;;;;;IAGU,mBAAV,CAAA,SAAA,CAAA,cAAwB;;;;;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YAC7C,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC;SACtC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;;;;;;IAIvD,mBAAV,CAAA,SAAA,CAAA,YAAsB;;;;;QAClB,qBAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACzE,qBAAM,aAAa,GAAG,IAAI,aAAa,CAAC;YACtC,gBAAgB,EAAtB,gBAAsB;YAChB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;YAClC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;YACxC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE;YAC1C,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAClD;QAED,OAAO,aAAa,CAAC;;;;;;IAIf,mBAAV,CAAA,SAAA,CAAA,uBAAiC;;;;;;QAC7B,qBAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC1C,qBAAM,WAAW,GAAG,EAAC,OAAO,EAAE,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,eAAe,CAAC,OAAO,EAAC,CAAC;QACzF,qBAAM,YAAY,GAAG,EAAC,QAAQ,EAAE,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,eAAe,CAAC,QAAQ,EAAC,CAAC;QAC9F,qBAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;aACtC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC;aAC9D,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;aACzB,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;aACzB,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEzC,KAAK,qBAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,QAAQ,CAAC,oBAAoB,CACzB,EAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAC,EACxE,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAC,CAC/E,CAAC;SACH;QAED,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAA,GAAG,EAA3C,EAA+C,OAAA,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAA5E,EAA4E,CAAC,CAAC;QAE1E,OAAO,QAAQ,CAAC;;;;;;IAIV,mBAAV,CAAA,SAAA,CAAA,cAAwB;;;;;;QACpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;YAE5B,EAAM,IAAI,CAAC,WAAW,GAAE,aAAa,EAArC,CAAwC,SAAS,CAAC,UAAC,KAAoB,EAAvE;gBACQ,IAAI,KAAK,CAAC,OAAO,KAAKD,4BAAM,EAAE;oBAC5B,KAAI,CAAC,cAAc,EAAE,CAAC;iBACvB;aACF,CAAP,CAAA;SACK;aAAM;;YAEL,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;gBAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;YACnC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SACpB;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,UAAA,KAAK,EAAnF;gBACQ,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAChC,CAAC,CAAC;SACJ;;;;;;IAIK,mBAAV,CAAA,SAAA,CAAA,cAAwB;;;;;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;;;;;;IAInC,mBAAV,CAAA,SAAA,CAAA,eAAyB;;;;;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;;;QA1V7C,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,qEAAqE;oBAC/E,QAAQ,EAAE,qBAAqB;iBAChC,EAAD,EAAA;;;;QAlEA,EAAA,IAAA,EAAQ,OAAO,GAAf;QAJA,EAAA,IAAA,EAAED,yBAAW,GAAb;QACA,EAAA,IAAA,EAAED,8BAAgB,GAAlB;QA8PA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAOD,oBAAM,EAAb,IAAA,EAAA,CAAc,qCAAqC,EAAnD,EAAA,EAAA,EAAA;QA/QA,EAAA,IAAA,EAAmBF,gCAAc,EAAjC,UAAA,EAAA,CAAA,EAAA,IAAA,EAgROC,sBAAQ,EAhRf,EAAA,EAAA;;;QAkGA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAGF,mBAAK,EAAR,IAAA,EAAA,CAAS,2BAA2B,EAApC,EAAA,EAAA;QAGA,WAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,8BAA8B,EAAvC,EAAA,EAAA;QAGA,SAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,4BAA4B,EAArC,EAAA,EAAA;QAUA,SAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,4BAA4B,EAArC,EAAA,EAAA;QAUA,OAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,0BAA0B,EAAnC,EAAA,EAAA;QAGA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,2BAA2B,EAApC,EAAA,EAAA;QAGA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,6BAA6B,EAAtC,EAAA,EAAA;QAGA,WAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,8BAA8B,EAAvC,EAAA,EAAA;QAGA,eAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,kCAAkC,EAA3C,EAAA,EAAA;QAGA,gBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,mCAAmC,EAA5C,EAAA,EAAA;QAIA,MAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,yBAAyB,EAAlC,EAAA,EAAA;QAGA,aAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,gCAAgC,EAAzC,EAAA,EAAA;QAKA,cAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,iCAAiC,EAA1C,EAAA,EAAA;QAQA,mBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,QAAQ,EAAjB,EAAA,EAAA;QAQA,sBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,WAAW,EAApB,EAAA,EAAA;QAQA,oBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,SAAS,EAAlB,EAAA,EAAA;QAQA,oBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,SAAS,EAAlB,EAAA,EAAA;QAQA,kBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,OAAO,EAAhB,EAAA,EAAA;QAQA,mBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,QAAQ,EAAjB,EAAA,EAAA;QAQA,qBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,UAAU,EAAnB,EAAA,EAAA;QAQA,sBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,WAAW,EAApB,EAAA,EAAA;QAQA,0BAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,eAAe,EAAxB,EAAA,EAAA;QAQA,2BAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,gBAAgB,EAAzB,EAAA,EAAA;QAUA,iBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,MAAM,EAAf,EAAA,EAAA;QAQA,wBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,mBAAK,EAAR,IAAA,EAAA,CAAS,aAAa,EAAtB,EAAA,EAAA;QAKA,eAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,oBAAM,EAAT,EAAA;QAGA,gBAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,oBAAM,EAAT,EAAA;QAGA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,oBAAM,EAAT,EAAA;QAGA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAGA,oBAAM,EAAT,EAAA;;IA/QA,OAAA,mBAAA,CAAA;CA+FA,EAAA,CAAA,CAAA;;;;;;;ADvFA,IAea,iBAAiB,GAAe;IAC3C,OAAO;IACP,sBAAsB;IACtB,oCAAoC;IACpCD,8CAAuB;IACvB,0BAA0B;IAC1B,8CAA8C;CAC/C,CAAC;;;;;QAEF,EAAA,IAAA,EAACD,sBAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAACF,4BAAU,EAAEC,gCAAY,EAAEF,2CAAoB,CAAC;oBACzD,OAAO,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,EAAEA,2CAAoB,CAAC;oBACtE,YAAY,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC;oBACrD,SAAS,EAAE,CAAC,iBAAiB,EAAE,qBAAqB,CAAC;iBACtD,EAAD,EAAA;;;;IArCA,OAAA,aAAA,CAAA;CAsCA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;IDnBgDD,SAAhD,CAAA,0BAAA,EAAA,MAAA,CAAA,CAAgE;;;;;;;IACpD,0BAAZ,CAAA,SAAA,CAAA,gBAA4B;;;IAA1B,YAAF;QAAE,IAAF,KAAA,GAAA,IAAA,CAIG;QAHC,MAAJ,CAAA,SAAA,CAAU,gBAAgB,CAA1B,IAAA,CAAA,IAAA,CAA4B,CAAC;QACzB,IAAI,CAAC,gCAAgC,EAAE,CAAC;QACxC,IAAI,CAAC,4BAA4B,CAAC,YAAtC,EAA4C,OAAA,KAAI,CAAC,gCAAgC,EAAE,CAAnF,EAAmF,CAAC,CAAC;KAClF,CAAH;;;;IAEU,0BAAV,CAAA,SAAA,CAAA,gCAA0C;;;;QACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,OAAO;SACR;QACD,qBAAI,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACpD,qBAAI,MAAM,GAAG,iBAAiB,IAAI,QAAQ,CAAC,IAAI,CAAC;QAChD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;;;;;;IAGrC,0BAAV,CAAA,SAAA,CAAA,4BAAsC;;;;IAAtC,UAAuC,EAAc,EAArD;QACI,IAAI,QAAQ,CAAC,iBAAiB,EAAE;YAC9B,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;SACnD;aAAM,IAAI,QAAQ,CAAC,uBAAuB,EAAE;YAC3C,QAAQ,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC;SACzD;aAAM,IAAI,mBAAC,QAAe,GAAE,oBAAoB,EAAE;YACjD,QAAQ,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;SACtD;aAAM,IAAI,mBAAC,QAAe,GAAE,mBAAmB,EAAE;YAChD,QAAQ,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;SACrD;;;;;;;;;;;IAOH,0BAAF,CAAA,SAAA,CAAA,oBAAsB;;;;;IAApB,YAAF;QACI,OAAO,QAAQ,CAAC,iBAAiB;YAC7B,QAAQ,CAAC,uBAAuB;YAChC,mBAAC,QAAe,GAAE,oBAAoB;YACtC,mBAAC,QAAe,GAAE,mBAAmB;YACrC,IAAI,CAAC;KACV,CAAH;;QAvCA,EAAA,IAAA,EAACD,wBAAU,EAAX;;;;IAlBA,OAAA,0BAAA,CAAA;CAmBA,CAAgD,gBAAgB,CAAhE,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}