{"version": 3, "file": "ForkJoinObservable.js", "sourceRoot": "", "sources": ["../../src/observable/ForkJoinObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAAkD,eAAe,CAAC,CAAA;AAGlE,gCAAgC,mBAAmB,CAAC,CAAA;AACpD,wBAAwB,iBAAiB,CAAC,CAAA;AAE1C,kCAAkC,2BAA2B,CAAC,CAAA;AAC9D,gCAAgC,oBAAoB,CAAC,CAAA;AAGrD;;;;GAIG;AACH;IAA2C,sCAAa;IACtD,4BAAoB,OAA0C,EAC1C,cAA6C;QAC/D,iBAAO,CAAC;QAFU,YAAO,GAAP,OAAO,CAAmC;QAC1C,mBAAc,GAAd,cAAc,CAA+B;IAEjE,CAAC;IAoBD,mCAAmC;IAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgGG;IACI,yBAAM,GAAb;QAAiB,iBAEgD;aAFhD,WAEgD,CAFhD,sBAEgD,CAFhD,IAEgD;YAFhD,gCAEgD;;QAC/D,EAAE,CAAC,CAAC,OAAO,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,iCAAe,EAAK,CAAC;QAClC,CAAC;QAED,IAAI,cAAc,GAAmC,IAAI,CAAC;QAC1D,EAAE,CAAC,CAAC,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC;YACtD,cAAc,GAAmC,OAAO,CAAC,GAAG,EAAE,CAAC;QACjE,CAAC;QAED,8EAA8E;QAC9E,8EAA8E;QAC9E,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,OAAO,GAAsC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,IAAI,iCAAe,EAAK,CAAC;QAClC,CAAC;QAED,MAAM,CAAC,IAAI,kBAAkB,CAAoC,OAAO,EAAE,cAAc,CAAC,CAAC;IAC5F,CAAC;IAED,oCAAoC,CAAC,uCAAU,GAAV,UAAW,UAA2B;QACzE,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/E,CAAC;IACH,yBAAC;AAAD,CAAC,AAvJD,CAA2C,uBAAU,GAuJpD;AAvJY,0BAAkB,qBAuJ9B,CAAA;AAED;;;;GAIG;AACH;IAAoC,sCAAqB;IAMvD,4BAAY,WAA0B,EAClB,OAA0C,EAC1C,cAA6C;QAC/D,kBAAM,WAAW,CAAC,CAAC;QAFD,YAAO,GAAP,OAAO,CAAmC;QAC1C,mBAAc,GAAd,cAAc,CAA+B;QAPzD,cAAS,GAAG,CAAC,CAAC;QAGd,eAAU,GAAG,CAAC,CAAC;QAOrB,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAE7B,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,iBAAiB,GAAG,qCAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAEnE,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBACf,iBAAkB,CAAC,UAAU,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,uCAAU,GAAV,UAAW,UAAe,EAAE,UAAa,EAC9B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACxC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;QACrC,EAAE,CAAC,CAAC,CAAO,QAAS,CAAC,SAAS,CAAC,CAAC,CAAC;YACzB,QAAS,CAAC,SAAS,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,2CAAc,GAAd,UAAe,QAA+B;QAC5C,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAA,SAAmD,EAA3C,0BAAU,EAAE,kCAAc,EAAE,kBAAM,CAAU;QACpD,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAE1B,EAAE,CAAC,CAAC,CAAO,QAAS,CAAC,SAAS,CAAC,CAAC,CAAC;YAC/B,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,MAAM,CAAC;QACT,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC;QACT,CAAC;QAED,EAAE,CAAC,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC;YACvB,IAAM,KAAK,GAAG,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;YAC3E,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QAED,WAAW,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IACH,yBAAC;AAAD,CAAC,AA3DD,CAAoC,iCAAe,GA2DlD", "sourcesContent": ["import { Observable, SubscribableOrPromise } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { EmptyObservable } from './EmptyObservable';\nimport { isArray } from '../util/isArray';\n\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class ForkJoinObservable<T> extends Observable<T> {\n  constructor(private sources: Array<SubscribableOrPromise<any>>,\n              private resultSelector?: (...values: Array<any>) => T) {\n    super();\n  }\n\n  /* tslint:disable:max-line-length */\n  static create<T, T2>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>): Observable<[T, T2]>;\n  static create<T, T2, T3>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>): Observable<[T, T2, T3]>;\n  static create<T, T2, T3, T4>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>, v4: SubscribableOrPromise<T4>): Observable<[T, T2, T3, T4]>;\n  static create<T, T2, T3, T4, T5>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>, v4: SubscribableOrPromise<T4>, v5: SubscribableOrPromise<T5>): Observable<[T, T2, T3, T4, T5]>;\n  static create<T, T2, T3, T4, T5, T6>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>, v4: SubscribableOrPromise<T4>, v5: SubscribableOrPromise<T5>, v6: SubscribableOrPromise<T6>): Observable<[T, T2, T3, T4, T5, T6]>;\n  static create<T, R>(v1: SubscribableOrPromise<T>, project: (v1: T) => R): Observable<R>;\n  static create<T, T2, R>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, project: (v1: T, v2: T2) => R): Observable<R>;\n  static create<T, T2, T3, R>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>, project: (v1: T, v2: T2, v3: T3) => R): Observable<R>;\n  static create<T, T2, T3, T4, R>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>, v4: SubscribableOrPromise<T4>, project: (v1: T, v2: T2, v3: T3, v4: T4) => R): Observable<R>;\n  static create<T, T2, T3, T4, T5, R>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>, v4: SubscribableOrPromise<T4>, v5: SubscribableOrPromise<T5>, project: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5) => R): Observable<R>;\n  static create<T, T2, T3, T4, T5, T6, R>(v1: SubscribableOrPromise<T>, v2: SubscribableOrPromise<T2>, v3: SubscribableOrPromise<T3>, v4: SubscribableOrPromise<T4>, v5: SubscribableOrPromise<T5>, v6: SubscribableOrPromise<T6>, project: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6) => R): Observable<R>;\n  static create<T>(sources: SubscribableOrPromise<T>[]): Observable<T[]>;\n  static create<R>(sources: SubscribableOrPromise<any>[]): Observable<R>;\n  static create<T, R>(sources: SubscribableOrPromise<T>[], project: (...values: Array<T>) => R): Observable<R>;\n  static create<R>(sources: SubscribableOrPromise<any>[], project: (...values: Array<any>) => R): Observable<R>;\n  static create<T>(...sources: SubscribableOrPromise<T>[]): Observable<T[]>;\n  static create<R>(...sources: SubscribableOrPromise<any>[]): Observable<R>;\n  /* tslint:enable:max-line-length */\n\n  /**\n   * Joins last values emitted by passed Observables.\n   *\n   * <span class=\"informal\">Wait for Observables to complete and then combine last values they emitted.</span>\n   *\n   * <img src=\"./img/forkJoin.png\" width=\"100%\">\n   *\n   * `forkJoin` is an operator that takes any number of Observables which can be passed either as an array\n   * or directly as arguments. If no input Observables are provided, resulting stream will complete\n   * immediately.\n   *\n   * `forkJoin` will wait for all passed Observables to complete and then it will emit an array with last\n   * values from corresponding Observables. So if you pass `n` Observables to the operator, resulting\n   * array will have `n` values, where first value is the last thing emitted by the first Observable,\n   * second value is the last thing emitted by the second Observable and so on. That means `forkJoin` will\n   * not emit more than once and it will complete after that. If you need to emit combined values not only\n   * at the end of lifecycle of passed Observables, but also throughout it, try out {@link combineLatest}\n   * or {@link zip} instead.\n   *\n   * In order for resulting array to have the same length as the number of input Observables, whenever any of\n   * that Observables completes without emitting any value, `forkJoin` will complete at that moment as well\n   * and it will not emit anything either, even if it already has some last values from other Observables.\n   * Conversely, if there is an Observable that never completes, `forkJoin` will never complete as well,\n   * unless at any point some other Observable completes without emitting value, which brings us back to\n   * the previous case. Overall, in order for `forkJoin` to emit a value, all Observables passed as arguments\n   * have to emit something at least once and complete.\n   *\n   * If any input Observable errors at some point, `forkJoin` will error as well and all other Observables\n   * will be immediately unsubscribed.\n   *\n   * Optionally `forkJoin` accepts project function, that will be called with values which normally\n   * would land in emitted array. Whatever is returned by project function, will appear in output\n   * Observable instead. This means that default project can be thought of as a function that takes\n   * all its arguments and puts them into an array. Note that project function will be called only\n   * when output Observable is supposed to emit a result.\n   *\n   * @example <caption>Use forkJoin with operator emitting immediately</caption>\n   * const observable = Rx.Observable.forkJoin(\n   *   Rx.Observable.of(1, 2, 3, 4),\n   *   Rx.Observable.of(5, 6, 7, 8)\n   * );\n   * observable.subscribe(\n   *   value => console.log(value),\n   *   err => {},\n   *   () => console.log('This is how it ends!')\n   * );\n   *\n   * // Logs:\n   * // [4, 8]\n   * // \"This is how it ends!\"\n   *\n   *\n   * @example <caption>Use forkJoin with operator emitting after some time</caption>\n   * const observable = Rx.Observable.forkJoin(\n   *   Rx.Observable.interval(1000).take(3), // emit 0, 1, 2 every second and complete\n   *   Rx.Observable.interval(500).take(4) // emit 0, 1, 2, 3 every half a second and complete\n   * );\n   * observable.subscribe(\n   *   value => console.log(value),\n   *   err => {},\n   *   () => console.log('This is how it ends!')\n   * );\n   *\n   * // Logs:\n   * // [2, 3] after 3 seconds\n   * // \"This is how it ends!\" immediately after\n   *\n   *\n   * @example <caption>Use forkJoin with project function</caption>\n   * const observable = Rx.Observable.forkJoin(\n   *   Rx.Observable.interval(1000).take(3), // emit 0, 1, 2 every second and complete\n   *   Rx.Observable.interval(500).take(4), // emit 0, 1, 2, 3 every half a second and complete\n   *   (n, m) => n + m\n   * );\n   * observable.subscribe(\n   *   value => console.log(value),\n   *   err => {},\n   *   () => console.log('This is how it ends!')\n   * );\n   *\n   * // Logs:\n   * // 5 after 3 seconds\n   * // \"This is how it ends!\" immediately after\n   *\n   * @see {@link combineLatest}\n   * @see {@link zip}\n   *\n   * @param {...SubscribableOrPromise} sources Any number of Observables provided either as an array or as an arguments\n   * passed directly to the operator.\n   * @param {function} [project] Function that takes values emitted by input Observables and returns value\n   * that will appear in resulting Observable instead of default array.\n   * @return {Observable} Observable emitting either an array of last values emitted by passed Observables\n   * or value from project function.\n   * @static true\n   * @name forkJoin\n   * @owner Observable\n   */\n  static create<T>(...sources: Array<SubscribableOrPromise<any> |\n                                  Array<SubscribableOrPromise<any>> |\n                                  ((...values: Array<any>) => any)>): Observable<T> {\n    if (sources === null || arguments.length === 0) {\n      return new EmptyObservable<T>();\n    }\n\n    let resultSelector: (...values: Array<any>) => any = null;\n    if (typeof sources[sources.length - 1] === 'function') {\n      resultSelector = <(...values: Array<any>) => any>sources.pop();\n    }\n\n    // if the first and only other argument besides the resultSelector is an array\n    // assume it's been called with `forkJoin([obs1, obs2, obs3], resultSelector)`\n    if (sources.length === 1 && isArray(sources[0])) {\n      sources = <Array<SubscribableOrPromise<any>>>sources[0];\n    }\n\n    if (sources.length === 0) {\n      return new EmptyObservable<T>();\n    }\n\n    return new ForkJoinObservable(<Array<SubscribableOrPromise<any>>>sources, resultSelector);\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<any>): Subscription {\n    return new ForkJoinSubscriber(subscriber, this.sources, this.resultSelector);\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass ForkJoinSubscriber<T> extends OuterSubscriber<T, T> {\n  private completed = 0;\n  private total: number;\n  private values: any[];\n  private haveValues = 0;\n\n  constructor(destination: Subscriber<T>,\n              private sources: Array<SubscribableOrPromise<any>>,\n              private resultSelector?: (...values: Array<any>) => T) {\n    super(destination);\n\n    const len = sources.length;\n    this.total = len;\n    this.values = new Array(len);\n\n    for (let i = 0; i < len; i++) {\n      const source = sources[i];\n      const innerSubscription = subscribeToResult(this, source, null, i);\n\n      if (innerSubscription) {\n        (<any> innerSubscription).outerIndex = i;\n        this.add(innerSubscription);\n      }\n    }\n  }\n\n  notifyNext(outerValue: any, innerValue: T,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, T>): void {\n    this.values[outerIndex] = innerValue;\n    if (!(<any>innerSub)._hasValue) {\n      (<any>innerSub)._hasValue = true;\n      this.haveValues++;\n    }\n  }\n\n  notifyComplete(innerSub: InnerSubscriber<T, T>): void {\n    const destination = this.destination;\n    const { haveValues, resultSelector, values } = this;\n    const len = values.length;\n\n    if (!(<any>innerSub)._hasValue) {\n      destination.complete();\n      return;\n    }\n\n    this.completed++;\n\n    if (this.completed !== len) {\n      return;\n    }\n\n    if (haveValues === len) {\n      const value = resultSelector ? resultSelector.apply(this, values) : values;\n      destination.next(value);\n    }\n\n    destination.complete();\n  }\n}\n"]}