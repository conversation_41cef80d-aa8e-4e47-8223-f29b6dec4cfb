{"version": 3, "file": "cdk-overlay.umd.min.js", "sources": ["../../src/cdk/overlay/fullscreen-overlay-container.ts", "../../node_modules/tslib/tslib.es6.js", "../../src/cdk/overlay/scroll/scroll-strategy.ts", "../../src/cdk/overlay/position/scroll-clip.ts", "../../src/cdk/overlay/overlay-ref.ts", "../../src/cdk/overlay/keyboard/overlay-keyboard-dispatcher.ts", "../../src/cdk/overlay/overlay-container.ts", "../../src/cdk/overlay/overlay-directives.ts", "../../src/cdk/overlay/scroll/noop-scroll-strategy.ts", "../../src/cdk/overlay/overlay-config.ts", "../../src/cdk/overlay/position/connected-position.ts", "../../src/cdk/overlay/scroll/close-scroll-strategy.ts", "../../src/cdk/overlay/scroll/block-scroll-strategy.ts", "../../src/cdk/overlay/scroll/reposition-scroll-strategy.ts", "../../src/cdk/overlay/scroll/scroll-strategy-options.ts", "../../src/cdk/overlay/position/connected-position-strategy.ts", "../../src/cdk/overlay/position/global-position-strategy.ts", "../../src/cdk/overlay/position/overlay-position-builder.ts", "../../src/cdk/overlay/overlay.ts", "../../src/cdk/overlay/overlay-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {OverlayContainer} from './overlay-container';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\n@Injectable()\nexport class FullscreenOverlayContainer extends OverlayContainer {\n  protected _createContainer(): void {\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n\n  private _adjustParentForFullscreenChange(): void {\n    if (!this._containerElement) {\n      return;\n    }\n    let fullscreenElement = this.getFullscreenElement();\n    let parent = fullscreenElement || document.body;\n    parent.appendChild(this._containerElement);\n  }\n\n  private _addFullscreenChangeListener(fn: () => void) {\n    if (document.fullscreenEnabled) {\n      document.addEventListener('fullscreenchange', fn);\n    } else if (document.webkitFullscreenEnabled) {\n      document.addEventListener('webkitfullscreenchange', fn);\n    } else if ((document as any).mozFullScreenEnabled) {\n      document.addEventListener('mozfullscreenchange', fn);\n    } else if ((document as any).msFullscreenEnabled) {\n      document.addEventListener('MSFullscreenChange', fn);\n    }\n  }\n\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement(): Element {\n    return document.fullscreenElement ||\n        document.webkitFullscreenElement ||\n        (document as any).mozFullScreenElement ||\n        (document as any).msFullscreenElement ||\n        null;\n  }\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OverlayRef} from '../overlay-ref';\n\n/**\n * Describes a strategy that will be used by an overlay to handle scroll events while it is open.\n */\nexport interface ScrollStrategy {\n  /** Enable this scroll strategy (called when the attached overlay is attached to a portal). */\n  enable: () => void;\n\n  /** Disable this scroll strategy (called when the attached overlay is detached from a portal). */\n  disable: () => void;\n\n  /** Attaches this `ScrollStrategy` to an overlay. */\n  attach: (overlayRef: OverlayRef) => void;\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nexport function getMatScrollStrategyAlreadyAttachedError(): Error {\n  return Error(`Scroll strategy has already been attached.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// TODO(jelbourn): move this to live with the rest of the scrolling code\n// TODO(jelbourn): someday replace this with IntersectionObservers\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nexport function isElementScrolledOutsideView(element: ClientRect, scrollContainers: ClientRect[]) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n\n\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nexport function isElementClippedByScrolling(element: ClientRect, scrollContainers: ClientRect[]) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction} from '@angular/cdk/bidi';\nimport {ComponentPortal, Portal, PortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {ComponentRef, EmbeddedViewRef, NgZone} from '@angular/core';\nimport {Observable} from 'rxjs/Observable';\nimport {take} from 'rxjs/operators/take';\nimport {Subject} from 'rxjs/Subject';\nimport {OverlayKeyboardDispatcher} from './keyboard/overlay-keyboard-dispatcher';\nimport {OverlayConfig} from './overlay-config';\n\n\n/** An object where all of its properties cannot be written. */\nexport type ImmutableObject<T> = {\n  readonly [P in keyof T]: T[P];\n};\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nexport class OverlayRef implements PortalOutlet {\n  private _backdropElement: HTMLElement | null = null;\n  private _backdropClick: Subject<MouseEvent> = new Subject();\n  private _attachments = new Subject<void>();\n  private _detachments = new Subject<void>();\n\n  /** Stream of keydown events dispatched to this overlay. */\n  _keydownEvents = new Subject<KeyboardEvent>();\n\n  constructor(\n      private _portalOutlet: PortalOutlet,\n      private _pane: HTMLElement,\n      private _config: ImmutableObject<OverlayConfig>,\n      private _ngZone: NgZone,\n      private _keyboardDispatcher: OverlayKeyboardDispatcher,\n      private _document: Document) {\n\n    if (_config.scrollStrategy) {\n      _config.scrollStrategy.attach(this);\n    }\n  }\n\n  /** The overlay's HTML element */\n  get overlayElement(): HTMLElement {\n    return this._pane;\n  }\n\n  /** The overlay's backdrop HTML element. */\n  get backdropElement(): HTMLElement | null {\n    return this._backdropElement;\n  }\n\n  attach<T>(portal: ComponentPortal<T>): ComponentRef<T>;\n  attach<T>(portal: TemplatePortal<T>): EmbeddedViewRef<T>;\n  attach(portal: any): any;\n\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal: Portal<any>): any {\n    let attachResult = this._portalOutlet.attach(portal);\n\n    if (this._config.positionStrategy) {\n      this._config.positionStrategy.attach(this);\n    }\n\n    // Update the pane element with the given configuration.\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n\n    if (this._config.scrollStrategy) {\n      this._config.scrollStrategy.enable();\n    }\n\n    // Update the position once the zone is stable so that the overlay will be fully rendered\n    // before attempting to position it, as the position may depend on the size of the rendered\n    // content.\n    this._ngZone.onStable.asObservable().pipe(take(1)).subscribe(() => {\n      // The overlay could've been detached before the zone has stabilized.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    });\n\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n\n    if (this._config.panelClass) {\n      // We can't do a spread here, because IE doesn't support setting multiple classes.\n      if (Array.isArray(this._config.panelClass)) {\n        this._config.panelClass.forEach(cls => this._pane.classList.add(cls));\n      } else {\n        this._pane.classList.add(this._config.panelClass);\n      }\n    }\n\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n\n    return attachResult;\n  }\n\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach(): any {\n    if (!this.hasAttached()) {\n      return;\n    }\n\n    this.detachBackdrop();\n\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n\n    if (this._config.positionStrategy && this._config.positionStrategy.detach) {\n      this._config.positionStrategy.detach();\n    }\n\n    if (this._config.scrollStrategy) {\n      this._config.scrollStrategy.disable();\n    }\n\n    const detachmentResult = this._portalOutlet.detach();\n\n    // Only emit after everything is detached.\n    this._detachments.next();\n\n    // Remove this overlay from keyboard dispatcher tracking\n    this._keyboardDispatcher.remove(this);\n\n    return detachmentResult;\n  }\n\n  /** Cleans up the overlay from the DOM. */\n  dispose(): void {\n    const isAttached = this.hasAttached();\n\n    if (this._config.positionStrategy) {\n      this._config.positionStrategy.dispose();\n    }\n\n    if (this._config.scrollStrategy) {\n      this._config.scrollStrategy.disable();\n    }\n\n    this.detachBackdrop();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n\n    if (isAttached) {\n      this._detachments.next();\n    }\n\n    this._detachments.complete();\n  }\n\n  /** Whether the overlay has attached content. */\n  hasAttached(): boolean {\n    return this._portalOutlet.hasAttached();\n  }\n\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick(): Observable<MouseEvent> {\n    return this._backdropClick.asObservable();\n  }\n\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments(): Observable<void> {\n    return this._attachments.asObservable();\n  }\n\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments(): Observable<void> {\n    return this._detachments.asObservable();\n  }\n\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents(): Observable<KeyboardEvent> {\n    return this._keydownEvents.asObservable();\n  }\n\n  /** Gets the the current overlay configuration, which is immutable. */\n  getConfig(): OverlayConfig {\n    return this._config;\n  }\n\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._config.positionStrategy) {\n      this._config.positionStrategy.apply();\n    }\n  }\n\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig: OverlaySizeConfig) {\n    this._config = {...this._config, ...sizeConfig};\n    this._updateElementSize();\n  }\n\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir: Direction) {\n    this._config = {...this._config, direction: dir};\n    this._updateElementDirection();\n  }\n\n  /** Updates the text direction of the overlay panel. */\n  private _updateElementDirection() {\n    this._pane.setAttribute('dir', this._config.direction!);\n  }\n\n  /** Updates the size of the overlay element based on the overlay config. */\n  private _updateElementSize() {\n    if (this._config.width || this._config.width === 0) {\n      this._pane.style.width = formatCssUnit(this._config.width);\n    }\n\n    if (this._config.height || this._config.height === 0) {\n      this._pane.style.height = formatCssUnit(this._config.height);\n    }\n\n    if (this._config.minWidth || this._config.minWidth === 0) {\n      this._pane.style.minWidth = formatCssUnit(this._config.minWidth);\n    }\n\n    if (this._config.minHeight || this._config.minHeight === 0) {\n      this._pane.style.minHeight = formatCssUnit(this._config.minHeight);\n    }\n\n    if (this._config.maxWidth || this._config.maxWidth === 0) {\n      this._pane.style.maxWidth = formatCssUnit(this._config.maxWidth);\n    }\n\n    if (this._config.maxHeight || this._config.maxHeight === 0) {\n      this._pane.style.maxHeight = formatCssUnit(this._config.maxHeight);\n    }\n  }\n\n  /** Toggles the pointer events for the overlay pane element. */\n  private _togglePointerEvents(enablePointer: boolean) {\n    this._pane.style.pointerEvents = enablePointer ? 'auto' : 'none';\n  }\n\n  /** Attaches a backdrop for this overlay. */\n  private _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n\n    this._backdropElement = this._document.createElement('div');\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n\n    if (this._config.backdropClass) {\n      this._backdropElement.classList.add(this._config.backdropClass);\n    }\n\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._pane.parentElement!.insertBefore(this._backdropElement, this._pane);\n\n    // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n    this._backdropElement.addEventListener('click',\n        (event: MouseEvent) => this._backdropClick.next(event));\n\n    // Add class to fade-in the backdrop after one frame.\n    if (typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  private _updateStackingOrder() {\n    if (this._pane.nextSibling) {\n      this._pane.parentNode!.appendChild(this._pane);\n    }\n  }\n\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop(): void {\n    let backdropToDetach = this._backdropElement;\n\n    if (backdropToDetach) {\n      let finishDetach = () => {\n        // It may not be attached to anything in certain cases (e.g. unit tests).\n        if (backdropToDetach && backdropToDetach.parentNode) {\n          backdropToDetach.parentNode.removeChild(backdropToDetach);\n        }\n\n        // It is possible that a new portal has been attached to this overlay since we started\n        // removing the backdrop. If that is the case, only clear the backdrop reference if it\n        // is still the same instance that we started to remove.\n        if (this._backdropElement == backdropToDetach) {\n          this._backdropElement = null;\n        }\n      };\n\n      backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n\n      if (this._config.backdropClass) {\n        backdropToDetach.classList.remove(this._config.backdropClass);\n      }\n\n      backdropToDetach.addEventListener('transitionend', finishDetach);\n\n      // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n      // In this case we make it unclickable and we try to remove it after a delay.\n      backdropToDetach.style.pointerEvents = 'none';\n\n      // Run this outside the Angular zone because there's nothing that Angular cares about.\n      // If it were to run inside the Angular zone, every test that used Overlay would have to be\n      // either async or fakeAsync.\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(finishDetach, 500);\n      });\n    }\n  }\n}\n\nfunction formatCssUnit(value: number | string) {\n  return typeof value === 'string' ? value as string : `${value}px`;\n}\n\n\n/** Size properties for an overlay. */\nexport interface OverlaySizeConfig {\n  width?: number | string;\n  height?: number | string;\n  minWidth?: number | string;\n  minHeight?: number | string;\n  maxWidth?: number | string;\n  maxHeight?: number | string;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, Inject, InjectionToken, Optional, SkipSelf, OnDestroy} from '@angular/core';\nimport {OverlayRef} from '../overlay-ref';\nimport {Subscription} from 'rxjs/Subscription';\nimport {filter} from 'rxjs/operators/filter';\nimport {fromEvent} from 'rxjs/observable/fromEvent';\nimport {DOCUMENT} from '@angular/common';\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n@Injectable()\nexport class OverlayKeyboardDispatcher implements OnDestroy {\n\n  /** Currently attached overlays in the order they were attached. */\n  _attachedOverlays: OverlayRef[] = [];\n\n  private _keydownEventSubscription: Subscription | null;\n\n  constructor(@Inject(DOCUMENT) private _document: any) {}\n\n  ngOnDestroy() {\n    this._unsubscribeFromKeydownEvents();\n  }\n\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef: OverlayRef): void {\n    // Lazily start dispatcher once first overlay is added\n    if (!this._keydownEventSubscription) {\n      this._subscribeToKeydownEvents();\n    }\n\n    this._attachedOverlays.push(overlayRef);\n  }\n\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef: OverlayRef): void {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this._unsubscribeFromKeydownEvents();\n    }\n  }\n\n  /**\n   * Subscribe to keydown events that land on the body and dispatch those\n   * events to the appropriate overlay.\n   */\n  private _subscribeToKeydownEvents(): void {\n    const bodyKeydownEvents = fromEvent<KeyboardEvent>(this._document.body, 'keydown', true);\n\n    this._keydownEventSubscription = bodyKeydownEvents.pipe(\n      filter(() => !!this._attachedOverlays.length)\n    ).subscribe(event => {\n      // Dispatch keydown event to the correct overlay.\n      this._selectOverlayFromEvent(event)._keydownEvents.next(event);\n    });\n  }\n\n  /** Removes the global keydown subscription. */\n  private _unsubscribeFromKeydownEvents(): void {\n    if (this._keydownEventSubscription) {\n      this._keydownEventSubscription.unsubscribe();\n      this._keydownEventSubscription = null;\n    }\n  }\n\n  /** Select the appropriate overlay from a keydown event. */\n  private _selectOverlayFromEvent(event: KeyboardEvent): OverlayRef {\n    // Check if any overlays contain the event\n    const targetedOverlay = this._attachedOverlays.find(overlay => {\n      return overlay.overlayElement === event.target ||\n          overlay.overlayElement.contains(event.target as HTMLElement);\n    });\n\n    // Use the overlay if it exists, otherwise choose the most recently attached one\n    return targetedOverlay || this._attachedOverlays[this._attachedOverlays.length - 1];\n  }\n\n}\n\n/** @docs-private */\nexport function OVERLAY_KEYBOARD_DISPATCHER_PROVIDER_FACTORY(\n    dispatcher: OverlayKeyboardDispatcher, _document: any) {\n  return dispatcher || new OverlayKeyboardDispatcher(_document);\n}\n\n/** @docs-private */\nexport const OVERLAY_KEYBOARD_DISPATCHER_PROVIDER = {\n  // If there is already an OverlayKeyboardDispatcher available, use that.\n  // Otherwise, provide a new one.\n  provide: OverlayKeyboardDispatcher,\n  deps: [\n    [new Optional(), new SkipSelf(), OverlayKeyboardDispatcher],\n\n    // Coerce to `InjectionToken` so that the `deps` match the \"shape\"\n    // of the type expected by Angular\n    DOCUMENT as InjectionToken<any>\n  ],\n  useFactory: OVERLAY_KEYBOARD_DISPATCHER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, InjectionToken, Inject, Optional, SkipSelf, OnDestroy} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\n\n\n/** Container inside which all overlays will render. */\n@Injectable()\nexport class OverlayContainer implements OnDestroy {\n  protected _containerElement: HTMLElement;\n\n  constructor(@Inject(DOCUMENT) private _document: any) {}\n\n  ngOnDestroy() {\n    if (this._containerElement && this._containerElement.parentNode) {\n      this._containerElement.parentNode.removeChild(this._containerElement);\n    }\n  }\n\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time  it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement(): HTMLElement {\n    if (!this._containerElement) { this._createContainer(); }\n    return this._containerElement;\n  }\n\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  protected _createContainer(): void {\n    const container = this._document.createElement('div');\n\n    container.classList.add('cdk-overlay-container');\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n}\n\n/** @docs-private */\nexport function OVERLAY_CONTAINER_PROVIDER_FACTORY(parentContainer: OverlayContainer,\n  _document: any) {\n  return parentContainer || new OverlayContainer(_document);\n}\n\n/** @docs-private */\nexport const OVERLAY_CONTAINER_PROVIDER = {\n  // If there is already an OverlayContainer available, use that. Otherwise, provide a new one.\n  provide: OverlayContainer,\n  deps: [\n    [new Optional(), new SkipSelf(), OverlayContainer],\n    DOCUMENT as InjectionToken<any> // We need to use the InjectionToken somewhere to keep TS happy\n  ],\n  useFactory: OVERLAY_CONTAINER_PROVIDER_FACTORY\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {ESCAPE} from '@angular/cdk/keycodes';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Optional,\n  Output,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Overlay} from './overlay';\nimport {OverlayConfig} from './overlay-config';\nimport {OverlayRef} from './overlay-ref';\nimport {\n  ConnectedOverlayPositionChange,\n  ConnectionPositionPair,\n} from './position/connected-position';\nimport {ConnectedPositionStrategy} from './position/connected-position-strategy';\nimport {RepositionScrollStrategy, ScrollStrategy} from './scroll/index';\n\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n  new ConnectionPositionPair(\n      {originX: 'start', originY: 'bottom'},\n      {overlayX: 'start', overlayY: 'top'}),\n  new ConnectionPositionPair(\n      {originX: 'start', originY: 'top'},\n      {overlayX: 'start', overlayY: 'bottom'}),\n  new ConnectionPositionPair(\n    {originX: 'end', originY: 'top'},\n    {overlayX: 'end', overlayY: 'bottom'}),\n  new ConnectionPositionPair(\n    {originX: 'end', originY: 'bottom'},\n    {overlayX: 'end', overlayY: 'top'}),\n];\n\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY =\n    new InjectionToken<() => ScrollStrategy>('cdk-connected-overlay-scroll-strategy');\n\n/** @docs-private */\nexport function CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay: Overlay):\n    () => RepositionScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/** @docs-private */\nexport const CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n  exportAs: 'cdkOverlayOrigin',\n})\nexport class CdkOverlayOrigin {\n  constructor(\n      /** Reference to the element on which the directive is applied. */\n      public elementRef: ElementRef) { }\n}\n\n\n/**\n * Directive to facilitate declarative creation of an Overlay using a ConnectedPositionStrategy.\n */\n@Directive({\n  selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n  exportAs: 'cdkConnectedOverlay'\n})\nexport class CdkConnectedOverlay implements OnDestroy, OnChanges {\n  private _overlayRef: OverlayRef;\n  private _templatePortal: TemplatePortal;\n  private _hasBackdrop = false;\n  private _backdropSubscription = Subscription.EMPTY;\n  private _offsetX: number = 0;\n  private _offsetY: number = 0;\n  private _position: ConnectedPositionStrategy;\n\n  /** Origin for the connected overlay. */\n  @Input('cdkConnectedOverlayOrigin') origin: CdkOverlayOrigin;\n\n  /** Registered connected position pairs. */\n  @Input('cdkConnectedOverlayPositions') positions: ConnectionPositionPair[];\n\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  @Input('cdkConnectedOverlayOffsetX')\n  get offsetX(): number { return this._offsetX; }\n  set offsetX(offsetX: number) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._position.withOffsetX(offsetX);\n    }\n  }\n\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  @Input('cdkConnectedOverlayOffsetY')\n  get offsetY() { return this._offsetY; }\n  set offsetY(offsetY: number) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._position.withOffsetY(offsetY);\n    }\n  }\n\n  /** The width of the overlay panel. */\n  @Input('cdkConnectedOverlayWidth') width: number | string;\n\n  /** The height of the overlay panel. */\n  @Input('cdkConnectedOverlayHeight') height: number | string;\n\n  /** The min width of the overlay panel. */\n  @Input('cdkConnectedOverlayMinWidth') minWidth: number | string;\n\n  /** The min height of the overlay panel. */\n  @Input('cdkConnectedOverlayMinHeight') minHeight: number | string;\n\n  /** The custom class to be set on the backdrop element. */\n  @Input('cdkConnectedOverlayBackdropClass') backdropClass: string;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  @Input('cdkConnectedOverlayScrollStrategy') scrollStrategy: ScrollStrategy =\n      this._scrollStrategy();\n\n  /** Whether the overlay is open. */\n  @Input('cdkConnectedOverlayOpen') open: boolean = false;\n\n  /** Whether or not the overlay should attach a backdrop. */\n  @Input('cdkConnectedOverlayHasBackdrop')\n  get hasBackdrop() { return this._hasBackdrop; }\n  set hasBackdrop(value: any) { this._hasBackdrop = coerceBooleanProperty(value); }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('origin')\n  get _deprecatedOrigin(): CdkOverlayOrigin { return this.origin; }\n  set _deprecatedOrigin(_origin: CdkOverlayOrigin) { this.origin = _origin; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('positions')\n  get _deprecatedPositions(): ConnectionPositionPair[] { return this.positions; }\n  set _deprecatedPositions(_positions: ConnectionPositionPair[]) { this.positions = _positions; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('offsetX')\n  get _deprecatedOffsetX(): number { return this.offsetX; }\n  set _deprecatedOffsetX(_offsetX: number) { this.offsetX = _offsetX; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('offsetY')\n  get _deprecatedOffsetY(): number { return this.offsetY; }\n  set _deprecatedOffsetY(_offsetY: number) { this.offsetY = _offsetY; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('width')\n  get _deprecatedWidth(): number | string { return this.width; }\n  set _deprecatedWidth(_width: number | string) { this.width = _width; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('height')\n  get _deprecatedHeight(): number | string { return this.height; }\n  set _deprecatedHeight(_height: number | string) { this.height = _height; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('minWidth')\n  get _deprecatedMinWidth(): number | string { return this.minWidth; }\n  set _deprecatedMinWidth(_minWidth: number | string) { this.minWidth = _minWidth; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('minHeight')\n  get _deprecatedMinHeight(): number | string { return this.minHeight; }\n  set _deprecatedMinHeight(_minHeight: number | string) { this.minHeight = _minHeight; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('backdropClass')\n  get _deprecatedBackdropClass(): string { return this.backdropClass; }\n  set _deprecatedBackdropClass(_backdropClass: string) { this.backdropClass = _backdropClass; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('scrollStrategy')\n  get _deprecatedScrollStrategy(): ScrollStrategy { return this.scrollStrategy; }\n  set _deprecatedScrollStrategy(_scrollStrategy: ScrollStrategy) {\n    this.scrollStrategy = _scrollStrategy;\n  }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('open')\n  get _deprecatedOpen(): boolean { return this.open; }\n  set _deprecatedOpen(_open: boolean) { this.open = _open; }\n\n  /**\n   * @deprecated\n   * @deletion-target 6.0.0\n   */\n  @Input('hasBackdrop')\n  get _deprecatedHasBackdrop() { return this.hasBackdrop; }\n  set _deprecatedHasBackdrop(_hasBackdrop: any) { this.hasBackdrop = _hasBackdrop; }\n\n  /** Event emitted when the backdrop is clicked. */\n  @Output() backdropClick = new EventEmitter<void>();\n\n  /** Event emitted when the position has changed. */\n  @Output() positionChange = new EventEmitter<ConnectedOverlayPositionChange>();\n\n  /** Event emitted when the overlay has been attached. */\n  @Output() attach = new EventEmitter<void>();\n\n  /** Event emitted when the overlay has been detached. */\n  @Output() detach = new EventEmitter<void>();\n\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n\n  constructor(\n      private _overlay: Overlay,\n      templateRef: TemplateRef<any>,\n      viewContainerRef: ViewContainerRef,\n      @Inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY) private _scrollStrategy,\n      @Optional() private _dir: Directionality) {\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n  }\n\n  /** The associated overlay reference. */\n  get overlayRef(): OverlayRef {\n    return this._overlayRef;\n  }\n\n  /** The element's layout direction. */\n  get dir(): Direction {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n\n  ngOnDestroy() {\n    this._destroyOverlay();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._position) {\n      if (changes['positions'] || changes['_deprecatedPositions']) {\n        this._position.withPositions(this.positions);\n      }\n\n      if (changes['origin'] || changes['_deprecatedOrigin']) {\n        this._position.setOrigin(this.origin.elementRef);\n\n        if (this.open) {\n          this._position.apply();\n        }\n      }\n    }\n\n    if (changes['open'] || changes['_deprecatedOpen']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n\n  /** Creates an overlay */\n  private _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n\n    this._overlayRef = this._overlay.create(this._buildConfig());\n  }\n\n  /** Builds the overlay config based on the directive's inputs */\n  private _buildConfig(): OverlayConfig {\n    const positionStrategy = this._position = this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop\n    });\n\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n\n    return overlayConfig;\n  }\n\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  private _createPositionStrategy(): ConnectedPositionStrategy {\n    const primaryPosition = this.positions[0];\n    const originPoint = {originX: primaryPosition.originX, originY: primaryPosition.originY};\n    const overlayPoint = {overlayX: primaryPosition.overlayX, overlayY: primaryPosition.overlayY};\n    const strategy = this._overlay.position()\n      .connectedTo(this.origin.elementRef, originPoint, overlayPoint)\n      .withOffsetX(this.offsetX)\n      .withOffsetY(this.offsetY);\n\n    for (let i = 1; i < this.positions.length; i++) {\n      strategy.withFallbackPosition(\n          {originX: this.positions[i].originX, originY: this.positions[i].originY},\n          {overlayX: this.positions[i].overlayX, overlayY: this.positions[i].overlayY}\n      );\n    }\n\n    strategy.onPositionChange.subscribe(pos => this.positionChange.emit(pos));\n\n    return strategy;\n  }\n\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n  private _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n\n      this._overlayRef!.keydownEvents().subscribe((event: KeyboardEvent) => {\n        if (event.keyCode === ESCAPE) {\n          this._detachOverlay();\n        }\n      });\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight,\n      });\n    }\n\n    this._position.withDirection(this.dir);\n    this._overlayRef.setDirection(this.dir);\n\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n      this.attach.emit();\n    }\n\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(() => {\n        this.backdropClick.emit();\n      });\n    }\n  }\n\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n  private _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n      this.detach.emit();\n    }\n\n    this._backdropSubscription.unsubscribe();\n  }\n\n  /** Destroys the overlay created by this directive. */\n  private _destroyOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n\n    this._backdropSubscription.unsubscribe();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\n\n/** Scroll strategy that doesn't do anything. */\nexport class NoopScrollStrategy implements ScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() { }\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() { }\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() { }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position/position-strategy';\nimport {Direction} from '@angular/cdk/bidi';\nimport {ScrollStrategy} from './scroll/scroll-strategy';\nimport {NoopScrollStrategy} from './scroll/noop-scroll-strategy';\n\n\n/** Initial configuration used when creating an overlay. */\nexport class OverlayConfig {\n  /** Strategy with which to position the overlay. */\n  positionStrategy?: PositionStrategy;\n\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy?: ScrollStrategy = new NoopScrollStrategy();\n\n  /** Custom class to add to the overlay pane. */\n  panelClass?: string | string[] = '';\n\n  /** Whether the overlay has a backdrop. */\n  hasBackdrop?: boolean = false;\n\n  /** Custom class to add to the backdrop */\n  backdropClass?: string = 'cdk-overlay-dark-backdrop';\n\n  /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n  width?: number | string;\n\n  /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n  height?: number | string;\n\n  /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  minWidth?: number | string;\n\n  /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  minHeight?: number | string;\n\n  /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxWidth?: number | string;\n\n  /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxHeight?: number | string;\n\n  /** The direction of the text in the overlay panel. */\n  direction?: Direction = 'ltr';\n\n  constructor(config?: OverlayConfig) {\n    if (config) {\n      Object.keys(config)\n        .filter(key => typeof config[key] !== 'undefined')\n        .forEach(key => this[key] = config[key]);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Horizontal dimension of a connection point on the perimeter of the origin or overlay element. */\nimport {Optional} from '@angular/core';\nexport type HorizontalConnectionPos = 'start' | 'center' | 'end';\n\n/** Vertical dimension of a connection point on the perimeter of the origin or overlay element. */\nexport type VerticalConnectionPos = 'top' | 'center' | 'bottom';\n\n\n/** A connection point on the origin element. */\nexport interface OriginConnectionPosition {\n  originX: HorizontalConnectionPos;\n  originY: VerticalConnectionPos;\n}\n\n/** A connection point on the overlay element. */\nexport interface OverlayConnectionPosition {\n  overlayX: HorizontalConnectionPos;\n  overlayY: VerticalConnectionPos;\n}\n\n/** The points of the origin element and the overlay element to connect. */\nexport class ConnectionPositionPair {\n  /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n  originX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n  originY: VerticalConnectionPos;\n  /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n  overlayX: HorizontalConnectionPos;\n  /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n  overlayY: VerticalConnectionPos;\n\n  constructor(\n    origin: OriginConnectionPosition,\n    overlay: OverlayConnectionPosition,\n    public offsetX?: number,\n    public offsetY?: number) {\n\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nexport class ScrollingVisibility {\n  isOriginClipped: boolean;\n  isOriginOutsideView: boolean;\n  isOverlayClipped: boolean;\n  isOverlayOutsideView: boolean;\n}\n\n/** The change event emitted by the strategy when a fallback position is used. */\nexport class ConnectedOverlayPositionChange {\n  constructor(\n      /** The position used as a result of this change. */\n      public connectionPair: ConnectionPositionPair,\n      /** @docs-private */\n      @Optional() public scrollableViewProperties: ScrollingVisibility) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {NgZone} from '@angular/core';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {OverlayRef} from '../overlay-ref';\nimport {Subscription} from 'rxjs/Subscription';\nimport {ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\n\n/**\n * Config options for the CloseScrollStrategy.\n */\nexport interface CloseScrollStrategyConfig {\n  /** Amount of pixels the user has to scroll before the overlay is closed. */\n  threshold?: number;\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nexport class CloseScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription|null = null;\n  private _overlayRef: OverlayRef;\n  private _initialScrollPosition: number;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _ngZone: Ng<PERSON>one,\n    private _viewportRuler: ViewportRuler,\n    private _config?: CloseScrollStrategyConfig) {}\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n\n    const stream = this._scrollDispatcher.scrolled(0);\n\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config!.threshold!) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n\n  /** Detaches the overlay ref and disables the scroll strategy. */\n  private _detach = () => {\n    this.disable();\n\n    if (this._overlayRef.hasAttached()) {\n      this._ngZone.run(() => this._overlayRef.detach());\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ScrollStrategy} from './scroll-strategy';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\n\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nexport class BlockScrollStrategy implements ScrollStrategy {\n  private _previousHTMLStyles = { top: '', left: '' };\n  private _previousScrollPosition: { top: number, left: number };\n  private _isEnabled = false;\n  private _document: Document;\n\n  constructor(private _viewportRuler: ViewportRuler, document: any) {\n    this._document = document;\n  }\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach() { }\n\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = `${-this._previousScrollPosition.left}px`;\n      root.style.top = `${-this._previousScrollPosition.top}px`;\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const previousHtmlScrollBehavior = html.style['scrollBehavior'] || '';\n      const previousBodyScrollBehavior = body.style['scrollBehavior'] || '';\n\n      this._isEnabled = false;\n\n      html.style.left = this._previousHTMLStyles.left;\n      html.style.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      html.style['scrollBehavior'] = body.style['scrollBehavior'] = 'auto';\n\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n\n      html.style['scrollBehavior'] = previousHtmlScrollBehavior;\n      body.style['scrollBehavior'] = previousBodyScrollBehavior;\n    }\n  }\n\n  private _canBeEnabled(): boolean {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgZone} from '@angular/core';\nimport {Subscription} from 'rxjs/Subscription';\nimport {ScrollStrategy, getMatScrollStrategyAlreadyAttachedError} from './scroll-strategy';\nimport {OverlayRef} from '../overlay-ref';\nimport {ScrollDispatcher, ViewportRuler} from '@angular/cdk/scrolling';\nimport {isElementScrolledOutsideView} from '../position/scroll-clip';\n\n/**\n * Config options for the RepositionScrollStrategy.\n */\nexport interface RepositionScrollStrategyConfig {\n  /** Time in milliseconds to throttle the scroll events. */\n  scrollThrottle?: number;\n\n  /** Whether to close the overlay once the user has scrolled away completely. */\n  autoClose?: boolean;\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nexport class RepositionScrollStrategy implements ScrollStrategy {\n  private _scrollSubscription: Subscription|null = null;\n  private _overlayRef: OverlayRef;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewportRuler: ViewportRuler,\n    private _ngZone: NgZone,\n    private _config?: RepositionScrollStrategyConfig) { }\n\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef: OverlayRef) {\n    if (this._overlayRef) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {width, height} = this._viewportRuler.getViewportSize();\n\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{width, height, bottom: height, right: width, top: 0, left: 0}];\n\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Injectable, NgZone, Inject} from '@angular/core';\nimport {CloseScrollStrategy, CloseScrollStrategyConfig} from './close-scroll-strategy';\nimport {NoopScrollStrategy} from './noop-scroll-strategy';\nimport {BlockScrollStrategy} from './block-scroll-strategy';\nimport {ScrollDispatcher} from '@angular/cdk/scrolling';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  RepositionScrollStrategy,\n  RepositionScrollStrategyConfig,\n} from './reposition-scroll-strategy';\n\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\n@Injectable()\nexport class ScrollStrategyOptions {\n  private _document: Document;\n\n  constructor(\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewportRuler: ViewportRuler,\n    private _ngZone: NgZone,\n    @Inject(DOCUMENT) document: any) {\n      this._document = document;\n    }\n\n  /** Do nothing on scroll. */\n  noop = () => new NoopScrollStrategy();\n\n  /**\n   * Close the overlay as soon as the user scrolls.\n   * @param config Configuration to be used inside the scroll strategy.\n   */\n  close = (config?: CloseScrollStrategyConfig) => new CloseScrollStrategy(this._scrollDispatcher,\n      this._ngZone, this._viewportRuler, config)\n\n  /** Block scrolling. */\n  block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n\n  /**\n   * Update the overlay's position on scroll.\n   * @param config Configuration to be used inside the scroll strategy.\n   * Allows debouncing the reposition calls.\n   */\n  reposition = (config?: RepositionScrollStrategyConfig) => new RepositionScrollStrategy(\n      this._scrollDispatcher, this._viewportRuler, this._ngZone, config)\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position-strategy';\nimport {ElementRef} from '@angular/core';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {\n  ConnectionPositionPair,\n  OriginConnectionPosition,\n  OverlayConnectionPosition,\n  ConnectedOverlayPositionChange,\n  ScrollingVisibility,\n} from './connected-position';\nimport {Subject} from 'rxjs/Subject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Observable} from 'rxjs/Observable';\nimport {CdkScrollable} from '@angular/cdk/scrolling';\nimport {isElementScrolledOutsideView, isElementClippedByScrolling} from './scroll-clip';\nimport {OverlayRef} from '../overlay-ref';\n\n\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nexport class ConnectedPositionStrategy implements PositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  private _overlayRef: OverlayRef;\n\n  /** Layout direction of the position strategy. */\n  private _dir = 'ltr';\n\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  private _offsetX: number = 0;\n\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  private _offsetY: number = 0;\n\n  /** The Scrollable containers used to check scrollable view properties on position change. */\n  private scrollables: CdkScrollable[] = [];\n\n  /** Subscription to viewport resize events. */\n  private _resizeSubscription = Subscription.EMPTY;\n\n  /** Whether the we're dealing with an RTL context */\n  get _isRtl() {\n    return this._dir === 'rtl';\n  }\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  _preferredPositions: ConnectionPositionPair[] = [];\n\n  /** The origin element against which the overlay will be positioned. */\n  private _origin: HTMLElement;\n\n  /** The overlay pane element. */\n  private _pane: HTMLElement;\n\n  /** The last position to have been calculated as the best fit position. */\n  private _lastConnectedPosition: ConnectionPositionPair;\n\n  /** Whether the position strategy is applied currently. */\n  private _applied = false;\n\n  /** Whether the overlay position is locked. */\n  private _positionLocked = false;\n\n  private _onPositionChange = new Subject<ConnectedOverlayPositionChange>();\n\n  /** Emits an event when the connection point changes. */\n  get onPositionChange(): Observable<ConnectedOverlayPositionChange> {\n    return this._onPositionChange.asObservable();\n  }\n\n  constructor(\n      originPos: OriginConnectionPosition,\n      overlayPos: OverlayConnectionPosition,\n      private _connectedTo: ElementRef,\n      private _viewportRuler: ViewportRuler,\n      private _document: any) {\n    this._origin = this._connectedTo.nativeElement;\n    this.withFallbackPosition(originPos, overlayPos);\n  }\n\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions(): ConnectionPositionPair[] {\n    return this._preferredPositions;\n  }\n\n  /** Attach this position strategy to an overlay. */\n  attach(overlayRef: OverlayRef): void {\n    this._overlayRef = overlayRef;\n    this._pane = overlayRef.overlayElement;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => this.apply());\n  }\n\n  /** Disposes all resources used by the position strategy. */\n  dispose() {\n    this._applied = false;\n    this._resizeSubscription.unsubscribe();\n    this._onPositionChange.complete();\n  }\n\n  /** @docs-private */\n  detach() {\n    this._applied = false;\n    this._resizeSubscription.unsubscribe();\n  }\n\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin fits on-screen.\n   * @docs-private\n   */\n  apply(): void {\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the  old position, in order to\n    // prevent the overlay from jumping around.\n    if (this._applied && this._positionLocked && this._lastConnectedPosition) {\n      this.recalculateLastPosition();\n      return;\n    }\n\n    this._applied = true;\n\n    // We need the bounding rects for the origin and the overlay to determine how to position\n    // the overlay relative to the origin.\n    const element = this._pane;\n    const originRect = this._origin.getBoundingClientRect();\n    const overlayRect = element.getBoundingClientRect();\n\n    // We use the viewport size to determine whether a position would go off-screen.\n    const viewportSize = this._viewportRuler.getViewportSize();\n\n    // Fallback point if none of the fallbacks fit into the viewport.\n    let fallbackPoint: OverlayPoint | undefined;\n    let fallbackPosition: ConnectionPositionPair | undefined;\n\n    // We want to place the overlay in the first of the preferred positions such that the\n    // overlay fits on-screen.\n    for (let pos of this._preferredPositions) {\n      // Get the (x, y) point of connection on the origin, and then use that to get the\n      // (top, left) coordinate for the overlay at `pos`.\n      let originPoint = this._getOriginConnectionPoint(originRect, pos);\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, viewportSize, pos);\n\n      // If the overlay in the calculated position fits on-screen, put it there and we're done.\n      if (overlayPoint.fitsInViewport) {\n        this._setElementPosition(element, overlayRect, overlayPoint, pos);\n\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastConnectedPosition = pos;\n\n        return;\n      } else if (!fallbackPoint || fallbackPoint.visibleArea < overlayPoint.visibleArea) {\n        fallbackPoint = overlayPoint;\n        fallbackPosition = pos;\n      }\n    }\n\n    // If none of the preferred positions were in the viewport, take the one\n    // with the largest visible area.\n    this._setElementPosition(element, overlayRect, fallbackPoint!, fallbackPosition!);\n  }\n\n  /**\n   * Re-positions the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  recalculateLastPosition(): void {\n    // If the overlay has never been positioned before, do nothing.\n    if (!this._lastConnectedPosition) {\n      return;\n    }\n\n    const originRect = this._origin.getBoundingClientRect();\n    const overlayRect = this._pane.getBoundingClientRect();\n    const viewportSize = this._viewportRuler.getViewportSize();\n    const lastPosition = this._lastConnectedPosition || this._preferredPositions[0];\n\n    let originPoint = this._getOriginConnectionPoint(originRect, lastPosition);\n    let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, viewportSize, lastPosition);\n    this._setElementPosition(this._pane, overlayRect, overlayPoint, lastPosition);\n  }\n\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables: CdkScrollable[]) {\n    this.scrollables = scrollables;\n  }\n\n  /**\n   * Adds a new preferred fallback position.\n   * @param originPos\n   * @param overlayPos\n   */\n  withFallbackPosition(\n      originPos: OriginConnectionPosition,\n      overlayPos: OverlayConnectionPosition,\n      offsetX?: number,\n      offsetY?: number): this {\n\n    const position = new ConnectionPositionPair(originPos, overlayPos, offsetX, offsetY);\n    this._preferredPositions.push(position);\n    return this;\n  }\n\n  /**\n   * Sets the layout direction so the overlay's position can be adjusted to match.\n   * @param dir New layout direction.\n   */\n  withDirection(dir: 'ltr' | 'rtl'): this {\n    this._dir = dir;\n    return this;\n  }\n\n  /**\n   * Sets an offset for the overlay's connection point on the x-axis\n   * @param offset New offset in the X axis.\n   */\n  withOffsetX(offset: number): this {\n    this._offsetX = offset;\n    return this;\n  }\n\n  /**\n   * Sets an offset for the overlay's connection point on the y-axis\n   * @param  offset New offset in the Y axis.\n   */\n  withOffsetY(offset: number): this {\n    this._offsetY = offset;\n    return this;\n  }\n\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked: boolean): this {\n    this._positionLocked = isLocked;\n    return this;\n  }\n\n  /**\n   * Overwrites the current set of positions with an array of new ones.\n   * @param positions Position pairs to be set on the strategy.\n   */\n  withPositions(positions: ConnectionPositionPair[]): this {\n    this._preferredPositions = positions.slice();\n    return this;\n  }\n\n  /**\n   * Sets the origin element, relative to which to position the overlay.\n   * @param origin Reference to the new origin element.\n   */\n  setOrigin(origin: ElementRef): this {\n    this._origin = origin.nativeElement;\n    return this;\n  }\n\n  /**\n   * Gets the horizontal (x) \"start\" dimension based on whether the overlay is in an RTL context.\n   * @param rect\n   */\n  private _getStartX(rect: ClientRect): number {\n    return this._isRtl ? rect.right : rect.left;\n  }\n\n  /**\n   * Gets the horizontal (x) \"end\" dimension based on whether the overlay is in an RTL context.\n   * @param rect\n   */\n  private _getEndX(rect: ClientRect): number {\n    return this._isRtl ? rect.left : rect.right;\n  }\n\n\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   * @param originRect\n   * @param pos\n   */\n  private _getOriginConnectionPoint(originRect: ClientRect, pos: ConnectionPositionPair): Point {\n    const originStartX = this._getStartX(originRect);\n    const originEndX = this._getEndX(originRect);\n\n    let x: number;\n    if (pos.originX == 'center') {\n      x = originStartX + (originRect.width / 2);\n    } else {\n      x = pos.originX == 'start' ? originStartX : originEndX;\n    }\n\n    let y: number;\n    if (pos.originY == 'center') {\n      y = originRect.top + (originRect.height / 2);\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n\n    return {x, y};\n  }\n\n\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected, as well as how much of the element\n   * would be inside the viewport at that position.\n   */\n  private _getOverlayPoint(\n      originPoint: Point,\n      overlayRect: ClientRect,\n      viewportSize: {width: number; height: number},\n      pos: ConnectionPositionPair): OverlayPoint {\n    // Calculate the (overlayStartX, overlayStartY), the start of the potential overlay position\n    // relative to the origin point.\n    let overlayStartX: number;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl ? 0 : -overlayRect.width;\n    }\n\n    let overlayStartY: number;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n\n    // The (x, y) offsets of the overlay based on the current position.\n    let offsetX = typeof pos.offsetX === 'undefined' ? this._offsetX : pos.offsetX;\n    let offsetY = typeof pos.offsetY === 'undefined' ? this._offsetY : pos.offsetY;\n\n    // The (x, y) coordinates of the overlay.\n    let x = originPoint.x + overlayStartX + offsetX;\n    let y = originPoint.y + overlayStartY + offsetY;\n\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = (x + overlayRect.width) - viewportSize.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = (y + overlayRect.height) - viewportSize.height;\n\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlayRect.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlayRect.height, topOverflow, bottomOverflow);\n\n    // The area of the element that's within the viewport.\n    let visibleArea = visibleWidth * visibleHeight;\n    let fitsInViewport = (overlayRect.width * overlayRect.height) === visibleArea;\n\n    return {x, y, fitsInViewport, visibleArea};\n  }\n\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  private _getScrollVisibility(overlay: HTMLElement): ScrollingVisibility {\n    const originBounds = this._origin.getBoundingClientRect();\n    const overlayBounds = overlay.getBoundingClientRect();\n    const scrollContainerBounds =\n        this.scrollables.map(s => s.getElementRef().nativeElement.getBoundingClientRect());\n\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n    };\n  }\n\n  /** Physically positions the overlay element to the given coordinate. */\n  private _setElementPosition(\n      element: HTMLElement,\n      overlayRect: ClientRect,\n      overlayPoint: Point,\n      pos: ConnectionPositionPair) {\n\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear above\n    // or below the origin and the direction in which the element will expand.\n    let verticalStyleProperty = pos.overlayY === 'bottom' ? 'bottom' : 'top';\n\n    // When using `bottom`, we adjust the y position such that it is the distance\n    // from the bottom of the viewport rather than the top.\n    let y = verticalStyleProperty === 'top' ?\n        overlayPoint.y :\n        this._document.documentElement.clientHeight - (overlayPoint.y + overlayRect.height);\n\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty: string;\n    if (this._dir === 'rtl') {\n      horizontalStyleProperty = pos.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = pos.overlayX === 'end' ? 'right' : 'left';\n    }\n\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    let x = horizontalStyleProperty === 'left' ?\n      overlayPoint.x :\n      this._document.documentElement.clientWidth - (overlayPoint.x + overlayRect.width);\n\n\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    ['top', 'bottom', 'left', 'right'].forEach(p => element.style[p] = null);\n\n    element.style[verticalStyleProperty] = `${y}px`;\n    element.style[horizontalStyleProperty] = `${x}px`;\n\n    // Notify that the position has been changed along with its change properties.\n    const scrollableViewProperties = this._getScrollVisibility(element);\n    const positionChange = new ConnectedOverlayPositionChange(pos, scrollableViewProperties);\n    this._onPositionChange.next(positionChange);\n  }\n\n  /**\n   * Subtracts the amount that an element is overflowing on an axis from it's length.\n   */\n  private _subtractOverflows(length: number, ...overflows: number[]): number {\n    return overflows.reduce((currentValue: number, currentOverflow: number) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n}\n\n/** A simple (x, y) coordinate. */\ninterface Point {\n  x: number;\n  y: number;\n}\n\n/**\n * Expands the simple (x, y) coordinate by adding info about whether the\n * element would fit inside the viewport at that position, as well as\n * how much of the element would be visible.\n */\ninterface OverlayPoint extends Point {\n  visibleArea: number;\n  fitsInViewport: boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {PositionStrategy} from './position-strategy';\nimport {OverlayRef} from '../overlay-ref';\n\n\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nexport class GlobalPositionStrategy implements PositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  private _overlayRef: OverlayRef;\n\n  private _cssPosition = 'static';\n  private _topOffset = '';\n  private _bottomOffset = '';\n  private _leftOffset = '';\n  private _rightOffset = '';\n  private _alignItems = '';\n  private _justifyContent = '';\n  private _width = '';\n  private _height = '';\n\n  /** A lazily-created wrapper for the overlay element that is used as a flex container. */\n  private _wrapper: HTMLElement | null = null;\n\n  constructor(private _document: any) {}\n\n  attach(overlayRef: OverlayRef): void {\n    const config = overlayRef.getConfig();\n\n    this._overlayRef = overlayRef;\n\n    if (this._width && !config.width) {\n      overlayRef.updateSize({width: this._width});\n    }\n\n    if (this._height && !config.height) {\n      overlayRef.updateSize({height: this._height});\n    }\n  }\n\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value: string = ''): this {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value: string = ''): this {\n    this._rightOffset = '';\n    this._leftOffset = value;\n    this._justifyContent = 'flex-start';\n    return this;\n  }\n\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value: string = ''): this {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value: string = ''): this {\n    this._leftOffset = '';\n    this._rightOffset = value;\n    this._justifyContent = 'flex-end';\n    return this;\n  }\n\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @deletion-target 7.0.0\n   */\n  width(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({width: value});\n    } else {\n      this._width = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @deletion-target 7.0.0\n   */\n  height(value: string = ''): this {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({height: value});\n    } else {\n      this._height = value;\n    }\n\n    return this;\n  }\n\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset: string = ''): this {\n    this.left(offset);\n    this._justifyContent = 'center';\n    return this;\n  }\n\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset: string = ''): this {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   *\n   * @returns Resolved when the styles have been applied.\n   */\n  apply(): void {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef.hasAttached()) {\n      return;\n    }\n\n    const element = this._overlayRef.overlayElement;\n\n    if (!this._wrapper && element.parentNode) {\n      this._wrapper = this._document.createElement('div');\n      this._wrapper!.classList.add('cdk-global-overlay-wrapper');\n      element.parentNode.insertBefore(this._wrapper!, element);\n      this._wrapper!.appendChild(element);\n    }\n\n    const styles = element.style;\n    const parentStyles = (element.parentNode as HTMLElement).style;\n    const config = this._overlayRef.getConfig();\n\n    styles.position = this._cssPosition;\n    styles.marginLeft = config.width === '100%' ? '0' : this._leftOffset;\n    styles.marginTop = config.height === '100%' ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = this._rightOffset;\n\n    parentStyles.justifyContent = config.width === '100%' ? 'flex-start' : this._justifyContent;\n    parentStyles.alignItems = config.height === '100%' ? 'flex-start' : this._alignItems;\n  }\n\n  /** Removes the wrapper element from the DOM. */\n  dispose(): void {\n    if (this._wrapper && this._wrapper.parentNode) {\n      this._wrapper.parentNode.removeChild(this._wrapper);\n      this._wrapper = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ElementRef, Injectable, Inject} from '@angular/core';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {ConnectedPositionStrategy} from './connected-position-strategy';\nimport {GlobalPositionStrategy} from './global-position-strategy';\nimport {OverlayConnectionPosition, OriginConnectionPosition} from './connected-position';\nimport {DOCUMENT} from '@angular/common';\n\n\n/** Builder for overlay position strategy. */\n@Injectable()\nexport class OverlayPositionBuilder {\n  constructor(private _viewportRuler: ViewportRuler,\n              @Inject(DOCUMENT) private _document: any) { }\n\n  /**\n   * Creates a global position strategy.\n   */\n  global(): GlobalPositionStrategy {\n    return new GlobalPositionStrategy(this._document);\n  }\n\n  /**\n   * Creates a relative position strategy.\n   * @param elementRef\n   * @param originPos\n   * @param overlayPos\n   */\n  connectedTo(\n      elementRef: ElementRef,\n      originPos: OriginConnectionPosition,\n      overlayPos: OverlayConnectionPosition): ConnectedPositionStrategy {\n\n    return new ConnectedPositionStrategy(originPos, overlayPos, elementRef,\n        this._viewportRuler, this._document);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentFactoryResolver,\n  Injectable,\n  ApplicationRef,\n  Injector,\n  NgZone,\n  Inject,\n} from '@angular/core';\nimport {DomPortalOutlet} from '@angular/cdk/portal';\nimport {OverlayConfig} from './overlay-config';\nimport {OverlayRef} from './overlay-ref';\nimport {OverlayPositionBuilder} from './position/overlay-position-builder';\nimport {OverlayKeyboardDispatcher} from './keyboard/overlay-keyboard-dispatcher';\nimport {OverlayContainer} from './overlay-container';\nimport {ScrollStrategyOptions} from './scroll/index';\nimport {DOCUMENT} from '@angular/common';\n\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\n@Injectable()\nexport class Overlay {\n  constructor(\n              /** Scrolling strategies that can be used when creating an overlay. */\n              public scrollStrategies: ScrollStrategyOptions,\n              private _overlayContainer: OverlayContainer,\n              private _componentFactoryResolver: ComponentFactoryResolver,\n              private _positionBuilder: OverlayPositionBuilder,\n              private _keyboardDispatcher: OverlayKeyboardDispatcher,\n              private _appRef: ApplicationRef,\n              private _injector: Injector,\n              private _ngZone: NgZone,\n              @Inject(DOCUMENT) private _document: any) { }\n\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config?: OverlayConfig): OverlayRef {\n    const pane = this._createPaneElement();\n    const portalOutlet = this._createPortalOutlet(pane);\n\n    return new OverlayRef(\n      portalOutlet,\n      pane,\n      new OverlayConfig(config),\n      this._ngZone,\n      this._keyboardDispatcher,\n      this._document\n    );\n  }\n\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position(): OverlayPositionBuilder {\n    return this._positionBuilder;\n  }\n\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  private _createPaneElement(): HTMLElement {\n    const pane = this._document.createElement('div');\n\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    this._overlayContainer.getContainerElement().appendChild(pane);\n\n    return pane;\n  }\n\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  private _createPortalOutlet(pane: HTMLElement): DomPortalOutlet {\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector);\n  }\n\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {BidiModule} from '@angular/cdk/bidi';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {ScrollDispatchModule, VIEWPORT_RULER_PROVIDER} from '@angular/cdk/scrolling';\nimport {NgModule, Provider} from '@angular/core';\nimport {Overlay} from './overlay';\nimport {OVERLAY_CONTAINER_PROVIDER} from './overlay-container';\nimport {\n  CdkConnectedOverlay,\n  CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER,\n  CdkOverlayOrigin,\n} from './overlay-directives';\nimport {OverlayPositionBuilder} from './position/overlay-position-builder';\nimport {OVERLAY_KEYBOARD_DISPATCHER_PROVIDER} from './keyboard/overlay-keyboard-dispatcher';\nimport {ScrollStrategyOptions} from './scroll/scroll-strategy-options';\n\nexport const OVERLAY_PROVIDERS: Provider[] = [\n  Overlay,\n  OverlayPositionBuilder,\n  OVERLAY_KEYBOARD_DISPATCHER_PROVIDER,\n  VIEWPORT_RULER_PROVIDER,\n  OVERLAY_CONTAINER_PROVIDER,\n  CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER,\n];\n\n@NgModule({\n  imports: [BidiModule, PortalModule, ScrollDispatchModule],\n  exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollDispatchModule],\n  declarations: [CdkConnectedOverlay, CdkOverlayOrigin],\n  providers: [OVERLAY_PROVIDERS, ScrollStrategyOptions],\n})\nexport class OverlayModule {}\n"], "names": ["type", "Injectable", "FullscreenOverlayContainer", "OverlayContainer", "__extends", "d", "b", "__", "this", "constructor", "extendStatics", "prototype", "Object", "create", "getMatScrollStrategyAlreadyAttachedError", "Error", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "top", "outsideBelow", "outsideLeft", "right", "left", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "formatCssUnit", "value", "OVERLAY_KEYBOARD_DISPATCHER_PROVIDER_FACTORY", "dispatcher", "_document", "OverlayKeyboardDispatcher", "OVERLAY_CONTAINER_PROVIDER_FACTORY", "parentContainer", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "reposition", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__assign", "assign", "t", "s", "i", "n", "arguments", "length", "call", "NoopScrollStrategy", "enable", "disable", "attach", "OverlayConfig", "config", "_this", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "direction", "keys", "filter", "key", "for<PERSON>ach", "ConnectionPositionPair", "origin", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "ScrollingVisibility", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "decorators", "Optional", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_viewportRuler", "_config", "_scrollSubscription", "_detach", "_overlayRef", "has<PERSON>tta<PERSON>", "run", "detach", "overlayRef", "stream", "scrolled", "threshold", "_initialScrollPosition", "getViewportScrollPosition", "subscribe", "scrollPosition", "Math", "abs", "updatePosition", "unsubscribe", "BlockScrollStrategy", "document", "_previousHTMLStyles", "_isEnabled", "_canBeEnabled", "root", "documentElement", "_previousScrollPosition", "style", "classList", "add", "html", "body", "previousHtmlScrollBehavior", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "overlayElement", "getBoundingClientRect", "_a", "ScrollStrategyOptions", "noop", "close", "block", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "NgZone", "undefined", "Inject", "args", "DOCUMENT", "OverlayRef", "_portalOutlet", "_pane", "_keyboardDispatcher", "_backdropElement", "_backdropClick", "Subject", "_attachments", "_detachments", "_keydownEvents", "defineProperty", "portal", "attachResult", "positionStrategy", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "onStable", "asObservable", "pipe", "take", "_togglePointerEvents", "_attachBackdrop", "isArray", "cls", "next", "detachBackdrop", "detachmentResult", "dispose", "isAttached", "complete", "backdropClick", "attachments", "detachments", "keydownEvents", "getConfig", "apply", "updateSize", "sizeConfig", "tslib_1.__assign", "setDirection", "dir", "setAttribute", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "enablePointer", "pointerEvents", "createElement", "insertBefore", "addEventListener", "event", "requestAnimationFrame", "runOutsideAngular", "nextS<PERSON>ling", "append<PERSON><PERSON><PERSON>", "backdropToDetach", "finishDetach_1", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "ConnectedPositionStrategy", "originPos", "overlayPos", "_connectedTo", "_dir", "_offsetX", "_offsetY", "scrollables", "_resizeSubscription", "Subscription", "EMPTY", "_preferredPositions", "_applied", "_positionLocked", "_onPositionChange", "_origin", "nativeElement", "withFallbackPosition", "change", "_lastConnectedPosition", "recalculateLastPosition", "fallbackPoint", "fallbackPosition", "originRect", "viewportSize", "_i", "pos", "originPoint", "_getOriginConnectionPoint", "overlayPoint", "_getOverlayPoint", "fitsInViewport", "_setElementPosition", "visibleArea", "lastPosition", "withScrollableContainers", "position", "push", "withDirection", "withOffsetX", "offset", "withOffsetY", "withLockedPosition", "isLocked", "withPositions", "positions", "slice", "<PERSON><PERSON><PERSON><PERSON>", "_getStartX", "rect", "_isRtl", "_getEndX", "x", "originStartX", "originEndX", "y", "overlayStartX", "overlayStartY", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "_getScrollVisibility", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "getElementRef", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "horizontalStyleProperty", "verticalStyleProperty", "clientHeight", "clientWidth", "positionChange", "overflows", "reduce", "currentValue", "currentOverflow", "max", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_leftOffset", "_rightOffset", "_alignItems", "_justifyContent", "_width", "_height", "_wrapper", "centerHorizontally", "centerVertically", "styles", "parentStyles", "marginLeft", "marginTop", "marginBottom", "marginRight", "justifyContent", "alignItems", "OverlayPositionBuilder", "global", "connectedTo", "elementRef", "_attachedOverlays", "ngOnDestroy", "_unsubscribeFromKeydownEvents", "_keydownEventSubscription", "_subscribeToKeydownEvents", "index", "indexOf", "splice", "bodyKeydownEvents", "fromEvent", "_selectOverlayFromEvent", "find", "target", "OVERLAY_KEYBOARD_DISPATCHER_PROVIDER", "provide", "deps", "SkipSelf", "useFactory", "_containerElement", "getContainerElement", "_createContainer", "container", "OVERLAY_CONTAINER_PROVIDER", "nextUniqueId", "Overlay", "_overlayContainer", "_componentFactoryResolver", "_positionBuilder", "_appRef", "_injector", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "id", "DomPortalOutlet", "ComponentFactoryResolver", "ApplicationRef", "Injector", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "InjectionToken", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "CdkOverlayOrigin", "Directive", "selector", "exportAs", "ElementRef", "CdkConnectedOverlay", "_overlay", "templateRef", "viewContainerRef", "_scrollStrategy", "_hasBackdrop", "_backdropSubscription", "open", "EventEmitter", "_templatePortal", "TemplatePortal", "_position", "coerceBooleanProperty", "_positions", "_minWidth", "_minHeight", "_backdropClass", "_open", "_destroyOverlay", "ngOnChanges", "changes", "_attachOverlay", "_detachOverlay", "_createOverlay", "_buildConfig", "_createPositionStrategy", "overlayConfig", "primaryPosition", "strategy", "onPositionChange", "emit", "keyCode", "ESCAPE", "TemplateRef", "ViewContainerRef", "Directionality", "Input", "_deprecated<PERSON><PERSON><PERSON>", "_deprecatedPositions", "_deprecatedOffsetX", "_deprecatedOffsetY", "_deprecatedWidth", "_deprecatedHeight", "_deprecatedMin<PERSON>idth", "_deprecatedMinHeight", "_deprecatedBackdropClass", "_deprecatedScrollStrategy", "_deprecatedOpen", "_deprecatedHasBackdrop", "Output", "OVERLAY_PROVIDERS", "VIEWPORT_RULER_PROVIDER", "NgModule", "imports", "BidiModule", "PortalModule", "ScrollDispatchModule", "exports", "declarations", "providers", "OverlayModule", "tslib_1.__extends", "_super", "_adjustParentForFullscreenChange", "_addFullscreenChangeListener", "getFullscreenElement", "fn", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement"], "mappings": ";;;;;;;miCCoBA,SAAgBI,GAAUC,EAAGC,GAEzB,QAASC,KAAOC,KAAKC,YAAcJ,EADnCK,EAAcL,EAAGC,GAEjBD,EAAEM,UAAkB,OAANL,EAAaM,OAAOC,OAAOP,IAAMC,EAAGI,UAAYL,EAAEK,UAAW,GAAIJ,ICInF,QAAAO,KACE,MAAOC,OAAM,8CCVf,QAAAC,GAA6CC,EAAqBC,GAChE,MAAOA,GAAiBC,KAAK,SAAAC,GAC3B,GAAMC,GAAeJ,EAAQK,OAASF,EAAgBG,IAChDC,EAAeP,EAAQM,IAAMH,EAAgBE,OAC7CG,EAAcR,EAAQS,MAAQN,EAAgBO,KAC9CC,EAAeX,EAAQU,KAAOP,EAAgBM,KAEpD,OAAOL,IAAgBG,GAAgBC,GAAeG,IAY1D,QAAAC,GAA4CZ,EAAqBC,GAC/D,MAAOA,GAAiBC,KAAK,SAAAW,GAC3B,GAAMC,GAAed,EAAQM,IAAMO,EAAoBP,IACjDS,EAAef,EAAQK,OAASQ,EAAoBR,OACpDW,EAAchB,EAAQU,KAAOG,EAAoBH,KACjDO,EAAejB,EAAQS,MAAQI,EAAoBJ,KAEzD,OAAOK,IAAgBC,GAAgBC,GAAeC,ICwT1D,QAAAC,GAAuBC,GACrB,MAAwB,gBAAVA,GAAkB,EAAwBA,EAA1D,KCrQA,QAAAC,GACIC,EAAuCC,GACzC,MAAOD,IAAc,GAAIE,GAA0BD,GChDrD,QAAAE,GAAmDC,EACjDH,GACA,MAAOG,IAAmB,GAAIvC,GAAiBoC,GCQjD,QAAAI,GAAuEC,GAErE,MAAO,YAAM,MAAAA,GAAQC,iBAAiBC,cN9CxC,GAAIpC,GAAgBE,OAAOmC,iBACpBC,uBAA2BC,QAAS,SAAU5C,EAAGC,GAAKD,EAAE2C,UAAY1C,IACvE,SAAUD,EAAGC,GAAK,IAAK,GAAI4C,KAAK5C,GAAOA,EAAE6C,eAAeD,KAAI7C,EAAE6C,GAAK5C,EAAE4C,KAQ9DE,EAAWxC,OAAOyC,QAAU,SAAkBC,GACrD,IAAK,GAAIC,GAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAAK,CACjDD,EAAIG,UAAUF,EACd,KAAK,GAAIN,KAAKK,GAAO3C,OAAOD,UAAUwC,eAAeS,KAAKL,EAAGL,KAAII,EAAEJ,GAAKK,EAAEL,IAE9E,MAAOI,IOpBXO,EAAA,yBAXA,MAaEA,GAAFlD,UAAAmD,OAAE,aAEAD,EAAFlD,UAAAoD,QAAE,aAEAF,EAAFlD,UAAAqD,OAAE,aAjBFH,KCeAI,EAAA,WAqCE,QAAFA,GAAcC,GAAZ,GAAFC,GAAA3D,IAhCAA,MAAA4D,eAAoC,GAAIP,GAGxCrD,KAAA6D,WAAmC,GAGnC7D,KAAA8D,aAA0B,EAG1B9D,KAAA+D,cAA2B,4BAqB3B/D,KAAAgE,UAA0B,MAGlBN,GACFtD,OAAO6D,KAAKP,GACTQ,OAAO,SAAAC,GAAO,WAAuB,KAAhBT,EAAOS,KAC5BC,QAAQ,SAAAD,GAAO,MAAAR,GAAKQ,GAAOT,EAAOS,KAxD3C,MAAAV,MC6BAY,EAAA,WAUE,QAAFA,GACIC,EACAlC,EACOmC,EACAC,GADAxE,KAAXuE,QAAWA,EACAvE,KAAXwE,QAAWA,EAEPxE,KAAKyE,QAAUH,EAAOG,QACtBzE,KAAK0E,QAAUJ,EAAOI,QACtB1E,KAAK2E,SAAWvC,EAAQuC,SACxB3E,KAAK4E,SAAWxC,EAAQwC,SAhD5B,MAAAP,MA6EAQ,EAAA,yBA7EA,MAAAA,mBAsFE,QAAFC,GAEaC,EAEYC,GAFZhF,KAAb+E,eAAaA,EAEY/E,KAAzBgF,yBAAyBA,EA1FzB,2CA6BAxF,KAAa6E,IAgDb7E,KAAaqF,EAAbI,aAAAzF,KAaO0F,EAAAA,cA1FPJ,KCwBAK,EAAA,WAKE,QAAFA,GACYC,EACAC,EACAC,EACAC,GAJV,GAAF5B,GAAA3D,IACYA,MAAZoF,kBAAYA,EACApF,KAAZqF,QAAYA,EACArF,KAAZsF,eAAYA,EACAtF,KAAZuF,QAAYA,EARZvF,KAAAwF,oBAAmD,KAqDnDxF,KAAAyF,QAAoB,WAChB9B,EAAKJ,UAEDI,EAAK+B,YAAYC,eACnBhC,EAAK0B,QAAQO,IAAI,WAAM,MAAAjC,GAAK+B,YAAYG,YAlF9C,MAoCEV,GAAFhF,UAAAqD,OAAE,SAAOsC,GACL,GAAI9F,KAAK0F,YACP,KAAMpF,IAGRN,MAAK0F,YAAcI,GAIrBX,EAAFhF,UAAAmD,OAAE,WAAA,GAAFK,GAAA3D,IACI,KAAIA,KAAKwF,oBAAT,CAIA,GAAMO,GAAS/F,KAAKoF,kBAAkBY,SAAS,EAE3ChG,MAAKuF,SAAWvF,KAAKuF,QAAQU,WAAajG,KAAKuF,QAAQU,UAAY,GACrEjG,KAAKkG,uBAAyBlG,KAAKsF,eAAea,4BAA4BpF,IAE9Ef,KAAKwF,oBAAsBO,EAAOK,UAAU,WAC1C,GAAMC,GAAiB1C,EAAK2B,eAAea,4BAA4BpF,GAEnEuF,MAAKC,IAAIF,EAAiB1C,EAAKuC,wBAA0BvC,EAAY,QAAW,UAClFA,EAAK8B,UAEL9B,EAAK+B,YAAYc,oBAIrBxG,KAAKwF,oBAAsBO,EAAOK,UAAUpG,KAAKyF,WAKrDN,EAAFhF,UAAAoD,QAAE,WACMvD,KAAKwF,sBACPxF,KAAKwF,oBAAoBiB,cACzBzG,KAAKwF,oBAAsB,OAzEjCL,KCcAuB,EAAA,WAME,QAAFA,GAAsBpB,EAA+BqB,GAA/B3G,KAAtBsF,eAAsBA,EALtBtF,KAAA4G,qBAAkC7F,IAAK,GAAII,KAAM,IAEjDnB,KAAA6G,YAAuB,EAInB7G,KAAK+B,UAAY4E,EArBrB,MAyBED,GAAFvG,UAAAqD,OAAE,aAGAkD,EAAFvG,UAAAmD,OAAE,WACE,GAAItD,KAAK8G,gBAAiB,CACxB,GAAMC,GAAO/G,KAAK+B,UAAUiF,eAE5BhH,MAAKiH,wBAA0BjH,KAAKsF,eAAea,4BAGnDnG,KAAK4G,oBAAoBzF,KAAO4F,EAAKG,MAAM/F,MAAQ,GACnDnB,KAAK4G,oBAAoB7F,IAAMgG,EAAKG,MAAMnG,KAAO,GAIjDgG,EAAKG,MAAM/F,MAAWnB,KAAKiH,wBAAwB9F,KAAzD,KACM4F,EAAKG,MAAMnG,KAAUf,KAAKiH,wBAAwBlG,IAAxD,KACMgG,EAAKI,UAAUC,IAAI,0BACnBpH,KAAK6G,YAAa,IAKtBH,EAAFvG,UAAAoD,QAAE,WACE,GAAIvD,KAAK6G,WAAY,CACnB,GAAMQ,GAAOrH,KAAK+B,UAAUiF,gBACtBM,EAAOtH,KAAK+B,UAAUuF,KACtBC,EAA6BF,EAAKH,MAAsB,gBAAK,GAC7DM,EAA6BF,EAAKJ,MAAsB,gBAAK,EAEnElH,MAAK6G,YAAa,EAElBQ,EAAKH,MAAM/F,KAAOnB,KAAK4G,oBAAoBzF,KAC3CkG,EAAKH,MAAMnG,IAAMf,KAAK4G,oBAAoB7F,IAC1CsG,EAAKF,UAAUM,OAAO,0BAItBJ,EAAKH,MAAsB,eAAII,EAAKJ,MAAsB,eAAI,OAE9DQ,OAAOC,OAAO3H,KAAKiH,wBAAwB9F,KAAMnB,KAAKiH,wBAAwBlG,KAE9EsG,EAAKH,MAAsB,eAAIK,EAC/BD,EAAKJ,MAAsB,eAAIM,IAI3Bd,EAAVvG,UAAA2G,yBAMI,GAFa9G,KAAK+B,UAAUiF,gBAEnBG,UAAUS,SAAS,2BAA6B5H,KAAK6G,WAC5D,OAAO,CAGT,IAAMS,GAAOtH,KAAK+B,UAAUuF,KACtBO,EAAW7H,KAAKsF,eAAewC,iBACrC,OAAOR,GAAKS,aAAeF,EAASG,QAAUV,EAAKW,YAAcJ,EAASK,OApF9ExB,KC6BAyB,EAAA,WAIE,QAAFA,GACY/C,EACAE,EACAD,EACAE,GAHAvF,KAAZoF,kBAAYA,EACApF,KAAZsF,eAAYA,EACAtF,KAAZqF,QAAYA,EACArF,KAAZuF,QAAYA,EAPZvF,KAAAwF,oBAAmD,KA9BnD,MAwCE2C,GAAFhI,UAAAqD,OAAE,SAAOsC,GACL,GAAI9F,KAAK0F,YACP,KAAMpF,IAGRN,MAAK0F,YAAcI,GAIrBqC,EAAFhI,UAAAmD,OAAE,WAAA,GAAFK,GAAA3D,IACI,KAAKA,KAAKwF,oBAAqB,CAC7B,GAAM4C,GAAWpI,KAAKuF,QAAUvF,KAAKuF,QAAQ8C,eAAiB,CAE9DrI,MAAKwF,oBAAsBxF,KAAKoF,kBAAkBY,SAASoC,GAAUhC,UAAU,WAI7E,GAHAzC,EAAK+B,YAAYc,iBAGb7C,EAAK4B,SAAW5B,EAAK4B,QAAQ+C,UAAW,CAC1C,GAAMC,GAAc5E,EAAK+B,YAAY8C,eAAeC,wBAC9DC,EAAA/E,EAAA2B,eAAAwC,kBAAiBI,EAAjBQ,EAAAR,MAAwBF,EAAxBU,EAAAV,MAMcxH,GAA6B+H,IAFXL,MAAhCA,EAAuCF,OAAvCA,EAA+ClH,OAAQkH,EAAQ9G,MAAOgH,EAAOnH,IAAK,EAAGI,KAAM,OAG/EwC,EAAKJ,UACLI,EAAK0B,QAAQO,IAAI,WAAM,MAAAjC,GAAK+B,YAAYG,iBAQlDsC,EAAFhI,UAAAoD,QAAE,WACMvD,KAAKwF,sBACPxF,KAAKwF,oBAAoBiB,cACzBzG,KAAKwF,oBAAsB,OA9EjC2C,kBC8BE,QAAFQ,GACYvD,EACAE,EACAD,EACUsB,GAJpB,GAAFhD,GAAA3D,IACYA,MAAZoF,kBAAYA,EACApF,KAAZsF,eAAYA,EACAtF,KAAZqF,QAAYA,EAMZrF,KAAA4I,KAAS,WAAM,MAAA,IAAIvF,IAMnBrD,KAAA6I,MAAU,SAACnF,GAAuC,MAAA,IAAIyB,GAAoBxB,EAAKyB,kBACzEzB,EAAK0B,QAAS1B,EAAK2B,eAAgB5B,IAGzC1D,KAAA8I,MAAU,WAAM,MAAA,IAAIpC,GAAoB/C,EAAK2B,eAAgB3B,EAAK5B,YAOlE/B,KAAAsC,WAAe,SAACoB,GAA4C,MAAA,IAAIyE,GAC1DxE,EAAKyB,kBAAmBzB,EAAK2B,eAAgB3B,EAAK0B,QAAS3B,IAtB3D1D,KAAK+B,UAAY4E,EAnCvB,sBA0BAnH,KAACC,EAAAA,iDAfDD,KAAQuJ,EAAAA,mBACRvJ,KAAQwJ,EAAAA,gBALRxJ,KAAoByJ,EAAAA,SA2BpBzJ,SAAA0J,GAAAjE,aAAAzF,KAAK2J,EAAAA,OAALC,MAAYC,EAAAA,eAlCZV,KV2BAW,EAAA,WASE,QAAFA,GACcC,EACAC,EACAjE,EACAF,EACAoE,EACA1H,GALA/B,KAAduJ,cAAcA,EACAvJ,KAAdwJ,MAAcA,EACAxJ,KAAduF,QAAcA,EACAvF,KAAdqF,QAAcA,EACArF,KAAdyJ,oBAAcA,EACAzJ,KAAd+B,UAAcA,EAdd/B,KAAA0J,iBAAiD,KACjD1J,KAAA2J,eAAgD,GAAIC,GAAAA,QACpD5J,KAAA6J,aAAyB,GAAID,GAAAA,QAC7B5J,KAAA8J,aAAyB,GAAIF,GAAAA,QAG7B5J,KAAA+J,eAAmB,GAAIH,GAAAA,QAUfrE,EAAQ3B,gBACV2B,EAAQ3B,eAAeJ,OAAOxD,MA7CpC,MAkDEI,QAAF4J,eAAMV,EAANnJ,UAAA,sBAAE,WACE,MAAOH,MAAKwJ,uCAIdpJ,OAAF4J,eAAMV,EAANnJ,UAAA,uBAAE,WACE,MAAOH,MAAK0J,kDAcdJ,EAAFnJ,UAAAqD,OAAE,SAAOyG,GAAP,GAAFtG,GAAA3D,KACQkK,EAAelK,KAAKuJ,cAAc/F,OAAOyG,EA+C7C,OA7CIjK,MAAKuF,QAAQ4E,kBACfnK,KAAKuF,QAAQ4E,iBAAiB3G,OAAOxD,MAIvCA,KAAKoK,uBACLpK,KAAKqK,qBACLrK,KAAKsK,0BAEDtK,KAAKuF,QAAQ3B,gBACf5D,KAAKuF,QAAQ3B,eAAeN,SAM9BtD,KAAKqF,QAAQkF,SAASC,eAAeC,KAAKC,EAAAA,KAAK,IAAItE,UAAU,WAEvDzC,EAAKgC,eACPhC,EAAK6C,mBAKTxG,KAAK2K,sBAAqB,GAEtB3K,KAAKuF,QAAQzB,aACf9D,KAAK4K,kBAGH5K,KAAKuF,QAAQ1B,aAEXpB,MAAMoI,QAAQ7K,KAAKuF,QAAQ1B,YAC7B7D,KAAKuF,QAAQ1B,WAAWO,QAAQ,SAAA0G,GAAO,MAAAnH,GAAK6F,MAAMrC,UAAUC,IAAI0D,KAEhE9K,KAAKwJ,MAAMrC,UAAUC,IAAIpH,KAAKuF,QAAQ1B,aAK1C7D,KAAK6J,aAAakB,OAGlB/K,KAAKyJ,oBAAoBrC,IAAIpH,MAEtBkK,GAOTZ,EAAFnJ,UAAA0F,OAAE,WACE,GAAK7F,KAAK2F,cAAV,CAIA3F,KAAKgL,iBAKLhL,KAAK2K,sBAAqB,GAEtB3K,KAAKuF,QAAQ4E,kBAAoBnK,KAAKuF,QAAQ4E,iBAAiBtE,QACjE7F,KAAKuF,QAAQ4E,iBAAiBtE,SAG5B7F,KAAKuF,QAAQ3B,gBACf5D,KAAKuF,QAAQ3B,eAAeL,SAG9B,IAAM0H,GAAmBjL,KAAKuJ,cAAc1D,QAQ5C,OALA7F,MAAK8J,aAAaiB,OAGlB/K,KAAKyJ,oBAAoBhC,OAAOzH,MAEzBiL,IAIT3B,EAAFnJ,UAAA+K,QAAE,WACE,GAAMC,GAAanL,KAAK2F,aAEpB3F,MAAKuF,QAAQ4E,kBACfnK,KAAKuF,QAAQ4E,iBAAiBe,UAG5BlL,KAAKuF,QAAQ3B,gBACf5D,KAAKuF,QAAQ3B,eAAeL,UAG9BvD,KAAKgL,iBACLhL,KAAKyJ,oBAAoBhC,OAAOzH,MAChCA,KAAKuJ,cAAc2B,UACnBlL,KAAK6J,aAAauB,WAClBpL,KAAK2J,eAAeyB,WACpBpL,KAAK+J,eAAeqB,WAEhBD,GACFnL,KAAK8J,aAAaiB,OAGpB/K,KAAK8J,aAAasB,YAIpB9B,EAAFnJ,UAAAwF,YAAE,WACE,MAAO3F,MAAKuJ,cAAc5D,eAI5B2D,EAAFnJ,UAAAkL,cAAE,WACE,MAAOrL,MAAK2J,eAAea,gBAI7BlB,EAAFnJ,UAAAmL,YAAE,WACE,MAAOtL,MAAK6J,aAAaW,gBAI3BlB,EAAFnJ,UAAAoL,YAAE,WACE,MAAOvL,MAAK8J,aAAaU,gBAI3BlB,EAAFnJ,UAAAqL,cAAE,WACE,MAAOxL,MAAK+J,eAAeS,gBAI7BlB,EAAFnJ,UAAAsL,UAAE,WACE,MAAOzL,MAAKuF,SAId+D,EAAFnJ,UAAAqG,eAAE,WACMxG,KAAKuF,QAAQ4E,kBACfnK,KAAKuF,QAAQ4E,iBAAiBuB,SAKlCpC,EAAFnJ,UAAAwL,WAAE,SAAWC,GACT5L,KAAKuF,QAATsG,KAAuB7L,KAAKuF,QAAYqG,GACpC5L,KAAKqK,sBAIPf,EAAFnJ,UAAA2L,aAAE,SAAaC,GACX/L,KAAKuF,QAATsG,KAAuB7L,KAAKuF,SAASvB,UAAW+H,IAC5C/L,KAAKsK,2BAIChB,EAAVnJ,UAAAmK,mCACItK,KAAKwJ,MAAMwC,aAAa,MAAOhM,KAAKuF,QAAiB,YAI/C+D,EAAVnJ,UAAAkK,+BACQrK,KAAKuF,QAAQ2C,OAAgC,IAAvBlI,KAAKuF,QAAQ2C,SACrClI,KAAKwJ,MAAMtC,MAAMgB,MAAQvG,EAAc3B,KAAKuF,QAAQ2C,SAGlDlI,KAAKuF,QAAQyC,QAAkC,IAAxBhI,KAAKuF,QAAQyC,UACtChI,KAAKwJ,MAAMtC,MAAMc,OAASrG,EAAc3B,KAAKuF,QAAQyC,UAGnDhI,KAAKuF,QAAQ0G,UAAsC,IAA1BjM,KAAKuF,QAAQ0G,YACxCjM,KAAKwJ,MAAMtC,MAAM+E,SAAWtK,EAAc3B,KAAKuF,QAAQ0G,YAGrDjM,KAAKuF,QAAQ2G,WAAwC,IAA3BlM,KAAKuF,QAAQ2G,aACzClM,KAAKwJ,MAAMtC,MAAMgF,UAAYvK,EAAc3B,KAAKuF,QAAQ2G,aAGtDlM,KAAKuF,QAAQ4G,UAAsC,IAA1BnM,KAAKuF,QAAQ4G,YACxCnM,KAAKwJ,MAAMtC,MAAMiF,SAAWxK,EAAc3B,KAAKuF,QAAQ4G,YAGrDnM,KAAKuF,QAAQ6G,WAAwC,IAA3BpM,KAAKuF,QAAQ6G,aACzCpM,KAAKwJ,MAAMtC,MAAMkF,UAAYzK,EAAc3B,KAAKuF,QAAQ6G,aAKpD9C,EAAVnJ,UAAAwK,qBAAA,SAA+B0B,GAC3BrM,KAAKwJ,MAAMtC,MAAMoF,cAAgBD,EAAgB,OAAS,QAIpD/C,EAAVnJ,UAAAyK,qCAGI5K,MAAK0J,iBAAmB1J,KAAK+B,UAAUwK,cAAc,OACrDvM,KAAK0J,iBAAiBvC,UAAUC,IAAI,wBAEhCpH,KAAKuF,QAAQxB,eACf/D,KAAK0J,iBAAiBvC,UAAUC,IAAIpH,KAAKuF,QAAQxB,eAKnD/D,KAAKwJ,MAAmB,cAAEgD,aAAaxM,KAAK0J,iBAAkB1J,KAAKwJ,OAInExJ,KAAK0J,iBAAiB+C,iBAAiB,QACnC,SAACC,GAAsB,MAAA/I,GAAKgG,eAAeoB,KAAK2B,KAGf,mBAA1BC,uBACT3M,KAAKqF,QAAQuH,kBAAkB,WAC7BD,sBAAsB,WAChBhJ,EAAK+F,kBACP/F,EAAK+F,iBAAiBvC,UAAUC,IAvBnB,oCA4BnBpH,KAAK0J,iBAAiBvC,UAAUC,IA5Bb,iCAuCfkC,EAAVnJ,UAAAiK,gCACQpK,KAAKwJ,MAAMqD,aACb7M,KAAKwJ,MAAgB,WAAEsD,YAAY9M,KAAKwJ,QAK5CF,EAAFnJ,UAAA6K,eAAE,WAAA,GAAFrH,GAAA3D,KACQ+M,EAAmB/M,KAAK0J,gBAE5B,IAAIqD,EAAkB,CACpB,GAAIC,GAAe,WAEbD,GAAoBA,EAAiBE,YACvCF,EAAiBE,WAAWC,YAAYH,GAMtCpJ,EAAK+F,kBAAoBqD,IAC3BpJ,EAAK+F,iBAAmB,MAI5BqD,GAAiB5F,UAAUM,OAAO,gCAE9BzH,KAAKuF,QAAQxB,eACfgJ,EAAiB5F,UAAUM,OAAOzH,KAAKuF,QAAQxB,eAGjDgJ,EAAiBN,iBAAiB,gBAAiBO,GAInDD,EAAiB7F,MAAMoF,cAAgB,OAKvCtM,KAAKqF,QAAQuH,kBAAkB,WAC7BO,WAAWH,EAAc,SA9VjC1D,KWkCA8D,EAAA,WAiDE,QAAFA,GACMC,EACAC,EACQC,EACAjI,EACAvD,GAFA/B,KAAduN,aAAcA,EACAvN,KAAdsF,eAAcA,EACAtF,KAAd+B,UAAcA,EAjDd/B,KAAAwN,KAAiB,MAGjBxN,KAAAyN,SAA6B,EAG7BzN,KAAA0N,SAA6B,EAG7B1N,KAAA2N,eAGA3N,KAAA4N,oBAAgCC,EAAAA,aAAaC,MAQ7C9N,KAAA+N,uBAYA/N,KAAAgO,UAAqB,EAGrBhO,KAAAiO,iBAA4B,EAE5BjO,KAAAkO,kBAA8B,GAAItE,GAAAA,QAa9B5J,KAAKmO,QAAUnO,KAAKuN,aAAaa,cACjCpO,KAAKqO,qBAAqBhB,EAAWC,GA1FzC,MAsDElN,QAAF4J,eAAMoD,EAANjN,UAAA,cAAE,WACE,MAAqB,QAAdH,KAAKwN,sCAwBdpN,OAAF4J,eAAMoD,EAANjN,UAAA,wBAAE,WACE,MAAOH,MAAKkO,kBAAkB1D,gDAchCpK,OAAF4J,eAAMoD,EAANjN,UAAA,iBAAE,WACE,MAAOH,MAAK+N,qDAIdX,EAAFjN,UAAAqD,OAAE,SAAOsC,GAAP,GAAFnC,GAAA3D,IACIA,MAAK0F,YAAcI,EACnB9F,KAAKwJ,MAAQ1D,EAAW0C,eACxBxI,KAAK4N,oBAAoBnH,cACzBzG,KAAK4N,oBAAsB5N,KAAKsF,eAAegJ,SAASlI,UAAU,WAAM,MAAAzC,GAAK+H,WAI/E0B,EAAFjN,UAAA+K,QAAE,WACElL,KAAKgO,UAAW,EAChBhO,KAAK4N,oBAAoBnH,cACzBzG,KAAKkO,kBAAkB9C,YAIzBgC,EAAFjN,UAAA0F,OAAE,WACE7F,KAAKgO,UAAW,EAChBhO,KAAK4N,oBAAoBnH,eAQ3B2G,EAAFjN,UAAAuL,MAAE,WAIE,GAAI1L,KAAKgO,UAAYhO,KAAKiO,iBAAmBjO,KAAKuO,uBAEhD,WADAvO,MAAKwO,yBAIPxO,MAAKgO,UAAW,CAiBhB,KAAgB,GALZS,GACAC,EATEjO,EAAUT,KAAKwJ,MACfmF,EAAa3O,KAAKmO,QAAQ1F,wBAC1BF,EAAc9H,EAAQgI,wBAGtBmG,EAAe5O,KAAKsF,eAAewC,kBAQ7C+G,EAAA,EAAoBnG,EAAA1I,KAAK+N,oBAALc,EAApBnG,EAAAvF,OAAoB0L,IAApB,CAAS,GAAIC,GAAbpG,EAAAmG,GAGUE,EAAc/O,KAAKgP,0BAA0BL,EAAYG,GACzDG,EAAejP,KAAKkP,iBAAiBH,EAAaxG,EAAaqG,EAAcE,EAGjF,IAAIG,EAAaE,eAMf,MALAnP,MAAKoP,oBAAoB3O,EAAS8H,EAAa0G,EAAcH,QAG7D9O,KAAKuO,uBAAyBO,KAGpBL,GAAiBA,EAAcY,YAAcJ,EAAaI,eACpEZ,EAAgBQ,EAChBP,EAAmBI,GAMvB9O,KAAKoP,oBAAoB3O,EAAS8H,EAAW,EAAe,IAQ9D6E,EAAFjN,UAAAqO,wBAAE,WAEE,GAAKxO,KAAKuO,uBAAV,CAIA,GAAMI,GAAa3O,KAAKmO,QAAQ1F,wBAC1BF,EAAcvI,KAAKwJ,MAAMf,wBACzBmG,EAAe5O,KAAKsF,eAAewC,kBACnCwH,EAAetP,KAAKuO,wBAA0BvO,KAAK+N,oBAAoB,GAEzEgB,EAAc/O,KAAKgP,0BAA0BL,EAAYW,GACzDL,EAAejP,KAAKkP,iBAAiBH,EAAaxG,EAAaqG,EAAcU,EACjFtP,MAAKoP,oBAAoBpP,KAAKwJ,MAAOjB,EAAa0G,EAAcK,KAQlElC,EAAFjN,UAAAoP,yBAAE,SAAyB5B,GACvB3N,KAAK2N,YAAcA,GAQrBP,EAAFjN,UAAAkO,qBAAE,SACIhB,EACAC,EACA/I,EACAC,GAEF,GAAMgL,GAAW,GAAInL,GAAuBgJ,EAAWC,EAAY/I,EAASC,EAE5E,OADAxE,MAAK+N,oBAAoB0B,KAAKD,GACvBxP,MAOToN,EAAFjN,UAAAuP,cAAE,SAAc3D,GAEZ,MADA/L,MAAKwN,KAAOzB,EACL/L,MAOToN,EAAFjN,UAAAwP,YAAE,SAAYC,GAEV,MADA5P,MAAKyN,SAAWmC,EACT5P,MAOToN,EAAFjN,UAAA0P,YAAE,SAAYD,GAEV,MADA5P,MAAK0N,SAAWkC,EACT5P,MASToN,EAAFjN,UAAA2P,mBAAE,SAAmBC,GAEjB,MADA/P,MAAKiO,gBAAkB8B,EAChB/P,MAOToN,EAAFjN,UAAA6P,cAAE,SAAcC,GAEZ,MADAjQ,MAAK+N,oBAAsBkC,EAAUC,QAC9BlQ,MAOToN,EAAFjN,UAAAgQ,UAAE,SAAU7L,GAER,MADAtE,MAAKmO,QAAU7J,EAAO8J,cACfpO,MAODoN,EAAVjN,UAAAiQ,WAAA,SAAqBC,GACjB,MAAOrQ,MAAKsQ,OAASD,EAAKnP,MAAQmP,EAAKlP,MAOjCiM,EAAVjN,UAAAoQ,SAAA,SAAmBF,GACf,MAAOrQ,MAAKsQ,OAASD,EAAKlP,KAAOkP,EAAKnP,OAShCkM,EAAVjN,UAAA6O,0BAAA,SAAoCL,EAAwBG,GACxD,GAGI0B,GAHEC,EAAezQ,KAAKoQ,WAAWzB,GAC/B+B,EAAa1Q,KAAKuQ,SAAS5B,EAI/B6B,GADiB,UAAf1B,EAAIrK,QACFgM,EAAgB9B,EAAWzG,MAAQ,EAEpB,SAAf4G,EAAIrK,QAAqBgM,EAAeC,CAG9C,IAAIC,EAOJ,OALEA,GADiB,UAAf7B,EAAIpK,QACFiK,EAAW5N,IAAO4N,EAAW3G,OAAS,EAEvB,OAAf8G,EAAIpK,QAAmBiK,EAAW5N,IAAM4N,EAAW7N,QAGjD0P,EAAZA,EAAeG,EAAfA,IASUvD,EAAVjN,UAAA+O,iBACA,SAAMH,EACAxG,EACAqG,EACAE,GAGF,GAAI8B,EAEFA,GADkB,UAAhB9B,EAAInK,UACW4D,EAAYL,MAAQ,EACX,UAAjB4G,EAAInK,SACG3E,KAAKsQ,QAAU/H,EAAYL,MAAQ,EAEnClI,KAAKsQ,OAAS,GAAK/H,EAAYL,KAGjD,IAAI2I,EAEFA,GADkB,UAAhB/B,EAAIlK,UACW2D,EAAYP,OAAS,EAEN,OAAhB8G,EAAIlK,SAAoB,GAAK2D,EAAYP,MAI3D,IAAIzD,OAAiC,KAAhBuK,EAAIvK,QAA0BvE,KAAKyN,SAAWqB,EAAIvK,QACnEC,MAAiC,KAAhBsK,EAAItK,QAA0BxE,KAAK0N,SAAWoB,EAAItK,QAGnEgM,EAAIzB,EAAYyB,EAAII,EAAgBrM,EACpCoM,EAAI5B,EAAY4B,EAAIE,EAAgBrM,EAGpCsM,EAAe,EAAIN,EACnBO,EAAiBP,EAAIjI,EAAYL,MAAS0G,EAAa1G,MACvD8I,EAAc,EAAIL,EAClBM,EAAkBN,EAAIpI,EAAYP,OAAU4G,EAAa5G,OAGzDkJ,EAAelR,KAAKmR,mBAAmB5I,EAAYL,MAAO4I,EAAcC,GACxEK,EAAgBpR,KAAKmR,mBAAmB5I,EAAYP,OAAQgJ,EAAaC,GAGzE5B,EAAc6B,EAAeE,CAGjC,QAAQZ,EAAZA,EAAeG,EAAfA,EAAkBxB,eAFQ5G,EAAYL,MAAQK,EAAYP,SAAYqH,EAEpCA,YAAlCA,IAOUjC,EAAVjN,UAAAkR,qBAAA,SAA+BjP,GAC3B,GAAMkP,GAAetR,KAAKmO,QAAQ1F,wBAC5B8I,EAAgBnP,EAAQqG,wBACxB+I,EACFxR,KAAK2N,YAAY8D,IAAI,SAAA1O,GAAK,MAAAA,GAAE2O,gBAAgBtD,cAAc3F,yBAE9D,QACEkJ,gBAAiBtQ,EAA4BiQ,EAAcE,GAC3DI,oBAAqBpR,EAA6B8Q,EAAcE,GAChEK,iBAAkBxQ,EAA4BkQ,EAAeC,GAC7DM,qBAAsBtR,EAA6B+Q,EAAeC,KAK9DpE,EAAVjN,UAAAiP,oBACA,SAAM3O,EACA8H,EACA0G,EACAH,GAIF,GAYIiD,GAZAC,EAAyC,WAAjBlD,EAAIlK,SAAwB,SAAW,MAI/D+L,EAA8B,QAA1BqB,EACJ/C,EAAa0B,EACb3Q,KAAK+B,UAAUiF,gBAAgBiL,cAAgBhD,EAAa0B,EAAIpI,EAAYP,OAQ9E+J,GADgB,QAAd/R,KAAKwN,KACoC,QAAjBsB,EAAInK,SAAqB,OAAS,QAEjB,QAAjBmK,EAAInK,SAAqB,QAAU,MAK/D,IAAI6L,GAAgC,SAA5BuB,EACN9C,EAAauB,EACbxQ,KAAK+B,UAAUiF,gBAAgBkL,aAAejD,EAAauB,EAAIjI,EAAYL,QAK5E,MAAO,SAAU,OAAQ,SAAS9D,QAAQ,SAAA1B,GAAK,MAAAjC,GAAQyG,MAAMxE,GAAK,OAEnEjC,EAAQyG,MAAM8K,GAA4BrB,EAA9C,KACIlQ,EAAQyG,MAAM6K,GAA8BvB,EAAhD,IAGI,IAAMxL,GAA2BhF,KAAKqR,qBAAqB5Q,GACrD0R,EAAiB,GAAIrN,GAA+BgK,EAAK9J,EAC/DhF,MAAKkO,kBAAkBnD,KAAKoH,IAMtB/E,EAAVjN,UAAAgR,mBAAA,SAA6BhO,GAA7B,IAA6C,GAA7CiP,MAAAvD,EAAA,EAA6CA,EAA7C3L,UAAAC,OAA6C0L,IAAAuD,EAA7CvD,EAAA,GAAA3L,UAAA2L,EACI,OAAOuD,GAAUC,OAAO,SAACC,EAAsBC,GAC7C,MAAOD,GAAehM,KAAKkM,IAAID,EAAiB,IAC/CpP,IA9bPiK,KCkBAqF,EAAA,WAiBE,QAAFA,GAAsB1Q,GAAA/B,KAAtB+B,UAAsBA,EAbtB/B,KAAA0S,aAAyB,SACzB1S,KAAA2S,WAAuB,GACvB3S,KAAA4S,cAA0B,GAC1B5S,KAAA6S,YAAwB,GACxB7S,KAAA8S,aAAyB,GACzB9S,KAAA+S,YAAwB,GACxB/S,KAAAgT,gBAA4B,GAC5BhT,KAAAiT,OAAmB,GACnBjT,KAAAkT,QAAoB,GAGpBlT,KAAAmT,SAAyC,KAjCzC,MAqCEV,GAAFtS,UAAAqD,OAAE,SAAOsC,GACL,GAAMpC,GAASoC,EAAW2F,WAE1BzL,MAAK0F,YAAcI,EAEf9F,KAAKiT,SAAWvP,EAAOwE,OACzBpC,EAAW6F,YAAYzD,MAAOlI,KAAKiT,SAGjCjT,KAAKkT,UAAYxP,EAAOsE,QAC1BlC,EAAW6F,YAAY3D,OAAQhI,KAAKkT,WAQxCT,EAAFtS,UAAAY,IAAE,SAAIa,GAIF,WAJJ,KAAAA,IAAMA,EAAN,IACI5B,KAAK4S,cAAgB,GACrB5S,KAAK2S,WAAa/Q,EAClB5B,KAAK+S,YAAc,aACZ/S,MAOTyS,EAAFtS,UAAAgB,KAAE,SAAKS,GAIH,WAJJ,KAAAA,IAAOA,EAAP,IACI5B,KAAK8S,aAAe,GACpB9S,KAAK6S,YAAcjR,EACnB5B,KAAKgT,gBAAkB,aAChBhT,MAOTyS,EAAFtS,UAAAW,OAAE,SAAOc,GAIL,WAJJ,KAAAA,IAASA,EAAT,IACI5B,KAAK2S,WAAa,GAClB3S,KAAK4S,cAAgBhR,EACrB5B,KAAK+S,YAAc,WACZ/S,MAOTyS,EAAFtS,UAAAe,MAAE,SAAMU,GAIJ,WAJJ,KAAAA,IAAQA,EAAR,IACI5B,KAAK6S,YAAc,GACnB7S,KAAK8S,aAAelR,EACpB5B,KAAKgT,gBAAkB,WAChBhT,MASTyS,EAAFtS,UAAA+H,MAAE,SAAMtG,GAOJ,WAPJ,KAAAA,IAAQA,EAAR,IACQ5B,KAAK0F,YACP1F,KAAK0F,YAAYiG,YAAYzD,MAAOtG,IAEpC5B,KAAKiT,OAASrR,EAGT5B,MASTyS,EAAFtS,UAAA6H,OAAE,SAAOpG,GAOL,WAPJ,KAAAA,IAASA,EAAT,IACQ5B,KAAK0F,YACP1F,KAAK0F,YAAYiG,YAAY3D,OAAQpG,IAErC5B,KAAKkT,QAAUtR,EAGV5B,MASTyS,EAAFtS,UAAAiT,mBAAE,SAAmBxD,GAGjB,WAHJ,KAAAA,IAAqBA,EAArB,IACI5P,KAAKmB,KAAKyO,GACV5P,KAAKgT,gBAAkB,SAChBhT,MASTyS,EAAFtS,UAAAkT,iBAAE,SAAiBzD,GAGf,WAHJ,KAAAA,IAAmBA,EAAnB,IACI5P,KAAKe,IAAI6O,GACT5P,KAAK+S,YAAc,SACZ/S,MASTyS,EAAFtS,UAAAuL,MAAE,WAIE,GAAK1L,KAAK0F,YAAYC,cAAtB,CAIA,GAAMlF,GAAUT,KAAK0F,YAAY8C,gBAE5BxI,KAAKmT,UAAY1S,EAAQwM,aAC5BjN,KAAKmT,SAAWnT,KAAK+B,UAAUwK,cAAc,OAC7CvM,KAAa,SAAEmH,UAAUC,IAAI,8BAC7B3G,EAAQwM,WAAWT,aAAaxM,KAAa,SAAGS,GAChDT,KAAa,SAAE8M,YAAYrM,GAG7B,IAAM6S,GAAS7S,EAAQyG,MACjBqM,EAAgB9S,EAAiC,WAAEyG,MACnDxD,EAAS1D,KAAK0F,YAAY+F,WAEhC6H,GAAO9D,SAAWxP,KAAK0S,aACvBY,EAAOE,WAA8B,SAAjB9P,EAAOwE,MAAmB,IAAMlI,KAAK6S,YACzDS,EAAOG,UAA8B,SAAlB/P,EAAOsE,OAAoB,IAAMhI,KAAK2S,WACzDW,EAAOI,aAAe1T,KAAK4S,cAC3BU,EAAOK,YAAc3T,KAAK8S,aAE1BS,EAAaK,eAAkC,SAAjBlQ,EAAOwE,MAAmB,aAAelI,KAAKgT,gBAC5EO,EAAaM,WAA+B,SAAlBnQ,EAAOsE,OAAoB,aAAehI,KAAK+S,cAI3EN,EAAFtS,UAAA+K,QAAE,WACMlL,KAAKmT,UAAYnT,KAAKmT,SAASlG,aACjCjN,KAAKmT,SAASlG,WAAWC,YAAYlN,KAAKmT,UAC1CnT,KAAKmT,SAAW,OAhMtBV,kBCmBE,QAAFqB,GAAsBxO,EACkBvD,GADlB/B,KAAtBsF,eAAsBA,EACkBtF,KAAxC+B,UAAwCA,EApBxC,MAyBE+R,GAAF3T,UAAA4T,OAAE,WACE,MAAO,IAAItB,GAAuBzS,KAAK+B,YASzC+R,EAAF3T,UAAA6T,YAAE,SACIC,EACA5G,EACAC,GAEF,MAAO,IAAIF,GAA0BC,EAAWC,EAAY2G,EACxDjU,KAAKsF,eAAgBtF,KAAK+B,2BAxBlCvC,KAACC,EAAAA,iDARDD,KAAQwJ,EAAAA,gBAWRxJ,SAAA0J,GAAAjE,aAAAzF,KAAe2J,EAAAA,OAAfC,MAAsBC,EAAAA,eApBtByK,kBZ4BE,QAAF9R,GAAwCD,GAAA/B,KAAxC+B,UAAwCA,EAJxC/B,KAAAkU,qBAxBA,MA8BElS,GAAF7B,UAAAgU,YAAE,WACEnU,KAAKoU,iCAIPpS,EAAF7B,UAAAiH,IAAE,SAAItB,GAEG9F,KAAKqU,2BACRrU,KAAKsU,4BAGPtU,KAAKkU,kBAAkBzE,KAAK3J,IAI9B9D,EAAF7B,UAAAsH,OAAE,SAAO3B,GACL,GAAMyO,GAAQvU,KAAKkU,kBAAkBM,QAAQ1O,EAEzCyO,IAAS,GACXvU,KAAKkU,kBAAkBO,OAAOF,EAAO,GAID,IAAlCvU,KAAKkU,kBAAkB/Q,QACzBnD,KAAKoU,iCAQDpS,EAAV7B,UAAAmU,gDACUI,EAAoBC,EAAAA,UAAyB3U,KAAK+B,UAAUuF,KAAM,WAAW,EAEnFtH,MAAKqU,0BAA4BK,EAAkBjK,KACjDvG,EAAAA,OAAO,WAAM,QAAEP,EAAKuQ,kBAAkB/Q,UACtCiD,UAAU,SAAAsG,GAEV/I,EAAKiR,wBAAwBlI,GAAO3C,eAAegB,KAAK2B,MAKpD1K,EAAV7B,UAAAiU,yCACQpU,KAAKqU,4BACPrU,KAAKqU,0BAA0B5N,cAC/BzG,KAAKqU,0BAA4B,OAK7BrS,EAAV7B,UAAAyU,wBAAA,SAAkClI,GAQ9B,MANwB1M,MAAKkU,kBAAkBW,KAAK,SAAAzS,GAClD,MAAOA,GAAQoG,iBAAmBkE,EAAMoI,QACpC1S,EAAQoG,eAAeZ,SAAS8E,EAA2B,WAIvC1M,KAAKkU,kBAAkBlU,KAAKkU,kBAAkB/Q,OAAS,mBAtErF3D,KAACC,EAAAA,iDAQDD,SAAA0J,GAAAjE,aAAAzF,KAAe2J,EAAAA,OAAfC,MAAsBC,EAAAA,eA5BtBrH,KAsGa+S,GAGXC,QAAShT,EACTiT,OACG,GAAI/P,GAAAA,SAAY,GAAIgQ,GAAAA,SAAYlT,GAIjCqH,EAA+B,UAEjC8L,WAAYtT,gBChGZ,QAAFlC,GAAwCoC,GAAA/B,KAAxC+B,UAAwCA,EAjBxC,MAmBEpC,GAAFQ,UAAAgU,YAAE,WACMnU,KAAKoV,mBAAqBpV,KAAKoV,kBAAkBnI,YACnDjN,KAAKoV,kBAAkBnI,WAAWC,YAAYlN,KAAKoV,oBAUvDzV,EAAFQ,UAAAkV,oBAAE,WAEE,MADKrV,MAAKoV,mBAAqBpV,KAAKsV,mBAC7BtV,KAAKoV,mBAOJzV,EAAZQ,UAAAmV,iBAAE,WACE,GAAMC,GAAYvV,KAAK+B,UAAUwK,cAAc,MAE/CgJ,GAAUpO,UAAUC,IAAI,yBACxBpH,KAAK+B,UAAUuF,KAAKwF,YAAYyI,GAChCvV,KAAKoV,kBAAoBG,kBAhC7B/V,KAACC,EAAAA,iDAIDD,SAAA0J,GAAAjE,aAAAzF,KAAe2J,EAAAA,OAAfC,MAAsBC,EAAAA,eAjBtB1J,KAwDa6V,GAEXR,QAASrV,EACTsV,OACG,GAAI/P,GAAAA,SAAY,GAAIgQ,GAAAA,SAAYvV,GACjC0J,YAEF8L,WAAYlT,GYpCVwT,EAAe,eAYjB,QAAFC,GAEqBrT,EACCsT,EACAC,EACAC,EACApM,EACAqM,EACAC,EACA1Q,EACkBtD,GARnB/B,KAArBqC,iBAAqBA,EACCrC,KAAtB2V,kBAAsBA,EACA3V,KAAtB4V,0BAAsBA,EACA5V,KAAtB6V,iBAAsBA,EACA7V,KAAtByJ,oBAAsBA,EACAzJ,KAAtB8V,QAAsBA,EACA9V,KAAtB+V,UAAsBA,EACA/V,KAAtBqF,QAAsBA,EACkBrF,KAAxC+B,UAAwCA,EAjDxC,MAwDE2T,GAAFvV,UAAAE,OAAE,SAAOqD,GACL,GAAMsS,GAAOhW,KAAKiW,qBACZC,EAAelW,KAAKmW,oBAAoBH,EAE9C,OAAO,IAAI1M,GACT4M,EACAF,EACA,GAAIvS,GAAcC,GAClB1D,KAAKqF,QACLrF,KAAKyJ,oBACLzJ,KAAK+B,YAST2T,EAAFvV,UAAAqP,SAAE,WACE,MAAOxP,MAAK6V,kBAONH,EAAVvV,UAAA8V,8BACI,GAAMD,GAAOhW,KAAK+B,UAAUwK,cAAc,MAM1C,OAJAyJ,GAAKI,GAAK,eAAeX,IACzBO,EAAK7O,UAAUC,IAAI,oBACnBpH,KAAK2V,kBAAkBN,sBAAsBvI,YAAYkJ,GAElDA,GAQDN,EAAVvV,UAAAgW,oBAAA,SAA8BH,GAC1B,MAAO,IAAIK,GAAAA,gBAAgBL,EAAMhW,KAAK4V,0BAA2B5V,KAAK8V,QAAS9V,KAAK+V,2BA9DxFvW,KAACC,EAAAA,iDAfDD,KAAQmJ,IADRnJ,KAAQG,IAZRH,KAAE8W,EAAAA,2BAUF9W,KAAQsU,IACRtU,KAAQwC,IATRxC,KAAE+W,EAAAA,iBACF/W,KAAEgX,EAAAA,WACFhX,KAAEyJ,EAAAA,SAoCFzJ,SAAA0J,GAAAjE,aAAAzF,KAAe2J,EAAAA,OAAfC,MAAsBC,EAAAA,eAjDtBqM,KXwCMe,GACJ,GAAIpS,IACCI,QAAS,QAASC,QAAS,WAC3BC,SAAU,QAASC,SAAU,QAClC,GAAIP,IACCI,QAAS,QAASC,QAAS,QAC3BC,SAAU,QAASC,SAAU,WAClC,GAAIP,IACDI,QAAS,MAAOC,QAAS,QACzBC,SAAU,MAAOC,SAAU,WAC9B,GAAIP,IACDI,QAAS,MAAOC,QAAS,WACzBC,SAAU,MAAOC,SAAU,SAInB8R,EACT,GAAIC,GAAAA,eAAqC,yCAShCC,GACX5B,QAAS0B,EACTzB,MAAOS,GACPP,WAAYhT,gBAaZ,QAAF0U,GAEa5C,GAAAjU,KAAbiU,WAAaA,EApFb,sBA6EAzU,KAACsX,EAAAA,UAAD1N,OACE2N,SAAU,6DACVC,SAAU,2DAjEZxX,KAAEyX,EAAAA,cAdFJ,kBA6QE,QAAFK,GACcC,EACRC,EACAC,EACuDC,EACnC9J,GAJZxN,KAAdmX,SAAcA,EAG+CnX,KAA7DsX,gBAA6DA,EACnCtX,KAA1BwN,KAA0BA,EAhL1BxN,KAAAuX,cAAyB,EACzBvX,KAAAwX,sBAAkC3J,EAAAA,aAAaC,MAC/C9N,KAAAyN,SAA6B,EAC7BzN,KAAA0N,SAA6B,EA8C7B1N,KAAA4D,eAAM5D,KAAKsX,kBAGXtX,KAAAyX,MAAoD,EA0GpDzX,KAAAqL,cAA4B,GAAIqM,GAAAA,aAGhC1X,KAAAmS,eAA6B,GAAIuF,GAAAA,aAGjC1X,KAAAwD,OAAqB,GAAIkU,GAAAA,aAGzB1X,KAAA6F,OAAqB,GAAI6R,GAAAA,aAUrB1X,KAAK2X,gBAAkB,GAAIC,GAAAA,eAAeR,EAAaC,GAnR3D,MAgHAjX,QAAA4J,eAAMkN,EAAN/W,UAAA,eAAA,WAA0B,MAAOH,MAAKyN,cACpC,SAAYlJ,GACVvE,KAAKyN,SAAWlJ,EACZvE,KAAK6X,WACP7X,KAAK6X,UAAUlI,YAAYpL,oCAMjCnE,OAAA4J,eAAMkN,EAAN/W,UAAA,eAAA,WAAkB,MAAOH,MAAK0N,cAC5B,SAAYlJ,GACVxE,KAAK0N,SAAWlJ,EACZxE,KAAK6X,WACP7X,KAAK6X,UAAUhI,YAAYrL,oCA4BjCpE,OAAA4J,eAAMkN,EAAN/W,UAAA,mBAAA,WAAsB,MAAOH,MAAKuX,kBAChC,SAAgB3V,GAAc5B,KAAKuX,aAAeO,EAAAA,sBAAsBlW,oCAO1ExB,OAAA4J,eAAMkN,EAAN/W,UAAA,yBAAA,WAA8C,MAAOH,MAAKsE,YACxD,SAAsB6J,GAA6BnO,KAAKsE,OAAS6J,mCAOnE/N,OAAA4J,eAAMkN,EAAN/W,UAAA,4BAAA,WAAyD,MAAOH,MAAKiQ,eACnE,SAAyB8H,GAAwC/X,KAAKiQ,UAAY8H,mCAOpF3X,OAAA4J,eAAMkN,EAAN/W,UAAA,0BAAA,WAAqC,MAAOH,MAAKuE,aAC/C,SAAuBkJ,GAAoBzN,KAAKuE,QAAUkJ,mCAO5DrN,OAAA4J,eAAMkN,EAAN/W,UAAA,0BAAA,WAAqC,MAAOH,MAAKwE,aAC/C,SAAuBkJ,GAAoB1N,KAAKwE,QAAUkJ,mCAO5DtN,OAAA4J,eAAMkN,EAAN/W,UAAA,wBAAA,WAA4C,MAAOH,MAAKkI,WACtD,SAAqB+K,GAA2BjT,KAAKkI,MAAQ+K,mCAO/D7S,OAAA4J,eAAMkN,EAAN/W,UAAA,yBAAA,WAA6C,MAAOH,MAAKgI,YACvD,SAAsBkL,GAA4BlT,KAAKgI,OAASkL,mCAOlE9S,OAAA4J,eAAMkN,EAAN/W,UAAA,2BAAA,WAA+C,MAAOH,MAAKiM,cACzD,SAAwB+L,GAA8BhY,KAAKiM,SAAW+L,mCAOxE5X,OAAA4J,eAAMkN,EAAN/W,UAAA,4BAAA,WAAgD,MAAOH,MAAKkM,eAC1D,SAAyB+L,GAA+BjY,KAAKkM,UAAY+L,mCAO3E7X,OAAA4J,eAAMkN,EAAN/W,UAAA,gCAAA,WAA2C,MAAOH,MAAK+D,mBACrD,SAA6BmU,GAA0BlY,KAAK+D,cAAgBmU,mCAO9E9X,OAAA4J,eAAMkN,EAAN/W,UAAA,iCAAA,WAAoD,MAAOH,MAAK4D,oBAC9D,SAA8B0T,GAC5BtX,KAAK4D,eAAiB0T,mCAQ1BlX,OAAA4J,eAAMkN,EAAN/W,UAAA,uBAAA,WAAmC,MAAOH,MAAKyX,UAC7C,SAAoBU,GAAkBnY,KAAKyX,KAAOU,mCAOpD/X,OAAA4J,eAAMkN,EAAN/W,UAAA,8BAAA,WAAiC,MAAOH,MAAK8D,iBAC3C,SAA2ByT,GAAqBvX,KAAK8D,YAAcyT,mCA0BnEnX,OAAF4J,eAAMkN,EAAN/W,UAAA,kBAAE,WACE,MAAOH,MAAK0F,6CAIdtF,OAAF4J,eAAMkN,EAAN/W,UAAA,WAAE,WACE,MAAOH,MAAKwN,KAAOxN,KAAKwN,KAAK5L,MAAQ,uCAGvCsV,EAAF/W,UAAAgU,YAAE,WACEnU,KAAKoY,mBAGPlB,EAAF/W,UAAAkY,YAAE,SAAYC,GACNtY,KAAK6X,aACHS,EAAmB,WAAKA,EAA8B,uBACxDtY,KAAK6X,UAAU7H,cAAchQ,KAAKiQ,YAGhCqI,EAAgB,QAAKA,EAA2B,qBAClDtY,KAAK6X,UAAU1H,UAAUnQ,KAAKsE,OAAO2P,YAEjCjU,KAAKyX,MACPzX,KAAK6X,UAAUnM,WAKjB4M,EAAc,MAAKA,EAAyB,mBAC9CtY,KAAKyX,KAAOzX,KAAKuY,iBAAmBvY,KAAKwY,mBAKrCtB,EAAV/W,UAAAsY,0BACSzY,KAAKiQ,WAAcjQ,KAAKiQ,UAAU9M,SACrCnD,KAAKiQ,UAAYwG,GAGnBzW,KAAK0F,YAAc1F,KAAKmX,SAAS9W,OAAOL,KAAK0Y,iBAIvCxB,EAAV/W,UAAAuY,wBACI,GAAMvO,GAAmBnK,KAAK6X,UAAY7X,KAAK2Y,0BACzCC,EAAgB,GAAInV,IACxB0G,iBAANA,EACMvG,eAAgB5D,KAAK4D,eACrBE,YAAa9D,KAAK8D,aAuBpB,QApBI9D,KAAKkI,OAAwB,IAAflI,KAAKkI,SACrB0Q,EAAc1Q,MAAQlI,KAAKkI,QAGzBlI,KAAKgI,QAA0B,IAAhBhI,KAAKgI,UACtB4Q,EAAc5Q,OAAShI,KAAKgI,SAG1BhI,KAAKiM,UAA8B,IAAlBjM,KAAKiM,YACxB2M,EAAc3M,SAAWjM,KAAKiM,WAG5BjM,KAAKkM,WAAgC,IAAnBlM,KAAKkM,aACzB0M,EAAc1M,UAAYlM,KAAKkM,WAG7BlM,KAAK+D,gBACP6U,EAAc7U,cAAgB/D,KAAK+D,eAG9B6U,GAID1B,EAAV/W,UAAAwY,mCASI,IAAK,WARCE,EAAkB7Y,KAAKiQ,UAAU,GACjClB,GAAetK,QAASoU,EAAgBpU,QAASC,QAASmU,EAAgBnU,SAC1EuK,GAAgBtK,SAAUkU,EAAgBlU,SAAUC,SAAUiU,EAAgBjU,UAC9EkU,EAAW9Y,KAAKmX,SAAS3H,WAC5BwE,YAAYhU,KAAKsE,OAAO2P,WAAYlF,EAAaE,GACjDU,YAAY3P,KAAKuE,SACjBsL,YAAY7P,KAAKwE,SAEXxB,EAAI,EAAGA,EAAIhD,KAAKiQ,UAAU9M,OAAQH,IACzC8V,EAASzK,sBACJ5J,QAASzE,KAAKiQ,UAAUjN,GAAGyB,QAASC,QAAS1E,KAAKiQ,UAAUjN,GAAG0B,UAC/DC,SAAU3E,KAAKiQ,UAAUjN,GAAG2B,SAAUC,SAAU5E,KAAKiQ,UAAUjN,GAAG4B,UAMzE,OAFAkU,GAASC,iBAAiB3S,UAAU,SAAA0I,GAAO,MAAAnL,GAAKwO,eAAe6G,KAAKlK,KAE7DgK,GAID5B,EAAV/W,UAAAoY,oCACSvY,MAAK0F,YAUR1F,KAAK0F,YAAYiG,YACfzD,MAAOlI,KAAKkI,MACZ+D,SAAUjM,KAAKiM,SACfjE,OAAQhI,KAAKgI,OACbkE,UAAWlM,KAAKkM,aAblBlM,KAAKyY,iBAELzY,KAAgB,YAAEwL,gBAAgBpF,UAAU,SAACsG,GACvCA,EAAMuM,UAAYC,EAAAA,QACpBvV,EAAK6U,oBAaXxY,KAAK6X,UAAUnI,cAAc1P,KAAK+L,KAClC/L,KAAK0F,YAAYoG,aAAa9L,KAAK+L,KAE9B/L,KAAK0F,YAAYC,gBACpB3F,KAAK0F,YAAYlC,OAAOxD,KAAK2X,iBAC7B3X,KAAKwD,OAAOwV,QAGVhZ,KAAK8D,cACP9D,KAAKwX,sBAAwBxX,KAAK0F,YAAY2F,gBAAgBjF,UAAU,WACtEzC,EAAK0H,cAAc2N,WAMjB9B,EAAV/W,UAAAqY,0BACQxY,KAAK0F,cACP1F,KAAK0F,YAAYG,SACjB7F,KAAK6F,OAAOmT,QAGdhZ,KAAKwX,sBAAsB/Q,eAIrByQ,EAAV/W,UAAAiY,2BACQpY,KAAK0F,aACP1F,KAAK0F,YAAYwF,UAGnBlL,KAAKwX,sBAAsB/Q,8BA/U/BjH,KAACsX,EAAAA,UAAD1N,OACE2N,SAAU,sEACVC,SAAU,8DAjEZxX,KAAQkW,IAJRlW,KAAE2Z,EAAAA,cACF3Z,KAAE4Z,EAAAA,mBAwPF5Z,SAAA0J,GAAAjE,aAAAzF,KAAO2J,EAAAA,OAAPC,MAAcsN,OAzQdlX,KAAmB6Z,EAAAA,eAAnBpU,aAAAzF,KA0QO0F,EAAAA,gCAzKPZ,SAAA9E,KAAG8Z,EAAAA,MAAHlQ,MAAS,+BAGT6G,YAAAzQ,KAAG8Z,EAAAA,MAAHlQ,MAAS,kCAGT7E,UAAA/E,KAAG8Z,EAAAA,MAAHlQ,MAAS,gCAUT5E,UAAAhF,KAAG8Z,EAAAA,MAAHlQ,MAAS,gCAUTlB,QAAA1I,KAAG8Z,EAAAA,MAAHlQ,MAAS,8BAGTpB,SAAAxI,KAAG8Z,EAAAA,MAAHlQ,MAAS,+BAGT6C,WAAAzM,KAAG8Z,EAAAA,MAAHlQ,MAAS,iCAGT8C,YAAA1M,KAAG8Z,EAAAA,MAAHlQ,MAAS,kCAGTrF,gBAAAvE,KAAG8Z,EAAAA,MAAHlQ,MAAS,sCAGTxF,iBAAApE,KAAG8Z,EAAAA,MAAHlQ,MAAS,uCAITqO,OAAAjY,KAAG8Z,EAAAA,MAAHlQ,MAAS,6BAGTtF,cAAAtE,KAAG8Z,EAAAA,MAAHlQ,MAAS,oCAQTmQ,oBAAA/Z,KAAG8Z,EAAAA,MAAHlQ,MAAS,YAQToQ,uBAAAha,KAAG8Z,EAAAA,MAAHlQ,MAAS,eAQTqQ,qBAAAja,KAAG8Z,EAAAA,MAAHlQ,MAAS,aAQTsQ,qBAAAla,KAAG8Z,EAAAA,MAAHlQ,MAAS,aAQTuQ,mBAAAna,KAAG8Z,EAAAA,MAAHlQ,MAAS,WAQTwQ,oBAAApa,KAAG8Z,EAAAA,MAAHlQ,MAAS,YAQTyQ,sBAAAra,KAAG8Z,EAAAA,MAAHlQ,MAAS,cAQT0Q,uBAAAta,KAAG8Z,EAAAA,MAAHlQ,MAAS,eAQT2Q,2BAAAva,KAAG8Z,EAAAA,MAAHlQ,MAAS,mBAQT4Q,4BAAAxa,KAAG8Z,EAAAA,MAAHlQ,MAAS,oBAUT6Q,kBAAAza,KAAG8Z,EAAAA,MAAHlQ,MAAS,UAQT8Q,yBAAA1a,KAAG8Z,EAAAA,MAAHlQ,MAAS,iBAKTiC,gBAAA7L,KAAG2a,EAAAA,SAGHhI,iBAAA3S,KAAG2a,EAAAA,SAGH3W,SAAAhE,KAAG2a,EAAAA,SAGHtU,SAAArG,KAAG2a,EAAAA,UAzQHjD,KYuBakD,GACX1E,EACA5B,EACAiB,EACAsF,EAAAA,wBACA7E,EACAoB,8BA7BF,sBAgCApX,KAAC8a,EAAAA,SAADlR,OACEmR,SAAUC,EAAAA,WAAYC,EAAAA,aAAcC,EAAAA,sBACpCC,SAAUzD,EAAqBL,EAAkB6D,EAAAA,sBACjDE,cAAe1D,EAAqBL,GACpCgE,WAAYT,EAAmBzR,6CApCjCmS,+EnBAA,MAmBgDC,GAAhDrb,EAAAsb,GACYtb,EAAZS,UAAAmV,iBAAE,WAAA,GAAF3R,GAAA3D,IACIgb,GAAJ7a,UAAUmV,iBAAVlS,KAAApD,MACIA,KAAKib,mCACLjb,KAAKkb,6BAA6B,WAAM,MAAAvX,GAAKsX,sCAGvCvb,EAAVS,UAAA8a,4CACI,GAAKjb,KAAKoV,kBAAV,EAGwBpV,KAAKmb,wBACKxU,SAASW,MACpCwF,YAAY9M,KAAKoV,qBAGlB1V,EAAVS,UAAA+a,6BAAA,SAAuCE,GAC/BzU,SAAS0U,kBACX1U,SAAS8F,iBAAiB,mBAAoB2O,GACrCzU,SAAS2U,wBAClB3U,SAAS8F,iBAAiB,yBAA0B2O,GAC3C,SAAkBG,qBAC3B5U,SAAS8F,iBAAiB,sBAAuB2O,GACxC,SAAkBI,qBAC3B7U,SAAS8F,iBAAiB,qBAAsB2O,IAQpD1b,EAAFS,UAAAgb,qBAAE,WACE,MAAOxU,UAAS8U,mBACZ9U,SAAS+U,yBACT,SAAkBC,sBAClB,SAAkBC,qBAClB;uJAtCRpc,KAACC,EAAAA,mDAlBDC,GAmBgDC"}