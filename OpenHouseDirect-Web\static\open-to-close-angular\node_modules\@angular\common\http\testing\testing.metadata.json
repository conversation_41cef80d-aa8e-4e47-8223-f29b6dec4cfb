{"__symbolic": "module", "version": 4, "metadata": {"HttpTestingController": {"__symbolic": "class", "members": {"match": [{"__symbolic": "method"}], "expectOne": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "expectNone": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "verify": [{"__symbolic": "method"}]}}, "RequestMatch": {"__symbolic": "interface"}, "HttpClientTestingModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 22, "character": 1}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/common/http", "name": "HttpClientModule", "line": 24, "character": 4}], "providers": [{"__symbolic": "reference", "name": "ɵa"}, {"provide": {"__symbolic": "reference", "module": "@angular/common/http", "name": "HttpBackend", "line": 28, "character": 14}, "useExisting": {"__symbolic": "reference", "name": "ɵa"}}, {"provide": {"__symbolic": "reference", "name": "HttpTestingController"}, "useExisting": {"__symbolic": "reference", "name": "ɵa"}}]}]}], "members": {}}, "TestRequest": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "HttpRequest", "module": "@angular/common/http", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "name": "Observer", "module": "rxjs/Observer", "arguments": [{"__symbolic": "reference", "name": "HttpEvent", "module": "@angular/common/http", "arguments": [{"__symbolic": "reference", "name": "any"}]}]}]}], "flush": [{"__symbolic": "method"}], "error": [{"__symbolic": "method"}], "event": [{"__symbolic": "method"}]}}, "ɵa": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 29, "character": 1}}], "members": {"handle": [{"__symbolic": "method"}], "_match": [{"__symbolic": "method"}], "match": [{"__symbolic": "method"}], "expectOne": [{"__symbolic": "method"}], "expectNone": [{"__symbolic": "method"}], "verify": [{"__symbolic": "method"}], "descriptionFromMatcher": [{"__symbolic": "method"}]}}}, "origins": {"HttpTestingController": "./src/api", "RequestMatch": "./src/api", "HttpClientTestingModule": "./src/module", "TestRequest": "./src/request", "ɵa": "./src/backend"}, "importAs": "@angular/common/http/testing"}