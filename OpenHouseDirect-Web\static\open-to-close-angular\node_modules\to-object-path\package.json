{"_from": "to-object-path@^0.3.0", "_id": "to-object-path@0.3.0", "_inBundle": false, "_integrity": "sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg==", "_location": "/to-object-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "to-object-path@^0.3.0", "name": "to-object-path", "escapedName": "to-object-path", "rawSpec": "^0.3.0", "saveSpec": null, "fetchSpec": "^0.3.0"}, "_requiredBy": ["/cache-base"], "_resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz", "_shasum": "297588b7b0e7e0ac08e04e672f85c1f4999e17af", "_spec": "to-object-path@^0.3.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\cache-base", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/to-object-path/issues"}, "bundleDependencies": false, "dependencies": {"kind-of": "^3.0.2"}, "deprecated": false, "description": "Create an object path from a list or array of strings.", "devDependencies": {"base": "^0.6.7", "mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/to-object-path", "keywords": ["dot", "nested", "notation", "object", "path", "stringify"], "license": "MIT", "main": "index.js", "name": "to-object-path", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-object-path.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["get-value", "set-value", "has-value", "omit-value", "unset-value"]}}, "version": "0.3.0"}