{"version": 3, "file": "ArrayLikeObservable.js", "sourceRoot": "", "sources": ["../../src/observable/ArrayLikeObservable.ts"], "names": [], "mappings": ";;;;;;AACA,2BAA2B,eAAe,CAAC,CAAA;AAC3C,iCAAiC,oBAAoB,CAAC,CAAA;AACtD,gCAAgC,mBAAmB,CAAC,CAAA;AAIpD;;;;GAIG;AACH;IAA4C,uCAAa;IAmCvD,6BAAoB,SAAuB,EAAU,SAAsB;QACzE,iBAAO,CAAC;QADU,cAAS,GAAT,SAAS,CAAc;QAAU,cAAS,GAAT,SAAS,CAAa;QAEzE,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAvCM,0BAAM,GAAb,UAAiB,SAAuB,EAAE,SAAsB;QAC9D,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAChC,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACjB,MAAM,CAAC,IAAI,iCAAe,EAAK,CAAC;QAClC,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,IAAI,mCAAgB,CAAS,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAC/D,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,MAAM,CAAC,IAAI,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEM,4BAAQ,GAAf,UAAgB,KAAU;QAChB,+BAAS,EAAE,mBAAK,EAAE,qBAAM,EAAE,6BAAU,CAAW;QAEvD,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,EAAE,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC;YACpB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAElC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QAEjB,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAaD,oCAAoC,CAAC,wCAAU,GAAV,UAAW,UAAyB;QACvE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAA,SAAqC,EAA7B,wBAAS,EAAE,wBAAS,CAAU;QACtC,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAEhC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACzD,oBAAS,EAAE,YAAK,EAAE,cAAM,EAAE,sBAAU;aACrC,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AA3DD,CAA4C,uBAAU,GA2DrD;AA3DY,2BAAmB,sBA2D/B,CAAA", "sourcesContent": ["import { IScheduler } from '../Scheduler';\nimport { Observable } from '../Observable';\nimport { ScalarObservable } from './ScalarObservable';\nimport { EmptyObservable } from './EmptyObservable';\nimport { Subscriber } from '../Subscriber';\nimport { TeardownLogic } from '../Subscription';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class ArrayLikeObservable<T> extends Observable<T> {\n\n  static create<T>(arrayLike: ArrayLike<T>, scheduler?: IScheduler): Observable<T> {\n    const length = arrayLike.length;\n    if (length === 0) {\n      return new EmptyObservable<T>();\n    } else if (length === 1) {\n      return new ScalarObservable<T>(<any>arrayLike[0], scheduler);\n    } else {\n      return new ArrayLikeObservable(arrayLike, scheduler);\n    }\n  }\n\n  static dispatch(state: any) {\n    const { arrayLike, index, length, subscriber } = state;\n\n    if (subscriber.closed) {\n      return;\n    }\n\n    if (index >= length) {\n      subscriber.complete();\n      return;\n    }\n\n    subscriber.next(arrayLike[index]);\n\n    state.index = index + 1;\n\n    (<any> this).schedule(state);\n  }\n\n  // value used if Array has one value and _isScalar\n  private value: any;\n\n  constructor(private arrayLike: ArrayLike<T>, private scheduler?: IScheduler) {\n    super();\n    if (!scheduler && arrayLike.length === 1) {\n      this._isScalar = true;\n      this.value = arrayLike[0];\n    }\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n    let index = 0;\n    const { arrayLike, scheduler } = this;\n    const length = arrayLike.length;\n\n    if (scheduler) {\n      return scheduler.schedule(ArrayLikeObservable.dispatch, 0, {\n        arrayLike, index, length, subscriber\n      });\n    } else {\n      for (let i = 0; i < length && !subscriber.closed; i++) {\n        subscriber.next(arrayLike[i]);\n      }\n      subscriber.complete();\n    }\n  }\n}\n"]}