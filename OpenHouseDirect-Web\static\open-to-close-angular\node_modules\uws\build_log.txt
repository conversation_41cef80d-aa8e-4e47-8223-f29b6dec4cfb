
C:\Users\<USER>\openhouse\OpenHouseDirect-Web\static\open-to-close-angular\node_modules\uws>if not defined npm_config_node_gyp (node "C:\Users\<USER>\AppData\Local\nvm\v8.9.0\node_modules\npm\bin\node-gyp-bin\\..\..\node_modules\node-gyp\bin\node-gyp.js" rebuild )  else (node "" rebuild ) 
gyp ERR! configure error 
gyp ERR! stack Error: Can't find Python executable "C:\Users\<USER>\anaconda3\python.EXE", you can set the PYTHON env variable.
gyp ERR! stack     at PythonFinder.failNoPython (C:\Users\<USER>\AppData\Local\nvm\v8.9.0\node_modules\npm\node_modules\node-gyp\lib\configure.js:483:19)
gyp ERR! stack     at PythonFinder.<anonymous> (C:\Users\<USER>\AppData\Local\nvm\v8.9.0\node_modules\npm\node_modules\node-gyp\lib\configure.js:508:16)
gyp ERR! stack     at C:\Users\<USER>\AppData\Local\nvm\v8.9.0\node_modules\npm\node_modules\graceful-fs\polyfills.js:284:29
gyp ERR! stack     at FSReqWrap.oncomplete (fs.js:152:21)
gyp ERR! System Windows_NT 10.0.26100
gyp ERR! command "C:\\nvm4w\\nodejs\\node.exe" "C:\\Users\\<USER>\\AppData\\Local\\nvm\\v8.9.0\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js" "rebuild"
gyp ERR! cwd C:\Users\<USER>\openhouse\OpenHouseDirect-Web\static\open-to-close-angular\node_modules\uws
gyp ERR! node -v v8.9.0
gyp ERR! node-gyp -v v3.6.2
gyp ERR! not ok 
