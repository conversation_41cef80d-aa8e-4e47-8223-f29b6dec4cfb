{"_from": "run-queue@^1.0.3", "_id": "run-queue@1.0.3", "_inBundle": false, "_integrity": "sha512-ntymy489o0/QQplUDnpYAYUsO50K9SBrIVaKCWDOJzYJts0f9WH9RFJkyagebkw5+y1oi00R7ynNW/d12GBumg==", "_location": "/run-queue", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "run-queue@^1.0.3", "name": "run-queue", "escapedName": "run-queue", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/copy-concurrently", "/move-concurrently"], "_resolved": "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz", "_shasum": "e848396f057d223f24386924618e25694161ec47", "_spec": "run-queue@^1.0.3", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\move-concurrently", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "bugs": {"url": "https://github.com/iarna/run-queue/issues"}, "bundleDependencies": false, "dependencies": {"aproba": "^1.1.1"}, "deprecated": false, "description": "A promise based, dynamic priority queue runner, with concurrency limiting.", "devDependencies": {"standard": "^8.6.0", "tap": "^10.2.0"}, "directories": {"test": "test"}, "files": ["queue.js"], "homepage": "https://npmjs.com/package/run-queue", "keywords": [], "license": "ISC", "main": "queue.js", "name": "run-queue", "repository": {"type": "git", "url": "git+https://github.com/iarna/run-queue.git"}, "scripts": {"test": "standard && tap -J test"}, "version": "1.0.3"}