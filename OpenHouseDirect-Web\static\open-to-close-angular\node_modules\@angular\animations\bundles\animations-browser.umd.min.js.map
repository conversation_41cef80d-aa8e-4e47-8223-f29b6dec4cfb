{"version": 3, "sources": ["animations-browser.umd.js"], "names": ["global", "factory", "exports", "module", "require", "define", "amd", "ng", "animations", "browser", "this", "_angular_animations", "__extends", "d", "b", "__", "constructor", "extendStatics", "prototype", "Object", "create", "optimizeGroupPlayer", "players", "length", "NoopAnimationPlayer", "ɵAnimationGroupPlayer", "normalizeKeyframes", "driver", "normalizer", "element", "keyframes", "preStyles", "postStyles", "errors", "normalizedKeyframes", "previousOffset", "previousKeyframe", "for<PERSON>ach", "kf", "offset", "isSameOffset", "normalizedKeyframe", "keys", "prop", "normalizedProp", "normalizedValue", "normalizePropertyName", "ɵPRE_STYLE", "AUTO_STYLE", "normalizeStyleValue", "push", "Error", "join", "listenOnPlayer", "player", "eventName", "event", "callback", "onStart", "copyAnimationEvent", "totalTime", "onDone", "onDestroy", "e", "phaseName", "makeAnimationEvent", "triggerName", "fromState", "toState", "undefined", "data", "getOrSetAsInMap", "map", "key", "defaultValue", "value", "Map", "get", "set", "parseTimelineCommand", "command", "separatorPos", "indexOf", "substring", "substr", "containsVendorPrefix", "validateStyleProperty", "_CACHED_BODY", "getBodyNode", "_IS_WEBKIT", "style", "result", "char<PERSON>t", "toUpperCase", "document", "body", "resolveTimingValue", "matches", "match", "_convertTimeValueToMS", "parseFloat", "unit", "ONE_SECOND", "resolveTiming", "timings", "allowNegativeValues", "hasOwnProperty", "parseTimeExpression", "exp", "duration", "regex", "delay", "easing", "delayMatch", "Math", "floor", "easingVal", "containsErrors", "startIndex", "splice", "copyObj", "obj", "destination", "normalizeStyles", "styles", "normalizedStyles", "Array", "isArray", "copyStyles", "readPrototype", "setStyles", "camelProp", "dashCaseToCamelCase", "eraseStyles", "normalizeAnimationEntry", "steps", "sequence", "validateStyleParams", "options", "params", "extractStyleParams", "varName", "val", "toString", "PARAM_REGEX", "exec", "lastIndex", "interpolateParams", "original", "str", "replace", "_", "localVal", "iteratorToArray", "iterator", "arr", "item", "next", "done", "input", "DASH_CASE_REGEXP", "m", "_i", "arguments", "allowPreviousPlayerStylesMerge", "visitDslNode", "visitor", "node", "context", "type", "visitTrigger", "visitState", "visitTransition", "visitSequence", "visitGroup", "visitAnimate", "visitKeyframes", "visitStyle", "visitReference", "visitAnimateChild", "visitAnimateRef", "visit<PERSON><PERSON><PERSON>", "visitStagger", "parseTransitionExpr", "transitionValue", "expressions", "split", "parseInnerTransitionStr", "eventStr", "parseAnimationAlias", "separator", "makeLambdaFromStates", "isFullAnyStateExpr", "ANY_STATE", "alias", "lhs", "rhs", "LHS_MATCH_BOOLEAN", "TRUE_BOOLEAN_VALUES", "has", "FALSE_BOOLEAN_VALUES", "RHS_MATCH_BOOLEAN", "lhsMatch", "rhsMatch", "buildAnimationAst", "metadata", "AnimationAstBuilderVisitor", "build", "normalizeSelector", "selector", "hasAmpersand", "find", "token", "SELF_TOKEN", "SELF_TOKEN_REGEX", "NG_TRIGGER_SELECTOR", "NG_ANIMATING_SELECTOR", "normalizeParams", "consumeOffset", "styleTuple", "isObject", "constructTimingAst", "makeTimingAst", "strValue", "some", "v", "ast", "dynamic", "normalizeAnimationOptions", "createTimelineInstruction", "preStyleProps", "postStyleProps", "subTimeline", "buildAnimationTimelines", "rootElement", "enterClassName", "leaveClassName", "startingStyles", "finalStyles", "subInstructions", "AnimationTimelineBuilderVisitor", "buildKeyframes", "roundOffset", "decimalPoints", "mult", "pow", "round", "flattenStyles", "allStyles", "allProperties", "createTransitionInstruction", "isRemovalTransition", "fromStyles", "to<PERSON><PERSON>les", "timelines", "queriedElements", "oneOrMoreTransitionsMatch", "matchFns", "currentState", "nextState", "fn", "buildTrigger", "name", "AnimationTrigger", "createFallbackTransition", "states", "AnimationTransitionFactory", "animation", "matchers", "queryCount", "depCount", "balanceProperties", "key1", "key2", "deleteOrUnsetInMap", "currentV<PERSON>ues", "index", "delete", "normalizeTriggerValue", "isElementNode", "isTriggerEventValid", "cloakElement", "oldValue", "display", "cloakAndComputeStyles", "valuesMap", "elements", "elementPropsMap", "defaultStyle", "cloakVals", "failedElements", "props", "computeStyle", "REMOVAL_FLAG", "NULL_REMOVED_QUERIED_STATE", "i", "buildRootMap", "roots", "nodes", "getRoot", "NULL_NODE", "root", "localRootMap", "parent", "parentNode", "rootMap", "nodeSet", "Set", "addClass", "className", "classList", "add", "classes", "CLASSES_CACHE_KEY", "removeClass", "remove", "removeNodesAfterAnimationDone", "engine", "processLeaveNode", "flattenGroupPlayers", "finalPlayers", "_flattenGroupPlayersRecur", "obj<PERSON><PERSON><PERSON>", "a", "k1", "k2", "replacePostStylesAsPre", "allPreStyleElements", "allPostStyleElements", "postEntry", "preEntry", "_computeStyle", "window", "getComputedStyle", "supportsWebAnimations", "Element", "setPrototypeOf", "__proto__", "p", "__assign", "assign", "t", "s", "n", "call", "_contains", "elm1", "elm2", "_matches", "_query", "multi", "contains", "proto", "fn_1", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "apply", "results", "querySelectorAll", "elm", "querySelector", "matchesElement", "containsElement", "invoke<PERSON><PERSON>y", "NoopAnimationDriver", "query", "animate", "previousPlayers", "AnimationDriver", "NOOP", "RegExp", "SUBSTITUTION_EXPR_START", "_driver", "AnimationAstBuilderContext", "_resetContextStyleTimingState", "currentQuerySelector", "collectedStyles", "currentTime", "_this", "transitions", "definitions", "def", "stateDef_1", "name_1", "transition", "styleAst", "astParams", "containsDynamicStyles", "missingSubs_1", "params_1", "stylesObj_1", "sub", "size", "missingSubsArr", "values", "expr", "furthestTime", "step", "innerAst", "max", "timingAst", "currentAnimateTimings", "styleMetadata", "styleMetadata_1", "isEmpty", "newStyleData", "_styleAst", "isEmptyStep", "_makeStyleAst", "_validateStyleAst", "collectedEasing", "styleData", "styleMap", "endTime", "startTime", "tuple", "collectedEntry", "updateCollectedStyle", "totalKeyframesWithOffsets", "offsets", "offsetsOutOfOrder", "keyframesOutOfRange", "style$$1", "offsetVal", "generatedOffset", "limit", "animateDuration", "durationUpToThisFrame", "parentSelector", "<PERSON><PERSON><PERSON><PERSON>", "_a", "includeSelf", "optional", "originalSelector", "currentTransition", "ElementInstructionMap", "_map", "consume", "instructions", "append", "existingInstructions", "clear", "ENTER_TOKEN_REGEX", "LEAVE_TOKEN_REGEX", "AnimationTimelineContext", "currentTimeline", "filter", "timeline", "containsAnimation", "tl", "allowOnlyTimelineStyles", "elementInstructions", "innerContext", "createSubContext", "_visitSubInstructions", "transformIntoNewTimeline", "previousNode", "instruction", "instructionTimings", "appendInstructionToTimeline", "updateOptions", "subContextCount", "ctx", "snapshotCurrentStyles", "DEFAULT_NOOP_PREVIOUS_NODE", "delayNextStep", "applyStylesToKeyframe", "innerTimelines", "mergeTimelineCollectedStyles", "_visitTiming", "incrementTime", "getCurrentStyleProperties", "<PERSON><PERSON><PERSON><PERSON>", "applyEmptyStep", "innerTimeline", "forwardTime", "elms", "currentQueryTotal", "sameElementTimeline", "currentQueryIndex", "parentContext", "abs", "maxTime", "currentStaggerTime", "startingTime", "_enterClassName", "_leaveClassName", "initialTimeline", "TimelineBuilder", "defineProperty", "enumerable", "configurable", "skipIfExists", "newOptions", "optionsToUpdate", "newParams", "paramsToUpdate_1", "_copyOptions", "oldParams_1", "newTime", "target", "fork", "updatedTimings", "builder", "SubTimelineBuilder", "stretchStartingKeyframe", "time", "slice", "_elementTimelineStylesLookup", "_previousKeyframe", "_currentKeyframe", "_keyframes", "_styleSummary", "_pendingStyles", "_backFill", "_currentEmptyStepKeyframe", "_localTimelineStyles", "_globalTimelineStyles", "_loadKeyframe", "hasPreStyleStep", "_updateStyle", "getFinalKeyframe", "properties", "details0", "details1", "finalKeyframes", "keyframe", "finalKeyframe", "preProps", "postProps", "kf0", "kf1", "_super", "_stretchStartingKeyframe", "newKeyframes", "startingGap", "newFirstKeyframe", "oldFirstKeyframe", "oldOffset", "timeAtKeyframe", "Animation", "errorMessage", "_animationAst", "buildTimelines", "destinationStyles", "start", "dest", "AnimationStyleNormalizer", "NoopAnimationStyleNormalizer", "propertyName", "userProvidedProperty", "normalizedProperty", "WebAnimationsStyleNormalizer", "strVal", "trim", "DIMENSIONAL_PROP_MAP", "valAndSuffixMatch", "EMPTY_OBJECT", "_triggerName", "_stateStyles", "buildStyles", "stateName", "backupStateStyler", "stateStyler", "backup<PERSON><PERSON><PERSON>", "currentOptions", "nextOptions", "transitionAnimationParams", "currentAnimationParams", "currentStateStyles", "nextAnimationParams", "nextStateStyles", "preStyleMap", "postStyleMap", "isRemoval", "animationOptions", "queriedElementsList", "AnimationStateStyles", "defaultParams", "combinedParams", "styleObj_1", "transitionFactories", "fallbackTransition", "matchTransition", "f", "matchStyles", "EMPTY_INSTRUCTION_MAP", "TimelineAnimationEngine", "_normalizer", "_animations", "_playersById", "register", "id", "_buildPlayer", "autoStylesMap", "inst", "destroy", "_getPlayer", "listen", "baseEvent", "args", "play", "pause", "reset", "restart", "finish", "init", "setPosition", "EMPTY_PLAYER_ARRAY", "NULL_REMOVAL_STATE", "namespaceId", "setForRemoval", "hasAnimation", "removed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StateValue", "isObj", "absorbOptions", "DEFAULT_STATE_VALUE", "DELETED_STATE_VALUE", "AnimationTransitionNamespace", "hostElement", "_engine", "_triggers", "_queue", "_elementListeners", "_hostClassName", "phase", "listeners", "triggersWithStates", "statesByElement", "NG_TRIGGER_CLASSNAME", "afterFlush", "_getTrigger", "trigger", "defaultToFallback", "TransitionAnimationPlayer", "playersOnElement", "players<PERSON>y<PERSON><PERSON>", "queued", "isFallbackTransition", "totalQueuedPlayers", "index_1", "fromStyles_1", "toStyles_1", "reportError", "deregister", "stateMap", "entry", "clearElementCache", "elementPlayers", "_signalRemovalForInnerTriggers", "namespaces", "fetchNamespacesByElement", "ns", "triggerLeaveAnimation", "destroyAfterComplete", "triggerStates", "players_1", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>oved", "prepareLeaveAnimationListeners", "visitedTriggers_1", "listener", "elementStates", "removeNode", "childElementCount", "containsPotentialParentTransition", "totalAnimations", "currentPlayers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent_1", "triggers", "destroyInnerAnimations", "_onRemovalComplete", "insertNode", "drainQueuedTransitions", "microtaskId", "destroyed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort", "d0", "d1", "elementContainsData", "containsData", "TransitionAnimationEngine", "newHostElements", "disabledNodes", "_namespaceLookup", "_namespaceList", "_flushFns", "_whenQuietFns", "namespacesByHostElement", "collectedEnterElements", "collectedLeaveElements", "onRemovalComplete", "createNamespace", "_balanceNamespaceList", "collectEnterElement", "found", "nextNamespace", "registerTrigger", "_fetchNamespace", "afterFlushAnimationsDone", "nsId", "insertBefore", "details", "markElementAsDisabled", "_buildInstruction", "subTimelines", "containerElement", "destroyActiveAnimationsForElement", "finishActiveQueriedAnimationOnElement", "whenRenderingDone", "Promise", "resolve", "flush", "cleanupFns", "_flushAnimations", "quietFns_1", "skippedPlayers", "skippedPlayersMap", "queuedInstructions", "disabledElementsSet", "nodesThatAreDisabled", "i_1", "bodyNode", "allTriggerElements", "from", "enterNodeMap", "enterNodeMapIds", "allLeaveNodes", "mergedLeaveNodes", "leaveNodesWithoutAnimations", "i_2", "leaveNodeMapIds", "leaveNodeMap", "allPlayers", "erroneousTransitions", "i_3", "stringMap", "setVal_1", "setVal", "errors_1", "error", "allPreviousPlayersMap", "animationElementMap", "_beforeAnimationBuild", "_getPreviousPlayers", "prevPlayer", "replaceNodes", "postStylesMap", "preStylesMap", "post", "pre", "rootPlayers", "subPlayers", "NO_PARENT_ANIMATION_ELEMENT_DETECTED", "parentWithAnimation_1", "parentsToAdd", "detectedParent", "innerPlayer", "_buildAnimation", "setRealPlayer", "parentPlayers", "parentPlayer", "playersFor<PERSON>lement", "syncPlayerEvents", "i_4", "queriedPlayerResults", "queriedInnerElements", "j", "queriedPlayers", "activePlayers", "isQueriedElement", "toStateValue", "queriedElementPlayers", "isRemovalAnimation_1", "targetNameSpaceId", "targetTriggerName", "this_1", "timelineInstruction", "realPlayer", "getRealPlayer", "<PERSON><PERSON><PERSON><PERSON>", "allQueriedPlayers", "allConsumedElements", "allSubElements", "allNewPlayers", "pp", "wrappedPlayer", "_player", "_containsRealPlayer", "_queuedCallbacks", "triggerCallback", "_queueEvent", "hasStarted", "getPosition", "AnimationEngine", "_triggerCache", "_transitionEngine", "_timelineEngine", "componentId", "cache<PERSON>ey", "onInsert", "onRemove", "disableAnimations", "disable", "process", "property", "action", "eventPhase", "concat", "WebAnimationsPlayer", "_onDoneFns", "_onStartFns", "_onDestroyFns", "_initialized", "_finished", "_started", "_destroyed", "previousStyles", "currentSnapshot", "_duration", "_delay", "_onFinish", "_preparePlayerBeforeStart", "previousStyleProps", "startingKeyframe_1", "missingStyleProps_1", "self_1", "domPlayer", "_triggerWebAnimation", "_finalKeyframe", "addEventListener", "_resetDomPlayerState", "cancel", "methods", "WebAnimationsDriver", "fill", "playerOptions", "previousWebAnimationPlayers", "ɵAnimation", "ɵAnimationStyleNormalizer", "ɵNoopAnimationStyleNormalizer", "ɵWebAnimationsStyleNormalizer", "ɵNoopAnimationDriver", "ɵAnimationEngine", "ɵWebAnimationsDriver", "ɵsupportsWebAnimations", "ɵWebAnimationsPlayer"], "mappings": ";;;;;CAKC,SAAUA,OAAQC,SACC,gBAAZC,UAA0C,mBAAXC,QAAyBF,QAAQC,QAASE,QAAQ,wBACtE,kBAAXC,SAAyBA,OAAOC,IAAMD,OAAO,+BAAgC,UAAW,uBAAwBJ,SACtHA,SAASD,OAAOO,GAAKP,OAAOO,OAAUP,OAAOO,GAAGC,WAAaR,OAAOO,GAAGC,eAAkBR,OAAOO,GAAGC,WAAWC,YAAcT,OAAOO,GAAGC,aACtIE,KAAM,SAAWR,QAAQS,qBAAuB,YAsBlD,SAASC,WAAUC,EAAGC,GAElB,QAASC,MAAOL,KAAKM,YAAcH,EADnCI,cAAcJ,EAAGC,GAEjBD,EAAEK,UAAkB,OAANJ,EAAaK,OAAOC,OAAON,IAAMC,GAAGG,UAAYJ,EAAEI,UAAW,GAAIH;;;;;AAwBnF,QAASM,qBAAoBC,SACzB,OAAQA,QAAQC,QACZ,IAAK,GACD,MAAO,IAAIZ,qBAAoBa,mBACnC,KAAK,GACD,MAAOF,SAAQ,EACnB,SACI,MAAO,IAAIX,qBAAoBc,sBAAsBH,UAYjE,QAASI,oBAAmBC,OAAQC,WAAYC,QAASC,UAAWC,UAAWC,gBACzD,KAAdD,YAAwBA,kBACT,KAAfC,aAAyBA,cAC7B,IAAqBC,WACAC,uBACAC,gBAAkB,EAClBC,iBAAmB,IA+BxC,IA9BAN,UAAUO,QAAQ,SAAUC,IACxB,GAAqBC,QAA2BD,GAAY,OACvCE,aAAeD,QAAUJ,eACzBM,mBAAsBD,cAAgBJ,oBAC3DjB,QAAOuB,KAAKJ,IAAID,QAAQ,SAAUM,MAC9B,GAAqBC,gBAAiBD,KACjBE,gBAAkBP,GAAGK,KAC1C,IAAa,WAATA,KAEA,OADAC,eAAiBhB,WAAWkB,sBAAsBF,eAAgBX,QAC1DY,iBACJ,IAAKlC,qBAAoBoC,WACrBF,gBAAkBd,UAAUY,KAC5B,MACJ,KAAKhC,qBAAoBqC,WACrBH,gBAAkBb,WAAWW,KAC7B,MACJ,SACIE,gBACIjB,WAAWqB,oBAAoBN,KAAMC,eAAgBC,gBAAiBZ,QAItFQ,mBAAmBG,gBAAkBC,kBAEpCL,cACDN,oBAAoBgB,KAAKT,oBAE7BL,iBAAmBK,mBACnBN,eAAiBI,SAEjBN,OAAOV,OAAQ,CAEf,KAAM,IAAI4B,OAAM,sDAAgElB,OAAOmB,KADrD,UAGtC,MAAOlB,qBASX,QAASmB,gBAAeC,OAAQC,UAAWC,MAAOC,UAC9C,OAAQF,WACJ,IAAK,QACDD,OAAOI,QAAQ,WAAc,MAAOD,UAASD,OAASG,mBAAmBH,MAAO,QAASF,OAAOM,aAChG,MACJ,KAAK,OACDN,OAAOO,OAAO,WAAc,MAAOJ,UAASD,OAASG,mBAAmBH,MAAO,OAAQF,OAAOM,aAC9F,MACJ,KAAK,UACDN,OAAOQ,UAAU,WAAc,MAAOL,UAASD,OAASG,mBAAmBH,MAAO,UAAWF,OAAOM,eAUhH,QAASD,oBAAmBI,EAAGC,UAAWJ,WACtC,GAAqBJ,OAAQS,mBAAmBF,EAAElC,QAASkC,EAAEG,YAAaH,EAAEI,UAAWJ,EAAEK,QAASJ,WAAaD,EAAEC,cAAwBK,IAAbT,UAAyBG,EAAEH,UAAYA,WAC9IU,KAAO,EAA8B,KAI1D,OAHY,OAARA,OACA,MAAkC,MAAIA,MAEnCd,MAWX,QAASS,oBAAmBpC,QAASqC,YAAaC,UAAWC,QAASJ,UAAWJ,WAG7E,WAFkB,KAAdI,YAAwBA,UAAY,QACtB,KAAdJ,YAAwBA,UAAY,IAC/B/B,QAASA,QAASqC,YAAaA,YAAaC,UAAWA,UAAWC,QAASA,QAASJ,UAAWA,UAAWJ,UAAWA,WAQlI,QAASW,iBAAgBC,IAAKC,IAAKC,cAC/B,GAAqBC,MAarB,OAZIH,eAAeI,MACfD,MAAQH,IAAIK,IAAIJ,OAEZD,IAAIM,IAAIL,IAAKE,MAAQD,eAIzBC,MAAQH,IAAIC,QAERE,MAAQH,IAAIC,KAAOC,cAGpBC,MAMX,QAASI,sBAAqBC,SAC1B,GAAqBC,cAAeD,QAAQE,QAAQ,IAGpD,QAF0BF,QAAQG,UAAU,EAAGF,cACjBD,QAAQI,OAAOH,aAAe,IA0ChE,QAASI,sBAAqB1C,MAG1B,MAA+B,SAAxBA,KAAKwC,UAAU,EAAG,GAQ7B,QAASG,uBAAsB3C,MACtB4C,eACDA,aAAeC,kBACfC,aAA8B,aAAiBC,OAAS,oBAAuC,cAAiBA,MAEpH,IAAqBC,SAAS,CAC9B,IAAqB,aAAiBD,QAAUL,qBAAqB1C,SACjEgD,OAAShD,OAAyB,cAAiB+C,QACpCD,WAAY,CAEvBE,OADiC,SAAWhD,KAAKiD,OAAO,GAAGC,cAAgBlD,KAAKyC,OAAO,IAChD,cAAiBM,MAGhE,MAAOC,QAKX,QAASH,eACL,MAAuB,mBAAZM,UACAA,SAASC,KAEb,KAqIX,QAASC,oBAAmBrB,OACxB,GAAoB,gBAATA,OACP,MAAOA,MACX,IAAqBsB,SAAU,MAA2BC,MAAM,oBAChE,QAAKD,SAAWA,QAAQ1E,OAAS,EACtB,EACJ4E,sBAAsBC,WAAWH,QAAQ,IAAKA,QAAQ,IAOjE,QAASE,uBAAsBxB,MAAO0B,MAClC,OAAQA,MACJ,IAAK,IACD,MAAO1B,OAAQ2B,UACnB,SAEI,MAAO3B,QASnB,QAAS4B,eAAcC,QAASvE,OAAQwE,qBACpC,MAAOD,SAAQE,eAAe,YAA+B,QACzDC,oBAAqC,QAAW1E,OAAQwE,qBAQhE,QAASE,qBAAoBC,IAAK3E,OAAQwE,qBACtC,GACqBI,UADAC,MAAQ,2EAERC,MAAQ,EACRC,OAAS,EAC9B,IAAmB,gBAARJ,KAAkB,CACzB,GAAqBX,SAAUW,IAAIV,MAAMY,MACzC,IAAgB,OAAZb,QAEA,MADAhE,QAAOiB,KAAK,8BAAiC0D,IAAM,kBAC1CC,SAAU,EAAGE,MAAO,EAAGC,OAAQ,GAE5CH,UAAWV,sBAAsBC,WAAWH,QAAQ,IAAKA,QAAQ,GACjE,IAAqBgB,YAAahB,QAAQ,EACxB,OAAdgB,aACAF,MAAQZ,sBAAsBe,KAAKC,MAAMf,WAAWa,aAAchB,QAAQ,IAE9E,IAAqBmB,WAAYnB,QAAQ,EACrCmB,aACAJ,OAASI,eAIbP,UAA4B,GAEhC,KAAKJ,oBAAqB,CACtB,GAAqBY,iBAAiB,EACjBC,WAAarF,OAAOV,MACrCsF,UAAW,IACX5E,OAAOiB,KAAK,oEACZmE,gBAAiB,GAEjBN,MAAQ,IACR9E,OAAOiB,KAAK,iEACZmE,gBAAiB,GAEjBA,gBACApF,OAAOsF,OAAOD,WAAY,EAAG,8BAAiCV,IAAM,iBAG5E,OAASC,SAAUA,SAAUE,MAAOA,MAAOC,OAAQA,QAOvD,QAASQ,SAAQC,IAAKC,aAGlB,WAFoB,KAAhBA,cAA0BA,gBAC9BvG,OAAOuB,KAAK+E,KAAKpF,QAAQ,SAAUM,MAAQ+E,YAAY/E,MAAQ8E,IAAI9E,QAC5D+E,YAMX,QAASC,iBAAgBC,QACrB,GAAqBC,oBAOrB,OANIC,OAAMC,QAAQH,QACdA,OAAOvF,QAAQ,SAAUiC,MAAQ,MAAO0D,YAAW1D,MAAM,EAAOuD,oBAGhEG,WAAWJ,QAAQ,EAAOC,kBAEvBA,iBAQX,QAASG,YAAWJ,OAAQK,cAAeP,aAEvC,OADoB,KAAhBA,cAA0BA,gBAC1BO,cAIA,IAAK,GAAqBtF,QAAQiF,QAC9BF,YAAY/E,MAAQiF,OAAOjF,UAI/B6E,SAAQI,OAAQF,YAEpB,OAAOA,aAOX,QAASQ,WAAUrG,QAAS+F,QACpB/F,QAAe,OACfV,OAAOuB,KAAKkF,QAAQvF,QAAQ,SAAUM,MAClC,GAAqBwF,WAAYC,oBAAoBzF,KACrDd,SAAQ6D,MAAMyC,WAAaP,OAAOjF,QAS9C,QAAS0F,aAAYxG,QAAS+F,QACtB/F,QAAe,OACfV,OAAOuB,KAAKkF,QAAQvF,QAAQ,SAAUM,MAClC,GAAqBwF,WAAYC,oBAAoBzF,KACrDd,SAAQ6D,MAAMyC,WAAa,KAQvC,QAASG,yBAAwBC,OAC7B,MAAIT,OAAMC,QAAQQ,OACM,GAAhBA,MAAMhH,OACCgH,MAAM,GACV5H,oBAAoB6H,SAASD,OAEhB,MAQ5B,QAASE,qBAAoB9D,MAAO+D,QAASzG,QACzC,GAAqB0G,QAASD,QAAQC,WACjB1C,QAAU2C,mBAAmBjE,MAC9CsB,SAAQ1E,QACR0E,QAAQ5D,QAAQ,SAAUwG,SACjBF,OAAOjC,eAAemC,UACvB5G,OAAOiB,KAAK,+CAAiD2F,QAAU,kCAUvF,QAASD,oBAAmBjE,OACxB,GAAqBgE,UACrB,IAAqB,gBAAVhE,OAAoB,CAG3B,IAFA,GAAqBmE,KAAMnE,MAAMoE,WACZ7C,UAAQ,GACtBA,MAAQ8C,YAAYC,KAAKH,MAC5BH,OAAOzF,KAAuBgD,MAAM,GAExC8C,aAAYE,UAAY,EAE5B,MAAOP,QAQX,QAASQ,mBAAkBxE,MAAOgE,OAAQ1G,QACtC,GAAqBmH,UAAWzE,MAAMoE,WACjBM,IAAMD,SAASE,QAAQN,YAAa,SAAUO,EAAGV,SAClE,GAAqBW,UAAWb,OAAOE,QAMvC,OAJKF,QAAOjC,eAAemC,WACvB5G,OAAOiB,KAAK,kDAAoD2F,SAChEW,SAAW,IAERA,SAAST,YAGpB,OAAOM,MAAOD,SAAWzE,MAAQ0E,IAMrC,QAASI,iBAAgBC,UAGrB,IAFA,GAAqBC,QACAC,KAAOF,SAASG,QAC7BD,KAAKE,MACTH,IAAIzG,KAAK0G,KAAKjF,OACdiF,KAAOF,SAASG,MAEpB,OAAOF,KAaX,QAASvB,qBAAoB2B,OACzB,MAAOA,OAAMT,QAAQU,iBAAkB,WAEnC,IAAK,GADDC,MACKC,GAAK,EAAGA,GAAKC,UAAU5I,OAAQ2I,KACpCD,EAAEC,IAAMC,UAAUD,GAEtB,OAAOD,GAAE,GAAGpE,gBAQpB,QAASuE,gCAA+BvD,SAAUE,OAC9C,MAAoB,KAAbF,UAA4B,IAAVE,MAQ7B,QAASsD,cAAaC,QAASC,KAAMC,SACjC,OAAQD,KAAKE,MACT,IAAK,GACD,MAAOH,SAAQI,aAAaH,KAAMC,QACtC,KAAK,GACD,MAAOF,SAAQK,WAAWJ,KAAMC,QACpC,KAAK,GACD,MAAOF,SAAQM,gBAAgBL,KAAMC,QACzC,KAAK,GACD,MAAOF,SAAQO,cAAcN,KAAMC,QACvC,KAAK,GACD,MAAOF,SAAQQ,WAAWP,KAAMC,QACpC,KAAK,GACD,MAAOF,SAAQS,aAAaR,KAAMC,QACtC,KAAK,GACD,MAAOF,SAAQU,eAAeT,KAAMC,QACxC,KAAK,GACD,MAAOF,SAAQW,WAAWV,KAAMC,QACpC,KAAK,GACD,MAAOF,SAAQY,eAAeX,KAAMC,QACxC,KAAK,GACD,MAAOF,SAAQa,kBAAkBZ,KAAMC,QAC3C,KAAK,IACD,MAAOF,SAAQc,gBAAgBb,KAAMC,QACzC,KAAK,IACD,MAAOF,SAAQe,WAAWd,KAAMC,QACpC,KAAK,IACD,MAAOF,SAAQgB,aAAaf,KAAMC,QACtC,SACI,KAAM,IAAIrH,OAAM,8CAAgDoH,KAAKE,OAqBjF,QAASc,qBAAoBC,gBAAiBvJ,QAC1C,GAAqBwJ,eASrB,OAR8B,gBAAnBD,iBACP,gBACKE,MAAM,WACNrJ,QAAQ,SAAUgH,KAAO,MAAOsC,yBAAwBtC,IAAKoC,YAAaxJ,UAG/EwJ,YAAYvI,KAAsB,iBAE/BuI,YAQX,QAASE,yBAAwBC,SAAUH,YAAaxJ,QACpD,GAAmB,KAAf2J,SAAS,GAAW,CACpB,GAAqBjG,QAASkG,oBAAoBD,SAAU3J,OAC5D,IAAqB,kBAAV0D,QAEP,WADA8F,aAAYvI,KAAKyC,OAGrBiG,UAA4B,OAEhC,GAAqB1F,OAAQ0F,SAAS1F,MAAM,0CAC5C,IAAa,MAATA,OAAiBA,MAAM3E,OAAS,EAEhC,MADAU,QAAOiB,KAAK,uCAA0C0I,SAAW,sBAC1DH,WAEX,IAAqBtH,WAAY+B,MAAM,GAClB4F,UAAY5F,MAAM,GAClB9B,QAAU8B,MAAM,EACrCuF,aAAYvI,KAAK6I,qBAAqB5H,UAAWC,SACjD,IAAqB4H,oBAAqB7H,WAAa8H,WAAa7H,SAAW6H,SAC3D,MAAhBH,UAAU,IAAcE,oBACxBP,YAAYvI,KAAK6I,qBAAqB3H,QAASD,YAQvD,QAAS0H,qBAAoBK,MAAOjK,QAChC,OAAQiK,OACJ,IAAK,SACD,MAAO,WACX,KAAK,SACD,MAAO,WACX,KAAK,aACD,MAAO,UAAU/H,UAAWC,SAAW,MAAOgC,YAAWhC,SAAWgC,WAAWjC,WACnF,KAAK,aACD,MAAO,UAAUA,UAAWC,SAAW,MAAOgC,YAAWhC,SAAWgC,WAAWjC,WACnF,SAEI,MADAlC,QAAOiB,KAAK,+BAAkCgJ,MAAQ,sBAC/C,UAcnB,QAASH,sBAAqBI,IAAKC,KAC/B,GAAqBC,mBAAoBC,oBAAoBC,IAAIJ,MAAQK,qBAAqBD,IAAIJ,KAC7EM,kBAAoBH,oBAAoBC,IAAIH,MAAQI,qBAAqBD,IAAIH,IAClG,OAAO,UAAUjI,UAAWC,SACxB,GAAqBsI,UAAWP,KAAOF,WAAaE,KAAOhI,UACtCwI,SAAWP,KAAOH,WAAaG,KAAOhI,OAO3D,QANKsI,UAAYL,mBAA0C,iBAAdlI,aACzCuI,SAAWvI,UAAYmI,oBAAoBC,IAAIJ,KAAOK,qBAAqBD,IAAIJ,OAE9EQ,UAAYF,mBAAwC,iBAAZrI,WACzCuI,SAAWvI,QAAUkI,oBAAoBC,IAAIH,KAAOI,qBAAqBD,IAAIH,MAE1EM,UAAYC,UAgB3B,QAASC,mBAAkBjL,OAAQkL,SAAU5K,QACzC,MAAO,IAAI6K,4BAA2BnL,QAAQoL,MAAMF,SAAU5K,QAqhBlE,QAAS+K,mBAAkBC,UACvB,GAAqBC,gBAAeD,SAASvB,MAAM,WAAWyB,KAAK,SAAUC,OAAS,MAAOA,QAASC,YAQtG,OAPIH,gBACAD,SAAWA,SAAS3D,QAAQgE,iBAAkB,KAGlDL,SAAWA,SAAS3D,QAAQ,OAAQiE,qBAC/BjE,QAAQ,QAAS,SAAUpD,OAAS,MAAOqH,qBAAsB,IAAMrH,MAAMd,OAAO,KACpFkE,QAAQ,cAAekE,wBACpBP,SAAUC,cAMtB,QAASO,iBAAgBhG,KACrB,MAAOA,KAAMD,QAAQC,KAAO,KAqBhC,QAASiG,eAAc9F,QACnB,GAAqB,gBAAVA,QACP,MAAO,KACX,IAAqBrF,QAAS,IAC9B,IAAIuF,MAAMC,QAAQH,QACdA,OAAOvF,QAAQ,SAAUsL,YACrB,GAAIC,SAASD,aAAeA,WAAWjH,eAAe,UAAW,CAC7D,GAAqBe,KAAuB,UAC5ClF,QAAS6D,WAA6BqB,IAAa,cAC5CA,KAAY,cAI1B,IAAImG,SAAShG,SAAWA,OAAOlB,eAAe,UAAW,CAC1D,GAAqBe,KAAuB,MAC5ClF,QAAS6D,WAA6BqB,IAAa,cAC5CA,KAAY,OAEvB,MAAOlF,QAMX,QAASqL,UAASjJ,OACd,OAAQmD,MAAMC,QAAQpD,QAA0B,gBAATA,OAO3C,QAASkJ,oBAAmBlJ,MAAO1C,QAC/B,GAAqBuE,SAAU,IAC/B,IAAI7B,MAAM+B,eAAe,YACrBF,QAA2B,UAE1B,IAAoB,gBAAT7B,OAAmB,CAC/B,GAAqBkC,UAAWN,cAA+B,MAAStE,QAAQ4E,QAChF,OAAOiH,eAA+B,SAAY,EAAG,IAEzD,GAAqBC,UAA4B,KAEjD,IADiCA,SAASrC,MAAM,OAAOsC,KAAK,SAAUC,GAAK,MAAsB,KAAfA,EAAErI,OAAO,IAA4B,KAAfqI,EAAErI,OAAO,KAClG,CACX,GAAqBsI,KAAwBJ,cAAc,EAAG,EAAG,GAGjE,OAFAI,KAAIC,SAAU,EACdD,IAAIH,SAAWA,SACS,IAG5B,MADAvH,SAAUA,SAAWD,cAAcwH,SAAU9L,QACtC6L,cAActH,QAAQK,SAAUL,QAAQO,MAAOP,QAAQQ,QAMlE,QAASoH,2BAA0B1F,SAU/B,MATIA,UACAA,QAAUlB,QAAQkB,SACdA,QAAgB,SAChBA,QAAgB,OAAuB+E,gBAAgB/E,QAAgB,UAI3EA,WAEGA,QAQX,QAASoF,eAAcjH,SAAUE,MAAOC,QACpC,OAASH,SAAUA,SAAUE,MAAOA,MAAOC,OAAQA,QAsBvD,QAASqH,2BAA0BxM,QAASC,UAAWwM,cAAeC,eAAgB1H,SAAUE,MAAOC,OAAQwH,aAG3G,WAFe,KAAXxH,SAAqBA,OAAS,UACd,KAAhBwH,cAA0BA,aAAc,IAExC/D,KAAM,EACN5I,QAASA,QACTC,UAAWA,UACXwM,cAAeA,cACfC,eAAgBA,eAChB1H,SAAUA,SACVE,MAAOA,MACPnD,UAAWiD,SAAWE,MAAOC,OAAQA,OAAQwH,YAAaA,aAwFlE,QAASC,yBAAwB9M,OAAQ+M,YAAaR,IAAKS,eAAgBC,eAAgBC,eAAgBC,YAAapG,QAASqG,gBAAiB9M,QAI9I,WAHuB,KAAnB4M,iBAA6BA,uBACb,KAAhBC,cAA0BA,oBACf,KAAX7M,SAAqBA,YAClB,GAAI+M,kCAAkCC,eAAetN,OAAQ+M,YAAaR,IAAKS,eAAgBC,eAAgBC,eAAgBC,YAAapG,QAASqG,gBAAiB9M,QAwiCjL,QAASiN,aAAY3M,OAAQ4M,mBACH,KAAlBA,gBAA4BA,cAAgB,EAChD,IAAqBC,MAAOlI,KAAKmI,IAAI,GAAIF,cAAgB,EACzD,OAAOjI,MAAKoI,MAAM/M,OAAS6M,MAAQA,KAOvC,QAASG,eAAcxF,MAAOyF,WAC1B,GACqBC,eADA7H,SAWrB,OATAmC,OAAM1H,QAAQ,SAAU+K,OACN,MAAVA,OACAqC,cAAgBA,eAAiBtO,OAAOuB,KAAK8M,WAC7CC,cAAcpN,QAAQ,SAAUM,MAAQiF,OAAOjF,MAAQhC,oBAAoBqC,cAG3EgF,WAA4B,OAAS,EAAOJ,UAG7CA,OAmMX,QAAS8H,6BAA4B7N,QAASqC,YAAaC,UAAWC,QAASuL,oBAAqBC,WAAYC,SAAUC,UAAWC,gBAAiBzB,cAAeC,eAAgBtM,QACjL,OACIwI,KAAM,EACN5I,QAASA,QACTqC,YAAaA,YACbyL,oBAAqBA,oBACrBxL,UAAWA,UACXyL,WAAYA,WACZxL,QAASA,QACTyL,SAAUA,SACVC,UAAWA,UACXC,gBAAiBA,gBACjBzB,cAAeA,cACfC,eAAgBA,eAChBtM,OAAQA,QA2GhB,QAAS+N,2BAA0BC,SAAUC,aAAcC,WACvD,MAAOF,UAASjC,KAAK,SAAUoC,IAAM,MAAOA,IAAGF,aAAcC,aAqDjE,QAASE,cAAaC,KAAMpC,KACxB,MAAO,IAAIqC,kBAAiBD,KAAMpC,KAmEtC,QAASsC,0BAAyBtM,YAAauM,QAW3C,MAAO,IAAIC,4BAA2BxM,aAPlCuG,KAAM,EACNkG,WAH+BlG,KAAM,EAAkBlC,SAAWG,QAAS,MAI3EkI,UAL6B,SAAUzM,UAAWC,SAAW,OAAO,IAMpEsE,QAAS,KACTmI,WAAY,EACZC,SAAU,GAEiDL,QAQnE,QAASM,mBAAkBtJ,IAAKuJ,KAAMC,MAC9BxJ,IAAIf,eAAesK,MACdvJ,IAAIf,eAAeuK,QACpBxJ,IAAIwJ,MAAQxJ,IAAIuJ,OAGfvJ,IAAIf,eAAeuK,QACxBxJ,IAAIuJ,MAAQvJ,IAAIwJ,OAglExB,QAASC,oBAAmB1M,IAAKC,IAAKE,OAClC,GAAqBwM,cACrB,IAAI3M,cAAeI,MAEf,GADAuM,cAAgB3M,IAAIK,IAAIJ,KACL,CACf,GAAI0M,cAAc5P,OAAQ,CACtB,GAAqB6P,OAAQD,cAAcjM,QAAQP,MACnDwM,eAAc5J,OAAO6J,MAAO,GAEJ,GAAxBD,cAAc5P,QACdiD,IAAI6M,OAAO5M,UAMnB,IADA0M,cAAgB3M,IAAIC,KACD,CACf,GAAI0M,cAAc5P,OAAQ,CACtB,GAAqB6P,OAAQD,cAAcjM,QAAQP,MACnDwM,eAAc5J,OAAO6J,MAAO,GAEJ,GAAxBD,cAAc5P,cACPiD,KAAIC,KAIvB,MAAO0M,eAMX,QAASG,uBAAsB3M,OAI3B,MAAgB,OAATA,MAAgBA,MAAQ,KAMnC,QAAS4M,eAAchH,MACnB,MAAOA,OAA6B,IAArBA,KAAe,SAMlC,QAASiH,qBAAoBjO,WACzB,MAAoB,SAAbA,WAAqC,QAAbA,UAOnC,QAASkO,cAAa5P,QAAS8C,OAC3B,GAAqB+M,UAAW7P,QAAQ6D,MAAMiM,OAE9C,OADA9P,SAAQ6D,MAAMiM,QAAmB,MAAThN,MAAgBA,MAAQ,OACzC+M,SAUX,QAASE,uBAAsBC,UAAWlQ,OAAQmQ,SAAUC,gBAAiBC,cACzE,GAAqBC,aACrBH,UAASzP,QAAQ,SAAUR,SAAW,MAAOoQ,WAAU/O,KAAKuO,aAAa5P,WACzE,IAAqBqQ,kBACrBH,iBAAgB1P,QAAQ,SAAU8P,MAAOtQ,SACrC,GAAqB+F,UACrBuK,OAAM9P,QAAQ,SAAUM,MACpB,GAAqBgC,OAAQiD,OAAOjF,MAAQhB,OAAOyQ,aAAavQ,QAASc,KAAMqP,aAG1ErN,QAAyB,GAAhBA,MAAMpD,SAChBM,QAAQwQ,cAAgBC,2BACxBJ,eAAehP,KAAKrB,YAG5BgQ,UAAU/M,IAAIjD,QAAS+F,SAI3B,IAAqB2K,GAAI,CAEzB,OADAT,UAASzP,QAAQ,SAAUR,SAAW,MAAO4P,cAAa5P,QAASoQ,UAAUM,QACtEL,eAOX,QAASM,cAAaC,MAAOC,OAYzB,QAASC,SAAQpI,MACb,IAAKA,KACD,MAAOqI,UACX,IAAqBC,MAAOC,aAAajO,IAAI0F,KAC7C,IAAIsI,KACA,MAAOA,KACX,IAAqBE,QAASxI,KAAKyI,UAcnC,OAXIH,MAFAI,QAAQ1G,IAAIwG,QAELA,OAEFG,QAAQ3G,IAAIwG,QAEVH,UAIAD,QAAQI,QAEnBD,aAAahO,IAAIyF,KAAMsI,MAChBA,KA/BX,GAAqBI,SAAU,GAAIrO,IAEnC,IADA6N,MAAMpQ,QAAQ,SAAUwQ,MAAQ,MAAOI,SAAQnO,IAAI+N,WAC/B,GAAhBH,MAAMnR,OACN,MAAO0R,QACX,IAAqBL,WAAY,EACZM,QAAU,GAAIC,KAAIT,OAClBI,aAAe,GAAIlO,IAiCxC,OANA8N,OAAMrQ,QAAQ,SAAUkI,MACpB,GAAqBsI,MAAOF,QAAQpI,KAChCsI,QAASD,WACUK,QAAQpO,IAAIgO,MAAQ3P,KAAKqH,QAG7C0I,QAQX,QAASG,UAASvR,QAASwR,WACvB,GAAIxR,QAAQyR,UACRzR,QAAQyR,UAAUC,IAAIF,eAErB,CACD,GAAqBG,SAAU3R,QAAQ4R,kBAClCD,WACDA,QAAU3R,QAAQ4R,uBAEtBD,QAAQH,YAAa,GAQ7B,QAASK,aAAY7R,QAASwR,WAC1B,GAAIxR,QAAQyR,UACRzR,QAAQyR,UAAUK,OAAON,eAExB,CACD,GAAqBG,SAAU3R,QAAQ4R,kBACnCD,gBACOA,SAAQH,YAU3B,QAASO,+BAA8BC,OAAQhS,QAASP,SACpDD,oBAAoBC,SAASuC,OAAO,WAAc,MAAOgQ,QAAOC,iBAAiBjS,WAMrF,QAASkS,qBAAoBzS,SACzB,GAAqB0S,gBAErB,OADAC,2BAA0B3S,QAAS0S,cAC5BA,aAOX,QAASC,2BAA0B3S,QAAS0S,cACxC,IAAK,GAAqBzB,GAAI,EAAGA,EAAIjR,QAAQC,OAAQgR,IAAK,CACtD,GAAqBjP,QAAShC,QAAQiR,EAClCjP,kBAAkB3C,qBAAoBc,sBACtCwS,0BAA0B3Q,OAAOhC,QAAS0S,cAG1CA,aAAa9Q,KAAsB,SAS/C,QAASgR,WAAUC,EAAGrT,GAClB,GAAqBsT,IAAKjT,OAAOuB,KAAKyR,GACjBE,GAAKlT,OAAOuB,KAAK5B,EACtC,IAAIsT,GAAG7S,QAAU8S,GAAG9S,OAChB,OAAO,CACX,KAAK,GAAqBgR,GAAI,EAAGA,EAAI6B,GAAG7S,OAAQgR,IAAK,CACjD,GAAqB5P,MAAOyR,GAAG7B,EAC/B,KAAKzR,EAAE4F,eAAe/D,OAASwR,EAAExR,QAAU7B,EAAE6B,MACzC,OAAO,EAEf,OAAO,EAQX,QAAS2R,wBAAuBzS,QAAS0S,oBAAqBC,sBAC1D,GAAqBC,WAAYD,qBAAqB3P,IAAIhD,QAC1D,KAAK4S,UACD,OAAO,CACX,IAAqBC,UAAWH,oBAAoB1P,IAAIhD,QAQxD,OAPI6S,UACAD,UAAUpS,QAAQ,SAAUiC,MAAQ,MAAO,UAAaiP,IAAIjP,QAG5DiQ,oBAAoBzP,IAAIjD,QAAS4S,WAErCD,qBAAqBnD,OAAOxP,UACrB,EAmgBX,QAAS8S,eAAc9S,QAASc,MAC5B,MAA0BiS,QAAOC,iBAAiBhT,SAAWc,MA4GjE,QAASmS,yBACL,MAA0B,mBAAZC,UAAwF,kBAAtD,SAA6B7T,UAAmB,QA59LpG,GAAID,eAAgBE,OAAO6T,iBACpBC,uBAA2BnN,QAAS,SAAUjH,EAAGC,GAAKD,EAAEoU,UAAYnU,IACvE,SAAUD,EAAGC,GAAK,IAAK,GAAIoU,KAAKpU,GAAOA,EAAE4F,eAAewO,KAAIrU,EAAEqU,GAAKpU,EAAEoU,KAQrEC,SAAWhU,OAAOiU,QAAU,SAAkBC,GAC9C,IAAK,GAAIC,GAAG/C,EAAI,EAAGgD,EAAIpL,UAAU5I,OAAQgR,EAAIgD,EAAGhD,IAAK,CACjD+C,EAAInL,UAAUoI,EACd,KAAK,GAAI2C,KAAKI,GAAOnU,OAAOD,UAAUwF,eAAe8O,KAAKF,EAAGJ,KAAIG,EAAEH,GAAKI,EAAEJ,IAE9E,MAAOG,IA8JPI,UAAY,SAAUC,KAAMC,MAAQ,OAAO,GAC3CC,SAAW,SAAU/T,QAASoL,UAC9B,OAAO,GAEP4I,OAAS,SAAUhU,QAASoL,SAAU6I,OACtC,SAEJ,IAAsB,mBAAXf,SAAwB,CAG/B,GADAU,UAAY,SAAUC,KAAMC,MAAQ,MAAyBD,MAAKK,SAASJ,OACvEZ,QAAQ7T,UAAU+E,QAClB2P,SAAW,SAAU/T,QAASoL,UAAY,MAAOpL,SAAQoE,QAAQgH,eAEhE,CACD,GAAqB+I,OAA0BjB,QAAiB,UAC3CkB,KAAOD,MAAME,iBAAmBF,MAAMG,oBAAsBH,MAAMI,mBACnFJ,MAAMK,kBAAoBL,MAAMM,qBAChCL,QACAL,SAAW,SAAU/T,QAASoL,UAAY,MAAOgJ,MAAKM,MAAM1U,SAAUoL,aAG9E4I,OAAS,SAAUhU,QAASoL,SAAU6I,OAClC,GAAqBU,WACrB,IAAIV,MACAU,QAAQtT,KAAKqT,MAAMC,QAAS3U,QAAQ4U,iBAAiBxJ,eAEpD,CACD,GAAqByJ,KAAM7U,QAAQ8U,cAAc1J,SAC7CyJ,MACAF,QAAQtT,KAAKwT,KAGrB,MAAOF,UAYf,GAAIjR,cAAe,KACfE,YAAa,EA6BbmR,eAAiBhB,SACjBiB,gBAAkBpB,UAClBqB,YAAcjB,OASdkB,oBAAqC,WACrC,QAASA,wBAuFT,MAjFAA,qBAAoB7V,UAAUoE,sBAI9B,SAAU3C,MAAQ,MAAO2C,uBAAsB3C,OAM/CoU,oBAAoB7V,UAAU0V,eAK9B,SAAU/U,QAASoL,UACf,MAAO2J,gBAAe/U,QAASoL,WAOnC8J,oBAAoB7V,UAAU2V,gBAK9B,SAAUnB,KAAMC,MAAQ,MAAOkB,iBAAgBnB,KAAMC,OAOrDoB,oBAAoB7V,UAAU8V,MAM9B,SAAUnV,QAASoL,SAAU6I,OACzB,MAAOgB,aAAYjV,QAASoL,SAAU6I,QAQ1CiB,oBAAoB7V,UAAUkR,aAM9B,SAAUvQ,QAASc,KAAM+B,cACrB,MAAOA,eAAgB,IAW3BqS,oBAAoB7V,UAAU+V,QAS9B,SAAUpV,QAASC,UAAW+E,SAAUE,MAAOC,OAAQkQ,iBAEnD,WADwB,KAApBA,kBAA8BA,oBAC3B,GAAIvW,qBAAoBa,qBAE5BuV,uBAMPI,gBAAiC,WACjC,QAASA,oBAGT,MADAA,iBAAgBC,KAAO,GAAIL,qBACpBI,mBAOP7Q,WAAa,IAQbiH,oBAAsB,cAEtBC,sBAAwB,gBAwLxBxE,YAAc,GAAIqO,QAAOC,oBAAmE,KAwD5FtN,iBAAmB,gBAwEnBiC,UAAY,IAuEZK,oBAAsB,GAAI6G,MAAK,OAAQ,MACvC3G,qBAAuB,GAAI2G,MAAK,QAAS,MA0BzC9F,WAAa,QACbC,iBAAmB,GAAI+J,QAAO,KAAOhK,WAAa,OAAQ,KAW1DP,2BAA4C,WAC5C,QAASA,4BAA2ByK,SAChC7W,KAAK6W,QAAUA,QA0gBnB,MAngBAzK,4BAA2B5L,UAAU6L,MAKrC,SAAUF,SAAU5K,QAChB,GAAqBuI,SAAU,GAAIgN,4BAA2BvV,OAE9D,OADAvB,MAAK+W,8BAA8BjN,SACVH,aAAa3J,KAAM4H,wBAAwBuE,UAAWrC,UAMnFsC,2BAA2B5L,UAAUuW,8BAIrC,SAAUjN,SACNA,QAAQkN,qBA7BI,GA8BZlN,QAAQmN,mBACRnN,QAAQmN,gBA/BI,OAgCZnN,QAAQoN,YAAc,GAO1B9K,2BAA2B5L,UAAUwJ,aAKrC,SAAUmC,SAAUrC,SAChB,GAAIqN,OAAQnX,KACSmQ,WAAarG,QAAQqG,WAAa,EAClCC,SAAWtG,QAAQsG,SAAW,EAC9BL,UACAqH,cAyBrB,OAxB+B,KAA3BjL,SAASyD,KAAK1K,OAAO,IACrB4E,QAAQvI,OAAOiB,KAAK,wFAExB2J,SAASkL,YAAY1V,QAAQ,SAAU2V,KAEnC,GADAH,MAAMJ,8BAA8BjN,SACpB,GAAZwN,IAAIvN,KAAuB,CAC3B,GAAqBwN,YAA8B,IAC9BC,OAASD,WAAW3H,IACzC4H,QAAOxM,MAAM,WAAWrJ,QAAQ,SAAUkT,GACtC0C,WAAW3H,KAAOiF,EAClB9E,OAAOvN,KAAK2U,MAAMlN,WAAWsN,WAAYzN,YAE7CyN,WAAW3H,KAAO4H,WAEjB,IAAgB,GAAZF,IAAIvN,KAA4B,CACrC,GAAqB0N,YAAaN,MAAMjN,gBAAiC,IAAOJ,QAChFqG,aAAcsH,WAAWtH,WACzBC,UAAYqH,WAAWrH,SACvBgH,YAAY5U,KAAKiV,gBAGjB3N,SAAQvI,OAAOiB,KAAK,8EAIxBuH,KAAM,EACN6F,KAAMzD,SAASyD,KAAMG,OAAQA,OAAQqH,YAAaA,YAAajH,WAAYA,WAAYC,SAAUA,SACjGpI,QAAS,OAQjBoE,2BAA2B5L,UAAUyJ,WAKrC,SAAUkC,SAAUrC,SAChB,GAAqB4N,UAAW1X,KAAKuK,WAAW4B,SAASjF,OAAQ4C,SAC5C6N,UAAaxL,SAASnE,SAAWmE,SAASnE,QAAQC,QAAW,IAClF,IAAIyP,SAASE,sBAAuB,CAChC,GAAqBC,eAAgB,GAAIpF,KACpBqF,SAAWH,aAahC,IAZAD,SAASxQ,OAAOvF,QAAQ,SAAUsC,OAC9B,GAAIiJ,SAASjJ,OAAQ,CACjB,GAAqB8T,aAA+B,KACpDtX,QAAOuB,KAAK+V,aAAapW,QAAQ,SAAUM,MACvCiG,mBAAmB6P,YAAY9V,OAAON,QAAQ,SAAUqW,KAC/CF,SAAS9R,eAAegS,MACzBH,cAAchF,IAAImF,YAMlCH,cAAcI,KAAM,CACpB,GAAqBC,gBAAiBnP,gBAAgB8O,cAAcM,SACpErO,SAAQvI,OAAOiB,KAAK,UAAa2J,SAASyD,KAAO,iFAAoFsI,eAAexV,KAAK,QAGjK,OACIqH,KAAM,EACN6F,KAAMzD,SAASyD,KACf5K,MAAO0S,SACP1P,QAAS2P,WAAc1P,OAAQ0P,WAAc,OAQrDvL,2BAA2B5L,UAAU0J,gBAKrC,SAAUiC,SAAUrC,SAChBA,QAAQqG,WAAa,EACrBrG,QAAQsG,SAAW,CACnB,IAAqBH,WAAYtG,aAAa3J,KAAM4H,wBAAwBuE,SAAS8D,WAAYnG,QAEjG,QACIC,KAAM,EACNmG,SAH4BrF,oBAAoBsB,SAASiM,KAAMtO,QAAQvI,QAIvE0O,UAAWA,UACXE,WAAYrG,QAAQqG,WACpBC,SAAUtG,QAAQsG,SAClBpI,QAAS0F,0BAA0BvB,SAASnE,WAQpDoE,2BAA2B5L,UAAU2J,cAKrC,SAAUgC,SAAUrC,SAChB,GAAIqN,OAAQnX,IACZ,QACI+J,KAAM,EACNlC,MAAOsE,SAAStE,MAAM/D,IAAI,SAAU8Q,GAAK,MAAOjL,cAAawN,MAAOvC,EAAG9K,WACvE9B,QAAS0F,0BAA0BvB,SAASnE,WAQpDoE,2BAA2B5L,UAAU4J,WAKrC,SAAU+B,SAAUrC,SAChB,GAAIqN,OAAQnX,KACSkX,YAAcpN,QAAQoN,YACtBmB,aAAe,EACfxQ,MAAQsE,SAAStE,MAAM/D,IAAI,SAAUwU,MACtDxO,QAAQoN,YAAcA,WACtB,IAAqBqB,UAAW5O,aAAawN,MAAOmB,KAAMxO,QAE1D,OADAuO,cAAe7R,KAAKgS,IAAIH,aAAcvO,QAAQoN,aACvCqB,UAGX,OADAzO,SAAQoN,YAAcmB,cAElBtO,KAAM,EACNlC,MAAOA,MACPG,QAAS0F,0BAA0BvB,SAASnE,WAQpDoE,2BAA2B5L,UAAU6J,aAKrC,SAAU8B,SAAUrC,SAChB,GAAqB2O,WAAYtL,mBAAmBhB,SAASrG,QAASgE,QAAQvI,OAC9EuI,SAAQ4O,sBAAwBD,SAChC,IAAqBf,UACAiB,cAAgBxM,SAASjF,OAASiF,SAASjF,OAASjH,oBAAoB+E,SAC7F,IAA0B,GAAtB2T,cAAc5O,KACd2N,SAAW1X,KAAKsK,eAAgC,cAAiBR,aAEhE,CACD,GAAqB8O,iBAAoCzM,SAAe,OACnD0M,SAAU,CAC/B,KAAKD,gBAAiB,CAClBC,SAAU,CACV,IAAqBC,gBACjBL,WAAUnS,SACVwS,aAAqB,OAAIL,UAAUnS,QAEvCsS,gBAAkB3Y,oBAAoB+E,MAAM8T,cAEhDhP,QAAQoN,aAAeuB,UAAUtS,SAAWsS,UAAUpS,KACtD,IAAqB0S,WAAY/Y,KAAKuK,WAAWqO,gBAAiB9O,QAClEiP,WAAUC,YAAcH,QACxBnB,SAAWqB,UAGf,MADAjP,SAAQ4O,sBAAwB,MAE5B3O,KAAM,EACNjE,QAAS2S,UACTzT,MAAO0S,SACP1P,QAAS,OAQjBoE,2BAA2B5L,UAAU+J,WAKrC,SAAU4B,SAAUrC,SAChB,GAAqB0D,KAAMxN,KAAKiZ,cAAc9M,SAAUrC,QAExD,OADA9J,MAAKkZ,kBAAkB1L,IAAK1D,SACrB0D,KAOXpB,2BAA2B5L,UAAUyY,cAKrC,SAAU9M,SAAUrC,SAChB,GAAqB5C,UACjBE,OAAMC,QAAQ8E,SAASjF,QACJiF,SAAgB,OAAExK,QAAQ,SAAUsL,YAC1B,gBAAdA,YACHA,YAAchN,oBAAoBqC,WAClC4E,OAAO1E,KAAsB,YAG7BsH,QAAQvI,OAAOiB,KAAK,mCAAqCyK,WAAa,oBAI1E/F,OAAO1E,KAAsB,cAKrC0E,OAAO1E,KAAK2J,SAASjF,OAEzB,IAAqB0Q,wBAAwB,EACxBuB,gBAAkB,IAoBvC,OAnBAjS,QAAOvF,QAAQ,SAAUyX,WACrB,GAAIlM,SAASkM,WAAY,CACrB,GAAqBC,UAA4B,UAC5B/S,OAAS+S,SAAiB,MAK/C,IAJI/S,SACA6S,gBAAmC,aAC5BE,UAAiB,SAEvBzB,sBACD,IAAK,GAAqB3V,QAAQoX,UAAU,CACxC,GAAqBpV,OAAQoV,SAASpX,KACtC,IAAIgC,MAAMoE,WAAW7D,QA/sBf,OA+sBmD,EAAG,CACxDoT,uBAAwB,CACxB,aAOhB7N,KAAM,EACN7C,OAAQA,OACRZ,OAAQ6S,gBACRtX,OAAQsK,SAAStK,OAAQ+V,sBAAuBA,sBAChD5P,QAAS,OAQjBoE,2BAA2B5L,UAAU0Y,kBAKrC,SAAU1L,IAAK1D,SACX,GAAIqN,OAAQnX,KACS8F,QAAUgE,QAAQ4O,sBAClBY,QAAUxP,QAAQoN,YAClBqC,UAAYzP,QAAQoN,WACrCpR,UAAWyT,UAAY,IACvBA,WAAazT,QAAQK,SAAWL,QAAQO,OAE5CmH,IAAItG,OAAOvF,QAAQ,SAAU6X,OACL,gBAATA,QAEX/Y,OAAOuB,KAAKwX,OAAO7X,QAAQ,SAAUM,MACjC,IAAKkV,MAAMN,QAAQjS,sBAAsB3C,MAErC,WADA6H,SAAQvI,OAAOiB,KAAK,oCAAuCP,KAAO,mDAGtE,IAAqBgV,iBAAkBnN,QAAQmN,gBAAmCnN,QAA6B,sBAC1F2P,eAAiBxC,gBAAgBhV,MACjCyX,sBAAuB,CACxCD,kBACIF,WAAaD,SAAWC,WAAaE,eAAeF,WACpDD,SAAWG,eAAeH,UAC1BxP,QAAQvI,OAAOiB,KAAK,qBAAwBP,KAAO,uCAA2CwX,eAAeF,UAAY,YAAgBE,eAAeH,QAAU,4EAAgFC,UAAY,YAAgBD,QAAU,OACxRI,sBAAuB,GAK3BH,UAAYE,eAAeF,WAE3BG,uBACAzC,gBAAgBhV,OAAUsX,UAAWA,UAAWD,QAASA,UAEzDxP,QAAQ9B,SACRD,oBAAoByR,MAAMvX,MAAO6H,QAAQ9B,QAAS8B,QAAQvI,aAU1E6K,2BAA2B5L,UAAU8J,eAKrC,SAAU6B,SAAUrC,SAChB,GAAIqN,OAAQnX,KACSwN,KAAQzD,KAAM,EAAmB7C,UAAYc,QAAS,KAC3E,KAAK8B,QAAQ4O,sBAET,MADA5O,SAAQvI,OAAOiB,KAAK,4DACbgL,GAEX,IACqBmM,2BAA4B,EAC5BC,WACAC,mBAAoB,EACpBC,qBAAsB,EACtBrY,eAAiB,EACjBL,UAAY+K,SAAStE,MAAM/D,IAAI,SAAUoD,QAC1D,GAAqB6S,UAAW5C,MAAM8B,cAAc/R,OAAQ4C,SACvCkQ,UAA+B,MAAnBD,SAASlY,OAAiBkY,SAASlY,OAASmL,cAAc+M,SAAS7S,QAC/ErF,OAAS,CAS9B,OARiB,OAAbmY,YACAL,4BACA9X,OAASkY,SAASlY,OAASmY,WAE/BF,oBAAsBA,qBAAuBjY,OAAS,GAAKA,OAAS,EACpEgY,kBAAoBA,mBAAqBhY,OAASJ,eAClDA,eAAiBI,OACjB+X,QAAQpX,KAAKX,QACNkY,UAEPD,sBACAhQ,QAAQvI,OAAOiB,KAAK,+DAEpBqX,mBACA/P,QAAQvI,OAAOiB,KAAK,uDAExB,IAAqB3B,QAASsL,SAAStE,MAAMhH,OACxBoZ,gBAAkB,CACnCN,2BAA4B,GAAKA,0BAA4B9Y,OAC7DiJ,QAAQvI,OAAOiB,KAAK,yEAEc,GAA7BmX,4BACLM,gBAhCuC,GAgCEpZ,OAAS,GAEtD,IAAqBqZ,OAAQrZ,OAAS,EACjBqW,YAAcpN,QAAQoN,YACtBwB,sBAA2C5O,QAA8B,sBACzEqQ,gBAAkBzB,sBAAsBvS,QAU7D,OATA/E,WAAUO,QAAQ,SAAUC,GAAIiQ,GAC5B,GAAqBhQ,QAASoY,gBAAkB,EAAKpI,GAAKqI,MAAQ,EAAKD,gBAAkBpI,EAAM+H,QAAQ/H,GAClFuI,sBAAwBvY,OAASsY,eACtDrQ,SAAQoN,YAAcA,YAAcwB,sBAAsBrS,MAAQ+T,sBAClE1B,sBAAsBvS,SAAWiU,sBACjCjD,MAAM+B,kBAAkBtX,GAAIkI,SAC5BlI,GAAGC,OAASA,OACZ2L,IAAItG,OAAO1E,KAAKZ,MAEb4L,KAOXpB,2BAA2B5L,UAAUgK,eAKrC,SAAU2B,SAAUrC,SAChB,OACIC,KAAM,EACNkG,UAAWtG,aAAa3J,KAAM4H,wBAAwBuE,SAAS8D,WAAYnG,SAC3E9B,QAAS0F,0BAA0BvB,SAASnE,WAQpDoE,2BAA2B5L,UAAUiK,kBAKrC,SAAU0B,SAAUrC,SAEhB,MADAA,SAAQsG,YAEJrG,KAAM,EACN/B,QAAS0F,0BAA0BvB,SAASnE,WAQpDoE,2BAA2B5L,UAAUkK,gBAKrC,SAAUyB,SAAUrC,SAChB,OACIC,KAAM,GACNkG,UAAWjQ,KAAKwK,eAAe2B,SAAS8D,UAAWnG,SACnD9B,QAAS0F,0BAA0BvB,SAASnE,WAQpDoE,2BAA2B5L,UAAUmK,WAKrC,SAAUwB,SAAUrC,SAChB,GAAqBuQ,gBAAoCvQ,QAA6B,qBACjE9B,QAA6BmE,SAASnE,WAC3D8B,SAAQqG,aACRrG,QAAQwQ,aAAenO,QACvB,IAAIoO,IAAKjO,kBAAkBH,SAASI,UAAWA,SAAWgO,GAAG,GAAIC,YAAcD,GAAG,EAClFzQ,SAAQkN,qBACJqD,eAAexZ,OAAUwZ,eAAiB,IAAM9N,SAAYA,SAChE1I,gBAAgBiG,QAAQmN,gBAAiBnN,QAAQkN,wBACjD,IAAqB/G,WAAYtG,aAAa3J,KAAM4H,wBAAwBuE,SAAS8D,WAAYnG,QAGjG,OAFAA,SAAQwQ,aAAe,KACvBxQ,QAAQkN,qBAAuBqD,gBAE3BtQ,KAAM,GACNwC,SAAUA,SACV2N,MAAOlS,QAAQkS,OAAS,EACxBO,WAAYzS,QAAQyS,SAAUD,YAAaA,YAAavK,UAAWA,UACnEyK,iBAAkBvO,SAASI,SAC3BvE,QAAS0F,0BAA0BvB,SAASnE,WAQpDoE,2BAA2B5L,UAAUoK,aAKrC,SAAUuB,SAAUrC,SACXA,QAAQwQ,cACTxQ,QAAQvI,OAAOiB,KAAK,+CAExB,IAAqBsD,SAA+B,SAArBqG,SAASrG,SAClCK,SAAU,EAAGE,MAAO,EAAGC,OAAQ,QACjCT,cAAcsG,SAASrG,QAASgE,QAAQvI,QAAQ,EACpD,QACIwI,KAAM,GACNkG,UAAWtG,aAAa3J,KAAM4H,wBAAwBuE,SAAS8D,WAAYnG,SAAUhE,QAASA,QAC9FkC,QAAS,OAGVoE,8BAwBP0K,2BAA4C,WAC5C,QAASA,4BAA2BvV,QAChCvB,KAAKuB,OAASA,OACdvB,KAAKmQ,WAAa,EAClBnQ,KAAKoQ,SAAW,EAChBpQ,KAAK2a,kBAAoB,KACzB3a,KAAKsa,aAAe,KACpBta,KAAKgX,qBAAuB,KAC5BhX,KAAK0Y,sBAAwB,KAC7B1Y,KAAKkX,YAAc,EACnBlX,KAAKiX,mBACLjX,KAAKgI,QAAU,KAEnB,MAAO8O,+BA0HP8D,sBAAuC,WACvC,QAASA,yBACL5a,KAAK6a,KAAO,GAAI3W,KAqDpB,MA/CA0W,uBAAsBpa,UAAUsa,QAIhC,SAAU3Z,SACN,GAAqB4Z,cAAe/a,KAAK6a,KAAK1W,IAAIhD,QAOlD,OANI4Z,cACA/a,KAAK6a,KAAKlK,OAAOxP,SAGjB4Z,gBAEGA,cAOXH,sBAAsBpa,UAAUwa,OAKhC,SAAU7Z,QAAS4Z,cACf,GAAqBE,sBAAuBjb,KAAK6a,KAAK1W,IAAIhD,QACrD8Z,uBACDjb,KAAK6a,KAAKzW,IAAIjD,QAAS8Z,yBAE3BA,qBAAqBzY,KAAKqT,MAAMoF,qBAAsBF,eAM1DH,sBAAsBpa,UAAUqL,IAIhC,SAAU1K,SAAW,MAAOnB,MAAK6a,KAAKhP,IAAI1K,UAI1CyZ,sBAAsBpa,UAAU0a,MAGhC,WAAclb,KAAK6a,KAAKK,SACjBN,yBASPO,kBAAoB,GAAIxE,QADV,SAC8B,KAE5CyE,kBAAoB,GAAIzE,QADV,SAC8B,KAoB5CrI,gCAAiD,WACjD,QAASA,oCAubT,MAxaAA,iCAAgC9N,UAAU+N,eAa1C,SAAUtN,OAAQ+M,YAAaR,IAAKS,eAAgBC,eAAgBC,eAAgBC,YAAapG,QAASqG,gBAAiB9M,YACxG,KAAXA,SAAqBA,WACzB8M,gBAAkBA,iBAAmB,GAAIuM,sBACzC,IAAqB9Q,SAAU,GAAIuR,0BAAyBpa,OAAQ+M,YAAaK,gBAAiBJ,eAAgBC,eAAgB3M;qHAClIuI,SAAQ9B,QAAUA,QAClB8B,QAAQwR,gBAAgB9T,WAAW2G,gBAAiB,KAAMrE,QAAQvI,OAAQyG,SAC1E2B,aAAa3J,KAAMwN,IAAK1D,QAExB,IAAqBsF,WAAYtF,QAAQsF,UAAUmM,OAAO,SAAUC,UAAY,MAAOA,UAASC,qBAChG,IAAIrM,UAAUvO,QAAUJ,OAAOuB,KAAKoM,aAAavN,OAAQ,CACrD,GAAqB6a,IAAKtM,UAAUA,UAAUvO,OAAS,EAClD6a,IAAGC,2BACJD,GAAGlU,WAAW4G,aAAc,KAAMtE,QAAQvI,OAAQyG,SAG1D,MAAOoH,WAAUvO,OAASuO,UAAUtL,IAAI,SAAU0X,UAAY,MAAOA,UAASjN,oBACzEZ,0BAA0BK,qBAAyB,EAAG,EAAG,IAAI,KAOtEM,gCAAgC9N,UAAUwJ,aAK1C,SAAUwD,IAAK1D,WAQfwE,gCAAgC9N,UAAUyJ,WAK1C,SAAUuD,IAAK1D,WAQfwE,gCAAgC9N,UAAU0J,gBAK1C,SAAUsD,IAAK1D,WAQfwE,gCAAgC9N,UAAUiK,kBAK1C,SAAU+C,IAAK1D,SACX,GAAqB8R,qBAAsB9R,QAAQuE,gBAAgByM,QAAQhR,QAAQ3I,QACnF,IAAIya,oBAAqB,CACrB,GAAqBC,cAAe/R,QAAQgS,iBAAiBtO,IAAIxF,SAC5CuR,UAAYzP,QAAQwR,gBAAgBpE,YACpCoC,QAAUtZ,KAAK+b,sBAAsBH,oBAAqBC,aAAgCA,aAAoB,QAC/HtC,YAAaD,SAGbxP,QAAQkS,yBAAyB1C,SAGzCxP,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAUkK,gBAK1C,SAAU8C,IAAK1D,SACX,GAAqB+R,cAAe/R,QAAQgS,iBAAiBtO,IAAIxF,QACjE6T,cAAaG,2BACbhc,KAAKwK,eAAegD,IAAIyC,UAAW4L,cACnC/R,QAAQkS,yBAAyBH,aAAaP,gBAAgBpE,aAC9DpN,QAAQmS,aAAezO,KAQ3Bc,gCAAgC9N,UAAUub,sBAM1C,SAAUhB,aAAcjR,QAAS9B,SAC7B,GAAqBuR,WAAYzP,QAAQwR,gBAAgBpE,YACpCmB,aAAekB,UAGfpT,SAA+B,MAApB6B,QAAQ7B,SAAmBb,mBAAmB0C,QAAQ7B,UAAY,KAC7EE,MAAyB,MAAjB2B,QAAQ3B,MAAgBf,mBAAmB0C,QAAQ3B,OAAS,IAQzF,OAPiB,KAAbF,UACA4U,aAAapZ,QAAQ,SAAUua,aAC3B,GAAqBC,oBAAqBrS,QAAQsS,4BAA4BF,YAAa/V,SAAUE,MACrGgS,cACI7R,KAAKgS,IAAIH,aAAc8D,mBAAmBhW,SAAWgW,mBAAmB9V,SAG7EgS,cAOX/J,gCAAgC9N,UAAUgK,eAK1C,SAAUgD,IAAK1D,SACXA,QAAQuS,cAAc7O,IAAIxF,SAAS,GACnC2B,aAAa3J,KAAMwN,IAAIyC,UAAWnG,SAClCA,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAU2J,cAK1C,SAAUqD,IAAK1D,SACX,GAAIqN,OAAQnX,KACSsc,gBAAkBxS,QAAQwS,gBAC1BC,IAAMzS,QACN9B,QAAUwF,IAAIxF,OACnC,IAAIA,UAAYA,QAAQC,QAAUD,QAAQ3B,SACtCkW,IAAMzS,QAAQgS,iBAAiB9T,SAC/BuU,IAAIP,2BACiB,MAAjBhU,QAAQ3B,OAAe,CACM,GAAzBkW,IAAIN,aAAalS,OACjBwS,IAAIjB,gBAAgBkB,wBACpBD,IAAIN,aAAeQ,2BAEvB,IAAqBpW,OAAQf,mBAAmB0C,QAAQ3B,MACxDkW,KAAIG,cAAcrW,OAGtBmH,IAAI3F,MAAMhH,SACV2M,IAAI3F,MAAMlG,QAAQ,SAAUiT,GAAK,MAAOjL,cAAawN,MAAOvC,EAAG2H,OAE/DA,IAAIjB,gBAAgBqB,wBAIhBJ,IAAID,gBAAkBA,iBACtBC,IAAIP,4BAGZlS,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAU4J,WAK1C,SAAUoD,IAAK1D,SACX,GAAIqN,OAAQnX,KACS4c,kBACAvE,aAAevO,QAAQwR,gBAAgBpE,YACvC7Q,MAAQmH,IAAIxF,SAAWwF,IAAIxF,QAAQ3B,MAAQf,mBAAmBkI,IAAIxF,QAAQ3B,OAAS,CACxGmH,KAAI3F,MAAMlG,QAAQ,SAAUiT,GACxB,GAAqBiH,cAAe/R,QAAQgS,iBAAiBtO,IAAIxF,QAC7D3B,QACAwV,aAAaa,cAAcrW,OAE/BsD,aAAawN,MAAOvC,EAAGiH,cACvBxD,aAAe7R,KAAKgS,IAAIH,aAAcwD,aAAaP,gBAAgBpE,aACnE0F,eAAepa,KAAKqZ,aAAaP,mBAKrCsB,eAAejb,QAAQ,SAAU6Z,UAAY,MAAO1R,SAAQwR,gBAAgBuB,6BAA6BrB,YACzG1R,QAAQkS,yBAAyB3D,cACjCvO,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAUsc,aAK1C,SAAUtP,IAAK1D,SACX,GAAI,IAAyB2D,QAAS,CAClC,GAAqBJ,UAAW,IAAyBA,QAEzD,OAAOxH,eAD4BiE,QAAQ7B,OAASQ,kBAAkB4E,SAAUvD,QAAQ7B,OAAQ6B,QAAQvI,QAAU8L,SAChFvD,QAAQvI,QAG1C,OAAS4E,SAAUqH,IAAIrH,SAAUE,MAAOmH,IAAInH,MAAOC,OAAQkH,IAAIlH,SAQvEgI,gCAAgC9N,UAAU6J,aAK1C,SAAUmD,IAAK1D,SACX,GAAqBhE,SAAUgE,QAAQ4O,sBAAwB1Y,KAAK8c,aAAatP,IAAI1H,QAASgE,SACzE0R,SAAW1R,QAAQwR,eACpCxV,SAAQO,QACRyD,QAAQiT,cAAcjX,QAAQO,OAC9BmV,SAASgB,wBAEb,IAAqBzC,UAAWvM,IAAIxI,KACf,IAAjB+U,SAAShQ,KACT/J,KAAKsK,eAAeyP,SAAUjQ,UAG9BA,QAAQiT,cAAcjX,QAAQK,UAC9BnG,KAAKuK,WAA4B,SAAYT,SAC7C0R,SAASmB,yBAEb7S,QAAQ4O,sBAAwB,KAChC5O,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAU+J,WAK1C,SAAUiD,IAAK1D,SACX,GAAqB0R,UAAW1R,QAAQwR,gBACnBxV,QAA6BgE,QAA8B,uBAG3EhE,SAAW0V,SAASwB,4BAA4Bnc,QACjD2a,SAASyB,cAEb,IAAqB3W,QAAUR,SAAWA,QAAQQ,QAAWkH,IAAIlH,MAC7DkH,KAAIwL,YACJwC,SAAS0B,eAAe5W,QAGxBkV,SAAShU,UAAUgG,IAAItG,OAAQZ,OAAQwD,QAAQvI,OAAQuI,QAAQ9B,SAEnE8B,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAU8J,eAK1C,SAAUkD,IAAK1D,SACX,GAAqB4O,uBAA2C5O,QAA8B,sBACzEyP,UAAgCzP,QAAyB,gBAAE3D,SAC3DA,SAAWuS,sBAAsBvS,SACjC0V,aAAe/R,QAAQgS,mBACvBqB,cAAgBtB,aAAaP,eAClD6B,eAAc7W,OAASoS,sBAAsBpS,OAC7CkH,IAAItG,OAAOvF,QAAQ,SAAU2W,MACzB,GAAqBzW,QAASyW,KAAKzW,QAAU,CAC7Csb,eAAcC,YAAYvb,OAASsE,UACnCgX,cAAc3V,UAAU8Q,KAAKpR,OAAQoR,KAAKhS,OAAQwD,QAAQvI,OAAQuI,QAAQ9B,SAC1EmV,cAAcR,0BAIlB7S,QAAQwR,gBAAgBuB,6BAA6BM,eAGrDrT,QAAQkS,yBAAyBzC,UAAYpT,UAC7C2D,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAUmK,WAK1C,SAAU6C,IAAK1D,SACX,GAAIqN,OAAQnX,KAGSuZ,UAAYzP,QAAQwR,gBAAgBpE,YACpClP,QAA6BwF,IAAIxF,YACjC3B,MAAQ2B,QAAQ3B,MAAQf,mBAAmB0C,QAAQ3B,OAAS,CAC7EA,SAAwC,IAA9ByD,QAAQmS,aAAalS,MACjB,GAAbwP,WAAkBzP,QAAQwR,gBAAgB0B,4BAA4Bnc,UACvEiJ,QAAQwR,gBAAgBkB,wBACxB1S,QAAQmS,aAAeQ,2BAE3B,IAAqBpE,cAAekB,UACf8D,KAAOvT,QAAQsM,YAAY5I,IAAIjB,SAAUiB,IAAIkN,iBAAkBlN,IAAI0M,MAAO1M,IAAIgN,cAAaxS,QAAQyS,SAAyB3Q,QAAQvI,OACzJuI,SAAQwT,kBAAoBD,KAAKxc,MACjC,IAAqB0c,qBAAsB,IAC3CF,MAAK1b,QAAQ,SAAUR,QAAS0Q,GAC5B/H,QAAQ0T,kBAAoB3L,CAC5B,IAAqBgK,cAAe/R,QAAQgS,iBAAiBtO,IAAIxF,QAAS7G,QACtEkF,QACAwV,aAAaa,cAAcrW,OAE3BlF,UAAY2I,QAAQ3I,UACpBoc,oBAAsB1B,aAAaP,iBAEvC3R,aAAawN,MAAO3J,IAAIyC,UAAW4L,cAInCA,aAAaP,gBAAgBqB,uBAC7B,IAAqBrD,SAAUuC,aAAaP,gBAAgBpE,WAC5DmB,cAAe7R,KAAKgS,IAAIH,aAAciB,WAE1CxP,QAAQ0T,kBAAoB,EAC5B1T,QAAQwT,kBAAoB,EAC5BxT,QAAQkS,yBAAyB3D,cAC7BkF,sBACAzT,QAAQwR,gBAAgBuB,6BAA6BU,qBACrDzT,QAAQwR,gBAAgBkB,yBAE5B1S,QAAQmS,aAAezO,KAO3Bc,gCAAgC9N,UAAUoK,aAK1C,SAAU4C,IAAK1D,SACX,GAAqB2T,eAAmC3T,QAAsB,cACzD4R,GAAK5R,QAAQwR,gBACbxV,QAAU0H,IAAI1H,QACdK,SAAWK,KAAKkX,IAAI5X,QAAQK,UAC5BwX,QAAUxX,UAAY2D,QAAQwT,kBAAoB,GAClDjX,MAAQF,SAAW2D,QAAQ0T,iBAEhD,QAD0C1X,QAAQK,SAAW,EAAI,UAAYL,QAAQQ,QAEjF,IAAK,UACDD,MAAQsX,QAAUtX,KAClB,MACJ,KAAK,OACDA,MAAQoX,cAAcG,mBAG9B,GAAqBpC,UAAW1R,QAAQwR,eACpCjV,QACAmV,SAASkB,cAAcrW,MAE3B,IAAqBwX,cAAerC,SAAStE,WAC7CvN,cAAa3J,KAAMwN,IAAIyC,UAAWnG,SAClCA,QAAQmS,aAAezO,IAKvBiQ,cAAcG,mBACTlC,GAAGxE,YAAc2G,cAAiBnC,GAAGnC,UAAYkE,cAAcnC,gBAAgB/B,YAEjFjL,mCAEPmO,8BACApB,yBAA0C,WAC1C,QAASA,0BAAyBxE,QAAS1V,QAASkN,gBAAiByP,gBAAiBC,gBAAiBxc,OAAQ6N,UAAW4O,iBACtHhe,KAAK6W,QAAUA,QACf7W,KAAKmB,QAAUA,QACfnB,KAAKqO,gBAAkBA,gBACvBrO,KAAK8d,gBAAkBA,gBACvB9d,KAAK+d,gBAAkBA,gBACvB/d,KAAKuB,OAASA,OACdvB,KAAKoP,UAAYA,UACjBpP,KAAKyd,cAAgB,KACrBzd,KAAK0Y,sBAAwB,KAC7B1Y,KAAKic,aAAeQ,2BACpBzc,KAAKsc,gBAAkB,EACvBtc,KAAKgI,WACLhI,KAAKwd,kBAAoB,EACzBxd,KAAKsd,kBAAoB,EACzBtd,KAAK4d,mBAAqB,EAC1B5d,KAAKsb,gBAAkB0C,iBAAmB,GAAIC,iBAAgBje,KAAK6W,QAAS1V,QAAS,GACrFiO,UAAU5M,KAAKxC,KAAKsb,iBA8LxB,MA5LA7a,QAAOyd,eAAe7C,yBAAyB7a,UAAW,UACtD2D,IAGA,WAAc,MAAOnE,MAAKgI,QAAQC,QAClCkW,YAAY,EACZC,cAAc,IAOlB/C,yBAAyB7a,UAAU6b,cAKnC,SAAUrU,QAASqW,cACf,GAAIlH,OAAQnX,IACZ,IAAKgI,QAAL,CAEA,GAAqBsW,YAA8B,QAC9BC,gBAAkBve,KAAKgI,OAEjB,OAAvBsW,WAAWnY,WACX,gBAAqCA,SAAWb,mBAAmBgZ,WAAWnY,WAE1D,MAApBmY,WAAWjY,QACXkY,gBAAgBlY,MAAQf,mBAAmBgZ,WAAWjY,OAE1D,IAAqBmY,WAAYF,WAAWrW,MAC5C,IAAIuW,UAAW,CACX,GAAqBC,kBAAsCF,gBAAuB,MAC7EE,oBACDA,iBAAmBze,KAAKgI,QAAQC,WAEpCxH,OAAOuB,KAAKwc,WAAW7c,QAAQ,SAAUiO,MAChCyO,cAAiBI,iBAAiBzY,eAAe4J,QAClD6O,iBAAiB7O,MAAQnH,kBAAkB+V,UAAU5O,MAAO6O,iBAAkBtH,MAAM5V,cAQpG8Z,yBAAyB7a,UAAUke,aAGnC,WACI,GAAqB1W,WACrB,IAAIhI,KAAKgI,QAAS,CACd,GAAqB2W,aAAc3e,KAAKgI,QAAQC,MAChD,IAAI0W,YAAa,CACb,GAAqB7G,UAAW9P,QAAgB,SAChDvH,QAAOuB,KAAK2c,aAAahd,QAAQ,SAAUiO,MAAQkI,SAASlI,MAAQ+O,YAAY/O,SAGxF,MAAO5H,UAQXqT,yBAAyB7a,UAAUsb,iBAMnC,SAAU9T,QAAS7G,QAASyd,aACR,KAAZ5W,UAAsBA,QAAU,KACpC,IAAqB6W,QAAS1d,SAAWnB,KAAKmB,QACzB2I,QAAU,GAAIuR,0BAAyBrb,KAAK6W,QAASgI,OAAQ7e,KAAKqO,gBAAiBrO,KAAK8d,gBAAiB9d,KAAK+d,gBAAiB/d,KAAKuB,OAAQvB,KAAKoP,UAAWpP,KAAKsb,gBAAgBwD,KAAKD,OAAQD,SAAW,GAS9N,OARA9U,SAAQmS,aAAejc,KAAKic,aAC5BnS,QAAQ4O,sBAAwB1Y,KAAK0Y,sBACrC5O,QAAQ9B,QAAUhI,KAAK0e,eACvB5U,QAAQuS,cAAcrU,SACtB8B,QAAQ0T,kBAAoBxd,KAAKwd,kBACjC1T,QAAQwT,kBAAoBtd,KAAKsd,kBACjCxT,QAAQ2T,cAAgBzd,KACxBA,KAAKsc,kBACExS,SAMXuR,yBAAyB7a,UAAUwb,yBAInC,SAAU4C,SAIN,MAHA5e,MAAKic,aAAeQ,2BACpBzc,KAAKsb,gBAAkBtb,KAAKsb,gBAAgBwD,KAAK9e,KAAKmB,QAASyd,SAC/D5e,KAAKoP,UAAU5M,KAAKxC,KAAKsb,iBAClBtb,KAAKsb,iBAQhBD,yBAAyB7a,UAAU4b,4BAMnC,SAAUF,YAAa/V,SAAUE,OAC7B,GAAqB0Y,iBACjB5Y,SAAsB,MAAZA,SAAmBA,SAAW+V,YAAY/V,SACpDE,MAAOrG,KAAKsb,gBAAgBpE,aAAwB,MAAT7Q,MAAgBA,MAAQ,GAAK6V,YAAY7V,MACpFC,OAAQ,IAES0Y,QAAU,GAAIC,oBAAmBjf,KAAK6W,QAASqF,YAAY/a,QAAS+a,YAAY9a,UAAW8a,YAAYtO,cAAesO,YAAYrO,eAAgBkR,eAAgB7C,YAAYgD,wBAEnM,OADAlf,MAAKoP,UAAU5M,KAAKwc,SACbD,gBAMX1D,yBAAyB7a,UAAUuc,cAInC,SAAUoC,MACNnf,KAAKsb,gBAAgB8B,YAAYpd,KAAKsb,gBAAgBnV,SAAWgZ,OAMrE9D,yBAAyB7a,UAAUkc,cAInC,SAAUrW,OAEFA,MAAQ,GACRrG,KAAKsb,gBAAgBoB,cAAcrW,QAY3CgV,yBAAyB7a,UAAU4V,YASnC,SAAU7J,SAAUmO,iBAAkBR,MAAOM,YAAaC,SAAUlZ,QAChE,GAAqBuU,WAIrB,IAHI0E,aACA1E,QAAQtT,KAAKxC,KAAKmB,SAElBoL,SAAS1L,OAAS,EAAG,CAErB0L,SAAWA,SAAS3D,QAAQuS,kBAAmB,IAAMnb,KAAK8d,iBAC1DvR,SAAWA,SAAS3D,QAAQwS,kBAAmB,IAAMpb,KAAK+d,gBAC1D,IAAqB3I,OAAiB,GAAT8E,MACR9I,SAAWpR,KAAK6W,QAAQP,MAAMtW,KAAKmB,QAASoL,SAAU6I,MAC7D,KAAV8E,QACA9I,SAAW8I,MAAQ,EAAI9I,SAASgO,MAAMhO,SAASvQ,OAASqZ,MAAO9I,SAASvQ,QACpEuQ,SAASgO,MAAM,EAAGlF,QAE1BpE,QAAQtT,KAAKqT,MAAMC,QAAS1E,UAKhC,MAHKqJ,WAA8B,GAAlB3E,QAAQjV,QACrBU,OAAOiB,KAAK,WAAckY,iBAAmB,4CAAgDA,iBAAmB,uDAE7G5E,SAEJuF,4BAEP4C,gBAAiC,WACjC,QAASA,iBAAgBpH,QAAS1V,QAASoY,UAAW8F,8BAClDrf,KAAK6W,QAAUA,QACf7W,KAAKmB,QAAUA,QACfnB,KAAKuZ,UAAYA,UACjBvZ,KAAKqf,6BAA+BA,6BACpCrf,KAAKmG,SAAW,EAChBnG,KAAKsf,qBACLtf,KAAKuf,oBACLvf,KAAKwf,WAAa,GAAItb,KACtBlE,KAAKyf,iBACLzf,KAAK0f,kBACL1f,KAAK2f,aACL3f,KAAK4f,0BAA4B,KAC5B5f,KAAKqf,+BACNrf,KAAKqf,6BAA+B,GAAInb,MAE5ClE,KAAK6f,qBAAuBpf,OAAOC,OAAOV,KAAK2f,cAC/C3f,KAAK8f,sBAA2C9f,KAAKqf,6BAA6Blb,IAAIhD,SACjFnB,KAAK8f,wBACN9f,KAAK8f,sBAAwB9f,KAAK6f,qBAClC7f,KAAKqf,6BAA6Bjb,IAAIjD,QAASnB,KAAK6f,uBAExD7f,KAAK+f,gBAkTT,MA7SA9B,iBAAgBzd,UAAUib,kBAG1B,WACI,OAAQzb,KAAKwf,WAAWvH,MACpB,IAAK,GACD,OAAO,CACX,KAAK,GACD,MAAOjY,MAAKgd,4BAA4Bnc,OAAS,CACrD,SACI,OAAO,IAMnBod,gBAAgBzd,UAAUwc,0BAG1B,WAAc,MAAOvc,QAAOuB,KAAKhC,KAAKuf,mBACtC9e,OAAOyd,eAAeD,gBAAgBzd,UAAW,eAC7C2D,IAGA,WAAc,MAAOnE,MAAKuZ,UAAYvZ,KAAKmG,UAC3CgY,YAAY,EACZC,cAAc,IAMlBH,gBAAgBzd,UAAUkc,cAI1B,SAAUrW,OAKN,GAAqB2Z,iBAA0C,GAAxBhgB,KAAKwf,WAAWvH,MAAaxX,OAAOuB,KAAKhC,KAAK0f,gBAAgB7e,MACjGb,MAAKmG,UAAY6Z,iBACjBhgB,KAAKod,YAAYpd,KAAKkX,YAAc7Q,OAChC2Z,iBACAhgB,KAAKwc,yBAITxc,KAAKuZ,WAAalT,OAQ1B4X,gBAAgBzd,UAAUse,KAK1B,SAAU3d,QAAS+V,aAEf,MADAlX,MAAK2c,wBACE,GAAIsB,iBAAgBje,KAAK6W,QAAS1V,QAAS+V,aAAelX,KAAKkX,YAAalX,KAAKqf,+BAK5FpB,gBAAgBzd,UAAUuf,cAG1B,WACQ/f,KAAKuf,mBACLvf,KAAKsf,kBAAoBtf,KAAKuf,kBAElCvf,KAAKuf,iBAAsCvf,KAAKwf,WAAWrb,IAAInE,KAAKmG,UAC/DnG,KAAKuf,mBACNvf,KAAKuf,iBAAmB9e,OAAOC,OAAOV,KAAK2f,cAC3C3f,KAAKwf,WAAWpb,IAAIpE,KAAKmG,SAAUnG,KAAKuf,oBAMhDtB,gBAAgBzd,UAAUyc,aAG1B,WACIjd,KAAKmG,UA1xBmB,EA2xBxBnG,KAAK+f,iBAMT9B,gBAAgBzd,UAAU4c,YAI1B,SAAU+B,MACNnf,KAAK2c,wBACL3c,KAAKmG,SAAWgZ,KAChBnf,KAAK+f,iBAOT9B,gBAAgBzd,UAAUyf,aAK1B,SAAUhe,KAAMgC,OACZjE,KAAK6f,qBAAqB5d,MAAQgC,MAClCjE,KAAK8f,sBAAsB7d,MAAQgC,MACnCjE,KAAKyf,cAAcxd,OAAUkd,KAAMnf,KAAKkX,YAAajT,MAAOA,QAKhEga,gBAAgBzd,UAAUmb,wBAG1B,WAAc,MAAO3b,MAAK4f,4BAA8B5f,KAAKuf,kBAK7DtB,gBAAgBzd,UAAU0c,eAI1B,SAAU5W,QACN,GAAI6Q,OAAQnX,IACRsG,UACAtG,KAAKsf,kBAA0B,OAAIhZ,QAQvC7F,OAAOuB,KAAKhC,KAAK8f,uBAAuBne,QAAQ,SAAUM,MACtDkV,MAAMwI,UAAU1d,MAAQkV,MAAM2I,sBAAsB7d,OAAShC,oBAAoBqC,WACjF6U,MAAMoI,iBAAiBtd,MAAQhC,oBAAoBqC,aAEvDtC,KAAK4f,0BAA4B5f,KAAKuf,kBAS1CtB,gBAAgBzd,UAAUgH,UAO1B,SAAU6B,MAAO/C,OAAQ/E,OAAQyG,SAC7B,GAAImP,OAAQnX,IACRsG,UACAtG,KAAKsf,kBAA0B,OAAIhZ,OAEvC,IAAqB2B,QAAUD,SAAWA,QAAQC,WAC7Bf,OAAS2H,cAAcxF,MAAOrJ,KAAK8f,sBACxDrf,QAAOuB,KAAKkF,QAAQvF,QAAQ,SAAUM,MAClC,GAAqBmG,KAAMK,kBAAkBvB,OAAOjF,MAAOgG,OAAQ1G,OACnE4V,OAAMuI,eAAezd,MAAQmG,IACxB+O,MAAM0I,qBAAqB7Z,eAAe/D,QAC3CkV,MAAMwI,UAAU1d,MAAQkV,MAAM2I,sBAAsB9Z,eAAe/D,MAC/DkV,MAAM2I,sBAAsB7d,MAC5BhC,oBAAoBqC,YAE5B6U,MAAM8I,aAAahe,KAAMmG,QAMjC6V,gBAAgBzd,UAAUmc,sBAG1B,WACI,GAAIxF,OAAQnX,KACSkH,OAASlH,KAAK0f,eACdjO,MAAQhR,OAAOuB,KAAKkF,OACrB,IAAhBuK,MAAM5Q,SAEVb,KAAK0f,kBACLjO,MAAM9P,QAAQ,SAAUM,MACpB,GAAqBmG,KAAMlB,OAAOjF,KAClCkV,OAAMoI,iBAAiBtd,MAAQmG,MAEnC3H,OAAOuB,KAAKhC,KAAK6f,sBAAsBle,QAAQ,SAAUM,MAChDkV,MAAMoI,iBAAiBvZ,eAAe/D,QACvCkV,MAAMoI,iBAAiBtd,MAAQkV,MAAM0I,qBAAqB5d,WAOtEgc,gBAAgBzd,UAAUgc,sBAG1B,WACI,GAAIrF,OAAQnX,IACZS,QAAOuB,KAAKhC,KAAK6f,sBAAsBle,QAAQ,SAAUM,MACrD,GAAqBmG,KAAM+O,MAAM0I,qBAAqB5d,KACtDkV,OAAMuI,eAAezd,MAAQmG,IAC7B+O,MAAM8I,aAAahe,KAAMmG,QAMjC6V,gBAAgBzd,UAAU0f,iBAG1B,WAAc,MAAOlgB,MAAKwf,WAAWrb,IAAInE,KAAKmG,WAC9C1F,OAAOyd,eAAeD,gBAAgBzd,UAAW,cAC7C2D,IAGA,WACI,GAAqBgc,cACrB,KAAK,GAAqBle,QAAQjC,MAAKuf,iBACnCY,WAAW3d,KAAKP,KAEpB,OAAOke,aAEXhC,YAAY,EACZC,cAAc,IAMlBH,gBAAgBzd,UAAUqc,6BAI1B,SAAUrB,UACN,GAAIrE,OAAQnX,IACZS,QAAOuB,KAAKwZ,SAASiE,eAAe9d,QAAQ,SAAUM,MAClD,GAAqBme,UAAWjJ,MAAMsI,cAAcxd,MAC/Boe,SAAW7E,SAASiE,cAAcxd,QAClDme,UAAYC,SAASlB,KAAOiB,SAASjB,OACtChI,MAAM8I,aAAahe,KAAMoe,SAASpc,UAO9Cga,gBAAgBzd,UAAU+N,eAG1B,WACI,GAAI4I,OAAQnX,IACZA,MAAK2c,uBACL,IAAqB/O,eAAgB,GAAI6E,KACpB5E,eAAiB,GAAI4E,KACrBoG,QAAmC,IAAzB7Y,KAAKwf,WAAWvH,MAAgC,IAAlBjY,KAAKmG,SAC7Cma,iBACrBtgB,MAAKwf,WAAW7d,QAAQ,SAAU4e,SAAUpB,MACxC,GAAqBqB,eAAgBlZ,WAAWiZ,UAAU,EAC1D9f,QAAOuB,KAAKwe,eAAe7e,QAAQ,SAAUM,MACzC,GAAqBgC,OAAQuc,cAAcve,KACvCgC,QAAShE,oBAAoBoC,WAC7BuL,cAAciF,IAAI5Q,MAEbgC,OAAShE,oBAAoBqC,YAClCuL,eAAegF,IAAI5Q,QAGtB4W,UACD2H,cAAsB,OAAIrB,KAAOhI,MAAMhR,UAE3Cma,eAAe9d,KAAKge,gBAExB,IAAqBC,UAAW7S,cAAcqK,KAAOlP,gBAAgB6E,cAAcuK,aAC9DuI,UAAY7S,eAAeoK,KAAOlP,gBAAgB8E,eAAesK,YAEtF,IAAIU,QAAS,CACT,GAAqB8H,KAAML,eAAe,GACrBM,IAAM9Z,QAAQ6Z,IACnCA,KAAY,OAAI,EAChBC,IAAY,OAAI,EAChBN,gBAAkBK,IAAKC,KAE3B,MAAOjT,2BAA0B3N,KAAKmB,QAASmf,eAAgBG,SAAUC,UAAW1gB,KAAKmG,SAAUnG,KAAKuZ,UAAWvZ,KAAKsG,QAAQ,IAE7H2X,mBAEPgB,mBAAoC,SAAU4B,QAE9C,QAAS5B,oBAAmBhe,OAAQE,QAASC,UAAWwM,cAAeC,eAAgB/H,QAASgb,8BAC3D,KAA7BA,2BAAuCA,0BAA2B,EACtE,IAAI3J,OAAQ0J,OAAO/L,KAAK9U,KAAMiB,OAAQE,QAAS2E,QAAQO,QAAUrG,IAOjE,OANAmX,OAAMhW,QAAUA,QAChBgW,MAAM/V,UAAYA,UAClB+V,MAAMvJ,cAAgBA,cACtBuJ,MAAMtJ,eAAiBA,eACvBsJ,MAAM2J,yBAA2BA,yBACjC3J,MAAMrR,SAAYK,SAAUL,QAAQK,SAAUE,MAAOP,QAAQO,MAAOC,OAAQR,QAAQQ,QAC7E6Q,MA4DX,MAtEAjX,WAAU+e,mBAAoB4B,QAe9B5B,mBAAmBze,UAAUib,kBAG7B,WAAc,MAAOzb,MAAKoB,UAAUP,OAAS,GAI7Coe,mBAAmBze,UAAU+N,eAG7B,WACI,GAAqBnN,WAAYpB,KAAKoB,UAClCmZ,GAAKva,KAAK8F,QAASO,MAAQkU,GAAGlU,MAAOF,SAAWoU,GAAGpU,SAAUG,OAASiU,GAAGjU,MAC7E,IAAItG,KAAK8gB,0BAA4Bza,MAAO,CACxC,GAAqB0a,iBACA7d,UAAYiD,SAAWE,MACvB2a,YAAc3a,MAAQnD,UAEtB+d,iBAAmB3Z,WAAWlG,UAAU,IAAI,EACjE6f,kBAAyB,OAAI,EAC7BF,aAAave,KAAKye,iBAClB,IAAqBC,kBAAmB5Z,WAAWlG,UAAU,IAAI,EACjE8f,kBAAyB,OAAI1S,YAAYwS,aACzCD,aAAave,KAAK0e,iBAiBlB,KAAK,GADgBhH,OAAQ9Y,UAAUP,OAAS,EACtBgR,EAAI,EAAGA,GAAKqI,MAAOrI,IAAK,CAC9C,GAAqBjQ,IAAK0F,WAAWlG,UAAUyQ,IAAI,GAC9BsP,UAA8Bvf,GAAY,OAC1Cwf,eAAiB/a,MAAQ8a,UAAYhb,QAC1DvE,IAAW,OAAI4M,YAAY4S,eAAiBle,WAC5C6d,aAAave,KAAKZ,IAGtBuE,SAAWjD,UACXmD,MAAQ,EACRC,OAAS,GACTlF,UAAY2f,aAEhB,MAAOpT,2BAA0B3N,KAAKmB,QAASC,UAAWpB,KAAK4N,cAAe5N,KAAK6N,eAAgB1H,SAAUE,MAAOC,QAAQ,IAEzH2Y,oBACThB,iBAmCEoD,UAA2B,WAC3B,QAASA,WAAUxK,QAASxN,OACxBrJ,KAAK6W,QAAUA,OACf,IAAqBtV,WACAiM,IAAMtB,kBAAkB2K,QAASxN,MAAO9H,OAC7D,IAAIA,OAAOV,OAAQ,CACf,GAAqBygB,cAAe,iCAAmC/f,OAAOmB,KAAK,KACnF,MAAM,IAAID,OAAM6e,cAEpBthB,KAAKuhB,cAAgB/T,IA8BzB,MApBA6T,WAAU7gB,UAAUghB,eAQpB,SAAUrgB,QAASgN,eAAgBsT,kBAAmBzZ,QAASqG,iBAC3D,GAAqBqT,OAAQta,MAAMC,QAAQ8G,gBAAkBlH,gBAAgBkH,gBAAmC,eAC3FwT,KAAOva,MAAMC,QAAQoa,mBAAqBxa,gBAAgBwa,mBAAsC,kBAChGlgB,SACrB8M,iBAAkBA,iBAAmB,GAAIuM,sBACzC,IAAqB3V,QAAS8I,wBAAwB/N,KAAK6W,QAAS1V,QAASnB,KAAKuhB,cAjxEpE,WACA,WAgxEqHG,MAAOC,KAAM3Z,QAASqG,gBAAiB9M,OAC1K,IAAIA,OAAOV,OAAQ,CACf,GAAqBygB,cAAe,+BAAiC/f,OAAOmB,KAAK,KACjF,MAAM,IAAID,OAAM6e,cAEpB,MAAOrc,SAEJoc,aAkBPO,yBAA0C,WAC1C,QAASA,6BAET,MAAOA,6BAKPC,6BAA8C,WAC9C,QAASA,iCA8BT,MAvBAA,8BAA6BrhB,UAAU4B,sBAKvC,SAAU0f,aAAcvgB,QAAU,MAAOugB,eAQzCD,6BAA6BrhB,UAAU+B,oBAOvC,SAAUwf,qBAAsBC,mBAAoB/d,MAAO1C,QACvD,MAAwB,QAErBsgB,gCAOPI,6BAA8C,SAAUpB,QAExD,QAASoB,gCACL,MAAkB,QAAXpB,QAAmBA,OAAOhL,MAAM7V,KAAMyJ,YAAczJ,KA6C/D,MA/CAE,WAAU+hB,6BAA8BpB,QASxCoB,6BAA6BzhB,UAAU4B,sBAKvC,SAAU0f,aAAcvgB,QACpB,MAAOmG,qBAAoBoa,eAS/BG,6BAA6BzhB,UAAU+B,oBAOvC,SAAUwf,qBAAsBC,mBAAoB/d,MAAO1C,QACvD,GAAqBoE,MAAO,GACPuc,OAASje,MAAMoE,WAAW8Z,MAC/C,IAAIC,qBAAqBJ,qBAAiC,IAAV/d,OAAyB,MAAVA,MAC3D,GAAqB,gBAAVA,OACP0B,KAAO,SAEN,CACD,GAAqB0c,mBAAoBpe,MAAMuB,MAAM,yBACjD6c,oBAAoD,GAA/BA,kBAAkB,GAAGxhB,QAC1CU,OAAOiB,KAAK,uCAAyCuf,qBAAuB,IAAM9d,OAI9F,MAAOie,QAASvc,MAEbsc,8BACTL,0BACEQ,qBAMJ,SAAwBpgB,MACpB,GAAqB8B,OAErB,OADA9B,MAAKL,QAAQ,SAAUoC,KAAO,MAAOD,KAAIC,MAAO,IACzCD,KAT+B,iUACrCkH,MAAM,MAwDPsX,gBACAtS,2BAA4C,WAC5C,QAASA,4BAA2BuS,aAAc/U,IAAKgV,cACnDxiB,KAAKuiB,aAAeA,aACpBviB,KAAKwN,IAAMA,IACXxN,KAAKwiB,aAAeA,aAsFxB,MA/EAxS,4BAA2BxP,UAAUgF,MAKrC,SAAUgK,aAAcC,WACpB,MAAOH,2BAA0BtP,KAAKwN,IAAI0C,SAAUV,aAAcC,YAQtEO,2BAA2BxP,UAAUiiB,YAMrC,SAAUC,UAAWza,OAAQ1G,QACzB,GAAqBohB,mBAAoB3iB,KAAKwiB,aAAa,KACtCI,YAAc5iB,KAAKwiB,aAAaE,WAChCG,aAAeF,kBAAoBA,kBAAkBF,YAAYxa,OAAQ1G,UAC9F,OAAOqhB,aAAcA,YAAYH,YAAYxa,OAAQ1G,QAAUshB,cAcnE7S,2BAA2BxP,UAAU6L,MAYrC,SAAUpL,OAAQE,QAASqO,aAAcC,UAAWxB,eAAgBC,eAAgB4U,eAAgBC,YAAa1U,iBAC7G,GAAqB9M,WACAyhB,0BAA4BhjB,KAAKwN,IAAIxF,SAAWhI,KAAKwN,IAAIxF,QAAQC,QAAUqa,aAC3EW,uBAAyBH,gBAAkBA,eAAe7a,QAAUqa,aACpEY,mBAAqBljB,KAAKyiB,YAAYjT,aAAcyT,uBAAwB1hB,QAC5E4hB,oBAAsBJ,aAAeA,YAAY9a,QAAUqa,aAC3Dc,gBAAkBpjB,KAAKyiB,YAAYhT,UAAW0T,oBAAqB5hB,QACnE8N,gBAAkB,GAAIoD,KACtB4Q,YAAc,GAAInf,KAClBof,aAAe,GAAIpf,KACnBqf,UAA0B,SAAd9T,UACZ+T,kBAAqBvb,OAAQwM,YAAauO,0BAA2BG,sBACrE/T,UAAYrB,wBAAwB9M,OAAQE,QAASnB,KAAKwN,IAAIyC,UAAWhC,eAAgBC,eAAgBgV,mBAAoBE,gBAAiBI,iBAAkBnV,gBAAiB9M,OACtM,IAAIA,OAAOV,OACP,MAAOmO,6BAA4B7N,QAASnB,KAAKuiB,aAAc/S,aAAcC,UAAW8T,UAAWL,mBAAoBE,sBAAyBC,YAAaC,aAAc/hB,OAE/K6N,WAAUzN,QAAQ,SAAU+Z,IACxB,GAAqB1F,KAAM0F,GAAGva,QACTsf,SAAW5c,gBAAgBwf,YAAarN,OAC7D0F,IAAG9N,cAAcjM,QAAQ,SAAUM,MAAQ,MAAOwe,UAASxe,OAAQ,GACnE,IAAqBye,WAAY7c,gBAAgByf,aAActN,OAC/D0F,IAAG7N,eAAelM,QAAQ,SAAUM,MAAQ,MAAOye,WAAUze,OAAQ,IACjE+T,MAAQ7U,SACRkO,gBAAgBwD,IAAImD,MAG5B,IAAqByN,qBAAsB1a,gBAAgBsG,gBAAgB8I,SAC3E,OAAOnJ,6BAA4B7N,QAASnB,KAAKuiB,aAAc/S,aAAcC,UAAW8T,UAAWL,mBAAoBE,gBAAiBhU,UAAWqU,oBAAqBJ,YAAaC,eAElLtT,8BAWP0T,qBAAsC,WACtC,QAASA,sBAAqBxc,OAAQyc,eAClC3jB,KAAKkH,OAASA,OACdlH,KAAK2jB,cAAgBA,cAmCzB,MA5BAD,sBAAqBljB,UAAUiiB,YAK/B,SAAUxa,OAAQ1G,QACd,GAAqB6M,gBACAwV,eAAiB9c,QAAQ9G,KAAK2jB,cAmBnD,OAlBAljB,QAAOuB,KAAKiG,QAAQtG,QAAQ,SAAUoC,KAClC,GAAqBE,OAAQgE,OAAOlE,IACvB,OAATE,QACA2f,eAAe7f,KAAOE,SAG9BjE,KAAKkH,OAAOA,OAAOvF,QAAQ,SAAUsC,OACjC,GAAqB,gBAAVA,OAAoB,CAC3B,GAAqB4f,YAA8B,KACnDpjB,QAAOuB,KAAK6hB,YAAYliB,QAAQ,SAAUM,MACtC,GAAqBmG,KAAMyb,WAAW5hB,KAClCmG,KAAIvH,OAAS,IACbuH,IAAMK,kBAAkBL,IAAKwb,eAAgBriB,SAEjD6M,YAAYnM,MAAQmG,SAIzBgG,aAEJsV,wBAmBP7T,iBAAkC,WAClC,QAASA,kBAAiBD,KAAMpC,KAC5B,GAAI2J,OAAQnX,IACZA,MAAK4P,KAAOA,KACZ5P,KAAKwN,IAAMA,IACXxN,KAAK8jB,uBACL9jB,KAAK+P,UACLvC,IAAIuC,OAAOpO,QAAQ,SAAU6L,KACzB,GAAqBmW,eAAiBnW,IAAIxF,SAAWwF,IAAIxF,QAAQC,UACjEkP,OAAMpH,OAAOvC,IAAIoC,MAAQ,GAAI8T,sBAAqBlW,IAAIxI,MAAO2e,iBAEjEtT,kBAAkBrQ,KAAK+P,OAAQ,OAAQ,KACvCM,kBAAkBrQ,KAAK+P,OAAQ,QAAS,KACxCvC,IAAI4J,YAAYzV,QAAQ,SAAU6L,KAC9B2J,MAAM2M,oBAAoBthB,KAAK,GAAIwN,4BAA2BJ,KAAMpC,IAAK2J,MAAMpH,WAEnF/P,KAAK+jB,mBAAqBjU,yBAAyBF,KAAM5P,KAAK+P,QAuClE,MArCAtP,QAAOyd,eAAerO,iBAAiBrP,UAAW,mBAC9C2D,IAGA,WAAc,MAAOnE,MAAKwN,IAAI2C,WAAa,GAC3CgO,YAAY,EACZC,cAAc,IAOlBvO,iBAAiBrP,UAAUwjB,gBAK3B,SAAUxU,aAAcC,WAEpB,MAD6BzP,MAAK8jB,oBAAoBrX,KAAK,SAAUwX,GAAK,MAAOA,GAAEze,MAAMgK,aAAcC,cACvF,MAQpBI,iBAAiBrP,UAAU0jB,YAM3B,SAAU1U,aAAcvH,OAAQ1G,QAC5B,MAAOvB,MAAK+jB,mBAAmBtB,YAAYjT,aAAcvH,OAAQ1G,SAE9DsO,oBAyCPsU,sBAAwB,GAAIvJ,uBAC5BwJ,wBAAyC,WACzC,QAASA,yBAAwBvN,QAASwN,aACtCrkB,KAAK6W,QAAUA,QACf7W,KAAKqkB,YAAcA,YACnBrkB,KAAKskB,eACLtkB,KAAKukB,gBACLvkB,KAAKY,WA6LT,MAtLAwjB,yBAAwB5jB,UAAUgkB,SAKlC,SAAUC,GAAItY,UACV,GAAqB5K,WACAiM,IAAMtB,kBAAkBlM,KAAK6W,QAAS1K,SAAU5K,OACrE,IAAIA,OAAOV,OACP,KAAM,IAAI4B,OAAM,8DAAgElB,OAAOmB,KAAK,MAG5F1C,MAAKskB,YAAYG,IAAMjX,KAS/B4W,wBAAwB5jB,UAAUkkB,aAMlC,SAAU7S,EAAGxQ,UAAWC,YACpB,GAAqBH,SAAU0Q,EAAE1Q,QACZC,UAAYJ,mBAAmBhB,KAAK6W,QAAS7W,KAAKqkB,YAAaljB,QAAS0Q,EAAEzQ,UAAWC,UAAWC,WACrH,OAAOtB,MAAK6W,QAAQN,QAAQpV,QAASC,UAAWyQ,EAAE1L,SAAU0L,EAAExL,MAAOwL,EAAEvL,YAQ3E8d,wBAAwB5jB,UAAUE,OAMlC,SAAU+jB,GAAItjB,QAAS6G,SACnB,GAAImP,OAAQnX,SACI,KAAZgI,UAAsBA,WAC1B,IAEqB+S,cAFAxZ,UACAiM,IAAMxN,KAAKskB,YAAYG,IAEvBE,cAAgB,GAAIzgB,IAYzC,IAXIsJ,KACAuN,aAAehN,wBAAwB/N,KAAK6W,QAAS1V,QAASqM,IApwFpD,WACA,iBAmwFmGxF,QAASmc,sBAAuB5iB,QAC7IwZ,aAAapZ,QAAQ,SAAUijB,MAC3B,GAAqB1d,QAASrD,gBAAgB8gB,cAAeC,KAAKzjB,WAClEyjB,MAAK/W,eAAelM,QAAQ,SAAUM,MAAQ,MAAOiF,QAAOjF,MAAQ,WAIxEV,OAAOiB,KAAK,uEACZuY,iBAEAxZ,OAAOV,OACP,KAAM,IAAI4B,OAAM,+DAAiElB,OAAOmB,KAAK,MAEjGiiB,eAAchjB,QAAQ,SAAUuF,OAAQ/F,SACpCV,OAAOuB,KAAKkF,QAAQvF,QAAQ,SAAUM,MAAQiF,OAAOjF,MAAQkV,MAAMN,QAAQnF,aAAavQ,QAASc,KAAMhC,oBAAoBqC,eAE/H,IAAqB1B,SAAUma,aAAajX,IAAI,SAAU+N,GACtD,GAAqB3K,QAASyd,cAAcxgB,IAAI0N,EAAE1Q,QAClD,OAAOgW,OAAMuN,aAAa7S,KAAO3K,UAEhBtE,OAASjC,oBAAoBC,QAIlD,OAHAZ,MAAKukB,aAAaE,IAAM7hB,OACxBA,OAAOQ,UAAU,WAAc,MAAO+T,OAAM0N,QAAQJ,MACpDzkB,KAAKY,QAAQ4B,KAAKI,QACXA,QAMXwhB,wBAAwB5jB,UAAUqkB,QAIlC,SAAUJ,IACN,GAAqB7hB,QAAS5C,KAAK8kB,WAAWL,GAC9C7hB,QAAOiiB,gBACA7kB,MAAKukB,aAAaE,GACzB,IAAqB/T,OAAQ1Q,KAAKY,QAAQ4D,QAAQ5B,OAC9C8N,QAAS,GACT1Q,KAAKY,QAAQiG,OAAO6J,MAAO,IAOnC0T,wBAAwB5jB,UAAUskB,WAIlC,SAAUL,IACN,GAAqB7hB,QAAS5C,KAAKukB,aAAaE,GAChD,KAAK7hB,OACD,KAAM,IAAIH,OAAM,oDAAsDgiB,GAE1E,OAAO7hB,SASXwhB,wBAAwB5jB,UAAUukB,OAOlC,SAAUN,GAAItjB,QAAS0B,UAAWE,UAE9B,GAAqBiiB,WAAYzhB,mBAAmBpC,QAAS,GAAI,GAAI,GAErE,OADAwB,gBAAe3C,KAAK8kB,WAAWL,IAAK5hB,UAAWmiB,UAAWjiB,UACnD,cASXqhB,wBAAwB5jB,UAAU8D,QAOlC,SAAUmgB,GAAItjB,QAASmD,QAAS2gB,MAC5B,GAAe,YAAX3gB,QAEA,WADAtE,MAAKwkB,SAASC,GAAsBQ,KAAK,GAG7C,IAAe,UAAX3gB,QAAqB,CACrB,GAAqB0D,SAA6Bid,KAAK,MAEvD,YADAjlB,MAAKU,OAAO+jB,GAAItjB,QAAS6G,SAG7B,GAAqBpF,QAAS5C,KAAK8kB,WAAWL,GAC9C,QAAQngB,SACJ,IAAK,OACD1B,OAAOsiB,MACP,MACJ,KAAK,QACDtiB,OAAOuiB,OACP,MACJ,KAAK,QACDviB,OAAOwiB,OACP,MACJ,KAAK,UACDxiB,OAAOyiB,SACP,MACJ,KAAK,SACDziB,OAAO0iB,QACP,MACJ,KAAK,OACD1iB,OAAO2iB,MACP,MACJ,KAAK,cACD3iB,OAAO4iB,YAAY9f,WAA6Buf,KAAK,IACrD,MACJ,KAAK,UACDjlB,KAAK6kB,QAAQJ,MAIlBL,2BAaPqB,sBACAC,oBACAC,YAAa,GACbC,cAAe,KACfC,cAAc,EACdC,sBAAsB,GAEtBlU,4BACA+T,YAAa,GACbC,cAAe,KACfC,cAAc,EACdC,sBAAsB,GAMtBnU,aAAe,eAKfoU,WAA4B,WAC5B,QAASA,YAAW1c,MAAOsc,iBACH,KAAhBA,cAA0BA,YAAc,IAC5C3lB,KAAK2lB,YAAcA,WACnB,IAAqBK,OAAQ3c,OAASA,MAAMrD,eAAe,SACtC/B,MAAQ+hB,MAAQ3c,MAAa,MAAIA,KAEtD,IADArJ,KAAKiE,MAAQ2M,sBAAsB3M,OAC/B+hB,MAAO,CACP,GAAqBhe,SAAUlB,QAAyB,aACjDkB,SAAe,MACtBhI,KAAKgI,QAA2B,YAGhChI,MAAKgI,UAEJhI,MAAKgI,QAAQC,SACdjI,KAAKgI,QAAQC,WA8BrB,MA3BAxH,QAAOyd,eAAe6H,WAAWvlB,UAAW,UACxC2D,IAGA,WAAc,MAAyBnE,MAAKgI,QAAc,QAC1DmW,YAAY,EACZC,cAAc,IAMlB2H,WAAWvlB,UAAUylB,cAIrB,SAAUje,SACN,GAAqBwW,WAAYxW,QAAQC,MACzC,IAAIuW,UAAW,CACX,GAAqBG,aAAiC3e,KAAKgI,QAAe,MAC1EvH,QAAOuB,KAAKwc,WAAW7c,QAAQ,SAAUM,MACZ,MAArB0c,YAAY1c,QACZ0c,YAAY1c,MAAQuc,UAAUvc,WAKvC8jB,cAGPG,oBAAsB,GAAIH,YADb,QAEbI,oBAAsB,GAAIJ,YAAW,WACrCK,6BAA8C,WAC9C,QAASA,8BAA6B3B,GAAI4B,YAAaC,SACnDtmB,KAAKykB,GAAKA,GACVzkB,KAAKqmB,YAAcA,YACnBrmB,KAAKsmB,QAAUA,QACftmB,KAAKY,WACLZ,KAAKumB,aACLvmB,KAAKwmB,UACLxmB,KAAKymB,kBAAoB,GAAIviB,KAC7BlE,KAAK0mB,eAAiB,UAAYjC,GAClC/R,SAAS2T,YAAarmB,KAAK0mB,gBAme/B,MA1dAN,8BAA6B5lB,UAAUukB,OAOvC,SAAU5jB,QAASyO,KAAM+W,MAAO5jB,UAC5B,GAAIoU,OAAQnX,IACZ,KAAKA,KAAKumB,UAAUvgB,eAAe4J,MAC/B,KAAM,IAAInN,OAAM,oDAAuDkkB,MAAQ,oCAAwC/W,KAAO,oBAElI,IAAa,MAAT+W,OAAiC,GAAhBA,MAAM9lB,OACvB,KAAM,IAAI4B,OAAM,8CAAiDmN,KAAO,6CAE5E,KAAKkB,oBAAoB6V,OACrB,KAAM,IAAIlkB,OAAM,yCAA4CkkB,MAAQ,gCAAoC/W,KAAO,sBAEnH,IAAqBgX,WAAY/iB,gBAAgB7D,KAAKymB,kBAAmBtlB,YACpDyC,MAASgM,KAAMA,KAAM+W,MAAOA,MAAO5jB,SAAUA,SAClE6jB,WAAUpkB,KAAKoB,KACf,IAAqBijB,oBAAqBhjB,gBAAgB7D,KAAKsmB,QAAQQ,gBAAiB3lB;gFAMxF,OALK0lB,oBAAmB7gB,eAAe4J,QACnC8C,SAASvR,QAlgGM,cAmgGfuR,SAASvR,QAAS4lB,cAA6BnX,MAC/CiX,mBAAmBjX,MAAQsW,qBAExB,WAOH/O,MAAMmP,QAAQU,WAAW,WACrB,GAAqBtW,OAAQkW,UAAUpiB,QAAQZ,KAC3C8M,QAAS,GACTkW,UAAU/f,OAAO6J,MAAO,GAEvByG,MAAMoP,UAAU3W,aACViX,oBAAmBjX,UAU1CwW,6BAA6B5lB,UAAUgkB,SAKvC,SAAU5U,KAAMpC,KACZ,OAAIxN,KAAKumB,UAAU3W,QAKf5P,KAAKumB,UAAU3W,MAAQpC,KAChB,IAOf4Y,6BAA6B5lB,UAAUymB,YAIvC,SAAUrX,MACN,GAAqBsX,SAAUlnB,KAAKumB,UAAU3W,KAC9C,KAAKsX,QACD,KAAM,IAAIzkB,OAAM,mCAAsCmN,KAAO,6BAEjE,OAAOsX,UASXd,6BAA6B5lB,UAAU0mB,QAOvC,SAAU/lB,QAASqC,YAAaS,MAAOkjB,mBACnC,GAAIhQ,OAAQnX,SACc,KAAtBmnB,oBAAgCA,mBAAoB,EACxD,IAAqBD,SAAUlnB,KAAKinB,YAAYzjB,aAC3BZ,OAAS,GAAIwkB,2BAA0BpnB,KAAKykB,GAAIjhB,YAAarC,SAC7D0lB,mBAAqB7mB,KAAKsmB,QAAQQ,gBAAgB3iB,IAAIhD,QACtE0lB,sBACDnU,SAASvR,QAhlGM,cAilGfuR,SAASvR,QAAS4lB,cAA6BvjB,aAC/CxD,KAAKsmB,QAAQQ,gBAAgB1iB,IAAIjD,QAAS0lB,uBAE9C,IAAqBpjB,WAAYojB,mBAAmBrjB,aAC/BE,QAAU,GAAIqiB,YAAW9hB,MAAOjE,KAAKykB,GAM1D,MAL6BxgB,OAASA,MAAM+B,eAAe,WAC7CvC,WACVC,QAAQuiB,cAAcxiB,UAAUuE,SAEpC6e,mBAAmBrjB,aAAeE,QAC7BD,WAGA,GAAIA,YAAc0iB,oBACnB,MAAOvjB,YAHPa,WAAYyiB,mBAYhB,IAnJS,SA4IwBxiB,QAAQO,OAOvBR,UAAUQ,QAAUP,QAAQO,MAA9C,CAmBA,GAAqBojB,kBAAmBxjB,gBAAgB7D,KAAKsmB,QAAQgB,iBAAkBnmB,WACvFkmB,kBAAiB1lB,QAAQ,SAAUiB,QAK3BA,OAAO+iB,aAAexO,MAAMsN,IAAM7hB,OAAOY,aAAeA,aAAeZ,OAAO2kB,QAC9E3kB,OAAOiiB,WAGf,IAAqBpN,YAAayP,QAAQlD,gBAAgBvgB,UAAUQ,MAAOP,QAAQO,OAC9DujB,sBAAuB,CAC5C,KAAK/P,WAAY,CACb,IAAK0P,kBACD,MACJ1P,YAAayP,QAAQnD,mBACrByD,sBAAuB,EAuB3B,MArBAxnB,MAAKsmB,QAAQmB,qBACbznB,KAAKwmB,OAAOhkB,MAAOrB,QAASA,QAASqC,YAAaA,YAAaiU,WAAYA,WAAYhU,UAAWA,UAAWC,QAASA,QAASd,OAAQA,OAAQ4kB,qBAAsBA,uBAChKA,uBACD9U,SAASvR,QAvQE,qBAwQXyB,OAAOI,QAAQ,WAAcgQ,YAAY7R,QAxQ9B,wBA0QfyB,OAAOO,OAAO,WACV,GAAqBuN,OAAQyG,MAAMvW,QAAQ4D,QAAQ5B,OAC/C8N,QAAS,GACTyG,MAAMvW,QAAQiG,OAAO6J,MAAO,EAEhC,IAAqB9P,SAAUuW,MAAMmP,QAAQgB,iBAAiBnjB,IAAIhD,QAClE,IAAIP,QAAS,CACT,GAAqB8mB,SAAU9mB,QAAQ4D,QAAQ5B,OAC3C8kB,UAAW,GACX9mB,QAAQiG,OAAO6gB,QAAS,MAIpC1nB,KAAKY,QAAQ4B,KAAKI,QAClBykB,iBAAiB7kB,KAAKI,QACfA,OAvDH,IAAK4Q,UAAU/P,UAAUwE,OAAQvE,QAAQuE,QAAS,CAC9C,GAAqB1G,WACAomB,aAAeT,QAAQhD,YAAYzgB,UAAUQ,MAAOR,UAAUwE,OAAQ1G,QACtEqmB,WAAaV,QAAQhD,YAAYxgB,QAAQO,MAAOP,QAAQuE,OAAQ1G,OACjFA,QAAOV,OACPb,KAAKsmB,QAAQuB,YAAYtmB,QAGzBvB,KAAKsmB,QAAQU,WAAW,WACpBrf,YAAYxG,QAASwmB,cACrBngB,UAAUrG,QAASymB,gBAmDvCxB,6BAA6B5lB,UAAUsnB,WAIvC,SAAUlY,MACN,GAAIuH,OAAQnX,WACLA,MAAKumB,UAAU3W,MACtB5P,KAAKsmB,QAAQQ,gBAAgBnlB,QAAQ,SAAUomB,SAAU5mB,eAAkB4mB,UAASnY,QACpF5P,KAAKymB,kBAAkB9kB,QAAQ,SAAUilB,UAAWzlB,SAChDgW,MAAMsP,kBAAkBriB,IAAIjD,QAASylB,UAAUrL,OAAO,SAAUyM,OAAS,MAAOA,OAAMpY,MAAQA,WAOtGwW,6BAA6B5lB,UAAUynB,kBAIvC,SAAU9mB,SACNnB,KAAKsmB,QAAQQ,gBAAgBnW,OAAOxP,SACpCnB,KAAKymB,kBAAkB9V,OAAOxP,QAC9B,IAAqB+mB,gBAAiBloB,KAAKsmB,QAAQgB,iBAAiBnjB,IAAIhD,QACpE+mB,kBACAA,eAAevmB,QAAQ,SAAUiB,QAAU,MAAOA,QAAOiiB,YACzD7kB,KAAKsmB,QAAQgB,iBAAiB3W,OAAOxP,WAS7CilB,6BAA6B5lB,UAAU2nB,+BAMvC,SAAUna,YAAalE,QAASyM,SAC5B,GAAIY,OAAQnX,SACI,KAAZuW,UAAsBA,SAAU,GAIpCvW,KAAKsmB,QAAQrlB,OAAOqV,MAAMtI,YAAanB,qBAAqB,GAAMlL,QAAQ,SAAUqU,KAGhF,IAAIA,IAAIrE,cAAR,CAEA,GAAqByW,YAAajR,MAAMmP,QAAQ+B,yBAAyBrS,IACrEoS,YAAWnQ,KACXmQ,WAAWzmB,QAAQ,SAAU2mB,IAAM,MAAOA,IAAGC,sBAAsBvS,IAAKlM,SAAS,GAAO,KAGxFqN,MAAM8Q,kBAAkBjS,SAWpCoQ,6BAA6B5lB,UAAU+nB,sBAOvC,SAAUpnB,QAAS2I,QAAS0e,qBAAsBrB,mBAC9C,GAAIhQ,OAAQnX,KACSyoB,cAAgBzoB,KAAKsmB,QAAQQ,gBAAgB3iB,IAAIhD,QACtE,IAAIsnB,cAAe,CACf,GAAqBC,aAWrB,IAVAjoB,OAAOuB,KAAKymB,eAAe9mB,QAAQ,SAAU6B,aAGzC,GAAI2T,MAAMoP,UAAU/iB,aAAc,CAC9B,GAAqBZ,QAASuU,MAAM+P,QAAQ/lB,QAASqC,YAvSxD,OAuSiF2jB,kBAC1EvkB,SACA8lB,UAAUlmB,KAAKI,WAIvB8lB,UAAU7nB,OAKV,MAJAb,MAAKsmB,QAAQqC,qBAAqB3oB,KAAKykB,GAAItjB,SAAS,EAAM2I,SACtD0e,sBACA7nB,oBAAoB+nB,WAAWvlB,OAAO,WAAc,MAAOgU,OAAMmP,QAAQlT,iBAAiBjS,YAEvF,EAGf,OAAO,GAMXilB,6BAA6B5lB,UAAUooB,+BAIvC,SAAUznB,SACN,GAAIgW,OAAQnX,KACS4mB,UAAY5mB,KAAKymB,kBAAkBtiB,IAAIhD,QAC5D,IAAIylB,UAAW,CACX,GAAqBiC,mBAAoB,GAAIpW,IAC7CmU,WAAUjlB,QAAQ,SAAUmnB,UACxB,GAAqBtlB,aAAcslB,SAASlZ,IAC5C,KAAIiZ,kBAAkBhd,IAAIrI,aAA1B,CAEAqlB,kBAAkBhW,IAAIrP,YACtB,IAAqB0jB,SAAU/P,MAAMoP,UAAU/iB,aAC1BiU,WAAayP,QAAQnD,mBACrBgF,cAAmC5R,MAAMmP,QAAQQ,gBAAgB3iB,IAAIhD,SACrEsC,UAAYslB,cAAcvlB,cAAgB0iB,oBAC1CxiB,QAAU,GAAIqiB,YA7UlC,QA8UoBnjB,OAAS,GAAIwkB,2BAA0BjQ,MAAMsN,GAAIjhB,YAAarC,QACnFgW,OAAMmP,QAAQmB,qBACdtQ,MAAMqP,OAAOhkB,MACTrB,QAASA,QACTqC,YAAaA,YACbiU,WAAYA,WACZhU,UAAWA,UACXC,QAASA,QACTd,OAAQA,OACR4kB,sBAAsB,SAUtCpB,6BAA6B5lB,UAAUwoB,WAKvC,SAAU7nB,QAAS2I,SACf,GAAIqN,OAAQnX,KACSmT,OAASnT,KAAKsmB,OAKnC,IAJInlB,QAAQ8nB,mBACRjpB,KAAKmoB,+BAA+BhnB,QAAS2I,SAAS,IAGtD9J,KAAKuoB,sBAAsBpnB,QAAS2I,SAAS,GAAjD,CAIA,GAAqBof,oCAAoC,CACzD,IAAI/V,OAAOgW,gBAAiB,CACxB,GAAqBC,gBAAiBjW,OAAOvS,QAAQC,OAASsS,OAAOkW,wBAAwBllB,IAAIhD,WAKjG,IAAIioB,gBAAkBA,eAAevoB,OACjCqoB,mCAAoC,MAIpC,KADA,GAAqBI,UAAWnoB,QACzBmoB,SAAWA,SAAShX,YAAY,CACnC,GAAqBiX,UAAWpW,OAAO2T,gBAAgB3iB,IAAImlB,SAC3D,IAAIC,SAAU,CACVL,mCAAoC,CACpC,SAShBlpB,KAAK4oB,+BAA+BznB,SAGhC+nB,kCACA/V,OAAOwV,qBAAqB3oB,KAAKykB,GAAItjB,SAAS,EAAO2I,UAKrDqJ,OAAO6T,WAAW,WAAc,MAAO7P,OAAM8Q,kBAAkB9mB,WAC/DgS,OAAOqW,uBAAuBroB,SAC9BgS,OAAOsW,mBAAmBtoB,QAAS2I,YAQ3Csc,6BAA6B5lB,UAAUkpB,WAKvC,SAAUvoB,QAASkR,QAAUK,SAASvR,QAASnB,KAAK0mB,iBAKpDN,6BAA6B5lB,UAAUmpB,uBAIvC,SAAUC,aACN,GAAIzS,OAAQnX,KACS+a,eA4BrB,OA3BA/a,MAAKwmB,OAAO7kB,QAAQ,SAAUqmB,OAC1B,GAAqBplB,QAASolB,MAAMplB,MACpC,KAAIA,OAAOinB,UAAX,CAEA,GAAqB1oB,SAAU6mB,MAAM7mB,QAChBylB,UAAYzP,MAAMsP,kBAAkBtiB,IAAIhD,QACzDylB,YACAA,UAAUjlB,QAAQ,SAAUmnB,UACxB,GAAIA,SAASlZ,MAAQoY,MAAMxkB,YAAa,CACpC,GAAqBwhB,WAAYzhB,mBAAmBpC,QAAS6mB,MAAMxkB,YAAawkB,MAAMvkB,UAAUQ,MAAO+jB,MAAMtkB,QAAQO,MACrH,WAAsC,MAAI2lB,YAC1CjnB,eAAeqlB,MAAMplB,OAAQkmB,SAASnC,MAAO3B,UAAW8D,SAAS/lB,aAIzEH,OAAOknB,iBACP3S,MAAMmP,QAAQU,WAAW,WAGrBpkB,OAAOiiB,YAIX9J,aAAavY,KAAKwlB,UAG1BhoB,KAAKwmB,UACEzL,aAAagP,KAAK,SAAUtW,EAAGrT,GAGlC,GAAqB4pB,IAAKvW,EAAEgE,WAAWjK,IAAI4C,SACtB6Z,GAAK7pB,EAAEqX,WAAWjK,IAAI4C,QAC3C,OAAU,IAAN4Z,IAAiB,GAANC,GACJD,GAAKC,GAET9S,MAAMmP,QAAQrlB,OAAOkV,gBAAgB1C,EAAEtS,QAASf,EAAEe,SAAW,GAAK,KAOjFilB,6BAA6B5lB,UAAUqkB,QAIvC,SAAU/a,SACN9J,KAAKY,QAAQe,QAAQ,SAAU6S,GAAK,MAAOA,GAAEqQ,YAC7C7kB,KAAKmoB,+BAA+BnoB,KAAKqmB,YAAavc,UAM1Dsc,6BAA6B5lB,UAAU0pB,oBAIvC,SAAU/oB,SACN,GAAqBgpB,eAAe,CAKpC,OAJInqB,MAAKymB,kBAAkB5a,IAAI1K,WAC3BgpB,cAAe,GACnBA,eACKnqB,KAAKwmB,OAAO/Z,KAAK,SAAUub,OAAS,MAAOA,OAAM7mB,UAAYA,WAA+BgpB,cAG9F/D,gCAMPgE,0BAA2C,WAC3C,QAASA,2BAA0BnpB,OAAQojB,aACvCrkB,KAAKiB,OAASA,OACdjB,KAAKqkB,YAAcA,YACnBrkB,KAAKY,WACLZ,KAAKqqB,gBAAkB,GAAInmB,KAC3BlE,KAAKsnB,iBAAmB,GAAIpjB,KAC5BlE,KAAKqpB,wBAA0B,GAAInlB,KACnClE,KAAK8mB,gBAAkB,GAAI5iB,KAC3BlE,KAAKsqB,cAAgB,GAAI7X,KACzBzS,KAAKmpB,gBAAkB,EACvBnpB,KAAKynB,mBAAqB,EAC1BznB,KAAKuqB,oBACLvqB,KAAKwqB,kBACLxqB,KAAKyqB,aACLzqB,KAAK0qB,iBACL1qB,KAAK2qB,wBAA0B,GAAIzmB,KACnClE,KAAK4qB,0BACL5qB,KAAK6qB,0BACL7qB,KAAK8qB,kBAAoB,SAAU3pB,QAAS2I,WAykChD,MAhkCAsgB,2BAA0B5pB,UAAUipB,mBAMpC,SAAUtoB,QAAS2I,SAAW9J,KAAK8qB,kBAAkB3pB,QAAS2I,UAC9DrJ,OAAOyd,eAAekM,0BAA0B5pB,UAAW,iBACvD2D,IAGA,WACI,GAAqBvD,WAQrB,OAPAZ,MAAKwqB,eAAe7oB,QAAQ,SAAU2mB,IAClCA,GAAG1nB,QAAQe,QAAQ,SAAUiB,QACrBA,OAAO2kB,QACP3mB,QAAQ4B,KAAKI,YAIlBhC,SAEXud,YAAY,EACZC,cAAc,IAOlBgM,0BAA0B5pB,UAAUuqB,gBAKpC,SAAUpF,YAAaU,aACnB,GAAqBiC,IAAK,GAAIlC,8BAA6BT,YAAaU,YAAarmB,KAgBrF,OAfIqmB,aAAY/T,WACZtS,KAAKgrB,sBAAsB1C,GAAIjC,cAM/BrmB,KAAKqqB,gBAAgBjmB,IAAIiiB,YAAaiC,IAMtCtoB,KAAKirB,oBAAoB5E,cAEtBrmB,KAAKuqB,iBAAiB5E,aAAe2C,IAOhD8B,0BAA0B5pB,UAAUwqB,sBAKpC,SAAU1C,GAAIjC,aACV,GAAqBnM,OAAQla,KAAKwqB,eAAe3pB,OAAS,CAC1D,IAAIqZ,OAAS,EAAG,CAEZ,IAAK,GADgBgR,QAAQ,EACHrZ,EAAIqI,MAAOrI,GAAK,EAAGA,IAAK,CAC9C,GAAqBsZ,eAAgBnrB,KAAKwqB,eAAe3Y,EACzD,IAAI7R,KAAKiB,OAAOkV,gBAAgBgV,cAAc9E,YAAaA,aAAc,CACrErmB,KAAKwqB,eAAe3jB,OAAOgL,EAAI,EAAG,EAAGyW,IACrC4C,OAAQ,CACR,QAGHA,OACDlrB,KAAKwqB,eAAe3jB,OAAO,EAAG,EAAGyhB,QAIrCtoB,MAAKwqB,eAAehoB,KAAK8lB,GAG7B,OADAtoB,MAAK2qB,wBAAwBvmB,IAAIiiB,YAAaiC,IACvCA,IAOX8B,0BAA0B5pB,UAAUgkB,SAKpC,SAAUmB,YAAaU,aACnB,GAAqBiC,IAAKtoB,KAAKuqB,iBAAiB5E,YAIhD,OAHK2C,MACDA,GAAKtoB,KAAK+qB,gBAAgBpF,YAAaU,cAEpCiC,IAQX8B,0BAA0B5pB,UAAU4qB,gBAMpC,SAAUzF,YAAa/V,KAAMsX,SACzB,GAAqBoB,IAAKtoB,KAAKuqB,iBAAiB5E,YAC5C2C,KAAMA,GAAG9D,SAAS5U,KAAMsX,UACxBlnB,KAAKmpB,mBAQbiB,0BAA0B5pB,UAAUqkB,QAKpC,SAAUc,YAAa7b,SACnB,GAAIqN,OAAQnX,IACZ,IAAK2lB,YAAL,CAEA,GAAqB2C,IAAKtoB,KAAKqrB,gBAAgB1F,YAC/C3lB,MAAKgnB,WAAW,WACZ7P,MAAMwT,wBAAwBha,OAAO2X,GAAGjC,mBACjClP,OAAMoT,iBAAiB5E,YAC9B,IAAqBjV,OAAQyG,MAAMqT,eAAehmB,QAAQ8jB,GACtD5X,QAAS,GACTyG,MAAMqT,eAAe3jB,OAAO6J,MAAO,KAG3C1Q,KAAKsrB,yBAAyB,WAAc,MAAOhD,IAAGzD,QAAQ/a,aAMlEsgB,0BAA0B5pB,UAAU6qB,gBAIpC,SAAU5G,IAAM,MAAOzkB,MAAKuqB,iBAAiB9F,KAK7C2F,0BAA0B5pB,UAAU6nB,yBAIpC,SAAUlnB,SAMN,GAAqBinB,YAAa,GAAI3V,KACjBsW,cAAgB/oB,KAAK8mB,gBAAgB3iB,IAAIhD,QAC9D,IAAI4nB,cAEA,IAAK,GADgB/mB,MAAOvB,OAAOuB,KAAK+mB,eACdlX,EAAI,EAAGA,EAAI7P,KAAKnB,OAAQgR,IAAK,CACnD,GAAqB0Z,MAAOxC,cAAc/mB,KAAK6P,IAAI8T,WACnD,IAAI4F,KAAM,CACN,GAAqBjD,IAAKtoB,KAAKqrB,gBAAgBE,KAC3CjD,KACAF,WAAWvV,IAAIyV,KAK/B,MAAOF,aASXgC,0BAA0B5pB,UAAU0mB,QAOpC,SAAUvB,YAAaxkB,QAASyO,KAAM3L,OAClC,QAAI4M,cAAc1P,WACdnB,KAAKqrB,gBAAgB1F,aAAauB,QAAQ/lB,QAASyO,KAAM3L,QAClD,IAWfmmB,0BAA0B5pB,UAAUkpB,WAOpC,SAAU/D,YAAaxkB,QAASkR,OAAQmZ,cACpC,GAAK3a,cAAc1P,SAAnB,CAIA,GAAqBsqB,SAA4BtqB,QAAQwQ,aAOzD,IANI8Z,SAAWA,QAAQ7F,gBACnB6F,QAAQ7F,eAAgB,GAKxBD,YAAa,CACb,GAAqB2C,IAAKtoB,KAAKqrB,gBAAgB1F,YAO3C2C,KACAA,GAAGoB,WAAWvoB,QAASkR,QAI3BmZ,cACAxrB,KAAKirB,oBAAoB9pB,WAOjCipB,0BAA0B5pB,UAAUyqB,oBAIpC,SAAU9pB,SAAWnB,KAAK4qB,uBAAuBpoB,KAAKrB,UAMtDipB,0BAA0B5pB,UAAUkrB,sBAKpC,SAAUvqB,QAAS8C,OACXA,MACKjE,KAAKsqB,cAAcze,IAAI1K,WACxBnB,KAAKsqB,cAAczX,IAAI1R,SACvBuR,SAASvR,QA52BA,wBA+2BRnB,KAAKsqB,cAAcze,IAAI1K,WAC5BnB,KAAKsqB,cAAc3Z,OAAOxP,SAC1B6R,YAAY7R,QAj3BC,yBA03BrBipB,0BAA0B5pB,UAAUwoB,WAMpC,SAAUrD,YAAaxkB,QAAS2I,SAC5B,IAAK+G,cAAc1P,SAEf,WADAnB,MAAKypB,mBAAmBtoB,QAAS2I,QAGrC,IAAqBwe,IAAK3C,YAAc3lB,KAAKqrB,gBAAgB1F,aAAe,IACxE2C,IACAA,GAAGU,WAAW7nB,QAAS2I,SAGvB9J,KAAK2oB,qBAAqBhD,YAAaxkB,SAAS,EAAO2I,UAU/DsgB,0BAA0B5pB,UAAUmoB,qBAOpC,SAAUhD,YAAaxkB,QAAS0kB,aAAc/b,SAC1C9J,KAAK6qB,uBAAuBroB,KAAKrB,SACjCA,QAAQwQ,eACJgU,YAAaA,YACbC,cAAe9b,QAAS+b,aAAcA,aACtCC,sBAAsB,IAW9BsE,0BAA0B5pB,UAAUukB,OAQpC,SAAUY,YAAaxkB,QAASyO,KAAM+W,MAAO5jB,UACzC,MAAI8N,eAAc1P,SACPnB,KAAKqrB,gBAAgB1F,aAAaZ,OAAO5jB,QAASyO,KAAM+W,MAAO5jB,UAEnE,cASXqnB,0BAA0B5pB,UAAUmrB,kBAOpC,SAAU3D,MAAO4D,aAAc3d,eAAgBC,gBAC3C,MAAO8Z,OAAMvQ,WAAWpL,MAAMrM,KAAKiB,OAAQ+mB,MAAM7mB,QAAS6mB,MAAMvkB,UAAUQ,MAAO+jB,MAAMtkB,QAAQO,MAAOgK,eAAgBC,eAAgB8Z,MAAMvkB,UAAUuE,QAASggB,MAAMtkB,QAAQsE,QAAS4jB,eAM1LxB,0BAA0B5pB,UAAUgpB,uBAIpC,SAAUqC,kBACN,GAAI1U,OAAQnX,KACSoR,SAAWpR,KAAKiB,OAAOqV,MAAMuV,iBAAkBhf,qBAAqB,EACzFuE,UAASzP,QAAQ,SAAUR,SAAW,MAAOgW,OAAM2U,kCAAkC3qB,WAC5C,GAArCnB,KAAKqpB,wBAAwBpR,OAEjC7G,SAAWpR,KAAKiB,OAAOqV,MAAMuV,iBAAkB/e,uBAAuB,GACtEsE,SAASzP,QAAQ,SAAUR,SAAW,MAAOgW,OAAM4U,sCAAsC5qB,aAM7FipB,0BAA0B5pB,UAAUsrB,kCAIpC,SAAU3qB,SACN,GAAqBP,SAAUZ,KAAKsnB,iBAAiBnjB,IAAIhD,QACrDP,UACAA,QAAQe,QAAQ,SAAUiB,QAIlBA,OAAO2kB,OACP3kB,OAAOknB,kBAAmB,EAG1BlnB,OAAOiiB,WAInB,IAAqBkD,UAAW/nB,KAAK8mB,gBAAgB3iB,IAAIhD,QACrD4mB,WACAtnB,OAAOuB,KAAK+lB,UAAUpmB,QAAQ,SAAU6B,aAAe,MAAOukB,UAASvkB,aAAe2iB,uBAO9FiE,0BAA0B5pB,UAAUurB,sCAIpC,SAAU5qB,SACN,GAAqBP,SAAUZ,KAAKqpB,wBAAwBllB,IAAIhD,QAC5DP,UACAA,QAAQe,QAAQ,SAAUiB,QAAU,MAAOA,QAAO0iB,YAM1D8E,0BAA0B5pB,UAAUwrB,kBAGpC,WACI,GAAI7U,OAAQnX,IACZ,OAAO,IAAIisB,SAAQ,SAAUC,SACzB,GAAI/U,MAAMvW,QAAQC,OACd,MAAOF,qBAAoBwW,MAAMvW,SAASuC,OAAO,WAAc,MAAO+oB,YAGtEA,cAQZ9B,0BAA0B5pB,UAAU4S,iBAIpC,SAAUjS,SACN,GAAIgW,OAAQnX,KACSyrB,QAA4BtqB,QAAQwQ,aACzD,IAAI8Z,SAAWA,QAAQ7F,cAAe,CAGlC,GADAzkB,QAAQwQ,cAAgB+T,mBACpB+F,QAAQ9F,YAAa,CACrB3lB,KAAKwpB,uBAAuBroB,QAC5B,IAAqBmnB,IAAKtoB,KAAKqrB,gBAAgBI,QAAQ9F,YACnD2C,KACAA,GAAGL,kBAAkB9mB,SAG7BnB,KAAKypB,mBAAmBtoB,QAASsqB,QAAQ7F,eAEzC5lB,KAAKiB,OAAOiV,eAAe/U,QA5iCf,yBA6iCZnB,KAAK0rB,sBAAsBvqB,SAAS,GAExCnB,KAAKiB,OAAOqV,MAAMnV,QA/iCF,wBA+iC8B,GAAMQ,QAAQ,SAAUkI,MAClEsN,MAAMuU,sBAAsBvqB,SAAS,MAO7CipB,0BAA0B5pB,UAAU2rB,MAIpC,SAAUvC,aACN,GAAIzS,OAAQnX,SACQ,KAAhB4pB,cAA0BA,aAAe,EAC7C,IAAqBhpB,WAKrB,IAJIZ,KAAKqqB,gBAAgBpS,OACrBjY,KAAKqqB,gBAAgB1oB,QAAQ,SAAU2mB,GAAInnB,SAAW,MAAOgW,OAAM6T,sBAAsB1C,GAAInnB,WAC7FnB,KAAKqqB,gBAAgBnP,SAErBlb,KAAKmpB,iBAAmBnpB,KAAK4qB,uBAAuB/pB,OACpD,IAAK,GAAqBgR,GAAI,EAAGA,EAAI7R,KAAK4qB,uBAAuB/pB,OAAQgR,IAAK,CAC1E,GAAqBmE,KAAMhW,KAAK4qB,uBAAuB/Y,EACvDa,UAASsD,IArkCJ,oBAwkCb,GAAIhW,KAAKwqB,eAAe3pB,SACnBb,KAAKynB,oBAAsBznB,KAAK6qB,uBAAuBhqB,QAAS,CACjE,GAAqBurB,cACrB,KACIxrB,QAAUZ,KAAKqsB,iBAAiBD,WAAYxC,aAEhD,QACI,IAAK,GAAqB/X,GAAI,EAAGA,EAAIua,WAAWvrB,OAAQgR,IACpDua,WAAWva,UAKnB,KAAK,GAAqBA,GAAI,EAAGA,EAAI7R,KAAK6qB,uBAAuBhqB,OAAQgR,IAAK,CAC1E,GAAqB1Q,SAAUnB,KAAK6qB,uBAAuBhZ,EAC3D7R,MAAKoT,iBAAiBjS,SAQ9B,GALAnB,KAAKynB,mBAAqB,EAC1BznB,KAAK4qB,uBAAuB/pB,OAAS,EACrCb,KAAK6qB,uBAAuBhqB,OAAS,EACrCb,KAAKyqB,UAAU9oB,QAAQ,SAAU+N,IAAM,MAAOA,QAC9C1P,KAAKyqB,aACDzqB,KAAK0qB,cAAc7pB,OAAQ,CAI3B,GAAqByrB,YAAatsB,KAAK0qB,aACvC1qB,MAAK0qB,iBACD9pB,QAAQC,OACRF,oBAAoBC,SAASuC,OAAO,WAAcmpB,WAAW3qB,QAAQ,SAAU+N,IAAM,MAAOA,UAG5F4c,WAAW3qB,QAAQ,SAAU+N,IAAM,MAAOA,UAQtD0a,0BAA0B5pB,UAAUqnB,YAIpC,SAAUtmB,QACN,KAAM,IAAIkB,OAAM,kFAAoFlB,OAAOmB,KAAK,QAOpH0nB,0BAA0B5pB,UAAU6rB,iBAKpC,SAAUD,WAAYxC,aAClB,GAAIzS,OAAQnX,KACS4rB,aAAe,GAAIhR,uBACnB2R,kBACAC,kBAAoB,GAAItoB,KACxBuoB,sBACApd,gBAAkB,GAAInL,KACtB2P,oBAAsB,GAAI3P,KAC1B4P,qBAAuB,GAAI5P,KAC3BwoB,oBAAsB,GAAIja,IAC/CzS,MAAKsqB,cAAc3oB,QAAQ,SAAUkI,MACjC6iB,oBAAoB7Z,IAAIhJ,KAExB,KAAK,GADgB8iB,sBAAuBxV,MAAMlW,OAAOqV,MAAMzM,KAjpCrD,sBAipC4E,GAC5D+iB,IAAM,EAAGA,IAAMD,qBAAqB9rB,OAAQ+rB,MAClEF,oBAAoB7Z,IAAI8Z,qBAAqBC,OAGrD,IAAqBC,UAAW/nB,cACXgoB,mBAAqB1lB,MAAM2lB,KAAK/sB,KAAK8mB,gBAAgB9kB,QACrDgrB,aAAelb,aAAagb,mBAAoB9sB,KAAK4qB,wBAIrDqC,gBAAkB,GAAI/oB,KACtB2N,EAAI,CACzBmb,cAAarrB,QAAQ,SAAUqQ,MAAOG,MAClC,GAAqBQ,WA7iIX,WA6iIyCd,GACnDob,iBAAgB7oB,IAAI+N,KAAMQ,WAC1BX,MAAMrQ,QAAQ,SAAUkI,MAAQ,MAAO6I,UAAS7I,KAAM8I,cAK1D,KAAK,GAHgBua,kBACAC,iBAAmB,GAAI1a,KACvB2a,4BAA8B,GAAI3a,KAC7B4a,IAAM,EAAGA,IAAMrtB,KAAK6qB,uBAAuBhqB,OAAQwsB,MAAO,CAChF,GAAqBlsB,SAAUnB,KAAK6qB,uBAAuBwC,KACtC5B,QAA4BtqB,QAAQwQ,aACrD8Z,UAAWA,QAAQ7F,gBACnBsH,cAAc1qB,KAAKrB,SACnBgsB,iBAAiBta,IAAI1R,SACjBsqB,QAAQ5F,aACR7lB,KAAKiB,OAAOqV,MAAMnV,QAzqClB,qBAyqC0C,GAAMQ,QAAQ,SAAUqU,KAAO,MAAOmX,kBAAiBta,IAAImD,OAGrGoX,4BAA4Bva,IAAI1R,UAI5C,GAAqBmsB,iBAAkB,GAAIppB,KACtBqpB,aAAezb,aAAagb,mBAAoB1lB,MAAM2lB,KAAKI,kBAChFI,cAAa5rB,QAAQ,SAAUqQ,MAAOG,MAClC,GAAqBQ,WApkIX,WAokIyCd,GACnDyb,iBAAgBlpB,IAAI+N,KAAMQ,WAC1BX,MAAMrQ,QAAQ,SAAUkI,MAAQ,MAAO6I,UAAS7I,KAAM8I,eAE1DyZ,WAAW5pB,KAAK,WACZwqB,aAAarrB,QAAQ,SAAUqQ,MAAOG,MAClC,GAAqBQ,WAA+Bsa,gBAAgB9oB,IAAIgO,KACxEH,OAAMrQ,QAAQ,SAAUkI,MAAQ,MAAOmJ,aAAYnJ,KAAM8I,eAE7D4a,aAAa5rB,QAAQ,SAAUqQ,MAAOG,MAClC,GAAqBQ,WAA+B2a,gBAAgBnpB,IAAIgO,KACxEH,OAAMrQ,QAAQ,SAAUkI,MAAQ,MAAOmJ,aAAYnJ,KAAM8I,eAE7Dua,cAAcvrB,QAAQ,SAAUR,SAAWgW,MAAM/D,iBAAiBjS,YAItE,KAAK,GAFgBqsB,eACAC,wBACKC,IAAM1tB,KAAKwqB,eAAe3pB,OAAS,EAAG6sB,KAAO,EAAGA,MAAO,CACnD1tB,KAAKwqB,eAAekD,KAC3C/D,uBAAuBC,aAAajoB,QAAQ,SAAUqmB,OACrD,GAAqBplB,QAASolB,MAAMplB,MACpC4qB,YAAWhrB,KAAKI,OAChB,IAAqBzB,SAAU6mB,MAAM7mB,OACrC,KAAK0rB,WAAa1V,MAAMlW,OAAOkV,gBAAgB0W,SAAU1rB,SAErD,WADAyB,QAAOiiB,SAGX,IAAqB3W,gBAAoCof,gBAAgBnpB,IAAIhD,SACxD8M,eAAoCgf,gBAAgB9oB,IAAIhD,SACxD+a,YAAiC/E,MAAMwU,kBAAkB3D,MAAO4D,aAAc3d,eAAgBC,eACnH,IAAIgO,YAAY3a,QAAU2a,YAAY3a,OAAOV,OAEzC,WADA4sB,sBAAqBjrB,KAAK0Z,YAK9B,IAAI8L,MAAMR,qBAIN,MAHA5kB,QAAOI,QAAQ,WAAc,MAAO2E,aAAYxG,QAAS+a,YAAYhN,cACrEtM,OAAOQ,UAAU,WAAc,MAAOoE,WAAUrG,QAAS+a,YAAY/M,gBACrEod,gBAAe/pB,KAAKI,OAQxBsZ,aAAY9M,UAAUzN,QAAQ,SAAU+Z,IAAM,MAAOA,IAAGwD,yBAA0B,IAClF0M,aAAa5Q,OAAO7Z,QAAS+a,YAAY9M,UACzC,IAAqBoK,QAAU0C,YAAaA,YAAatZ,OAAQA,OAAQzB,QAASA,QAClFsrB,oBAAmBjqB,KAAKgX,OACxB0C,YAAY7M,gBAAgB1N,QAAQ,SAAUR,SAAW,MAAO0C,iBAAgBwL,gBAAiBlO,YAAaqB,KAAKI,UACnHsZ,YAAYtO,cAAcjM,QAAQ,SAAUgsB,UAAWxsB,SACnD,GAAqBsQ,OAAQhR,OAAOuB,KAAK2rB,UACzC,IAAIlc,MAAM5Q,OAAQ,CACd,GAAqB+sB,UAA8B/Z,oBAAoB1P,IAAIhD,QACtEysB,WACD/Z,oBAAoBzP,IAAIjD,QAASysB,SAAW,GAAInb,MAEpDhB,MAAM9P,QAAQ,SAAUM,MAAQ,MAAO2rB,UAAS/a,IAAI5Q,WAG5Dia,YAAYrO,eAAelM,QAAQ,SAAUgsB,UAAWxsB,SACpD,GAAqBsQ,OAAQhR,OAAOuB,KAAK2rB,WACpBE,OAA4B/Z,qBAAqB3P,IAAIhD,QACrE0sB,SACD/Z,qBAAqB1P,IAAIjD,QAAS0sB,OAAS,GAAIpb,MAEnDhB,MAAM9P,QAAQ,SAAUM,MAAQ,MAAO4rB,QAAOhb,IAAI5Q,YAI9D,GAAIwrB,qBAAqB5sB,OAAQ,CAC7B,GAAqBitB,YACrBL,sBAAqB9rB,QAAQ,SAAUua,aACnC4R,SAAStrB,KAAK,IAAM0Z,YAAY1Y,YAAc,yBAC5C0Y,YAAmB,OAAEva,QAAQ,SAAUosB,OAAS,MAAOD,UAAStrB,KAAK,KAAOurB,MAAQ,UAE1FP,WAAW7rB,QAAQ,SAAUiB,QAAU,MAAOA,QAAOiiB,YACrD7kB,KAAK6nB,YAAYiG,UAErB,GAAqBE,uBAAwB,GAAI9pB,KAK5B+pB,oBAAsB,GAAI/pB,IAC/CuoB,oBAAmB9qB,QAAQ,SAAUqmB,OACjC,GAAqB7mB,SAAU6mB,MAAM7mB,OACjCyqB,cAAa/f,IAAI1K,WACjB8sB,oBAAoB7pB,IAAIjD,QAASA,SACjCgW,MAAM+W,sBAAsBlG,MAAMplB,OAAO+iB,YAAaqC,MAAM9L,YAAa8R,0BAGjFzB,eAAe5qB,QAAQ,SAAUiB,QAC7B,GAAqBzB,SAAUyB,OAAOzB,OACCgW,OAAMgX,oBAAoBhtB,SAAS,EAAOyB,OAAO+iB,YAAa/iB,OAAOY,YAAa,MACzG7B,QAAQ,SAAUysB,YAC9BvqB,gBAAgBmqB,sBAAuB7sB,YAAaqB,KAAK4rB,YACzDA,WAAWvJ,aAUnB,IAAqBwJ,cAAenB,cAAc3R,OAAO,SAAU1R,MAC/D,MAAO+J,wBAAuB/J,KAAMgK,oBAAqBC,wBAGxCwa,cAAgB,GAAIpqB,IACGgN,uBAAsBod,cAAetuB,KAAKiB,OAAQmsB,4BAA6BtZ,qBAAsB7T,oBAAoBqC,YAChJX,QAAQ,SAAUkI,MAC/B+J,uBAAuB/J,KAAMgK,oBAAqBC,uBAClDua,aAAa7rB,KAAKqH,OAI1B,IAAqB0kB,cAAe,GAAIrqB,IACxC8oB,cAAarrB,QAAQ,SAAUqQ,MAAOG,MAClCjB,sBAAsBqd,aAAcpX,MAAMlW,OAAQ,GAAIwR,KAAIT,OAAQ6B,oBAAqB5T,oBAAoBoC,cAE/GgsB,aAAa1sB,QAAQ,SAAUkI,MAC3B,GAAqB2kB,MAAOF,cAAcnqB,IAAI0F,MACzB4kB,IAAMF,aAAapqB,IAAI0F,KAC5CykB,eAAclqB,IAAIyF,KAAwB4K,YAAa+Z,KAAMC,OAEjE,IAAqBC,gBACAC,cACAC,uCACrBnC,oBAAmB9qB,QAAQ,SAAUqmB,OACjC,GAAI7mB,SAAU6mB,MAAM7mB,QAASyB,OAASolB,MAAMplB,OAAQsZ,YAAc8L,MAAM9L,WAGxE,IAAI0P,aAAa/f,IAAI1K,SAAU,CAC3B,GAAIurB,oBAAoB7gB,IAAI1K,SAGxB,MAFAyB,QAAOQ,UAAU,WAAc,MAAOoE,WAAUrG,QAAS+a,YAAY/M,gBACrEod,gBAAe/pB,KAAKI,OASxB,IAAqBisB,uBAAwBD,oCAC7C,IAAIX,oBAAoBhW,KAAO,EAAG,CAG9B,IAFA,GAAqBjC,KAAM7U,QACN2tB,gBACd9Y,IAAMA,IAAI1D,YAAY,CACzB,GAAqByc,gBAAiBd,oBAAoB9pB,IAAI6R,IAC9D,IAAI+Y,eAAgB,CAChBF,sBAAwBE,cACxB,OAEJD,aAAatsB,KAAKwT,KAEtB8Y,aAAantB,QAAQ,SAAU0Q,QAAU,MAAO4b,qBAAoB7pB,IAAIiO,OAAQwc,yBAEpF,GAAqBG,aAAc7X,MAAM8X,gBAAgBrsB,OAAO+iB,YAAazJ,YAAa8R,sBAAuBxB,kBAAmB+B,aAAcD,cAElJ,IADA1rB,OAAOssB,cAAcF,aACjBH,wBAA0BD,qCAC1BF,YAAYlsB,KAAKI,YAEhB,CACD,GAAqBusB,eAAgBhY,MAAMmQ,iBAAiBnjB,IAAI0qB,sBAC5DM,gBAAiBA,cAActuB,SAC/B+B,OAAOwsB,aAAezuB,oBAAoBwuB,gBAE9C5C,eAAe/pB,KAAKI,aAIxB+E,aAAYxG,QAAS+a,YAAYhN,YACjCtM,OAAOQ,UAAU,WAAc,MAAOoE,WAAUrG,QAAS+a,YAAY/M,YAIrEwf,WAAWnsB,KAAKI,QACZ8pB,oBAAoB7gB,IAAI1K,UACxBorB,eAAe/pB,KAAKI,UAKhC+rB,WAAWhtB,QAAQ,SAAUiB,QAGzB,GAAqBysB,mBAAoB7C,kBAAkBroB,IAAIvB,OAAOzB,QACtE,IAAIkuB,mBAAqBA,kBAAkBxuB,OAAQ,CAC/C,GAAqBmuB,aAAcruB,oBAAoB0uB,kBACvDzsB,QAAOssB,cAAcF,gBAM7BzC,eAAe5qB,QAAQ,SAAUiB,QACzBA,OAAOwsB,aACPxsB,OAAO0sB,iBAAiB1sB,OAAOwsB,cAG/BxsB,OAAOiiB,WAMf,KAAK,GAAqB0K,KAAM,EAAGA,IAAMrC,cAAcrsB,OAAQ0uB,MAAO,CAClE,GAAqBpuB,SAAU+rB,cAAcqC,KACxB9D,QAA4BtqB,QAAQwQ,aAKzD,IAJAqB,YAAY7R,QA3xIF,aA+xINsqB,UAAWA,QAAQ5F,aAAvB,CAEA,GAAqBjlB,WAIrB,IAAIyO,gBAAgB4I,KAAM,CACtB,GAAqBuX,sBAAuBngB,gBAAgBlL,IAAIhD,QAC5DquB,uBAAwBA,qBAAqB3uB,QAC7CD,QAAQ4B,KAAKqT,MAAMjV,QAAS4uB,qBAGhC,KAAK,GADgBC,sBAAuBzvB,KAAKiB,OAAOqV,MAAMnV,QAAS2L,uBAAuB,GACpE4iB,EAAI,EAAGA,EAAID,qBAAqB5uB,OAAQ6uB,IAAK,CACnE,GAAqBC,gBAAiBtgB,gBAAgBlL,IAAIsrB,qBAAqBC,GAC3EC,iBAAkBA,eAAe9uB,QACjCD,QAAQ4B,KAAKqT,MAAMjV,QAAS+uB,iBAIxC,GAAqBC,eAAgBhvB,QAAQ2a,OAAO,SAAU/G,GAAK,OAAQA,EAAEqV,WACzE+F,eAAc/uB,OACdqS,8BAA8BlT,KAAMmB,QAASyuB,eAG7C5vB,KAAKoT,iBAAiBjS,UAc9B,MAVA+rB,eAAcrsB,OAAS,EACvB6tB,YAAY/sB,QAAQ,SAAUiB,QAC1BuU,MAAMvW,QAAQ4B,KAAKI,QACnBA,OAAOO,OAAO,WACVP,OAAOiiB,SACP,IAAqBnU,OAAQyG,MAAMvW,QAAQ4D,QAAQ5B,OACnDuU,OAAMvW,QAAQiG,OAAO6J,MAAO,KAEhC9N,OAAOsiB,SAEJwJ,aAOXtE,0BAA0B5pB,UAAU0pB,oBAKpC,SAAUvE,YAAaxkB,SACnB,GAAqBgpB,eAAe,EACfsB,QAA4BtqB,QAAQwQ,aASzD,OARI8Z,UAAWA,QAAQ7F,gBACnBuE,cAAe,GACfnqB,KAAKsnB,iBAAiBzb,IAAI1K,WAC1BgpB,cAAe,GACfnqB,KAAKqpB,wBAAwBxd,IAAI1K,WACjCgpB,cAAe,GACfnqB,KAAK8mB,gBAAgBjb,IAAI1K,WACzBgpB,cAAe,GACZnqB,KAAKqrB,gBAAgB1F,aAAauE,oBAAoB/oB,UAAYgpB,cAM7EC,0BAA0B5pB,UAAUwmB,WAIpC,SAAUjkB,UAAY/C,KAAKyqB,UAAUjoB,KAAKO,WAK1CqnB,0BAA0B5pB,UAAU8qB,yBAIpC,SAAUvoB,UAAY/C,KAAK0qB,cAAcloB,KAAKO,WAS9CqnB,0BAA0B5pB,UAAU2tB,oBAQpC,SAAUhtB,QAAS0uB,iBAAkBlK,YAAaniB,YAAassB,cAC3D,GAAqBlvB,WACrB,IAAIivB,iBAAkB,CAClB,GAAqBE,uBAAwB/vB,KAAKqpB,wBAAwBllB,IAAIhD,QAC1E4uB,yBACAnvB,QAAUmvB,2BAGb,CACD,GAAqB7H,gBAAiBloB,KAAKsnB,iBAAiBnjB,IAAIhD,QAChE,IAAI+mB,eAAgB,CAChB,GAAqB8H,uBAAwBF,cAn7C5C,QAm7C4DA,YAC7D5H,gBAAevmB,QAAQ,SAAUiB,QACzBA,OAAO2kB,SAENyI,sBAAwBptB,OAAOY,aAAeA,cAEnD5C,QAAQ4B,KAAKI,WAazB,OATI+iB,aAAeniB,eACf5C,QAAUA,QAAQ2a,OAAO,SAAU3Y,QAC/B,QAAI+iB,aAAeA,aAAe/iB,OAAO+iB,gBAErCniB,aAAeA,aAAeZ,OAAOY,gBAK1C5C,SAQXwpB,0BAA0B5pB,UAAU0tB,sBAMpC,SAAUvI,YAAazJ,YAAa8R,uBAsBhC,IAAK,GArBgBxqB,aAAc0Y,YAAY1Y,YAC1BwK,YAAckO,YAAY/a,QAG1B8uB,kBAAoB/T,YAAYjN,wBAAsBtL,GAAYgiB,YAClEuK,kBAAoBhU,YAAYjN,wBAAsBtL,GAAYH,YAenF2sB,OAASnwB,KACJwJ,GAAK,EAAG+Q,GAAK2B,YAAY9M,UAAW5F,GAAK+Q,GAAG1Z,OAAQ2I,KAAM,CAC/D,GAAI4mB,qBAAsB7V,GAAG/Q,KAhBnB,SAAU4mB,qBACpB,GAAqBjvB,SAAUivB,oBAAoBjvB,QAC9B0uB,iBAAmB1uB,UAAY6M,YAC/BpN,QAAUiD,gBAAgBmqB,sBAAuB7sB,WAC/BgvB,QAAOhC,oBAAoBhtB,QAAS0uB,iBAAkBI,kBAAmBC,kBAAmBhU,YAAYxY,SAC/H/B,QAAQ,SAAUiB,QAC9B,GAAqBytB,YAA+BztB,OAAO0tB,eACvDD,YAAWE,eACXF,WAAWE,gBAEf3tB,OAAOiiB,UACPjkB,QAAQ4B,KAAKI,WAMTwtB,qBAIZzoB,YAAYqG,YAAakO,YAAYhN,aAWzCkb,0BAA0B5pB,UAAUyuB,gBASpC,SAAUtJ,YAAazJ,YAAa8R,sBAAuBxB,kBAAmB+B,aAAcD,eACxF,GAAInX,OAAQnX,KACSwD,YAAc0Y,YAAY1Y,YAC1BwK,YAAckO,YAAY/a,QAG1BqvB,qBACAC,oBAAsB,GAAIhe,KAC1Bie,eAAiB,GAAIje,KACrBke,cAAgBzU,YAAY9M,UAAUtL,IAAI,SAAUssB,qBACrE,GAAqBjvB,SAAUivB,oBAAoBjvB,OACnDsvB,qBAAoB5d,IAAI1R,QAExB,IAAqBsqB,SAAUtqB,QAAQwQ,aACvC,IAAI8Z,SAAWA,QAAQ3F,qBACnB,MAAO,IAAI7lB,qBAAoBa,mBACnC,IAAqB+uB,kBAAmB1uB,UAAY6M,YAC/BwI,gBAAkBnD,qBAAqB2a,sBAAsB7pB,IAAIhD,UAAYskB,oBAC7F3hB,IAAI,SAAU0Q,GAAK,MAAOA,GAAE8b,mBAC5B/U,OAAO,SAAU/G,GAKlB,GAAqBoc,IAAsB,CAC3C,SAAOA,GAAGzvB,SAAUyvB,GAAGzvB,UAAYA,UAElBE,UAAYktB,aAAapqB,IAAIhD,SAC7BG,WAAagtB,cAAcnqB,IAAIhD,SAC/BC,UAAYJ,mBAAmBmW,MAAMlW,OAAQkW,MAAMkN,YAAaljB,QAASivB,oBAAoBhvB,UAAWC,UAAWC,YACnHsB,OAASuU,MAAMuN,aAAa0L,oBAAqBhvB,UAAWoV,gBAMjF,IAHI4Z,oBAAoBtiB,aAAe0e,mBACnCkE,eAAe7d,IAAI1R,SAEnB0uB,iBAAkB,CAClB,GAAqBgB,eAAgB,GAAIzJ,2BAA0BzB,YAAaniB,YAAarC,QAC7F0vB,eAAc3B,cAActsB,QAC5B4tB,kBAAkBhuB,KAAKquB,eAE3B,MAAOjuB,SAEX4tB,mBAAkB7uB,QAAQ,SAAUiB,QAChCiB,gBAAgBsT,MAAMkS,wBAAyBzmB,OAAOzB,YAAaqB,KAAKI,QACxEA,OAAOO,OAAO,WAAc,MAAOqN,oBAAmB2G,MAAMkS,wBAAyBzmB,OAAOzB,QAASyB,YAEzG6tB,oBAAoB9uB,QAAQ,SAAUR,SAAW,MAAOuR,UAASvR,QAtgJ5C,iBAugJrB,IAAqByB,QAASjC,oBAAoBgwB,cAQlD,OAPA/tB,QAAOQ,UAAU,WACbqtB,oBAAoB9uB,QAAQ,SAAUR,SAAW,MAAO6R,aAAY7R,QAzgJnD,kBA0gJjBqG,UAAUwG,YAAakO,YAAY/M,YAIvCuhB,eAAe/uB,QAAQ,SAAUR,SAAW0C,gBAAgB2oB,kBAAmBrrB,YAAaqB,KAAKI,UAC1FA,QAQXwnB,0BAA0B5pB,UAAUkkB,aAMpC,SAAUxI,YAAa9a,UAAWoV,iBAC9B,MAAIpV,WAAUP,OAAS,EACZb,KAAKiB,OAAOsV,QAAQ2F,YAAY/a,QAASC,UAAW8a,YAAY/V,SAAU+V,YAAY7V,MAAO6V,YAAY5V,OAAQkQ,iBAIrH,GAAIvW,qBAAoBa,qBAE5BspB,6BAEPhD,0BAA2C,WAC3C,QAASA,2BAA0BzB,YAAaniB,YAAarC,SACzDnB,KAAK2lB,YAAcA,YACnB3lB,KAAKwD,YAAcA,YACnBxD,KAAKmB,QAAUA,QACfnB,KAAK8wB,QAAU,GAAI7wB,qBAAoBa,oBACvCd,KAAK+wB,qBAAsB,EAC3B/wB,KAAKgxB,oBACLhxB,KAAK6pB,WAAY,EACjB7pB,KAAK8pB,kBAAmB,EACxB9pB,KAAKunB,QAAS,EA2MlB,MArMAH,2BAA0B5mB,UAAU0uB,cAIpC,SAAUtsB,QACN,GAAIuU,OAAQnX,IACRA,MAAK+wB,sBAET/wB,KAAK8wB,QAAUluB,OACfnC,OAAOuB,KAAKhC,KAAKgxB,kBAAkBrvB,QAAQ,SAAUglB,OACjDxP,MAAM6Z,iBAAiBrK,OAAOhlB,QAAQ,SAAUoB,UAAY,MAAOJ,gBAAeC,OAAQ+jB,UAAOhjB,GAAWZ,cAEhH/C,KAAKgxB,oBACLhxB,KAAK+wB,qBAAsB,EAC3B,KAA0BxJ,QAAS,IAKvCH,0BAA0B5mB,UAAU8vB,cAGpC,WAAc,MAAOtwB,MAAK8wB,SAK1B1J,0BAA0B5mB,UAAU8uB,iBAIpC,SAAU1sB,QACN,GAAIuU,OAAQnX,KACSwU,EAAsBxU,KAAY,OACnDwU,GAAEyc,iBACFruB,OAAOI,QAAQ,WAAc,MAAOwR,GAAEyc,gBAAgB,WAE1DruB,OAAOO,OAAO,WAAc,MAAOgU,OAAMmO,WACzC1iB,OAAOQ,UAAU,WAAc,MAAO+T,OAAM0N,aAOhDuC,0BAA0B5mB,UAAU0wB,YAKpC,SAAUthB,KAAM7M,UACZc,gBAAgB7D,KAAKgxB,iBAAkBphB,SAAUpN,KAAKO,WAM1DqkB,0BAA0B5mB,UAAU2C,OAIpC,SAAUuM,IACF1P,KAAKunB,QACLvnB,KAAKkxB,YAAY,OAAQxhB,IAE7B1P,KAAK8wB,QAAQ3tB,OAAOuM,KAMxB0X,0BAA0B5mB,UAAUwC,QAIpC,SAAU0M,IACF1P,KAAKunB,QACLvnB,KAAKkxB,YAAY,QAASxhB,IAE9B1P,KAAK8wB,QAAQ9tB,QAAQ0M,KAMzB0X,0BAA0B5mB,UAAU4C,UAIpC,SAAUsM,IACF1P,KAAKunB,QACLvnB,KAAKkxB,YAAY,UAAWxhB,IAEhC1P,KAAK8wB,QAAQ1tB,UAAUsM,KAK3B0X,0BAA0B5mB,UAAU+kB,KAGpC,WAAcvlB,KAAK8wB,QAAQvL,QAI3B6B,0BAA0B5mB,UAAU2wB,WAGpC,WAAc,OAAOnxB,KAAKunB,QAAiBvnB,KAAK8wB,QAAQK,cAIxD/J,0BAA0B5mB,UAAU0kB,KAGpC,YAAellB,KAAKunB,QAAUvnB,KAAK8wB,QAAQ5L,QAI3CkC,0BAA0B5mB,UAAU2kB,MAGpC,YAAenlB,KAAKunB,QAAUvnB,KAAK8wB,QAAQ3L,SAI3CiC,0BAA0B5mB,UAAU6kB,QAGpC,YAAerlB,KAAKunB,QAAUvnB,KAAK8wB,QAAQzL,WAI3C+B,0BAA0B5mB,UAAU8kB,OAGpC,WAActlB,KAAK8wB,QAAQxL,UAI3B8B,0BAA0B5mB,UAAUqkB,QAGpC,WACI,KAA0BgF,WAAY,EACtC7pB,KAAK8wB,QAAQjM,WAKjBuC,0BAA0B5mB,UAAU4kB,MAGpC,YAAeplB,KAAKunB,QAAUvnB,KAAK8wB,QAAQ1L,SAK3CgC,0BAA0B5mB,UAAUglB,YAIpC,SAAUhR,GACDxU,KAAKunB,QACNvnB,KAAK8wB,QAAQtL,YAAYhR,IAMjC4S,0BAA0B5mB,UAAU4wB,YAGpC;2DAAc,MAAOpxB,MAAKunB,OAAS,EAAIvnB,KAAK8wB,QAAQM,eACpD3wB,OAAOyd,eAAekJ,0BAA0B5mB,UAAW,aACvD2D,IAGA,WAAc,MAAOnE,MAAK8wB,QAAQ5tB,WAClCib,YAAY,EACZC,cAAc,IAOlBgJ,0BAA0B5mB,UAAUywB,gBAIpC,SAAU3tB,WACN,GAAqBkR,GAAsBxU,KAAY,OACnDwU,GAAEyc,iBACFzc,EAAEyc,gBAAgB3tB,YAGnB8jB,6BAoJPrU,kBAAoB,YA8GpBse,gBAAiC,WACjC,QAASA,iBAAgBxa,QAAS3V,YAC9B,GAAIiW,OAAQnX,IACZA,MAAK6W,QAAUA,QACf7W,KAAKsxB,iBACLtxB,KAAK8qB,kBAAoB,SAAU3pB,QAAS2I,WAC5C9J,KAAKuxB,kBAAoB,GAAInH,2BAA0BvT,QAAS3V,YAChElB,KAAKwxB,gBAAkB,GAAIpN,yBAAwBvN,QAAS3V,YAC5DlB,KAAKuxB,kBAAkBzG,kBAAoB,SAAU3pB,QAAS2I,SAC1D,MAAOqN,OAAM2T,kBAAkB3pB,QAAS2I,UAsLhD,MA3KAunB,iBAAgB7wB,UAAU4qB,gBAQ1B,SAAUqG,YAAa9L,YAAaU,YAAazW,KAAMzD,UACnD,GAAqBulB,UAAWD,YAAc,IAAM7hB,KAC/BsX,QAAUlnB,KAAKsxB,cAAcI,SAClD,KAAKxK,QAAS,CACV,GAAqB3lB,WACAiM,IAAwBtB,kBAAkBlM,KAAK6W,QAA0B,SAAYtV,OAC1G,IAAIA,OAAOV,OACP,KAAM,IAAI4B,OAAM,0BAA6BmN,KAAO,0DAA6DrO,OAAOmB,KAAK,SAEjIwkB,SAAUvX,aAAaC,KAAMpC,KAC7BxN,KAAKsxB,cAAcI,UAAYxK,QAEnClnB,KAAKuxB,kBAAkBnG,gBAAgBzF,YAAa/V,KAAMsX,UAO9DmK,gBAAgB7wB,UAAUgkB,SAK1B,SAAUmB,YAAaU,aACnBrmB,KAAKuxB,kBAAkB/M,SAASmB,YAAaU,cAOjDgL,gBAAgB7wB,UAAUqkB,QAK1B,SAAUc,YAAa7b,SACnB9J,KAAKuxB,kBAAkB1M,QAAQc,YAAa7b,UAShDunB,gBAAgB7wB,UAAUmxB,SAO1B,SAAUhM,YAAaxkB,QAASkR,OAAQmZ,cACpCxrB,KAAKuxB,kBAAkB7H,WAAW/D,YAAaxkB,QAASkR,OAAQmZ,eAQpE6F,gBAAgB7wB,UAAUoxB,SAM1B,SAAUjM,YAAaxkB,QAAS2I,SAC5B9J,KAAKuxB,kBAAkBvI,WAAWrD,YAAaxkB,QAAS2I,UAO5DunB,gBAAgB7wB,UAAUqxB,kBAK1B,SAAU1wB,QAAS2wB,SACf9xB,KAAKuxB,kBAAkB7F,sBAAsBvqB,QAAS2wB,UAS1DT,gBAAgB7wB,UAAUuxB,QAO1B,SAAUpM,YAAaxkB,QAAS6wB,SAAU/tB,OACtC,GAA0B,KAAtB+tB,SAAS9sB,OAAO,GAAW,CAC3B,GAAIqV,IAAKlW,qBAAqB2tB,UAAWvN,GAAKlK,GAAG,GAAI0X,OAAS1X,GAAG,GAC5C0K,KAAwB,KAC7CjlB,MAAKwxB,gBAAgBltB,QAAQmgB,GAAItjB,QAAS8wB,OAAQhN,UAGlDjlB,MAAKuxB,kBAAkBrK,QAAQvB,YAAaxkB,QAAS6wB,SAAU/tB,QAWvEotB,gBAAgB7wB,UAAUukB,OAQ1B,SAAUY,YAAaxkB,QAAS0B,UAAWqvB,WAAYnvB,UAEnD,GAA2B,KAAvBF,UAAUqC,OAAO,GAAW,CAC5B,GAAIqV,IAAKlW,qBAAqBxB,WAAY4hB,GAAKlK,GAAG,GAAI0X,OAAS1X,GAAG,EAClE,OAAOva,MAAKwxB,gBAAgBzM,OAAON,GAAItjB,QAAS8wB,OAAQlvB,UAE5D,MAAO/C,MAAKuxB,kBAAkBxM,OAAOY,YAAaxkB,QAAS0B,UAAWqvB,WAAYnvB,WAMtFsuB,gBAAgB7wB,UAAU2rB,MAI1B,SAAUvC,iBACc,KAAhBA,cAA0BA,aAAe,GAC7C5pB,KAAKuxB,kBAAkBpF,MAAMvC,cAEjCnpB,OAAOyd,eAAemT,gBAAgB7wB,UAAW,WAC7C2D,IAGA,WACI,MAA0BnE,MAAKuxB,kBAA0B,QACpDY,OAAyBnyB,KAAKwxB,gBAAuB,UAE9DrT,YAAY,EACZC,cAAc,IAKlBiT,gBAAgB7wB,UAAUwrB,kBAG1B,WAAc,MAAOhsB,MAAKuxB,kBAAkBvF,qBACrCqF,mBAOPe,oBAAqC,WACrC,QAASA,qBAAoBjxB,QAASC,UAAW4G,QAASwO,qBAC9B,KAApBA,kBAA8BA,mBAClC,IAAIW,OAAQnX,IACZA,MAAKmB,QAAUA,QACfnB,KAAKoB,UAAYA,UACjBpB,KAAKgI,QAAUA,QACfhI,KAAKwW,gBAAkBA,gBACvBxW,KAAKqyB,cACLryB,KAAKsyB,eACLtyB,KAAKuyB,iBACLvyB,KAAKwyB,cAAe,EACpBxyB,KAAKyyB,WAAY,EACjBzyB,KAAK0yB,UAAW,EAChB1yB,KAAK2yB,YAAa,EAClB3yB,KAAKmf,KAAO,EACZnf,KAAKovB,aAAe,KACpBpvB,KAAK4yB,kBACL5yB,KAAK6yB,mBACL7yB,KAAK8yB,UAA8B9qB,QAAmB,SACtDhI,KAAK+yB,OAA2B/qB,QAAgB,OAAK,EACrDhI,KAAKmf,KAAOnf,KAAK8yB,UAAY9yB,KAAK+yB,OAC9BrpB,+BAA+B1J,KAAK8yB,UAAW9yB,KAAK+yB,SACpDvc,gBAAgB7U,QAAQ,SAAUiB,QAC9B,GAAqBsE,QAAStE,OAAOiwB,eACrCpyB,QAAOuB,KAAKkF,QAAQvF,QAAQ,SAAUM,MAAQ,MAAOkV,OAAMyb,eAAe3wB,MAAQiF,OAAOjF,UAsRrG,MA/QAmwB,qBAAoB5xB,UAAUwyB,UAG9B,WACShzB,KAAKyyB,YACNzyB,KAAKyyB,WAAY,EACjBzyB,KAAKqyB,WAAW1wB,QAAQ,SAAU+N,IAAM,MAAOA,QAC/C1P,KAAKqyB,gBAMbD,oBAAoB5xB,UAAU+kB,KAG9B,WACIvlB,KAAK0kB,eACL1kB,KAAKizB,6BAKTb,oBAAoB5xB,UAAUkkB,aAG9B,WACI,GAAIvN,OAAQnX,IACZ,KAAIA,KAAKwyB,aAAT,CAEAxyB,KAAKwyB,cAAe,CACpB,IAAqBpxB,WAAYpB,KAAKoB,UAAU0C,IAAI,SAAUoD,QAAU,MAAOI,YAAWJ,QAAQ,KAC7EgsB,mBAAqBzyB,OAAOuB,KAAKhC,KAAK4yB,eAC3D,IAAIM,mBAAmBryB,QAAUO,UAAUP,OAAQ,CAC/C,GAAqBsyB,oBAAqB/xB,UAAU,GAC/BgyB,sBAOrB,IANAF,mBAAmBvxB,QAAQ,SAAUM,MAC5BkxB,mBAAmBntB,eAAe/D,OACnCmxB,oBAAoB5wB,KAAKP,MAE7BkxB,mBAAmBlxB,MAAQkV,MAAMyb,eAAe3wB,QAEhDmxB,oBAAoBvyB,OASpB,IAAK,GARgBwyB,QAASrzB,KAQJ6R,EAAI,EAAGA,EAAIzQ,UAAUP,OAAQgR,KAPzC,WACV,GAAqBjQ,IAAKR,UAAUyQ,EACpCuhB,qBAAoBzxB,QAAQ,SAAUM,MAClCL,GAAGK,MAAQgS,cAAcof,OAAOlyB,QAASc,WASzD,KAA0BqxB,UACtBtzB,KAAKuzB,qBAAqBvzB,KAAKmB,QAASC,UAAWpB,KAAKgI,SAC5DhI,KAAKwzB,eAAiBpyB,UAAUP,OAASO,UAAUA,UAAUP,OAAS,MACtEb,KAAKszB,UAAUG,iBAAiB,SAAU,WAAc,MAAOtc,OAAM6b,gBAKzEZ,oBAAoB5xB,UAAUyyB,0BAG9B,WAEQjzB,KAAK+yB,OACL/yB,KAAK0zB,uBAGL1zB,KAAKszB,UAAUnO,SAWvBiN,oBAAoB5xB,UAAU+yB,qBAO9B,SAAUpyB,QAASC,UAAW4G,SAG1B,MAAyB7G,SAAiB,QAAEC,UAAW4G,UAM3DoqB,oBAAoB5xB,UAAUwC,QAI9B,SAAU0M,IAAM1P,KAAKsyB,YAAY9vB,KAAKkN,KAKtC0iB,oBAAoB5xB,UAAU2C,OAI9B,SAAUuM,IAAM1P,KAAKqyB,WAAW7vB,KAAKkN,KAKrC0iB,oBAAoB5xB,UAAU4C,UAI9B,SAAUsM,IAAM1P,KAAKuyB,cAAc/vB,KAAKkN,KAIxC0iB,oBAAoB5xB,UAAU0kB,KAG9B,WACIllB,KAAK0kB,eACA1kB,KAAKmxB,eACNnxB,KAAKsyB,YAAY3wB,QAAQ,SAAU+N,IAAM,MAAOA,QAChD1P,KAAKsyB,eACLtyB,KAAK0yB,UAAW,GAEpB1yB,KAAKszB,UAAUpO,QAKnBkN,oBAAoB5xB,UAAU2kB,MAG9B,WACInlB,KAAKulB,OACLvlB,KAAKszB,UAAUnO,SAKnBiN,oBAAoB5xB,UAAU8kB,OAG9B,WACItlB,KAAKulB,OACLvlB,KAAKgzB,YACLhzB,KAAKszB,UAAUhO,UAKnB8M,oBAAoB5xB,UAAU4kB,MAG9B,WACIplB,KAAK0zB,uBACL1zB,KAAK2yB,YAAa,EAClB3yB,KAAKyyB,WAAY,EACjBzyB,KAAK0yB,UAAW,GAKpBN,oBAAoB5xB,UAAUkzB,qBAG9B,WACQ1zB,KAAKszB,WACLtzB,KAAKszB,UAAUK,UAMvBvB,oBAAoB5xB,UAAU6kB,QAG9B,WACIrlB,KAAKolB,QACLplB,KAAKklB,QAKTkN,oBAAoB5xB,UAAU2wB,WAG9B,WAAc,MAAOnxB,MAAK0yB,UAI1BN,oBAAoB5xB,UAAUqkB,QAG9B,WACS7kB,KAAK2yB,aACN3yB,KAAK2yB,YAAa,EAClB3yB,KAAK0zB,uBACL1zB,KAAKgzB,YACLhzB,KAAKuyB,cAAc5wB,QAAQ,SAAU+N,IAAM,MAAOA,QAClD1P,KAAKuyB,mBAObH,oBAAoB5xB,UAAUglB,YAI9B,SAAUhR,GAAKxU,KAAKszB,UAAUpc,YAAc1C,EAAIxU,KAAKmf,MAIrDiT,oBAAoB5xB,UAAU4wB,YAG9B,WAAc,MAAOpxB,MAAKszB,UAAUpc,YAAclX,KAAKmf,MACvD1e,OAAOyd,eAAekU,oBAAoB5xB,UAAW,aACjD2D,IAGA,WAAc,MAAOnE,MAAK+yB,OAAS/yB,KAAK8yB,WACxC3U,YAAY,EACZC,cAAc,IAKlBgU,oBAAoB5xB,UAAU+vB,cAG9B,WACI,GAAIpZ,OAAQnX,KACSkH,SACjBlH,MAAKmxB,cACL1wB,OAAOuB,KAAKhC,KAAKwzB,gBAAgB7xB,QAAQ,SAAUM,MACnC,UAARA,OACAiF,OAAOjF,MACHkV,MAAMsb,UAAYtb,MAAMqc,eAAevxB,MAAQgS,cAAckD,MAAMhW,QAASc,SAI5FjC,KAAK6yB,gBAAkB3rB,QAO3BkrB,oBAAoB5xB,UAAUywB,gBAI9B,SAAU3tB,WACN,GAAqBswB,SAAuB,SAAbtwB,UAAuBtD,KAAKsyB,YAActyB,KAAKqyB,UAC9EuB,SAAQjyB,QAAQ,SAAU+N,IAAM,MAAOA,QACvCkkB,QAAQ/yB,OAAS,GAEduxB,uBAePyB,oBAAqC,WACrC,QAASA,wBA+FT,MAzFAA,qBAAoBrzB,UAAUoE,sBAI9B,SAAU3C,MAAQ,MAAO2C,uBAAsB3C,OAM/C4xB,oBAAoBrzB,UAAU0V,eAK9B,SAAU/U,QAASoL,UACf,MAAO2J,gBAAe/U,QAASoL,WAOnCsnB,oBAAoBrzB,UAAU2V,gBAK9B,SAAUnB,KAAMC,MAAQ,MAAOkB,iBAAgBnB,KAAMC,OAOrD4e,oBAAoBrzB,UAAU8V,MAM9B,SAAUnV,QAASoL,SAAU6I,OACzB,MAAOgB,aAAYjV,QAASoL,SAAU6I,QAQ1Cye,oBAAoBrzB,UAAUkR,aAM9B,SAAUvQ,QAASc,KAAM+B,cACrB,MAA4CkQ,QAAOC,iBAAiBhT,SAAWc,OAWnF4xB,oBAAoBrzB,UAAU+V,QAS9B,SAAUpV,QAASC,UAAW+E,SAAUE,MAAOC,OAAQkQ,qBAC3B,KAApBA,kBAA8BA,mBAClC,IAAqBsd,MAAgB,GAATztB,MAAa,OAAS,WAC7B0tB,eAAkB5tB,SAAUA,SAAUE,MAAOA,MAAOytB,KAAMA,KAG3ExtB,UACAytB,cAAsB,OAAIztB,OAE9B,IAAqB0tB,6BAAgDxd,gBAAgB+E,OAAO,SAAU3Y,QAAU,MAAOA,kBAAkBwvB,sBACzI,OAAO,IAAIA,qBAAoBjxB,QAASC,UAAW2yB,cAAeC,8BAE/DH,sBASXr0B,SAAQiX,gBAAkBA,gBAC1BjX,QAAQy0B,WAAa5S,UACrB7hB,QAAQ00B,0BAA4BtS,yBACpCpiB,QAAQ20B,8BAAgCtS,6BACxCriB,QAAQ40B,8BAAgCnS,6BACxCziB,QAAQ60B,qBAAuBhe,oBAC/B7W,QAAQ80B,iBAAmBjD,gBAC3B7xB,QAAQ+0B,qBAAuBV,oBAC/Br0B,QAAQg1B,uBAAyBpgB,sBACjC5U,QAAQi1B,qBAAuBrC,oBAE/B3xB,OAAOyd,eAAe1e,QAAS,cAAgByE,OAAO", "file": "animations-browser.umd.min.js", "sourcesContent": ["/**\n * @license Angular v5.2.11\n * (c) 2010-2018 Google, Inc. https://angular.io/\n * License: MIT\n */\n(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('@angular/animations')) :\n\ttypeof define === 'function' && define.amd ? define('@angular/animations/browser', ['exports', '@angular/animations'], factory) :\n\t(factory((global.ng = global.ng || {}, global.ng.animations = global.ng.animations || {}, global.ng.animations.browser = {}),global.ng.animations));\n}(this, (function (exports,_angular_animations) { 'use strict';\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n};\n\n/**\n * @license Angular v5.2.11\n * (c) 2010-2018 Google, Inc. https://angular.io/\n * License: MIT\n */\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @param {?} players\n * @return {?}\n */\nfunction optimizeGroupPlayer(players) {\n    switch (players.length) {\n        case 0:\n            return new _angular_animations.NoopAnimationPlayer();\n        case 1:\n            return players[0];\n        default:\n            return new _angular_animations.ɵAnimationGroupPlayer(players);\n    }\n}\n/**\n * @param {?} driver\n * @param {?} normalizer\n * @param {?} element\n * @param {?} keyframes\n * @param {?=} preStyles\n * @param {?=} postStyles\n * @return {?}\n */\nfunction normalizeKeyframes(driver, normalizer, element, keyframes, preStyles, postStyles) {\n    if (preStyles === void 0) { preStyles = {}; }\n    if (postStyles === void 0) { postStyles = {}; }\n    var /** @type {?} */ errors = [];\n    var /** @type {?} */ normalizedKeyframes = [];\n    var /** @type {?} */ previousOffset = -1;\n    var /** @type {?} */ previousKeyframe = null;\n    keyframes.forEach(function (kf) {\n        var /** @type {?} */ offset = /** @type {?} */ (kf['offset']);\n        var /** @type {?} */ isSameOffset = offset == previousOffset;\n        var /** @type {?} */ normalizedKeyframe = (isSameOffset && previousKeyframe) || {};\n        Object.keys(kf).forEach(function (prop) {\n            var /** @type {?} */ normalizedProp = prop;\n            var /** @type {?} */ normalizedValue = kf[prop];\n            if (prop !== 'offset') {\n                normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n                switch (normalizedValue) {\n                    case _angular_animations.ɵPRE_STYLE:\n                        normalizedValue = preStyles[prop];\n                        break;\n                    case _angular_animations.AUTO_STYLE:\n                        normalizedValue = postStyles[prop];\n                        break;\n                    default:\n                        normalizedValue =\n                            normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n                        break;\n                }\n            }\n            normalizedKeyframe[normalizedProp] = normalizedValue;\n        });\n        if (!isSameOffset) {\n            normalizedKeyframes.push(normalizedKeyframe);\n        }\n        previousKeyframe = normalizedKeyframe;\n        previousOffset = offset;\n    });\n    if (errors.length) {\n        var /** @type {?} */ LINE_START = '\\n - ';\n        throw new Error(\"Unable to animate due to the following errors:\" + LINE_START + errors.join(LINE_START));\n    }\n    return normalizedKeyframes;\n}\n/**\n * @param {?} player\n * @param {?} eventName\n * @param {?} event\n * @param {?} callback\n * @return {?}\n */\nfunction listenOnPlayer(player, eventName, event, callback) {\n    switch (eventName) {\n        case 'start':\n            player.onStart(function () { return callback(event && copyAnimationEvent(event, 'start', player.totalTime)); });\n            break;\n        case 'done':\n            player.onDone(function () { return callback(event && copyAnimationEvent(event, 'done', player.totalTime)); });\n            break;\n        case 'destroy':\n            player.onDestroy(function () { return callback(event && copyAnimationEvent(event, 'destroy', player.totalTime)); });\n            break;\n    }\n}\n/**\n * @param {?} e\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nfunction copyAnimationEvent(e, phaseName, totalTime) {\n    var /** @type {?} */ event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime);\n    var /** @type {?} */ data = (/** @type {?} */ (e))['_data'];\n    if (data != null) {\n        (/** @type {?} */ (event))['_data'] = data;\n    }\n    return event;\n}\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?=} phaseName\n * @param {?=} totalTime\n * @return {?}\n */\nfunction makeAnimationEvent(element, triggerName, fromState, toState, phaseName, totalTime) {\n    if (phaseName === void 0) { phaseName = ''; }\n    if (totalTime === void 0) { totalTime = 0; }\n    return { element: element, triggerName: triggerName, fromState: fromState, toState: toState, phaseName: phaseName, totalTime: totalTime };\n}\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} defaultValue\n * @return {?}\n */\nfunction getOrSetAsInMap(map, key, defaultValue) {\n    var /** @type {?} */ value;\n    if (map instanceof Map) {\n        value = map.get(key);\n        if (!value) {\n            map.set(key, value = defaultValue);\n        }\n    }\n    else {\n        value = map[key];\n        if (!value) {\n            value = map[key] = defaultValue;\n        }\n    }\n    return value;\n}\n/**\n * @param {?} command\n * @return {?}\n */\nfunction parseTimelineCommand(command) {\n    var /** @type {?} */ separatorPos = command.indexOf(':');\n    var /** @type {?} */ id = command.substring(1, separatorPos);\n    var /** @type {?} */ action = command.substr(separatorPos + 1);\n    return [id, action];\n}\nvar _contains = function (elm1, elm2) { return false; };\nvar _matches = function (element, selector) {\n    return false;\n};\nvar _query = function (element, selector, multi) {\n    return [];\n};\nif (typeof Element != 'undefined') {\n    // this is well supported in all browsers\n    _contains = function (elm1, elm2) { return /** @type {?} */ (elm1.contains(elm2)); };\n    if (Element.prototype.matches) {\n        _matches = function (element, selector) { return element.matches(selector); };\n    }\n    else {\n        var /** @type {?} */ proto = /** @type {?} */ (Element.prototype);\n        var /** @type {?} */ fn_1 = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector ||\n            proto.oMatchesSelector || proto.webkitMatchesSelector;\n        if (fn_1) {\n            _matches = function (element, selector) { return fn_1.apply(element, [selector]); };\n        }\n    }\n    _query = function (element, selector, multi) {\n        var /** @type {?} */ results = [];\n        if (multi) {\n            results.push.apply(results, element.querySelectorAll(selector));\n        }\n        else {\n            var /** @type {?} */ elm = element.querySelector(selector);\n            if (elm) {\n                results.push(elm);\n            }\n        }\n        return results;\n    };\n}\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction containsVendorPrefix(prop) {\n    // Webkit is the only real popular vendor prefix nowadays\n    // cc: http://shouldiprefix.com/\n    return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nvar _CACHED_BODY = null;\nvar _IS_WEBKIT = false;\n/**\n * @param {?} prop\n * @return {?}\n */\nfunction validateStyleProperty(prop) {\n    if (!_CACHED_BODY) {\n        _CACHED_BODY = getBodyNode() || {};\n        _IS_WEBKIT = /** @type {?} */ ((_CACHED_BODY)).style ? ('WebkitAppearance' in /** @type {?} */ ((_CACHED_BODY)).style) : false;\n    }\n    var /** @type {?} */ result = true;\n    if (/** @type {?} */ ((_CACHED_BODY)).style && !containsVendorPrefix(prop)) {\n        result = prop in /** @type {?} */ ((_CACHED_BODY)).style;\n        if (!result && _IS_WEBKIT) {\n            var /** @type {?} */ camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.substr(1);\n            result = camelProp in /** @type {?} */ ((_CACHED_BODY)).style;\n        }\n    }\n    return result;\n}\n/**\n * @return {?}\n */\nfunction getBodyNode() {\n    if (typeof document != 'undefined') {\n        return document.body;\n    }\n    return null;\n}\nvar matchesElement = _matches;\nvar containsElement = _contains;\nvar invokeQuery = _query;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * \\@experimental\n */\nvar NoopAnimationDriver = /** @class */ (function () {\n    function NoopAnimationDriver() {\n    }\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.validateStyleProperty = /**\n     * @param {?} prop\n     * @return {?}\n     */\n    function (prop) { return validateStyleProperty(prop); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.matchesElement = /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    function (element, selector) {\n        return matchesElement(element, selector);\n    };\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.containsElement = /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    function (elm1, elm2) { return containsElement(elm1, elm2); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.query = /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    function (element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    };\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.computeStyle = /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    function (element, prop, defaultValue) {\n        return defaultValue || '';\n    };\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    NoopAnimationDriver.prototype.animate = /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    function (element, keyframes, duration, delay, easing, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        return new _angular_animations.NoopAnimationPlayer();\n    };\n    return NoopAnimationDriver;\n}());\n/**\n * \\@experimental\n * @abstract\n */\nvar AnimationDriver = /** @class */ (function () {\n    function AnimationDriver() {\n    }\n    AnimationDriver.NOOP = new NoopAnimationDriver();\n    return AnimationDriver;\n}());\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar ONE_SECOND = 1000;\nvar SUBSTITUTION_EXPR_START = '{{';\nvar SUBSTITUTION_EXPR_END = '}}';\nvar ENTER_CLASSNAME = 'ng-enter';\nvar LEAVE_CLASSNAME = 'ng-leave';\n\n\nvar NG_TRIGGER_CLASSNAME = 'ng-trigger';\nvar NG_TRIGGER_SELECTOR = '.ng-trigger';\nvar NG_ANIMATING_CLASSNAME = 'ng-animating';\nvar NG_ANIMATING_SELECTOR = '.ng-animating';\n/**\n * @param {?} value\n * @return {?}\n */\nfunction resolveTimingValue(value) {\n    if (typeof value == 'number')\n        return value;\n    var /** @type {?} */ matches = (/** @type {?} */ (value)).match(/^(-?[\\.\\d]+)(m?s)/);\n    if (!matches || matches.length < 2)\n        return 0;\n    return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n/**\n * @param {?} value\n * @param {?} unit\n * @return {?}\n */\nfunction _convertTimeValueToMS(value, unit) {\n    switch (unit) {\n        case 's':\n            return value * ONE_SECOND;\n        default:\n            // ms or something else\n            return value;\n    }\n}\n/**\n * @param {?} timings\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction resolveTiming(timings, errors, allowNegativeValues) {\n    return timings.hasOwnProperty('duration') ? /** @type {?} */ (timings) :\n        parseTimeExpression(/** @type {?} */ (timings), errors, allowNegativeValues);\n}\n/**\n * @param {?} exp\n * @param {?} errors\n * @param {?=} allowNegativeValues\n * @return {?}\n */\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n    var /** @type {?} */ regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n    var /** @type {?} */ duration;\n    var /** @type {?} */ delay = 0;\n    var /** @type {?} */ easing = '';\n    if (typeof exp === 'string') {\n        var /** @type {?} */ matches = exp.match(regex);\n        if (matches === null) {\n            errors.push(\"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n            return { duration: 0, delay: 0, easing: '' };\n        }\n        duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n        var /** @type {?} */ delayMatch = matches[3];\n        if (delayMatch != null) {\n            delay = _convertTimeValueToMS(Math.floor(parseFloat(delayMatch)), matches[4]);\n        }\n        var /** @type {?} */ easingVal = matches[5];\n        if (easingVal) {\n            easing = easingVal;\n        }\n    }\n    else {\n        duration = /** @type {?} */ (exp);\n    }\n    if (!allowNegativeValues) {\n        var /** @type {?} */ containsErrors = false;\n        var /** @type {?} */ startIndex = errors.length;\n        if (duration < 0) {\n            errors.push(\"Duration values below 0 are not allowed for this animation step.\");\n            containsErrors = true;\n        }\n        if (delay < 0) {\n            errors.push(\"Delay values below 0 are not allowed for this animation step.\");\n            containsErrors = true;\n        }\n        if (containsErrors) {\n            errors.splice(startIndex, 0, \"The provided timing value \\\"\" + exp + \"\\\" is invalid.\");\n        }\n    }\n    return { duration: duration, delay: delay, easing: easing };\n}\n/**\n * @param {?} obj\n * @param {?=} destination\n * @return {?}\n */\nfunction copyObj(obj, destination) {\n    if (destination === void 0) { destination = {}; }\n    Object.keys(obj).forEach(function (prop) { destination[prop] = obj[prop]; });\n    return destination;\n}\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction normalizeStyles(styles) {\n    var /** @type {?} */ normalizedStyles = {};\n    if (Array.isArray(styles)) {\n        styles.forEach(function (data) { return copyStyles(data, false, normalizedStyles); });\n    }\n    else {\n        copyStyles(styles, false, normalizedStyles);\n    }\n    return normalizedStyles;\n}\n/**\n * @param {?} styles\n * @param {?} readPrototype\n * @param {?=} destination\n * @return {?}\n */\nfunction copyStyles(styles, readPrototype, destination) {\n    if (destination === void 0) { destination = {}; }\n    if (readPrototype) {\n        // we make use of a for-in loop so that the\n        // prototypically inherited properties are\n        // revealed from the backFill map\n        for (var /** @type {?} */ prop in styles) {\n            destination[prop] = styles[prop];\n        }\n    }\n    else {\n        copyObj(styles, destination);\n    }\n    return destination;\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nfunction setStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = styles[prop];\n        });\n    }\n}\n/**\n * @param {?} element\n * @param {?} styles\n * @return {?}\n */\nfunction eraseStyles(element, styles) {\n    if (element['style']) {\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ camelProp = dashCaseToCamelCase(prop);\n            element.style[camelProp] = '';\n        });\n    }\n}\n/**\n * @param {?} steps\n * @return {?}\n */\nfunction normalizeAnimationEntry(steps) {\n    if (Array.isArray(steps)) {\n        if (steps.length == 1)\n            return steps[0];\n        return _angular_animations.sequence(steps);\n    }\n    return /** @type {?} */ (steps);\n}\n/**\n * @param {?} value\n * @param {?} options\n * @param {?} errors\n * @return {?}\n */\nfunction validateStyleParams(value, options, errors) {\n    var /** @type {?} */ params = options.params || {};\n    var /** @type {?} */ matches = extractStyleParams(value);\n    if (matches.length) {\n        matches.forEach(function (varName) {\n            if (!params.hasOwnProperty(varName)) {\n                errors.push(\"Unable to resolve the local animation param \" + varName + \" in the given list of values\");\n            }\n        });\n    }\n}\nvar PARAM_REGEX = new RegExp(SUBSTITUTION_EXPR_START + \"\\\\s*(.+?)\\\\s*\" + SUBSTITUTION_EXPR_END, 'g');\n/**\n * @param {?} value\n * @return {?}\n */\nfunction extractStyleParams(value) {\n    var /** @type {?} */ params = [];\n    if (typeof value === 'string') {\n        var /** @type {?} */ val = value.toString();\n        var /** @type {?} */ match = void 0;\n        while (match = PARAM_REGEX.exec(val)) {\n            params.push(/** @type {?} */ (match[1]));\n        }\n        PARAM_REGEX.lastIndex = 0;\n    }\n    return params;\n}\n/**\n * @param {?} value\n * @param {?} params\n * @param {?} errors\n * @return {?}\n */\nfunction interpolateParams(value, params, errors) {\n    var /** @type {?} */ original = value.toString();\n    var /** @type {?} */ str = original.replace(PARAM_REGEX, function (_, varName) {\n        var /** @type {?} */ localVal = params[varName];\n        // this means that the value was never overidden by the data passed in by the user\n        if (!params.hasOwnProperty(varName)) {\n            errors.push(\"Please provide a value for the animation param \" + varName);\n            localVal = '';\n        }\n        return localVal.toString();\n    });\n    // we do this to assert that numeric values stay as they are\n    return str == original ? value : str;\n}\n/**\n * @param {?} iterator\n * @return {?}\n */\nfunction iteratorToArray(iterator) {\n    var /** @type {?} */ arr = [];\n    var /** @type {?} */ item = iterator.next();\n    while (!item.done) {\n        arr.push(item.value);\n        item = iterator.next();\n    }\n    return arr;\n}\n/**\n * @param {?} source\n * @param {?} destination\n * @return {?}\n */\n\nvar DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n/**\n * @param {?} input\n * @return {?}\n */\nfunction dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, function () {\n        var m = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            m[_i] = arguments[_i];\n        }\n        return m[1].toUpperCase();\n    });\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @return {?}\n */\nfunction allowPreviousPlayerStylesMerge(duration, delay) {\n    return duration === 0 || delay === 0;\n}\n/**\n * @param {?} visitor\n * @param {?} node\n * @param {?} context\n * @return {?}\n */\nfunction visitDslNode(visitor, node, context) {\n    switch (node.type) {\n        case 7 /* Trigger */:\n            return visitor.visitTrigger(node, context);\n        case 0 /* State */:\n            return visitor.visitState(node, context);\n        case 1 /* Transition */:\n            return visitor.visitTransition(node, context);\n        case 2 /* Sequence */:\n            return visitor.visitSequence(node, context);\n        case 3 /* Group */:\n            return visitor.visitGroup(node, context);\n        case 4 /* Animate */:\n            return visitor.visitAnimate(node, context);\n        case 5 /* Keyframes */:\n            return visitor.visitKeyframes(node, context);\n        case 6 /* Style */:\n            return visitor.visitStyle(node, context);\n        case 8 /* Reference */:\n            return visitor.visitReference(node, context);\n        case 9 /* AnimateChild */:\n            return visitor.visitAnimateChild(node, context);\n        case 10 /* AnimateRef */:\n            return visitor.visitAnimateRef(node, context);\n        case 11 /* Query */:\n            return visitor.visitQuery(node, context);\n        case 12 /* Stagger */:\n            return visitor.visitStagger(node, context);\n        default:\n            throw new Error(\"Unable to resolve animation metadata node #\" + node.type);\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nvar ANY_STATE = '*';\n/**\n * @param {?} transitionValue\n * @param {?} errors\n * @return {?}\n */\nfunction parseTransitionExpr(transitionValue, errors) {\n    var /** @type {?} */ expressions = [];\n    if (typeof transitionValue == 'string') {\n        (/** @type {?} */ (transitionValue))\n            .split(/\\s*,\\s*/)\n            .forEach(function (str) { return parseInnerTransitionStr(str, expressions, errors); });\n    }\n    else {\n        expressions.push(/** @type {?} */ (transitionValue));\n    }\n    return expressions;\n}\n/**\n * @param {?} eventStr\n * @param {?} expressions\n * @param {?} errors\n * @return {?}\n */\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n    if (eventStr[0] == ':') {\n        var /** @type {?} */ result = parseAnimationAlias(eventStr, errors);\n        if (typeof result == 'function') {\n            expressions.push(result);\n            return;\n        }\n        eventStr = /** @type {?} */ (result);\n    }\n    var /** @type {?} */ match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n    if (match == null || match.length < 4) {\n        errors.push(\"The provided transition expression \\\"\" + eventStr + \"\\\" is not supported\");\n        return expressions;\n    }\n    var /** @type {?} */ fromState = match[1];\n    var /** @type {?} */ separator = match[2];\n    var /** @type {?} */ toState = match[3];\n    expressions.push(makeLambdaFromStates(fromState, toState));\n    var /** @type {?} */ isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n    if (separator[0] == '<' && !isFullAnyStateExpr) {\n        expressions.push(makeLambdaFromStates(toState, fromState));\n    }\n}\n/**\n * @param {?} alias\n * @param {?} errors\n * @return {?}\n */\nfunction parseAnimationAlias(alias, errors) {\n    switch (alias) {\n        case ':enter':\n            return 'void => *';\n        case ':leave':\n            return '* => void';\n        case ':increment':\n            return function (fromState, toState) { return parseFloat(toState) > parseFloat(fromState); };\n        case ':decrement':\n            return function (fromState, toState) { return parseFloat(toState) < parseFloat(fromState); };\n        default:\n            errors.push(\"The transition alias value \\\"\" + alias + \"\\\" is not supported\");\n            return '* => *';\n    }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nvar TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\nvar FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\n/**\n * @param {?} lhs\n * @param {?} rhs\n * @return {?}\n */\nfunction makeLambdaFromStates(lhs, rhs) {\n    var /** @type {?} */ LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n    var /** @type {?} */ RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n    return function (fromState, toState) {\n        var /** @type {?} */ lhsMatch = lhs == ANY_STATE || lhs == fromState;\n        var /** @type {?} */ rhsMatch = rhs == ANY_STATE || rhs == toState;\n        if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n            lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n        }\n        if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n            rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n        }\n        return lhsMatch && rhsMatch;\n    };\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar SELF_TOKEN = ':self';\nvar SELF_TOKEN_REGEX = new RegExp(\"s*\" + SELF_TOKEN + \"s*,?\", 'g');\n/**\n * @param {?} driver\n * @param {?} metadata\n * @param {?} errors\n * @return {?}\n */\nfunction buildAnimationAst(driver, metadata, errors) {\n    return new AnimationAstBuilderVisitor(driver).build(metadata, errors);\n}\nvar ROOT_SELECTOR = '';\nvar AnimationAstBuilderVisitor = /** @class */ (function () {\n    function AnimationAstBuilderVisitor(_driver) {\n        this._driver = _driver;\n    }\n    /**\n     * @param {?} metadata\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.build = /**\n     * @param {?} metadata\n     * @param {?} errors\n     * @return {?}\n     */\n    function (metadata, errors) {\n        var /** @type {?} */ context = new AnimationAstBuilderContext(errors);\n        this._resetContextStyleTimingState(context);\n        return /** @type {?} */ (visitDslNode(this, normalizeAnimationEntry(metadata), context));\n    };\n    /**\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype._resetContextStyleTimingState = /**\n     * @param {?} context\n     * @return {?}\n     */\n    function (context) {\n        context.currentQuerySelector = ROOT_SELECTOR;\n        context.collectedStyles = {};\n        context.collectedStyles[ROOT_SELECTOR] = {};\n        context.currentTime = 0;\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitTrigger = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        var /** @type {?} */ queryCount = context.queryCount = 0;\n        var /** @type {?} */ depCount = context.depCount = 0;\n        var /** @type {?} */ states = [];\n        var /** @type {?} */ transitions = [];\n        if (metadata.name.charAt(0) == '@') {\n            context.errors.push('animation triggers cannot be prefixed with an `@` sign (e.g. trigger(\\'@foo\\', [...]))');\n        }\n        metadata.definitions.forEach(function (def) {\n            _this._resetContextStyleTimingState(context);\n            if (def.type == 0 /* State */) {\n                var /** @type {?} */ stateDef_1 = /** @type {?} */ (def);\n                var /** @type {?} */ name_1 = stateDef_1.name;\n                name_1.split(/\\s*,\\s*/).forEach(function (n) {\n                    stateDef_1.name = n;\n                    states.push(_this.visitState(stateDef_1, context));\n                });\n                stateDef_1.name = name_1;\n            }\n            else if (def.type == 1 /* Transition */) {\n                var /** @type {?} */ transition = _this.visitTransition(/** @type {?} */ (def), context);\n                queryCount += transition.queryCount;\n                depCount += transition.depCount;\n                transitions.push(transition);\n            }\n            else {\n                context.errors.push('only state() and transition() definitions can sit inside of a trigger()');\n            }\n        });\n        return {\n            type: 7 /* Trigger */,\n            name: metadata.name, states: states, transitions: transitions, queryCount: queryCount, depCount: depCount,\n            options: null\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitState = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ styleAst = this.visitStyle(metadata.styles, context);\n        var /** @type {?} */ astParams = (metadata.options && metadata.options.params) || null;\n        if (styleAst.containsDynamicStyles) {\n            var /** @type {?} */ missingSubs_1 = new Set();\n            var /** @type {?} */ params_1 = astParams || {};\n            styleAst.styles.forEach(function (value) {\n                if (isObject(value)) {\n                    var /** @type {?} */ stylesObj_1 = /** @type {?} */ (value);\n                    Object.keys(stylesObj_1).forEach(function (prop) {\n                        extractStyleParams(stylesObj_1[prop]).forEach(function (sub) {\n                            if (!params_1.hasOwnProperty(sub)) {\n                                missingSubs_1.add(sub);\n                            }\n                        });\n                    });\n                }\n            });\n            if (missingSubs_1.size) {\n                var /** @type {?} */ missingSubsArr = iteratorToArray(missingSubs_1.values());\n                context.errors.push(\"state(\\\"\" + metadata.name + \"\\\", ...) must define default values for all the following style substitutions: \" + missingSubsArr.join(', '));\n            }\n        }\n        return {\n            type: 0 /* State */,\n            name: metadata.name,\n            style: styleAst,\n            options: astParams ? { params: astParams } : null\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitTransition = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        context.queryCount = 0;\n        context.depCount = 0;\n        var /** @type {?} */ animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        var /** @type {?} */ matchers = parseTransitionExpr(metadata.expr, context.errors);\n        return {\n            type: 1 /* Transition */,\n            matchers: matchers,\n            animation: animation,\n            queryCount: context.queryCount,\n            depCount: context.depCount,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitSequence = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        return {\n            type: 2 /* Sequence */,\n            steps: metadata.steps.map(function (s) { return visitDslNode(_this, s, context); }),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitGroup = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        var /** @type {?} */ currentTime = context.currentTime;\n        var /** @type {?} */ furthestTime = 0;\n        var /** @type {?} */ steps = metadata.steps.map(function (step) {\n            context.currentTime = currentTime;\n            var /** @type {?} */ innerAst = visitDslNode(_this, step, context);\n            furthestTime = Math.max(furthestTime, context.currentTime);\n            return innerAst;\n        });\n        context.currentTime = furthestTime;\n        return {\n            type: 3 /* Group */,\n            steps: steps,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitAnimate = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ timingAst = constructTimingAst(metadata.timings, context.errors);\n        context.currentAnimateTimings = timingAst;\n        var /** @type {?} */ styleAst;\n        var /** @type {?} */ styleMetadata = metadata.styles ? metadata.styles : _angular_animations.style({});\n        if (styleMetadata.type == 5 /* Keyframes */) {\n            styleAst = this.visitKeyframes(/** @type {?} */ (styleMetadata), context);\n        }\n        else {\n            var /** @type {?} */ styleMetadata_1 = /** @type {?} */ (metadata.styles);\n            var /** @type {?} */ isEmpty = false;\n            if (!styleMetadata_1) {\n                isEmpty = true;\n                var /** @type {?} */ newStyleData = {};\n                if (timingAst.easing) {\n                    newStyleData['easing'] = timingAst.easing;\n                }\n                styleMetadata_1 = _angular_animations.style(newStyleData);\n            }\n            context.currentTime += timingAst.duration + timingAst.delay;\n            var /** @type {?} */ _styleAst = this.visitStyle(styleMetadata_1, context);\n            _styleAst.isEmptyStep = isEmpty;\n            styleAst = _styleAst;\n        }\n        context.currentAnimateTimings = null;\n        return {\n            type: 4 /* Animate */,\n            timings: timingAst,\n            style: styleAst,\n            options: null\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitStyle = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ ast = this._makeStyleAst(metadata, context);\n        this._validateStyleAst(ast, context);\n        return ast;\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype._makeStyleAst = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ styles = [];\n        if (Array.isArray(metadata.styles)) {\n            (/** @type {?} */ (metadata.styles)).forEach(function (styleTuple) {\n                if (typeof styleTuple == 'string') {\n                    if (styleTuple == _angular_animations.AUTO_STYLE) {\n                        styles.push(/** @type {?} */ (styleTuple));\n                    }\n                    else {\n                        context.errors.push(\"The provided style string value \" + styleTuple + \" is not allowed.\");\n                    }\n                }\n                else {\n                    styles.push(/** @type {?} */ (styleTuple));\n                }\n            });\n        }\n        else {\n            styles.push(metadata.styles);\n        }\n        var /** @type {?} */ containsDynamicStyles = false;\n        var /** @type {?} */ collectedEasing = null;\n        styles.forEach(function (styleData) {\n            if (isObject(styleData)) {\n                var /** @type {?} */ styleMap = /** @type {?} */ (styleData);\n                var /** @type {?} */ easing = styleMap['easing'];\n                if (easing) {\n                    collectedEasing = /** @type {?} */ (easing);\n                    delete styleMap['easing'];\n                }\n                if (!containsDynamicStyles) {\n                    for (var /** @type {?} */ prop in styleMap) {\n                        var /** @type {?} */ value = styleMap[prop];\n                        if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n                            containsDynamicStyles = true;\n                            break;\n                        }\n                    }\n                }\n            }\n        });\n        return {\n            type: 6 /* Style */,\n            styles: styles,\n            easing: collectedEasing,\n            offset: metadata.offset, containsDynamicStyles: containsDynamicStyles,\n            options: null\n        };\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype._validateStyleAst = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        var /** @type {?} */ timings = context.currentAnimateTimings;\n        var /** @type {?} */ endTime = context.currentTime;\n        var /** @type {?} */ startTime = context.currentTime;\n        if (timings && startTime > 0) {\n            startTime -= timings.duration + timings.delay;\n        }\n        ast.styles.forEach(function (tuple) {\n            if (typeof tuple == 'string')\n                return;\n            Object.keys(tuple).forEach(function (prop) {\n                if (!_this._driver.validateStyleProperty(prop)) {\n                    context.errors.push(\"The provided animation property \\\"\" + prop + \"\\\" is not a supported CSS property for animations\");\n                    return;\n                }\n                var /** @type {?} */ collectedStyles = context.collectedStyles[/** @type {?} */ ((context.currentQuerySelector))];\n                var /** @type {?} */ collectedEntry = collectedStyles[prop];\n                var /** @type {?} */ updateCollectedStyle = true;\n                if (collectedEntry) {\n                    if (startTime != endTime && startTime >= collectedEntry.startTime &&\n                        endTime <= collectedEntry.endTime) {\n                        context.errors.push(\"The CSS property \\\"\" + prop + \"\\\" that exists between the times of \\\"\" + collectedEntry.startTime + \"ms\\\" and \\\"\" + collectedEntry.endTime + \"ms\\\" is also being animated in a parallel animation between the times of \\\"\" + startTime + \"ms\\\" and \\\"\" + endTime + \"ms\\\"\");\n                        updateCollectedStyle = false;\n                    }\n                    // we always choose the smaller start time value since we\n                    // want to have a record of the entire animation window where\n                    // the style property is being animated in between\n                    startTime = collectedEntry.startTime;\n                }\n                if (updateCollectedStyle) {\n                    collectedStyles[prop] = { startTime: startTime, endTime: endTime };\n                }\n                if (context.options) {\n                    validateStyleParams(tuple[prop], context.options, context.errors);\n                }\n            });\n        });\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitKeyframes = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var _this = this;\n        var /** @type {?} */ ast = { type: 5 /* Keyframes */, styles: [], options: null };\n        if (!context.currentAnimateTimings) {\n            context.errors.push(\"keyframes() must be placed inside of a call to animate()\");\n            return ast;\n        }\n        var /** @type {?} */ MAX_KEYFRAME_OFFSET = 1;\n        var /** @type {?} */ totalKeyframesWithOffsets = 0;\n        var /** @type {?} */ offsets = [];\n        var /** @type {?} */ offsetsOutOfOrder = false;\n        var /** @type {?} */ keyframesOutOfRange = false;\n        var /** @type {?} */ previousOffset = 0;\n        var /** @type {?} */ keyframes = metadata.steps.map(function (styles) {\n            var /** @type {?} */ style$$1 = _this._makeStyleAst(styles, context);\n            var /** @type {?} */ offsetVal = style$$1.offset != null ? style$$1.offset : consumeOffset(style$$1.styles);\n            var /** @type {?} */ offset = 0;\n            if (offsetVal != null) {\n                totalKeyframesWithOffsets++;\n                offset = style$$1.offset = offsetVal;\n            }\n            keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n            offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n            previousOffset = offset;\n            offsets.push(offset);\n            return style$$1;\n        });\n        if (keyframesOutOfRange) {\n            context.errors.push(\"Please ensure that all keyframe offsets are between 0 and 1\");\n        }\n        if (offsetsOutOfOrder) {\n            context.errors.push(\"Please ensure that all keyframe offsets are in order\");\n        }\n        var /** @type {?} */ length = metadata.steps.length;\n        var /** @type {?} */ generatedOffset = 0;\n        if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n            context.errors.push(\"Not all style() steps within the declared keyframes() contain offsets\");\n        }\n        else if (totalKeyframesWithOffsets == 0) {\n            generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n        }\n        var /** @type {?} */ limit = length - 1;\n        var /** @type {?} */ currentTime = context.currentTime;\n        var /** @type {?} */ currentAnimateTimings = /** @type {?} */ ((context.currentAnimateTimings));\n        var /** @type {?} */ animateDuration = currentAnimateTimings.duration;\n        keyframes.forEach(function (kf, i) {\n            var /** @type {?} */ offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];\n            var /** @type {?} */ durationUpToThisFrame = offset * animateDuration;\n            context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n            currentAnimateTimings.duration = durationUpToThisFrame;\n            _this._validateStyleAst(kf, context);\n            kf.offset = offset;\n            ast.styles.push(kf);\n        });\n        return ast;\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitReference = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        return {\n            type: 8 /* Reference */,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitAnimateChild = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        context.depCount++;\n        return {\n            type: 9 /* AnimateChild */,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitAnimateRef = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        return {\n            type: 10 /* AnimateRef */,\n            animation: this.visitReference(metadata.animation, context),\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitQuery = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        var /** @type {?} */ parentSelector = /** @type {?} */ ((context.currentQuerySelector));\n        var /** @type {?} */ options = /** @type {?} */ ((metadata.options || {}));\n        context.queryCount++;\n        context.currentQuery = metadata;\n        var _a = normalizeSelector(metadata.selector), selector = _a[0], includeSelf = _a[1];\n        context.currentQuerySelector =\n            parentSelector.length ? (parentSelector + ' ' + selector) : selector;\n        getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});\n        var /** @type {?} */ animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        context.currentQuery = null;\n        context.currentQuerySelector = parentSelector;\n        return {\n            type: 11 /* Query */,\n            selector: selector,\n            limit: options.limit || 0,\n            optional: !!options.optional, includeSelf: includeSelf, animation: animation,\n            originalSelector: metadata.selector,\n            options: normalizeAnimationOptions(metadata.options)\n        };\n    };\n    /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationAstBuilderVisitor.prototype.visitStagger = /**\n     * @param {?} metadata\n     * @param {?} context\n     * @return {?}\n     */\n    function (metadata, context) {\n        if (!context.currentQuery) {\n            context.errors.push(\"stagger() can only be used inside of query()\");\n        }\n        var /** @type {?} */ timings = metadata.timings === 'full' ?\n            { duration: 0, delay: 0, easing: 'full' } :\n            resolveTiming(metadata.timings, context.errors, true);\n        return {\n            type: 12 /* Stagger */,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context), timings: timings,\n            options: null\n        };\n    };\n    return AnimationAstBuilderVisitor;\n}());\n/**\n * @param {?} selector\n * @return {?}\n */\nfunction normalizeSelector(selector) {\n    var /** @type {?} */ hasAmpersand = selector.split(/\\s*,\\s*/).find(function (token) { return token == SELF_TOKEN; }) ? true : false;\n    if (hasAmpersand) {\n        selector = selector.replace(SELF_TOKEN_REGEX, '');\n    }\n    // the :enter and :leave selectors are filled in at runtime during timeline building\n    selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n        .replace(/@\\w+/g, function (match) { return NG_TRIGGER_SELECTOR + '-' + match.substr(1); })\n        .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n    return [selector, hasAmpersand];\n}\n/**\n * @param {?} obj\n * @return {?}\n */\nfunction normalizeParams(obj) {\n    return obj ? copyObj(obj) : null;\n}\nvar AnimationAstBuilderContext = /** @class */ (function () {\n    function AnimationAstBuilderContext(errors) {\n        this.errors = errors;\n        this.queryCount = 0;\n        this.depCount = 0;\n        this.currentTransition = null;\n        this.currentQuery = null;\n        this.currentQuerySelector = null;\n        this.currentAnimateTimings = null;\n        this.currentTime = 0;\n        this.collectedStyles = {};\n        this.options = null;\n    }\n    return AnimationAstBuilderContext;\n}());\n/**\n * @param {?} styles\n * @return {?}\n */\nfunction consumeOffset(styles) {\n    if (typeof styles == 'string')\n        return null;\n    var /** @type {?} */ offset = null;\n    if (Array.isArray(styles)) {\n        styles.forEach(function (styleTuple) {\n            if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {\n                var /** @type {?} */ obj = /** @type {?} */ (styleTuple);\n                offset = parseFloat(/** @type {?} */ (obj['offset']));\n                delete obj['offset'];\n            }\n        });\n    }\n    else if (isObject(styles) && styles.hasOwnProperty('offset')) {\n        var /** @type {?} */ obj = /** @type {?} */ (styles);\n        offset = parseFloat(/** @type {?} */ (obj['offset']));\n        delete obj['offset'];\n    }\n    return offset;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isObject(value) {\n    return !Array.isArray(value) && typeof value == 'object';\n}\n/**\n * @param {?} value\n * @param {?} errors\n * @return {?}\n */\nfunction constructTimingAst(value, errors) {\n    var /** @type {?} */ timings = null;\n    if (value.hasOwnProperty('duration')) {\n        timings = /** @type {?} */ (value);\n    }\n    else if (typeof value == 'number') {\n        var /** @type {?} */ duration = resolveTiming(/** @type {?} */ (value), errors).duration;\n        return makeTimingAst(/** @type {?} */ (duration), 0, '');\n    }\n    var /** @type {?} */ strValue = /** @type {?} */ (value);\n    var /** @type {?} */ isDynamic = strValue.split(/\\s+/).some(function (v) { return v.charAt(0) == '{' && v.charAt(1) == '{'; });\n    if (isDynamic) {\n        var /** @type {?} */ ast = /** @type {?} */ (makeTimingAst(0, 0, ''));\n        ast.dynamic = true;\n        ast.strValue = strValue;\n        return /** @type {?} */ (ast);\n    }\n    timings = timings || resolveTiming(strValue, errors);\n    return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\n/**\n * @param {?} options\n * @return {?}\n */\nfunction normalizeAnimationOptions(options) {\n    if (options) {\n        options = copyObj(options);\n        if (options['params']) {\n            options['params'] = /** @type {?} */ ((normalizeParams(options['params'])));\n        }\n    }\n    else {\n        options = {};\n    }\n    return options;\n}\n/**\n * @param {?} duration\n * @param {?} delay\n * @param {?} easing\n * @return {?}\n */\nfunction makeTimingAst(duration, delay, easing) {\n    return { duration: duration, delay: delay, easing: easing };\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @record\n */\n\n/**\n * @param {?} element\n * @param {?} keyframes\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?} duration\n * @param {?} delay\n * @param {?=} easing\n * @param {?=} subTimeline\n * @return {?}\n */\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing, subTimeline) {\n    if (easing === void 0) { easing = null; }\n    if (subTimeline === void 0) { subTimeline = false; }\n    return {\n        type: 1 /* TimelineAnimation */,\n        element: element,\n        keyframes: keyframes,\n        preStyleProps: preStyleProps,\n        postStyleProps: postStyleProps,\n        duration: duration,\n        delay: delay,\n        totalTime: duration + delay, easing: easing, subTimeline: subTimeline\n    };\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar ElementInstructionMap = /** @class */ (function () {\n    function ElementInstructionMap() {\n        this._map = new Map();\n    }\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.consume = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ instructions = this._map.get(element);\n        if (instructions) {\n            this._map.delete(element);\n        }\n        else {\n            instructions = [];\n        }\n        return instructions;\n    };\n    /**\n     * @param {?} element\n     * @param {?} instructions\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.append = /**\n     * @param {?} element\n     * @param {?} instructions\n     * @return {?}\n     */\n    function (element, instructions) {\n        var /** @type {?} */ existingInstructions = this._map.get(element);\n        if (!existingInstructions) {\n            this._map.set(element, existingInstructions = []);\n        }\n        existingInstructions.push.apply(existingInstructions, instructions);\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.has = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) { return this._map.has(element); };\n    /**\n     * @return {?}\n     */\n    ElementInstructionMap.prototype.clear = /**\n     * @return {?}\n     */\n    function () { this._map.clear(); };\n    return ElementInstructionMap;\n}());\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar ONE_FRAME_IN_MILLISECONDS = 1;\nvar ENTER_TOKEN = ':enter';\nvar ENTER_TOKEN_REGEX = new RegExp(ENTER_TOKEN, 'g');\nvar LEAVE_TOKEN = ':leave';\nvar LEAVE_TOKEN_REGEX = new RegExp(LEAVE_TOKEN, 'g');\n/**\n * @param {?} driver\n * @param {?} rootElement\n * @param {?} ast\n * @param {?} enterClassName\n * @param {?} leaveClassName\n * @param {?=} startingStyles\n * @param {?=} finalStyles\n * @param {?=} options\n * @param {?=} subInstructions\n * @param {?=} errors\n * @return {?}\n */\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors) {\n    if (startingStyles === void 0) { startingStyles = {}; }\n    if (finalStyles === void 0) { finalStyles = {}; }\n    if (errors === void 0) { errors = []; }\n    return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nvar AnimationTimelineBuilderVisitor = /** @class */ (function () {\n    function AnimationTimelineBuilderVisitor() {\n    }\n    /**\n     * @param {?} driver\n     * @param {?} rootElement\n     * @param {?} ast\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?} startingStyles\n     * @param {?} finalStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @param {?=} errors\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.buildKeyframes = /**\n     * @param {?} driver\n     * @param {?} rootElement\n     * @param {?} ast\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?} startingStyles\n     * @param {?} finalStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @param {?=} errors\n     * @return {?}\n     */\n    function (driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors) {\n        if (errors === void 0) { errors = []; }\n        subInstructions = subInstructions || new ElementInstructionMap();\n        var /** @type {?} */ context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n        context.options = options;\n        context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n        visitDslNode(this, ast, context);\n        // this checks to see if an actual animation happened\n        var /** @type {?} */ timelines = context.timelines.filter(function (timeline) { return timeline.containsAnimation(); });\n        if (timelines.length && Object.keys(finalStyles).length) {\n            var /** @type {?} */ tl = timelines[timelines.length - 1];\n            if (!tl.allowOnlyTimelineStyles()) {\n                tl.setStyles([finalStyles], null, context.errors, options);\n            }\n        }\n        return timelines.length ? timelines.map(function (timeline) { return timeline.buildKeyframes(); }) :\n            [createTimelineInstruction(rootElement, [], [], [], 0, 0, '', false)];\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitTrigger = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        // these values are not visited in this AST\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitState = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        // these values are not visited in this AST\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitTransition = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        // these values are not visited in this AST\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitAnimateChild = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ elementInstructions = context.subInstructions.consume(context.element);\n        if (elementInstructions) {\n            var /** @type {?} */ innerContext = context.createSubContext(ast.options);\n            var /** @type {?} */ startTime = context.currentTimeline.currentTime;\n            var /** @type {?} */ endTime = this._visitSubInstructions(elementInstructions, innerContext, /** @type {?} */ (innerContext.options));\n            if (startTime != endTime) {\n                // we do this on the upper context because we created a sub context for\n                // the sub child animations\n                context.transformIntoNewTimeline(endTime);\n            }\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitAnimateRef = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ innerContext = context.createSubContext(ast.options);\n        innerContext.transformIntoNewTimeline();\n        this.visitReference(ast.animation, innerContext);\n        context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} instructions\n     * @param {?} context\n     * @param {?} options\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype._visitSubInstructions = /**\n     * @param {?} instructions\n     * @param {?} context\n     * @param {?} options\n     * @return {?}\n     */\n    function (instructions, context, options) {\n        var /** @type {?} */ startTime = context.currentTimeline.currentTime;\n        var /** @type {?} */ furthestTime = startTime;\n        // this is a special-case for when a user wants to skip a sub\n        // animation from being fired entirely.\n        var /** @type {?} */ duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n        var /** @type {?} */ delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n        if (duration !== 0) {\n            instructions.forEach(function (instruction) {\n                var /** @type {?} */ instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n                furthestTime =\n                    Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n            });\n        }\n        return furthestTime;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitReference = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        context.updateOptions(ast.options, true);\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitSequence = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        var /** @type {?} */ subContextCount = context.subContextCount;\n        var /** @type {?} */ ctx = context;\n        var /** @type {?} */ options = ast.options;\n        if (options && (options.params || options.delay)) {\n            ctx = context.createSubContext(options);\n            ctx.transformIntoNewTimeline();\n            if (options.delay != null) {\n                if (ctx.previousNode.type == 6 /* Style */) {\n                    ctx.currentTimeline.snapshotCurrentStyles();\n                    ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n                }\n                var /** @type {?} */ delay = resolveTimingValue(options.delay);\n                ctx.delayNextStep(delay);\n            }\n        }\n        if (ast.steps.length) {\n            ast.steps.forEach(function (s) { return visitDslNode(_this, s, ctx); });\n            // this is here just incase the inner steps only contain or end with a style() call\n            ctx.currentTimeline.applyStylesToKeyframe();\n            // this means that some animation function within the sequence\n            // ended up creating a sub timeline (which means the current\n            // timeline cannot overlap with the contents of the sequence)\n            if (ctx.subContextCount > subContextCount) {\n                ctx.transformIntoNewTimeline();\n            }\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitGroup = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        var /** @type {?} */ innerTimelines = [];\n        var /** @type {?} */ furthestTime = context.currentTimeline.currentTime;\n        var /** @type {?} */ delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n        ast.steps.forEach(function (s) {\n            var /** @type {?} */ innerContext = context.createSubContext(ast.options);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            visitDslNode(_this, s, innerContext);\n            furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n            innerTimelines.push(innerContext.currentTimeline);\n        });\n        // this operation is run after the AST loop because otherwise\n        // if the parent timeline's collected styles were updated then\n        // it would pass in invalid data into the new-to-be forked items\n        innerTimelines.forEach(function (timeline) { return context.currentTimeline.mergeTimelineCollectedStyles(timeline); });\n        context.transformIntoNewTimeline(furthestTime);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype._visitTiming = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        if ((/** @type {?} */ (ast)).dynamic) {\n            var /** @type {?} */ strValue = (/** @type {?} */ (ast)).strValue;\n            var /** @type {?} */ timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n            return resolveTiming(timingValue, context.errors);\n        }\n        else {\n            return { duration: ast.duration, delay: ast.delay, easing: ast.easing };\n        }\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitAnimate = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n        var /** @type {?} */ timeline = context.currentTimeline;\n        if (timings.delay) {\n            context.incrementTime(timings.delay);\n            timeline.snapshotCurrentStyles();\n        }\n        var /** @type {?} */ style$$1 = ast.style;\n        if (style$$1.type == 5 /* Keyframes */) {\n            this.visitKeyframes(style$$1, context);\n        }\n        else {\n            context.incrementTime(timings.duration);\n            this.visitStyle(/** @type {?} */ (style$$1), context);\n            timeline.applyStylesToKeyframe();\n        }\n        context.currentAnimateTimings = null;\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitStyle = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ timeline = context.currentTimeline;\n        var /** @type {?} */ timings = /** @type {?} */ ((context.currentAnimateTimings));\n        // this is a special case for when a style() call\n        // directly follows  an animate() call (but not inside of an animate() call)\n        if (!timings && timeline.getCurrentStyleProperties().length) {\n            timeline.forwardFrame();\n        }\n        var /** @type {?} */ easing = (timings && timings.easing) || ast.easing;\n        if (ast.isEmptyStep) {\n            timeline.applyEmptyStep(easing);\n        }\n        else {\n            timeline.setStyles(ast.styles, easing, context.errors, context.options);\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitKeyframes = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ currentAnimateTimings = /** @type {?} */ ((context.currentAnimateTimings));\n        var /** @type {?} */ startTime = (/** @type {?} */ ((context.currentTimeline))).duration;\n        var /** @type {?} */ duration = currentAnimateTimings.duration;\n        var /** @type {?} */ innerContext = context.createSubContext();\n        var /** @type {?} */ innerTimeline = innerContext.currentTimeline;\n        innerTimeline.easing = currentAnimateTimings.easing;\n        ast.styles.forEach(function (step) {\n            var /** @type {?} */ offset = step.offset || 0;\n            innerTimeline.forwardTime(offset * duration);\n            innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n            innerTimeline.applyStylesToKeyframe();\n        });\n        // this will ensure that the parent timeline gets all the styles from\n        // the child even if the new timeline below is not used\n        context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n        // we do this because the window between this timeline and the sub timeline\n        // should ensure that the styles within are exactly the same as they were before\n        context.transformIntoNewTimeline(startTime + duration);\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitQuery = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var _this = this;\n        // in the event that the first step before this is a style step we need\n        // to ensure the styles are applied before the children are animated\n        var /** @type {?} */ startTime = context.currentTimeline.currentTime;\n        var /** @type {?} */ options = /** @type {?} */ ((ast.options || {}));\n        var /** @type {?} */ delay = options.delay ? resolveTimingValue(options.delay) : 0;\n        if (delay && (context.previousNode.type === 6 /* Style */ ||\n            (startTime == 0 && context.currentTimeline.getCurrentStyleProperties().length))) {\n            context.currentTimeline.snapshotCurrentStyles();\n            context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        var /** @type {?} */ furthestTime = startTime;\n        var /** @type {?} */ elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n        context.currentQueryTotal = elms.length;\n        var /** @type {?} */ sameElementTimeline = null;\n        elms.forEach(function (element, i) {\n            context.currentQueryIndex = i;\n            var /** @type {?} */ innerContext = context.createSubContext(ast.options, element);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            if (element === context.element) {\n                sameElementTimeline = innerContext.currentTimeline;\n            }\n            visitDslNode(_this, ast.animation, innerContext);\n            // this is here just incase the inner steps only contain or end\n            // with a style() call (which is here to signal that this is a preparatory\n            // call to style an element before it is animated again)\n            innerContext.currentTimeline.applyStylesToKeyframe();\n            var /** @type {?} */ endTime = innerContext.currentTimeline.currentTime;\n            furthestTime = Math.max(furthestTime, endTime);\n        });\n        context.currentQueryIndex = 0;\n        context.currentQueryTotal = 0;\n        context.transformIntoNewTimeline(furthestTime);\n        if (sameElementTimeline) {\n            context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n            context.currentTimeline.snapshotCurrentStyles();\n        }\n        context.previousNode = ast;\n    };\n    /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTimelineBuilderVisitor.prototype.visitStagger = /**\n     * @param {?} ast\n     * @param {?} context\n     * @return {?}\n     */\n    function (ast, context) {\n        var /** @type {?} */ parentContext = /** @type {?} */ ((context.parentContext));\n        var /** @type {?} */ tl = context.currentTimeline;\n        var /** @type {?} */ timings = ast.timings;\n        var /** @type {?} */ duration = Math.abs(timings.duration);\n        var /** @type {?} */ maxTime = duration * (context.currentQueryTotal - 1);\n        var /** @type {?} */ delay = duration * context.currentQueryIndex;\n        var /** @type {?} */ staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n        switch (staggerTransformer) {\n            case 'reverse':\n                delay = maxTime - delay;\n                break;\n            case 'full':\n                delay = parentContext.currentStaggerTime;\n                break;\n        }\n        var /** @type {?} */ timeline = context.currentTimeline;\n        if (delay) {\n            timeline.delayNextStep(delay);\n        }\n        var /** @type {?} */ startingTime = timeline.currentTime;\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n        // time = duration + delay\n        // the reason why this computation is so complex is because\n        // the inner timeline may either have a delay value or a stretched\n        // keyframe depending on if a subtimeline is not used or is used.\n        parentContext.currentStaggerTime =\n            (tl.currentTime - startingTime) + (tl.startTime - parentContext.currentTimeline.startTime);\n    };\n    return AnimationTimelineBuilderVisitor;\n}());\nvar DEFAULT_NOOP_PREVIOUS_NODE = /** @type {?} */ ({});\nvar AnimationTimelineContext = /** @class */ (function () {\n    function AnimationTimelineContext(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n        this._driver = _driver;\n        this.element = element;\n        this.subInstructions = subInstructions;\n        this._enterClassName = _enterClassName;\n        this._leaveClassName = _leaveClassName;\n        this.errors = errors;\n        this.timelines = timelines;\n        this.parentContext = null;\n        this.currentAnimateTimings = null;\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.subContextCount = 0;\n        this.options = {};\n        this.currentQueryIndex = 0;\n        this.currentQueryTotal = 0;\n        this.currentStaggerTime = 0;\n        this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n        timelines.push(this.currentTimeline);\n    }\n    Object.defineProperty(AnimationTimelineContext.prototype, \"params\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.options.params; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} options\n     * @param {?=} skipIfExists\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.updateOptions = /**\n     * @param {?} options\n     * @param {?=} skipIfExists\n     * @return {?}\n     */\n    function (options, skipIfExists) {\n        var _this = this;\n        if (!options)\n            return;\n        var /** @type {?} */ newOptions = /** @type {?} */ (options);\n        var /** @type {?} */ optionsToUpdate = this.options;\n        // NOTE: this will get patched up when other animation methods support duration overrides\n        if (newOptions.duration != null) {\n            (/** @type {?} */ (optionsToUpdate)).duration = resolveTimingValue(newOptions.duration);\n        }\n        if (newOptions.delay != null) {\n            optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n        }\n        var /** @type {?} */ newParams = newOptions.params;\n        if (newParams) {\n            var /** @type {?} */ paramsToUpdate_1 = /** @type {?} */ ((optionsToUpdate.params));\n            if (!paramsToUpdate_1) {\n                paramsToUpdate_1 = this.options.params = {};\n            }\n            Object.keys(newParams).forEach(function (name) {\n                if (!skipIfExists || !paramsToUpdate_1.hasOwnProperty(name)) {\n                    paramsToUpdate_1[name] = interpolateParams(newParams[name], paramsToUpdate_1, _this.errors);\n                }\n            });\n        }\n    };\n    /**\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype._copyOptions = /**\n     * @return {?}\n     */\n    function () {\n        var /** @type {?} */ options = {};\n        if (this.options) {\n            var /** @type {?} */ oldParams_1 = this.options.params;\n            if (oldParams_1) {\n                var /** @type {?} */ params_1 = options['params'] = {};\n                Object.keys(oldParams_1).forEach(function (name) { params_1[name] = oldParams_1[name]; });\n            }\n        }\n        return options;\n    };\n    /**\n     * @param {?=} options\n     * @param {?=} element\n     * @param {?=} newTime\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.createSubContext = /**\n     * @param {?=} options\n     * @param {?=} element\n     * @param {?=} newTime\n     * @return {?}\n     */\n    function (options, element, newTime) {\n        if (options === void 0) { options = null; }\n        var /** @type {?} */ target = element || this.element;\n        var /** @type {?} */ context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n        context.previousNode = this.previousNode;\n        context.currentAnimateTimings = this.currentAnimateTimings;\n        context.options = this._copyOptions();\n        context.updateOptions(options);\n        context.currentQueryIndex = this.currentQueryIndex;\n        context.currentQueryTotal = this.currentQueryTotal;\n        context.parentContext = this;\n        this.subContextCount++;\n        return context;\n    };\n    /**\n     * @param {?=} newTime\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.transformIntoNewTimeline = /**\n     * @param {?=} newTime\n     * @return {?}\n     */\n    function (newTime) {\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n        this.timelines.push(this.currentTimeline);\n        return this.currentTimeline;\n    };\n    /**\n     * @param {?} instruction\n     * @param {?} duration\n     * @param {?} delay\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.appendInstructionToTimeline = /**\n     * @param {?} instruction\n     * @param {?} duration\n     * @param {?} delay\n     * @return {?}\n     */\n    function (instruction, duration, delay) {\n        var /** @type {?} */ updatedTimings = {\n            duration: duration != null ? duration : instruction.duration,\n            delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n            easing: ''\n        };\n        var /** @type {?} */ builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n        this.timelines.push(builder);\n        return updatedTimings;\n    };\n    /**\n     * @param {?} time\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.incrementTime = /**\n     * @param {?} time\n     * @return {?}\n     */\n    function (time) {\n        this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n    };\n    /**\n     * @param {?} delay\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.delayNextStep = /**\n     * @param {?} delay\n     * @return {?}\n     */\n    function (delay) {\n        // negative delays are not yet supported\n        if (delay > 0) {\n            this.currentTimeline.delayNextStep(delay);\n        }\n    };\n    /**\n     * @param {?} selector\n     * @param {?} originalSelector\n     * @param {?} limit\n     * @param {?} includeSelf\n     * @param {?} optional\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationTimelineContext.prototype.invokeQuery = /**\n     * @param {?} selector\n     * @param {?} originalSelector\n     * @param {?} limit\n     * @param {?} includeSelf\n     * @param {?} optional\n     * @param {?} errors\n     * @return {?}\n     */\n    function (selector, originalSelector, limit, includeSelf, optional, errors) {\n        var /** @type {?} */ results = [];\n        if (includeSelf) {\n            results.push(this.element);\n        }\n        if (selector.length > 0) {\n            // if :self is only used then the selector is empty\n            selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n            selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n            var /** @type {?} */ multi = limit != 1;\n            var /** @type {?} */ elements = this._driver.query(this.element, selector, multi);\n            if (limit !== 0) {\n                elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) :\n                    elements.slice(0, limit);\n            }\n            results.push.apply(results, elements);\n        }\n        if (!optional && results.length == 0) {\n            errors.push(\"`query(\\\"\" + originalSelector + \"\\\")` returned zero elements. (Use `query(\\\"\" + originalSelector + \"\\\", { optional: true })` if you wish to allow this.)\");\n        }\n        return results;\n    };\n    return AnimationTimelineContext;\n}());\nvar TimelineBuilder = /** @class */ (function () {\n    function TimelineBuilder(_driver, element, startTime, _elementTimelineStylesLookup) {\n        this._driver = _driver;\n        this.element = element;\n        this.startTime = startTime;\n        this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n        this.duration = 0;\n        this._previousKeyframe = {};\n        this._currentKeyframe = {};\n        this._keyframes = new Map();\n        this._styleSummary = {};\n        this._pendingStyles = {};\n        this._backFill = {};\n        this._currentEmptyStepKeyframe = null;\n        if (!this._elementTimelineStylesLookup) {\n            this._elementTimelineStylesLookup = new Map();\n        }\n        this._localTimelineStyles = Object.create(this._backFill, {});\n        this._globalTimelineStyles = /** @type {?} */ ((this._elementTimelineStylesLookup.get(element)));\n        if (!this._globalTimelineStyles) {\n            this._globalTimelineStyles = this._localTimelineStyles;\n            this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n        }\n        this._loadKeyframe();\n    }\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.containsAnimation = /**\n     * @return {?}\n     */\n    function () {\n        switch (this._keyframes.size) {\n            case 0:\n                return false;\n            case 1:\n                return this.getCurrentStyleProperties().length > 0;\n            default:\n                return true;\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.getCurrentStyleProperties = /**\n     * @return {?}\n     */\n    function () { return Object.keys(this._currentKeyframe); };\n    Object.defineProperty(TimelineBuilder.prototype, \"currentTime\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.startTime + this.duration; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} delay\n     * @return {?}\n     */\n    TimelineBuilder.prototype.delayNextStep = /**\n     * @param {?} delay\n     * @return {?}\n     */\n    function (delay) {\n        // in the event that a style() step is placed right before a stagger()\n        // and that style() step is the very first style() value in the animation\n        // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n        // properly applies the style() values to work with the stagger...\n        var /** @type {?} */ hasPreStyleStep = this._keyframes.size == 1 && Object.keys(this._pendingStyles).length;\n        if (this.duration || hasPreStyleStep) {\n            this.forwardTime(this.currentTime + delay);\n            if (hasPreStyleStep) {\n                this.snapshotCurrentStyles();\n            }\n        }\n        else {\n            this.startTime += delay;\n        }\n    };\n    /**\n     * @param {?} element\n     * @param {?=} currentTime\n     * @return {?}\n     */\n    TimelineBuilder.prototype.fork = /**\n     * @param {?} element\n     * @param {?=} currentTime\n     * @return {?}\n     */\n    function (element, currentTime) {\n        this.applyStylesToKeyframe();\n        return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype._loadKeyframe = /**\n     * @return {?}\n     */\n    function () {\n        if (this._currentKeyframe) {\n            this._previousKeyframe = this._currentKeyframe;\n        }\n        this._currentKeyframe = /** @type {?} */ ((this._keyframes.get(this.duration)));\n        if (!this._currentKeyframe) {\n            this._currentKeyframe = Object.create(this._backFill, {});\n            this._keyframes.set(this.duration, this._currentKeyframe);\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.forwardFrame = /**\n     * @return {?}\n     */\n    function () {\n        this.duration += ONE_FRAME_IN_MILLISECONDS;\n        this._loadKeyframe();\n    };\n    /**\n     * @param {?} time\n     * @return {?}\n     */\n    TimelineBuilder.prototype.forwardTime = /**\n     * @param {?} time\n     * @return {?}\n     */\n    function (time) {\n        this.applyStylesToKeyframe();\n        this.duration = time;\n        this._loadKeyframe();\n    };\n    /**\n     * @param {?} prop\n     * @param {?} value\n     * @return {?}\n     */\n    TimelineBuilder.prototype._updateStyle = /**\n     * @param {?} prop\n     * @param {?} value\n     * @return {?}\n     */\n    function (prop, value) {\n        this._localTimelineStyles[prop] = value;\n        this._globalTimelineStyles[prop] = value;\n        this._styleSummary[prop] = { time: this.currentTime, value: value };\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.allowOnlyTimelineStyles = /**\n     * @return {?}\n     */\n    function () { return this._currentEmptyStepKeyframe !== this._currentKeyframe; };\n    /**\n     * @param {?} easing\n     * @return {?}\n     */\n    TimelineBuilder.prototype.applyEmptyStep = /**\n     * @param {?} easing\n     * @return {?}\n     */\n    function (easing) {\n        var _this = this;\n        if (easing) {\n            this._previousKeyframe['easing'] = easing;\n        }\n        // special case for animate(duration):\n        // all missing styles are filled with a `*` value then\n        // if any destination styles are filled in later on the same\n        // keyframe then they will override the overridden styles\n        // We use `_globalTimelineStyles` here because there may be\n        // styles in previous keyframes that are not present in this timeline\n        Object.keys(this._globalTimelineStyles).forEach(function (prop) {\n            _this._backFill[prop] = _this._globalTimelineStyles[prop] || _angular_animations.AUTO_STYLE;\n            _this._currentKeyframe[prop] = _angular_animations.AUTO_STYLE;\n        });\n        this._currentEmptyStepKeyframe = this._currentKeyframe;\n    };\n    /**\n     * @param {?} input\n     * @param {?} easing\n     * @param {?} errors\n     * @param {?=} options\n     * @return {?}\n     */\n    TimelineBuilder.prototype.setStyles = /**\n     * @param {?} input\n     * @param {?} easing\n     * @param {?} errors\n     * @param {?=} options\n     * @return {?}\n     */\n    function (input, easing, errors, options) {\n        var _this = this;\n        if (easing) {\n            this._previousKeyframe['easing'] = easing;\n        }\n        var /** @type {?} */ params = (options && options.params) || {};\n        var /** @type {?} */ styles = flattenStyles(input, this._globalTimelineStyles);\n        Object.keys(styles).forEach(function (prop) {\n            var /** @type {?} */ val = interpolateParams(styles[prop], params, errors);\n            _this._pendingStyles[prop] = val;\n            if (!_this._localTimelineStyles.hasOwnProperty(prop)) {\n                _this._backFill[prop] = _this._globalTimelineStyles.hasOwnProperty(prop) ?\n                    _this._globalTimelineStyles[prop] :\n                    _angular_animations.AUTO_STYLE;\n            }\n            _this._updateStyle(prop, val);\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.applyStylesToKeyframe = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        var /** @type {?} */ styles = this._pendingStyles;\n        var /** @type {?} */ props = Object.keys(styles);\n        if (props.length == 0)\n            return;\n        this._pendingStyles = {};\n        props.forEach(function (prop) {\n            var /** @type {?} */ val = styles[prop];\n            _this._currentKeyframe[prop] = val;\n        });\n        Object.keys(this._localTimelineStyles).forEach(function (prop) {\n            if (!_this._currentKeyframe.hasOwnProperty(prop)) {\n                _this._currentKeyframe[prop] = _this._localTimelineStyles[prop];\n            }\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.snapshotCurrentStyles = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        Object.keys(this._localTimelineStyles).forEach(function (prop) {\n            var /** @type {?} */ val = _this._localTimelineStyles[prop];\n            _this._pendingStyles[prop] = val;\n            _this._updateStyle(prop, val);\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.getFinalKeyframe = /**\n     * @return {?}\n     */\n    function () { return this._keyframes.get(this.duration); };\n    Object.defineProperty(TimelineBuilder.prototype, \"properties\", {\n        get: /**\n         * @return {?}\n         */\n        function () {\n            var /** @type {?} */ properties = [];\n            for (var /** @type {?} */ prop in this._currentKeyframe) {\n                properties.push(prop);\n            }\n            return properties;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} timeline\n     * @return {?}\n     */\n    TimelineBuilder.prototype.mergeTimelineCollectedStyles = /**\n     * @param {?} timeline\n     * @return {?}\n     */\n    function (timeline) {\n        var _this = this;\n        Object.keys(timeline._styleSummary).forEach(function (prop) {\n            var /** @type {?} */ details0 = _this._styleSummary[prop];\n            var /** @type {?} */ details1 = timeline._styleSummary[prop];\n            if (!details0 || details1.time > details0.time) {\n                _this._updateStyle(prop, details1.value);\n            }\n        });\n    };\n    /**\n     * @return {?}\n     */\n    TimelineBuilder.prototype.buildKeyframes = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        this.applyStylesToKeyframe();\n        var /** @type {?} */ preStyleProps = new Set();\n        var /** @type {?} */ postStyleProps = new Set();\n        var /** @type {?} */ isEmpty = this._keyframes.size === 1 && this.duration === 0;\n        var /** @type {?} */ finalKeyframes = [];\n        this._keyframes.forEach(function (keyframe, time) {\n            var /** @type {?} */ finalKeyframe = copyStyles(keyframe, true);\n            Object.keys(finalKeyframe).forEach(function (prop) {\n                var /** @type {?} */ value = finalKeyframe[prop];\n                if (value == _angular_animations.ɵPRE_STYLE) {\n                    preStyleProps.add(prop);\n                }\n                else if (value == _angular_animations.AUTO_STYLE) {\n                    postStyleProps.add(prop);\n                }\n            });\n            if (!isEmpty) {\n                finalKeyframe['offset'] = time / _this.duration;\n            }\n            finalKeyframes.push(finalKeyframe);\n        });\n        var /** @type {?} */ preProps = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n        var /** @type {?} */ postProps = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : [];\n        // special case for a 0-second animation (which is designed just to place styles onscreen)\n        if (isEmpty) {\n            var /** @type {?} */ kf0 = finalKeyframes[0];\n            var /** @type {?} */ kf1 = copyObj(kf0);\n            kf0['offset'] = 0;\n            kf1['offset'] = 1;\n            finalKeyframes = [kf0, kf1];\n        }\n        return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n    };\n    return TimelineBuilder;\n}());\nvar SubTimelineBuilder = /** @class */ (function (_super) {\n    __extends(SubTimelineBuilder, _super);\n    function SubTimelineBuilder(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe) {\n        if (_stretchStartingKeyframe === void 0) { _stretchStartingKeyframe = false; }\n        var _this = _super.call(this, driver, element, timings.delay) || this;\n        _this.element = element;\n        _this.keyframes = keyframes;\n        _this.preStyleProps = preStyleProps;\n        _this.postStyleProps = postStyleProps;\n        _this._stretchStartingKeyframe = _stretchStartingKeyframe;\n        _this.timings = { duration: timings.duration, delay: timings.delay, easing: timings.easing };\n        return _this;\n    }\n    /**\n     * @return {?}\n     */\n    SubTimelineBuilder.prototype.containsAnimation = /**\n     * @return {?}\n     */\n    function () { return this.keyframes.length > 1; };\n    /**\n     * @return {?}\n     */\n    SubTimelineBuilder.prototype.buildKeyframes = /**\n     * @return {?}\n     */\n    function () {\n        var /** @type {?} */ keyframes = this.keyframes;\n        var _a = this.timings, delay = _a.delay, duration = _a.duration, easing = _a.easing;\n        if (this._stretchStartingKeyframe && delay) {\n            var /** @type {?} */ newKeyframes = [];\n            var /** @type {?} */ totalTime = duration + delay;\n            var /** @type {?} */ startingGap = delay / totalTime;\n            // the original starting keyframe now starts once the delay is done\n            var /** @type {?} */ newFirstKeyframe = copyStyles(keyframes[0], false);\n            newFirstKeyframe['offset'] = 0;\n            newKeyframes.push(newFirstKeyframe);\n            var /** @type {?} */ oldFirstKeyframe = copyStyles(keyframes[0], false);\n            oldFirstKeyframe['offset'] = roundOffset(startingGap);\n            newKeyframes.push(oldFirstKeyframe);\n            /*\n                    When the keyframe is stretched then it means that the delay before the animation\n                    starts is gone. Instead the first keyframe is placed at the start of the animation\n                    and it is then copied to where it starts when the original delay is over. This basically\n                    means nothing animates during that delay, but the styles are still renderered. For this\n                    to work the original offset values that exist in the original keyframes must be \"warped\"\n                    so that they can take the new keyframe + delay into account.\n            \n                    delay=1000, duration=1000, keyframes = 0 .5 1\n            \n                    turns into\n            \n                    delay=0, duration=2000, keyframes = 0 .33 .66 1\n                   */\n            // offsets between 1 ... n -1 are all warped by the keyframe stretch\n            var /** @type {?} */ limit = keyframes.length - 1;\n            for (var /** @type {?} */ i = 1; i <= limit; i++) {\n                var /** @type {?} */ kf = copyStyles(keyframes[i], false);\n                var /** @type {?} */ oldOffset = /** @type {?} */ (kf['offset']);\n                var /** @type {?} */ timeAtKeyframe = delay + oldOffset * duration;\n                kf['offset'] = roundOffset(timeAtKeyframe / totalTime);\n                newKeyframes.push(kf);\n            }\n            // the new starting keyframe should be added at the start\n            duration = totalTime;\n            delay = 0;\n            easing = '';\n            keyframes = newKeyframes;\n        }\n        return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n    };\n    return SubTimelineBuilder;\n}(TimelineBuilder));\n/**\n * @param {?} offset\n * @param {?=} decimalPoints\n * @return {?}\n */\nfunction roundOffset(offset, decimalPoints) {\n    if (decimalPoints === void 0) { decimalPoints = 3; }\n    var /** @type {?} */ mult = Math.pow(10, decimalPoints - 1);\n    return Math.round(offset * mult) / mult;\n}\n/**\n * @param {?} input\n * @param {?} allStyles\n * @return {?}\n */\nfunction flattenStyles(input, allStyles) {\n    var /** @type {?} */ styles = {};\n    var /** @type {?} */ allProperties;\n    input.forEach(function (token) {\n        if (token === '*') {\n            allProperties = allProperties || Object.keys(allStyles);\n            allProperties.forEach(function (prop) { styles[prop] = _angular_animations.AUTO_STYLE; });\n        }\n        else {\n            copyStyles(/** @type {?} */ (token), false, styles);\n        }\n    });\n    return styles;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar Animation = /** @class */ (function () {\n    function Animation(_driver, input) {\n        this._driver = _driver;\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ ast = buildAnimationAst(_driver, input, errors);\n        if (errors.length) {\n            var /** @type {?} */ errorMessage = \"animation validation failed:\\n\" + errors.join(\"\\n\");\n            throw new Error(errorMessage);\n        }\n        this._animationAst = ast;\n    }\n    /**\n     * @param {?} element\n     * @param {?} startingStyles\n     * @param {?} destinationStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    Animation.prototype.buildTimelines = /**\n     * @param {?} element\n     * @param {?} startingStyles\n     * @param {?} destinationStyles\n     * @param {?} options\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    function (element, startingStyles, destinationStyles, options, subInstructions) {\n        var /** @type {?} */ start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : /** @type {?} */ (startingStyles);\n        var /** @type {?} */ dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : /** @type {?} */ (destinationStyles);\n        var /** @type {?} */ errors = [];\n        subInstructions = subInstructions || new ElementInstructionMap();\n        var /** @type {?} */ result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n        if (errors.length) {\n            var /** @type {?} */ errorMessage = \"animation building failed:\\n\" + errors.join(\"\\n\");\n            throw new Error(errorMessage);\n        }\n        return result;\n    };\n    return Animation;\n}());\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * \\@experimental Animation support is experimental.\n * @abstract\n */\nvar AnimationStyleNormalizer = /** @class */ (function () {\n    function AnimationStyleNormalizer() {\n    }\n    return AnimationStyleNormalizer;\n}());\n/**\n * \\@experimental Animation support is experimental.\n */\nvar NoopAnimationStyleNormalizer = /** @class */ (function () {\n    function NoopAnimationStyleNormalizer() {\n    }\n    /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    NoopAnimationStyleNormalizer.prototype.normalizePropertyName = /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    function (propertyName, errors) { return propertyName; };\n    /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    NoopAnimationStyleNormalizer.prototype.normalizeStyleValue = /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    function (userProvidedProperty, normalizedProperty, value, errors) {\n        return /** @type {?} */ (value);\n    };\n    return NoopAnimationStyleNormalizer;\n}());\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar WebAnimationsStyleNormalizer = /** @class */ (function (_super) {\n    __extends(WebAnimationsStyleNormalizer, _super);\n    function WebAnimationsStyleNormalizer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    WebAnimationsStyleNormalizer.prototype.normalizePropertyName = /**\n     * @param {?} propertyName\n     * @param {?} errors\n     * @return {?}\n     */\n    function (propertyName, errors) {\n        return dashCaseToCamelCase(propertyName);\n    };\n    /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    WebAnimationsStyleNormalizer.prototype.normalizeStyleValue = /**\n     * @param {?} userProvidedProperty\n     * @param {?} normalizedProperty\n     * @param {?} value\n     * @param {?} errors\n     * @return {?}\n     */\n    function (userProvidedProperty, normalizedProperty, value, errors) {\n        var /** @type {?} */ unit = '';\n        var /** @type {?} */ strVal = value.toString().trim();\n        if (DIMENSIONAL_PROP_MAP[normalizedProperty] && value !== 0 && value !== '0') {\n            if (typeof value === 'number') {\n                unit = 'px';\n            }\n            else {\n                var /** @type {?} */ valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n                if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n                    errors.push(\"Please provide a CSS unit value for \" + userProvidedProperty + \":\" + value);\n                }\n            }\n        }\n        return strVal + unit;\n    };\n    return WebAnimationsStyleNormalizer;\n}(AnimationStyleNormalizer));\nvar DIMENSIONAL_PROP_MAP = makeBooleanMap('width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective'\n    .split(','));\n/**\n * @param {?} keys\n * @return {?}\n */\nfunction makeBooleanMap(keys) {\n    var /** @type {?} */ map = {};\n    keys.forEach(function (key) { return map[key] = true; });\n    return map;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @record\n */\n\n/**\n * @param {?} element\n * @param {?} triggerName\n * @param {?} fromState\n * @param {?} toState\n * @param {?} isRemovalTransition\n * @param {?} fromStyles\n * @param {?} toStyles\n * @param {?} timelines\n * @param {?} queriedElements\n * @param {?} preStyleProps\n * @param {?} postStyleProps\n * @param {?=} errors\n * @return {?}\n */\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, errors) {\n    return {\n        type: 0 /* TransitionAnimation */,\n        element: element,\n        triggerName: triggerName,\n        isRemovalTransition: isRemovalTransition,\n        fromState: fromState,\n        fromStyles: fromStyles,\n        toState: toState,\n        toStyles: toStyles,\n        timelines: timelines,\n        queriedElements: queriedElements,\n        preStyleProps: preStyleProps,\n        postStyleProps: postStyleProps,\n        errors: errors\n    };\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar EMPTY_OBJECT = {};\nvar AnimationTransitionFactory = /** @class */ (function () {\n    function AnimationTransitionFactory(_triggerName, ast, _stateStyles) {\n        this._triggerName = _triggerName;\n        this.ast = ast;\n        this._stateStyles = _stateStyles;\n    }\n    /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    AnimationTransitionFactory.prototype.match = /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    function (currentState, nextState) {\n        return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState);\n    };\n    /**\n     * @param {?} stateName\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationTransitionFactory.prototype.buildStyles = /**\n     * @param {?} stateName\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    function (stateName, params, errors) {\n        var /** @type {?} */ backupStateStyler = this._stateStyles['*'];\n        var /** @type {?} */ stateStyler = this._stateStyles[stateName];\n        var /** @type {?} */ backupStyles = backupStateStyler ? backupStateStyler.buildStyles(params, errors) : {};\n        return stateStyler ? stateStyler.buildStyles(params, errors) : backupStyles;\n    };\n    /**\n     * @param {?} driver\n     * @param {?} element\n     * @param {?} currentState\n     * @param {?} nextState\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?=} currentOptions\n     * @param {?=} nextOptions\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    AnimationTransitionFactory.prototype.build = /**\n     * @param {?} driver\n     * @param {?} element\n     * @param {?} currentState\n     * @param {?} nextState\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @param {?=} currentOptions\n     * @param {?=} nextOptions\n     * @param {?=} subInstructions\n     * @return {?}\n     */\n    function (driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions) {\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n        var /** @type {?} */ currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n        var /** @type {?} */ currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n        var /** @type {?} */ nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n        var /** @type {?} */ nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n        var /** @type {?} */ queriedElements = new Set();\n        var /** @type {?} */ preStyleMap = new Map();\n        var /** @type {?} */ postStyleMap = new Map();\n        var /** @type {?} */ isRemoval = nextState === 'void';\n        var /** @type {?} */ animationOptions = { params: __assign({}, transitionAnimationParams, nextAnimationParams) };\n        var /** @type {?} */ timelines = buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n        if (errors.length) {\n            return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, errors);\n        }\n        timelines.forEach(function (tl) {\n            var /** @type {?} */ elm = tl.element;\n            var /** @type {?} */ preProps = getOrSetAsInMap(preStyleMap, elm, {});\n            tl.preStyleProps.forEach(function (prop) { return preProps[prop] = true; });\n            var /** @type {?} */ postProps = getOrSetAsInMap(postStyleMap, elm, {});\n            tl.postStyleProps.forEach(function (prop) { return postProps[prop] = true; });\n            if (elm !== element) {\n                queriedElements.add(elm);\n            }\n        });\n        var /** @type {?} */ queriedElementsList = iteratorToArray(queriedElements.values());\n        return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap, postStyleMap);\n    };\n    return AnimationTransitionFactory;\n}());\n/**\n * @param {?} matchFns\n * @param {?} currentState\n * @param {?} nextState\n * @return {?}\n */\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState) {\n    return matchFns.some(function (fn) { return fn(currentState, nextState); });\n}\nvar AnimationStateStyles = /** @class */ (function () {\n    function AnimationStateStyles(styles, defaultParams) {\n        this.styles = styles;\n        this.defaultParams = defaultParams;\n    }\n    /**\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationStateStyles.prototype.buildStyles = /**\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    function (params, errors) {\n        var /** @type {?} */ finalStyles = {};\n        var /** @type {?} */ combinedParams = copyObj(this.defaultParams);\n        Object.keys(params).forEach(function (key) {\n            var /** @type {?} */ value = params[key];\n            if (value != null) {\n                combinedParams[key] = value;\n            }\n        });\n        this.styles.styles.forEach(function (value) {\n            if (typeof value !== 'string') {\n                var /** @type {?} */ styleObj_1 = /** @type {?} */ (value);\n                Object.keys(styleObj_1).forEach(function (prop) {\n                    var /** @type {?} */ val = styleObj_1[prop];\n                    if (val.length > 1) {\n                        val = interpolateParams(val, combinedParams, errors);\n                    }\n                    finalStyles[prop] = val;\n                });\n            }\n        });\n        return finalStyles;\n    };\n    return AnimationStateStyles;\n}());\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * \\@experimental Animation support is experimental.\n * @param {?} name\n * @param {?} ast\n * @return {?}\n */\nfunction buildTrigger(name, ast) {\n    return new AnimationTrigger(name, ast);\n}\n/**\n * \\@experimental Animation support is experimental.\n */\nvar AnimationTrigger = /** @class */ (function () {\n    function AnimationTrigger(name, ast) {\n        var _this = this;\n        this.name = name;\n        this.ast = ast;\n        this.transitionFactories = [];\n        this.states = {};\n        ast.states.forEach(function (ast) {\n            var /** @type {?} */ defaultParams = (ast.options && ast.options.params) || {};\n            _this.states[ast.name] = new AnimationStateStyles(ast.style, defaultParams);\n        });\n        balanceProperties(this.states, 'true', '1');\n        balanceProperties(this.states, 'false', '0');\n        ast.transitions.forEach(function (ast) {\n            _this.transitionFactories.push(new AnimationTransitionFactory(name, ast, _this.states));\n        });\n        this.fallbackTransition = createFallbackTransition(name, this.states);\n    }\n    Object.defineProperty(AnimationTrigger.prototype, \"containsQueries\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this.ast.queryCount > 0; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    AnimationTrigger.prototype.matchTransition = /**\n     * @param {?} currentState\n     * @param {?} nextState\n     * @return {?}\n     */\n    function (currentState, nextState) {\n        var /** @type {?} */ entry = this.transitionFactories.find(function (f) { return f.match(currentState, nextState); });\n        return entry || null;\n    };\n    /**\n     * @param {?} currentState\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    AnimationTrigger.prototype.matchStyles = /**\n     * @param {?} currentState\n     * @param {?} params\n     * @param {?} errors\n     * @return {?}\n     */\n    function (currentState, params, errors) {\n        return this.fallbackTransition.buildStyles(currentState, params, errors);\n    };\n    return AnimationTrigger;\n}());\n/**\n * @param {?} triggerName\n * @param {?} states\n * @return {?}\n */\nfunction createFallbackTransition(triggerName, states) {\n    var /** @type {?} */ matchers = [function (fromState, toState) { return true; }];\n    var /** @type {?} */ animation = { type: 2 /* Sequence */, steps: [], options: null };\n    var /** @type {?} */ transition = {\n        type: 1 /* Transition */,\n        animation: animation,\n        matchers: matchers,\n        options: null,\n        queryCount: 0,\n        depCount: 0\n    };\n    return new AnimationTransitionFactory(triggerName, transition, states);\n}\n/**\n * @param {?} obj\n * @param {?} key1\n * @param {?} key2\n * @return {?}\n */\nfunction balanceProperties(obj, key1, key2) {\n    if (obj.hasOwnProperty(key1)) {\n        if (!obj.hasOwnProperty(key2)) {\n            obj[key2] = obj[key1];\n        }\n    }\n    else if (obj.hasOwnProperty(key2)) {\n        obj[key1] = obj[key2];\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nvar TimelineAnimationEngine = /** @class */ (function () {\n    function TimelineAnimationEngine(_driver, _normalizer) {\n        this._driver = _driver;\n        this._normalizer = _normalizer;\n        this._animations = {};\n        this._playersById = {};\n        this.players = [];\n    }\n    /**\n     * @param {?} id\n     * @param {?} metadata\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.register = /**\n     * @param {?} id\n     * @param {?} metadata\n     * @return {?}\n     */\n    function (id, metadata) {\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ ast = buildAnimationAst(this._driver, metadata, errors);\n        if (errors.length) {\n            throw new Error(\"Unable to build the animation due to the following errors: \" + errors.join(\"\\n\"));\n        }\n        else {\n            this._animations[id] = ast;\n        }\n    };\n    /**\n     * @param {?} i\n     * @param {?} preStyles\n     * @param {?=} postStyles\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype._buildPlayer = /**\n     * @param {?} i\n     * @param {?} preStyles\n     * @param {?=} postStyles\n     * @return {?}\n     */\n    function (i, preStyles, postStyles) {\n        var /** @type {?} */ element = i.element;\n        var /** @type {?} */ keyframes = normalizeKeyframes(this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n        return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, []);\n    };\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?=} options\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.create = /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?=} options\n     * @return {?}\n     */\n    function (id, element, options) {\n        var _this = this;\n        if (options === void 0) { options = {}; }\n        var /** @type {?} */ errors = [];\n        var /** @type {?} */ ast = this._animations[id];\n        var /** @type {?} */ instructions;\n        var /** @type {?} */ autoStylesMap = new Map();\n        if (ast) {\n            instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, {}, {}, options, EMPTY_INSTRUCTION_MAP, errors);\n            instructions.forEach(function (inst) {\n                var /** @type {?} */ styles = getOrSetAsInMap(autoStylesMap, inst.element, {});\n                inst.postStyleProps.forEach(function (prop) { return styles[prop] = null; });\n            });\n        }\n        else {\n            errors.push('The requested animation doesn\\'t exist or has already been destroyed');\n            instructions = [];\n        }\n        if (errors.length) {\n            throw new Error(\"Unable to create the animation due to the following errors: \" + errors.join(\"\\n\"));\n        }\n        autoStylesMap.forEach(function (styles, element) {\n            Object.keys(styles).forEach(function (prop) { styles[prop] = _this._driver.computeStyle(element, prop, _angular_animations.AUTO_STYLE); });\n        });\n        var /** @type {?} */ players = instructions.map(function (i) {\n            var /** @type {?} */ styles = autoStylesMap.get(i.element);\n            return _this._buildPlayer(i, {}, styles);\n        });\n        var /** @type {?} */ player = optimizeGroupPlayer(players);\n        this._playersById[id] = player;\n        player.onDestroy(function () { return _this.destroy(id); });\n        this.players.push(player);\n        return player;\n    };\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.destroy = /**\n     * @param {?} id\n     * @return {?}\n     */\n    function (id) {\n        var /** @type {?} */ player = this._getPlayer(id);\n        player.destroy();\n        delete this._playersById[id];\n        var /** @type {?} */ index = this.players.indexOf(player);\n        if (index >= 0) {\n            this.players.splice(index, 1);\n        }\n    };\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype._getPlayer = /**\n     * @param {?} id\n     * @return {?}\n     */\n    function (id) {\n        var /** @type {?} */ player = this._playersById[id];\n        if (!player) {\n            throw new Error(\"Unable to find the timeline player referenced by \" + id);\n        }\n        return player;\n    };\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} callback\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.listen = /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} callback\n     * @return {?}\n     */\n    function (id, element, eventName, callback) {\n        // triggerName, fromState, toState are all ignored for timeline animations\n        var /** @type {?} */ baseEvent = makeAnimationEvent(element, '', '', '');\n        listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n        return function () { };\n    };\n    /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} command\n     * @param {?} args\n     * @return {?}\n     */\n    TimelineAnimationEngine.prototype.command = /**\n     * @param {?} id\n     * @param {?} element\n     * @param {?} command\n     * @param {?} args\n     * @return {?}\n     */\n    function (id, element, command, args) {\n        if (command == 'register') {\n            this.register(id, /** @type {?} */ (args[0]));\n            return;\n        }\n        if (command == 'create') {\n            var /** @type {?} */ options = /** @type {?} */ ((args[0] || {}));\n            this.create(id, element, options);\n            return;\n        }\n        var /** @type {?} */ player = this._getPlayer(id);\n        switch (command) {\n            case 'play':\n                player.play();\n                break;\n            case 'pause':\n                player.pause();\n                break;\n            case 'reset':\n                player.reset();\n                break;\n            case 'restart':\n                player.restart();\n                break;\n            case 'finish':\n                player.finish();\n                break;\n            case 'init':\n                player.init();\n                break;\n            case 'setPosition':\n                player.setPosition(parseFloat(/** @type {?} */ (args[0])));\n                break;\n            case 'destroy':\n                this.destroy(id);\n                break;\n        }\n    };\n    return TimelineAnimationEngine;\n}());\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar QUEUED_CLASSNAME = 'ng-animate-queued';\nvar QUEUED_SELECTOR = '.ng-animate-queued';\nvar DISABLED_CLASSNAME = 'ng-animate-disabled';\nvar DISABLED_SELECTOR = '.ng-animate-disabled';\nvar STAR_CLASSNAME = 'ng-star-inserted';\nvar STAR_SELECTOR = '.ng-star-inserted';\nvar EMPTY_PLAYER_ARRAY = [];\nvar NULL_REMOVAL_STATE = {\n    namespaceId: '',\n    setForRemoval: null,\n    hasAnimation: false,\n    removedBeforeQueried: false\n};\nvar NULL_REMOVED_QUERIED_STATE = {\n    namespaceId: '',\n    setForRemoval: null,\n    hasAnimation: false,\n    removedBeforeQueried: true\n};\n/**\n * @record\n */\n\nvar REMOVAL_FLAG = '__ng_removed';\n/**\n * @record\n */\n\nvar StateValue = /** @class */ (function () {\n    function StateValue(input, namespaceId) {\n        if (namespaceId === void 0) { namespaceId = ''; }\n        this.namespaceId = namespaceId;\n        var /** @type {?} */ isObj = input && input.hasOwnProperty('value');\n        var /** @type {?} */ value = isObj ? input['value'] : input;\n        this.value = normalizeTriggerValue(value);\n        if (isObj) {\n            var /** @type {?} */ options = copyObj(/** @type {?} */ (input));\n            delete options['value'];\n            this.options = /** @type {?} */ (options);\n        }\n        else {\n            this.options = {};\n        }\n        if (!this.options.params) {\n            this.options.params = {};\n        }\n    }\n    Object.defineProperty(StateValue.prototype, \"params\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return /** @type {?} */ (this.options.params); },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} options\n     * @return {?}\n     */\n    StateValue.prototype.absorbOptions = /**\n     * @param {?} options\n     * @return {?}\n     */\n    function (options) {\n        var /** @type {?} */ newParams = options.params;\n        if (newParams) {\n            var /** @type {?} */ oldParams_1 = /** @type {?} */ ((this.options.params));\n            Object.keys(newParams).forEach(function (prop) {\n                if (oldParams_1[prop] == null) {\n                    oldParams_1[prop] = newParams[prop];\n                }\n            });\n        }\n    };\n    return StateValue;\n}());\nvar VOID_VALUE = 'void';\nvar DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nvar DELETED_STATE_VALUE = new StateValue('DELETED');\nvar AnimationTransitionNamespace = /** @class */ (function () {\n    function AnimationTransitionNamespace(id, hostElement, _engine) {\n        this.id = id;\n        this.hostElement = hostElement;\n        this._engine = _engine;\n        this.players = [];\n        this._triggers = {};\n        this._queue = [];\n        this._elementListeners = new Map();\n        this._hostClassName = 'ng-tns-' + id;\n        addClass(hostElement, this._hostClassName);\n    }\n    /**\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.listen = /**\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    function (element, name, phase, callback) {\n        var _this = this;\n        if (!this._triggers.hasOwnProperty(name)) {\n            throw new Error(\"Unable to listen on the animation trigger event \\\"\" + phase + \"\\\" because the animation trigger \\\"\" + name + \"\\\" doesn't exist!\");\n        }\n        if (phase == null || phase.length == 0) {\n            throw new Error(\"Unable to listen on the animation trigger \\\"\" + name + \"\\\" because the provided event is undefined!\");\n        }\n        if (!isTriggerEventValid(phase)) {\n            throw new Error(\"The provided animation trigger event \\\"\" + phase + \"\\\" for the animation trigger \\\"\" + name + \"\\\" is not supported!\");\n        }\n        var /** @type {?} */ listeners = getOrSetAsInMap(this._elementListeners, element, []);\n        var /** @type {?} */ data = { name: name, phase: phase, callback: callback };\n        listeners.push(data);\n        var /** @type {?} */ triggersWithStates = getOrSetAsInMap(this._engine.statesByElement, element, {});\n        if (!triggersWithStates.hasOwnProperty(name)) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n            triggersWithStates[name] = DEFAULT_STATE_VALUE;\n        }\n        return function () {\n            // the event listener is removed AFTER the flush has occurred such\n            // that leave animations callbacks can fire (otherwise if the node\n            // is removed in between then the listeners would be deregistered)\n            // the event listener is removed AFTER the flush has occurred such\n            // that leave animations callbacks can fire (otherwise if the node\n            // is removed in between then the listeners would be deregistered)\n            _this._engine.afterFlush(function () {\n                var /** @type {?} */ index = listeners.indexOf(data);\n                if (index >= 0) {\n                    listeners.splice(index, 1);\n                }\n                if (!_this._triggers[name]) {\n                    delete triggersWithStates[name];\n                }\n            });\n        };\n    };\n    /**\n     * @param {?} name\n     * @param {?} ast\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.register = /**\n     * @param {?} name\n     * @param {?} ast\n     * @return {?}\n     */\n    function (name, ast) {\n        if (this._triggers[name]) {\n            // throw\n            return false;\n        }\n        else {\n            this._triggers[name] = ast;\n            return true;\n        }\n    };\n    /**\n     * @param {?} name\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype._getTrigger = /**\n     * @param {?} name\n     * @return {?}\n     */\n    function (name) {\n        var /** @type {?} */ trigger = this._triggers[name];\n        if (!trigger) {\n            throw new Error(\"The provided animation trigger \\\"\" + name + \"\\\" has not been registered!\");\n        }\n        return trigger;\n    };\n    /**\n     * @param {?} element\n     * @param {?} triggerName\n     * @param {?} value\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.trigger = /**\n     * @param {?} element\n     * @param {?} triggerName\n     * @param {?} value\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    function (element, triggerName, value, defaultToFallback) {\n        var _this = this;\n        if (defaultToFallback === void 0) { defaultToFallback = true; }\n        var /** @type {?} */ trigger = this._getTrigger(triggerName);\n        var /** @type {?} */ player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        var /** @type {?} */ triggersWithStates = this._engine.statesByElement.get(element);\n        if (!triggersWithStates) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n            this._engine.statesByElement.set(element, triggersWithStates = {});\n        }\n        var /** @type {?} */ fromState = triggersWithStates[triggerName];\n        var /** @type {?} */ toState = new StateValue(value, this.id);\n        var /** @type {?} */ isObj = value && value.hasOwnProperty('value');\n        if (!isObj && fromState) {\n            toState.absorbOptions(fromState.options);\n        }\n        triggersWithStates[triggerName] = toState;\n        if (!fromState) {\n            fromState = DEFAULT_STATE_VALUE;\n        }\n        else if (fromState === DELETED_STATE_VALUE) {\n            return player;\n        }\n        var /** @type {?} */ isRemoval = toState.value === VOID_VALUE;\n        // normally this isn't reached by here, however, if an object expression\n        // is passed in then it may be a new object each time. Comparing the value\n        // is important since that will stay the same despite there being a new object.\n        // The removal arc here is special cased because the same element is triggered\n        // twice in the event that it contains animations on the outer/inner portions\n        // of the host container\n        if (!isRemoval && fromState.value === toState.value) {\n            // this means that despite the value not changing, some inner params\n            // have changed which means that the animation final styles need to be applied\n            if (!objEquals(fromState.params, toState.params)) {\n                var /** @type {?} */ errors = [];\n                var /** @type {?} */ fromStyles_1 = trigger.matchStyles(fromState.value, fromState.params, errors);\n                var /** @type {?} */ toStyles_1 = trigger.matchStyles(toState.value, toState.params, errors);\n                if (errors.length) {\n                    this._engine.reportError(errors);\n                }\n                else {\n                    this._engine.afterFlush(function () {\n                        eraseStyles(element, fromStyles_1);\n                        setStyles(element, toStyles_1);\n                    });\n                }\n            }\n            return;\n        }\n        var /** @type {?} */ playersOnElement = getOrSetAsInMap(this._engine.playersByElement, element, []);\n        playersOnElement.forEach(function (player) {\n            // only remove the player if it is queued on the EXACT same trigger/namespace\n            // we only also deal with queued players here because if the animation has\n            // started then we want to keep the player alive until the flush happens\n            // (which is where the previousPlayers are passed into the new palyer)\n            if (player.namespaceId == _this.id && player.triggerName == triggerName && player.queued) {\n                player.destroy();\n            }\n        });\n        var /** @type {?} */ transition = trigger.matchTransition(fromState.value, toState.value);\n        var /** @type {?} */ isFallbackTransition = false;\n        if (!transition) {\n            if (!defaultToFallback)\n                return;\n            transition = trigger.fallbackTransition;\n            isFallbackTransition = true;\n        }\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({ element: element, triggerName: triggerName, transition: transition, fromState: fromState, toState: toState, player: player, isFallbackTransition: isFallbackTransition });\n        if (!isFallbackTransition) {\n            addClass(element, QUEUED_CLASSNAME);\n            player.onStart(function () { removeClass(element, QUEUED_CLASSNAME); });\n        }\n        player.onDone(function () {\n            var /** @type {?} */ index = _this.players.indexOf(player);\n            if (index >= 0) {\n                _this.players.splice(index, 1);\n            }\n            var /** @type {?} */ players = _this._engine.playersByElement.get(element);\n            if (players) {\n                var /** @type {?} */ index_1 = players.indexOf(player);\n                if (index_1 >= 0) {\n                    players.splice(index_1, 1);\n                }\n            }\n        });\n        this.players.push(player);\n        playersOnElement.push(player);\n        return player;\n    };\n    /**\n     * @param {?} name\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.deregister = /**\n     * @param {?} name\n     * @return {?}\n     */\n    function (name) {\n        var _this = this;\n        delete this._triggers[name];\n        this._engine.statesByElement.forEach(function (stateMap, element) { delete stateMap[name]; });\n        this._elementListeners.forEach(function (listeners, element) {\n            _this._elementListeners.set(element, listeners.filter(function (entry) { return entry.name != name; }));\n        });\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.clearElementCache = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        this._engine.statesByElement.delete(element);\n        this._elementListeners.delete(element);\n        var /** @type {?} */ elementPlayers = this._engine.playersByElement.get(element);\n        if (elementPlayers) {\n            elementPlayers.forEach(function (player) { return player.destroy(); });\n            this._engine.playersByElement.delete(element);\n        }\n    };\n    /**\n     * @param {?} rootElement\n     * @param {?} context\n     * @param {?=} animate\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype._signalRemovalForInnerTriggers = /**\n     * @param {?} rootElement\n     * @param {?} context\n     * @param {?=} animate\n     * @return {?}\n     */\n    function (rootElement, context, animate) {\n        var _this = this;\n        if (animate === void 0) { animate = false; }\n        // emulate a leave animation for all inner nodes within this node.\n        // If there are no animations found for any of the nodes then clear the cache\n        // for the element.\n        this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true).forEach(function (elm) {\n            // this means that an inner remove() operation has already kicked off\n            // the animation on this element...\n            if (elm[REMOVAL_FLAG])\n                return;\n            var /** @type {?} */ namespaces = _this._engine.fetchNamespacesByElement(elm);\n            if (namespaces.size) {\n                namespaces.forEach(function (ns) { return ns.triggerLeaveAnimation(elm, context, false, true); });\n            }\n            else {\n                _this.clearElementCache(elm);\n            }\n        });\n    };\n    /**\n     * @param {?} element\n     * @param {?} context\n     * @param {?=} destroyAfterComplete\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.triggerLeaveAnimation = /**\n     * @param {?} element\n     * @param {?} context\n     * @param {?=} destroyAfterComplete\n     * @param {?=} defaultToFallback\n     * @return {?}\n     */\n    function (element, context, destroyAfterComplete, defaultToFallback) {\n        var _this = this;\n        var /** @type {?} */ triggerStates = this._engine.statesByElement.get(element);\n        if (triggerStates) {\n            var /** @type {?} */ players_1 = [];\n            Object.keys(triggerStates).forEach(function (triggerName) {\n                // this check is here in the event that an element is removed\n                // twice (both on the host level and the component level)\n                if (_this._triggers[triggerName]) {\n                    var /** @type {?} */ player = _this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n                    if (player) {\n                        players_1.push(player);\n                    }\n                }\n            });\n            if (players_1.length) {\n                this._engine.markElementAsRemoved(this.id, element, true, context);\n                if (destroyAfterComplete) {\n                    optimizeGroupPlayer(players_1).onDone(function () { return _this._engine.processLeaveNode(element); });\n                }\n                return true;\n            }\n        }\n        return false;\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.prepareLeaveAnimationListeners = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var _this = this;\n        var /** @type {?} */ listeners = this._elementListeners.get(element);\n        if (listeners) {\n            var /** @type {?} */ visitedTriggers_1 = new Set();\n            listeners.forEach(function (listener) {\n                var /** @type {?} */ triggerName = listener.name;\n                if (visitedTriggers_1.has(triggerName))\n                    return;\n                visitedTriggers_1.add(triggerName);\n                var /** @type {?} */ trigger = _this._triggers[triggerName];\n                var /** @type {?} */ transition = trigger.fallbackTransition;\n                var /** @type {?} */ elementStates = /** @type {?} */ ((_this._engine.statesByElement.get(element)));\n                var /** @type {?} */ fromState = elementStates[triggerName] || DEFAULT_STATE_VALUE;\n                var /** @type {?} */ toState = new StateValue(VOID_VALUE);\n                var /** @type {?} */ player = new TransitionAnimationPlayer(_this.id, triggerName, element);\n                _this._engine.totalQueuedPlayers++;\n                _this._queue.push({\n                    element: element,\n                    triggerName: triggerName,\n                    transition: transition,\n                    fromState: fromState,\n                    toState: toState,\n                    player: player,\n                    isFallbackTransition: true\n                });\n            });\n        }\n    };\n    /**\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.removeNode = /**\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (element, context) {\n        var _this = this;\n        var /** @type {?} */ engine = this._engine;\n        if (element.childElementCount) {\n            this._signalRemovalForInnerTriggers(element, context, true);\n        }\n        // this means that a * => VOID animation was detected and kicked off\n        if (this.triggerLeaveAnimation(element, context, true))\n            return;\n        // find the player that is animating and make sure that the\n        // removal is delayed until that player has completed\n        var /** @type {?} */ containsPotentialParentTransition = false;\n        if (engine.totalAnimations) {\n            var /** @type {?} */ currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n            // when this `if statement` does not continue forward it means that\n            // a previous animation query has selected the current element and\n            // is animating it. In this situation want to continue fowards and\n            // allow the element to be queued up for animation later.\n            if (currentPlayers && currentPlayers.length) {\n                containsPotentialParentTransition = true;\n            }\n            else {\n                var /** @type {?} */ parent_1 = element;\n                while (parent_1 = parent_1.parentNode) {\n                    var /** @type {?} */ triggers = engine.statesByElement.get(parent_1);\n                    if (triggers) {\n                        containsPotentialParentTransition = true;\n                        break;\n                    }\n                }\n            }\n        }\n        // at this stage we know that the element will either get removed\n        // during flush or will be picked up by a parent query. Either way\n        // we need to fire the listeners for this element when it DOES get\n        // removed (once the query parent animation is done or after flush)\n        this.prepareLeaveAnimationListeners(element);\n        // whether or not a parent has an animation we need to delay the deferral of the leave\n        // operation until we have more information (which we do after flush() has been called)\n        if (containsPotentialParentTransition) {\n            engine.markElementAsRemoved(this.id, element, false, context);\n        }\n        else {\n            // we do this after the flush has occurred such\n            // that the callbacks can be fired\n            engine.afterFlush(function () { return _this.clearElementCache(element); });\n            engine.destroyInnerAnimations(element);\n            engine._onRemovalComplete(element, context);\n        }\n    };\n    /**\n     * @param {?} element\n     * @param {?} parent\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.insertNode = /**\n     * @param {?} element\n     * @param {?} parent\n     * @return {?}\n     */\n    function (element, parent) { addClass(element, this._hostClassName); };\n    /**\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.drainQueuedTransitions = /**\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    function (microtaskId) {\n        var _this = this;\n        var /** @type {?} */ instructions = [];\n        this._queue.forEach(function (entry) {\n            var /** @type {?} */ player = entry.player;\n            if (player.destroyed)\n                return;\n            var /** @type {?} */ element = entry.element;\n            var /** @type {?} */ listeners = _this._elementListeners.get(element);\n            if (listeners) {\n                listeners.forEach(function (listener) {\n                    if (listener.name == entry.triggerName) {\n                        var /** @type {?} */ baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n                        (/** @type {?} */ (baseEvent))['_data'] = microtaskId;\n                        listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n                    }\n                });\n            }\n            if (player.markedForDestroy) {\n                _this._engine.afterFlush(function () {\n                    // now we can destroy the element properly since the event listeners have\n                    // been bound to the player\n                    player.destroy();\n                });\n            }\n            else {\n                instructions.push(entry);\n            }\n        });\n        this._queue = [];\n        return instructions.sort(function (a, b) {\n            // if depCount == 0 them move to front\n            // otherwise if a contains b then move back\n            var /** @type {?} */ d0 = a.transition.ast.depCount;\n            var /** @type {?} */ d1 = b.transition.ast.depCount;\n            if (d0 == 0 || d1 == 0) {\n                return d0 - d1;\n            }\n            return _this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n        });\n    };\n    /**\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.destroy = /**\n     * @param {?} context\n     * @return {?}\n     */\n    function (context) {\n        this.players.forEach(function (p) { return p.destroy(); });\n        this._signalRemovalForInnerTriggers(this.hostElement, context);\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    AnimationTransitionNamespace.prototype.elementContainsData = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ containsData = false;\n        if (this._elementListeners.has(element))\n            containsData = true;\n        containsData =\n            (this._queue.find(function (entry) { return entry.element === element; }) ? true : false) || containsData;\n        return containsData;\n    };\n    return AnimationTransitionNamespace;\n}());\n/**\n * @record\n */\n\nvar TransitionAnimationEngine = /** @class */ (function () {\n    function TransitionAnimationEngine(driver, _normalizer) {\n        this.driver = driver;\n        this._normalizer = _normalizer;\n        this.players = [];\n        this.newHostElements = new Map();\n        this.playersByElement = new Map();\n        this.playersByQueriedElement = new Map();\n        this.statesByElement = new Map();\n        this.disabledNodes = new Set();\n        this.totalAnimations = 0;\n        this.totalQueuedPlayers = 0;\n        this._namespaceLookup = {};\n        this._namespaceList = [];\n        this._flushFns = [];\n        this._whenQuietFns = [];\n        this.namespacesByHostElement = new Map();\n        this.collectedEnterElements = [];\n        this.collectedLeaveElements = [];\n        this.onRemovalComplete = function (element, context) { };\n    }\n    /** @internal */\n    /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._onRemovalComplete = /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (element, context) { this.onRemovalComplete(element, context); };\n    Object.defineProperty(TransitionAnimationEngine.prototype, \"queuedPlayers\", {\n        get: /**\n         * @return {?}\n         */\n        function () {\n            var /** @type {?} */ players = [];\n            this._namespaceList.forEach(function (ns) {\n                ns.players.forEach(function (player) {\n                    if (player.queued) {\n                        players.push(player);\n                    }\n                });\n            });\n            return players;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.createNamespace = /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (namespaceId, hostElement) {\n        var /** @type {?} */ ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n        if (hostElement.parentNode) {\n            this._balanceNamespaceList(ns, hostElement);\n        }\n        else {\n            // defer this later until flush during when the host element has\n            // been inserted so that we know exactly where to place it in\n            // the namespace list\n            this.newHostElements.set(hostElement, ns);\n            // given that this host element is apart of the animation code, it\n            // may or may not be inserted by a parent node that is an of an\n            // animation renderer type. If this happens then we can still have\n            // access to this item when we query for :enter nodes. If the parent\n            // is a renderer then the set data-structure will normalize the entry\n            this.collectEnterElement(hostElement);\n        }\n        return this._namespaceLookup[namespaceId] = ns;\n    };\n    /**\n     * @param {?} ns\n     * @param {?} hostElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._balanceNamespaceList = /**\n     * @param {?} ns\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (ns, hostElement) {\n        var /** @type {?} */ limit = this._namespaceList.length - 1;\n        if (limit >= 0) {\n            var /** @type {?} */ found = false;\n            for (var /** @type {?} */ i = limit; i >= 0; i--) {\n                var /** @type {?} */ nextNamespace = this._namespaceList[i];\n                if (this.driver.containsElement(nextNamespace.hostElement, hostElement)) {\n                    this._namespaceList.splice(i + 1, 0, ns);\n                    found = true;\n                    break;\n                }\n            }\n            if (!found) {\n                this._namespaceList.splice(0, 0, ns);\n            }\n        }\n        else {\n            this._namespaceList.push(ns);\n        }\n        this.namespacesByHostElement.set(hostElement, ns);\n        return ns;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.register = /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (namespaceId, hostElement) {\n        var /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n        if (!ns) {\n            ns = this.createNamespace(namespaceId, hostElement);\n        }\n        return ns;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} name\n     * @param {?} trigger\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.registerTrigger = /**\n     * @param {?} namespaceId\n     * @param {?} name\n     * @param {?} trigger\n     * @return {?}\n     */\n    function (namespaceId, name, trigger) {\n        var /** @type {?} */ ns = this._namespaceLookup[namespaceId];\n        if (ns && ns.register(name, trigger)) {\n            this.totalAnimations++;\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.destroy = /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, context) {\n        var _this = this;\n        if (!namespaceId)\n            return;\n        var /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n        this.afterFlush(function () {\n            _this.namespacesByHostElement.delete(ns.hostElement);\n            delete _this._namespaceLookup[namespaceId];\n            var /** @type {?} */ index = _this._namespaceList.indexOf(ns);\n            if (index >= 0) {\n                _this._namespaceList.splice(index, 1);\n            }\n        });\n        this.afterFlushAnimationsDone(function () { return ns.destroy(context); });\n    };\n    /**\n     * @param {?} id\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._fetchNamespace = /**\n     * @param {?} id\n     * @return {?}\n     */\n    function (id) { return this._namespaceLookup[id]; };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.fetchNamespacesByElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        // normally there should only be one namespace per element, however\n        // if @triggers are placed on both the component element and then\n        // its host element (within the component code) then there will be\n        // two namespaces returned. We use a set here to simply the dedupe\n        // of namespaces incase there are multiple triggers both the elm and host\n        var /** @type {?} */ namespaces = new Set();\n        var /** @type {?} */ elementStates = this.statesByElement.get(element);\n        if (elementStates) {\n            var /** @type {?} */ keys = Object.keys(elementStates);\n            for (var /** @type {?} */ i = 0; i < keys.length; i++) {\n                var /** @type {?} */ nsId = elementStates[keys[i]].namespaceId;\n                if (nsId) {\n                    var /** @type {?} */ ns = this._fetchNamespace(nsId);\n                    if (ns) {\n                        namespaces.add(ns);\n                    }\n                }\n            }\n        }\n        return namespaces;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.trigger = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} value\n     * @return {?}\n     */\n    function (namespaceId, element, name, value) {\n        if (isElementNode(element)) {\n            this._fetchNamespace(namespaceId).trigger(element, name, value);\n            return true;\n        }\n        return false;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.insertNode = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    function (namespaceId, element, parent, insertBefore) {\n        if (!isElementNode(element))\n            return;\n        // special case for when an element is removed and reinserted (move operation)\n        // when this occurs we do not want to use the element for deletion later\n        var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval) {\n            details.setForRemoval = false;\n        }\n        // in the event that the namespaceId is blank then the caller\n        // code does not contain any animation code in it, but it is\n        // just being called so that the node is marked as being inserted\n        if (namespaceId) {\n            var /** @type {?} */ ns = this._fetchNamespace(namespaceId);\n            // This if-statement is a workaround for router issue #21947.\n            // The router sometimes hits a race condition where while a route\n            // is being instantiated a new navigation arrives, triggering leave\n            // animation of DOM that has not been fully initialized, until this\n            // is resolved, we need to handle the scenario when DOM is not in a\n            // consistent state during the animation.\n            if (ns) {\n                ns.insertNode(element, parent);\n            }\n        }\n        // only *directives and host elements are inserted before\n        if (insertBefore) {\n            this.collectEnterElement(element);\n        }\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.collectEnterElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) { this.collectedEnterElements.push(element); };\n    /**\n     * @param {?} element\n     * @param {?} value\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.markElementAsDisabled = /**\n     * @param {?} element\n     * @param {?} value\n     * @return {?}\n     */\n    function (element, value) {\n        if (value) {\n            if (!this.disabledNodes.has(element)) {\n                this.disabledNodes.add(element);\n                addClass(element, DISABLED_CLASSNAME);\n            }\n        }\n        else if (this.disabledNodes.has(element)) {\n            this.disabledNodes.delete(element);\n            removeClass(element, DISABLED_CLASSNAME);\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.removeNode = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, element, context) {\n        if (!isElementNode(element)) {\n            this._onRemovalComplete(element, context);\n            return;\n        }\n        var /** @type {?} */ ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n        if (ns) {\n            ns.removeNode(element, context);\n        }\n        else {\n            this.markElementAsRemoved(namespaceId, element, false, context);\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?=} hasAnimation\n     * @param {?=} context\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.markElementAsRemoved = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?=} hasAnimation\n     * @param {?=} context\n     * @return {?}\n     */\n    function (namespaceId, element, hasAnimation, context) {\n        this.collectedLeaveElements.push(element);\n        element[REMOVAL_FLAG] = {\n            namespaceId: namespaceId,\n            setForRemoval: context, hasAnimation: hasAnimation,\n            removedBeforeQueried: false\n        };\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.listen = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} name\n     * @param {?} phase\n     * @param {?} callback\n     * @return {?}\n     */\n    function (namespaceId, element, name, phase, callback) {\n        if (isElementNode(element)) {\n            return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n        }\n        return function () { };\n    };\n    /**\n     * @param {?} entry\n     * @param {?} subTimelines\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._buildInstruction = /**\n     * @param {?} entry\n     * @param {?} subTimelines\n     * @param {?} enterClassName\n     * @param {?} leaveClassName\n     * @return {?}\n     */\n    function (entry, subTimelines, enterClassName, leaveClassName) {\n        return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines);\n    };\n    /**\n     * @param {?} containerElement\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.destroyInnerAnimations = /**\n     * @param {?} containerElement\n     * @return {?}\n     */\n    function (containerElement) {\n        var _this = this;\n        var /** @type {?} */ elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n        elements.forEach(function (element) { return _this.destroyActiveAnimationsForElement(element); });\n        if (this.playersByQueriedElement.size == 0)\n            return;\n        elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n        elements.forEach(function (element) { return _this.finishActiveQueriedAnimationOnElement(element); });\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.destroyActiveAnimationsForElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ players = this.playersByElement.get(element);\n        if (players) {\n            players.forEach(function (player) {\n                // special case for when an element is set for destruction, but hasn't started.\n                // in this situation we want to delay the destruction until the flush occurs\n                // so that any event listeners attached to the player are triggered.\n                if (player.queued) {\n                    player.markedForDestroy = true;\n                }\n                else {\n                    player.destroy();\n                }\n            });\n        }\n        var /** @type {?} */ stateMap = this.statesByElement.get(element);\n        if (stateMap) {\n            Object.keys(stateMap).forEach(function (triggerName) { return stateMap[triggerName] = DELETED_STATE_VALUE; });\n        }\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.finishActiveQueriedAnimationOnElement = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var /** @type {?} */ players = this.playersByQueriedElement.get(element);\n        if (players) {\n            players.forEach(function (player) { return player.finish(); });\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.whenRenderingDone = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        return new Promise(function (resolve) {\n            if (_this.players.length) {\n                return optimizeGroupPlayer(_this.players).onDone(function () { return resolve(); });\n            }\n            else {\n                resolve();\n            }\n        });\n    };\n    /**\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.processLeaveNode = /**\n     * @param {?} element\n     * @return {?}\n     */\n    function (element) {\n        var _this = this;\n        var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval) {\n            // this will prevent it from removing it twice\n            element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n            if (details.namespaceId) {\n                this.destroyInnerAnimations(element);\n                var /** @type {?} */ ns = this._fetchNamespace(details.namespaceId);\n                if (ns) {\n                    ns.clearElementCache(element);\n                }\n            }\n            this._onRemovalComplete(element, details.setForRemoval);\n        }\n        if (this.driver.matchesElement(element, DISABLED_SELECTOR)) {\n            this.markElementAsDisabled(element, false);\n        }\n        this.driver.query(element, DISABLED_SELECTOR, true).forEach(function (node) {\n            _this.markElementAsDisabled(element, false);\n        });\n    };\n    /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.flush = /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    function (microtaskId) {\n        var _this = this;\n        if (microtaskId === void 0) { microtaskId = -1; }\n        var /** @type {?} */ players = [];\n        if (this.newHostElements.size) {\n            this.newHostElements.forEach(function (ns, element) { return _this._balanceNamespaceList(ns, element); });\n            this.newHostElements.clear();\n        }\n        if (this.totalAnimations && this.collectedEnterElements.length) {\n            for (var /** @type {?} */ i = 0; i < this.collectedEnterElements.length; i++) {\n                var /** @type {?} */ elm = this.collectedEnterElements[i];\n                addClass(elm, STAR_CLASSNAME);\n            }\n        }\n        if (this._namespaceList.length &&\n            (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n            var /** @type {?} */ cleanupFns = [];\n            try {\n                players = this._flushAnimations(cleanupFns, microtaskId);\n            }\n            finally {\n                for (var /** @type {?} */ i = 0; i < cleanupFns.length; i++) {\n                    cleanupFns[i]();\n                }\n            }\n        }\n        else {\n            for (var /** @type {?} */ i = 0; i < this.collectedLeaveElements.length; i++) {\n                var /** @type {?} */ element = this.collectedLeaveElements[i];\n                this.processLeaveNode(element);\n            }\n        }\n        this.totalQueuedPlayers = 0;\n        this.collectedEnterElements.length = 0;\n        this.collectedLeaveElements.length = 0;\n        this._flushFns.forEach(function (fn) { return fn(); });\n        this._flushFns = [];\n        if (this._whenQuietFns.length) {\n            // we move these over to a variable so that\n            // if any new callbacks are registered in another\n            // flush they do not populate the existing set\n            var /** @type {?} */ quietFns_1 = this._whenQuietFns;\n            this._whenQuietFns = [];\n            if (players.length) {\n                optimizeGroupPlayer(players).onDone(function () { quietFns_1.forEach(function (fn) { return fn(); }); });\n            }\n            else {\n                quietFns_1.forEach(function (fn) { return fn(); });\n            }\n        }\n    };\n    /**\n     * @param {?} errors\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.reportError = /**\n     * @param {?} errors\n     * @return {?}\n     */\n    function (errors) {\n        throw new Error(\"Unable to process animations due to the following failed trigger transitions\\n \" + errors.join('\\n'));\n    };\n    /**\n     * @param {?} cleanupFns\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._flushAnimations = /**\n     * @param {?} cleanupFns\n     * @param {?} microtaskId\n     * @return {?}\n     */\n    function (cleanupFns, microtaskId) {\n        var _this = this;\n        var /** @type {?} */ subTimelines = new ElementInstructionMap();\n        var /** @type {?} */ skippedPlayers = [];\n        var /** @type {?} */ skippedPlayersMap = new Map();\n        var /** @type {?} */ queuedInstructions = [];\n        var /** @type {?} */ queriedElements = new Map();\n        var /** @type {?} */ allPreStyleElements = new Map();\n        var /** @type {?} */ allPostStyleElements = new Map();\n        var /** @type {?} */ disabledElementsSet = new Set();\n        this.disabledNodes.forEach(function (node) {\n            disabledElementsSet.add(node);\n            var /** @type {?} */ nodesThatAreDisabled = _this.driver.query(node, QUEUED_SELECTOR, true);\n            for (var /** @type {?} */ i_1 = 0; i_1 < nodesThatAreDisabled.length; i_1++) {\n                disabledElementsSet.add(nodesThatAreDisabled[i_1]);\n            }\n        });\n        var /** @type {?} */ bodyNode = getBodyNode();\n        var /** @type {?} */ allTriggerElements = Array.from(this.statesByElement.keys());\n        var /** @type {?} */ enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n        // this must occur before the instructions are built below such that\n        // the :enter queries match the elements (since the timeline queries\n        // are fired during instruction building).\n        var /** @type {?} */ enterNodeMapIds = new Map();\n        var /** @type {?} */ i = 0;\n        enterNodeMap.forEach(function (nodes, root) {\n            var /** @type {?} */ className = ENTER_CLASSNAME + i++;\n            enterNodeMapIds.set(root, className);\n            nodes.forEach(function (node) { return addClass(node, className); });\n        });\n        var /** @type {?} */ allLeaveNodes = [];\n        var /** @type {?} */ mergedLeaveNodes = new Set();\n        var /** @type {?} */ leaveNodesWithoutAnimations = new Set();\n        for (var /** @type {?} */ i_2 = 0; i_2 < this.collectedLeaveElements.length; i_2++) {\n            var /** @type {?} */ element = this.collectedLeaveElements[i_2];\n            var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n            if (details && details.setForRemoval) {\n                allLeaveNodes.push(element);\n                mergedLeaveNodes.add(element);\n                if (details.hasAnimation) {\n                    this.driver.query(element, STAR_SELECTOR, true).forEach(function (elm) { return mergedLeaveNodes.add(elm); });\n                }\n                else {\n                    leaveNodesWithoutAnimations.add(element);\n                }\n            }\n        }\n        var /** @type {?} */ leaveNodeMapIds = new Map();\n        var /** @type {?} */ leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n        leaveNodeMap.forEach(function (nodes, root) {\n            var /** @type {?} */ className = LEAVE_CLASSNAME + i++;\n            leaveNodeMapIds.set(root, className);\n            nodes.forEach(function (node) { return addClass(node, className); });\n        });\n        cleanupFns.push(function () {\n            enterNodeMap.forEach(function (nodes, root) {\n                var /** @type {?} */ className = /** @type {?} */ ((enterNodeMapIds.get(root)));\n                nodes.forEach(function (node) { return removeClass(node, className); });\n            });\n            leaveNodeMap.forEach(function (nodes, root) {\n                var /** @type {?} */ className = /** @type {?} */ ((leaveNodeMapIds.get(root)));\n                nodes.forEach(function (node) { return removeClass(node, className); });\n            });\n            allLeaveNodes.forEach(function (element) { _this.processLeaveNode(element); });\n        });\n        var /** @type {?} */ allPlayers = [];\n        var /** @type {?} */ erroneousTransitions = [];\n        for (var /** @type {?} */ i_3 = this._namespaceList.length - 1; i_3 >= 0; i_3--) {\n            var /** @type {?} */ ns = this._namespaceList[i_3];\n            ns.drainQueuedTransitions(microtaskId).forEach(function (entry) {\n                var /** @type {?} */ player = entry.player;\n                allPlayers.push(player);\n                var /** @type {?} */ element = entry.element;\n                if (!bodyNode || !_this.driver.containsElement(bodyNode, element)) {\n                    player.destroy();\n                    return;\n                }\n                var /** @type {?} */ leaveClassName = /** @type {?} */ ((leaveNodeMapIds.get(element)));\n                var /** @type {?} */ enterClassName = /** @type {?} */ ((enterNodeMapIds.get(element)));\n                var /** @type {?} */ instruction = /** @type {?} */ ((_this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName)));\n                if (instruction.errors && instruction.errors.length) {\n                    erroneousTransitions.push(instruction);\n                    return;\n                }\n                // if a unmatched transition is queued to go then it SHOULD NOT render\n                // an animation and cancel the previously running animations.\n                if (entry.isFallbackTransition) {\n                    player.onStart(function () { return eraseStyles(element, instruction.fromStyles); });\n                    player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this means that if a parent animation uses this animation as a sub trigger\n                // then it will instruct the timeline builder to not add a player delay, but\n                // instead stretch the first keyframe gap up until the animation starts. The\n                // reason this is important is to prevent extra initialization styles from being\n                // required by the user in the animation.\n                instruction.timelines.forEach(function (tl) { return tl.stretchStartingKeyframe = true; });\n                subTimelines.append(element, instruction.timelines);\n                var /** @type {?} */ tuple = { instruction: instruction, player: player, element: element };\n                queuedInstructions.push(tuple);\n                instruction.queriedElements.forEach(function (element) { return getOrSetAsInMap(queriedElements, element, []).push(player); });\n                instruction.preStyleProps.forEach(function (stringMap, element) {\n                    var /** @type {?} */ props = Object.keys(stringMap);\n                    if (props.length) {\n                        var /** @type {?} */ setVal_1 = /** @type {?} */ ((allPreStyleElements.get(element)));\n                        if (!setVal_1) {\n                            allPreStyleElements.set(element, setVal_1 = new Set());\n                        }\n                        props.forEach(function (prop) { return setVal_1.add(prop); });\n                    }\n                });\n                instruction.postStyleProps.forEach(function (stringMap, element) {\n                    var /** @type {?} */ props = Object.keys(stringMap);\n                    var /** @type {?} */ setVal = /** @type {?} */ ((allPostStyleElements.get(element)));\n                    if (!setVal) {\n                        allPostStyleElements.set(element, setVal = new Set());\n                    }\n                    props.forEach(function (prop) { return setVal.add(prop); });\n                });\n            });\n        }\n        if (erroneousTransitions.length) {\n            var /** @type {?} */ errors_1 = [];\n            erroneousTransitions.forEach(function (instruction) {\n                errors_1.push(\"@\" + instruction.triggerName + \" has failed due to:\\n\"); /** @type {?} */\n                ((instruction.errors)).forEach(function (error) { return errors_1.push(\"- \" + error + \"\\n\"); });\n            });\n            allPlayers.forEach(function (player) { return player.destroy(); });\n            this.reportError(errors_1);\n        }\n        var /** @type {?} */ allPreviousPlayersMap = new Map();\n        // this map works to tell which element in the DOM tree is contained by\n        // which animation. Further down below this map will get populated once\n        // the players are built and in doing so it can efficiently figure out\n        // if a sub player is skipped due to a parent player having priority.\n        var /** @type {?} */ animationElementMap = new Map();\n        queuedInstructions.forEach(function (entry) {\n            var /** @type {?} */ element = entry.element;\n            if (subTimelines.has(element)) {\n                animationElementMap.set(element, element);\n                _this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n            }\n        });\n        skippedPlayers.forEach(function (player) {\n            var /** @type {?} */ element = player.element;\n            var /** @type {?} */ previousPlayers = _this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n            previousPlayers.forEach(function (prevPlayer) {\n                getOrSetAsInMap(allPreviousPlayersMap, element, []).push(prevPlayer);\n                prevPlayer.destroy();\n            });\n        });\n        // this is a special case for nodes that will be removed (either by)\n        // having their own leave animations or by being queried in a container\n        // that will be removed once a parent animation is complete. The idea\n        // here is that * styles must be identical to ! styles because of\n        // backwards compatibility (* is also filled in by default in many places).\n        // Otherwise * styles will return an empty value or auto since the element\n        // that is being getComputedStyle'd will not be visible (since * = destination)\n        var /** @type {?} */ replaceNodes = allLeaveNodes.filter(function (node) {\n            return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n        });\n        // POST STAGE: fill the * styles\n        var /** @type {?} */ postStylesMap = new Map();\n        var /** @type {?} */ allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, _angular_animations.AUTO_STYLE);\n        allLeaveQueriedNodes.forEach(function (node) {\n            if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n                replaceNodes.push(node);\n            }\n        });\n        // PRE STAGE: fill the ! styles\n        var /** @type {?} */ preStylesMap = new Map();\n        enterNodeMap.forEach(function (nodes, root) {\n            cloakAndComputeStyles(preStylesMap, _this.driver, new Set(nodes), allPreStyleElements, _angular_animations.ɵPRE_STYLE);\n        });\n        replaceNodes.forEach(function (node) {\n            var /** @type {?} */ post = postStylesMap.get(node);\n            var /** @type {?} */ pre = preStylesMap.get(node);\n            postStylesMap.set(node, /** @type {?} */ (__assign({}, post, pre)));\n        });\n        var /** @type {?} */ rootPlayers = [];\n        var /** @type {?} */ subPlayers = [];\n        var /** @type {?} */ NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n        queuedInstructions.forEach(function (entry) {\n            var element = entry.element, player = entry.player, instruction = entry.instruction;\n            // this means that it was never consumed by a parent animation which\n            // means that it is independent and therefore should be set for animation\n            if (subTimelines.has(element)) {\n                if (disabledElementsSet.has(element)) {\n                    player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this will flow up the DOM and query the map to figure out\n                // if a parent animation has priority over it. In the situation\n                // that a parent is detected then it will cancel the loop. If\n                // nothing is detected, or it takes a few hops to find a parent,\n                // then it will fill in the missing nodes and signal them as having\n                // a detected parent (or a NO_PARENT value via a special constant).\n                var /** @type {?} */ parentWithAnimation_1 = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n                if (animationElementMap.size > 1) {\n                    var /** @type {?} */ elm = element;\n                    var /** @type {?} */ parentsToAdd = [];\n                    while (elm = elm.parentNode) {\n                        var /** @type {?} */ detectedParent = animationElementMap.get(elm);\n                        if (detectedParent) {\n                            parentWithAnimation_1 = detectedParent;\n                            break;\n                        }\n                        parentsToAdd.push(elm);\n                    }\n                    parentsToAdd.forEach(function (parent) { return animationElementMap.set(parent, parentWithAnimation_1); });\n                }\n                var /** @type {?} */ innerPlayer = _this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n                player.setRealPlayer(innerPlayer);\n                if (parentWithAnimation_1 === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n                    rootPlayers.push(player);\n                }\n                else {\n                    var /** @type {?} */ parentPlayers = _this.playersByElement.get(parentWithAnimation_1);\n                    if (parentPlayers && parentPlayers.length) {\n                        player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n                    }\n                    skippedPlayers.push(player);\n                }\n            }\n            else {\n                eraseStyles(element, instruction.fromStyles);\n                player.onDestroy(function () { return setStyles(element, instruction.toStyles); });\n                // there still might be a ancestor player animating this\n                // element therefore we will still add it as a sub player\n                // even if its animation may be disabled\n                subPlayers.push(player);\n                if (disabledElementsSet.has(element)) {\n                    skippedPlayers.push(player);\n                }\n            }\n        });\n        // find all of the sub players' corresponding inner animation player\n        subPlayers.forEach(function (player) {\n            // even if any players are not found for a sub animation then it\n            // will still complete itself after the next tick since it's Noop\n            var /** @type {?} */ playersForElement = skippedPlayersMap.get(player.element);\n            if (playersForElement && playersForElement.length) {\n                var /** @type {?} */ innerPlayer = optimizeGroupPlayer(playersForElement);\n                player.setRealPlayer(innerPlayer);\n            }\n        });\n        // the reason why we don't actually play the animation is\n        // because all that a skipped player is designed to do is to\n        // fire the start/done transition callback events\n        skippedPlayers.forEach(function (player) {\n            if (player.parentPlayer) {\n                player.syncPlayerEvents(player.parentPlayer);\n            }\n            else {\n                player.destroy();\n            }\n        });\n        // run through all of the queued removals and see if they\n        // were picked up by a query. If not then perform the removal\n        // operation right away unless a parent animation is ongoing.\n        for (var /** @type {?} */ i_4 = 0; i_4 < allLeaveNodes.length; i_4++) {\n            var /** @type {?} */ element = allLeaveNodes[i_4];\n            var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n            removeClass(element, LEAVE_CLASSNAME);\n            // this means the element has a removal animation that is being\n            // taken care of and therefore the inner elements will hang around\n            // until that animation is over (or the parent queried animation)\n            if (details && details.hasAnimation)\n                continue;\n            var /** @type {?} */ players = [];\n            // if this element is queried or if it contains queried children\n            // then we want for the element not to be removed from the page\n            // until the queried animations have finished\n            if (queriedElements.size) {\n                var /** @type {?} */ queriedPlayerResults = queriedElements.get(element);\n                if (queriedPlayerResults && queriedPlayerResults.length) {\n                    players.push.apply(players, queriedPlayerResults);\n                }\n                var /** @type {?} */ queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n                for (var /** @type {?} */ j = 0; j < queriedInnerElements.length; j++) {\n                    var /** @type {?} */ queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n                    if (queriedPlayers && queriedPlayers.length) {\n                        players.push.apply(players, queriedPlayers);\n                    }\n                }\n            }\n            var /** @type {?} */ activePlayers = players.filter(function (p) { return !p.destroyed; });\n            if (activePlayers.length) {\n                removeNodesAfterAnimationDone(this, element, activePlayers);\n            }\n            else {\n                this.processLeaveNode(element);\n            }\n        }\n        // this is required so the cleanup method doesn't remove them\n        allLeaveNodes.length = 0;\n        rootPlayers.forEach(function (player) {\n            _this.players.push(player);\n            player.onDone(function () {\n                player.destroy();\n                var /** @type {?} */ index = _this.players.indexOf(player);\n                _this.players.splice(index, 1);\n            });\n            player.play();\n        });\n        return rootPlayers;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.elementContainsData = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @return {?}\n     */\n    function (namespaceId, element) {\n        var /** @type {?} */ containsData = false;\n        var /** @type {?} */ details = /** @type {?} */ (element[REMOVAL_FLAG]);\n        if (details && details.setForRemoval)\n            containsData = true;\n        if (this.playersByElement.has(element))\n            containsData = true;\n        if (this.playersByQueriedElement.has(element))\n            containsData = true;\n        if (this.statesByElement.has(element))\n            containsData = true;\n        return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n    };\n    /**\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.afterFlush = /**\n     * @param {?} callback\n     * @return {?}\n     */\n    function (callback) { this._flushFns.push(callback); };\n    /**\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype.afterFlushAnimationsDone = /**\n     * @param {?} callback\n     * @return {?}\n     */\n    function (callback) { this._whenQuietFns.push(callback); };\n    /**\n     * @param {?} element\n     * @param {?} isQueriedElement\n     * @param {?=} namespaceId\n     * @param {?=} triggerName\n     * @param {?=} toStateValue\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._getPreviousPlayers = /**\n     * @param {?} element\n     * @param {?} isQueriedElement\n     * @param {?=} namespaceId\n     * @param {?=} triggerName\n     * @param {?=} toStateValue\n     * @return {?}\n     */\n    function (element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n        var /** @type {?} */ players = [];\n        if (isQueriedElement) {\n            var /** @type {?} */ queriedElementPlayers = this.playersByQueriedElement.get(element);\n            if (queriedElementPlayers) {\n                players = queriedElementPlayers;\n            }\n        }\n        else {\n            var /** @type {?} */ elementPlayers = this.playersByElement.get(element);\n            if (elementPlayers) {\n                var /** @type {?} */ isRemovalAnimation_1 = !toStateValue || toStateValue == VOID_VALUE;\n                elementPlayers.forEach(function (player) {\n                    if (player.queued)\n                        return;\n                    if (!isRemovalAnimation_1 && player.triggerName != triggerName)\n                        return;\n                    players.push(player);\n                });\n            }\n        }\n        if (namespaceId || triggerName) {\n            players = players.filter(function (player) {\n                if (namespaceId && namespaceId != player.namespaceId)\n                    return false;\n                if (triggerName && triggerName != player.triggerName)\n                    return false;\n                return true;\n            });\n        }\n        return players;\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._beforeAnimationBuild = /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @return {?}\n     */\n    function (namespaceId, instruction, allPreviousPlayersMap) {\n        var /** @type {?} */ triggerName = instruction.triggerName;\n        var /** @type {?} */ rootElement = instruction.element;\n        // when a removal animation occurs, ALL previous players are collected\n        // and destroyed (even if they are outside of the current namespace)\n        var /** @type {?} */ targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n        var /** @type {?} */ targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n        var _loop_1 = function (timelineInstruction) {\n            var /** @type {?} */ element = timelineInstruction.element;\n            var /** @type {?} */ isQueriedElement = element !== rootElement;\n            var /** @type {?} */ players = getOrSetAsInMap(allPreviousPlayersMap, element, []);\n            var /** @type {?} */ previousPlayers = this_1._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n            previousPlayers.forEach(function (player) {\n                var /** @type {?} */ realPlayer = /** @type {?} */ (player.getRealPlayer());\n                if (realPlayer.beforeDestroy) {\n                    realPlayer.beforeDestroy();\n                }\n                player.destroy();\n                players.push(player);\n            });\n        };\n        var this_1 = this;\n        for (var _i = 0, _a = instruction.timelines; _i < _a.length; _i++) {\n            var timelineInstruction = _a[_i];\n            _loop_1(timelineInstruction);\n        }\n        // this needs to be done so that the PRE/POST styles can be\n        // computed properly without interfering with the previous animation\n        eraseStyles(rootElement, instruction.fromStyles);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @param {?} skippedPlayersMap\n     * @param {?} preStylesMap\n     * @param {?} postStylesMap\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._buildAnimation = /**\n     * @param {?} namespaceId\n     * @param {?} instruction\n     * @param {?} allPreviousPlayersMap\n     * @param {?} skippedPlayersMap\n     * @param {?} preStylesMap\n     * @param {?} postStylesMap\n     * @return {?}\n     */\n    function (namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n        var _this = this;\n        var /** @type {?} */ triggerName = instruction.triggerName;\n        var /** @type {?} */ rootElement = instruction.element;\n        // we first run this so that the previous animation player\n        // data can be passed into the successive animation players\n        var /** @type {?} */ allQueriedPlayers = [];\n        var /** @type {?} */ allConsumedElements = new Set();\n        var /** @type {?} */ allSubElements = new Set();\n        var /** @type {?} */ allNewPlayers = instruction.timelines.map(function (timelineInstruction) {\n            var /** @type {?} */ element = timelineInstruction.element;\n            allConsumedElements.add(element);\n            // FIXME (matsko): make sure to-be-removed animations are removed properly\n            var /** @type {?} */ details = element[REMOVAL_FLAG];\n            if (details && details.removedBeforeQueried)\n                return new _angular_animations.NoopAnimationPlayer();\n            var /** @type {?} */ isQueriedElement = element !== rootElement;\n            var /** @type {?} */ previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY)\n                .map(function (p) { return p.getRealPlayer(); }))\n                .filter(function (p) {\n                // the `element` is not apart of the AnimationPlayer definition, but\n                // Mock/WebAnimations\n                // use the element within their implementation. This will be added in Angular5 to\n                // AnimationPlayer\n                var /** @type {?} */ pp = /** @type {?} */ (p);\n                return pp.element ? pp.element === element : false;\n            });\n            var /** @type {?} */ preStyles = preStylesMap.get(element);\n            var /** @type {?} */ postStyles = postStylesMap.get(element);\n            var /** @type {?} */ keyframes = normalizeKeyframes(_this.driver, _this._normalizer, element, timelineInstruction.keyframes, preStyles, postStyles);\n            var /** @type {?} */ player = _this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n            // this means that this particular player belongs to a sub trigger. It is\n            // important that we match this player up with the corresponding (@trigger.listener)\n            if (timelineInstruction.subTimeline && skippedPlayersMap) {\n                allSubElements.add(element);\n            }\n            if (isQueriedElement) {\n                var /** @type {?} */ wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n                wrappedPlayer.setRealPlayer(player);\n                allQueriedPlayers.push(wrappedPlayer);\n            }\n            return player;\n        });\n        allQueriedPlayers.forEach(function (player) {\n            getOrSetAsInMap(_this.playersByQueriedElement, player.element, []).push(player);\n            player.onDone(function () { return deleteOrUnsetInMap(_this.playersByQueriedElement, player.element, player); });\n        });\n        allConsumedElements.forEach(function (element) { return addClass(element, NG_ANIMATING_CLASSNAME); });\n        var /** @type {?} */ player = optimizeGroupPlayer(allNewPlayers);\n        player.onDestroy(function () {\n            allConsumedElements.forEach(function (element) { return removeClass(element, NG_ANIMATING_CLASSNAME); });\n            setStyles(rootElement, instruction.toStyles);\n        });\n        // this basically makes all of the callbacks for sub element animations\n        // be dependent on the upper players for when they finish\n        allSubElements.forEach(function (element) { getOrSetAsInMap(skippedPlayersMap, element, []).push(player); });\n        return player;\n    };\n    /**\n     * @param {?} instruction\n     * @param {?} keyframes\n     * @param {?} previousPlayers\n     * @return {?}\n     */\n    TransitionAnimationEngine.prototype._buildPlayer = /**\n     * @param {?} instruction\n     * @param {?} keyframes\n     * @param {?} previousPlayers\n     * @return {?}\n     */\n    function (instruction, keyframes, previousPlayers) {\n        if (keyframes.length > 0) {\n            return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n        }\n        // special case for when an empty transition|definition is provided\n        // ... there is no point in rendering an empty animation\n        return new _angular_animations.NoopAnimationPlayer();\n    };\n    return TransitionAnimationEngine;\n}());\nvar TransitionAnimationPlayer = /** @class */ (function () {\n    function TransitionAnimationPlayer(namespaceId, triggerName, element) {\n        this.namespaceId = namespaceId;\n        this.triggerName = triggerName;\n        this.element = element;\n        this._player = new _angular_animations.NoopAnimationPlayer();\n        this._containsRealPlayer = false;\n        this._queuedCallbacks = {};\n        this.destroyed = false;\n        this.markedForDestroy = false;\n        this.queued = true;\n    }\n    /**\n     * @param {?} player\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.setRealPlayer = /**\n     * @param {?} player\n     * @return {?}\n     */\n    function (player) {\n        var _this = this;\n        if (this._containsRealPlayer)\n            return;\n        this._player = player;\n        Object.keys(this._queuedCallbacks).forEach(function (phase) {\n            _this._queuedCallbacks[phase].forEach(function (callback) { return listenOnPlayer(player, phase, undefined, callback); });\n        });\n        this._queuedCallbacks = {};\n        this._containsRealPlayer = true;\n        (/** @type {?} */ (this)).queued = false;\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.getRealPlayer = /**\n     * @return {?}\n     */\n    function () { return this._player; };\n    /**\n     * @param {?} player\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.syncPlayerEvents = /**\n     * @param {?} player\n     * @return {?}\n     */\n    function (player) {\n        var _this = this;\n        var /** @type {?} */ p = /** @type {?} */ (this._player);\n        if (p.triggerCallback) {\n            player.onStart(function () { return p.triggerCallback('start'); });\n        }\n        player.onDone(function () { return _this.finish(); });\n        player.onDestroy(function () { return _this.destroy(); });\n    };\n    /**\n     * @param {?} name\n     * @param {?} callback\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype._queueEvent = /**\n     * @param {?} name\n     * @param {?} callback\n     * @return {?}\n     */\n    function (name, callback) {\n        getOrSetAsInMap(this._queuedCallbacks, name, []).push(callback);\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.onDone = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        if (this.queued) {\n            this._queueEvent('done', fn);\n        }\n        this._player.onDone(fn);\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.onStart = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        if (this.queued) {\n            this._queueEvent('start', fn);\n        }\n        this._player.onStart(fn);\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.onDestroy = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) {\n        if (this.queued) {\n            this._queueEvent('destroy', fn);\n        }\n        this._player.onDestroy(fn);\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () { this._player.init(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this.queued ? false : this._player.hasStarted(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.play(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.pause = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.pause(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.restart = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.restart(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () { this._player.finish(); };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        (/** @type {?} */ (this)).destroyed = true;\n        this._player.destroy();\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.reset = /**\n     * @return {?}\n     */\n    function () { !this.queued && this._player.reset(); };\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.setPosition = /**\n     * @param {?} p\n     * @return {?}\n     */\n    function (p) {\n        if (!this.queued) {\n            this._player.setPosition(p);\n        }\n    };\n    /**\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.getPosition = /**\n     * @return {?}\n     */\n    function () { return this.queued ? 0 : this._player.getPosition(); };\n    Object.defineProperty(TransitionAnimationPlayer.prototype, \"totalTime\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this._player.totalTime; },\n        enumerable: true,\n        configurable: true\n    });\n    /* @internal */\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    TransitionAnimationPlayer.prototype.triggerCallback = /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    function (phaseName) {\n        var /** @type {?} */ p = /** @type {?} */ (this._player);\n        if (p.triggerCallback) {\n            p.triggerCallback(phaseName);\n        }\n    };\n    return TransitionAnimationPlayer;\n}());\n/**\n * @param {?} map\n * @param {?} key\n * @param {?} value\n * @return {?}\n */\nfunction deleteOrUnsetInMap(map, key, value) {\n    var /** @type {?} */ currentValues;\n    if (map instanceof Map) {\n        currentValues = map.get(key);\n        if (currentValues) {\n            if (currentValues.length) {\n                var /** @type {?} */ index = currentValues.indexOf(value);\n                currentValues.splice(index, 1);\n            }\n            if (currentValues.length == 0) {\n                map.delete(key);\n            }\n        }\n    }\n    else {\n        currentValues = map[key];\n        if (currentValues) {\n            if (currentValues.length) {\n                var /** @type {?} */ index = currentValues.indexOf(value);\n                currentValues.splice(index, 1);\n            }\n            if (currentValues.length == 0) {\n                delete map[key];\n            }\n        }\n    }\n    return currentValues;\n}\n/**\n * @param {?} value\n * @return {?}\n */\nfunction normalizeTriggerValue(value) {\n    // we use `!= null` here because it's the most simple\n    // way to test against a \"falsy\" value without mixing\n    // in empty strings or a zero value. DO NOT OPTIMIZE.\n    return value != null ? value : null;\n}\n/**\n * @param {?} node\n * @return {?}\n */\nfunction isElementNode(node) {\n    return node && node['nodeType'] === 1;\n}\n/**\n * @param {?} eventName\n * @return {?}\n */\nfunction isTriggerEventValid(eventName) {\n    return eventName == 'start' || eventName == 'done';\n}\n/**\n * @param {?} element\n * @param {?=} value\n * @return {?}\n */\nfunction cloakElement(element, value) {\n    var /** @type {?} */ oldValue = element.style.display;\n    element.style.display = value != null ? value : 'none';\n    return oldValue;\n}\n/**\n * @param {?} valuesMap\n * @param {?} driver\n * @param {?} elements\n * @param {?} elementPropsMap\n * @param {?} defaultStyle\n * @return {?}\n */\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n    var /** @type {?} */ cloakVals = [];\n    elements.forEach(function (element) { return cloakVals.push(cloakElement(element)); });\n    var /** @type {?} */ failedElements = [];\n    elementPropsMap.forEach(function (props, element) {\n        var /** @type {?} */ styles = {};\n        props.forEach(function (prop) {\n            var /** @type {?} */ value = styles[prop] = driver.computeStyle(element, prop, defaultStyle);\n            // there is no easy way to detect this because a sub element could be removed\n            // by a parent animation element being detached.\n            if (!value || value.length == 0) {\n                element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n                failedElements.push(element);\n            }\n        });\n        valuesMap.set(element, styles);\n    });\n    // we use a index variable here since Set.forEach(a, i) does not return\n    // an index value for the closure (but instead just the value)\n    var /** @type {?} */ i = 0;\n    elements.forEach(function (element) { return cloakElement(element, cloakVals[i++]); });\n    return failedElements;\n}\n/**\n * @param {?} roots\n * @param {?} nodes\n * @return {?}\n */\nfunction buildRootMap(roots, nodes) {\n    var /** @type {?} */ rootMap = new Map();\n    roots.forEach(function (root) { return rootMap.set(root, []); });\n    if (nodes.length == 0)\n        return rootMap;\n    var /** @type {?} */ NULL_NODE = 1;\n    var /** @type {?} */ nodeSet = new Set(nodes);\n    var /** @type {?} */ localRootMap = new Map();\n    /**\n     * @param {?} node\n     * @return {?}\n     */\n    function getRoot(node) {\n        if (!node)\n            return NULL_NODE;\n        var /** @type {?} */ root = localRootMap.get(node);\n        if (root)\n            return root;\n        var /** @type {?} */ parent = node.parentNode;\n        if (rootMap.has(parent)) {\n            // ngIf inside @trigger\n            root = parent;\n        }\n        else if (nodeSet.has(parent)) {\n            // ngIf inside ngIf\n            root = NULL_NODE;\n        }\n        else {\n            // recurse upwards\n            root = getRoot(parent);\n        }\n        localRootMap.set(node, root);\n        return root;\n    }\n    nodes.forEach(function (node) {\n        var /** @type {?} */ root = getRoot(node);\n        if (root !== NULL_NODE) {\n            /** @type {?} */ ((rootMap.get(root))).push(node);\n        }\n    });\n    return rootMap;\n}\nvar CLASSES_CACHE_KEY = '$$classes';\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction addClass(element, className) {\n    if (element.classList) {\n        element.classList.add(className);\n    }\n    else {\n        var /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        if (!classes) {\n            classes = element[CLASSES_CACHE_KEY] = {};\n        }\n        classes[className] = true;\n    }\n}\n/**\n * @param {?} element\n * @param {?} className\n * @return {?}\n */\nfunction removeClass(element, className) {\n    if (element.classList) {\n        element.classList.remove(className);\n    }\n    else {\n        var /** @type {?} */ classes = element[CLASSES_CACHE_KEY];\n        if (classes) {\n            delete classes[className];\n        }\n    }\n}\n/**\n * @param {?} engine\n * @param {?} element\n * @param {?} players\n * @return {?}\n */\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n    optimizeGroupPlayer(players).onDone(function () { return engine.processLeaveNode(element); });\n}\n/**\n * @param {?} players\n * @return {?}\n */\nfunction flattenGroupPlayers(players) {\n    var /** @type {?} */ finalPlayers = [];\n    _flattenGroupPlayersRecur(players, finalPlayers);\n    return finalPlayers;\n}\n/**\n * @param {?} players\n * @param {?} finalPlayers\n * @return {?}\n */\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n    for (var /** @type {?} */ i = 0; i < players.length; i++) {\n        var /** @type {?} */ player = players[i];\n        if (player instanceof _angular_animations.ɵAnimationGroupPlayer) {\n            _flattenGroupPlayersRecur(player.players, finalPlayers);\n        }\n        else {\n            finalPlayers.push(/** @type {?} */ (player));\n        }\n    }\n}\n/**\n * @param {?} a\n * @param {?} b\n * @return {?}\n */\nfunction objEquals(a, b) {\n    var /** @type {?} */ k1 = Object.keys(a);\n    var /** @type {?} */ k2 = Object.keys(b);\n    if (k1.length != k2.length)\n        return false;\n    for (var /** @type {?} */ i = 0; i < k1.length; i++) {\n        var /** @type {?} */ prop = k1[i];\n        if (!b.hasOwnProperty(prop) || a[prop] !== b[prop])\n            return false;\n    }\n    return true;\n}\n/**\n * @param {?} element\n * @param {?} allPreStyleElements\n * @param {?} allPostStyleElements\n * @return {?}\n */\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n    var /** @type {?} */ postEntry = allPostStyleElements.get(element);\n    if (!postEntry)\n        return false;\n    var /** @type {?} */ preEntry = allPreStyleElements.get(element);\n    if (preEntry) {\n        postEntry.forEach(function (data) { return ((preEntry)).add(data); });\n    }\n    else {\n        allPreStyleElements.set(element, postEntry);\n    }\n    allPostStyleElements.delete(element);\n    return true;\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar AnimationEngine = /** @class */ (function () {\n    function AnimationEngine(_driver, normalizer) {\n        var _this = this;\n        this._driver = _driver;\n        this._triggerCache = {};\n        this.onRemovalComplete = function (element, context) { };\n        this._transitionEngine = new TransitionAnimationEngine(_driver, normalizer);\n        this._timelineEngine = new TimelineAnimationEngine(_driver, normalizer);\n        this._transitionEngine.onRemovalComplete = function (element, context) {\n            return _this.onRemovalComplete(element, context);\n        };\n    }\n    /**\n     * @param {?} componentId\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @param {?} name\n     * @param {?} metadata\n     * @return {?}\n     */\n    AnimationEngine.prototype.registerTrigger = /**\n     * @param {?} componentId\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @param {?} name\n     * @param {?} metadata\n     * @return {?}\n     */\n    function (componentId, namespaceId, hostElement, name, metadata) {\n        var /** @type {?} */ cacheKey = componentId + '-' + name;\n        var /** @type {?} */ trigger = this._triggerCache[cacheKey];\n        if (!trigger) {\n            var /** @type {?} */ errors = [];\n            var /** @type {?} */ ast = /** @type {?} */ (buildAnimationAst(this._driver, /** @type {?} */ (metadata), errors));\n            if (errors.length) {\n                throw new Error(\"The animation trigger \\\"\" + name + \"\\\" has failed to build due to the following errors:\\n - \" + errors.join(\"\\n - \"));\n            }\n            trigger = buildTrigger(name, ast);\n            this._triggerCache[cacheKey] = trigger;\n        }\n        this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    AnimationEngine.prototype.register = /**\n     * @param {?} namespaceId\n     * @param {?} hostElement\n     * @return {?}\n     */\n    function (namespaceId, hostElement) {\n        this._transitionEngine.register(namespaceId, hostElement);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationEngine.prototype.destroy = /**\n     * @param {?} namespaceId\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, context) {\n        this._transitionEngine.destroy(namespaceId, context);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    AnimationEngine.prototype.onInsert = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} parent\n     * @param {?} insertBefore\n     * @return {?}\n     */\n    function (namespaceId, element, parent, insertBefore) {\n        this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    AnimationEngine.prototype.onRemove = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} context\n     * @return {?}\n     */\n    function (namespaceId, element, context) {\n        this._transitionEngine.removeNode(namespaceId, element, context);\n    };\n    /**\n     * @param {?} element\n     * @param {?} disable\n     * @return {?}\n     */\n    AnimationEngine.prototype.disableAnimations = /**\n     * @param {?} element\n     * @param {?} disable\n     * @return {?}\n     */\n    function (element, disable) {\n        this._transitionEngine.markElementAsDisabled(element, disable);\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} property\n     * @param {?} value\n     * @return {?}\n     */\n    AnimationEngine.prototype.process = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} property\n     * @param {?} value\n     * @return {?}\n     */\n    function (namespaceId, element, property, value) {\n        if (property.charAt(0) == '@') {\n            var _a = parseTimelineCommand(property), id = _a[0], action = _a[1];\n            var /** @type {?} */ args = /** @type {?} */ (value);\n            this._timelineEngine.command(id, element, action, args);\n        }\n        else {\n            this._transitionEngine.trigger(namespaceId, element, property, value);\n        }\n    };\n    /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} eventPhase\n     * @param {?} callback\n     * @return {?}\n     */\n    AnimationEngine.prototype.listen = /**\n     * @param {?} namespaceId\n     * @param {?} element\n     * @param {?} eventName\n     * @param {?} eventPhase\n     * @param {?} callback\n     * @return {?}\n     */\n    function (namespaceId, element, eventName, eventPhase, callback) {\n        // @@listen\n        if (eventName.charAt(0) == '@') {\n            var _a = parseTimelineCommand(eventName), id = _a[0], action = _a[1];\n            return this._timelineEngine.listen(id, element, action, callback);\n        }\n        return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n    };\n    /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    AnimationEngine.prototype.flush = /**\n     * @param {?=} microtaskId\n     * @return {?}\n     */\n    function (microtaskId) {\n        if (microtaskId === void 0) { microtaskId = -1; }\n        this._transitionEngine.flush(microtaskId);\n    };\n    Object.defineProperty(AnimationEngine.prototype, \"players\", {\n        get: /**\n         * @return {?}\n         */\n        function () {\n            return (/** @type {?} */ (this._transitionEngine.players))\n                .concat(/** @type {?} */ (this._timelineEngine.players));\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @return {?}\n     */\n    AnimationEngine.prototype.whenRenderingDone = /**\n     * @return {?}\n     */\n    function () { return this._transitionEngine.whenRenderingDone(); };\n    return AnimationEngine;\n}());\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar WebAnimationsPlayer = /** @class */ (function () {\n    function WebAnimationsPlayer(element, keyframes, options, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        var _this = this;\n        this.element = element;\n        this.keyframes = keyframes;\n        this.options = options;\n        this.previousPlayers = previousPlayers;\n        this._onDoneFns = [];\n        this._onStartFns = [];\n        this._onDestroyFns = [];\n        this._initialized = false;\n        this._finished = false;\n        this._started = false;\n        this._destroyed = false;\n        this.time = 0;\n        this.parentPlayer = null;\n        this.previousStyles = {};\n        this.currentSnapshot = {};\n        this._duration = /** @type {?} */ (options['duration']);\n        this._delay = /** @type {?} */ (options['delay']) || 0;\n        this.time = this._duration + this._delay;\n        if (allowPreviousPlayerStylesMerge(this._duration, this._delay)) {\n            previousPlayers.forEach(function (player) {\n                var /** @type {?} */ styles = player.currentSnapshot;\n                Object.keys(styles).forEach(function (prop) { return _this.previousStyles[prop] = styles[prop]; });\n            });\n        }\n    }\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._onFinish = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach(function (fn) { return fn(); });\n            this._onDoneFns = [];\n        }\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.init = /**\n     * @return {?}\n     */\n    function () {\n        this._buildPlayer();\n        this._preparePlayerBeforeStart();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._buildPlayer = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        if (this._initialized)\n            return;\n        this._initialized = true;\n        var /** @type {?} */ keyframes = this.keyframes.map(function (styles) { return copyStyles(styles, false); });\n        var /** @type {?} */ previousStyleProps = Object.keys(this.previousStyles);\n        if (previousStyleProps.length && keyframes.length) {\n            var /** @type {?} */ startingKeyframe_1 = keyframes[0];\n            var /** @type {?} */ missingStyleProps_1 = [];\n            previousStyleProps.forEach(function (prop) {\n                if (!startingKeyframe_1.hasOwnProperty(prop)) {\n                    missingStyleProps_1.push(prop);\n                }\n                startingKeyframe_1[prop] = _this.previousStyles[prop];\n            });\n            if (missingStyleProps_1.length) {\n                var /** @type {?} */ self_1 = this;\n                var _loop_1 = function () {\n                    var /** @type {?} */ kf = keyframes[i];\n                    missingStyleProps_1.forEach(function (prop) {\n                        kf[prop] = _computeStyle(self_1.element, prop);\n                    });\n                };\n                // tslint:disable-next-line\n                for (var /** @type {?} */ i = 1; i < keyframes.length; i++) {\n                    _loop_1();\n                }\n            }\n        }\n        (/** @type {?} */ (this)).domPlayer =\n            this._triggerWebAnimation(this.element, keyframes, this.options);\n        this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : {};\n        this.domPlayer.addEventListener('finish', function () { return _this._onFinish(); });\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._preparePlayerBeforeStart = /**\n     * @return {?}\n     */\n    function () {\n        // this is required so that the player doesn't start to animate right away\n        if (this._delay) {\n            this._resetDomPlayerState();\n        }\n        else {\n            this.domPlayer.pause();\n        }\n    };\n    /** @internal */\n    /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} options\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._triggerWebAnimation = /**\n     * \\@internal\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} options\n     * @return {?}\n     */\n    function (element, keyframes, options) {\n        // jscompiler doesn't seem to know animate is a native property because it's not fully\n        // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n        return /** @type {?} */ (element['animate'](keyframes, options));\n    };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.onStart = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onStartFns.push(fn); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.onDone = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDoneFns.push(fn); };\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.onDestroy = /**\n     * @param {?} fn\n     * @return {?}\n     */\n    function (fn) { this._onDestroyFns.push(fn); };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.play = /**\n     * @return {?}\n     */\n    function () {\n        this._buildPlayer();\n        if (!this.hasStarted()) {\n            this._onStartFns.forEach(function (fn) { return fn(); });\n            this._onStartFns = [];\n            this._started = true;\n        }\n        this.domPlayer.play();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.pause = /**\n     * @return {?}\n     */\n    function () {\n        this.init();\n        this.domPlayer.pause();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.finish = /**\n     * @return {?}\n     */\n    function () {\n        this.init();\n        this._onFinish();\n        this.domPlayer.finish();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.reset = /**\n     * @return {?}\n     */\n    function () {\n        this._resetDomPlayerState();\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype._resetDomPlayerState = /**\n     * @return {?}\n     */\n    function () {\n        if (this.domPlayer) {\n            this.domPlayer.cancel();\n        }\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.restart = /**\n     * @return {?}\n     */\n    function () {\n        this.reset();\n        this.play();\n    };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.hasStarted = /**\n     * @return {?}\n     */\n    function () { return this._started; };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.destroy = /**\n     * @return {?}\n     */\n    function () {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._resetDomPlayerState();\n            this._onFinish();\n            this._onDestroyFns.forEach(function (fn) { return fn(); });\n            this._onDestroyFns = [];\n        }\n    };\n    /**\n     * @param {?} p\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.setPosition = /**\n     * @param {?} p\n     * @return {?}\n     */\n    function (p) { this.domPlayer.currentTime = p * this.time; };\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.getPosition = /**\n     * @return {?}\n     */\n    function () { return this.domPlayer.currentTime / this.time; };\n    Object.defineProperty(WebAnimationsPlayer.prototype, \"totalTime\", {\n        get: /**\n         * @return {?}\n         */\n        function () { return this._delay + this._duration; },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.beforeDestroy = /**\n     * @return {?}\n     */\n    function () {\n        var _this = this;\n        var /** @type {?} */ styles = {};\n        if (this.hasStarted()) {\n            Object.keys(this._finalKeyframe).forEach(function (prop) {\n                if (prop != 'offset') {\n                    styles[prop] =\n                        _this._finished ? _this._finalKeyframe[prop] : _computeStyle(_this.element, prop);\n                }\n            });\n        }\n        this.currentSnapshot = styles;\n    };\n    /* @internal */\n    /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    WebAnimationsPlayer.prototype.triggerCallback = /**\n     * @param {?} phaseName\n     * @return {?}\n     */\n    function (phaseName) {\n        var /** @type {?} */ methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach(function (fn) { return fn(); });\n        methods.length = 0;\n    };\n    return WebAnimationsPlayer;\n}());\n/**\n * @param {?} element\n * @param {?} prop\n * @return {?}\n */\nfunction _computeStyle(element, prop) {\n    return (/** @type {?} */ (window.getComputedStyle(element)))[prop];\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nvar WebAnimationsDriver = /** @class */ (function () {\n    function WebAnimationsDriver() {\n    }\n    /**\n     * @param {?} prop\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.validateStyleProperty = /**\n     * @param {?} prop\n     * @return {?}\n     */\n    function (prop) { return validateStyleProperty(prop); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.matchesElement = /**\n     * @param {?} element\n     * @param {?} selector\n     * @return {?}\n     */\n    function (element, selector) {\n        return matchesElement(element, selector);\n    };\n    /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.containsElement = /**\n     * @param {?} elm1\n     * @param {?} elm2\n     * @return {?}\n     */\n    function (elm1, elm2) { return containsElement(elm1, elm2); };\n    /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.query = /**\n     * @param {?} element\n     * @param {?} selector\n     * @param {?} multi\n     * @return {?}\n     */\n    function (element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    };\n    /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.computeStyle = /**\n     * @param {?} element\n     * @param {?} prop\n     * @param {?=} defaultValue\n     * @return {?}\n     */\n    function (element, prop, defaultValue) {\n        return /** @type {?} */ ((/** @type {?} */ (window.getComputedStyle(element)))[prop]);\n    };\n    /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    WebAnimationsDriver.prototype.animate = /**\n     * @param {?} element\n     * @param {?} keyframes\n     * @param {?} duration\n     * @param {?} delay\n     * @param {?} easing\n     * @param {?=} previousPlayers\n     * @return {?}\n     */\n    function (element, keyframes, duration, delay, easing, previousPlayers) {\n        if (previousPlayers === void 0) { previousPlayers = []; }\n        var /** @type {?} */ fill = delay == 0 ? 'both' : 'forwards';\n        var /** @type {?} */ playerOptions = { duration: duration, delay: delay, fill: fill };\n        // we check for this to avoid having a null|undefined value be present\n        // for the easing (which results in an error for certain browsers #9752)\n        if (easing) {\n            playerOptions['easing'] = easing;\n        }\n        var /** @type {?} */ previousWebAnimationPlayers = /** @type {?} */ (previousPlayers.filter(function (player) { return player instanceof WebAnimationsPlayer; }));\n        return new WebAnimationsPlayer(element, keyframes, playerOptions, previousWebAnimationPlayers);\n    };\n    return WebAnimationsDriver;\n}());\n/**\n * @return {?}\n */\nfunction supportsWebAnimations() {\n    return typeof Element !== 'undefined' && typeof (/** @type {?} */ (Element)).prototype['animate'] === 'function';\n}\n\nexports.AnimationDriver = AnimationDriver;\nexports.ɵAnimation = Animation;\nexports.ɵAnimationStyleNormalizer = AnimationStyleNormalizer;\nexports.ɵNoopAnimationStyleNormalizer = NoopAnimationStyleNormalizer;\nexports.ɵWebAnimationsStyleNormalizer = WebAnimationsStyleNormalizer;\nexports.ɵNoopAnimationDriver = NoopAnimationDriver;\nexports.ɵAnimationEngine = AnimationEngine;\nexports.ɵWebAnimationsDriver = WebAnimationsDriver;\nexports.ɵsupportsWebAnimations = supportsWebAnimations;\nexports.ɵWebAnimationsPlayer = WebAnimationsPlayer;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n})));\n//# sourceMappingURL=animations-browser.umd.js.map\n"]}