{"version": 3, "file": "timeoutWith.js", "sourceRoot": "", "sources": ["../../../src/add/operator/timeoutWith.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,4BAA4B,4BAA4B,CAAC,CAAA;AAEzD,uBAAU,CAAC,SAAS,CAAC,WAAW,GAAG,yBAAW,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { timeoutWith } from '../../operator/timeoutWith';\n\nObservable.prototype.timeoutWith = timeoutWith;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    timeoutWith: typeof timeoutWith;\n  }\n}"]}