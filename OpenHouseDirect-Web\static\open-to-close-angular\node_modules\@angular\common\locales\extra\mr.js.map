{"version": 3, "file": "mr.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/mr.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACjD;YACE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM;YACvC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO;SACzC;KACF;IACD;QACE,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;QACnD;YACE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM;YACvC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO;SACzC;KACF;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC3D;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['म.रा.', 'दु', 'प', 'स', 'दु', 'सं', 'सा', 'रा'],\n    [\n      'मध्यरात्र', 'मध्यान्ह', 'पहाट', 'सकाळ',\n      'दुपार', 'संध्याकाळ', 'सायंकाळ', 'रात्र'\n    ],\n  ],\n  [\n    ['म.रा.', 'म', 'प', 'स', 'दु', 'सं', 'सा', 'रात्र'],\n    [\n      'मध्यरात्र', 'मध्यान्ह', 'पहाट', 'सकाळ',\n      'दुपार', 'संध्याकाळ', 'सायंकाळ', 'रात्र'\n    ],\n  ],\n  [\n    '00:00', '12:00', ['04:00', '06:00'], ['06:00', '12:00'], ['12:00', '16:00'],\n    ['16:00', '18:00'], ['18:00', '21:00'], ['21:00', '04:00']\n  ]\n];\n"]}