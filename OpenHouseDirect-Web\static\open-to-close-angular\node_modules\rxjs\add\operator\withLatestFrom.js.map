{"version": 3, "file": "withLatestFrom.js", "sourceRoot": "", "sources": ["../../../src/add/operator/withLatestFrom.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,+BAA+B,+BAA+B,CAAC,CAAA;AAE/D,uBAAU,CAAC,SAAS,CAAC,cAAc,GAAG,+BAAc,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { withLatestFrom } from '../../operator/withLatestFrom';\n\nObservable.prototype.withLatestFrom = withLatestFrom;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    withLatestFrom: typeof withLatestFrom;\n  }\n}"]}