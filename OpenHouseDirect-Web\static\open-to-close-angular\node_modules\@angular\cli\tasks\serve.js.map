{"version": 3, "file": "serve.js", "sourceRoot": "/users/hansl/sources/hansl/angular-cli/", "sources": ["tasks/serve.ts"], "names": [], "mappings": ";;AAAA,+BAA+B;AAC/B,6BAA6B;AAC7B,mCAAmC;AACnC,2BAA2B;AAC3B,iCAA0B;AAC1B,6CAAoD;AACpD,2DAAwE;AACxE,6DAA8D;AAE9D,6CAA6C;AAC7C,sDAA0D;AAC1D,8CAA+F;AAE/F,MAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACvD,MAAM,IAAI,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACrD,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3B,MAAM,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;AAE5B,8BAA8B,QAAgB,EAAE,SAAiB;IAC/D,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,EAAE,CAAC;IACZ,CAAC;IAED,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACpE,gEAAgE;QAChE,MAAM,CAAC,IAAI,CAAC;IACd,CAAC;IAED,qBAAqB;IACrB,6DAA6D;IAC7D,wCAAwC;IACxC,MAAM,aAAa,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC;SACnC,KAAK,CAAC,GAAG,CAAC;SACV,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;IAC/B,EAAE,CAAC,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxC,aAAa,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;IACD,MAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAE7F,EAAE,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACtC,EAAE,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC;YACxE,0FAA0F;YAC1F,MAAM,CAAC,IAAI,CAAC;QACd,CAAC;QACD,MAAM,CAAC,SAAS,CAAC;IACnB,CAAC;IAED,uCAAuC;IACvC,MAAM,CAAC,GAAG,kBAAkB,GAAG,SAAS,IAAI,EAAE,EAAE,CAAC;AACnD,CAAC;AAED,kBAAe,IAAI,CAAC,MAAM,CAAC;IACzB,GAAG,EAAE,UAAU,gBAAkC,EAAE,aAAkB;QACnE,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAEnB,IAAI,eAAoB,CAAC;QACzB,MAAM,aAAa,GAAG,kBAAS,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC;QACrD,MAAM,SAAS,GAAG,4BAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAEzD,MAAM,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,SAAS,CAAC,MAAM,CAAC;QACnE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,IAAI,WAAW,CAAC,iDAAiD,CAAC,CAAC;QAC3E,CAAC;QACD,EAAE,CAAC,CAAC,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3D,MAAM,IAAI,WAAW,CAAC,0DAA0D,CAAC,CAAC;QACpF,CAAC;QACD,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;YACpC,MAAM,IAAI,WAAW,CAAC,2DAA2D,CAAC,CAAC;QACrF,CAAC;QACD,EAAE,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACtC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,aAAa,GAAG;YACpB,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,EAAE;YACpC,QAAQ,EAAE,SAAS,CAAC,QAAQ;SAC7B,CAAC;QAEF,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAEtE,IAAI,aAAa,GAAG,IAAI,mCAAkB,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAEtF,MAAM,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC;YAC/B,QAAQ,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;YACjD,QAAQ,EAAE,gBAAgB,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACnF,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE;SACvC,CAAC,CAAC;QAEH,EAAE,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACtC,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;YACd,MAAM,CAAC,SAAS,CAAC;;;SAGpB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,aAAa,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,cAAc,CAAC;QAC7E,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;YAChC,IAAI,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;YAC7C,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAClC,UAAU,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM,UAAU,EAAE,CAAC;YAC5E,CAAC;YACD,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACxC,gBAAgB,CAAC,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC;YAC7C,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;QAED,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;YAChC,qEAAqE;YACrE,oEAAoE;YACpE,IAAI,oBAAoB,CAAC;YACzB,IAAI,CAAC;gBACH,oBAAoB,GAAG,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YACtE,CAAC;YAAC,KAAK,CAAC,CAAC,IAAD,CAAC;gBACP,MAAM,IAAI,WAAW,CAAC,sDAAsD,CAAC,CAAC;YAChF,CAAC;YACD,IAAI,WAAW,GAAG;gBAChB,GAAG,oBAAoB,IAAI,aAAa,EAAE;aAC3C,CAAC;YACF,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;gBACzB,MAAM,cAAc,GAAG,sDAAsD,CAAC;gBAE9E,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;YAChB,MAAM,CAAC,QAAQ,CAAC;SACnB,CAAC,CAAC;gBAEH,MAAM,WAAW,GAAG,kBAAS,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBACvE,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;oBAChB,EAAE,CAAC,SAAS,CAAC,2DAA2D,CAAC,CAAC;oBAC1E,EAAE,CAAC,SAAS,CAAC,wEAAwE,CAAC,CAAC;oBACvF,EAAE,CAAC,SAAS,CAAC,wDAAwD,CAAC,CAAC;oBACvE,EAAE,CAAC,SAAS,CAAC,SAAS,eAAK,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;oBACpD,EAAE,CAAC,SAAS,CAAC,oDAAoD,CAAC,CAAC;oBACnE,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;cAChB,MAAM,CAAC,gEAAgE,CAAC;WAC3E,CAAC,CAAC;gBACL,CAAC;gBACD,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC3C,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;gBACrE,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;oBAChC,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;cAChB,MAAM,CAAC,QAAQ,CAAC;;WAEnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;gBAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;YAAC,CAAC;YACjE,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,CAAC;QACnD,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YAChC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,8CAA8C,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5B,yEAAyE;YACzE,4CAA4C;YAC5C,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC5B,KAAK,EAAE,CAAC,QAAa,EAAE,EAAE;oBACvB,QAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,GAAG,EAAE;wBACxC,QAAQ,CAAC,eAAe,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;oBAClD,CAAC,CAAC,CAAC;gBACL,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;QAEzC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YAClB,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,WAAW,GAAG,6BAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEpE,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,EAAE,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAChF,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC7B,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YACnC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,MAAM,OAAO,GAAG,oBAAoB,GAAG,SAAS,GAAG,kBAAkB,CAAC;gBACtE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,MAAM,GAAW,IAAI,CAAC;QAC1B,IAAI,OAAO,GAAW,IAAI,CAAC;QAC3B,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzE,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7C,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAC3E,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5B,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,IAAI,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,SAAS,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,MAAM,gBAAgB,GACpB,oBAAoB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC9E,MAAM,WAAW,GAAG,kBAAS,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YAC7E,EAAE,CAAC,CAAC,gBAAgB,IAAI,IAAI,IAAI,WAAW,CAAC,CAAC,CAAC;gBAC5C,EAAE,CAAC,SAAS,CAAC,qBAAO,CAAA;cACd,eAAK,CAAC,MAAM,CAAC,SAAS,CAAC;;;WAG1B,CAAC,CAAC;YACP,CAAC;YACD,SAAS,GAAG,gBAAgB,IAAI,EAAE,CAAC;QACrC,CAAC;QACD,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC5B,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/B,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QAC9B,CAAC;QACD,MAAM,6BAA6B,GAA0C;YAC3E,OAAO,EAAE,EAAE,6BAA6B,EAAE,GAAG,EAAE;YAC/C,kBAAkB,EAAE;gBAClB,KAAK,EAAE,GAAG,SAAS,IAAI,SAAS,CAAC,KAAK,EAAE;gBACxC,cAAc,EAAE,IAAI;gBACpB,iBAAiB,EAAE,CAAC,WAAW,EAAE,uBAAuB,CAAC;aAC1D;YACD,KAAK,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;YACtD,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,WAAW;YAClB,QAAQ,EAAE,gBAAgB,CAAC,MAAM,KAAK,YAAY;YAClD,YAAY,EAAE;gBACZ,IAAI,EAAE,gBAAgB,CAAC,IAAI;aAC5B;YACD,KAAK,EAAE,gBAAgB,CAAC,GAAG;YAC3B,OAAO,EAAE;gBACP,MAAM,EAAE,gBAAgB,CAAC,MAAM,KAAK,aAAa;gBACjD,QAAQ,EAAE,KAAK;aAChB;YACD,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,gBAAgB,CAAC,UAAU;YACnC,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;YACnD,UAAU,EAAE,SAAS;SACtB,CAAC;QAEF,EAAE,CAAC,CAAC,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC;YACtC,6BAA6B,CAAC,GAAG,GAAG,MAAM,CAAC;YAC3C,6BAA6B,CAAC,IAAI,GAAG,OAAO,CAAC;QAC/C,CAAC;QAED,6BAA6B,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC;QAEzD,EAAE,CAAC,CAAC,gBAAgB,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC,CAAC;YAC7C,EAAE,CAAC,SAAS,CAAC,eAAK,CAAC,GAAG,CAAC,0BAAY,CAAA;;;;;;;OAOlC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,EAAE,CAAC,SAAS,CAAC,eAAK,CAAC,KAAK,CAAC,qBAAO,CAAA;;mDAEe,gBAAgB,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI;6BACpE,aAAa,GAAG,SAAS;;KAEjD,CAAC,CAAC,CAAC;QAEJ,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,eAAe,EAAE,6BAA6B,CAAC,CAAC;QACpF,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;YAC9B,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,KAAU,EAAE,EAAE;gBAC5C,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBACvC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,qBAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;gBACpD,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;oBACxB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,6BAAqB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;gBAC9D,CAAC;gBACD,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;oBACtB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,2BAAmB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;YACtC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAC9B,gBAAgB,CAAC,IAAI,EACrB,gBAAgB,CAAC,IAAI,EACrB,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBACxB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACR,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;gBACD,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC1B,GAAG,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC,CAAC,CAAC;YACL,8EAA8E;YAC9E,yFAAyF;YACzF,0FAA0F;YAC1F,uDAAuD;YACvD,qDAAqD;YACrD,8CAA8C;YAC9C,iFAAiF;YACjF,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACxC,UAAU,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC,aAAa;YACpD,CAAC;QACH,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAU,EAAE,EAAE;YACpB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACR,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,wCAAwC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC7F,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC"}