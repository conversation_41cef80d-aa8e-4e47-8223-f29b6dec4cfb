{"version": 3, "file": "cdk-layout.umd.min.js", "sources": ["../../src/cdk/layout/media-matcher.ts", "../../src/cdk/layout/breakpoints-observer.ts", "../../src/cdk/layout/breakpoints.ts", "../../src/cdk/layout/public-api.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Injectable} from '@angular/core';\nimport {Platform} from '@angular/cdk/platform';\n\n/**\n * Global registry for all dynamically-created, injected style tags.\n */\nconst styleElementForWebkitCompatibility: Map<string, HTMLStyleElement> = new Map();\n\n/** A utility for calling matchMedia queries. */\n@Injectable()\nexport class MediaMatcher {\n  /** The internal matchMedia method to return back a MediaQueryList like object. */\n  private _matchMedia: (query: string) => MediaQueryList;\n\n  constructor(private platform: Platform) {\n    this._matchMedia = this.platform.isBrowser && window.matchMedia ?\n      // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n      // call it from a different scope.\n      window.matchMedia.bind(window) :\n      noopMatchMedia;\n  }\n\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query: string): MediaQueryList {\n    if (this.platform.WEBKIT) {\n      createEmptyStyleRule(query);\n    }\n    return this._matchMedia(query);\n  }\n}\n\n/**\n * For Webkit engines that only trigger the MediaQueryListListener when there is at least one CSS\n * selector for the respective media query.\n */\nfunction createEmptyStyleRule(query: string) {\n  if (!styleElementForWebkitCompatibility.has(query)) {\n    try {\n      const style = document.createElement('style');\n\n      style.setAttribute('type', 'text/css');\n      if (!style.sheet) {\n        const cssText = `@media ${query} {.fx-query-test{ }}`;\n        style.appendChild(document.createTextNode(cssText));\n      }\n\n      document.getElementsByTagName('head')[0].appendChild(style);\n\n      // Store in private global registry\n      styleElementForWebkitCompatibility.set(query, style);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n}\n\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query: string): MediaQueryList {\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Injectable, NgZone, OnDestroy} from '@angular/core';\nimport {MediaMatcher} from './media-matcher';\nimport {Observable} from 'rxjs/Observable';\nimport {Subject} from 'rxjs/Subject';\nimport {map} from 'rxjs/operators/map';\nimport {startWith} from 'rxjs/operators/startWith';\nimport {takeUntil} from 'rxjs/operators/takeUntil';\nimport {coerceArray} from '@angular/cdk/coercion';\nimport {combineLatest} from 'rxjs/observable/combineLatest';\nimport {fromEventPattern} from 'rxjs/observable/fromEventPattern';\n\n/** The current state of a layout breakpoint. */\nexport interface BreakpointState {\n  /** Whether the breakpoint is currently matching. */\n  matches: boolean;\n}\n\ninterface Query {\n  observable: Observable<BreakpointState>;\n  mql: MediaQueryList;\n}\n\n/** Utility for checking the matching state of @media queries. */\n@Injectable()\nexport class BreakpointObserver implements OnDestroy {\n  /**  A map of all media queries currently being listened for. */\n  private _queries: Map<string, Query> = new Map();\n  /** A subject for all other observables to takeUntil based on. */\n  private _destroySubject: Subject<{}> = new Subject();\n\n  constructor(private mediaMatcher: MediaMatcher, private zone: NgZone) {}\n\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value: string | string[]): boolean {\n    let queries = coerceArray(value);\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value: string | string[]): Observable<BreakpointState> {\n    let queries = coerceArray(value);\n    let observables = queries.map(query => this._registerQuery(query).observable);\n\n    return combineLatest(observables, (a: BreakpointState, b: BreakpointState) => {\n      return {\n        matches: !!((a && a.matches) || (b && b.matches)),\n      };\n    });\n  }\n\n  /** Registers a specific query to be listened for. */\n  private _registerQuery(query: string): Query {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query)!;\n    }\n\n    let mql: MediaQueryList = this.mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    let queryObservable = fromEventPattern(\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      (listener: MediaQueryListListener) => {\n        mql.addListener((e: MediaQueryList) => this.zone.run(() => listener(e)));\n      },\n      (listener: MediaQueryListListener) => {\n        mql.removeListener((e: MediaQueryList) => this.zone.run(() => listener(e)));\n      })\n      .pipe(\n        takeUntil(this._destroySubject),\n        startWith(mql),\n        map((nextMql: MediaQueryList) => ({matches: nextMql.matches}))\n      );\n\n    // Add the MediaQueryList to the set of queries.\n    let output = {observable: queryObservable, mql: mql};\n    this._queries.set(query, output);\n    return output;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nexport const Breakpoints = {\n  XSmall: '(max-width: 599px)',\n  Small: '(min-width: 600px) and (max-width: 959px)',\n  Medium: '(min-width: 960px) and (max-width: 1279px)',\n  Large: '(min-width: 1280px) and (max-width: 1919px)',\n  XLarge: '(min-width: 1920px)',\n\n  Handset: '(max-width: 599px) and (orientation: portrait), ' +\n           '(max-width: 959px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839px) and (orientation: portrait), ' +\n          '(min-width: 960px) and (max-width: 1279px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' +\n       '(min-width: 1280px) and (orientation: landscape)',\n\n  HandsetPortrait: '(max-width: 599px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n\n  HandsetLandscape: '(max-width: 959px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {NgModule} from '@angular/core';\nimport {PlatformModule} from '@angular/cdk/platform';\nimport {BreakpointObserver} from './breakpoints-observer';\nimport {MediaMatcher} from './media-matcher';\n\n@NgModule({\n  providers: [BreakpointObserver, MediaMatcher],\n  imports: [PlatformModule],\n})\nexport class LayoutModule {}\n\nexport {BreakpointObserver, BreakpointState} from './breakpoints-observer';\nexport {Breakpoints} from './breakpoints';\nexport {MediaMatcher} from './media-matcher';\n"], "names": ["createEmptyStyleRule", "query", "styleElementForWebkitCompatibility", "has", "style", "document", "createElement", "setAttribute", "sheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "getElementsByTagName", "set", "e", "console", "error", "noopMatchMedia", "matches", "media", "addListener", "removeListener", "Map", "MediaMatcher", "platform", "this", "_matchMedia", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "bind", "prototype", "WEBKIT", "type", "Injectable", "Platform", "BreakpointObserver", "mediaMatcher", "zone", "_queries", "_destroySubject", "Subject", "ngOnDestroy", "next", "complete", "isMatched", "value", "_this", "coerce<PERSON><PERSON><PERSON>", "some", "mediaQuery", "_registerQuery", "mql", "observe", "queries", "observables", "map", "observable", "combineLatest", "a", "b", "get", "queryObservable", "fromEventPattern", "listener", "run", "pipe", "takeUntil", "startWith", "nextMql", "output", "NgZone", "Breakpoints", "XSmall", "Small", "Medium", "Large", "<PERSON>L<PERSON>ge", "Handset", "Tablet", "Web", "HandsetPortrait", "TabletPortrait", "WebPortrait", "HandsetLandscape", "TabletLandscape", "WebLandscape", "LayoutModule", "NgModule", "args", "providers", "imports", "PlatformModule"], "mappings": ";;;;;;;m5BA+CA,SAAAA,GAA8BC,GAC5B,IAAKC,EAAmCC,IAAIF,GAC1C,IACE,GAAMG,GAAQC,SAASC,cAAc,QAGrC,IADAF,EAAMG,aAAa,OAAQ,aACtBH,EAAMI,MAAO,CAChB,GAAMC,GAAU,UAAUR,EAAlC,sBACQG,GAAMM,YAAYL,SAASM,eAAeF,IAG5CJ,SAASO,qBAAqB,QAAQ,GAAGF,YAAYN,GAGrDF,EAAmCW,IAAIZ,EAAOG,GAC9C,MAAOU,GACPC,QAAQC,MAAMF,IAMpB,QAAAG,GAAwBhB,GACtB,OACEiB,QAAmB,QAAVjB,GAA6B,KAAVA,EAC5BkB,MAAOlB,EACPmB,YAAa,aACbC,eAAgB,cA7DpB,GAAMnB,GAAoE,GAAIoB,kBAQ5E,QAAFC,GAAsBC,GAAAC,KAAtBD,SAAsBA,EAClBC,KAAKC,YAAcD,KAAKD,SAASG,WAAaC,OAAOC,WAGnDD,OAAOC,WAAWC,KAAKF,QACvBX,EA1BN,MAmCEM,GAAFQ,UAAAF,WAAE,SAAW5B,GAIT,MAHIwB,MAAKD,SAASQ,QAChBhC,EAAqBC,GAEhBwB,KAAKC,YAAYzB,mBAvB5BgC,KAACC,EAAAA,iDARDD,KAAQE,EAAAA,YARRZ,kBCqCE,QAAFa,GAAsBC,EAAoCC,GAApCb,KAAtBY,aAAsBA,EAAoCZ,KAA1Da,KAA0DA,EAJ1Db,KAAAc,SAAyC,GAAIjB,KAE7CG,KAAAe,gBAAyC,GAAIC,GAAAA,QAnC7C,MAwCEL,GAAFL,UAAAW,YAAE,WACEjB,KAAKe,gBAAgBG,OACrBlB,KAAKe,gBAAgBI,YAQvBR,EAAFL,UAAAc,UAAE,SAAUC,GAAV,GAAFC,GAAAtB,IAEI,OADcuB,GAAAA,YAAYF,GACXG,KAAK,SAAAC,GAAc,MAAAH,GAAKI,eAAeD,GAAYE,IAAIlC,WAQxEkB,EAAFL,UAAAsB,QAAE,SAAQP,GAAR,GAAFC,GAAAtB,KACQ6B,EAAUN,EAAAA,YAAYF,GACtBS,EAAcD,EAAQE,IAAI,SAAAvD,GAAS,MAAA8C,GAAKI,eAAelD,GAAOwD,YAElE,OAAOC,GAAAA,cAAcH,EAAa,SAACI,EAAoBC,GACrD,OACE1C,WAAayC,GAAKA,EAAEzC,SAAa0C,GAAKA,EAAE1C,aAMtCkB,EAAVL,UAAAoB,eAAA,SAAyBlD,aAErB,IAAIwB,KAAKc,SAASpC,IAAIF,GACpB,MAAOwB,MAAKc,SAASsB,IAAI5D,EAG3B,IAAImD,GAAsB3B,KAAKY,aAAaR,WAAW5B,GAEnD6D,EAAkBC,EAAAA,iBAMpB,SAACC,GACCZ,EAAIhC,YAAY,SAACN,GAAsB,MAAAiC,GAAKT,KAAK2B,IAAI,WAAM,MAAAD,GAASlD,QAEtE,SAACkD,GACCZ,EAAI/B,eAAe,SAACP,GAAsB,MAAAiC,GAAKT,KAAK2B,IAAI,WAAM,MAAAD,GAASlD,SAExEoD,KACCC,EAAAA,UAAU1C,KAAKe,iBACf4B,EAAAA,UAAUhB,GACVI,EAAAA,IAAI,SAACa,GAA4B,OAAEnD,QAASmD,EAAQnD,YAIpDoD,GAAUb,WAAYK,EAAiBV,IAAKA,EAEhD,OADA3B,MAAKc,SAAS1B,IAAIZ,EAAOqE,GAClBA,kBAvEXrC,KAACC,EAAAA,iDAtBDD,KAAQV,IADRU,KAAoBsC,EAAAA,UAPpBnC,KCSaoC,GACXC,OAAQ,qBACRC,MAAO,4CACPC,OAAQ,6CACRC,MAAO,8CACPC,OAAQ,sBAERC,QAAS,kGAETC,OAAQ,iJAERC,IAAK,mGAGLC,gBAAiB,iDACjBC,eAAgB,wEAChBC,YAAa,iDAEbC,iBAAkB,kDAClBC,gBAAiB,0EACjBC,aAAc,oDCtBhBC,EAAA,yBAPA,sBAYAtD,KAACuD,EAAAA,SAADC,OACEC,WAAYtD,EAAoBb,GAChCoE,SAAUC,EAAAA,0DAdZL"}