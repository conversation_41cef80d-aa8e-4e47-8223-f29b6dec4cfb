{"__symbolic": "module", "version": 4, "exports": [{"export": [{"name": "CdkScrollable", "as": "CdkScrollable"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "ViewportRuler", "as": "ViewportRuler"}, {"name": "VIEWPORT_RULER_PROVIDER", "as": "VIEWPORT_RULER_PROVIDER"}], "from": "@angular/cdk/scrolling"}, {"export": [{"name": "ComponentType", "as": "ComponentType"}], "from": "@angular/cdk/portal"}], "metadata": {"OverlayConfig": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "OverlayConfig"}]}]}}, "HorizontalConnectionPos": {"__symbolic": "interface"}, "VerticalConnectionPos": {"__symbolic": "interface"}, "OriginConnectionPosition": {"__symbolic": "interface"}, "OverlayConnectionPosition": {"__symbolic": "interface"}, "ConnectionPositionPair": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "any"}, {"__symbolic": "reference", "name": "number"}, {"__symbolic": "reference", "name": "number"}]}]}}, "ScrollingVisibility": {"__symbolic": "class", "members": {}}, "ConnectedOverlayPositionChange": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 90, "character": 7}}]], "parameters": [{"__symbolic": "reference", "name": "ConnectionPositionPair"}, {"__symbolic": "reference", "name": "ScrollingVisibility"}]}]}}, "ScrollStrategy": {"__symbolic": "interface"}, "ScrollStrategyOptions": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 26, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 34, "character": 5}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 34, "character": 12}]}]], "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 31, "character": 31}, {"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ViewportRuler", "line": 32, "character": 28}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 33, "character": 21}, {"__symbolic": "reference", "name": "any"}]}]}}, "RepositionScrollStrategy": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 34, "character": 31}, {"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ViewportRuler", "line": 35, "character": 28}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 36, "character": 21}, {"__symbolic": "reference", "name": "any"}]}], "attach": [{"__symbolic": "method"}], "enable": [{"__symbolic": "method"}], "disable": [{"__symbolic": "method"}]}}, "CloseScrollStrategy": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 30, "character": 31}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 31, "character": 21}, {"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ViewportRuler", "line": 32, "character": 28}, {"__symbolic": "reference", "name": "any"}]}], "attach": [{"__symbolic": "method"}], "enable": [{"__symbolic": "method"}], "disable": [{"__symbolic": "method"}]}}, "NoopScrollStrategy": {"__symbolic": "class", "members": {"enable": [{"__symbolic": "method"}], "disable": [{"__symbolic": "method"}], "attach": [{"__symbolic": "method"}]}}, "BlockScrollStrategy": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ViewportRuler", "line": 20, "character": 38}, {"__symbolic": "reference", "name": "any"}]}], "attach": [{"__symbolic": "method"}], "enable": [{"__symbolic": "method"}], "disable": [{"__symbolic": "method"}], "_canBeEnabled": [{"__symbolic": "method"}]}}, "OVERLAY_PROVIDERS": [{"__symbolic": "reference", "name": "Overlay"}, {"__symbolic": "reference", "name": "OverlayPositionBuilder"}, {"__symbolic": "reference", "name": "ɵg"}, {"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "VIEWPORT_RULER_PROVIDER", "line": 27, "character": 2}, {"__symbolic": "reference", "name": "ɵb"}, {"__symbolic": "reference", "name": "ɵe"}], "OverlayModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 32, "character": 1}, "arguments": [{"imports": [{"__symbolic": "reference", "module": "@angular/cdk/bidi", "name": "BidiModule", "line": 33, "character": 12}, {"__symbolic": "reference", "module": "@angular/cdk/portal", "name": "PortalModule", "line": 33, "character": 24}, {"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ScrollDispatchModule", "line": 33, "character": 38}], "exports": [{"__symbolic": "reference", "name": "ConnectedOverlayDirective"}, {"__symbolic": "reference", "name": "OverlayOrigin"}, {"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ScrollDispatchModule", "line": 34, "character": 51}], "declarations": [{"__symbolic": "reference", "name": "ConnectedOverlayDirective"}, {"__symbolic": "reference", "name": "OverlayOrigin"}], "providers": [{"__symbolic": "reference", "name": "OVERLAY_PROVIDERS"}, {"__symbolic": "reference", "name": "ScrollStrategyOptions"}]}]}], "members": {}}, "Overlay": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 37, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, null, null, null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 49, "character": 15}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 49, "character": 22}]}]], "parameters": [{"__symbolic": "reference", "name": "ScrollStrategyOptions"}, {"__symbolic": "reference", "name": "OverlayContainer"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ComponentFactoryResolver", "line": 43, "character": 49}, {"__symbolic": "reference", "name": "OverlayPositionBuilder"}, {"__symbolic": "reference", "name": "OverlayKeyboardDispatcher"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ApplicationRef", "line": 46, "character": 31}, {"__symbolic": "reference", "module": "@angular/core", "name": "Injector", "line": 47, "character": 33}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 48, "character": 31}, {"__symbolic": "reference", "name": "any"}]}], "create": [{"__symbolic": "method"}], "position": [{"__symbolic": "method"}], "_createPaneElement": [{"__symbolic": "method"}], "_createPortalOutlet": [{"__symbolic": "method"}]}}, "ɵa": {"__symbolic": "function", "parameters": ["parentContainer", "_document"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "parentContainer"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "OverlayContainer"}, "arguments": [{"__symbolic": "reference", "name": "_document"}]}}}, "ɵb": {"provide": {"__symbolic": "reference", "name": "OverlayContainer"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 60, "character": 9}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 60, "character": 25}}, {"__symbolic": "reference", "name": "OverlayContainer"}], {"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 61, "character": 4}], "useFactory": {"__symbolic": "reference", "name": "ɵa"}}, "OverlayContainer": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 13, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 17, "character": 15}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 17, "character": 22}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "getContainerElement": [{"__symbolic": "method"}], "_createContainer": [{"__symbolic": "method"}]}}, "ɵc": {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "InjectionToken", "line": 57, "character": 8}, "arguments": ["cdk-connected-overlay-scroll-strategy"]}, "ɵd": {"__symbolic": "function", "parameters": ["overlay"], "value": {"__symbolic": "error", "message": "Lambda not supported", "line": 62, "character": 9, "module": "./overlay-directives"}}, "ɵe": {"provide": {"__symbolic": "reference", "name": "ɵc"}, "deps": [{"__symbolic": "reference", "name": "Overlay"}], "useFactory": {"__symbolic": "reference", "name": "ɵd"}}, "CdkOverlayOrigin": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 77, "character": 1}, "arguments": [{"selector": "[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]", "exportAs": "cdkOverlayOrigin"}]}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 84, "character": 25}]}]}}, "CdkConnectedOverlay": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Directive", "line": 91, "character": 1}, "arguments": [{"selector": "[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]", "exportAs": "cdkConnectedOverlay"}]}], "members": {"origin": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 105, "character": 3}, "arguments": ["cdkConnectedOverlayOrigin"]}]}], "positions": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 108, "character": 3}, "arguments": ["cdkConnectedOverlayPositions"]}]}], "offsetX": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 111, "character": 3}, "arguments": ["cdkConnectedOverlayOffsetX"]}]}], "offsetY": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 121, "character": 3}, "arguments": ["cdkConnectedOverlayOffsetY"]}]}], "width": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 131, "character": 3}, "arguments": ["cdkConnectedOverlayWidth"]}]}], "height": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 134, "character": 3}, "arguments": ["cdkConnectedOverlayHeight"]}]}], "minWidth": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 137, "character": 3}, "arguments": ["cdkConnectedOverlayMinWidth"]}]}], "minHeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 140, "character": 3}, "arguments": ["cdkConnectedOverlayMinHeight"]}]}], "backdropClass": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 143, "character": 3}, "arguments": ["cdkConnectedOverlayBackdropClass"]}]}], "scrollStrategy": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 146, "character": 3}, "arguments": ["cdkConnectedOverlayScrollStrategy"]}]}], "open": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 150, "character": 3}, "arguments": ["cdkConnectedOverlayOpen"]}]}], "hasBackdrop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 153, "character": 3}, "arguments": ["cdkConnectedOverlayHasBackdrop"]}]}], "_deprecatedOrigin": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 161, "character": 3}, "arguments": ["origin"]}]}], "_deprecatedPositions": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 169, "character": 3}, "arguments": ["positions"]}]}], "_deprecatedOffsetX": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 177, "character": 3}, "arguments": ["offsetX"]}]}], "_deprecatedOffsetY": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 185, "character": 3}, "arguments": ["offsetY"]}]}], "_deprecatedWidth": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 193, "character": 3}, "arguments": ["width"]}]}], "_deprecatedHeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 201, "character": 3}, "arguments": ["height"]}]}], "_deprecatedMinWidth": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 209, "character": 3}, "arguments": ["min<PERSON><PERSON><PERSON>"]}]}], "_deprecatedMinHeight": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 217, "character": 3}, "arguments": ["minHeight"]}]}], "_deprecatedBackdropClass": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 225, "character": 3}, "arguments": ["backdropClass"]}]}], "_deprecatedScrollStrategy": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 233, "character": 3}, "arguments": ["scrollStrategy"]}]}], "_deprecatedOpen": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 243, "character": 3}, "arguments": ["open"]}]}], "_deprecatedHasBackdrop": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Input", "line": 251, "character": 3}, "arguments": ["hasBackdrop"]}]}], "backdropClick": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 256, "character": 3}}]}], "positionChange": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 259, "character": 3}}]}], "attach": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 262, "character": 3}}]}], "detach": [{"__symbolic": "property", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Output", "line": 265, "character": 3}}]}], "__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, null, null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 273, "character": 7}, "arguments": [{"__symbolic": "reference", "name": "ɵc"}]}], [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 274, "character": 7}}]], "parameters": [{"__symbolic": "reference", "name": "Overlay"}, {"__symbolic": "reference", "name": "TemplateRef", "module": "@angular/core", "arguments": [{"__symbolic": "reference", "name": "any"}]}, {"__symbolic": "reference", "module": "@angular/core", "name": "ViewContainerRef", "line": 272, "character": 24}, null, {"__symbolic": "reference", "module": "@angular/cdk/bidi", "name": "Directionality", "line": 274, "character": 32}]}], "ngOnDestroy": [{"__symbolic": "method"}], "ngOnChanges": [{"__symbolic": "method"}], "_createOverlay": [{"__symbolic": "method"}], "_buildConfig": [{"__symbolic": "method"}], "_createPositionStrategy": [{"__symbolic": "method"}], "_attachOverlay": [{"__symbolic": "method"}], "_detachOverlay": [{"__symbolic": "method"}], "_destroyOverlay": [{"__symbolic": "method"}]}}, "FullscreenOverlayContainer": {"__symbolic": "class", "extends": {"__symbolic": "reference", "name": "OverlayContainer"}, "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 18, "character": 1}}], "members": {"_createContainer": [{"__symbolic": "method"}], "_adjustParentForFullscreenChange": [{"__symbolic": "method"}], "_addFullscreenChangeListener": [{"__symbolic": "method"}], "getFullscreenElement": [{"__symbolic": "method"}]}}, "OverlayRef": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/portal", "name": "PortalOutlet", "line": 37, "character": 29}, {"__symbolic": "error", "message": "Could not resolve type", "line": 38, "character": 21, "context": {"typeName": "HTMLElement"}, "module": "./overlay-ref"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 39, "character": 23, "context": {"typeName": "ImmutableObject"}, "module": "./overlay-ref"}, {"__symbolic": "reference", "module": "@angular/core", "name": "NgZone", "line": 40, "character": 23}, {"__symbolic": "reference", "name": "OverlayKeyboardDispatcher"}, {"__symbolic": "error", "message": "Could not resolve type", "line": 42, "character": 25, "context": {"typeName": "Document"}, "module": "./overlay-ref"}]}], "attach": [{"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}, {"__symbolic": "method"}], "detach": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "hasAttached": [{"__symbolic": "method"}], "backdropClick": [{"__symbolic": "method"}], "attachments": [{"__symbolic": "method"}], "detachments": [{"__symbolic": "method"}], "keydownEvents": [{"__symbolic": "method"}], "getConfig": [{"__symbolic": "method"}], "updatePosition": [{"__symbolic": "method"}], "updateSize": [{"__symbolic": "method"}], "setDirection": [{"__symbolic": "method"}], "_updateElementDirection": [{"__symbolic": "method"}], "_updateElementSize": [{"__symbolic": "method"}], "_togglePointerEvents": [{"__symbolic": "method"}], "_attachBackdrop": [{"__symbolic": "method"}], "_updateStackingOrder": [{"__symbolic": "method"}], "detachBackdrop": [{"__symbolic": "method"}]}}, "OverlaySizeConfig": {"__symbolic": "interface"}, "ɵf": {"__symbolic": "function", "parameters": ["dispatcher", "_document"], "value": {"__symbolic": "binop", "operator": "||", "left": {"__symbolic": "reference", "name": "dispatcher"}, "right": {"__symbolic": "new", "expression": {"__symbolic": "reference", "name": "OverlayKeyboardDispatcher"}, "arguments": [{"__symbolic": "reference", "name": "_document"}]}}}, "ɵg": {"provide": {"__symbolic": "reference", "name": "OverlayKeyboardDispatcher"}, "deps": [[{"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Optional", "line": 107, "character": 9}}, {"__symbolic": "new", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "SkipSelf", "line": 107, "character": 25}}, {"__symbolic": "reference", "name": "OverlayKeyboardDispatcher"}], {"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 111, "character": 4}], "useFactory": {"__symbolic": "reference", "name": "ɵf"}}, "OverlayKeyboardDispatcher": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 20, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [[{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 28, "character": 15}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 28, "character": 22}]}]], "parameters": [{"__symbolic": "reference", "name": "any"}]}], "ngOnDestroy": [{"__symbolic": "method"}], "add": [{"__symbolic": "method"}], "remove": [{"__symbolic": "method"}], "_subscribeToKeydownEvents": [{"__symbolic": "method"}], "_unsubscribeFromKeydownEvents": [{"__symbolic": "method"}], "_selectOverlayFromEvent": [{"__symbolic": "method"}]}}, "OverlayPositionBuilder": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 17, "character": 1}}], "members": {"__ctor__": [{"__symbolic": "constructor", "parameterDecorators": [null, [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Inject", "line": 20, "character": 15}, "arguments": [{"__symbolic": "reference", "module": "@angular/common", "name": "DOCUMENT", "line": 20, "character": 22}]}]], "parameters": [{"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ViewportRuler", "line": 19, "character": 38}, {"__symbolic": "reference", "name": "any"}]}], "global": [{"__symbolic": "method"}], "connectedTo": [{"__symbolic": "method"}]}}, "PositionStrategy": {"__symbolic": "interface"}, "GlobalPositionStrategy": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "any"}]}], "attach": [{"__symbolic": "method"}], "top": [{"__symbolic": "method"}], "left": [{"__symbolic": "method"}], "bottom": [{"__symbolic": "method"}], "right": [{"__symbolic": "method"}], "width": [{"__symbolic": "method"}], "height": [{"__symbolic": "method"}], "centerHorizontally": [{"__symbolic": "method"}], "centerVertically": [{"__symbolic": "method"}], "apply": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}]}}, "ConnectedPositionStrategy": {"__symbolic": "class", "members": {"__ctor__": [{"__symbolic": "constructor", "parameters": [{"__symbolic": "reference", "name": "OriginConnectionPosition"}, {"__symbolic": "reference", "name": "OverlayConnectionPosition"}, {"__symbolic": "reference", "module": "@angular/core", "name": "ElementRef", "line": 86, "character": 28}, {"__symbolic": "reference", "module": "@angular/cdk/scrolling", "name": "ViewportRuler", "line": 87, "character": 30}, {"__symbolic": "reference", "name": "any"}]}], "attach": [{"__symbolic": "method"}], "dispose": [{"__symbolic": "method"}], "detach": [{"__symbolic": "method"}], "apply": [{"__symbolic": "method"}], "recalculateLastPosition": [{"__symbolic": "method"}], "withScrollableContainers": [{"__symbolic": "method"}], "withFallbackPosition": [{"__symbolic": "method"}], "withDirection": [{"__symbolic": "method"}], "withOffsetX": [{"__symbolic": "method"}], "withOffsetY": [{"__symbolic": "method"}], "withLockedPosition": [{"__symbolic": "method"}], "withPositions": [{"__symbolic": "method"}], "setOrigin": [{"__symbolic": "method"}], "_getStartX": [{"__symbolic": "method"}], "_getEndX": [{"__symbolic": "method"}], "_getOriginConnectionPoint": [{"__symbolic": "method"}], "_getOverlayPoint": [{"__symbolic": "method"}], "_getScrollVisibility": [{"__symbolic": "method"}], "_setElementPosition": [{"__symbolic": "method"}], "_subtractOverflows": [{"__symbolic": "method"}]}}, "ConnectedOverlayDirective": {"__symbolic": "reference", "name": "CdkConnectedOverlay"}, "OverlayOrigin": {"__symbolic": "reference", "name": "CdkOverlayOrigin"}}, "origins": {"OverlayConfig": "./overlay-config", "HorizontalConnectionPos": "./position/connected-position", "VerticalConnectionPos": "./position/connected-position", "OriginConnectionPosition": "./position/connected-position", "OverlayConnectionPosition": "./position/connected-position", "ConnectionPositionPair": "./position/connected-position", "ScrollingVisibility": "./position/connected-position", "ConnectedOverlayPositionChange": "./position/connected-position", "ScrollStrategy": "./scroll/scroll-strategy", "ScrollStrategyOptions": "./scroll/scroll-strategy-options", "RepositionScrollStrategy": "./scroll/reposition-scroll-strategy", "CloseScrollStrategy": "./scroll/close-scroll-strategy", "NoopScrollStrategy": "./scroll/noop-scroll-strategy", "BlockScrollStrategy": "./scroll/block-scroll-strategy", "OVERLAY_PROVIDERS": "./overlay-module", "OverlayModule": "./overlay-module", "Overlay": "./overlay", "ɵa": "./overlay-container", "ɵb": "./overlay-container", "OverlayContainer": "./overlay-container", "ɵc": "./overlay-directives", "ɵd": "./overlay-directives", "ɵe": "./overlay-directives", "CdkOverlayOrigin": "./overlay-directives", "CdkConnectedOverlay": "./overlay-directives", "FullscreenOverlayContainer": "./fullscreen-overlay-container", "OverlayRef": "./overlay-ref", "OverlaySizeConfig": "./overlay-ref", "ɵf": "./keyboard/overlay-keyboard-dispatcher", "ɵg": "./keyboard/overlay-keyboard-dispatcher", "OverlayKeyboardDispatcher": "./keyboard/overlay-keyboard-dispatcher", "OverlayPositionBuilder": "./position/overlay-position-builder", "PositionStrategy": "./position/position-strategy", "GlobalPositionStrategy": "./position/global-position-strategy", "ConnectedPositionStrategy": "./position/connected-position-strategy", "ConnectedOverlayDirective": "./overlay-directives", "OverlayOrigin": "./overlay-directives"}, "importAs": "@angular/cdk/overlay"}