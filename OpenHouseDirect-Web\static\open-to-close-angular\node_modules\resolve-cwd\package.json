{"_from": "resolve-cwd@^2.0.0", "_id": "resolve-cwd@2.0.0", "_inBundle": false, "_integrity": "sha512-ccu8zQTrzVr954472aUVPLEcB3YpKSYR3cg/3lo1okzobPBM+1INXBbBZlDbnI/hbEocnf8j0QVo43hQKrbchg==", "_location": "/resolve-cwd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resolve-cwd@^2.0.0", "name": "resolve-cwd", "escapedName": "resolve-cwd", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/import-local"], "_resolved": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "_shasum": "00a9f7387556e27038eae232caa372a6a59b665a", "_spec": "resolve-cwd@^2.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\import-local", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/resolve-cwd/issues"}, "bundleDependencies": false, "dependencies": {"resolve-from": "^3.0.0"}, "deprecated": false, "description": "Resolve the path of a module like `require.resolve()` but from the current working directory", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/resolve-cwd#readme", "keywords": ["require", "resolve", "path", "module", "from", "like", "cwd", "current", "working", "directory", "import"], "license": "MIT", "name": "resolve-cwd", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-cwd.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0"}