{"_from": "null-check@^1.0.0", "_id": "null-check@1.0.0", "_inBundle": false, "_integrity": "sha512-j8ZNHg19TyIQOWCGeeQJBuu6xZYIEurf8M1Qsfd8mFrGEfIZytbw18YjKWg+LcO25NowXGZXZpKAx+Ui3TFfDw==", "_location": "/null-check", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "null-check@^1.0.0", "name": "null-check", "escapedName": "null-check", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/fs-access"], "_resolved": "https://registry.npmjs.org/null-check/-/null-check-1.0.0.tgz", "_shasum": "977dffd7176012b9ec30d2a39db5cf72a0439edd", "_spec": "null-check@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\fs-access", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/null-check/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Ensure a path doesn't contain null bytes", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/null-check#readme", "keywords": ["built-in", "core", "ponyfill", "polyfill", "shim", "fs", "path", "null", "bytes", "check"], "license": "MIT", "name": "null-check", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/null-check.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}