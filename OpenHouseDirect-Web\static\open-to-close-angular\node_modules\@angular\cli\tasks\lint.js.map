{"version": 3, "file": "lint.js", "sourceRoot": "/users/hansl/sources/hansl/angular-cli/", "sources": ["tasks/lint.ts"], "names": [], "mappings": ";;AAAA,oDAAoD;AACpD,wBAAwB;AACxB,iCAA0B;AAC1B,yBAAyB;AACzB,6BAA6B;AAC7B,yCAAsC;AACtC,6BAA6B;AAC7B,mCAAmC;AAInC,gFAA2E;AAC3E,sDAAkD;AAElD,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC5C,MAAM,IAAI,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AASrD;IAAA;QAGE,WAAM,GAAK,OAAO,CAAC;QACnB,WAAM,GAAK,KAAK,CAAC;QACjB,cAAS,GAAK,KAAK,CAAC;IAEtB,CAAC;CAAA;AAPD,0CAOC;AAED,kBAAe,IAAI,CAAC,MAAM,CAAC;IACzB,GAAG,EAAE,UAAU,OAAwB;QACrC,OAAO,qBAAQ,IAAI,eAAe,EAAE,EAAK,OAAO,CAAE,CAAC;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACtC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QAE1C,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBACpB,EAAE,CAAC,SAAS,CAAC,eAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAAC;YAChE,CAAC;YACD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,MAAM,aAAa,GAAG,6CAAoB,CAAC,WAAW,EAAE,QAAQ,CAAkB,CAAC;QACnF,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;QACpC,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC;QAElD,MAAM,MAAM,GAAG,WAAW;aACvB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACd,IAAI,OAAmB,CAAC;YACxB,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnB,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjD,CAAC;YAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC7B,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;oBACpB,EAAE,CAAC,SAAS,CAAC,eAAK,CAAC,MAAM,CAAC,wDAAwD,CAAC,CAAC,CAAC;gBACvF,CAAC;YACH,CAAC;YACD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACtD,MAAM,WAAW,GAAG;gBAClB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,SAAS,EAAE,OAAO,CAAC,MAAM;aAC1B,CAAC;YAEF,mEAAmE;YACnE,yEAAyE;YACzE,EAAE,CAAC,CAAC,kBAAS,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC7D,OAAO,GAAG,SAAS,CAAC;YACtB,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAEhD,IAAI,aAAa,CAAC;YAClB,IAAI,UAAU,CAAC;YACf,GAAG,CAAC,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC;gBACzB,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAExD,qDAAqD;gBACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC5C,EAAE,CAAC,CAAC,gBAAgB,KAAK,aAAa,CAAC,CAAC,CAAC;oBACvC,UAAU,GAAG,aAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;oBACxE,aAAa,GAAG,gBAAgB,CAAC;gBACnC,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC5B,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ;iBAC9B,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3D,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEjD,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClB,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC1D,CAAC;YAED,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC;gBACrC,KAAK,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC;YACzC,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,uCAAuC;gBACvC,KAAK,CAAC,UAAU,IAAI,QAAQ,CAAC,MAAM,CAAC;YACtC,CAAC;YAED,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC;gBACvC,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC;YAC7C,CAAC;YAED,MAAM,CAAC,KAAK,CAAC;QACf,CAAC,EAAE;YACD,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,SAAS;YAChB,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,CAAC;SAChB,CAAC,CAAC;QAEL,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9D,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBACf,MAAM,IAAI,WAAW,CAAC,eAAK,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;YAC/E,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;YAElC,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAC/D,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACX,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAED,iEAAiE;QACjE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YAC/C,EAAE,CAAC,SAAS,CAAC,eAAK,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC,CAAC;QACzE,CAAC;QAED,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YAC7C,EAAE,CAAC,SAAS,CAAC,eAAK,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC,CAAC;QACpE,CAAC;QAED,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,KAAK,CAAC,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5E,EAAE,CAAC,SAAS,CAAC,eAAK,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5F,CAAC;CACF,CAAC,CAAC;AAEH,8BAAiC,MAAoB;IACnD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC;AAED,wBACE,OAAmB,EACnB,UAAyB,EACzB,MAA4B;IAE5B,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,IAAI,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACjF,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,IAAI,oBAAoB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAE9E,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;QAClB,MAAM,CAAC,aAAa;aACjB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;aACrD,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACnD,CAAC;IAED,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACb,MAAM,CAAC,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAEhD,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,qBAAS,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEpF,YAAY,GAAG,YAAY;aACxB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,MAAM,CAAC,YAAY,CAAC;AACtB,CAAC;AAED,yBACE,IAAY,EACZ,MAAqB,EACrB,OAAoB;IAEpB,4EAA4E;IAC5E,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACZ,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC;YAC7C,MAAM,OAAO,GAAG,SAAS,IAAI,4CAA4C,MAAM,CAAC,OAAO,IAAI,CAAC;YAC5F,MAAM,IAAI,WAAW,CAAC,eAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,CAAC,SAAS,CAAC;IACnB,CAAC;IAED,sFAAsF;IACtF,IAAI,CAAC;QACH,MAAM,CAAC,oBAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IAClD,CAAC;IAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,MAAM,IAAI,WAAW,CAAC,wBAAwB,IAAI,IAAI,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC"}