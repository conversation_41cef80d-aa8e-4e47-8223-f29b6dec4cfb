{"__symbolic": "module", "version": 4, "metadata": {"coerceBooleanProperty": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "binop", "operator": "!=", "left": {"__symbolic": "reference", "name": "value"}, "right": null}, "right": {"__symbolic": "binop", "operator": "!==", "left": {"__symbolic": "reference", "name": "value"}, "right": "false"}}}, "coerceNumberProperty": {"__symbolic": "function", "parameters": ["value", "fallback<PERSON><PERSON><PERSON>"], "defaults": [null, 0], "value": {"__symbolic": "if", "condition": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "_isNumberValue"}, "arguments": [{"__symbolic": "reference", "name": "value"}]}, "thenExpression": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Number"}, "arguments": [{"__symbolic": "reference", "name": "value"}]}, "elseExpression": {"__symbolic": "reference", "name": "fallback<PERSON><PERSON><PERSON>"}}}, "_isNumberValue": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "binop", "operator": "&&", "left": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "isNaN"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "parseFloat"}, "arguments": [{"__symbolic": "reference", "name": "value"}]}]}}, "right": {"__symbolic": "pre", "operator": "!", "operand": {"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "isNaN"}, "arguments": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "name": "Number"}, "arguments": [{"__symbolic": "reference", "name": "value"}]}]}}}}, "coerceArray": {"__symbolic": "function", "parameters": ["value"], "value": {"__symbolic": "if", "condition": {"__symbolic": "call", "expression": {"__symbolic": "select", "expression": {"__symbolic": "reference", "name": "Array"}, "member": "isArray"}, "arguments": [{"__symbolic": "reference", "name": "value"}]}, "thenExpression": {"__symbolic": "reference", "name": "value"}, "elseExpression": [{"__symbolic": "reference", "name": "value"}]}}}, "origins": {"coerceBooleanProperty": "./boolean-property", "coerceNumberProperty": "./number-property", "_isNumberValue": "./number-property", "coerceArray": "./array"}, "importAs": "@angular/cdk/coercion"}