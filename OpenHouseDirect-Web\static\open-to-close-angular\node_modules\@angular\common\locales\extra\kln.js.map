{"version": 3, "file": "kln.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/kln.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe,EAAE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [];\n"]}