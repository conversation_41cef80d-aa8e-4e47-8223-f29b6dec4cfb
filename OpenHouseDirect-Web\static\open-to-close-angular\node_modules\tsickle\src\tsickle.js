/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function (o) {
    var m = typeof Symbol === "function" && o[Symbol.iterator], i = 0;
    if (m) return m.call(o);
    return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
};
(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define("tsickle/src/tsickle", ["require", "exports", "path", "source-map", "tsickle/src/class_decorator_downlevel_transformer", "tsickle/src/decorator-annotator", "tsickle/src/decorators", "tsickle/src/es5processor", "tsickle/src/fileoverview_comment_transformer", "tsickle/src/jsdoc", "tsickle/src/modules_manifest", "tsickle/src/rewriter", "tsickle/src/source_map_utils", "tsickle/src/transformer_sourcemap", "tsickle/src/transformer_util", "tsickle/src/type-translator", "tsickle/src/typescript", "tsickle/src/util", "tsickle/src/modules_manifest"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var path = require("path");
    var source_map_1 = require("source-map");
    var class_decorator_downlevel_transformer_1 = require("tsickle/src/class_decorator_downlevel_transformer");
    var decorator = require("tsickle/src/decorator-annotator");
    var decorators_1 = require("tsickle/src/decorators");
    var es5processor = require("tsickle/src/es5processor");
    var fileoverview_comment_transformer_1 = require("tsickle/src/fileoverview_comment_transformer");
    var jsdoc = require("tsickle/src/jsdoc");
    var modules_manifest_1 = require("tsickle/src/modules_manifest");
    var rewriter_1 = require("tsickle/src/rewriter");
    var source_map_utils_1 = require("tsickle/src/source_map_utils");
    var transformer_sourcemap_1 = require("tsickle/src/transformer_sourcemap");
    var transformer_util_1 = require("tsickle/src/transformer_util");
    var typeTranslator = require("tsickle/src/type-translator");
    var ts = require("tsickle/src/typescript");
    var util_1 = require("tsickle/src/util");
    var modules_manifest_2 = require("tsickle/src/modules_manifest");
    exports.ModulesManifest = modules_manifest_2.ModulesManifest;
    /**
     * The header to be used in generated externs.  This is not included in the
     * output of annotate() because annotate() works one file at a time, and
     * typically you create one externs file from the entire compilation unit.
     */
    exports.EXTERNS_HEADER = "/**\n * @externs\n * @suppress {duplicate,checkTypes}\n */\n// NOTE: generated by tsickle, do not edit.\n";
    /**
     * Symbols that are already declared as externs in Closure, that should
     * be avoided by tsickle's "declare ..." => externs.js conversion.
     */
    exports.closureExternsBlacklist = [
        'exports',
        'global',
        'module',
        // ErrorConstructor is the interface of the Error object itself.
        // tsickle detects that this is part of the TypeScript standard library
        // and assumes it's part of the Closure standard library, but this
        // assumption is wrong for ErrorConstructor.  To properly handle this
        // we'd somehow need to map methods defined on the ErrorConstructor
        // interface into properties on Closure's Error object, but for now it's
        // simpler to just blacklist it.
        'ErrorConstructor',
        'Symbol',
        'WorkerGlobalScope',
    ];
    function formatDiagnostics(diags) {
        return diags
            .map(function (d) {
            var res = ts.DiagnosticCategory[d.category];
            if (d.file) {
                res += ' at ' + formatLocation(d.file, d.start) + ':';
            }
            res += ' ' + ts.flattenDiagnosticMessageText(d.messageText, '\n');
            return res;
        })
            .join('\n');
    }
    exports.formatDiagnostics = formatDiagnostics;
    /** Returns a fileName:line:column string for the given position in the file. */
    function formatLocation(sf, start) {
        var res = sf.fileName;
        if (start !== undefined) {
            var _a = sf.getLineAndCharacterOfPosition(start), line = _a.line, character = _a.character;
            res += ':' + (line + 1) + ':' + (character + 1);
        }
        return res;
    }
    exports.formatLocation = formatLocation;
    /** @return true if node has the specified modifier flag set. */
    function isAmbient(node) {
        var current = node;
        while (current) {
            if (util_1.hasModifierFlag(current, ts.ModifierFlags.Ambient))
                return true;
            current = current.parent;
        }
        return false;
    }
    /**
     * TypeScript allows you to write identifiers quoted, like:
     *   interface Foo {
     *     'bar': string;
     *     'complex name': string;
     *   }
     *   Foo.bar;  // ok
     *   Foo['bar']  // ok
     *   Foo['complex name']  // ok
     *
     * In Closure-land, we want identify that the legal name 'bar' can become an
     * ordinary field, but we need to skip strings like 'complex name'.
     */
    function isValidClosurePropertyName(name) {
        // In local experimentation, it appears that reserved words like 'var' and
        // 'if' are legal JS and still accepted by Closure.
        return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
    }
    /** Returns the Closure name of a function parameter, special-casing destructuring. */
    function getParameterName(param, index) {
        switch (param.name.kind) {
            case ts.SyntaxKind.Identifier:
                var name_1 = rewriter_1.getIdentifierText(param.name);
                // TypeScript allows parameters named "arguments", but Closure
                // disallows this, even in externs.
                if (name_1 === 'arguments')
                    name_1 = 'tsickle_arguments';
                return name_1;
            case ts.SyntaxKind.ArrayBindingPattern:
            case ts.SyntaxKind.ObjectBindingPattern:
                // Closure crashes if you put a binding pattern in the externs.
                // Avoid this by just generating an unused name; the name is
                // ignored anyway.
                return "__" + index;
            default:
                // The above list of kinds is exhaustive.  param.name is 'never' at this point.
                var paramName = param.name;
                throw new Error("unhandled function parameter kind: " + ts.SyntaxKind[paramName.kind]);
        }
    }
    /** Flags that declare a field of the same name if set on a ctor parameter. */
    var FIELD_DECLARATION_MODIFIERS = ts.ModifierFlags.Private |
        ts.ModifierFlags.Protected | ts.ModifierFlags.Public | ts.ModifierFlags.Readonly;
    /**
     * A Rewriter subclass that adds Tsickle-specific (Closure translation) functionality.
     *
     * One Rewriter subclass manages .ts => .ts+Closure translation.
     * Another Rewriter subclass manages .ts => externs translation.
     */
    var ClosureRewriter = /** @class */ (function (_super) {
        __extends(ClosureRewriter, _super);
        function ClosureRewriter(typeChecker, file, host, sourceMapper) {
            var _this = _super.call(this, file, sourceMapper) || this;
            _this.typeChecker = typeChecker;
            _this.host = host;
            /**
             * A mapping of aliases for symbols in the current file, used when emitting types.
             * TypeScript emits imported symbols with unpredictable prefixes. To generate correct type
             * annotations, tsickle creates its own aliases for types, and registers them in this map (see
             * `emitImportDeclaration` and `forwardDeclare()` below). The aliases are then used when emitting
             * types.
             */
            _this.symbolsToAliasedNames = new Map();
            return _this;
        }
        /** Finds an exported (i.e. not global) declaration for the given symbol. */
        ClosureRewriter.prototype.findExportedDeclaration = function (sym) {
            var _this = this;
            // TODO(martinprobst): it's unclear when a symbol wouldn't have a declaration, maybe just for
            // some builtins (e.g. Symbol)?
            if (!sym.declarations || sym.declarations.length === 0)
                return undefined;
            // A symbol declared in this file does not need to be imported.
            if (sym.declarations.some(function (d) { return d.getSourceFile() === _this.file; }))
                return undefined;
            // Find an exported declaration.
            // Because tsickle runs with the --declaration flag, all types referenced from exported types
            // must be exported, too, so there must either be some declaration that is exported, or the
            // symbol is actually a global declaration (declared in a script file, not a module).
            var decl = sym.declarations.find(function (d) {
                // Check for Export | Default (default being a default export).
                if (!util_1.hasModifierFlag(d, ts.ModifierFlags.ExportDefault))
                    return false;
                // Exclude symbols declared in `declare global {...}` blocks, they are global and don't need
                // imports.
                var current = d;
                while (current) {
                    if (current.flags & ts.NodeFlags.GlobalAugmentation)
                        return false;
                    current = current.parent;
                }
                return true;
            });
            return decl;
        };
        /**
         * Get the ts.Symbol at a location or throw.
         * The TypeScript API can return undefined when fetching a symbol, but
         * in many contexts we know it won't (e.g. our input is already type-checked).
         */
        ClosureRewriter.prototype.mustGetSymbolAtLocation = function (node) {
            var sym = this.typeChecker.getSymbolAtLocation(node);
            if (!sym)
                throw new Error('no symbol');
            return sym;
        };
        /**
         * Handles emittng the jsdoc for methods, including overloads.
         * If overloaded, merges the signatures in the list of SignatureDeclarations into a single jsdoc.
         * - Total number of parameters will be the maximum count found across all variants.
         * - Different names at the same parameter index will be joined with "_or_"
         * - Variable args (...type[] in TypeScript) will be output as "...type",
         *    except if found at the same index as another argument.
         * @param  fnDecls Pass > 1 declaration for overloads of same name
         * @return The list of parameter names that should be used to emit the actual
         *    function statement; for overloads, name will have been merged.
         */
        ClosureRewriter.prototype.emitFunctionType = function (fnDecls, extraTags) {
            if (extraTags === void 0) { extraTags = []; }
            var typeChecker = this.typeChecker;
            var newDoc = extraTags;
            var lens = fnDecls.map(function (fnDecl) { return fnDecl.parameters.length; });
            var minArgsCount = Math.min.apply(Math, __spread(lens));
            var maxArgsCount = Math.max.apply(Math, __spread(lens));
            var isConstructor = fnDecls.find(function (d) { return d.kind === ts.SyntaxKind.Constructor; }) !== undefined;
            // For each parameter index i, paramTags[i] is an array of parameters
            // that can be found at index i.  E.g.
            //    function foo(x: string)
            //    function foo(y: number, z: string)
            // then paramTags[0] = [info about x, info about y].
            var paramTags = [];
            var returnTags = [];
            var typeParameterNames = new Set();
            try {
                for (var fnDecls_1 = __values(fnDecls), fnDecls_1_1 = fnDecls_1.next(); !fnDecls_1_1.done; fnDecls_1_1 = fnDecls_1.next()) {
                    var fnDecl = fnDecls_1_1.value;
                    // Construct the JSDoc comment by reading the existing JSDoc, if
                    // any, and merging it with the known types of the function
                    // parameters and return type.
                    var docTags = this.getJSDoc(fnDecl) || [];
                    try {
                        // Copy all the tags other than @param/@return into the new
                        // JSDoc without any change; @param/@return are handled specially.
                        // TODO: there may be problems if an annotation doesn't apply to all overloads;
                        // is it worth checking for this and erroring?
                        for (var docTags_1 = __values(docTags), docTags_1_1 = docTags_1.next(); !docTags_1_1.done; docTags_1_1 = docTags_1.next()) {
                            var tag = docTags_1_1.value;
                            if (tag.tagName === 'param' || tag.tagName === 'return')
                                continue;
                            newDoc.push(tag);
                        }
                    }
                    catch (e_1_1) { e_1 = { error: e_1_1 }; }
                    finally {
                        try {
                            if (docTags_1_1 && !docTags_1_1.done && (_a = docTags_1.return)) _a.call(docTags_1);
                        }
                        finally { if (e_1) throw e_1.error; }
                    }
                    // Add @abstract on "abstract" declarations.
                    if (util_1.hasModifierFlag(fnDecl, ts.ModifierFlags.Abstract)) {
                        newDoc.push({ tagName: 'abstract' });
                    }
                    // Add any @template tags.
                    // Multiple declarations with the same template variable names should work:
                    // the declarations get turned into union types, and Closure Compiler will need
                    // to find a union where all type arguments are satisfied.
                    if (fnDecl.typeParameters) {
                        try {
                            for (var _b = __values(fnDecl.typeParameters), _c = _b.next(); !_c.done; _c = _b.next()) {
                                var tp = _c.value;
                                typeParameterNames.add(rewriter_1.getIdentifierText(tp.name));
                            }
                        }
                        catch (e_2_1) { e_2 = { error: e_2_1 }; }
                        finally {
                            try {
                                if (_c && !_c.done && (_d = _b.return)) _d.call(_b);
                            }
                            finally { if (e_2) throw e_2.error; }
                        }
                    }
                    // Merge the parameters into a single list of merged names and list of types
                    var sig = typeChecker.getSignatureFromDeclaration(fnDecl);
                    if (!sig)
                        throw new Error("invalid signature " + fnDecl.name);
                    for (var i = 0; i < sig.declaration.parameters.length; i++) {
                        var paramNode = sig.declaration.parameters[i];
                        var name_2 = getParameterName(paramNode, i);
                        var isThisParam = name_2 === 'this';
                        var newTag = {
                            tagName: isThisParam ? 'this' : 'param',
                            optional: paramNode.initializer !== undefined || paramNode.questionToken !== undefined,
                            parameterName: isThisParam ? undefined : name_2,
                        };
                        var type = typeChecker.getTypeAtLocation(paramNode);
                        if (paramNode.dotDotDotToken !== undefined) {
                            newTag.restParam = true;
                            // In TypeScript you write "...x: number[]", but in Closure
                            // you don't write the array: "@param {...number} x".  Unwrap
                            // the Array<> wrapper.
                            var typeRef = type;
                            if (!typeRef.typeArguments)
                                throw new Error('invalid rest param');
                            type = typeRef.typeArguments[0];
                        }
                        newTag.type = this.typeToClosure(fnDecl, type);
                        try {
                            for (var docTags_2 = __values(docTags), docTags_2_1 = docTags_2.next(); !docTags_2_1.done; docTags_2_1 = docTags_2.next()) {
                                var _e = docTags_2_1.value, tagName = _e.tagName, parameterName = _e.parameterName, text = _e.text;
                                if (tagName === 'param' && parameterName === newTag.parameterName) {
                                    newTag.text = text;
                                    break;
                                }
                            }
                        }
                        catch (e_3_1) { e_3 = { error: e_3_1 }; }
                        finally {
                            try {
                                if (docTags_2_1 && !docTags_2_1.done && (_f = docTags_2.return)) _f.call(docTags_2);
                            }
                            finally { if (e_3) throw e_3.error; }
                        }
                        if (!paramTags[i])
                            paramTags.push([]);
                        paramTags[i].push(newTag);
                    }
                    // Return type.
                    if (!isConstructor) {
                        var retType = typeChecker.getReturnTypeOfSignature(sig);
                        var retTypeString = this.typeToClosure(fnDecl, retType);
                        var returnDoc = void 0;
                        try {
                            for (var docTags_3 = __values(docTags), docTags_3_1 = docTags_3.next(); !docTags_3_1.done; docTags_3_1 = docTags_3.next()) {
                                var _g = docTags_3_1.value, tagName = _g.tagName, text = _g.text;
                                if (tagName === 'return') {
                                    returnDoc = text;
                                    break;
                                }
                            }
                        }
                        catch (e_4_1) { e_4 = { error: e_4_1 }; }
                        finally {
                            try {
                                if (docTags_3_1 && !docTags_3_1.done && (_h = docTags_3.return)) _h.call(docTags_3);
                            }
                            finally { if (e_4) throw e_4.error; }
                        }
                        returnTags.push({
                            tagName: 'return',
                            type: retTypeString,
                            text: returnDoc,
                        });
                    }
                }
            }
            catch (e_5_1) { e_5 = { error: e_5_1 }; }
            finally {
                try {
                    if (fnDecls_1_1 && !fnDecls_1_1.done && (_j = fnDecls_1.return)) _j.call(fnDecls_1);
                }
                finally { if (e_5) throw e_5.error; }
            }
            if (typeParameterNames.size > 0) {
                newDoc.push({ tagName: 'template', text: Array.from(typeParameterNames.values()).join(', ') });
            }
            // Merge the JSDoc tags for each overloaded parameter.
            // Ensure each parameter has a unique name; the merging process can otherwise
            // accidentally generate the same parameter name twice.
            var paramNames = new Set();
            var foundOptional = false;
            for (var i = 0; i < maxArgsCount; i++) {
                var paramTag = jsdoc.merge(paramTags[i]);
                if (paramNames.has(paramTag.parameterName)) {
                    paramTag.parameterName += i.toString();
                }
                paramNames.add(paramTag.parameterName);
                // If the tag is optional, mark parameters following optional as optional,
                // even if they are not, since Closure restricts this, see
                // https://github.com/google/closure-compiler/issues/2314
                if (!paramTag.restParam && (paramTag.optional || foundOptional || i >= minArgsCount)) {
                    foundOptional = true;
                    paramTag.optional = true;
                }
                newDoc.push(paramTag);
                if (paramTag.restParam) {
                    // Cannot have any parameters after a rest param.
                    // Just dump the remaining parameters.
                    break;
                }
            }
            // Merge the JSDoc tags for each overloaded return.
            if (!isConstructor) {
                newDoc.push(jsdoc.merge(returnTags));
            }
            this.emit('\n' + jsdoc.toString(newDoc));
            return newDoc.filter(function (t) { return t.tagName === 'param'; }).map(function (t) { return t.parameterName; });
            var e_5, _j, e_1, _a, e_2, _d, e_3, _f, e_4, _h;
        };
        /**
         * Returns null if there is no existing comment.
         */
        ClosureRewriter.prototype.getJSDoc = function (node) {
            var text = node.getFullText();
            var comments = ts.getLeadingCommentRanges(text, 0);
            if (!comments || comments.length === 0)
                return null;
            // We need to search backwards for the first JSDoc comment to avoid ignoring such when another
            // code-level comment is between that comment and the function declaration (see
            // testfiles/doc_params for an example).
            var docRelativePos = 0;
            var parsed = null;
            for (var i = comments.length - 1; i >= 0; i--) {
                var _a = comments[i], pos = _a.pos, end = _a.end;
                // end is relative within node.getFullText(), add getFullStart to obtain coordinates that are
                // comparable to node positions.
                var docRelativeEnd = end + node.getFullStart();
                if (docRelativeEnd <= this.file.getStart() &&
                    this.file.text.substring(docRelativeEnd).startsWith('\n\n')) {
                    // This comment is at the very beginning of the file and there's an empty line between the
                    // comment and this node, it's a "detached comment". That means we should treat it as a
                    // file-level comment, not attached to this code node.
                    return null;
                }
                var comment = text.substring(pos, end);
                parsed = jsdoc.parse(comment);
                if (parsed) {
                    docRelativePos = node.getFullStart() + pos;
                    break;
                }
            }
            if (!parsed)
                return null;
            if (parsed.warnings) {
                var start = docRelativePos;
                this.diagnostics.push({
                    file: this.file,
                    start: start,
                    length: node.getStart() - start,
                    messageText: parsed.warnings.join('\n'),
                    category: ts.DiagnosticCategory.Warning,
                    code: 0,
                });
            }
            return parsed.tags;
        };
        ClosureRewriter.prototype.maybeAddTemplateClause = function (docTags, decl) {
            var _this = this;
            if (!decl.typeParameters)
                return;
            // Closure does not support template constraints (T extends X).
            docTags.push({
                tagName: 'template',
                text: decl.typeParameters
                    .map(function (tp) {
                    if (tp.constraint) {
                        _this.emit('\n// unsupported: template constraints.');
                    }
                    return rewriter_1.getIdentifierText(tp.name);
                })
                    .join(', ')
            });
        };
        ClosureRewriter.prototype.maybeAddHeritageClauses = function (docTags, decl) {
            if (!decl.heritageClauses)
                return;
            try {
                for (var _a = __values(decl.heritageClauses), _b = _a.next(); !_b.done; _b = _a.next()) {
                    var heritage = _b.value;
                    if (!heritage.types)
                        continue;
                    var isClass = decl.kind === ts.SyntaxKind.ClassDeclaration;
                    if (isClass && heritage.token !== ts.SyntaxKind.ImplementsKeyword && !isAmbient(decl)) {
                        // If a class has "extends Foo", that is preserved in the ES6 output
                        // and we don't need to do anything.  But if it has "implements Foo",
                        // that is a TS-specific thing and we need to translate it to the
                        // the Closure "@implements {Foo}".
                        // However for ambient declarations, we only emit externs, and in those we do need to
                        // add "@extends {Foo}" as they use ES5 syntax.
                        continue;
                    }
                    try {
                        for (var _c = __values(heritage.types), _d = _c.next(); !_d.done; _d = _c.next()) {
                            var impl = _d.value;
                            var tagName = decl.kind === ts.SyntaxKind.InterfaceDeclaration ? 'extends' : 'implements';
                            // We can only @implements an interface, not a class.
                            // But it's fine to translate TS "implements Class" into Closure
                            // "@extends {Class}" because this is just a type hint.
                            var typeChecker = this.typeChecker;
                            var sym = this.typeChecker.getSymbolAtLocation(impl.expression);
                            if (!sym) {
                                // It's possible for a class declaration to extend an expression that
                                // does not have have a symbol, for example when a mixin function is
                                // used to build a base class, as in `declare MyClass extends
                                // MyMixin(MyBaseClass)`.
                                //
                                // Handling this correctly is tricky. Closure throws on this
                                // `extends <expression>` syntax (see
                                // https://github.com/google/closure-compiler/issues/2182). We would
                                // probably need to generate an intermediate class declaration and
                                // extend that. For now, just omit the `extends` annotation.
                                this.debugWarn(decl, "could not resolve supertype: " + impl.getText());
                                docTags.push({
                                    tagName: '',
                                    text: 'NOTE: tsickle could not resolve supertype, ' +
                                        'class definition may be incomplete.\n'
                                });
                                continue;
                            }
                            var alias = sym;
                            if (sym.flags & ts.SymbolFlags.TypeAlias) {
                                // It's implementing a type alias.  Follow the type alias back
                                // to the original symbol to check whether it's a type or a value.
                                var type = this.typeChecker.getDeclaredTypeOfSymbol(sym);
                                if (!type.symbol) {
                                    // It's not clear when this can happen, but if it does all we
                                    // do is fail to emit the @implements, which isn't so harmful.
                                    continue;
                                }
                                alias = type.symbol;
                            }
                            if (alias.flags & ts.SymbolFlags.Alias) {
                                alias = typeChecker.getAliasedSymbol(alias);
                            }
                            var typeTranslator_1 = this.newTypeTranslator(impl.expression);
                            if (typeTranslator_1.isBlackListed(alias)) {
                                continue;
                            }
                            if (alias.flags & ts.SymbolFlags.Class) {
                                if (!isClass) {
                                    // Only classes can extend classes in TS. Ignoring the heritage clause should be safe,
                                    // as interfaces are @record anyway, so should prevent property disambiguation.
                                    // Problem: validate that methods are there?
                                    continue;
                                }
                                tagName = 'extends';
                            }
                            else if (alias.flags & ts.SymbolFlags.Value) {
                                // If the symbol was already in the value namespace, then it will
                                // not be a type in the Closure output (because Closure collapses
                                // the type and value namespaces).  Just ignore the implements.
                                continue;
                            }
                            // typeToClosure includes nullability modifiers, so call symbolToString directly here.
                            docTags.push({ tagName: tagName, type: typeTranslator_1.symbolToString(sym, true) });
                        }
                    }
                    catch (e_6_1) { e_6 = { error: e_6_1 }; }
                    finally {
                        try {
                            if (_d && !_d.done && (_e = _c.return)) _e.call(_c);
                        }
                        finally { if (e_6) throw e_6.error; }
                    }
                }
            }
            catch (e_7_1) { e_7 = { error: e_7_1 }; }
            finally {
                try {
                    if (_b && !_b.done && (_f = _a.return)) _f.call(_a);
                }
                finally { if (e_7) throw e_7.error; }
            }
            var e_7, _f, e_6, _e;
        };
        /** Emits a type annotation in JSDoc, or {?} if the type is unavailable. */
        ClosureRewriter.prototype.emitJSDocType = function (node, additionalDocTag, type) {
            this.emit(' /**');
            if (additionalDocTag) {
                this.emit(' ' + additionalDocTag);
            }
            this.emit(" @type {" + this.typeToClosure(node, type) + "} */");
        };
        /**
         * Convert a TypeScript ts.Type into the equivalent Closure type.
         *
         * @param context The ts.Node containing the type reference; used for resolving symbols
         *     in context.
         * @param type The type to translate; if not provided, the Node's type will be used.
         * @param resolveAlias If true, do not emit aliases as their symbol, but rather as the resolved
         *     type underlying the alias. This should be true only when emitting the typedef itself.
         */
        ClosureRewriter.prototype.typeToClosure = function (context, type, resolveAlias) {
            if (this.host.untyped) {
                return '?';
            }
            var typeChecker = this.typeChecker;
            if (!type) {
                type = typeChecker.getTypeAtLocation(context);
            }
            return this.newTypeTranslator(context).translate(type, resolveAlias);
        };
        ClosureRewriter.prototype.newTypeTranslator = function (context) {
            var _this = this;
            var translator = new typeTranslator.TypeTranslator(this.typeChecker, context, this.host.typeBlackListPaths, this.symbolsToAliasedNames, function (sym) { return _this.ensureSymbolDeclared(sym); });
            translator.warn = function (msg) { return _this.debugWarn(context, msg); };
            return translator;
        };
        /**
         * debug logs a debug warning.  These should only be used for cases
         * where tsickle is making a questionable judgement about what to do.
         * By default, tsickle does not report any warnings to the caller,
         * and warnings are hidden behind a debug flag, as warnings are only
         * for tsickle to debug itself.
         */
        ClosureRewriter.prototype.debugWarn = function (node, messageText) {
            if (!this.host.logWarning)
                return;
            // Use a ts.Diagnosic so that the warning includes context and file offets.
            var diagnostic = {
                file: this.file,
                start: node.getStart(),
                length: node.getEnd() - node.getStart(),
                messageText: messageText,
                category: ts.DiagnosticCategory.Warning,
                code: 0,
            };
            this.host.logWarning(diagnostic);
        };
        return ClosureRewriter;
    }(rewriter_1.Rewriter));
    var FILEOVERVIEW_COMMENTS = new Set(['fileoverview', 'externs', 'modName', 'mods', 'pintomodule']);
    /** Annotator translates a .ts to a .ts with Closure annotations. */
    var Annotator = /** @class */ (function (_super) {
        __extends(Annotator, _super);
        function Annotator(typeChecker, file, host, tsHost, tsOpts, sourceMapper) {
            var _this = _super.call(this, typeChecker, file, host, sourceMapper) || this;
            _this.tsHost = tsHost;
            _this.tsOpts = tsOpts;
            /** Exported symbol names that have been generated by expanding an "export * from ...". */
            _this.generatedExports = new Set();
            /** Collection of Identifiers used in an `import {foo}` declaration with their Symbol */
            _this.importedNames = [];
            _this.templateSpanStackCount = 0;
            _this.polymerBehaviorStackCount = 0;
            /**
             * The set of module symbols forward declared in the local namespace (with goog.forwarDeclare).
             *
             * Symbols not imported must be declared, which is done by adding forward declares to
             * `extraImports` below.
             */
            _this.forwardDeclaredModules = new Set();
            _this.extraDeclares = '';
            _this.forwardDeclareCounter = 0;
            return _this;
        }
        Annotator.prototype.annotate = function () {
            this.visit(this.file);
            return this.getOutput(this.extraDeclares);
        };
        Annotator.prototype.ensureSymbolDeclared = function (sym) {
            var decl = this.findExportedDeclaration(sym);
            if (!decl)
                return;
            // Actually import the symbol.
            var sf = decl.getSourceFile();
            var moduleSymbol = this.typeChecker.getSymbolAtLocation(sf);
            if (!moduleSymbol) {
                return; // A source file might not have a symbol if it's not a module (no ES6 im/exports).
            }
            // Already imported?
            if (this.forwardDeclaredModules.has(moduleSymbol))
                return;
            // TODO(martinprobst): this should possibly use fileNameToModuleId.
            var text = this.getForwardDeclareText(sf.fileName, moduleSymbol);
            this.extraDeclares += text;
        };
        Annotator.prototype.getExportDeclarationNames = function (node) {
            var _this = this;
            switch (node.kind) {
                case ts.SyntaxKind.VariableStatement:
                    var varDecl = node;
                    return varDecl.declarationList.declarations.map(function (d) { return _this.getExportDeclarationNames(d)[0]; });
                case ts.SyntaxKind.VariableDeclaration:
                case ts.SyntaxKind.FunctionDeclaration:
                case ts.SyntaxKind.InterfaceDeclaration:
                case ts.SyntaxKind.ClassDeclaration:
                case ts.SyntaxKind.ModuleDeclaration:
                    var decl = node;
                    if (!decl.name || decl.name.kind !== ts.SyntaxKind.Identifier) {
                        break;
                    }
                    return [decl.name];
                case ts.SyntaxKind.TypeAliasDeclaration:
                    var typeAlias = node;
                    return [typeAlias.name];
                default:
                    break;
            }
            this.error(node, "unsupported export declaration " + ts.SyntaxKind[node.kind] + ": " + node.getText());
            return [];
        };
        /**
         * Emits an ES6 export for the ambient declaration behind node, if it is indeed exported.
         */
        Annotator.prototype.maybeEmitAmbientDeclarationExport = function (node) {
            // In TypeScript, `export declare` simply generates no code in the exporting module, but does
            // generate a regular import in the importing module.
            // For Closure Compiler, such declarations must still be exported, so that importing code in
            // other modules can reference them. Because tsickle generates global symbols for such types,
            // the appropriate semantics are referencing the global name.
            if (this.host.untyped || !util_1.hasModifierFlag(node, ts.ModifierFlags.Export)) {
                return;
            }
            var declNames = this.getExportDeclarationNames(node);
            try {
                for (var declNames_1 = __values(declNames), declNames_1_1 = declNames_1.next(); !declNames_1_1.done; declNames_1_1 = declNames_1.next()) {
                    var decl = declNames_1_1.value;
                    var sym = this.mustGetSymbolAtLocation(decl);
                    var isValue = sym.flags & ts.SymbolFlags.Value;
                    var declName = rewriter_1.getIdentifierText(decl);
                    if (node.kind === ts.SyntaxKind.VariableStatement) {
                        // For variables, TypeScript rewrites every reference to the variable name as an
                        // "exports." access, to maintain mutable ES6 exports semantics. Indirecting through the
                        // window object means we reference the correct global symbol. Closure Compiler does
                        // understand that "var foo" in externs corresponds to "window.foo".
                        this.emit("\nexports." + declName + " = window." + declName + ";\n");
                    }
                    else if (!isValue) {
                        // Do not emit re-exports for ModuleDeclarations.
                        // Ambient ModuleDeclarations are always referenced as global symbols, so they don't need to
                        // be exported.
                        if (node.kind === ts.SyntaxKind.ModuleDeclaration)
                            continue;
                        // Non-value objects do not exist at runtime, so we cannot access the symbol (it only
                        // exists in externs). Export them as a typedef, which forwards to the type in externs.
                        this.emit("\n/** @typedef {" + declName + "} */\nexports." + declName + ";\n");
                    }
                    else {
                        this.emit("\nexports." + declName + " = " + declName + ";\n");
                    }
                }
            }
            catch (e_8_1) { e_8 = { error: e_8_1 }; }
            finally {
                try {
                    if (declNames_1_1 && !declNames_1_1.done && (_a = declNames_1.return)) _a.call(declNames_1);
                }
                finally { if (e_8) throw e_8.error; }
            }
            var e_8, _a;
        };
        /**
         * Examines a ts.Node and decides whether to do special processing of it for output.
         *
         * @return True if the ts.Node has been handled, false if we should
         *     emit it as is and visit its children.
         */
        Annotator.prototype.maybeProcess = function (node) {
            var _this = this;
            if (util_1.hasModifierFlag(node, ts.ModifierFlags.Ambient) || util_1.isDtsFileName(this.file.fileName)) {
                // An ambient declaration declares types for TypeScript's benefit, so we want to skip Tsickle
                // conversion of its contents.
                this.writeRange(node, node.getFullStart(), node.getEnd());
                // ... but it might need to be exported for downstream importing code.
                this.maybeEmitAmbientDeclarationExport(node);
                return true;
            }
            if (this.currentDecoratorConverter) {
                this.currentDecoratorConverter.beforeProcessNode(node);
            }
            switch (node.kind) {
                case ts.SyntaxKind.SourceFile:
                    this.handleSourceFile(node);
                    return true;
                case ts.SyntaxKind.ImportDeclaration:
                    var importDecl = node;
                    (_a = this.importedNames).push.apply(_a, __spread(decorator.collectImportedNames(this.typeChecker, importDecl)));
                    // No need to forward declare side effect imports.
                    if (!importDecl.importClause)
                        break;
                    // Introduce a goog.forwardDeclare for the module, so that if TypeScript does not emit the
                    // module because it's only used in type positions, the JSDoc comments still reference a
                    // valid Closure level symbol.
                    var sym = this.typeChecker.getSymbolAtLocation(importDecl.moduleSpecifier);
                    // modules might not have a symbol if they are unused.
                    if (!sym)
                        break;
                    // Write the export declaration here so that forward declares come after it, and
                    // fileoverview comments do not get moved behind statements.
                    this.writeNode(importDecl);
                    this.forwardDeclare(importDecl.moduleSpecifier, /* default import? */ !!importDecl.importClause.name);
                    this.addSourceMapping(node);
                    return true;
                case ts.SyntaxKind.ExportDeclaration:
                    var exportDecl = node;
                    var exportedSymbols = [];
                    if (!exportDecl.exportClause && exportDecl.moduleSpecifier) {
                        // It's an "export * from ..." statement.
                        // Rewrite it to re-export each exported symbol directly.
                        exportedSymbols = this.expandSymbolsFromExportStar(exportDecl);
                        var exportSymbolsToEmit = exportedSymbols.filter(function (s) { return _this.shouldEmitExportSymbol(s.sym); });
                        this.writeLeadingTrivia(exportDecl);
                        // Only emit the export if any non-type symbols are exported; otherwise it is not needed,
                        // as type only exports are elided by TS anyway.
                        if (exportSymbolsToEmit.length) {
                            this.emit('export');
                            this.emit(" {" + exportSymbolsToEmit.map(function (e) { return rewriter_1.unescapeName(e.name); }).join(',') + "}");
                            this.emit(' from ');
                            this.visit(exportDecl.moduleSpecifier);
                            this.emit(';');
                            this.addSourceMapping(exportDecl);
                        }
                    }
                    else {
                        // Write the export declaration here so that forward declares come after it, and
                        // fileoverview comments do not get moved behind statements.
                        this.writeNode(exportDecl);
                        if (exportDecl.exportClause) {
                            exportedSymbols = this.getNamedSymbols(exportDecl.exportClause.elements);
                        }
                    }
                    if (exportDecl.moduleSpecifier) {
                        this.forwardDeclare(exportDecl.moduleSpecifier);
                    }
                    if (exportedSymbols.length) {
                        this.emitTypeDefExports(exportedSymbols);
                    }
                    this.addSourceMapping(node);
                    return true;
                case ts.SyntaxKind.InterfaceDeclaration:
                    this.emitInterface(node);
                    // Emit the TS interface verbatim, with no tsickle processing of properties.
                    this.writeRange(node, node.getFullStart(), node.getEnd());
                    return true;
                case ts.SyntaxKind.VariableDeclaration:
                    var varDecl = node;
                    // Only emit a type annotation when it's a plain variable and
                    // not a binding pattern, as Closure doesn't(?) have a syntax
                    // for annotating binding patterns.  See issue #128.
                    // Don't emit type annotation when the variable statement is a @polymerBehavior,
                    // as otherwise the polymer closure checker will fail.
                    // See b/64389806
                    if (this.polymerBehaviorStackCount === 0 &&
                        varDecl.name.kind === ts.SyntaxKind.Identifier) {
                        this.emitJSDocType(varDecl);
                    }
                    return false;
                case ts.SyntaxKind.ClassDeclaration:
                    var classNode = node;
                    this.visitClassDeclaration(classNode);
                    return true;
                case ts.SyntaxKind.PublicKeyword:
                case ts.SyntaxKind.PrivateKeyword:
                    // The "public"/"private" keywords are encountered in two places:
                    // 1) In class fields (which don't appear in the transformed output).
                    // 2) In "parameter properties", e.g.
                    //      constructor(/** @export */ public foo: string).
                    // In case 2 it's important to not emit that JSDoc in the generated
                    // constructor, as this is illegal for Closure.  It's safe to just
                    // always skip comments preceding the 'public' keyword.
                    // See test_files/parameter_properties.ts.
                    this.writeNode(node, /* skipComments */ true);
                    return true;
                case ts.SyntaxKind.Constructor:
                    var ctor = node;
                    this.emitFunctionType([ctor]);
                    // Write the "constructor(...) {" bit, but iterate through any
                    // parameters if given so that we can examine them more closely.
                    this.writeNodeFrom(ctor, ctor.getStart());
                    return true;
                case ts.SyntaxKind.ArrowFunction:
                    // It's difficult to annotate arrow functions due to a bug in
                    // TypeScript (see tsickle issue 57).  For now, just pass them
                    // through unannotated.
                    return false;
                case ts.SyntaxKind.FunctionDeclaration:
                case ts.SyntaxKind.MethodDeclaration:
                case ts.SyntaxKind.GetAccessor:
                case ts.SyntaxKind.SetAccessor:
                    var fnDecl = node;
                    var tags = decorators_1.hasExportingDecorator(node, this.typeChecker) ? [{ tagName: 'export' }] : [];
                    if (!fnDecl.body) {
                        // Two cases: abstract methods and overloaded methods/functions.
                        // Abstract methods are handled in emitTypeAnnotationsHandler.
                        // Overloads are union-ized into the shared type in emitFunctionType.
                        return false;
                    }
                    this.emitFunctionType([fnDecl], tags);
                    this.newTypeTranslator(fnDecl).blacklistTypeParameters(this.symbolsToAliasedNames, fnDecl.typeParameters);
                    this.writeNodeFrom(fnDecl, fnDecl.getStart());
                    return true;
                case ts.SyntaxKind.TypeAliasDeclaration:
                    this.writeNode(node);
                    this.visitTypeAlias(node);
                    return true;
                case ts.SyntaxKind.EnumDeclaration:
                    this.processEnum(node);
                    return true;
                case ts.SyntaxKind.TemplateSpan:
                    this.templateSpanStackCount++;
                    this.writeNode(node);
                    this.templateSpanStackCount--;
                    return true;
                case ts.SyntaxKind.TypeAssertionExpression:
                case ts.SyntaxKind.AsExpression:
                    // Both of these cases are AssertionExpressions.
                    var typeAssertion = node;
                    if (this.polymerBehaviorStackCount > 0) {
                        // Don't emit type casts for Polymer behaviors that are declared
                        // by calling the Polymer function
                        // as the Polymer closure plugin does not work when emitting them.
                        // See b/64389806.
                        // Note: This only matters in the transformer version of tsickle,
                        // as the non transformer version never emitted type casts due to
                        // https://github.com/Microsoft/TypeScript/issues/9873 (see below).
                        return false;
                    }
                    // When using a type casts in template expressions,
                    // closure requires another pair of parens, otherwise it will
                    // complain with "Misplaced type annotation. Type annotations are not allowed here."
                    if (this.templateSpanStackCount > 0) {
                        this.emit('(');
                    }
                    this.emitJSDocType(typeAssertion);
                    // When TypeScript emits JS, it removes one layer of "redundant"
                    // parens, but we need them for the Closure type assertion.  Work
                    // around this by using two parens.  See test_files/coerce.*.
                    // This is needed in both, the transformer and non transformer version.
                    // TODO: in the non transformer version, the comment is currently dropped
                    //  alltegether from pure assignments due to
                    //  https://github.com/Microsoft/TypeScript/issues/9873.
                    this.emit('((');
                    this.writeNode(node);
                    this.emit('))');
                    if (this.templateSpanStackCount > 0) {
                        this.emit(')');
                    }
                    return true;
                case ts.SyntaxKind.NonNullExpression:
                    var nnexpr = node;
                    var type = this.typeChecker.getTypeAtLocation(nnexpr.expression);
                    if (type.flags & ts.TypeFlags.Union) {
                        var nonNullUnion = type
                            .types.filter(function (t) { return (t.flags & (ts.TypeFlags.Null | ts.TypeFlags.Undefined)) === 0; });
                        var typeCopy = Object.assign({}, type);
                        typeCopy.types = nonNullUnion;
                        type = typeCopy;
                    }
                    // See comment above.
                    if (this.templateSpanStackCount > 0) {
                        this.emit('(');
                    }
                    this.emitJSDocType(nnexpr, undefined, type);
                    // See comment above.
                    this.emit('((');
                    this.writeNode(nnexpr.expression);
                    this.emit('))');
                    if (this.templateSpanStackCount > 0) {
                        this.emit(')');
                    }
                    return true;
                case ts.SyntaxKind.PropertyDeclaration:
                case ts.SyntaxKind.VariableStatement:
                    var docTags = this.getJSDoc(node) || [];
                    if (decorators_1.hasExportingDecorator(node, this.typeChecker)) {
                        docTags.push({ tagName: 'export' });
                    }
                    if (docTags.length > 0 && node.getFirstToken()) {
                        this.emit('\n');
                        this.emit(jsdoc.toString(docTags));
                        var isPolymerBehavior = docTags.some(function (t) { return t.tagName === 'polymerBehavior'; });
                        if (isPolymerBehavior) {
                            this.polymerBehaviorStackCount++;
                        }
                        this.writeNodeFrom(node, node.getStart());
                        if (isPolymerBehavior) {
                            this.polymerBehaviorStackCount--;
                        }
                        return true;
                    }
                    break;
                case ts.SyntaxKind.PropertyAssignment:
                    var pa = node;
                    if (isPolymerBehaviorPropertyInCallExpression(pa)) {
                        this.polymerBehaviorStackCount++;
                        this.writeNodeFrom(node, node.getStart());
                        this.polymerBehaviorStackCount--;
                        return true;
                    }
                    return false;
                case ts.SyntaxKind.ElementAccessExpression:
                    // Warn for quoted accesses to properties that have a symbol declared.
                    // Mixing quoted and non-quoted access to a symbol (x['foo'] and x.foo) risks breaking
                    // Closure Compiler renaming. Quoted access is more cumbersome to write than dotted access
                    // though, so chances are users did intend to avoid renaming. The better fix is to use
                    // `declare interface` though.
                    var eae = node;
                    if (!eae.argumentExpression ||
                        eae.argumentExpression.kind !== ts.SyntaxKind.StringLiteral) {
                        return false;
                    }
                    var quotedPropSym = this.typeChecker.getSymbolAtLocation(eae.argumentExpression);
                    // If it has a symbol, it's actually a regular declared property.
                    if (!quotedPropSym)
                        return false;
                    var declarationHasQuotes = !quotedPropSym.declarations || quotedPropSym.declarations.some(function (d) {
                        var decl = d;
                        if (!decl.name)
                            return false;
                        return decl.name.kind === ts.SyntaxKind.StringLiteral;
                    });
                    // If the property is declared with quotes, it should also be accessed with them.
                    if (declarationHasQuotes)
                        return false;
                    var propName = eae.argumentExpression.text;
                    // Properties containing non-JS identifier names can only be accessed with quotes.
                    if (!isValidClosurePropertyName(propName))
                        return false;
                    var symName = this.typeChecker.symbolToString(quotedPropSym);
                    this.debugWarn(eae, "Declared property " + symName + " accessed with quotes. " +
                        "This can lead to renaming bugs. A better fix is to use 'declare interface' " +
                        "on the declaration.");
                    // Previously, the code below changed the quoted into a non-quoted access.
                    // this.writeNode(eae.expression);
                    // this.emit(`.${propName}`);
                    return false;
                case ts.SyntaxKind.PropertyAccessExpression:
                    if (this.host.disableAutoQuoting) {
                        return false;
                    }
                    // Convert dotted accesses to types that have an index type declared to quoted accesses, to
                    // avoid Closure renaming one access but not the other.
                    // This can happen because TS allows dotted access to string index types.
                    var pae = node;
                    var t = this.typeChecker.getTypeAtLocation(pae.expression);
                    if (!t.getStringIndexType())
                        return false;
                    // Types can have string index signatures and declared properties (of the matching type).
                    // These properties have a symbol, as opposed to pure string index types.
                    var propSym = this.typeChecker.getSymbolAtLocation(pae.name);
                    // The decision to return below is a judgement call. Presumably, in most situations, dotted
                    // access to a property is correct, and should not be turned into quoted access even if
                    // there is a string index on the type. However it is possible to construct programs where
                    // this is incorrect, e.g. where user code assigns into a property through the index access
                    // in another location.
                    if (propSym)
                        return false;
                    this.debugWarn(pae, this.typeChecker.typeToString(t) +
                        " has a string index type but is accessed using dotted access. " +
                        "Quoting the access.");
                    this.writeNode(pae.expression);
                    this.emit('["');
                    this.writeNode(pae.name);
                    this.emit('"]');
                    return true;
                case ts.SyntaxKind.Decorator:
                    if (this.currentDecoratorConverter) {
                        return this.currentDecoratorConverter.maybeProcessDecorator(node);
                    }
                    return false;
                default:
                    break;
            }
            return false;
            var _a;
        };
        Annotator.prototype.shouldEmitExportSymbol = function (sym) {
            if (sym.flags & ts.SymbolFlags.Alias) {
                sym = this.typeChecker.getAliasedSymbol(sym);
            }
            if ((sym.flags & ts.SymbolFlags.Value) === 0) {
                // Note: We create explicit reexports via closure at another place in
                return false;
            }
            if (!this.tsOpts.preserveConstEnums && sym.flags & ts.SymbolFlags.ConstEnum) {
                return false;
            }
            return true;
        };
        Annotator.prototype.handleSourceFile = function (sf) {
            // Emit leading detached comments: comments separated by a \n\n from the document.
            // While handlers below generally emit comments preceding them, not all of them do in all
            // situations (e.g. JSDoc preceding a class).
            // This is symmetric with `getJSDoc` below not returning detached file level comments.
            var comments = ts.getLeadingCommentRanges(sf.text, 0) || [];
            var start = sf.getFullStart();
            for (var i = comments.length - 1; i >= 0; i--) {
                if (sf.text.substring(comments[i].end, comments[i].end + 2) === '\n\n') {
                    this.emit(sf.text.substring(0, comments[i].end + 2));
                    start = comments[i].end + 2;
                    break;
                }
            }
            this.writeNodeFrom(sf, start);
        };
        /**
         * Given a "export * from ..." statement, gathers the symbol names it actually
         * exports to be used in a statement like "export {foo, bar, baz} from ...".
         *
         * This is necessary because TS transpiles "export *" by just doing a runtime loop
         * over the target module's exports, which means Closure won't see the declarations/types
         * that are exported.
         */
        Annotator.prototype.expandSymbolsFromExportStar = function (exportDecl) {
            // You can't have an "export *" without a module specifier.
            var moduleSpecifier = exportDecl.moduleSpecifier;
            // Gather the names of local exports, to avoid reexporting any
            // names that are already locally exported.
            var moduleSymbol = this.typeChecker.getSymbolAtLocation(this.file);
            var moduleExports = moduleSymbol && moduleSymbol.exports || new Map();
            // Expand the export list, then filter it to the symbols we want to reexport.
            var exports = this.typeChecker.getExportsOfModule(this.mustGetSymbolAtLocation(moduleSpecifier));
            var reexports = new Set();
            try {
                for (var exports_1 = __values(exports), exports_1_1 = exports_1.next(); !exports_1_1.done; exports_1_1 = exports_1.next()) {
                    var sym = exports_1_1.value;
                    var name_3 = rewriter_1.unescapeName(sym.name);
                    if (moduleExports instanceof Map) {
                        if (moduleExports.has(name_3)) {
                            // This name is shadowed by a local definition, such as:
                            // - export var foo ...
                            // - export {foo} from ...
                            // - export {bar as foo} from ...
                            continue;
                        }
                    }
                    else {
                        // TODO(#634): check if this is a safe cast.
                        if (moduleExports.has(name_3))
                            continue;
                    }
                    if (this.generatedExports.has(name_3)) {
                        // Already exported via an earlier expansion of an "export * from ...".
                        continue;
                    }
                    this.generatedExports.add(name_3);
                    reexports.add(sym);
                }
            }
            catch (e_9_1) { e_9 = { error: e_9_1 }; }
            finally {
                try {
                    if (exports_1_1 && !exports_1_1.done && (_a = exports_1.return)) _a.call(exports_1);
                }
                finally { if (e_9) throw e_9.error; }
            }
            return Array.from(reexports.keys()).map(function (sym) {
                return { name: sym.name, sym: sym };
            });
            var e_9, _a;
        };
        /**
         * Write an `exports.` assignment for each type alias exported in the given `exports`.
         * TypeScript by itself does not export non-value symbols (e.g. interfaces, typedefs), as it
         * expects to remove those entirely for runtime. For Closure, types must be
         * exported as downstream code will import the type.
         *
         * The tsickle pass turns interfaces into values by generating a `function MyInterface() {}` for
         * them, so in the second conversion pass, TypeScript does export a value for them. However for
         * pure typedefs, tsickle only generates a property access with a JSDoc comment, so they need to
         * be exported explicitly here.
         */
        Annotator.prototype.emitTypeDefExports = function (exports) {
            if (this.host.untyped)
                return;
            try {
                for (var exports_2 = __values(exports), exports_2_1 = exports_2.next(); !exports_2_1.done; exports_2_1 = exports_2.next()) {
                    var exp = exports_2_1.value;
                    if (exp.sym.flags & ts.SymbolFlags.Alias)
                        exp.sym = this.typeChecker.getAliasedSymbol(exp.sym);
                    var isTypeAlias = ((exp.sym.flags & ts.SymbolFlags.TypeAlias) !== 0 &&
                        (exp.sym.flags & ts.SymbolFlags.Value) === 0) ||
                        (exp.sym.flags & ts.SymbolFlags.Interface) !== 0 &&
                            (exp.sym.flags & ts.SymbolFlags.Value) === 0;
                    if (!isTypeAlias)
                        continue;
                    var typeName = this.symbolsToAliasedNames.get(exp.sym) || exp.sym.name;
                    this.emit("/** @typedef {" + typeName + "} */\nexports." + exp.name + "; // re-export typedef\n");
                }
            }
            catch (e_10_1) { e_10 = { error: e_10_1 }; }
            finally {
                try {
                    if (exports_2_1 && !exports_2_1.done && (_a = exports_2.return)) _a.call(exports_2);
                }
                finally { if (e_10) throw e_10.error; }
            }
            var e_10, _a;
        };
        Annotator.prototype.getNamedSymbols = function (specifiers) {
            var _this = this;
            return specifiers.map(function (e) {
                return {
                    // e.name might be renaming symbol as in `export {Foo as Bar}`, where e.name would be 'Bar'
                    // and != sym.name. Store away the name so forwardDeclare below can emit the right name.
                    name: rewriter_1.getIdentifierText(e.name),
                    sym: _this.mustGetSymbolAtLocation(e.name),
                };
            });
        };
        /**
         * Emits a `goog.forwardDeclare` alias for each symbol from the given list.
         * @param specifier the import specifier, i.e. module path ("from '...'").
         */
        Annotator.prototype.forwardDeclare = function (specifier, isDefaultImport) {
            if (isDefaultImport === void 0) { isDefaultImport = false; }
            var importPath = es5processor.resolveIndexShorthand({ options: this.tsOpts, host: this.tsHost }, this.file.fileName, specifier.text);
            var moduleSymbol = this.typeChecker.getSymbolAtLocation(specifier);
            this.emit(this.getForwardDeclareText(importPath, moduleSymbol, isDefaultImport));
        };
        /**
         * Returns the `const x = goog.forwardDeclare...` text for an import of the given `importPath`.
         * This also registers aliases for symbols from the module that map to this forward declare.
         */
        Annotator.prototype.getForwardDeclareText = function (importPath, moduleSymbol, isDefaultImport) {
            if (isDefaultImport === void 0) { isDefaultImport = false; }
            if (this.host.untyped)
                return '';
            var nsImport = es5processor.extractGoogNamespaceImport(importPath);
            var forwardDeclarePrefix = "tsickle_forward_declare_" + ++this.forwardDeclareCounter;
            var moduleNamespace = nsImport !== null ? nsImport : this.host.pathToModuleName(this.file.fileName, importPath);
            // In TypeScript, importing a module for use in a type annotation does not cause a runtime load.
            // In Closure Compiler, goog.require'ing a module causes a runtime load, so emitting requires
            // here would cause a change in load order, which is observable (and can lead to errors).
            // Instead, goog.forwardDeclare types, which allows using them in type annotations without
            // causing a load. See below for the exception to the rule.
            var emitText = "const " + forwardDeclarePrefix + " = goog.forwardDeclare(\"" + moduleNamespace + "\");\n";
            // Scripts do not have a symbol. Scripts can still be imported, either as side effect imports or
            // with an empty import set ("{}"). TypeScript does not emit a runtime load for an import with
            // an empty list of symbols, but the import forces any global declarations from the library to
            // be visible, which is what users use this for. No symbols from the script need forward
            // declaration, so just return.
            if (!moduleSymbol)
                return '';
            this.forwardDeclaredModules.add(moduleSymbol);
            var exports = this.typeChecker.getExportsOfModule(moduleSymbol);
            var hasValues = exports.some(function (e) {
                var isValue = (e.flags & ts.SymbolFlags.Value) !== 0;
                var isConstEnum = (e.flags & ts.SymbolFlags.ConstEnum) !== 0;
                // const enums are inlined by TypeScript (if preserveConstEnums=false), so there is never a
                // value import generated for them. That means for the purpose of force-importing modules,
                // they do not count as values. If preserveConstEnums=true, this shouldn't hurt.
                return isValue && !isConstEnum;
            });
            if (!hasValues) {
                // Closure Compiler's toolchain will drop files that are never goog.require'd *before* type
                // checking (e.g. when using --closure_entry_point or similar tools). This causes errors
                // complaining about values not matching 'NoResolvedType', or modules not having a certain
                // member.
                // To fix, explicitly goog.require() modules that only export types. This should usually not
                // cause breakages due to load order (as no symbols are accessible from the module - though
                // contrived code could observe changes in side effects).
                // This is a heuristic - if the module exports some values, but those are never imported,
                // the file will still end up not being imported. Hopefully modules that export values are
                // imported for their value in some place.
                emitText += "goog.require(\"" + moduleNamespace + "\"); // force type-only module to be loaded\n";
            }
            try {
                for (var exports_3 = __values(exports), exports_3_1 = exports_3.next(); !exports_3_1.done; exports_3_1 = exports_3.next()) {
                    var sym = exports_3_1.value;
                    if (sym.flags & ts.SymbolFlags.Alias) {
                        sym = this.typeChecker.getAliasedSymbol(sym);
                    }
                    // goog: imports don't actually use the .default property that TS thinks they have.
                    var qualifiedName = nsImport && isDefaultImport ? forwardDeclarePrefix :
                        forwardDeclarePrefix + '.' + sym.name;
                    this.symbolsToAliasedNames.set(sym, qualifiedName);
                }
            }
            catch (e_11_1) { e_11 = { error: e_11_1 }; }
            finally {
                try {
                    if (exports_3_1 && !exports_3_1.done && (_a = exports_3.return)) _a.call(exports_3);
                }
                finally { if (e_11) throw e_11.error; }
            }
            return emitText;
            var e_11, _a;
        };
        Annotator.prototype.visitClassDeclaration = function (classDecl) {
            this.addSourceMapping(classDecl);
            var oldDecoratorConverter = this.currentDecoratorConverter;
            this.currentDecoratorConverter =
                new decorator.DecoratorClassVisitor(this.typeChecker, this, classDecl, this.importedNames);
            var docTags = this.getJSDoc(classDecl) || [];
            if (util_1.hasModifierFlag(classDecl, ts.ModifierFlags.Abstract)) {
                docTags.push({ tagName: 'abstract' });
            }
            this.maybeAddTemplateClause(docTags, classDecl);
            if (!this.host.untyped) {
                this.maybeAddHeritageClauses(docTags, classDecl);
            }
            this.emit('\n');
            if (docTags.length > 0)
                this.emit(jsdoc.toString(docTags));
            decorator.visitClassContentIncludingDecorators(classDecl, this, this.currentDecoratorConverter);
            this.emitTypeAnnotationsHelper(classDecl);
            this.currentDecoratorConverter = oldDecoratorConverter;
            return true;
        };
        Annotator.prototype.emitInterface = function (iface) {
            // If this symbol is both a type and a value, we cannot emit both into Closure's
            // single namespace.
            var sym = this.mustGetSymbolAtLocation(iface.name);
            if (sym.flags & ts.SymbolFlags.Value)
                return;
            var docTags = this.getJSDoc(iface) || [];
            docTags.push({ tagName: 'record' });
            this.maybeAddTemplateClause(docTags, iface);
            if (!this.host.untyped) {
                this.maybeAddHeritageClauses(docTags, iface);
            }
            this.emit('\n');
            this.emit(jsdoc.toString(docTags));
            if (util_1.hasModifierFlag(iface, ts.ModifierFlags.Export))
                this.emit('export ');
            var name = rewriter_1.getIdentifierText(iface.name);
            this.emit("function " + name + "() {}\n");
            this.emit("\n\nfunction " + name + "_tsickle_Closure_declarations() {\n");
            var memberNamespace = [name, 'prototype'];
            try {
                for (var _a = __values(iface.members), _b = _a.next(); !_b.done; _b = _a.next()) {
                    var elem = _b.value;
                    var isOptional = elem.questionToken != null;
                    this.visitProperty(memberNamespace, elem, isOptional);
                }
            }
            catch (e_12_1) { e_12 = { error: e_12_1 }; }
            finally {
                try {
                    if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
                }
                finally { if (e_12) throw e_12.error; }
            }
            this.emit("}\n");
            var e_12, _c;
        };
        /**
         * emitTypeAnnotationsHelper produces a _tsickle_typeAnnotationsHelper() where
         * none existed in the original source. It's necessary in the case where
         * TypeScript syntax specifies there are additional properties on the class,
         * because to declare these in Closure you must declare these in a method
         * somewhere.
         */
        Annotator.prototype.emitTypeAnnotationsHelper = function (classDecl) {
            var _this = this;
            // Gather parameter properties from the constructor, if it exists.
            var ctors = [];
            var paramProps = [];
            var nonStaticProps = [];
            var staticProps = [];
            var abstractMethods = [];
            try {
                for (var _a = __values(classDecl.members), _b = _a.next(); !_b.done; _b = _a.next()) {
                    var member = _b.value;
                    if (member.kind === ts.SyntaxKind.Constructor) {
                        ctors.push(member);
                    }
                    else if (member.kind === ts.SyntaxKind.PropertyDeclaration) {
                        var prop = member;
                        var isStatic = util_1.hasModifierFlag(prop, ts.ModifierFlags.Static);
                        if (isStatic) {
                            staticProps.push(prop);
                        }
                        else {
                            nonStaticProps.push(prop);
                        }
                    }
                    else if (util_1.hasModifierFlag(member, ts.ModifierFlags.Abstract) &&
                        (member.kind === ts.SyntaxKind.MethodDeclaration ||
                            member.kind === ts.SyntaxKind.GetAccessor ||
                            member.kind === ts.SyntaxKind.SetAccessor)) {
                        abstractMethods.push(member);
                    }
                }
            }
            catch (e_13_1) { e_13 = { error: e_13_1 }; }
            finally {
                try {
                    if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
                }
                finally { if (e_13) throw e_13.error; }
            }
            if (ctors.length > 0) {
                var ctor = ctors[0];
                paramProps = ctor.parameters.filter(function (p) { return util_1.hasModifierFlag(p, FIELD_DECLARATION_MODIFIERS); });
            }
            if (nonStaticProps.length === 0 && paramProps.length === 0 && staticProps.length === 0 &&
                abstractMethods.length === 0 &&
                !(this.currentDecoratorConverter && this.currentDecoratorConverter.foundDecorators())) {
                // There are no members so we don't need to emit any type
                // annotations helper.
                return;
            }
            if (!classDecl.name)
                return;
            var className = rewriter_1.getIdentifierText(classDecl.name);
            // See test_files/fields/fields.ts:BaseThatThrows for a note on this wrapper.
            this.emit("\n\nfunction " + className + "_tsickle_Closure_declarations() {\n");
            if (this.currentDecoratorConverter) {
                this.currentDecoratorConverter.emitMetadataTypeAnnotationsHelpers();
            }
            staticProps.forEach(function (p) { return _this.visitProperty([className], p); });
            var memberNamespace = [className, 'prototype'];
            nonStaticProps.forEach(function (p) { return _this.visitProperty(memberNamespace, p); });
            paramProps.forEach(function (p) { return _this.visitProperty(memberNamespace, p); });
            try {
                for (var abstractMethods_1 = __values(abstractMethods), abstractMethods_1_1 = abstractMethods_1.next(); !abstractMethods_1_1.done; abstractMethods_1_1 = abstractMethods_1.next()) {
                    var fnDecl = abstractMethods_1_1.value;
                    var name_4 = this.propertyName(fnDecl);
                    if (!name_4) {
                        this.error(fnDecl, 'anonymous abstract function');
                        continue;
                    }
                    var tags = decorators_1.hasExportingDecorator(fnDecl, this.typeChecker) ? [{ tagName: 'export' }] : [];
                    var paramNames = this.emitFunctionType([fnDecl], tags);
                    // memberNamespace because abstract methods cannot be static in TypeScript.
                    this.emit(memberNamespace.join('.') + "." + name_4 + " = function(" + paramNames.join(', ') + ") {};\n");
                }
            }
            catch (e_14_1) { e_14 = { error: e_14_1 }; }
            finally {
                try {
                    if (abstractMethods_1_1 && !abstractMethods_1_1.done && (_d = abstractMethods_1.return)) _d.call(abstractMethods_1);
                }
                finally { if (e_14) throw e_14.error; }
            }
            this.emit("}\n");
            var e_13, _c, e_14, _d;
        };
        Annotator.prototype.propertyName = function (prop) {
            if (!prop.name)
                return null;
            switch (prop.name.kind) {
                case ts.SyntaxKind.Identifier:
                    return rewriter_1.getIdentifierText(prop.name);
                case ts.SyntaxKind.StringLiteral:
                    // E.g. interface Foo { 'bar': number; }
                    // If 'bar' is a name that is not valid in Closure then there's nothing we can do.
                    var text = prop.name.text;
                    if (!isValidClosurePropertyName(text))
                        return null;
                    return text;
                default:
                    return null;
            }
        };
        /**
         * @param optional If true, property is optional (e.g. written "foo?: string").
         */
        Annotator.prototype.visitProperty = function (namespace, prop, optional) {
            if (optional === void 0) { optional = false; }
            var name = this.propertyName(prop);
            if (!name) {
                this.emit("/* TODO: handle strange member:\n" + this.escapeForComment(prop.getText()) + "\n*/\n");
                return;
            }
            var type = this.typeToClosure(prop);
            // When a property is optional, e.g.
            //   foo?: string;
            // Then the TypeScript type of the property is string|undefined, the
            // typeToClosure translation handles it correctly, and string|undefined is
            // how you write an optional property in Closure.
            //
            // But in the special case of an optional property with type any:
            //   foo?: any;
            // The TypeScript type of the property is just "any" (because any includes
            // undefined as well) so our default translation of the type is just "?".
            // To mark the property as optional in Closure it must have "|undefined",
            // so the Closure type must be ?|undefined.
            if (optional && type === '?')
                type += '|undefined';
            var tags = this.getJSDoc(prop) || [];
            tags.push({ tagName: 'type', type: type });
            if (decorators_1.hasExportingDecorator(prop, this.typeChecker)) {
                tags.push({ tagName: 'export' });
            }
            // Avoid printing annotations that can conflict with @type
            // This avoids Closure's error "type annotation incompatible with other annotations"
            this.emit(jsdoc.toString(tags, new Set(['param', 'return'])));
            namespace = namespace.concat([name]);
            this.emit(namespace.join('.') + ";\n");
        };
        Annotator.prototype.visitTypeAlias = function (node) {
            if (this.host.untyped)
                return;
            // If the type is also defined as a value, skip emitting it. Closure collapses type & value
            // namespaces, the two emits would conflict if tsickle emitted both.
            var sym = this.mustGetSymbolAtLocation(node.name);
            if (sym.flags & ts.SymbolFlags.Value)
                return;
            // Write a Closure typedef, which involves an unused "var" declaration.
            // Note: in the case of an export, we cannot emit a literal "var" because
            // TypeScript drops exports that are never assigned to (and Closure
            // requires us to not assign to typedef exports).  Instead, emit the
            // "exports.foo;" line directly in that case.
            this.newTypeTranslator(node).blacklistTypeParameters(this.symbolsToAliasedNames, node.typeParameters);
            var typeStr = this.typeToClosure(node, undefined, true /* resolveAlias */);
            this.emit("\n/** @typedef {" + typeStr + "} */\n");
            if (util_1.hasModifierFlag(node, ts.ModifierFlags.Export)) {
                this.emit('exports.');
            }
            else {
                this.emit('var ');
            }
            this.emit(node.name.getText() + ";\n");
        };
        /**
         * getEnumMemberType computes the type of an enum member by inspecting
         * its initializer expression.
         */
        Annotator.prototype.getEnumMemberType = function (member) {
            // Enum members without initialization have type 'number'
            if (!member.initializer) {
                return 'number';
            }
            var type = this.typeChecker.getTypeAtLocation(member.initializer);
            // Note: checking against 'NumberLike' instead of just 'Number' means this code
            // handles both
            //   MEMBER = 3,  // TypeFlags.NumberLiteral
            // and
            //   MEMBER = someFunction(),  // TypeFlags.Number
            if (type.flags & ts.TypeFlags.NumberLike) {
                return 'number';
            }
            // If the value is not a number, it must be a string.
            // TypeScript does not allow enum members to have any other type.
            return 'string';
        };
        /**
         * getEnumType computes the Closure type of an enum, by iterating through the members
         * and gathering their types.
         */
        Annotator.prototype.getEnumType = function (enumDecl) {
            var hasNumber = false;
            var hasString = false;
            try {
                for (var _a = __values(enumDecl.members), _b = _a.next(); !_b.done; _b = _a.next()) {
                    var member = _b.value;
                    var type = this.getEnumMemberType(member);
                    if (type === 'string') {
                        hasString = true;
                    }
                    else if (type === 'number') {
                        hasNumber = true;
                    }
                }
            }
            catch (e_15_1) { e_15 = { error: e_15_1 }; }
            finally {
                try {
                    if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
                }
                finally { if (e_15) throw e_15.error; }
            }
            if (hasNumber && hasString) {
                return '?'; // Closure's new type inference doesn't support enums of unions.
            }
            else if (hasNumber) {
                return 'number';
            }
            else if (hasString) {
                return 'string';
            }
            else {
                // Perhaps an empty enum?
                return '?';
            }
            var e_15, _c;
        };
        /**
         * Processes an EnumDeclaration into a Closure type. Always emits a Closure type, even in untyped
         * mode, as that should be harmless (it only ever uses the number type).
         */
        Annotator.prototype.processEnum = function (node) {
            // Emit the enum declaration, which looks like:
            //   /** @enum {number} */
            //   const Foo = {BAR: 0, BAZ: 1, ...};
            //   export {Foo};  // even if originally exported on one line.
            // This declares an enum type for Closure Compiler (and Closure JS users of this TS code).
            // Splitting the enum into declaration and export is required so that local references to the
            // type resolve ("@type {Foo}").
            this.emit('\n');
            var name = node.name.getText();
            var enumType = this.getEnumType(node);
            this.emit("/** @enum {" + enumType + "} */\n");
            this.emit("const " + name + ": DontTypeCheckMe = {");
            // Emit enum values ('BAR: 0,').
            var enumIndex = 0;
            try {
                for (var _a = __values(node.members), _b = _a.next(); !_b.done; _b = _a.next()) {
                    var member = _b.value;
                    var memberName = member.name.getText();
                    // Emit any comments and leading whitespace on the enum value definition.
                    this.writeLeadingTrivia(member);
                    this.emit(memberName + ": ");
                    if (member.initializer) {
                        var enumConstValue = this.typeChecker.getConstantValue(member);
                        if (typeof enumConstValue === 'number') {
                            enumIndex = enumConstValue + 1;
                            this.emit(enumConstValue.toString());
                        }
                        else {
                            // Non-numeric enum value (string or an expression).
                            // Emit this initializer expression as-is.
                            // Note: if the member's initializer expression refers to another
                            // value within the enum (e.g. something like
                            //   enum Foo {
                            //     Field1,
                            //     Field2 = Field1 + something(),
                            //   }
                            // Then when we emit the initializer we produce invalid code because
                            // on the Closure side the reference to Field1 has to be namespaced,
                            // e.g. written "Foo.Field1 + something()".
                            // Hopefully this doesn't come up often -- if the enum instead has
                            // something like
                            //     Field2 = Field1 + 3,
                            // then it's still a constant expression and we inline the constant
                            // value in the above branch of this "if" statement.
                            this.visit(member.initializer);
                        }
                    }
                    else {
                        this.emit(enumIndex.toString());
                        enumIndex++;
                    }
                    this.emit(',');
                }
            }
            catch (e_16_1) { e_16 = { error: e_16_1 }; }
            finally {
                try {
                    if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
                }
                finally { if (e_16) throw e_16.error; }
            }
            this.emit('};\n');
            var isExported = util_1.hasModifierFlag(node, ts.ModifierFlags.Export);
            if (isExported)
                this.emit("export {" + name + "};\n");
            if (util_1.hasModifierFlag(node, ts.ModifierFlags.Const)) {
                // By TypeScript semantics, const enums disappear after TS compilation.
                // We still need to generate the runtime value above to make Closure Compiler's type system
                // happy and allow refering to enums from JS code, but we should at least not emit string
                // value mappings.
                return;
            }
            try {
                // Emit the reverse mapping of foo[foo.BAR] = 'BAR'; lines for number enum members
                for (var _d = __values(node.members), _e = _d.next(); !_e.done; _e = _d.next()) {
                    var member = _e.value;
                    var memberName = member.name.getText();
                    var memberType = this.getEnumMemberType(member);
                    if (memberType !== 'number') {
                        continue;
                    }
                    // TypeScript enum members can have Identifier names or String names.
                    // We need to emit slightly different code to support these two syntaxes:
                    if (member.name.kind === ts.SyntaxKind.Identifier) {
                        // Foo[Foo.ABC] = "ABC";
                        this.emit(name + "[" + name + "." + memberName + "] = \"" + memberName + "\";\n");
                    }
                    else {
                        // Foo[Foo["A B C"]] = "A B C";
                        this.emit(name + "[" + name + "[" + memberName + "]] = " + memberName + ";\n");
                    }
                }
            }
            catch (e_17_1) { e_17 = { error: e_17_1 }; }
            finally {
                try {
                    if (_e && !_e.done && (_f = _d.return)) _f.call(_d);
                }
                finally { if (e_17) throw e_17.error; }
            }
            var e_16, _c, e_17, _f;
        };
        return Annotator;
    }(ClosureRewriter));
    /** ExternsWriter generates Closure externs from TypeScript source. */
    var ExternsWriter = /** @class */ (function (_super) {
        __extends(ExternsWriter, _super);
        function ExternsWriter() {
            return _super !== null && _super.apply(this, arguments) || this;
        }
        ExternsWriter.prototype.process = function () {
            var _this = this;
            this.findExternRoots().forEach(function (node) { return _this.visit(node); });
            return this.getOutput();
        };
        ExternsWriter.prototype.ensureSymbolDeclared = function (sym) {
            var decl = this.findExportedDeclaration(sym);
            if (!decl)
                return; // symbol does not need declaring.
            this.error(this.file, "Cannot reference a non-global symbol from an externs: " + sym.name + " declared at " + formatLocation(decl.getSourceFile(), decl.getStart()));
        };
        ExternsWriter.prototype.newTypeTranslator = function (context) {
            var tt = _super.prototype.newTypeTranslator.call(this, context);
            tt.isForExterns = true;
            return tt;
        };
        ExternsWriter.prototype.findExternRoots = function () {
            if (util_1.isDtsFileName(this.file.fileName)) {
                return [this.file];
            }
            return this.file.statements.filter(function (stmt) { return util_1.hasModifierFlag(stmt, ts.ModifierFlags.Ambient); });
        };
        /** visit is the main entry point.  It generates externs from a ts.Node. */
        ExternsWriter.prototype.visit = function (node, namespace) {
            if (namespace === void 0) { namespace = []; }
            switch (node.kind) {
                case ts.SyntaxKind.SourceFile:
                    var sourceFile = node;
                    try {
                        for (var _a = __values(sourceFile.statements), _b = _a.next(); !_b.done; _b = _a.next()) {
                            var stmt = _b.value;
                            this.visit(stmt, namespace);
                        }
                    }
                    catch (e_18_1) { e_18 = { error: e_18_1 }; }
                    finally {
                        try {
                            if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
                        }
                        finally { if (e_18) throw e_18.error; }
                    }
                    break;
                case ts.SyntaxKind.ModuleDeclaration:
                    var decl = node;
                    switch (decl.name.kind) {
                        case ts.SyntaxKind.Identifier:
                            // E.g. "declare namespace foo {"
                            var name_5 = rewriter_1.getIdentifierText(decl.name);
                            if (name_5 === 'global') {
                                // E.g. "declare global { ... }".  Reset to the outer namespace.
                                namespace = [];
                            }
                            else {
                                if (this.isFirstDeclaration(decl)) {
                                    this.emit('/** @const */\n');
                                    this.writeExternsVariable(name_5, namespace, '{}');
                                }
                                namespace = namespace.concat(name_5);
                            }
                            if (decl.body)
                                this.visit(decl.body, namespace);
                            break;
                        case ts.SyntaxKind.StringLiteral:
                            // E.g. "declare module 'foo' {" (note the quotes).
                            // We still want to emit externs for this module, but
                            // Closure doesn't really provide a mechanism for
                            // module-scoped externs.  For now, ignore the enclosing
                            // namespace (because this is declaring a top-level module)
                            // and emit into a fake namespace.
                            // Declare the top-level "tsickle_declare_module".
                            this.emit('/** @const */\n');
                            this.writeExternsVariable('tsickle_declare_module', [], '{}');
                            namespace = ['tsickle_declare_module'];
                            // Declare the inner "tsickle_declare_module.foo", if it's not
                            // declared already elsewhere.
                            var importName = decl.name.text;
                            this.emit("// Derived from: declare module \"" + importName + "\"\n");
                            // We also don't care about the actual name of the module ("foo"
                            // in the above example), except that we want it to not conflict.
                            importName = importName.replace(/_/, '__').replace(/[^A-Za-z]/g, '_');
                            if (this.isFirstDeclaration(decl)) {
                                this.emit('/** @const */\n');
                                this.writeExternsVariable(importName, namespace, '{}');
                            }
                            // Declare the contents inside the "tsickle_declare_module.foo".
                            if (decl.body)
                                this.visit(decl.body, namespace.concat(importName));
                            break;
                        default:
                            this.errorUnimplementedKind(decl.name, 'externs generation of namespace');
                    }
                    break;
                case ts.SyntaxKind.ModuleBlock:
                    var block = node;
                    try {
                        for (var _d = __values(block.statements), _e = _d.next(); !_e.done; _e = _d.next()) {
                            var stmt = _e.value;
                            this.visit(stmt, namespace);
                        }
                    }
                    catch (e_19_1) { e_19 = { error: e_19_1 }; }
                    finally {
                        try {
                            if (_e && !_e.done && (_f = _d.return)) _f.call(_d);
                        }
                        finally { if (e_19) throw e_19.error; }
                    }
                    break;
                case ts.SyntaxKind.ImportEqualsDeclaration:
                    var importEquals = node;
                    var localName = rewriter_1.getIdentifierText(importEquals.name);
                    if (localName === 'ng') {
                        this.emit("\n/* Skipping problematic import ng = ...; */\n");
                        break;
                    }
                    if (importEquals.moduleReference.kind === ts.SyntaxKind.ExternalModuleReference) {
                        this.emit("\n/* TODO: import " + localName + " = require(...) */\n");
                        break;
                    }
                    var qn = rewriter_1.getEntityNameText(importEquals.moduleReference);
                    // @const so that Closure Compiler understands this is an alias.
                    if (namespace.length === 0)
                        this.emit('/** @const */\n');
                    this.writeExternsVariable(localName, namespace, qn);
                    break;
                case ts.SyntaxKind.ClassDeclaration:
                case ts.SyntaxKind.InterfaceDeclaration:
                    this.writeExternsType(node, namespace);
                    break;
                case ts.SyntaxKind.FunctionDeclaration:
                    var fnDecl = node;
                    var name_6 = fnDecl.name;
                    if (!name_6) {
                        this.error(fnDecl, 'anonymous function in externs');
                        break;
                    }
                    // Gather up all overloads of this function.
                    var sym = this.mustGetSymbolAtLocation(name_6);
                    var decls = sym.declarations.filter(function (d) { return d.kind === ts.SyntaxKind.FunctionDeclaration; });
                    // Only emit the first declaration of each overloaded function.
                    if (fnDecl !== decls[0])
                        break;
                    var params = this.emitFunctionType(decls);
                    this.writeExternsFunction(name_6, params, namespace);
                    break;
                case ts.SyntaxKind.VariableStatement:
                    try {
                        for (var _g = __values(node.declarationList.declarations), _h = _g.next(); !_h.done; _h = _g.next()) {
                            var decl_1 = _h.value;
                            this.writeExternsVariableDecl(decl_1, namespace);
                        }
                    }
                    catch (e_20_1) { e_20 = { error: e_20_1 }; }
                    finally {
                        try {
                            if (_h && !_h.done && (_j = _g.return)) _j.call(_g);
                        }
                        finally { if (e_20) throw e_20.error; }
                    }
                    break;
                case ts.SyntaxKind.EnumDeclaration:
                    this.writeExternsEnum(node, namespace);
                    break;
                case ts.SyntaxKind.TypeAliasDeclaration:
                    this.writeExternsTypeAlias(node, namespace);
                    break;
                default:
                    this.emit("\n/* TODO: " + ts.SyntaxKind[node.kind] + " in " + namespace.join('.') + " */\n");
                    break;
            }
            var e_18, _c, e_19, _f, e_20, _j;
        };
        /**
         * isFirstDeclaration returns true if decl is the first declaration
         * of its symbol.  E.g. imagine
         *   interface Foo { x: number; }
         *   interface Foo { y: number; }
         * we only want to emit the "@record" for Foo on the first one.
         */
        ExternsWriter.prototype.isFirstDeclaration = function (decl) {
            if (!decl.name)
                return true;
            var sym = this.mustGetSymbolAtLocation(decl.name);
            if (!sym.declarations || sym.declarations.length < 2)
                return true;
            return decl === sym.declarations[0];
        };
        ExternsWriter.prototype.writeExternsType = function (decl, namespace) {
            var name = decl.name;
            if (!name) {
                this.error(decl, 'anonymous type in externs');
                return;
            }
            var typeName = namespace.concat([name.getText()]).join('.');
            if (exports.closureExternsBlacklist.indexOf(typeName) >= 0)
                return;
            if (this.isFirstDeclaration(decl)) {
                var paramNames = [];
                var jsdocTags = [];
                var writeJsDoc = true;
                this.maybeAddHeritageClauses(jsdocTags, decl);
                if (decl.kind === ts.SyntaxKind.ClassDeclaration) {
                    jsdocTags.push({ tagName: 'constructor' });
                    jsdocTags.push({ tagName: 'struct' });
                    var ctors = decl
                        .members.filter(function (m) { return m.kind === ts.SyntaxKind.Constructor; });
                    if (ctors.length) {
                        writeJsDoc = false;
                        var firstCtor = ctors[0];
                        var ctorTags = [{ tagName: 'constructor' }, { tagName: 'struct' }];
                        if (ctors.length > 1) {
                            paramNames = this.emitFunctionType(ctors, ctorTags);
                        }
                        else {
                            paramNames = this.emitFunctionType([firstCtor], ctorTags);
                        }
                    }
                }
                else {
                    jsdocTags.push({ tagName: 'record' });
                    jsdocTags.push({ tagName: 'struct' });
                }
                if (writeJsDoc)
                    this.emit(jsdoc.toString(jsdocTags));
                this.writeExternsFunction(name, paramNames, namespace);
            }
            // Process everything except (MethodSignature|MethodDeclaration|Constructor)
            var methods = new Map();
            try {
                for (var _a = __values(decl.members), _b = _a.next(); !_b.done; _b = _a.next()) {
                    var member = _b.value;
                    switch (member.kind) {
                        case ts.SyntaxKind.PropertySignature:
                        case ts.SyntaxKind.PropertyDeclaration:
                            var prop = member;
                            if (prop.name.kind === ts.SyntaxKind.Identifier) {
                                this.emitJSDocType(prop);
                                if (util_1.hasModifierFlag(prop, ts.ModifierFlags.Static)) {
                                    this.emit("\n" + typeName + "." + prop.name.getText() + ";\n");
                                }
                                else {
                                    this.emit("\n" + typeName + ".prototype." + prop.name.getText() + ";\n");
                                }
                                continue;
                            }
                            // TODO: For now property names other than Identifiers are not handled; e.g.
                            //    interface Foo { "123bar": number }
                            break;
                        case ts.SyntaxKind.MethodSignature:
                        case ts.SyntaxKind.MethodDeclaration:
                            var method = member;
                            var isStatic = util_1.hasModifierFlag(method, ts.ModifierFlags.Static);
                            var methodSignature = method.name.getText() + "$$$" + (isStatic ? 'static' : 'instance');
                            if (methods.has(methodSignature)) {
                                methods.get(methodSignature).push(method);
                            }
                            else {
                                methods.set(methodSignature, [method]);
                            }
                            continue;
                        case ts.SyntaxKind.Constructor:
                            continue; // Handled above.
                        default:
                            // Members can include things like index signatures, for e.g.
                            //   interface Foo { [key: string]: number; }
                            // For now, just skip it.
                            break;
                    }
                    // If we get here, the member wasn't handled in the switch statement.
                    var memberName = namespace;
                    if (member.name) {
                        memberName = memberName.concat([member.name.getText()]);
                    }
                    this.emit("\n/* TODO: " + ts.SyntaxKind[member.kind] + ": " + memberName.join('.') + " */\n");
                }
            }
            catch (e_21_1) { e_21 = { error: e_21_1 }; }
            finally {
                try {
                    if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
                }
                finally { if (e_21) throw e_21.error; }
            }
            try {
                // Handle method declarations/signatures separately, since we need to deal with overloads.
                for (var _d = __values(Array.from(methods.values())), _e = _d.next(); !_e.done; _e = _d.next()) {
                    var methodVariants = _e.value;
                    var firstMethodVariant = methodVariants[0];
                    var parameterNames = void 0;
                    if (methodVariants.length > 1) {
                        parameterNames = this.emitFunctionType(methodVariants);
                    }
                    else {
                        parameterNames = this.emitFunctionType([firstMethodVariant]);
                    }
                    var methodNamespace = namespace.concat([name.getText()]);
                    // If the method is static, don't add the prototype.
                    if (!util_1.hasModifierFlag(firstMethodVariant, ts.ModifierFlags.Static)) {
                        methodNamespace.push('prototype');
                    }
                    this.writeExternsFunction(firstMethodVariant.name, parameterNames, methodNamespace);
                }
            }
            catch (e_22_1) { e_22 = { error: e_22_1 }; }
            finally {
                try {
                    if (_e && !_e.done && (_f = _d.return)) _f.call(_d);
                }
                finally { if (e_22) throw e_22.error; }
            }
            var e_21, _c, e_22, _f;
        };
        ExternsWriter.prototype.writeExternsVariableDecl = function (decl, namespace) {
            if (decl.name.kind === ts.SyntaxKind.Identifier) {
                var name_7 = rewriter_1.getIdentifierText(decl.name);
                if (exports.closureExternsBlacklist.indexOf(name_7) >= 0)
                    return;
                this.emitJSDocType(decl);
                this.emit('\n');
                this.writeExternsVariable(name_7, namespace);
            }
            else {
                this.errorUnimplementedKind(decl.name, 'externs for variable');
            }
        };
        ExternsWriter.prototype.writeExternsVariable = function (name, namespace, value) {
            var qualifiedName = namespace.concat([name]).join('.');
            if (namespace.length === 0)
                this.emit("var ");
            this.emit(qualifiedName);
            if (value)
                this.emit(" = " + value);
            this.emit(';\n');
        };
        ExternsWriter.prototype.writeExternsFunction = function (name, params, namespace) {
            var paramsStr = params.join(', ');
            if (namespace.length > 0) {
                var fqn = namespace.join('.');
                if (name.kind === ts.SyntaxKind.Identifier) {
                    fqn += '.'; // computed names include [ ] in their getText() representation.
                }
                fqn += name.getText();
                this.emit(fqn + " = function(" + paramsStr + ") {};\n");
            }
            else {
                if (name.kind !== ts.SyntaxKind.Identifier) {
                    this.error(name, 'Non-namespaced computed name in externs');
                }
                this.emit("function " + name.getText() + "(" + paramsStr + ") {}\n");
            }
        };
        ExternsWriter.prototype.writeExternsEnum = function (decl, namespace) {
            var name = rewriter_1.getIdentifierText(decl.name);
            this.emit('\n/** @const */\n');
            this.writeExternsVariable(name, namespace, '{}');
            namespace = namespace.concat([name]);
            try {
                for (var _a = __values(decl.members), _b = _a.next(); !_b.done; _b = _a.next()) {
                    var member = _b.value;
                    var memberName = void 0;
                    switch (member.name.kind) {
                        case ts.SyntaxKind.Identifier:
                            memberName = rewriter_1.getIdentifierText(member.name);
                            break;
                        case ts.SyntaxKind.StringLiteral:
                            var text = member.name.text;
                            if (isValidClosurePropertyName(text))
                                memberName = text;
                            break;
                        default:
                            break;
                    }
                    if (!memberName) {
                        this.emit("\n/* TODO: " + ts.SyntaxKind[member.name.kind] + ": " + member.name.getText() + " */\n");
                        continue;
                    }
                    this.emit('/** @const {number} */\n');
                    this.writeExternsVariable(memberName, namespace);
                }
            }
            catch (e_23_1) { e_23 = { error: e_23_1 }; }
            finally {
                try {
                    if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
                }
                finally { if (e_23) throw e_23.error; }
            }
            var e_23, _c;
        };
        ExternsWriter.prototype.writeExternsTypeAlias = function (decl, namespace) {
            var typeStr = this.typeToClosure(decl, undefined, true /* resolveAlias */);
            this.emit("\n/** @typedef {" + typeStr + "} */\n");
            this.writeExternsVariable(rewriter_1.getIdentifierText(decl.name), namespace);
        };
        return ExternsWriter;
    }(ClosureRewriter));
    function isPolymerBehaviorPropertyInCallExpression(pa) {
        var parentParent = pa.parent && pa.parent.parent;
        if (pa.name.kind !== ts.SyntaxKind.Identifier ||
            pa.name.text !== 'behaviors' || !pa.parent || !pa.parent.parent ||
            pa.parent.parent.kind !== ts.SyntaxKind.CallExpression) {
            return false;
        }
        var expr = parentParent.expression;
        return expr.kind === ts.SyntaxKind.Identifier && expr.text === 'Polymer';
    }
    function annotate(typeChecker, file, host, tsHost, tsOpts, sourceMapper) {
        return new Annotator(typeChecker, file, host, tsHost, tsOpts, sourceMapper).annotate();
    }
    exports.annotate = annotate;
    function writeExterns(typeChecker, file, host) {
        return new ExternsWriter(typeChecker, file, host).process();
    }
    exports.writeExterns = writeExterns;
    /** Concatenate all generated externs definitions together into a string. */
    function getGeneratedExterns(externs) {
        var allExterns = exports.EXTERNS_HEADER;
        try {
            for (var _a = __values(Object.keys(externs)), _b = _a.next(); !_b.done; _b = _a.next()) {
                var fileName = _b.value;
                allExterns += "// externs from " + fileName + ":\n";
                allExterns += externs[fileName];
            }
        }
        catch (e_24_1) { e_24 = { error: e_24_1 }; }
        finally {
            try {
                if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
            }
            finally { if (e_24) throw e_24.error; }
        }
        return allExterns;
        var e_24, _c;
    }
    exports.getGeneratedExterns = getGeneratedExterns;
    function mergeEmitResults(emitResults) {
        var diagnostics = [];
        var emitSkipped = true;
        var emittedFiles = [];
        var externs = {};
        var modulesManifest = new modules_manifest_1.ModulesManifest();
        try {
            for (var emitResults_1 = __values(emitResults), emitResults_1_1 = emitResults_1.next(); !emitResults_1_1.done; emitResults_1_1 = emitResults_1.next()) {
                var er = emitResults_1_1.value;
                diagnostics.push.apply(diagnostics, __spread(er.diagnostics));
                emitSkipped = emitSkipped || er.emitSkipped;
                emittedFiles.push.apply(emittedFiles, __spread(er.emittedFiles));
                Object.assign(externs, er.externs);
                modulesManifest.addManifest(er.modulesManifest);
            }
        }
        catch (e_25_1) { e_25 = { error: e_25_1 }; }
        finally {
            try {
                if (emitResults_1_1 && !emitResults_1_1.done && (_a = emitResults_1.return)) _a.call(emitResults_1);
            }
            finally { if (e_25) throw e_25.error; }
        }
        return { diagnostics: diagnostics, emitSkipped: emitSkipped, emittedFiles: emittedFiles, externs: externs, modulesManifest: modulesManifest };
        var e_25, _a;
    }
    exports.mergeEmitResults = mergeEmitResults;
    function emitWithTsickle(program, host, tsHost, tsOptions, targetSourceFile, writeFile, cancellationToken, emitOnlyDtsFiles, customTransformers) {
        if (customTransformers === void 0) { customTransformers = {}; }
        var tsickleDiagnostics = [];
        var typeChecker = program.getTypeChecker();
        var tsickleSourceTransformers = [];
        if (host.transformTypesToClosure) {
            // Note: tsickle.annotate can also lower decorators in the same run.
            tsickleSourceTransformers.push(transformer_sourcemap_1.createTransformerFromSourceMap(function (sourceFile, sourceMapper) {
                var _a = annotate(typeChecker, sourceFile, host, tsHost, tsOptions, sourceMapper), output = _a.output, diagnostics = _a.diagnostics;
                tsickleDiagnostics.push.apply(tsickleDiagnostics, __spread(diagnostics));
                return output;
            }));
            // Only add @suppress {checkTypes} comments when also adding type annotations.
            tsickleSourceTransformers.push(fileoverview_comment_transformer_1.transformFileoverviewComment);
            tsickleSourceTransformers.push(class_decorator_downlevel_transformer_1.classDecoratorDownlevelTransformer(typeChecker, tsickleDiagnostics));
        }
        else if (host.transformDecorators) {
            tsickleSourceTransformers.push(transformer_sourcemap_1.createTransformerFromSourceMap(function (sourceFile, sourceMapper) {
                var _a = decorator.convertDecorators(typeChecker, sourceFile, sourceMapper), output = _a.output, diagnostics = _a.diagnostics;
                tsickleDiagnostics.push.apply(tsickleDiagnostics, __spread(diagnostics));
                return output;
            }));
            tsickleSourceTransformers.push(class_decorator_downlevel_transformer_1.classDecoratorDownlevelTransformer(typeChecker, tsickleDiagnostics));
        }
        // // For debugging: transformer that just emits the same text.
        // beforeTsTransformers.push(createTransformer(host, typeChecker, (sourceFile, sourceMapper) => {
        //   sourceMapper.addMapping(sourceFile, {position: 0, line: 0, column: 0}, {position: 0, line: 0,
        //   column: 0}, sourceFile.text.length); return sourceFile.text;
        // }));
        var tsickleTransformers = transformer_util_1.createCustomTransformers({ before: tsickleSourceTransformers });
        var tsTransformers = {
            before: __spread((customTransformers.beforeTsickle || []), (tsickleTransformers.before || []).map(function (tf) { return skipTransformForSourceFileIfNeeded(host, tf); }), (customTransformers.beforeTs || [])),
            after: __spread((customTransformers.afterTs || []), (tsickleTransformers.after || []).map(function (tf) { return skipTransformForSourceFileIfNeeded(host, tf); }))
        };
        var writeFileDelegate = writeFile || tsHost.writeFile.bind(tsHost);
        var modulesManifest = new modules_manifest_1.ModulesManifest();
        var writeFileImpl = function (fileName, content, writeByteOrderMark, onError, sourceFiles) {
            if (path.extname(fileName) !== '.map') {
                if (tsOptions.inlineSourceMap) {
                    content = combineInlineSourceMaps(program, fileName, content);
                }
                else {
                    content = source_map_utils_1.removeInlineSourceMap(content);
                }
                content = es5processor.convertCommonJsToGoogModuleIfNeeded(host, modulesManifest, fileName, content);
            }
            else {
                content = combineSourceMaps(program, fileName, content);
            }
            if (host.addDtsClutzAliases && util_1.isDtsFileName(fileName) && sourceFiles) {
                content = addClutzAliases(fileName, content, sourceFiles, typeChecker, host);
            }
            writeFileDelegate(fileName, content, writeByteOrderMark, onError, sourceFiles);
        };
        var _a = program.emit(targetSourceFile, writeFileImpl, cancellationToken, emitOnlyDtsFiles, tsTransformers), tsDiagnostics = _a.diagnostics, emitSkipped = _a.emitSkipped, emittedFiles = _a.emittedFiles;
        var externs = {};
        if (host.transformTypesToClosure) {
            var sourceFiles = targetSourceFile ? [targetSourceFile] : program.getSourceFiles();
            sourceFiles.forEach(function (sf) {
                if (util_1.isDtsFileName(sf.fileName) && host.shouldSkipTsickleProcessing(sf.fileName)) {
                    return;
                }
                var _a = writeExterns(typeChecker, sf, host), output = _a.output, diagnostics = _a.diagnostics;
                if (output) {
                    externs[sf.fileName] = output;
                }
                if (diagnostics) {
                    tsickleDiagnostics.push.apply(tsickleDiagnostics, __spread(diagnostics));
                }
            });
        }
        // All diagnostics (including warnings) are treated as errors.
        // If the host decides to ignore warnings, just discard them.
        // Warnings include stuff like "don't use @type in your jsdoc"; tsickle
        // warns and then fixes up the code to be Closure-compatible anyway.
        tsickleDiagnostics = tsickleDiagnostics.filter(function (d) { return d.category === ts.DiagnosticCategory.Error ||
            !host.shouldIgnoreWarningsForPath(d.file.fileName); });
        return {
            modulesManifest: modulesManifest,
            emitSkipped: emitSkipped,
            emittedFiles: emittedFiles || [],
            diagnostics: __spread(tsDiagnostics, tsickleDiagnostics),
            externs: externs
        };
    }
    exports.emitWithTsickle = emitWithTsickle;
    function areAnyDeclarationsFromSourceFile(declarations, sourceFile) {
        try {
            for (var declarations_1 = __values(declarations), declarations_1_1 = declarations_1.next(); !declarations_1_1.done; declarations_1_1 = declarations_1.next()) {
                var decl = declarations_1_1.value;
                if (decl.getSourceFile() === sourceFile) {
                    return true;
                }
            }
        }
        catch (e_26_1) { e_26 = { error: e_26_1 }; }
        finally {
            try {
                if (declarations_1_1 && !declarations_1_1.done && (_a = declarations_1.return)) _a.call(declarations_1);
            }
            finally { if (e_26) throw e_26.error; }
        }
        return false;
        var e_26, _a;
    }
    function addToMultiMap(map, key, value) {
        var array = map.get(key);
        if (array) {
            array.push(value);
        }
        else {
            map.set(key, [value]);
        }
    }
    /**
     * A tsickle produced declaration file might be consumed be referenced by Clutz
     * produced .d.ts files, which use symbol names based on Closure's internal
     * naming conventions, so we need to provide aliases for all the exported symbols
     * in the Clutz naming convention.
     */
    function addClutzAliases(fileName, dtsFileContent, sourceFiles, typeChecker, host) {
        var reexportsByNamespace = new Map();
        try {
            for (var sourceFiles_1 = __values(sourceFiles), sourceFiles_1_1 = sourceFiles_1.next(); !sourceFiles_1_1.done; sourceFiles_1_1 = sourceFiles_1.next()) {
                var sf = sourceFiles_1_1.value;
                var moduleSymbol = typeChecker.getSymbolAtLocation(sf);
                var moduleExports = moduleSymbol && typeChecker.getExportsOfModule(moduleSymbol);
                if (!moduleExports) {
                    return dtsFileContent;
                }
                // pathToModuleName expects the file name to end in .js
                var jsFileName = fileName.replace('.d.ts', '.js');
                var moduleName = host.pathToModuleName('', jsFileName);
                var clutzModuleName = moduleName.replace(/\./g, '$');
                try {
                    // moduleExports is a ts.Map<ts.Symbol> which is an es6 Map, but has a
                    // different type for no reason
                    for (var moduleExports_1 = __values(moduleExports), moduleExports_1_1 = moduleExports_1.next(); !moduleExports_1_1.done; moduleExports_1_1 = moduleExports_1.next()) {
                        var symbol = moduleExports_1_1.value;
                        // We only want to add clutz aliases in the file the symbol was originally
                        // exported from, not in any files where the symbol was reexported, since
                        // the alias will refer to a symbol that might not be present in the reexporting
                        // file.  If there are no declarations, be conservative and emit the aliases.
                        var declarations = symbol.getDeclarations();
                        if (declarations && !areAnyDeclarationsFromSourceFile(declarations, sf)) {
                            continue;
                        }
                        // default is a keyword in typescript, so the name of the export being default
                        // means that it's a default export
                        if (symbol.name === 'default') {
                            dtsFileContent +=
                                "// skipped emitting clutz aliases for a default export, which aren't currently supported";
                            continue;
                        }
                        // Want to alias the symbol to match what clutz would produce, so clutz .d.ts's
                        // can reference symbols from typescript .d.ts's. See examples at:
                        // https://github.com/angular/clutz/tree/master/src/test/java/com/google/javascript/clutz
                        // The first symbol name is that currently produced by clutz, and the second
                        // is what incremental clutz will produce.
                        var reexports = [];
                        reexports.push({
                            namespace: 'ಠ_ಠ.clutz',
                            clutzSymbolName: "module$contents$" + clutzModuleName + "_" + symbol.name,
                            aliasedSymbolName: symbol.name
                        });
                        reexports.push({
                            namespace: 'ಠ_ಠ.clutz.module$exports$' + clutzModuleName,
                            clutzSymbolName: symbol.name,
                            aliasedSymbolName: "module$contents$" + clutzModuleName + "_" + symbol.name
                        });
                        var _a = getGenericTypeParameters(symbol), params = _a.params, paramsWithContraint = _a.paramsWithContraint;
                        if (symbol.flags & ts.SymbolFlags.Class) {
                            // classes need special care to match clutz, which seperates class types into a
                            // type for the static properties and a type for the instance properties
                            reexports.push({
                                namespace: 'ಠ_ಠ.clutz',
                                clutzSymbolName: "module$contents$" + clutzModuleName + "_" + symbol.name + "_Instance",
                                aliasedSymbolName: symbol.name
                            });
                            reexports.push({
                                namespace: 'ಠ_ಠ.clutz.module$exports$' + clutzModuleName,
                                clutzSymbolName: symbol.name + '_Instance',
                                aliasedSymbolName: "module$contents$" + clutzModuleName + "_" + symbol.name
                            });
                        }
                        if (symbol.flags & ts.SymbolFlags.Type || symbol.flags & ts.SymbolFlags.Class) {
                            try {
                                for (var reexports_1 = __values(reexports), reexports_1_1 = reexports_1.next(); !reexports_1_1.done; reexports_1_1 = reexports_1.next()) {
                                    var _b = reexports_1_1.value, namespace = _b.namespace, clutzSymbolName = _b.clutzSymbolName, aliasedSymbolName = _b.aliasedSymbolName;
                                    addToMultiMap(reexportsByNamespace, namespace, "type " + clutzSymbolName + paramsWithContraint + " = " + aliasedSymbolName + params + ";");
                                }
                            }
                            catch (e_27_1) { e_27 = { error: e_27_1 }; }
                            finally {
                                try {
                                    if (reexports_1_1 && !reexports_1_1.done && (_c = reexports_1.return)) _c.call(reexports_1);
                                }
                                finally { if (e_27) throw e_27.error; }
                            }
                        }
                        if (symbol.flags & ts.SymbolFlags.Value || symbol.flags & ts.SymbolFlags.Class) {
                            try {
                                for (var reexports_2 = __values(reexports), reexports_2_1 = reexports_2.next(); !reexports_2_1.done; reexports_2_1 = reexports_2.next()) {
                                    var _d = reexports_2_1.value, namespace = _d.namespace, clutzSymbolName = _d.clutzSymbolName, aliasedSymbolName = _d.aliasedSymbolName;
                                    addToMultiMap(reexportsByNamespace, namespace, "const " + clutzSymbolName + ": typeof " + aliasedSymbolName + ";");
                                }
                            }
                            catch (e_28_1) { e_28 = { error: e_28_1 }; }
                            finally {
                                try {
                                    if (reexports_2_1 && !reexports_2_1.done && (_e = reexports_2.return)) _e.call(reexports_2);
                                }
                                finally { if (e_28) throw e_28.error; }
                            }
                        }
                    }
                }
                catch (e_29_1) { e_29 = { error: e_29_1 }; }
                finally {
                    try {
                        if (moduleExports_1_1 && !moduleExports_1_1.done && (_f = moduleExports_1.return)) _f.call(moduleExports_1);
                    }
                    finally { if (e_29) throw e_29.error; }
                }
            }
        }
        catch (e_30_1) { e_30 = { error: e_30_1 }; }
        finally {
            try {
                if (sourceFiles_1_1 && !sourceFiles_1_1.done && (_g = sourceFiles_1.return)) _g.call(sourceFiles_1);
            }
            finally { if (e_30) throw e_30.error; }
        }
        if (reexportsByNamespace.size) {
            dtsFileContent += 'declare global {\n';
            try {
                for (var reexportsByNamespace_1 = __values(reexportsByNamespace), reexportsByNamespace_1_1 = reexportsByNamespace_1.next(); !reexportsByNamespace_1_1.done; reexportsByNamespace_1_1 = reexportsByNamespace_1.next()) {
                    var _h = __read(reexportsByNamespace_1_1.value, 2), namespace = _h[0], rexps = _h[1];
                    dtsFileContent += "\tnamespace " + namespace + " {\n";
                    try {
                        for (var rexps_1 = __values(rexps), rexps_1_1 = rexps_1.next(); !rexps_1_1.done; rexps_1_1 = rexps_1.next()) {
                            var rexp = rexps_1_1.value;
                            dtsFileContent += "\t\t" + rexp + "\n";
                        }
                    }
                    catch (e_31_1) { e_31 = { error: e_31_1 }; }
                    finally {
                        try {
                            if (rexps_1_1 && !rexps_1_1.done && (_j = rexps_1.return)) _j.call(rexps_1);
                        }
                        finally { if (e_31) throw e_31.error; }
                    }
                    dtsFileContent += '\t}\n';
                }
            }
            catch (e_32_1) { e_32 = { error: e_32_1 }; }
            finally {
                try {
                    if (reexportsByNamespace_1_1 && !reexportsByNamespace_1_1.done && (_k = reexportsByNamespace_1.return)) _k.call(reexportsByNamespace_1);
                }
                finally { if (e_32) throw e_32.error; }
            }
            dtsFileContent += '}\n';
        }
        return dtsFileContent;
        var e_30, _g, e_29, _f, e_27, _c, e_28, _e, e_32, _k, e_31, _j;
    }
    /**
     * Returns 2 strings specifying the generic type arguments for the symbol.  The constrained params
     * include any `T extends foo` arguments, the regular params are just a list of the type symbols,
     * since we need the constraints on the LHS of the alias declaration, but can't have them on the
     * RHS.
     */
    function getGenericTypeParameters(symbol) {
        if (!symbol.declarations) {
            return { params: '', paramsWithContraint: '' };
        }
        // All declarations have to have matching generic types, so we're safe just looking at
        // the first one.
        if (!symbol.declarations[0]) {
            return { params: '', paramsWithContraint: '' };
        }
        var declaration = symbol.declarations[0];
        if ([
            ts.SyntaxKind.FunctionDeclaration, ts.SyntaxKind.ConstructorKeyword,
            ts.SyntaxKind.ClassDeclaration, ts.SyntaxKind.InterfaceDeclaration,
            ts.SyntaxKind.TypeAliasDeclaration
        ].indexOf(declaration.kind) === -1) {
            return { params: '', paramsWithContraint: '' };
        }
        var declarationWithTypeParameters = declaration;
        if (!declarationWithTypeParameters.typeParameters) {
            return { params: '', paramsWithContraint: '' };
        }
        var paramList = [];
        var constrainedParamList = [];
        try {
            for (var _a = __values(declarationWithTypeParameters.typeParameters), _b = _a.next(); !_b.done; _b = _a.next()) {
                var param = _b.value;
                var constrainedParam = param.name.getText();
                if (param.constraint) {
                    constrainedParam += " extends " + param.constraint.getText();
                }
                if (param.default) {
                    constrainedParam += " = " + param.default.getText();
                }
                constrainedParamList.push(constrainedParam);
                paramList.push(param.name.getText());
            }
        }
        catch (e_33_1) { e_33 = { error: e_33_1 }; }
        finally {
            try {
                if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
            }
            finally { if (e_33) throw e_33.error; }
        }
        var params = "<" + paramList.join(',') + ">";
        var paramsWithContraint = "<" + constrainedParamList.join(',') + ">";
        return { params: params, paramsWithContraint: paramsWithContraint };
        var e_33, _c;
    }
    function skipTransformForSourceFileIfNeeded(host, delegateFactory) {
        return function (context) {
            var delegate = delegateFactory(context);
            return function (sourceFile) {
                if (host.shouldSkipTsickleProcessing(sourceFile.fileName)) {
                    return sourceFile;
                }
                return delegate(sourceFile);
            };
        };
    }
    function combineInlineSourceMaps(program, filePath, compiledJsWithInlineSourceMap) {
        if (util_1.isDtsFileName(filePath)) {
            return compiledJsWithInlineSourceMap;
        }
        var sourceMapJson = source_map_utils_1.extractInlineSourceMap(compiledJsWithInlineSourceMap);
        compiledJsWithInlineSourceMap = source_map_utils_1.removeInlineSourceMap(compiledJsWithInlineSourceMap);
        var composedSourceMap = combineSourceMaps(program, filePath, sourceMapJson);
        return source_map_utils_1.setInlineSourceMap(compiledJsWithInlineSourceMap, composedSourceMap);
    }
    function combineSourceMaps(program, filePath, tscSourceMapText) {
        var tscSourceMap = source_map_utils_1.parseSourceMap(tscSourceMapText);
        if (tscSourceMap.sourcesContent) {
            // strip incoming sourcemaps from the sources in the sourcemap
            // to reduce the size of the sourcemap.
            tscSourceMap.sourcesContent = tscSourceMap.sourcesContent.map(function (content) {
                if (source_map_utils_1.containsInlineSourceMap(content)) {
                    content = source_map_utils_1.removeInlineSourceMap(content);
                }
                return content;
            });
        }
        var fileDir = path.dirname(filePath);
        var tscSourceMapGenerator;
        try {
            for (var _a = __values(tscSourceMap.sources), _b = _a.next(); !_b.done; _b = _a.next()) {
                var sourceFileName = _b.value;
                var sourceFile = program.getSourceFile(path.resolve(fileDir, sourceFileName));
                if (!sourceFile || !source_map_utils_1.containsInlineSourceMap(sourceFile.text)) {
                    continue;
                }
                var preexistingSourceMapText = source_map_utils_1.extractInlineSourceMap(sourceFile.text);
                if (!tscSourceMapGenerator) {
                    tscSourceMapGenerator = source_map_1.SourceMapGenerator.fromSourceMap(new source_map_1.SourceMapConsumer(tscSourceMap));
                }
                tscSourceMapGenerator.applySourceMap(new source_map_1.SourceMapConsumer(source_map_utils_1.parseSourceMap(preexistingSourceMapText, sourceFileName)));
            }
        }
        catch (e_34_1) { e_34 = { error: e_34_1 }; }
        finally {
            try {
                if (_b && !_b.done && (_c = _a.return)) _c.call(_a);
            }
            finally { if (e_34) throw e_34.error; }
        }
        return tscSourceMapGenerator ? tscSourceMapGenerator.toString() : tscSourceMapText;
        var e_34, _c;
    }
});
//# sourceMappingURL=data:application/json;base64,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