{"version": 3, "file": "UsingObservable.js", "sourceRoot": "", "sources": ["../../src/observable/UsingObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAAkD,eAAe,CAAC,CAAA;AAIlE,kCAAkC,2BAA2B,CAAC,CAAA;AAC9D,gCAAgC,oBAAoB,CAAC,CAAA;AACrD;;;;GAIG;AACH;IAAwC,mCAAa;IAOnD,yBAAoB,eAAmD,EACnD,iBAAuF;QACzG,iBAAO,CAAC;QAFU,oBAAe,GAAf,eAAe,CAAoC;QACnD,sBAAiB,GAAjB,iBAAiB,CAAsE;IAE3G,CAAC;IARM,sBAAM,GAAb,UAAiB,eAAmD,EACnD,iBAAuF;QACtG,MAAM,CAAC,IAAI,eAAe,CAAI,eAAe,EAAE,iBAAiB,CAAC,CAAC;IACpE,CAAC;IAOD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAAyB;QACvE,IAAA,SAAmD,EAA3C,oCAAe,EAAE,wCAAiB,CAAU;QAEpD,IAAI,QAA+B,CAAC;QAEpC,IAAI,CAAC;YACH,QAAQ,GAA0B,eAAe,EAAE,CAAC;YACpD,MAAM,CAAC,IAAI,eAAe,CAAC,UAAU,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACtE,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AAxBD,CAAwC,uBAAU,GAwBjD;AAxBY,uBAAe,kBAwB3B,CAAA;AAED;IAAiC,mCAAqB;IACpD,yBAAY,WAA0B,EAClB,QAA+B,EAC/B,iBAAuF;QACzG,kBAAM,WAAW,CAAC,CAAC;QAFD,aAAQ,GAAR,QAAQ,CAAuB;QAC/B,sBAAiB,GAAjB,iBAAiB,CAAsE;QAEzG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAEO,gCAAM,GAAd;QACE,IAAI,CAAC;YACH,IAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;gBACX,IAAI,CAAC,GAAG,CAAC,qCAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;YAC5C,CAAC;QACH,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AAnBD,CAAiC,iCAAe,GAmB/C", "sourcesContent": ["import { Observable, SubscribableOrPromise } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { AnonymousSubscription, TeardownLogic } from '../Subscription';\n\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class UsingObservable<T> extends Observable<T> {\n\n  static create<T>(resourceFactory: () => AnonymousSubscription | void,\n                   observableFactory: (resource: AnonymousSubscription) => SubscribableOrPromise<T> | void): Observable<T> {\n    return new UsingObservable<T>(resourceFactory, observableFactory);\n  }\n\n  constructor(private resourceFactory: () => AnonymousSubscription | void,\n              private observableFactory: (resource: AnonymousSubscription) => SubscribableOrPromise<T> | void) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): TeardownLogic {\n    const { resourceFactory, observableFactory } = this;\n\n    let resource: AnonymousSubscription;\n\n    try {\n      resource = <AnonymousSubscription>resourceFactory();\n      return new UsingSubscriber(subscriber, resource, observableFactory);\n    } catch (err) {\n      subscriber.error(err);\n    }\n  }\n}\n\nclass UsingSubscriber<T> extends OuterSubscriber<T, T> {\n  constructor(destination: Subscriber<T>,\n              private resource: AnonymousSubscription,\n              private observableFactory: (resource: AnonymousSubscription) => SubscribableOrPromise<T> | void) {\n    super(destination);\n    destination.add(resource);\n    this.tryUse();\n  }\n\n  private tryUse(): void {\n    try {\n      const source = this.observableFactory.call(this, this.resource);\n      if (source) {\n        this.add(subscribeToResult(this, source));\n      }\n    } catch (err) {\n      this._error(err);\n    }\n  }\n}\n"]}