{"_from": "tough-cookie@~2.3.0", "_id": "tough-cookie@2.3.4", "_inBundle": false, "_integrity": "sha512-TZ6TTfI5NtZnuyy/Kecv+CnoROnyXn2DN97LontgQpCwsX2XyLYCC0ENhYkehSOwAp8rTQKc/NUIF7BkQ5rKLA==", "_location": "/tough-cookie", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tough-cookie@~2.3.0", "name": "tough-cookie", "escapedName": "tough-cookie", "rawSpec": "~2.3.0", "saveSpec": null, "fetchSpec": "~2.3.0"}, "_requiredBy": ["/loggly/request", "/request"], "_resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.4.tgz", "_shasum": "ec60cee38ac675063ffc97a5c18970578ee83655", "_spec": "tough-cookie@~2.3.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "dependencies": {"punycode": "^1.4.1"}, "deprecated": false, "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "devDependencies": {"async": "^1.4.2", "string.prototype.repeat": "^0.2.0", "vows": "^0.8.1"}, "engines": {"node": ">=0.8"}, "files": ["lib"], "homepage": "https://github.com/salesforce/tough-cookie", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "license": "BSD-3-<PERSON><PERSON>", "main": "./lib/cookie", "name": "tough-cookie", "repository": {"type": "git", "url": "git://github.com/salesforce/tough-cookie.git"}, "scripts": {"suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js", "test": "vows test/*_test.js"}, "version": "2.3.4"}