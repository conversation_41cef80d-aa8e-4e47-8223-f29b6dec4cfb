{"version": 3, "file": "using.js", "sourceRoot": "", "sources": ["../../../src/add/observable/using.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { using as staticUsing } from '../../observable/using';\n\nObservable.using = staticUsing;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let using: typeof staticUsing;\n  }\n}"]}