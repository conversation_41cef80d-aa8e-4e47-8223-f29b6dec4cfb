{"version": 3, "file": "delay.js", "sourceRoot": "", "sources": ["../../src/operators/delay.ts"], "names": [], "mappings": ";;;;;;AAAA,sBAAsB,oBAAoB,CAAC,CAAA;AAC3C,uBAAuB,gBAAgB,CAAC,CAAA;AAGxC,2BAA2B,eAAe,CAAC,CAAA;AAE3C,6BAA6B,iBAAiB,CAAC,CAAA;AAM/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,eAAyB,KAAkB,EAClB,SAA6B;IAA7B,yBAA6B,GAA7B,yBAA6B;IACpD,IAAM,aAAa,GAAG,eAAM,CAAC,KAAK,CAAC,CAAC;IACpC,IAAM,QAAQ,GAAG,aAAa,GAAG,CAAC,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAS,KAAK,CAAC,CAAC;IACtF,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAnD,CAAmD,CAAC;AACxF,CAAC;AALe,aAAK,QAKpB,CAAA;AAED;IACE,uBAAoB,KAAa,EACb,SAAqB;QADrB,UAAK,GAAL,KAAK,CAAQ;QACb,cAAS,GAAT,SAAS,CAAY;IACzC,CAAC;IAED,4BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACvF,CAAC;IACH,oBAAC;AAAD,CAAC,AARD,IAQC;AAQD;;;;GAIG;AACH;IAAiC,mCAAa;IAwB5C,yBAAY,WAA0B,EAClB,KAAa,EACb,SAAqB;QACvC,kBAAM,WAAW,CAAC,CAAC;QAFD,UAAK,GAAL,KAAK,CAAQ;QACb,cAAS,GAAT,SAAS,CAAY;QAzBjC,UAAK,GAA2B,EAAE,CAAC;QACnC,WAAM,GAAY,KAAK,CAAC;QACxB,YAAO,GAAY,KAAK,CAAC;IAyBjC,CAAC;IAvBc,wBAAQ,GAAvB,UAAwD,KAAoB;QAC1E,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;QAEtC,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClE,KAAK,CAAC,KAAK,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;QAED,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACrB,IAAM,OAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAK,CAAC,CAAC;QAC9B,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAQO,mCAAS,GAAjB,UAAkB,SAAqB;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAgB,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE;YAC/E,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS;SAClE,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,8CAAoB,GAA5B,UAA6B,YAA6B;QACxD,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC;QACT,CAAC;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAM,OAAO,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAC7E,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAES,+BAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,oBAAoB,CAAC,2BAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5D,CAAC;IAES,gCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,mCAAS,GAAnB;QACE,IAAI,CAAC,oBAAoB,CAAC,2BAAY,CAAC,cAAc,EAAE,CAAC,CAAC;IAC3D,CAAC;IACH,sBAAC;AAAD,CAAC,AAhED,CAAiC,uBAAU,GAgE1C;AAED;IACE,sBAA4B,IAAY,EACZ,YAA6B;QAD7B,SAAI,GAAJ,IAAI,CAAQ;QACZ,iBAAY,GAAZ,YAAY,CAAiB;IACzD,CAAC;IACH,mBAAC;AAAD,CAAC,AAJD,IAIC", "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { Operator } from '../Operator';\nimport { IScheduler } from '../Scheduler';\nimport { Subscriber } from '../Subscriber';\nimport { Action } from '../scheduler/Action';\nimport { Notification } from '../Notification';\nimport { Observable } from '../Observable';\nimport { PartialObserver } from '../Observer';\nimport { TeardownLogic } from '../Subscription';\nimport { MonoTypeOperatorFunction } from '../interfaces';\n\n/**\n * Delays the emission of items from the source Observable by a given timeout or\n * until a given Date.\n *\n * <span class=\"informal\">Time shifts each item by some specified amount of\n * milliseconds.</span>\n *\n * <img src=\"./img/delay.png\" width=\"100%\">\n *\n * If the delay argument is a Number, this operator time shifts the source\n * Observable by that amount of time expressed in milliseconds. The relative\n * time intervals between the values are preserved.\n *\n * If the delay argument is a Date, this operator time shifts the start of the\n * Observable execution until the given date occurs.\n *\n * @example <caption>Delay each click by one second</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var delayedClicks = clicks.delay(1000); // each click emitted after 1 second\n * delayedClicks.subscribe(x => console.log(x));\n *\n * @example <caption>Delay all clicks until a future date happens</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var date = new Date('March 15, 2050 12:00:00'); // in the future\n * var delayedClicks = clicks.delay(date); // click emitted only after that date\n * delayedClicks.subscribe(x => console.log(x));\n *\n * @see {@link debounceTime}\n * @see {@link delayWhen}\n *\n * @param {number|Date} delay The delay duration in milliseconds (a `number`) or\n * a `Date` until which the emission of the source items is delayed.\n * @param {Scheduler} [scheduler=async] The IScheduler to use for\n * managing the timers that handle the time-shift for each item.\n * @return {Observable} An Observable that delays the emissions of the source\n * Observable by the specified timeout or Date.\n * @method delay\n * @owner Observable\n */\nexport function delay<T>(delay: number|Date,\n                         scheduler: IScheduler = async): MonoTypeOperatorFunction<T> {\n  const absoluteDelay = isDate(delay);\n  const delayFor = absoluteDelay ? (+delay - scheduler.now()) : Math.abs(<number>delay);\n  return (source: Observable<T>) => source.lift(new DelayOperator(delayFor, scheduler));\n}\n\nclass DelayOperator<T> implements Operator<T, T> {\n  constructor(private delay: number,\n              private scheduler: IScheduler) {\n  }\n\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n    return source.subscribe(new DelaySubscriber(subscriber, this.delay, this.scheduler));\n  }\n}\n\ninterface DelayState<T> {\n  source: DelaySubscriber<T>;\n  destination: PartialObserver<T>;\n  scheduler: IScheduler;\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass DelaySubscriber<T> extends Subscriber<T> {\n  private queue: Array<DelayMessage<T>> = [];\n  private active: boolean = false;\n  private errored: boolean = false;\n\n  private static dispatch<T>(this: Action<DelayState<T>>, state: DelayState<T>): void {\n    const source = state.source;\n    const queue = source.queue;\n    const scheduler = state.scheduler;\n    const destination = state.destination;\n\n    while (queue.length > 0 && (queue[0].time - scheduler.now()) <= 0) {\n      queue.shift().notification.observe(destination);\n    }\n\n    if (queue.length > 0) {\n      const delay = Math.max(0, queue[0].time - scheduler.now());\n      this.schedule(state, delay);\n    } else {\n      this.unsubscribe();\n      source.active = false;\n    }\n  }\n\n  constructor(destination: Subscriber<T>,\n              private delay: number,\n              private scheduler: IScheduler) {\n    super(destination);\n  }\n\n  private _schedule(scheduler: IScheduler): void {\n    this.active = true;\n    this.add(scheduler.schedule<DelayState<T>>(DelaySubscriber.dispatch, this.delay, {\n      source: this, destination: this.destination, scheduler: scheduler\n    }));\n  }\n\n  private scheduleNotification(notification: Notification<T>): void {\n    if (this.errored === true) {\n      return;\n    }\n\n    const scheduler = this.scheduler;\n    const message = new DelayMessage(scheduler.now() + this.delay, notification);\n    this.queue.push(message);\n\n    if (this.active === false) {\n      this._schedule(scheduler);\n    }\n  }\n\n  protected _next(value: T) {\n    this.scheduleNotification(Notification.createNext(value));\n  }\n\n  protected _error(err: any) {\n    this.errored = true;\n    this.queue = [];\n    this.destination.error(err);\n  }\n\n  protected _complete() {\n    this.scheduleNotification(Notification.createComplete());\n  }\n}\n\nclass DelayMessage<T> {\n  constructor(public readonly time: number,\n              public readonly notification: Notification<T>) {\n  }\n}\n"]}