{"version": 3, "file": "ne.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ne.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE;YACE,SAAS,EAAE,UAAU,EAAE,OAAO;YAC9B,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK;SACnC;QACD,AADE;KAEH;IACD,AADE;IAEF;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    [\n      'मध्यरात', 'मध्यान्ह', 'बिहान',\n      'अपरान्ह', 'साँझ', 'बेलुकी', 'रात'\n    ],\n    ,\n  ],\n  ,\n  [\n    '00:00', '12:00', ['04:00', '12:00'], ['12:00', '16:00'], ['16:00', '19:00'],\n    ['19:00', '22:00'], ['22:00', '04:00']\n  ]\n];\n"]}