{"_from": "chokidar@^2.1.8", "_id": "chokidar@2.1.8", "_inBundle": false, "_integrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==", "_location": "/watchpack-chokidar2/chokidar", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "chokidar@^2.1.8", "name": "chokidar", "escapedName": "chokidar", "rawSpec": "^2.1.8", "saveSpec": null, "fetchSpec": "^2.1.8"}, "_requiredBy": ["/watchpack-chokidar2"], "_resolved": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.8.tgz", "_shasum": "804b3a7b6a99358c3c5c61e71d8728f041cff917", "_spec": "chokidar@^2.1.8", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\watchpack-chokidar2", "author": {"name": "<PERSON>", "url": "https://paulmillr.com"}, "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "bundleDependencies": false, "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "deprecated": false, "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "files": ["index.js", "lib/", "types/index.d.ts"], "homepage": "https://github.com/paulmillr/chokidar", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "license": "MIT", "name": "chokidar", "optionalDependencies": {"fsevents": "^1.2.7"}, "repository": {"type": "git", "url": "git+https://github.com/paulmillr/chokidar.git"}, "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "dtslint": "dtslint types", "test": "nyc mocha --exit"}, "types": "./types/index.d.ts", "version": "2.1.8"}