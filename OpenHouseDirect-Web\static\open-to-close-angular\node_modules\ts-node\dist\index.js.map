{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6BAA0E;AAC1E,yBAA0D;AAC1D,yBAAyC;AACzC,qDAAuD;AACvD,+BAAyB;AACzB,+BAAiC;AACjC,+BAAiC;AACjC,uBAAyB;AACzB,+BAAiC;AACjC,yCAAsC;AAEtC,qCAAmC;AAEnC,IAAM,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAA;AACtC,IAAM,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;AACjD,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,cAAM,OAAA,SAAS,EAAT,CAAS,CAAA;AAClF,IAAM,OAAO,GAAG,WAAW,CAAC,CAAC;IAC3B,UAAQ,GAAW,EAAE,EAAiB;QACpC,MAAM,CAAC,UAAC,CAAI;YACV,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;YACb,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,CAAC,CAAA;IACH,CAAC,CAAC,CAAC;IACH,UAAQ,CAAS,EAAE,EAAiB,IAAK,OAAA,EAAE,EAAF,CAAE,CAAA;AA4BhC,QAAA,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;AAuClC,IAAM,QAAQ,GAAG;IACf,OAAO,SAAA;IACP,UAAU,YAAA;IACV,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC1D,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;IACtD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IACzC,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC/D,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;IACvC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC5C,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAC7D,SAAS,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;CACjD,CAAA;AAKD,eAAuB,KAAyB;IAC9C,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;AAClD,CAAC;AAFD,sBAEC;AAKD,eAAuB,KAAyB;IAC9C,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;AAC9C,CAAC;AAFD,sBAEC;AAKD,0BAAkC,KAAa;IAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AAClC,CAAC;AAFD,4CAEC;AAKD;IAA6B,2BAAS;IAIpC,iBAAoB,WAA2B;QAA/C,YACE,kBACE,0CAAmC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,EAAT,CAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAG,CAChF,SACF;QAJmB,iBAAW,GAAX,WAAW,CAAgB;QAF/C,UAAI,GAAG,SAAS,CAAA;;IAMhB,CAAC;IAEH,cAAC;AAAD,CAAC,AAVD,CAA6B,sBAAS,GAUrC;AAVY,0BAAO;AA2BpB;IACE,IAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,YAAO,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAEhF,MAAM,CAAC,WAAI,CAAC,WAAM,EAAE,EAAE,aAAW,IAAM,CAAC,CAAA;AAC1C,CAAC;AAKD,kBAA0B,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC7C,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,YAAY,CAAA;IACjD,IAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IAC5C,IAAM,cAAc,GAAG,MAAM,CAC3B,OAAO,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,IAAI,EAAE,CACxD,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAC3C,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAA;IACnD,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAA;IAC5D,IAAM,WAAW,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpF,IAAM,SAAS,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAC9F,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAA;IAClF,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,IAAI,SAAS,EAAE,CAAA;IACvF,IAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,eAAe,CAAC,CAAA;IAC5F,IAAM,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;IAEnD,IAAM,KAAK,GAAU;QACnB,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QAC7B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QAC7B,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;KAC7B,CAAA;IAED,IAAM,MAAM,GAAG,MAAM,CACnB,CACE,OAAO,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;QACnC,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAC7C,CAAC,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,CACtC;QACD,CAAC,gBAAgB,CAAC,CACnB,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,IAAI,MAAM,CAAC,GAAG,CAAC,EAAf,CAAe,CAAC,CAAA;IAG7B,gBAAgB,CAAC,OAAO,CAAC;QACvB,WAAW,EAAE,MAAM;QACnB,YAAY,YAAE,IAAY;YACxB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC5B,CAAC;KACF,CAAC,CAAA;IAGF,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IACzB,IAAM,EAAE,GAAc,OAAO,CAAC,QAAQ,CAAC,CAAA;IACvC,IAAM,MAAM,GAAG,UAAU,CAAC,eAAe,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA;IAC5D,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;IAC1E,IAAM,UAAU,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IAElC,IAAM,QAAQ,GAAG,WAAI,CACnB,cAAO,CAAC,GAAG,EAAE,cAAc,CAAC,EAC5B,iBAAiB,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC,OAAO,EAAE,SAAS,WAAA,EAAE,cAAc,gBAAA,EAAE,MAAM,QAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CACxF,CAAA;IAGD,EAAE,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7B,MAAM,IAAI,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;IACrE,CAAC;IAGD,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACtB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACzB,CAAC;IAGD,GAAG,CAAC,CAAmB,UAAgB,EAAhB,KAAA,MAAM,CAAC,SAAS,EAAhB,cAAgB,EAAhB,IAAgB;QAAlC,IAAM,QAAQ,SAAA;QACjB,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;KAC7B;IAKD,sBAAuB,QAAgB;QACrC,IAAM,GAAG,GAAG,cAAO,CAAC,QAAQ,CAAC,CAAA;QAE7B,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;YACrF,MAAM,CAAC,MAAM,CAAA;QACf,CAAC;QAED,MAAM,CAAC,KAAK,CAAA;IACd,CAAC;IAKD,IAAI,SAAS,GAAG,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAc;QAAd,2BAAA,EAAA,cAAc;QACtE,IAAM,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE;YACtC,QAAQ,UAAA;YACR,eAAe,EAAE,MAAM,CAAC,OAAO;YAC/B,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,OAAO,CAAC,YAAY;SACnC,CAAC,CAAA;QAEF,IAAM,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;YACzC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC;YACvD,EAAE,CAAA;QAEJ,EAAE,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1B,MAAM,IAAI,OAAO,CAAC,iBAAiB,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC,CAAA;QAC3E,CAAC;QAED,MAAM,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,aAAuB,CAAC,CAAA;IAC5D,CAAC,CAAA;IAED,IAAI,OAAO,GAAG,WAAW,CACvB,QAAQ,EACR,WAAW,EACX,OAAO,EACP,KAAK,EACL,SAAS,EACT,YAAY,CACb,CAAA;IAED,IAAI,WAAW,GAAG,UAAU,KAAa,EAAE,SAAiB,EAAE,SAAiB;QAC7E,MAAM,IAAI,SAAS,CAAC,0DAAwD,CAAC,CAAA;IAC/E,CAAC,CAAA;IAGD,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAEd,IAAM,UAAQ,GAAG,UAAU,IAAY,EAAE,QAAgB;YACvD,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;gBACtC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;gBAC/B,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAChE,CAAC;QACH,CAAC,CAAA;QAGD,IAAM,WAAW,GAAG;YAClB,kBAAkB,EAAE,cAAM,OAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAA3B,CAA2B;YACrD,gBAAgB,EAAE,UAAC,QAAgB;gBACjC,IAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBAOxC,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,SAA0B,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC7E,CAAC;YACD,iBAAiB,YAAE,QAAgB;gBACjC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAC9B,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAC1B,MAAM,CAAC,SAAS,CAAA;oBAClB,CAAC;oBAED,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAA;gBAC9C,CAAC;gBAED,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA;YAC/D,CAAC;YACD,UAAU,EAAE,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;YAC7C,QAAQ,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;YACrC,aAAa,EAAE,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC;YAC7D,cAAc,EAAE,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC;YAChE,eAAe,EAAE,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC;YACnE,UAAU,EAAE,cAAM,OAAA,QAAG,EAAH,CAAG;YACrB,mBAAmB,EAAE,cAAM,OAAA,GAAG,EAAH,CAAG;YAC9B,sBAAsB,EAAE,cAAM,OAAA,MAAM,CAAC,OAAO,EAAd,CAAc;YAC5C,qBAAqB,EAAE,cAAM,OAAA,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAxC,CAAwC;YACrE,qBAAqB,EAAE,cAAM,OAAA,OAAO,CAAC,YAAY,EAApB,CAAoB;SAClD,CAAA;QAED,IAAM,SAAO,GAAG,EAAE,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;QAErD,SAAS,GAAG,UAAU,KAAa,EAAE,QAAgB,EAAE,UAAsB;YAAtB,2BAAA,EAAA,cAAsB;YAC3E,IAAM,MAAM,GAAG,SAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;YAG9C,IAAM,WAAW,GAAG,SAAO,CAAC,6BAA6B,EAAE;iBACxD,MAAM,CAAC,SAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;iBACjD,MAAM,CAAC,SAAO,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAA;YAEnD,IAAM,cAAc,GAAG,iBAAiB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;YAErE,EAAE,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC1B,MAAM,IAAI,OAAO,CAAC,iBAAiB,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC,CAAA;YAC3E,CAAC;YAED,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;gBACvB,MAAM,IAAI,SAAS,CAAI,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,mBAAgB,CAAC,CAAA;YACjE,CAAC;YAGD,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,IAAI,SAAS,CACjB,mCAAmC;oBACnC,kEAAkE;oBAClE,0EAA0E;oBAC1E,yDAAyD;qBACzD,MAAK,eAAQ,CAAC,QAAQ,CAAC,OAAK,CAAA,CAC7B,CAAA;YACH,CAAC;YAED,MAAM,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QACjE,CAAC,CAAA;QAED,OAAO,GAAG,WAAW,CACnB,QAAQ,EACR,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAmB;YAC3D,UAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAExB,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;QAC9C,CAAC,EACD,YAAY,CACb,CAAA;QAED,WAAW,GAAG,UAAU,IAAY,EAAE,QAAgB,EAAE,QAAgB;YACtE,UAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAExB,IAAM,IAAI,GAAG,SAAO,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YAC/D,IAAM,IAAI,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YACnE,IAAM,OAAO,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YAEvE,MAAM,CAAC,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,CAAA;QAC1B,CAAC,CAAA;IACH,CAAC;IAED,IAAM,QAAQ,GAAa,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,WAAW,aAAA,EAAE,UAAU,YAAA,EAAE,QAAQ,UAAA,EAAE,EAAE,IAAA,EAAE,CAAA;IAGlF,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;QAC1B,iBAAiB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAA;IACnE,CAAC,CAAC,CAAA;IAEF,MAAM,CAAC,QAAQ,CAAA;AACjB,CAAC;AAlOD,4BAkOC;AAKD,sBAAuB,QAAgB,EAAE,MAAgB;IACvD,IAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;IAE1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAf,CAAe,CAAC,CAAA;AAC1C,CAAC;AAKD,2BACE,GAAW,EACX,MAAgB,EAChB,QAAkB,EAClB,eAAyD;IAEzD,IAAM,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,eAAe,CAAA;IAEtD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAM,EAAE,QAAQ;QAClD,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;QACzB,CAAC;QAED,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;QAE3B,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAY,EAAE,QAAgB;YACnD,KAAK,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAA;YAElC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAA;QACxE,CAAC,CAAA;QAED,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;IACzB,CAAC,CAAA;AACH,CAAC;AAKD,mBAAoB,MAAW,EAAE,EAAY;IAE3C,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAA;IACzB,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAA;IAC7B,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAA;IAGpC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,CAAA;IAC7C,CAAC;IAGD,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAA;IAChD,CAAC;IAED,MAAM,CAAC,MAAM,CAAA;AACf,CAAC;AAKD,oBAAqB,eAAoB,EAAE,OAAqC,EAAE,GAAW,EAAE,EAAY;IACzG,IAAM,MAAM,GAAG,mBAAQ,CAAC,GAAG,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;IAG/E,MAAM,CAAC,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,eAAe,EAAE;QAChG,SAAS,EAAE,IAAI;QACf,eAAe,EAAE,KAAK;QACtB,aAAa,EAAE,IAAI;QACnB,WAAW,EAAE,KAAK;QAClB,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,aAAa;KACtB,CAAC,CAAA;IAEF,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC/D,IAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,cAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;IAEzE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,eAAe,KAAK,UAAU,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAA;IAC3E,CAAC;IAED,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,0BAA0B,KAAK,UAAU,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,0BAA0B,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC,CAAA;IAC7G,CAAC;IAED,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAA;AAC/E,CAAC;AAUD,qBACE,QAAgB,EAChB,WAAoB,EACpB,OAAqC,EACrC,KAAY,EACZ,OAA8E,EAC9E,YAA0C;IAE1C,EAAE,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAmB;YAClE,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;YAExB,IAAA,wCAAwD,EAAvD,aAAK,EAAE,iBAAS,CAAuC;YAC9D,IAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;YAErE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAA;YAEhC,MAAM,CAAC,MAAM,CAAA;QACf,CAAC,CAAA;IACH,CAAC;IAGD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAErB,MAAM,CAAC,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAmB;QAClE,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;QAE9B,IAAM,SAAS,GAAG,WAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC9D,IAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;QACxC,IAAM,UAAU,GAAG,KAAG,SAAS,GAAG,SAAW,CAAA;QAE7C,IAAI,CAAC;YACH,IAAM,QAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;YAClC,EAAE,CAAC,CAAC,mBAAmB,CAAC,QAAM,CAAC,CAAC,CAAC,CAAC;gBAChC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAM,CAAA;gBAChC,MAAM,CAAC,QAAM,CAAA;YACf,CAAC;QACH,CAAC;QAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAa,CAAC;QAEvB,IAAA,wCAAwD,EAAvD,aAAK,EAAE,iBAAS,CAAuC;QAC9D,IAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;QAErE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAA;QAChC,kBAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;QAEjC,MAAM,CAAC,MAAM,CAAA;IACf,CAAC,CAAA;AACH,CAAC;AAKD,sBAAuB,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,YAA0C;IACxH,IAAM,SAAS,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IAC7F,IAAM,gBAAgB,GAAG,gDAA8C,SAAW,CAAA;IAClF,IAAM,eAAe,GAAG,CAAG,eAAQ,CAAC,QAAQ,CAAC,SAAM,CAAA,CAAC,MAAM,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,cAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAA;IAEvH,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,GAAG,gBAAgB,CAAA;AACrE,CAAC;AAKD,yBAA0B,aAAqB,EAAE,QAAgB;IAC/D,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;IAC3C,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAA;IACzB,SAAS,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC9B,OAAO,SAAS,CAAC,UAAU,CAAA;IAC3B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;AAClC,CAAC;AAKD,sBAAuB,UAAkB,EAAE,QAAgB;IACzD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;SAC/B,MAAM,CAAC,cAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;SACjC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC;SAC3B,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC;SAC1B,MAAM,CAAC,KAAK,CAAC,CAAA;AAClB,CAAC;AAMD,6BAA8B,OAAe;IAC3C,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAChD,CAAC;AAKD,2BAA4B,IAAS;IACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AACvF,CAAC;AAKD,oBAA4B,QAAgB;IAC1C,IAAI,CAAC;QACH,IAAM,KAAK,GAAG,aAAQ,CAAC,QAAQ,CAAC,CAAA;QAEhC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE,CAAA;IACzC,CAAC;IAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACb,MAAM,CAAC,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AARD,gCAQC;AAKD,iBAAyB,QAAgB;IACvC,MAAM,CAAC,iBAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;AACvC,CAAC;AAFD,0BAEC;AAKD,2BAA4B,WAA4B,EAAE,MAAgB;IACxE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAA7B,CAA6B,CAAC,CAAA;AAC/D,CAAC;AAKD,2BAAmC,WAA4B,EAAE,GAAW,EAAE,EAAY,EAAE,UAAkB;IAC5G,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,gBAAgB,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,CAAC,EAAxC,CAAwC,CAAC,CAAA;AACvE,CAAC;AAFD,8CAEC;AAaD,0BACE,UAAyB,EACzB,GAAW,EACX,EAAY,EACZ,UAAkB;IAElB,IAAM,WAAW,GAAG,EAAE,CAAC,4BAA4B,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;IACzE,IAAA,sBAAI,CAAe;IAE3B,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QACpB,IAAM,IAAI,GAAG,eAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAEpD,EAAE,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YACf,IAAA,oEAAqF,EAAnF,cAAI,EAAE,wBAAS,CAAoE;YAC3F,IAAM,OAAO,GAAM,IAAI,WAAK,IAAI,GAAG,CAAC,GAAG,UAAU,WAAI,SAAS,GAAG,CAAC,YAAM,WAAW,UAAK,IAAI,MAAG,CAAA;YAE/F,MAAM,CAAC,EAAE,OAAO,SAAA,EAAE,IAAI,MAAA,EAAE,CAAA;QAC1B,CAAC;QAED,MAAM,CAAC,EAAE,OAAO,EAAK,IAAI,UAAK,WAAW,UAAK,IAAI,MAAG,EAAE,IAAI,MAAA,EAAE,CAAA;IAC/D,CAAC;IAED,MAAM,CAAC,EAAE,OAAO,EAAK,WAAW,UAAK,IAAI,MAAG,EAAE,IAAI,MAAA,EAAE,CAAA;AACtD,CAAC;AAvBD,4CAuBC;AAKD,oBAA4B,KAAc;IACxC,IAAM,KAAK,GAAM,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,kCAA+B,CAAA;IAE9D,MAAM,CAAI,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,UAAK,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,EAAT,CAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAG,CAAA;AACpF,CAAC;AAJD,gCAIC", "sourcesContent": ["import { relative, basename, extname, resolve, dirname, join } from 'path'\nimport { writeFileSync, readFileSync, statSync } from 'fs'\nimport { EOL, tmpdir, homedir } from 'os'\nimport sourceMapSupport = require('source-map-support')\nimport chalk from 'chalk'\nimport mkdirp = require('mkdirp')\nimport crypto = require('crypto')\nimport yn = require('yn')\nimport arrify = require('arrify')\nimport { BaseError } from 'make-error'\nimport * as TS from 'typescript'\nimport { loadSync } from 'tsconfig'\n\nconst pkg = require('../package.json')\nconst shouldDebug = yn(process.env.TS_NODE_DEBUG)\nconst debug = shouldDebug ? console.log.bind(console, 'ts-node') : () => undefined\nconst debugFn = shouldDebug ?\n  <T, U> (key: string, fn: (arg: T) => U) => {\n    return (x: T) => {\n      debug(key, x)\n      return fn(x)\n    }\n  } :\n  <T, U> (_: string, fn: (arg: T) => U) => fn\n\n/**\n * Common TypeScript interfaces between versions.\n */\nexport interface TSCommon {\n  version: typeof TS.version\n  sys: typeof TS.sys\n  ScriptSnapshot: typeof TS.ScriptSnapshot\n  displayPartsToString: typeof TS.displayPartsToString\n  createLanguageService: typeof TS.createLanguageService\n  getDefaultLibFilePath: typeof TS.getDefaultLibFilePath\n  getPreEmitDiagnostics: typeof TS.getPreEmitDiagnostics\n  flattenDiagnosticMessageText: typeof TS.flattenDiagnosticMessageText\n  transpileModule: typeof TS.transpileModule\n  ModuleKind: typeof TS.ModuleKind\n  ScriptTarget: typeof TS.ScriptTarget\n  findConfigFile: typeof TS.findConfigFile\n  readConfigFile: typeof TS.readConfigFile\n  parseJsonConfigFileContent: typeof TS.parseJsonConfigFileContent\n\n  // TypeScript 1.5 and 1.6.\n  parseConfigFile? (json: any, host: any, basePath: string): any\n}\n\n/**\n * Export the current version.\n */\nexport const VERSION = pkg.version\n\n/**\n * Registration options.\n */\nexport interface Options {\n  typeCheck?: boolean\n  cache?: boolean\n  cacheDirectory?: string\n  compiler?: string\n  project?: boolean | string\n  ignore?: boolean | string | string[]\n  ignoreWarnings?: number | string | Array<number | string>\n  getFile?: (path: string) => string\n  fileExists?: (path: string) => boolean\n  compilerOptions?: any\n  transformers?: TS.CustomTransformers\n}\n\n/**\n * Track the project information.\n */\ninterface Cache {\n  contents: { [path: string]: string }\n  versions: { [path: string]: number | undefined }\n  outputs: { [path: string]: string }\n}\n\n/**\n * Information retrieved from type info check.\n */\nexport interface TypeInfo {\n  name: string\n  comment: string\n}\n\n/**\n * Default register options.\n */\nconst DEFAULTS = {\n  getFile,\n  fileExists,\n  cache: yn(process.env['TS_NODE_CACHE'], { default: true }),\n  cacheDirectory: process.env['TS_NODE_CACHE_DIRECTORY'],\n  compiler: process.env['TS_NODE_COMPILER'],\n  compilerOptions: parse(process.env['TS_NODE_COMPILER_OPTIONS']),\n  project: process.env['TS_NODE_PROJECT'],\n  ignore: split(process.env['TS_NODE_IGNORE']),\n  ignoreWarnings: split(process.env['TS_NODE_IGNORE_WARNINGS']),\n  typeCheck: yn(process.env['TS_NODE_TYPE_CHECK'])\n}\n\n/**\n * Split a string array of values.\n */\nexport function split (value: string | undefined) {\n  return value ? value.split(/ *, */g) : undefined\n}\n\n/**\n * Parse a string as JSON.\n */\nexport function parse (value: string | undefined) {\n  return value ? JSON.parse(value) : undefined\n}\n\n/**\n * Replace backslashes with forward slashes.\n */\nexport function normalizeSlashes (value: string): string {\n  return value.replace(/\\\\/g, '/')\n}\n\n/**\n * TypeScript diagnostics error.\n */\nexport class TSError extends BaseError {\n\n  name = 'TSError'\n\n  constructor (public diagnostics: TSDiagnostic[]) {\n    super(\n      `⨯ Unable to compile TypeScript\\n${diagnostics.map(x => x.message).join('\\n')}`\n    )\n  }\n\n}\n\n/**\n * Return type for registering `ts-node`.\n */\nexport interface Register {\n  cwd: string\n  extensions: string[]\n  cachedir: string\n  ts: TSCommon\n  compile (code: string, fileName: string, lineOffset?: number): string\n  getTypeInfo (code: string, fileName: string, position: number): TypeInfo\n}\n\n/**\n * Return a default temp directory based on home directory of user.\n */\nfunction getTmpDir (): string {\n  const hash = crypto.createHash('sha256').update(homedir(), 'utf8').digest('hex')\n\n  return join(tmpdir(), `ts-node-${hash}`)\n}\n\n/**\n * Register TypeScript compiler.\n */\nexport function register (options: Options = {}): Register {\n  const compiler = options.compiler || 'typescript'\n  const emptyFileListWarnings = [18002, 18003]\n  const ignoreWarnings = arrify(\n    options.ignoreWarnings || DEFAULTS.ignoreWarnings || []\n  ).concat(emptyFileListWarnings).map(Number)\n  const getFile = options.getFile || DEFAULTS.getFile\n  const fileExists = options.fileExists || DEFAULTS.fileExists\n  const shouldCache = !!(options.cache === undefined ? DEFAULTS.cache : options.cache)\n  const typeCheck = !!(options.typeCheck === undefined ? DEFAULTS.typeCheck : options.typeCheck)\n  const project = options.project === undefined ? DEFAULTS.project : options.project\n  const cacheDirectory = options.cacheDirectory || DEFAULTS.cacheDirectory || getTmpDir()\n  const compilerOptions = Object.assign({}, DEFAULTS.compilerOptions, options.compilerOptions)\n  const originalJsHandler = require.extensions['.js']\n\n  const cache: Cache = {\n    contents: Object.create(null),\n    versions: Object.create(null),\n    outputs: Object.create(null)\n  }\n\n  const ignore = arrify(\n    (\n      typeof options.ignore === 'boolean' ?\n        (options.ignore === false ? [] : undefined) :\n        (options.ignore || DEFAULTS.ignore)\n    ) ||\n    ['/node_modules/']\n  ).map(str => new RegExp(str))\n\n  // Install source map support and read from cache.\n  sourceMapSupport.install({\n    environment: 'node',\n    retrieveFile (path: string) {\n      return cache.outputs[path]\n    }\n  })\n\n  // Require the TypeScript compiler and configuration.\n  const cwd = process.cwd()\n  const ts: typeof TS = require(compiler)\n  const config = readConfig(compilerOptions, project, cwd, ts)\n  const configDiagnostics = filterDiagnostics(config.errors, ignoreWarnings)\n  const extensions = ['.ts', '.tsx']\n\n  const cachedir = join(\n    resolve(cwd, cacheDirectory),\n    getCompilerDigest({ version: ts.version, typeCheck, ignoreWarnings, config, compiler })\n  )\n\n  // Render the configuration errors and exit the script.\n  if (configDiagnostics.length) {\n    throw new TSError(formatDiagnostics(configDiagnostics, cwd, ts, 0))\n  }\n\n  // Enable `allowJs` when flag is set.\n  if (config.options.allowJs) {\n    extensions.push('.js')\n    extensions.push('.jsx')\n  }\n\n  // Add all files into the file hash.\n  for (const fileName of config.fileNames) {\n    cache.versions[fileName] = 1\n  }\n\n  /**\n   * Get the extension for a transpiled file.\n   */\n  function getExtension (fileName: string) {\n    const ext = extname(fileName)\n\n    if (config.options.jsx === ts.JsxEmit.Preserve && (ext === '.tsx' || ext === '.jsx')) {\n      return '.jsx'\n    }\n\n    return '.js'\n  }\n\n  /**\n   * Create the basic required function using transpile mode.\n   */\n  let getOutput = function (code: string, fileName: string, lineOffset = 0): SourceOutput {\n    const result = ts.transpileModule(code, {\n      fileName,\n      compilerOptions: config.options,\n      reportDiagnostics: true,\n      transformers: options.transformers\n    })\n\n    const diagnosticList = result.diagnostics ?\n      filterDiagnostics(result.diagnostics, ignoreWarnings) :\n      []\n\n    if (diagnosticList.length) {\n      throw new TSError(formatDiagnostics(diagnosticList, cwd, ts, lineOffset))\n    }\n\n    return [result.outputText, result.sourceMapText as string]\n  }\n\n  let compile = readThrough(\n    cachedir,\n    shouldCache,\n    getFile,\n    cache,\n    getOutput,\n    getExtension\n  )\n\n  let getTypeInfo = function (_code: string, _fileName: string, _position: number): TypeInfo {\n    throw new TypeError(`Type information is unavailable without \"--type-check\"`)\n  }\n\n  // Use full language services when the fast option is disabled.\n  if (typeCheck) {\n    // Set the file contents into cache.\n    const setCache = function (code: string, fileName: string) {\n      if (cache.contents[fileName] !== code) {\n        cache.contents[fileName] = code\n        cache.versions[fileName] = (cache.versions[fileName] || 0) + 1\n      }\n    }\n\n    // Create the compiler host for type checking.\n    const serviceHost = {\n      getScriptFileNames: () => Object.keys(cache.versions),\n      getScriptVersion: (fileName: string) => {\n        const version = cache.versions[fileName]\n\n        // We need to return `undefined` and not a string here because TypeScript will use\n        // `getScriptVersion` and compare against their own version - which can be `undefined`.\n        // If we don't return `undefined` it results in `undefined === \"undefined\"` and run\n        // `createProgram` again (which is very slow). Using a `string` assertion here to avoid\n        // TypeScript errors from the function signature (expects `(x: string) => string`).\n        return version === undefined ? undefined as any as string : String(version)\n      },\n      getScriptSnapshot (fileName: string) {\n        if (!cache.contents[fileName]) {\n          if (!fileExists(fileName)) {\n            return undefined\n          }\n\n          cache.contents[fileName] = getFile(fileName)\n        }\n\n        return ts.ScriptSnapshot.fromString(cache.contents[fileName])\n      },\n      fileExists: debugFn('fileExists', fileExists),\n      readFile: debugFn('getFile', getFile),\n      readDirectory: debugFn('readDirectory', ts.sys.readDirectory),\n      getDirectories: debugFn('getDirectories', ts.sys.getDirectories),\n      directoryExists: debugFn('directoryExists', ts.sys.directoryExists),\n      getNewLine: () => EOL,\n      getCurrentDirectory: () => cwd,\n      getCompilationSettings: () => config.options,\n      getDefaultLibFileName: () => ts.getDefaultLibFilePath(config.options),\n      getCustomTransformers: () => options.transformers\n    }\n\n    const service = ts.createLanguageService(serviceHost)\n\n    getOutput = function (_code: string, fileName: string, lineOffset: number = 0) {\n      const output = service.getEmitOutput(fileName)\n\n      // Get the relevant diagnostics - this is 3x faster than `getPreEmitDiagnostics`.\n      const diagnostics = service.getCompilerOptionsDiagnostics()\n        .concat(service.getSyntacticDiagnostics(fileName))\n        .concat(service.getSemanticDiagnostics(fileName))\n\n      const diagnosticList = filterDiagnostics(diagnostics, ignoreWarnings)\n\n      if (diagnosticList.length) {\n        throw new TSError(formatDiagnostics(diagnosticList, cwd, ts, lineOffset))\n      }\n\n      if (output.emitSkipped) {\n        throw new TypeError(`${relative(cwd, fileName)}: Emit skipped`)\n      }\n\n      // Throw an error when requiring `.d.ts` files.\n      if (output.outputFiles.length === 0) {\n        throw new TypeError(\n          'Unable to require `.d.ts` file.\\n' +\n          'This is usually the result of a faulty configuration or import. ' +\n          'Make sure there is a `.js`, `.json` or another executable extension and ' +\n          'loader (attached before `ts-node`) available alongside ' +\n          `\\`${basename(fileName)}\\`.`\n        )\n      }\n\n      return [output.outputFiles[1].text, output.outputFiles[0].text]\n    }\n\n    compile = readThrough(\n      cachedir,\n      shouldCache,\n      getFile,\n      cache,\n      function (code: string, fileName: string, lineOffset?: number) {\n        setCache(code, fileName)\n\n        return getOutput(code, fileName, lineOffset)\n      },\n      getExtension\n    )\n\n    getTypeInfo = function (code: string, fileName: string, position: number) {\n      setCache(code, fileName)\n\n      const info = service.getQuickInfoAtPosition(fileName, position)\n      const name = ts.displayPartsToString(info ? info.displayParts : [])\n      const comment = ts.displayPartsToString(info ? info.documentation : [])\n\n      return { name, comment }\n    }\n  }\n\n  const register: Register = { cwd, compile, getTypeInfo, extensions, cachedir, ts }\n\n  // Register the extensions.\n  extensions.forEach(extension => {\n    registerExtension(extension, ignore, register, originalJsHandler)\n  })\n\n  return register\n}\n\n/**\n * Check if the filename should be ignored.\n */\nfunction shouldIgnore (filename: string, ignore: RegExp[]) {\n  const relname = normalizeSlashes(filename)\n\n  return ignore.some(x => x.test(relname))\n}\n\n/**\n * Register the extension for node.\n */\nfunction registerExtension (\n  ext: string,\n  ignore: RegExp[],\n  register: Register,\n  originalHandler: (m: NodeModule, filename: string) => any\n) {\n  const old = require.extensions[ext] || originalHandler\n\n  require.extensions[ext] = function (m: any, filename) {\n    if (shouldIgnore(filename, ignore)) {\n      return old(m, filename)\n    }\n\n    const _compile = m._compile\n\n    m._compile = function (code: string, fileName: string) {\n      debug('module._compile', fileName)\n\n      return _compile.call(this, register.compile(code, fileName), fileName)\n    }\n\n    return old(m, filename)\n  }\n}\n\n/**\n * Do post-processing on config options to correct them.\n */\nfunction fixConfig (config: any, ts: TSCommon) {\n  // Delete options that *should not* be passed through.\n  delete config.options.out\n  delete config.options.outFile\n  delete config.options.declarationDir\n\n  // Target ES5 output by default (instead of ES3).\n  if (config.options.target === undefined) {\n    config.options.target = ts.ScriptTarget.ES5\n  }\n\n  // Target CommonJS modules by default (instead of magically switching to ES6 when the target is ES6).\n  if (config.options.module === undefined) {\n    config.options.module = ts.ModuleKind.CommonJS\n  }\n\n  return config\n}\n\n/**\n * Load TypeScript configuration.\n */\nfunction readConfig (compilerOptions: any, project: string | boolean | undefined, cwd: string, ts: TSCommon) {\n  const result = loadSync(cwd, typeof project === 'string' ? project : undefined)\n\n  // Override default configuration options.\n  result.config.compilerOptions = Object.assign({}, result.config.compilerOptions, compilerOptions, {\n    sourceMap: true,\n    inlineSourceMap: false,\n    inlineSources: true,\n    declaration: false,\n    noEmit: false,\n    outDir: '$$ts-node$$'\n  })\n\n  const configPath = result.path && normalizeSlashes(result.path)\n  const basePath = configPath ? dirname(configPath) : normalizeSlashes(cwd)\n\n  if (typeof ts.parseConfigFile === 'function') {\n    return fixConfig(ts.parseConfigFile(result.config, ts.sys, basePath), ts)\n  }\n\n  if (typeof ts.parseJsonConfigFileContent === 'function') {\n    return fixConfig(ts.parseJsonConfigFileContent(result.config, ts.sys, basePath, undefined, configPath), ts)\n  }\n\n  throw new TypeError('Could not find a compatible `parseConfigFile` function')\n}\n\n/**\n * Internal source output.\n */\ntype SourceOutput = [string, string]\n\n/**\n * Wrap the function with caching.\n */\nfunction readThrough (\n  cachedir: string,\n  shouldCache: boolean,\n  getFile: (fileName: string) => string,\n  cache: Cache,\n  compile: (code: string, fileName: string, lineOffset?: number) => SourceOutput,\n  getExtension: (fileName: string) => string\n) {\n  if (shouldCache === false) {\n    return function (code: string, fileName: string, lineOffset?: number) {\n      debug('readThrough', fileName)\n\n      const [value, sourceMap] = compile(code, fileName, lineOffset)\n      const output = updateOutput(value, fileName, sourceMap, getExtension)\n\n      cache.outputs[fileName] = output\n\n      return output\n    }\n  }\n\n  // Make sure the cache directory exists before continuing.\n  mkdirp.sync(cachedir)\n\n  return function (code: string, fileName: string, lineOffset?: number) {\n    debug('readThrough', fileName)\n\n    const cachePath = join(cachedir, getCacheName(code, fileName))\n    const extension = getExtension(fileName)\n    const outputPath = `${cachePath}${extension}`\n\n    try {\n      const output = getFile(outputPath)\n      if (isValidCacheContent(output)) {\n        cache.outputs[fileName] = output\n        return output\n      }\n    } catch (err) {/* Ignore. */}\n\n    const [value, sourceMap] = compile(code, fileName, lineOffset)\n    const output = updateOutput(value, fileName, sourceMap, getExtension)\n\n    cache.outputs[fileName] = output\n    writeFileSync(outputPath, output)\n\n    return output\n  }\n}\n\n/**\n * Update the output remapping the source map.\n */\nfunction updateOutput (outputText: string, fileName: string, sourceMap: string, getExtension: (fileName: string) => string) {\n  const base64Map = new Buffer(updateSourceMap(sourceMap, fileName), 'utf8').toString('base64')\n  const sourceMapContent = `data:application/json;charset=utf-8;base64,${base64Map}`\n  const sourceMapLength = `${basename(fileName)}.map`.length + (getExtension(fileName).length - extname(fileName).length)\n\n  return outputText.slice(0, -1 * sourceMapLength) + sourceMapContent\n}\n\n/**\n * Update the source map contents for improved output.\n */\nfunction updateSourceMap (sourceMapText: string, fileName: string) {\n  const sourceMap = JSON.parse(sourceMapText)\n  sourceMap.file = fileName\n  sourceMap.sources = [fileName]\n  delete sourceMap.sourceRoot\n  return JSON.stringify(sourceMap)\n}\n\n/**\n * Get the file name for the cache entry.\n */\nfunction getCacheName (sourceCode: string, fileName: string) {\n  return crypto.createHash('sha256')\n    .update(extname(fileName), 'utf8')\n    .update('\\x001\\x00', 'utf8') // Store \"cache version\" in hash.\n    .update(sourceCode, 'utf8')\n    .digest('hex')\n}\n\n/**\n * Ensure the given cached content is valid by sniffing for a base64 encoded '}'\n * at the end of the content, which should exist if there is a valid sourceMap present.\n */\nfunction isValidCacheContent (content: string) {\n  return /(?:9|0=|Q==)$/.test(content.slice(-3))\n}\n\n/**\n * Create a hash of the current configuration.\n */\nfunction getCompilerDigest (opts: any) {\n  return crypto.createHash('sha256').update(JSON.stringify(opts), 'utf8').digest('hex')\n}\n\n/**\n * Check if the file exists.\n */\nexport function fileExists (fileName: string): boolean {\n  try {\n    const stats = statSync(fileName)\n\n    return stats.isFile() || stats.isFIFO()\n  } catch (err) {\n    return false\n  }\n}\n\n/**\n * Get the file from the file system.\n */\nexport function getFile (fileName: string): string {\n  return readFileSync(fileName, 'utf8')\n}\n\n/**\n * Filter diagnostics.\n */\nfunction filterDiagnostics (diagnostics: TS.Diagnostic[], ignore: number[]) {\n  return diagnostics.filter(x => ignore.indexOf(x.code) === -1)\n}\n\n/**\n * Format an array of diagnostics.\n */\nexport function formatDiagnostics (diagnostics: TS.Diagnostic[], cwd: string, ts: TSCommon, lineOffset: number) {\n  return diagnostics.map(x => formatDiagnostic(x, cwd, ts, lineOffset))\n}\n\n/**\n * Internal diagnostic representation.\n */\nexport interface TSDiagnostic {\n  message: string\n  code: number\n}\n\n/**\n * Format a diagnostic object into a string.\n */\nexport function formatDiagnostic (\n  diagnostic: TS.Diagnostic,\n  cwd: string,\n  ts: TSCommon,\n  lineOffset: number\n): TSDiagnostic {\n  const messageText = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\\n')\n  const { code } = diagnostic\n\n  if (diagnostic.file) {\n    const path = relative(cwd, diagnostic.file.fileName)\n\n    if (diagnostic.start) {\n      const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start)\n      const message = `${path} (${line + 1 + lineOffset},${character + 1}): ${messageText} (${code})`\n\n      return { message, code }\n    }\n\n    return { message: `${path}: ${messageText} (${code})`, code }\n  }\n\n  return { message: `${messageText} (${code})`, code }\n}\n\n/**\n * Stringify the `TSError` instance.\n */\nexport function printError (error: TSError) {\n  const title = `${chalk.red('⨯')} Unable to compile TypeScript`\n\n  return `${chalk.bold(title)}\\n${error.diagnostics.map(x => x.message).join('\\n')}`\n}\n"]}