{"version": 3, "file": "cdk-table.umd.min.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../src/cdk/table/table-errors.ts", "../../src/cdk/table/row.ts", "../../src/cdk/table/cell.ts", "../../src/cdk/table/table.ts", "../../src/cdk/table/table-module.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = Object.setPrototypeOf ||\r\n    ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n    function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = Object.assign || function __assign(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n    }\r\n    return t;\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = y[op[0] & 2 ? \"return\" : op[0] ? \"throw\" : \"next\"]) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [0, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { if (o[n]) i[n] = function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; }; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator];\r\n    return m ? m.call(o) : typeof __values === \"function\" ? __values(o) : o[Symbol.iterator]();\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an error to be thrown when attempting to find an unexisting column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nexport function getTableUnknownColumnError(id: string) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nexport function getTableDuplicateColumnNameError(name: string) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nexport function getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nexport function getTableMissingMatchingRowDefError() {\n  return Error(`Could not find a matching row definition for the provided row data.`);\n}\n\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nexport function getTableMissingRowDefsError() {\n  return Error('Missing definitions for header and row, ' +\n      'cannot determine which columns should be rendered.');\n}\n\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nexport function getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  IterableChanges,\n  IterableDiffer,\n  IterableDiffers,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {CdkCellDef} from './cell';\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nexport const CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nexport abstract class BaseRowDef {\n  /** The columns to be displayed on this row. */\n  columns: string[];\n\n  /** Differ used to check if any changes were made to the columns. */\n  protected _columnsDiffer: IterableDiffer<any>;\n\n  constructor(/** @docs-private */ public template: TemplateRef<any>,\n              protected _differs: IterableDiffers) { }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    const columns = changes['columns'].currentValue || [];\n    if (!this._columnsDiffer) {\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff(): IterableChanges<any> | null {\n    return this._columnsDiffer.diff(this.columns);\n  }\n}\n\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\n@Directive({\n  selector: '[cdkHeaderRowDef]',\n  inputs: ['columns: cdkHeaderRowDef'],\n})\nexport class CdkHeaderRowDef extends BaseRowDef {\n  constructor(template: TemplateRef<any>, _differs: IterableDiffers) {\n    super(template, _differs);\n  }\n}\n\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\n@Directive({\n  selector: '[cdkRowDef]',\n  inputs: ['columns: cdkRowDefColumns', 'when: cdkRowDefWhen'],\n})\nexport class CdkRowDef<T> extends BaseRowDef {\n  /**\n   * Function that should return true if this row template should be used for the provided index\n   * and row data. If left undefined, this row will be considered the default row template to use\n   * when no other when functions return true for the data.\n   * For every row, there must be at least one when function that passes or an undefined to default.\n   */\n  when: (index: number, rowData: T) => boolean;\n\n  // TODO(andrewseguin): Add an input for providing a switch function to determine\n  //   if this template should be used.\n  constructor(template: TemplateRef<any>, _differs: IterableDiffers) {\n    super(template, _differs);\n  }\n}\n\n/** Context provided to the row cells */\nexport interface CdkCellOutletRowContext<T> {\n  /** Data for the row that this cell is located within. */\n  $implicit: T;\n\n  /** Index location of the row that this cell is located within. */\n  index?: number;\n\n  /** Length of the number of total rows. */\n  count?: number;\n\n  /** True if this cell is contained in the first row. */\n  first?: boolean;\n\n  /** True if this cell is contained in the last row. */\n  last?: boolean;\n\n  /** True if this cell is contained in a row with an even-numbered index. */\n  even?: boolean;\n\n  /** True if this cell is contained in a row with an odd-numbered index. */\n  odd?: boolean;\n}\n\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\n@Directive({selector: '[cdkCellOutlet]'})\nexport class CdkCellOutlet {\n  /** The ordered list of cells to render within this outlet's view container */\n  cells: CdkCellDef[];\n\n  /** The data context to be provided to each cell */\n  context: any;\n\n  /**\n   * Static property containing the latest constructed instance of this class.\n   * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n   * createEmbeddedView. After one of these components are created, this property will provide\n   * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n   * construct the cells with the provided context.\n   */\n  static mostRecentCellOutlet: CdkCellOutlet | null = null;\n\n  constructor(public _viewContainer: ViewContainerRef) {\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n}\n\n/** Header template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-header-row',\n  template: CDK_ROW_TEMPLATE,\n  host: {\n    'class': 'cdk-header-row',\n    'role': 'row',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n})\nexport class CdkHeaderRow { }\n\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-row',\n  template: CDK_ROW_TEMPLATE,\n  host: {\n    'class': 'cdk-row',\n    'role': 'row',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n})\nexport class CdkRow { }\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentChild, Directive, ElementRef, Input, TemplateRef} from '@angular/core';\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\n@Directive({selector: '[cdkCellDef]'})\nexport class CdkCellDef {\n  constructor(/** @docs-private */ public template: TemplateRef<any>) { }\n}\n\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\n@Directive({selector: '[cdkHeaderCellDef]'})\nexport class CdkHeaderCellDef {\n  constructor(/** @docs-private */ public template: TemplateRef<any>) { }\n}\n\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\n@Directive({selector: '[cdkColumnDef]'})\nexport class CdkColumnDef {\n  /** Unique name for this column. */\n  @Input('cdkColumnDef')\n  get name(): string { return this._name; }\n  set name(name: string) {\n    // If the directive is set without a name (updated programatically), then this setter will\n    // trigger with an empty string and should not overwrite the programatically set value.\n    if (!name) { return; }\n\n    this._name = name;\n    this.cssClassFriendlyName = name.replace(/[^a-z0-9_-]/ig, '-');\n  }\n  _name: string;\n\n  /** @docs-private */\n  @ContentChild(CdkCellDef) cell: CdkCellDef;\n\n  /** @docs-private */\n  @ContentChild(CdkHeaderCellDef) headerCell: CdkHeaderCellDef;\n\n  /**\n   * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n   * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n   * do not match are replaced by the '-' character.\n   */\n  cssClassFriendlyName: string;\n}\n\n/** Header cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'cdk-header-cell',\n  host: {\n    'class': 'cdk-header-cell',\n    'role': 'columnheader',\n  },\n})\nexport class CdkHeaderCell {\n  constructor(columnDef: CdkColumnDef, elementRef: ElementRef) {\n    elementRef.nativeElement.classList.add(`cdk-column-${columnDef.cssClassFriendlyName}`);\n  }\n}\n\n/** Cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'cdk-cell',\n  host: {\n    'class': 'cdk-cell',\n    'role': 'gridcell',\n  },\n})\nexport class CdkCell {\n  constructor(columnDef: CdkColumnDef, elementRef: ElementRef) {\n    elementRef.nativeElement.classList.add(`cdk-column-${columnDef.cssClassFriendlyName}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterContentChecked,\n  Attribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EmbeddedViewRef,\n  Input,\n  isDevMode,\n  IterableChangeRecord,\n  IterableDiffer,\n  IterableDiffers,\n  OnInit,\n  QueryList,\n  TrackByFunction,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {CollectionViewer, DataSource} from '@angular/cdk/collections';\nimport {CdkCellOutlet, CdkCellOutletRowContext, CdkHeaderRowDef, CdkRowDef} from './row';\nimport {takeUntil} from 'rxjs/operators/takeUntil';\nimport {BehaviorSubject} from 'rxjs/BehaviorSubject';\nimport {Subscription} from 'rxjs/Subscription';\nimport {Subject} from 'rxjs/Subject';\nimport {CdkCellDef, CdkColumnDef, CdkHeaderCellDef} from './cell';\nimport {\n  getTableDuplicateColumnNameError,\n  getTableMissingMatchingRowDefError,\n  getTableMissingRowDefsError,\n  getTableMultipleDefaultRowDefsError,\n  getTableUnknownColumnError,\n  getTableUnknownDataSourceError\n} from './table-errors';\nimport {Observable} from 'rxjs/Observable';\nimport {of as observableOf} from 'rxjs/observable/of';\n\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\n@Directive({selector: '[rowPlaceholder]'})\nexport class RowPlaceholder {\n  constructor(public viewContainer: ViewContainerRef) { }\n}\n\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\n@Directive({selector: '[headerRowPlaceholder]'})\nexport class HeaderRowPlaceholder {\n  constructor(public viewContainer: ViewContainerRef) { }\n}\n\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nexport const CDK_TABLE_TEMPLATE = `\n  <ng-container headerRowPlaceholder></ng-container>\n  <ng-container rowPlaceholder></ng-container>`;\n\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nabstract class RowViewRef<T> extends EmbeddedViewRef<CdkCellOutletRowContext<T>> { }\n\n/**\n * A data table that renders a header row and data rows. Uses the dataSource input to determine\n * the data to be rendered. The data can be provided either as a data array, an Observable stream\n * that emits the data array to render, or a DataSource with a connect function that will\n * return an Observable stream that emits the data array to render.\n */\n@Component({\n  moduleId: module.id,\n  selector: 'cdk-table',\n  exportAs: 'cdkTable',\n  template: CDK_TABLE_TEMPLATE,\n  host: {\n    'class': 'cdk-table',\n  },\n  encapsulation: ViewEncapsulation.None,\n  preserveWhitespaces: false,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class CdkTable<T> implements CollectionViewer, OnInit, AfterContentChecked {\n  /** Subject that emits when the component has been destroyed. */\n  private _onDestroy = new Subject<void>();\n\n  /** Latest data provided by the data source. */\n  private _data: T[];\n\n  /** Subscription that listens for the data provided by the data source. */\n  private _renderChangeSubscription: Subscription | null;\n\n  /**\n   * Map of all the user's defined columns (header and data cell template) identified by name.\n   * Collection populated by the column definitions gathered by `ContentChildren` as well as any\n   * custom column definitions added to `_customColumnDefs`.\n   */\n  private _columnDefsByName = new Map<string,  CdkColumnDef>();\n\n  /**\n   * Set of all row defitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n   */\n  private _rowDefs: CdkRowDef<T>[];\n\n  /** Differ used to find the changes in the data provided by the data source. */\n  private _dataDiffer: IterableDiffer<T>;\n\n  /** Stores the row definition that does not have a when predicate. */\n  private _defaultRowDef: CdkRowDef<T> | null;\n\n  /** Column definitions that were defined outside of the direct content children of the table. */\n  private _customColumnDefs = new Set<CdkColumnDef>();\n\n  /** Row definitions that were defined outside of the direct content children of the table. */\n  private _customRowDefs = new Set<CdkRowDef<T>>();\n\n  /**\n   * Whether the header row definition has been changed. Triggers an update to the header row after\n   * content is checked.\n   */\n  private _headerRowDefChanged = false;\n\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  @Input()\n  get trackBy(): TrackByFunction<T> { return this._trackByFn; }\n  set trackBy(fn: TrackByFunction<T>) {\n    if (isDevMode() &&\n        fn != null && typeof fn !== 'function' &&\n        <any>console && <any>console.warn) {\n        console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  private _trackByFn: TrackByFunction<T>;\n\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  @Input()\n  get dataSource(): DataSource<T> | Observable<T[]> | T[] { return this._dataSource; }\n  set dataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  private _dataSource: DataSource<T> | Observable<T[]> | T[] | T[];\n\n  // TODO(andrewseguin): Remove max value as the end index\n  //   and instead calculate the view on init and scroll.\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   */\n  viewChange: BehaviorSubject<{start: number, end: number}> =\n      new BehaviorSubject<{start: number, end: number}>({start: 0, end: Number.MAX_VALUE});\n\n  // Placeholders within the table's template where the header and data rows will be inserted.\n  @ViewChild(RowPlaceholder) _rowPlaceholder: RowPlaceholder;\n  @ViewChild(HeaderRowPlaceholder) _headerRowPlaceholder: HeaderRowPlaceholder;\n\n  /**\n   * The column definitions provided by the user that contain what the header and cells should\n   * render for each column.\n   */\n  @ContentChildren(CdkColumnDef) _contentColumnDefs: QueryList<CdkColumnDef>;\n\n  /** Set of template definitions that used as the data row containers. */\n  @ContentChildren(CdkRowDef) _contentRowDefs: QueryList<CdkRowDef<T>>;\n\n  /**\n   * Template definition used as the header container. By default it stores the header row\n   * definition found as a direct content child. Override this value through `setHeaderRowDef` if\n   * the header row definition should be changed or was not defined as a part of the table's\n   * content.\n   */\n  @ContentChild(CdkHeaderRowDef) _headerRowDef: CdkHeaderRowDef;\n\n  constructor(private readonly _differs: IterableDiffers,\n              private readonly _changeDetectorRef: ChangeDetectorRef,\n              elementRef: ElementRef,\n              @Attribute('role') role: string) {\n    if (!role) {\n      elementRef.nativeElement.setAttribute('role', 'grid');\n    }\n  }\n\n  ngOnInit() {\n    // TODO(andrewseguin): Setup a listener for scrolling, emit the calculated view to viewChange\n    this._dataDiffer = this._differs.find([]).create(this._trackByFn);\n\n    // If the table has a header row definition defined as part of its content, flag this as a\n    // header row def change so that the content check will render the header row.\n    if (this._headerRowDef) {\n      this._headerRowDefChanged = true;\n    }\n  }\n\n  ngAfterContentChecked() {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n\n    // Make sure that the user has at least added a header row or row def.\n    if (!this._headerRowDef && !this._rowDefs.length) {\n      throw getTableMissingRowDefsError();\n    }\n\n    // Render updates if the list of columns have been changed for the header or row definitions.\n    this._renderUpdatedColumns();\n\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._renderHeaderRow();\n      this._headerRowDefChanged = false;\n    }\n\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    }\n  }\n\n  ngOnDestroy() {\n    this._rowPlaceholder.viewContainer.clear();\n    this._headerRowPlaceholder.viewContainer.clear();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n\n    if (this.dataSource instanceof DataSource) {\n      this.dataSource.disconnect(this);\n    }\n  }\n\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    const changes = this._dataDiffer.diff(this._data);\n    if (!changes) { return; }\n\n    const viewContainer = this._rowPlaceholder.viewContainer;\n    changes.forEachOperation(\n        (record: IterableChangeRecord<T>, adjustedPreviousIndex: number, currentIndex: number) => {\n          if (record.previousIndex == null) {\n            this._insertRow(record.item, currentIndex);\n          } else if (currentIndex == null) {\n            viewContainer.remove(adjustedPreviousIndex);\n          } else {\n            const view = <RowViewRef<T>>viewContainer.get(adjustedPreviousIndex);\n            viewContainer.move(view!, currentIndex);\n          }\n        });\n\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange((record: IterableChangeRecord<T>) => {\n      const rowView = <RowViewRef<T>>viewContainer.get(record.currentIndex!);\n      rowView.context.$implicit = record.item;\n    });\n  }\n\n  /**\n   * Sets the header row definition to be used. Overrides the header row definition gathered by\n   * using `ContentChild`, if one exists. Sets a flag that will re-render the header row after the\n   * table's content is checked.\n   */\n  setHeaderRowDef(headerRowDef: CdkHeaderRowDef) {\n    this._headerRowDef = headerRowDef;\n    this._headerRowDefChanged = true;\n  }\n\n  /** Adds a column definition that was not included as part of the direct content children. */\n  addColumnDef(columnDef: CdkColumnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n\n  /** Removes a column definition that was not included as part of the direct content children. */\n  removeColumnDef(columnDef: CdkColumnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n\n  /** Adds a row definition that was not included as part of the direct content children. */\n  addRowDef(rowDef: CdkRowDef<T>) {\n    this._customRowDefs.add(rowDef);\n  }\n\n  /** Removes a row definition that was not included as part of the direct content children. */\n  removeRowDef(rowDef: CdkRowDef<T>) {\n    this._customRowDefs.delete(rowDef);\n  }\n\n  /** Update the map containing the content's column definitions. */\n  private _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n\n    const columnDefs = this._contentColumnDefs ? this._contentColumnDefs.toArray() : [];\n    this._customColumnDefs.forEach(columnDef => columnDefs.push(columnDef));\n\n    columnDefs.forEach(columnDef => {\n      if (this._columnDefsByName.has(columnDef.name)) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n\n  /** Update the list of all available row definitions that can be used. */\n  private _cacheRowDefs() {\n    this._rowDefs = this._contentRowDefs ? this._contentRowDefs.toArray() : [];\n    this._customRowDefs.forEach(rowDef => this._rowDefs.push(rowDef));\n\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (defaultRowDefs.length > 1) { throw getTableMultipleDefaultRowDefsError(); }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n\n  /**\n   * Check if the header or rows have changed what columns they want to display. If there is a diff,\n   * then re-render that section.\n   */\n  private _renderUpdatedColumns() {\n    // Re-render the rows when the row definition columns change.\n    this._rowDefs.forEach(def => {\n      if (!!def.getColumnsDiff()) {\n        // Reset the data to an empty array so that renderRowChanges will re-render all new rows.\n        this._dataDiffer.diff([]);\n\n        this._rowPlaceholder.viewContainer.clear();\n        this.renderRows();\n      }\n    });\n\n    // Re-render the header row if there is a difference in its columns.\n    if (this._headerRowDef && this._headerRowDef.getColumnsDiff()) {\n      this._renderHeaderRow();\n    }\n  }\n\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row placeholder. Otherwise start listening for new data.\n   */\n  private _switchDataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    this._data = [];\n\n    if (this.dataSource instanceof DataSource) {\n      this.dataSource.disconnect(this);\n    }\n\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      this._rowPlaceholder.viewContainer.clear();\n    }\n\n    this._dataSource = dataSource;\n  }\n\n  /** Set up a subscription for the data provided by the data source. */\n  private _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) { return; }\n\n    let dataStream: Observable<T[]> | undefined;\n\n    // Check if the datasource is a DataSource object by observing if it has a connect function.\n    // Cannot check this.dataSource['connect'] due to potential property renaming, nor can it\n    // checked as an instanceof DataSource<T> since the table should allow for data sources\n    // that did not explicitly extend DataSource<T>.\n    if ((this.dataSource as DataSource<T>).connect  instanceof Function) {\n      dataStream = (this.dataSource as DataSource<T>).connect(this);\n    } else if (this.dataSource instanceof Observable) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = observableOf(this.dataSource);\n    }\n\n    if (dataStream === undefined) {\n      throw getTableUnknownDataSourceError();\n    }\n\n    this._renderChangeSubscription = dataStream\n        .pipe(takeUntil(this._onDestroy))\n        .subscribe(data => {\n          this._data = data;\n          this.renderRows();\n        });\n  }\n\n  /**\n   * Clears any existing content in the header row placeholder and creates a new embedded view\n   * in the placeholder using the header row definition.\n   */\n  private _renderHeaderRow() {\n    // Clear the header row placeholder if any content exists.\n    if (this._headerRowPlaceholder.viewContainer.length > 0) {\n      this._headerRowPlaceholder.viewContainer.clear();\n    }\n\n    const cells = this._getHeaderCellTemplatesForRow(this._headerRowDef);\n    if (!cells.length) { return; }\n\n    // TODO(andrewseguin): add some code to enforce that exactly\n    //   one CdkCellOutlet was instantiated as a result\n    //   of `createEmbeddedView`.\n    this._headerRowPlaceholder.viewContainer\n        .createEmbeddedView(this._headerRowDef.template, {cells});\n\n    cells.forEach(cell => {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cell.template, {});\n      }\n    });\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Finds the matching row definition that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definition that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDef(data: T, i: number): CdkRowDef<T> {\n    if (this._rowDefs.length == 1) { return this._rowDefs[0]; }\n\n    let rowDef = this._rowDefs.find(def => def.when && def.when(i, data)) || this._defaultRowDef;\n    if (!rowDef) { throw getTableMissingMatchingRowDefError(); }\n\n    return rowDef;\n  }\n\n  /**\n   * Create the embedded view for the data row template and place it in the correct index location\n   * within the data row view container.\n   */\n  private _insertRow(rowData: T, index: number) {\n    const row = this._getRowDef(rowData, index);\n\n    // Row context that will be provided to both the created embedded row view and its cells.\n    const context: CdkCellOutletRowContext<T> = {$implicit: rowData};\n\n    // TODO(andrewseguin): add some code to enforce that exactly one\n    //   CdkCellOutlet was instantiated as a result  of `createEmbeddedView`.\n    this._rowPlaceholder.viewContainer.createEmbeddedView(row.template, context, index);\n\n    this._getCellTemplatesForRow(row).forEach(cell => {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer\n            .createEmbeddedView(cell.template, context);\n      }\n    });\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  private _updateRowIndexContext() {\n    const viewContainer = this._rowPlaceholder.viewContainer;\n    for (let index = 0, count = viewContainer.length; index < count; index++) {\n      const viewRef = viewContainer.get(index) as RowViewRef<T>;\n      viewRef.context.index = index;\n      viewRef.context.count = count;\n      viewRef.context.first = index === 0;\n      viewRef.context.last = index === count - 1;\n      viewRef.context.even = index % 2 === 0;\n      viewRef.context.odd = !viewRef.context.even;\n    }\n  }\n\n  /**\n   * Returns the cell template definitions to insert into the header\n   * as defined by its list of columns to display.\n   */\n  private _getHeaderCellTemplatesForRow(headerDef: CdkHeaderRowDef): CdkHeaderCellDef[] {\n    if (!headerDef || !headerDef.columns) { return []; }\n    return headerDef.columns.map(columnId => {\n      const column = this._columnDefsByName.get(columnId);\n\n      if (!column) {\n        throw getTableUnknownColumnError(columnId);\n      }\n\n      return column.headerCell;\n    });\n  }\n\n  /**\n   * Returns the cell template definitions to insert in the provided row\n   * as defined by its list of columns to display.\n   */\n  private _getCellTemplatesForRow(rowDef: CdkRowDef<T>): CdkCellDef[] {\n    if (!rowDef.columns) { return []; }\n    return rowDef.columns.map(columnId => {\n      const column = this._columnDefsByName.get(columnId);\n\n      if (!column) {\n        throw getTableUnknownColumnError(columnId);\n      }\n\n      return column.cell;\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule} from '@angular/common';\nimport {NgModule} from '@angular/core';\nimport {HeaderRowPlaceholder, RowPlaceholder, CdkTable} from './table';\nimport {CdkCellOutlet, CdkHeaderRow, CdkHeaderRowDef, CdkRow, CdkRowDef} from './row';\nimport {CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCell, CdkCellDef} from './cell';\n\nconst EXPORTED_DECLARATIONS = [\n  CdkTable,\n  CdkRowDef,\n  CdkCellDef,\n  CdkCellOutlet,\n  CdkHeaderCellDef,\n  CdkColumnDef,\n  CdkCell,\n  CdkRow,\n  CdkHeaderCell,\n  CdkHeaderRow,\n  CdkHeader<PERSON>owDef,\n  RowPlaceholder,\n  HeaderRowPlaceholder,\n];\n\n@NgModule({\n  imports: [CommonModule],\n  exports: [EXPORTED_DECLARATIONS],\n  declarations: [EXPORTED_DECLARATIONS]\n\n})\nexport class CdkTableModule { }\n"], "names": ["__extends", "d", "b", "__", "this", "constructor", "extendStatics", "prototype", "Object", "create", "getTableUnknownColumnError", "id", "Error", "getTableDuplicateColumnNameError", "name", "getTableMultipleDefaultRowDefsError", "getTableMissingMatchingRowDefError", "getTableMissingRowDefsError", "getTableUnknownDataSourceError", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "CDK_ROW_TEMPLATE", "BaseRowDef", "template", "_differs", "ngOnChanges", "changes", "columns", "currentValue", "_<PERSON><PERSON><PERSON><PERSON>", "find", "diff", "getColumnsDiff", "CdkHeaderRowDef", "_super", "call", "tslib_1.__extends", "type", "Directive", "args", "selector", "inputs", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "CdkRowDef", "CdkCellOutlet", "_viewContainer", "mostRecentCellOutlet", "ViewContainerRef", "Component", "host", "class", "role", "changeDetection", "ChangeDetectionStrategy", "OnPush", "encapsulation", "ViewEncapsulation", "None", "preserveWhitespaces", "CdkRow", "ctorParameters", "CdkCellDef", "CdkHeaderCellDef", "defineProperty", "CdkColumnDef", "_name", "cssClassFriendlyName", "replace", "Input", "cell", "ContentChild", "headerCell", "CdkHeaderCell", "columnDef", "elementRef", "nativeElement", "classList", "add", "ElementRef", "CdkCell", "RowPlaceholder", "viewContainer", "HeaderRowPlaceholder", "CDK_TABLE_TEMPLATE", "RowViewRef", "EmbeddedViewRef", "CdkTable", "_changeDetectorRef", "_onD<PERSON>roy", "Subject", "_columnDefsByName", "Map", "_customColumnDefs", "Set", "_customRowDefs", "_headerRowDefChanged", "viewChange", "BehaviorSubject", "start", "end", "Number", "MAX_VALUE", "setAttribute", "_trackByFn", "fn", "isDevMode", "console", "warn", "JSON", "stringify", "_dataSource", "dataSource", "_switchDataSource", "ngOnInit", "_data<PERSON><PERSON>er", "_headerRowDef", "ngAfterContentChecked", "_cacheRowDefs", "_cacheColumnDefs", "_rowDefs", "length", "_renderUpdatedColumns", "_renderHeaderRow", "_renderChangeSubscription", "_observe<PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "_rowPlaceholder", "clear", "_headerRowPlaceholder", "next", "complete", "DataSource", "disconnect", "renderRows", "_this", "_data", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "previousIndex", "_insertRow", "item", "remove", "view", "get", "move", "_updateRowIndexContext", "forEachIdentityChange", "context", "$implicit", "setHeaderRowDef", "headerRowDef", "addColumnDef", "removeColumnDef", "delete", "addRowDef", "rowDef", "removeRowDef", "columnDefs", "_contentColumnDefs", "toArray", "for<PERSON>ach", "push", "has", "set", "_contentRowDefs", "defaultRowDefs", "filter", "def", "when", "_defaultRowDef", "unsubscribe", "dataStream", "connect", "Function", "Observable", "isArray", "observableOf", "undefined", "pipe", "takeUntil", "subscribe", "data", "cells", "_getHeaderCellTemplatesForRow", "createEmbeddedView", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getRowDef", "i", "rowData", "index", "row", "_getCellTemplatesForRow", "count", "viewRef", "first", "last", "even", "odd", "headerDef", "map", "columnId", "column", "exportAs", "decorators", "Attribute", "propDecorators", "ViewChild", "ContentChildren", "EXPORTED_DECLARATIONS", "CdkHeaderRow", "NgModule", "imports", "CommonModule", "exports", "declarations", "CdkTableModule"], "mappings": ";;;;;;;+uBAoBA,SAAgBA,GAAUC,EAAGC,GAEzB,QAASC,KAAOC,KAAKC,YAAcJ,EADnCK,EAAcL,EAAGC,GAEjBD,EAAEM,UAAkB,OAANL,EAAaM,OAAOC,OAAOP,IAAMC,EAAGI,UAAYL,EAAEK,UAAW,GAAIJ,ICVnF,QAAAO,GAA2CC,GACzC,MAAOC,OAAM,kCAAkCD,EAAjD,MAOA,QAAAE,GAAiDC,GAC/C,MAAOF,OAAM,+CAA+CE,EAA9D,MAOA,QAAAC,KACE,MAAOH,OAAM,wEAOf,QAAAI,KACE,MAAOJ,OAAM,uEAOf,QAAAK,KACE,MAAOL,OAAM,8FAQf,QAAAM,KACE,MAAON,OAAM,0EDvCf,GAAIN,GAAgBE,OAAOW,iBACpBC,uBAA2BC,QAAS,SAAUpB,EAAGC,GAAKD,EAAEmB,UAAYlB,IACvE,SAAUD,EAAGC,GAAK,IAAK,GAAIoB,KAAKpB,GAAOA,EAAEqB,eAAeD,KAAIrB,EAAEqB,GAAKpB,EAAEoB,KEQ5DE,EAAmB,8CAMhCC,EAAA,WAOE,QAAFA,GAA0CC,EAClBC,GADkBvB,KAA1CsB,SAA0CA,EAClBtB,KAAxBuB,SAAwBA,EAxCxB,MA0CEF,GAAFlB,UAAAqB,YAAE,SAAYC,GAGV,GAAMC,GAAUD,EAAiB,QAAEE,gBAC9B3B,MAAK4B,iBACR5B,KAAK4B,eAAiB5B,KAAKuB,SAASM,KAAKH,GAASrB,SAClDL,KAAK4B,eAAeE,KAAKJ,KAQ7BL,EAAFlB,UAAA4B,eAAE,WACE,MAAO/B,MAAK4B,eAAeE,KAAK9B,KAAK0B,UAzDzCL,mBAsEE,QAAFW,GAAcV,EAA4BC,GAC1C,MAAIU,GAAJC,KAAAlC,KAAUsB,EAAUC,IAApBvB,KAvEA,MAqEqCmC,GAArCH,EAAAC,kBAJAG,KAACC,EAAAA,UAADC,OACEC,SAAU,oBACVC,QAAS,oEAnDXJ,KAAEK,EAAAA,cAFFL,KAAEM,EAAAA,mBAdFV,GAqEqCX,iBA0BnC,QAAFsB,GAAcrB,EAA4BC,GAC1C,MAAIU,GAAJC,KAAAlC,KAAUsB,EAAUC,IAApBvB,KAhGA,MAoFkCmC,GAAlCQ,EAAAV,kBAJAG,KAACC,EAAAA,UAADC,OACEC,SAAU,cACVC,QAAS,4BAA6B,+DAlExCJ,KAAEK,EAAAA,cAFFL,KAAEM,EAAAA,mBAdFC,GAoFkCtB,gBA6DhC,QAAFuB,GAAqBC,GAAA7C,KAArB6C,eAAqBA,EACjBD,EAAcE,qBAAuB9C,KAlJzC,MA+IA4C,GAAAE,qBAAsD,oBAftDV,KAACC,EAAAA,UAADC,OAAYC,SAAU,0DA/GtBH,KAAEW,EAAAA,oBAjBFH,sDAuJAR,KAACY,EAAAA,UAADV,OAAAC,SAAA,iBACEjB,SAAUF,EACV6B,MACFC,MAAA,iBACMC,KAAN,OAEAC,gBAAiBC,EAAAA,wBAAjBC,OACAC,cAAAC,EAAAA,kBAAAC,KACEC,qBAAF,4EAkBA,4EAXAT,MACAC,MAAA,UACAC,KAAA,OAEEC,gBAAFC,EAAAA,wBAAAC,OACAC,cAAAC,EAAAA,kBAAAC,KACAC,qBAAA,MAIAC,EAAAC,eAAA,WAAA,UACAD,kBCjKE,QAAFE,GAA0CvC,GAAAtB,KAA1CsB,SAA0CA,EAhB1C,sBAcAc,KAACC,EAAAA,UAADC,OAAYC,SAAU,uDANtBH,KAAoDK,EAAAA,eARpDoB,kBAyBE,QAAFC,GAA0CxC,GAAAtB,KAA1CsB,SAA0CA,EAzB1C,sBAuBAc,KAACC,EAAAA,UAADC,OAAYC,SAAU,6DAftBH,KAAoDK,EAAAA,eARpDqB,gCAAA,MAoCA1D,QAAA2D,eAAMC,EAAN7D,UAAA,YAAA,WAAuB,MAAOH,MAAKiE,WACjC,SAASvD,GAGFA,IAELV,KAAKiE,MAAQvD,EACbV,KAAKkE,qBAAuBxD,EAAKyD,QAAQ,gBAAiB,sDAX9D/B,KAACC,EAAAA,UAADC,OAAYC,SAAU,6EAGtB7B,OAAA0B,KAAGgC,EAAAA,MAAH9B,MAAS,kBAaT+B,OAAAjC,KAAGkC,EAAAA,aAAHhC,MAAgBuB,KAGhBU,aAAAnC,KAAGkC,EAAAA,aAAHhC,MAAgBwB,MAnDhBE,kBAsEE,QAAFQ,GAAcC,EAAyBC,GACnCA,EAAWC,cAAcC,UAAUC,IAAI,cAAcJ,EAAUP,sBAvEnE,sBA8DA9B,KAACC,EAAAA,UAADC,OACEC,SAAU,kBACVU,MACEC,MAAS,kBACTC,KAAQ,wDAjCZf,KAAa4B,IAzBb5B,KAAiC0C,EAAAA,cARjCN,kBAoFE,QAAFO,GAAcN,EAAyBC,GACnCA,EAAWC,cAAcC,UAAUC,IAAI,cAAcJ,EAAUP,sBArFnE,sBA4EA9B,KAACC,EAAAA,UAADC,OACEC,SAAU,WACVU,MACEC,MAAS,WACTC,KAAQ,oDA/CZf,KAAa4B,IAzBb5B,KAAiC0C,EAAAA,cARjCC,kBCuDE,QAAFC,GAAqBC,GAAAjF,KAArBiF,cAAqBA,EAvDrB,sBAqDA7C,KAACC,EAAAA,UAADC,OAAYC,SAAU,2DAzBtBH,KAAEW,EAAAA,oBA5BFiC,kBAgEE,QAAFE,GAAqBD,GAAAjF,KAArBiF,cAAqBA,EAhErB,sBA8DA7C,KAACC,EAAAA,UAADC,OAAYC,SAAU,iEAlCtBH,KAAEW,EAAAA,oBA5BFmC,KAuEaC,EAAqB,4GAQlC,SAAAlD,+DAAqCE,EAArCiD,EAAAnD,IAAqCoD,EAAAA,4BA0InC,QAAFC,GAA+B/D,EACAgE,EACjBb,EACmBvB,GAHFnD,KAA/BuB,SAA+BA,EACAvB,KAA/BuF,mBAA+BA,EArH/BvF,KAAAwF,WAAuB,GAAIC,GAAAA,QAa3BzF,KAAA0F,kBAA8B,GAAIC,KAelC3F,KAAA4F,kBAA8B,GAAIC,KAGlC7F,KAAA8F,eAA2B,GAAID,KAM/B7F,KAAA+F,sBAAiC,EAwDjC/F,KAAAgG,WAAM,GAAIC,GAAAA,iBAA+CC,MAAO,EAAGC,IAAKC,OAAOC,YA2BtElD,GACHuB,EAAWC,cAAc2B,aAAa,OAAQ,QAPpD,MApEAlG,QAAA2D,eAAMuB,EAANnF,UAAA,eAAA,WAAsC,MAAOH,MAAKuG,gBAChD,SAAYC,GACNC,EAAAA,aACM,MAAND,GAA4B,kBAAPA,IAAiB,SACjBE,QAAY,MACjCA,QAAQC,KAAK,4CAA4CC,KAAKC,UAAUL,GAAhF,KAEIxG,KAAKuG,WAAaC,mCAyBtBpG,OAAA2D,eAAMuB,EAANnF,UAAA,kBAAA,WAA4D,MAAOH,MAAK8G,iBACtE,SAAeC,GACT/G,KAAK8G,cAAgBC,GACvB/G,KAAKgH,kBAAkBD,oCA4C3BzB,EAAFnF,UAAA8G,SAAE,WAEEjH,KAAKkH,YAAclH,KAAKuB,SAASM,SAASxB,OAAOL,KAAKuG,YAIlDvG,KAAKmH,gBACPnH,KAAK+F,sBAAuB,IAIhCT,EAAFnF,UAAAiH,sBAAE,WAME,GAJApH,KAAKqH,gBACLrH,KAAKsH,oBAGAtH,KAAKmH,gBAAkBnH,KAAKuH,SAASC,OACxC,KAAM3G,IAIRb,MAAKyH,wBAGDzH,KAAK+F,uBACP/F,KAAK0H,mBACL1H,KAAK+F,sBAAuB,GAK1B/F,KAAK+G,YAAc/G,KAAKuH,SAASC,OAAS,IAAMxH,KAAK2H,2BACvD3H,KAAK4H,yBAITtC,EAAFnF,UAAA0H,YAAE,WACE7H,KAAK8H,gBAAgB7C,cAAc8C,QACnC/H,KAAKgI,sBAAsB/C,cAAc8C,QACzC/H,KAAKwF,WAAWyC,OAChBjI,KAAKwF,WAAW0C,WAEZlI,KAAK+G,qBAAsBoB,GAAAA,YAC7BnI,KAAK+G,WAAWqB,WAAWpI,OAc/BsF,EAAFnF,UAAAkI,WAAE,WAAA,GAAFC,GAAAtI,KACUyB,EAAUzB,KAAKkH,YAAYpF,KAAK9B,KAAKuI,MAC3C,IAAK9G,EAAL,CAEA,GAAMwD,GAAgBjF,KAAK8H,gBAAgB7C,aAC3CxD,GAAQ+G,iBACJ,SAACC,EAAiCC,EAA+BC,GAC/D,GAA4B,MAAxBF,EAAOG,cACTN,EAAKO,WAAWJ,EAAOK,KAAMH,OACxB,IAAoB,MAAhBA,EACT1D,EAAc8D,OAAOL,OAChB,CACL,GAAMM,GAAsB/D,EAAcgE,IAAIP,EAC9CzD,GAAciE,KAAI,EAAQP,MAKlC3I,KAAKmJ,yBAIL1H,EAAQ2H,sBAAsB,SAACX,GACExD,EAAcgE,IAAIR,EAAmB,cAC5DY,QAAQC,UAAYb,EAAOK,SASvCxD,EAAFnF,UAAAoJ,gBAAE,SAAgBC,GACdxJ,KAAKmH,cAAgBqC,EACrBxJ,KAAK+F,sBAAuB,GAI9BT,EAAFnF,UAAAsJ,aAAE,SAAahF,GACXzE,KAAK4F,kBAAkBf,IAAIJ,IAI7Ba,EAAFnF,UAAAuJ,gBAAE,SAAgBjF,GACdzE,KAAK4F,kBAAkB+D,OAAOlF,IAIhCa,EAAFnF,UAAAyJ,UAAE,SAAUC,GACR7J,KAAK8F,eAAejB,IAAIgF,IAI1BvE,EAAFnF,UAAA2J,aAAE,SAAaD,GACX7J,KAAK8F,eAAe6D,OAAOE,IAIrBvE,EAAVnF,UAAAmH,sCACItH,MAAK0F,kBAAkBqC,OAEvB,IAAMgC,GAAa/J,KAAKgK,mBAAqBhK,KAAKgK,mBAAmBC,YACrEjK,MAAK4F,kBAAkBsE,QAAQ,SAAAzF,GAAa,MAAAsF,GAAWI,KAAK1F,KAE5DsF,EAAWG,QAAQ,SAAAzF,GACjB,GAAI6D,EAAK5C,kBAAkB0E,IAAI3F,EAAU/D,MACvC,KAAMD,GAAiCgE,EAAU/D,KAEnD4H,GAAK5C,kBAAkB2E,IAAI5F,EAAU/D,KAAM+D,MAKvCa,EAAVnF,UAAAkH,mCACIrH,MAAKuH,SAAWvH,KAAKsK,gBAAkBtK,KAAKsK,gBAAgBL,aAC5DjK,KAAK8F,eAAeoE,QAAQ,SAAAL,GAAU,MAAAvB,GAAKf,SAAS4C,KAAKN,IAEzD,IAAMU,GAAiBvK,KAAKuH,SAASiD,OAAO,SAAAC,GAAO,OAACA,EAAIC,MACxD,IAAIH,EAAe/C,OAAS,EAAK,KAAM7G,IACvCX,MAAK2K,eAAiBJ,EAAe,IAO/BjF,EAAVnF,UAAAsH,2CAEIzH,MAAKuH,SAAS2C,QAAQ,SAAAO,GACdA,EAAI1I,mBAERuG,EAAKpB,YAAYpF,SAEjBwG,EAAKR,gBAAgB7C,cAAc8C,QACnCO,EAAKD,gBAKLrI,KAAKmH,eAAiBnH,KAAKmH,cAAcpF,kBAC3C/B,KAAK0H,oBASDpC,EAAVnF,UAAA6G,kBAAA,SAA4BD,GACxB/G,KAAKuI,SAEDvI,KAAK+G,qBAAsBoB,GAAAA,YAC7BnI,KAAK+G,WAAWqB,WAAWpI,MAIzBA,KAAK2H,4BACP3H,KAAK2H,0BAA0BiD,cAC/B5K,KAAK2H,0BAA4B,MAG9BZ,IACC/G,KAAKkH,aACPlH,KAAKkH,YAAYpF,SAEnB9B,KAAK8H,gBAAgB7C,cAAc8C,SAGrC/H,KAAK8G,YAAcC,GAIbzB,EAAVnF,UAAAyH,2CAEI,IAAK5H,KAAK+G,WAAV,CAEA,GAAI8D,EAcJ,IARK7K,KAAgC,WAAE8K,kBAAoBC,UACzDF,EAAc7K,KAAgC,WAAE8K,QAAQ9K,MAC/CA,KAAK+G,qBAAsBiE,GAAAA,WACpCH,EAAa7K,KAAK+G,WACT9F,MAAMgK,QAAQjL,KAAK+G,cAC5B8D,EAAaK,EAAAA,GAAalL,KAAK+G,iBAGdoE,KAAfN,EACF,KAAM/J,IAGRd,MAAK2H,0BAA4BkD,EAC5BO,KAAKC,EAAAA,UAAUrL,KAAKwF,aACpB8F,UAAU,SAAAC,GACTjD,EAAKC,MAAQgD,EACbjD,EAAKD,iBAQL/C,EAAVnF,UAAAuH,4BAEQ1H,KAAKgI,sBAAsB/C,cAAcuC,OAAS,GACpDxH,KAAKgI,sBAAsB/C,cAAc8C,OAG3C,IAAMyD,GAAQxL,KAAKyL,8BAA8BzL,KAAKmH,cACjDqE,GAAMhE,SAKXxH,KAAKgI,sBAAsB/C,cACtByG,mBAAmB1L,KAAKmH,cAAc7F,UAAWkK,MAA1DA,IAEIA,EAAMtB,QAAQ,SAAA7F,GACRzB,EAAcE,sBAChBF,EAAcE,qBAAqBD,eAAe6I,mBAAmBrH,EAAK/C,eAI9EtB,KAAKuF,mBAAmBoG,iBAS1BrG,EAAFnF,UAAAyL,WAAE,SAAWL,EAASM,GAClB,GAA4B,GAAxB7L,KAAKuH,SAASC,OAAe,MAAOxH,MAAKuH,SAAS,EAEtD,IAAIsC,GAAS7J,KAAKuH,SAAS1F,KAAK,SAAA4I,GAAO,MAAAA,GAAIC,MAAQD,EAAIC,KAAKmB,EAAGN,MAAUvL,KAAK2K,cAC9E,KAAKd,EAAU,KAAMjJ,IAErB,OAAOiJ,IAODvE,EAAVnF,UAAA0I,WAAA,SAAqBiD,EAAYC,GAC7B,GAAMC,GAAMhM,KAAK4L,WAAWE,EAASC,GAG/B1C,GAAuCC,UAAWwC,EAIxD9L,MAAK8H,gBAAgB7C,cAAcyG,mBAAmBM,EAAI1K,SAAU+H,EAAS0C,GAE7E/L,KAAKiM,wBAAwBD,GAAK9B,QAAQ,SAAA7F,GACpCzB,EAAcE,sBAChBF,EAAcE,qBAAqBD,eAC9B6I,mBAAmBrH,EAAK/C,SAAU+H,KAI3CrJ,KAAKuF,mBAAmBoG,gBAOlBrG,EAAVnF,UAAAgJ,kCAEI,IAAK,GADClE,GAAgBjF,KAAK8H,gBAAgB7C,cAClC8G,EAAQ,EAAGG,EAAQjH,EAAcuC,OAAQuE,EAAQG,EAAOH,IAAS,CACxE,GAAMI,GAAUlH,EAAcgE,IAAI8C,EAClCI,GAAQ9C,QAAQ0C,MAAQA,EACxBI,EAAQ9C,QAAQ6C,MAAQA,EACxBC,EAAQ9C,QAAQ+C,MAAkB,IAAVL,EACxBI,EAAQ9C,QAAQgD,KAAON,IAAUG,EAAQ,EACzCC,EAAQ9C,QAAQiD,KAAOP,EAAQ,GAAM,EACrCI,EAAQ9C,QAAQkD,KAAOJ,EAAQ9C,QAAQiD,OAQnChH,EAAVnF,UAAAsL,8BAAA,SAAwCe,aACpC,OAAKA,IAAcA,EAAU9K,QACtB8K,EAAU9K,QAAQ+K,IAAI,SAAAC,GAC3B,GAAMC,GAASrE,EAAK5C,kBAAkBuD,IAAIyD,EAE1C,KAAKC,EACH,KAAMrM,GAA2BoM,EAGnC,OAAOC,GAAOpI,iBAQVe,EAAVnF,UAAA8L,wBAAA,SAAkCpC,aAC9B,OAAKA,GAAOnI,QACLmI,EAAOnI,QAAQ+K,IAAI,SAAAC,GACxB,GAAMC,GAASrE,EAAK5C,kBAAkBuD,IAAIyD,EAE1C,KAAKC,EACH,KAAMrM,GAA2BoM,EAGnC,OAAOC,GAAOtI,0BA3dpBjC,KAACY,EAAAA,UAADV,OAAAC,SAAA,YACEqK,SAAU,WACVtL,SAAU6D,EACVlC,MACFC,MAAA,aAEAK,cAAAC,EAAAA,kBAAAC,KACAC,qBAAA,EACEN,gBAAFC,EAAAA,wBAAAC,mGAxEAlB,KAAE0C,EAAAA,aAXF1C,SAAE+I,GAAF0B,aAAAzK,KAAA0K,EAAAA,UAAAxK,MAAA,aAgNAgD,EAAAyH,qEA1EAjF,kBAAA1F,KAAA4K,EAAAA,UAAA1K,MAAA0C,KAgCAgD,wBAAQ5F,KAAR4K,EAAAA,UAAA1K,MAAA4C,KAmBA8E,qBAAA5H,KAAA6K,EAAAA,gBAAA3K,MAAA0B,KACAsG,kBAAAlI,KAAA6K,EAAAA,gBAAA3K,MAAaK,KAMbwE,gBAAA/E,KAAAkC,EAAAA,aAAAhC,MAAAN,MAWAsD,MCzMM4H,GACJ5H,EACA3C,EACAkB,EACAjB,EACAkB,EACAE,EACAe,EACApB,EACAa,EACA2I,EACAnL,EACAgD,EACAE,8BA3BF,sBA8BA9C,KAACgL,EAAAA,SAAD9K,OACE+K,SAAUC,EAAAA,cACVC,SAAUL,GACVM,cAAeN,6CAjCjBO"}