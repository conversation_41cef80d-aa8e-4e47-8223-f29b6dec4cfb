{"_from": "restricted-input@4.0.3", "_id": "restricted-input@4.0.3", "_inBundle": false, "_integrity": "sha512-VpkwT5Fr3DhvoRZfPnmHDhnYAYETjjNzDlvA4NlW0iknFS47C5X4OCHEpOOxaPjvmka5V8d1ty1jVVoorZKvHg==", "_location": "/restricted-input", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "restricted-input@4.0.3", "name": "restricted-input", "escapedName": "restricted-input", "rawSpec": "4.0.3", "saveSpec": null, "fetchSpec": "4.0.3"}, "_requiredBy": ["/braintree-web"], "_resolved": "https://registry.npmjs.org/restricted-input/-/restricted-input-4.0.3.tgz", "_shasum": "73b4a4fc44c13eacad44603a37096a84e31b717b", "_spec": "restricted-input@4.0.3", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\braintree-web", "author": {"name": "Braintree", "email": "<EMAIL>", "url": "https://www.braintreepayments.com/"}, "bugs": {"url": "https://github.com/braintree/restricted-input/issues"}, "bundleDependencies": false, "dependencies": {"@braintree/browser-detection": "^1.17.2"}, "deprecated": false, "description": "Restrict inputs to certain valid characters (e.g. formatting phone or card numbers)", "devDependencies": {"@types/jest": "^29.5.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@wdio/browserstack-service": "^7.32.4", "@wdio/cli": "^7.32.4", "@wdio/local-runner": "^7.32.4", "@wdio/mocha-framework": "^7.19.3", "@wdio/spec-reporter": "^7.31.1", "@wdio/sync": "^7.19.4", "browserify": "^17.0.0", "browserstack-local": "^1.5.4", "chai": "^4.3.7", "chokidar-cli": "^3.0.0", "dotenv": "^16.3.1", "eslint": "^8.47.0", "eslint-config-braintree": "^6.0.0-typescript-prep-rc.2", "eslint-plugin-prettier": "^4.2.1", "express": "^4.18.2", "jest": "^29.6.3", "jest-environment-jsdom": "^29.6.3", "jsdoc": "^4.0.2", "prettier": "^2.8.8", "ts-jest": "^29.1.1", "tsify": "^5.0.4", "typescript": "^5.1.6", "uuid": "^9.0.0", "webdriverio": "^7.32.4"}, "files": ["dist", "supports-input-formatting.js"], "homepage": "https://github.com/braintree/restricted-input", "jest": {"testEnvironment": "jsdom", "preset": "ts-jest", "setupFilesAfterEnv": ["./test/unit/global.ts"], "testPathIgnorePatterns": ["<rootDir>/dist"]}, "keywords": ["input", "formatting", "filtering", "text", "spacing", "restrict"], "license": "MIT", "main": "dist/main.js", "name": "restricted-input", "repository": {"type": "git", "url": "git://github.com/braintree/restricted-input.git"}, "scripts": {"build": "tsc --declaration", "build:app": "mkdir -p dist-app; browserify ./src/main.ts -p [ tsify --strict ] -o dist-app/restricted-input.js -s RestrictedInput -v", "development": "npm start & chokidar 'src/lib/**/*.ts' -c 'npm run build:app; echo $(tput setaf 2)rebuilt$(tput sgr0)'", "doc": "jsdoc -r -R README.md -d jsdoc dist/lib/", "doc:watch": "npm run doc && chokidar 'src/lib/**/*.ts' -c 'npm run doc'", "lint": "eslint --ext js,ts src test", "postpublish": "npm run publish:demo", "posttest": "npm run lint", "prebuild": "prettier --write .", "predoc": "npm run build", "prepublishOnly": "npm run build && npm run build:app", "prestart": "npm run build:app", "publish:demo": "./publish-gh-pages.sh", "start": "node ./test/support/server.js", "test": "npm run test:unit", "test:all": "npm run test && npm run test:integration", "test:integration": "wdio wdio.conf.js", "test:unit": "jest test/unit", "test:watch": "jest --watchAll"}, "version": "4.0.3"}