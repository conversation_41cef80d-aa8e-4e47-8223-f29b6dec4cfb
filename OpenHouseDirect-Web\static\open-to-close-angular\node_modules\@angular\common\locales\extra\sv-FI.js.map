{"version": 3, "file": "sv-FI.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/sv-FI.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC;QAC1E,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC;QAC5E;YACE,SAAS,EAAE,aAAa,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,YAAY;YAC5E,WAAW;SACZ;KACF;IACD;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;QACvD,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;QAC1D,CAAC,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,CAAC;KACnE;IACD;QACE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QACvF,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['midn.', 'på morg.', 'på förm.', 'på efterm.', 'på kvällen', 'på natten'],\n    ['midnatt', 'på morg.', 'på förm.', 'på efterm.', 'på kvällen', 'på natten'],\n    [\n      'midnatt', 'på morgonen', 'på förmiddagen', 'på eftermiddagen', 'på kvällen',\n      'på natten'\n    ]\n  ],\n  [\n    ['midn.', 'morg.', 'förm.', 'efterm.', 'kväll', 'natt'],\n    ['midnatt', 'morgon', 'förm.', 'efterm.', 'kväll', 'natt'],\n    ['midnatt', 'morgon', 'förmiddag', 'eftermiddag', 'kväll', 'natt']\n  ],\n  [\n    '00:00', ['05:00', '10:00'], ['10:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'],\n    ['00:00', '05:00']\n  ]\n];\n"]}