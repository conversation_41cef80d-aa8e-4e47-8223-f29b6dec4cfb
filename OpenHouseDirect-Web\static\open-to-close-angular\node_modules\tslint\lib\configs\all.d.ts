export declare const rules: {
    "adjacent-overload-signatures": boolean;
    "ban-types": {
        options: string[][];
    };
    "member-access": (string | boolean)[];
    "member-ordering": (boolean | {
        "order": string;
        "alphabetize": boolean;
    })[];
    "no-any": boolean;
    "no-empty-interface": boolean;
    "no-import-side-effect": boolean;
    "no-inferrable-types": (string | boolean)[];
    "no-internal-module": boolean;
    "no-magic-numbers": boolean;
    "no-namespace": boolean;
    "no-non-null-assertion": boolean;
    "no-reference": boolean;
    "no-this-assignment": boolean;
    "no-var-requires": boolean;
    "only-arrow-functions": boolean;
    "prefer-for-of": boolean;
    "prefer-readonly": boolean;
    "promise-function-async": boolean;
    "typedef": (string | boolean)[];
    "typedef-whitespace": (boolean | {
        "call-signature": string;
        "index-signature": string;
        "parameter": string;
        "property-declaration": string;
        "variable-declaration": string;
    })[];
    "unified-signatures": boolean;
    "await-promise": boolean;
    "ban-comma-operator": boolean;
    "curly": boolean;
    "forin": boolean;
    "label-position": boolean;
    "no-arg": boolean;
    "no-bitwise": boolean;
    "no-conditional-assignment": boolean;
    "no-console": boolean;
    "no-construct": boolean;
    "no-debugger": boolean;
    "no-duplicate-super": boolean;
    "no-duplicate-switch-case": boolean;
    "no-duplicate-variable": (string | boolean)[];
    "no-dynamic-delete": boolean;
    "no-empty": boolean;
    "no-eval": boolean;
    "no-floating-promises": boolean;
    "no-for-in-array": boolean;
    "no-implicit-dependencies": boolean;
    "no-inferred-empty-object-type": boolean;
    "no-invalid-template-strings": boolean;
    "no-misused-new": boolean;
    "no-null-keyword": boolean;
    "no-object-literal-type-assertion": boolean;
    "no-return-await": boolean;
    "no-shadowed-variable": boolean;
    "no-string-literal": boolean;
    "no-string-throw": boolean;
    "no-sparse-arrays": boolean;
    "no-submodule-imports": boolean;
    "no-unbound-method": boolean;
    "no-unnecessary-class": (string | boolean)[];
    "no-unsafe-any": boolean;
    "no-unsafe-finally": boolean;
    "no-unused-expression": boolean;
    "no-unused-variable": boolean;
    "no-use-before-declare": boolean;
    "no-var-keyword": boolean;
    "no-void-expression": boolean;
    "prefer-conditional-expression": boolean;
    "radix": boolean;
    "restrict-plus-operands": boolean;
    "strict-boolean-expressions": boolean;
    "strict-type-predicates": boolean;
    "switch-default": boolean;
    "triple-equals": boolean;
    "use-default-type-parameter": boolean;
    "use-isnan": boolean;
    "cyclomatic-complexity": boolean;
    "eofline": boolean;
    "indent": (string | boolean)[];
    "linebreak-style": (string | boolean)[];
    "max-classes-per-file": (number | boolean)[];
    "max-file-line-count": (number | boolean)[];
    "max-line-length": (number | boolean)[];
    "no-default-export": boolean;
    "no-duplicate-imports": boolean;
    "no-irregular-whitespace": boolean;
    "no-mergeable-namespace": boolean;
    "no-parameter-reassignment": boolean;
    "no-require-imports": boolean;
    "no-trailing-whitespace": boolean;
    "object-literal-sort-keys": boolean;
    "prefer-const": boolean;
    "trailing-comma": (boolean | {
        "multiline": string;
        "singleline": string;
    })[];
    "align": (string | boolean)[];
    "array-type": (string | boolean)[];
    "arrow-parens": boolean;
    "arrow-return-shorthand": (string | boolean)[];
    "binary-expression-operand-order": boolean;
    "callable-types": boolean;
    "class-name": boolean;
    "comment-format": (string | boolean)[];
    "completed-docs": boolean;
    "deprecation": boolean;
    "encoding": boolean;
    "import-spacing": boolean;
    "interface-name": boolean;
    "interface-over-type-literal": boolean;
    "jsdoc-format": (string | boolean)[];
    "match-default-export-name": boolean;
    "new-parens": boolean;
    "newline-before-return": boolean;
    "newline-per-chained-call": boolean;
    "no-angle-bracket-type-assertion": boolean;
    "no-boolean-literal-compare": boolean;
    "no-consecutive-blank-lines": boolean;
    "no-parameter-properties": boolean;
    "no-redundant-jsdoc": boolean;
    "no-reference-import": boolean;
    "no-unnecessary-callback-wrapper": boolean;
    "no-unnecessary-initializer": boolean;
    "no-unnecessary-qualifier": boolean;
    "no-unnecessary-type-assertion": boolean;
    "number-literal-format": boolean;
    "object-literal-key-quotes": (string | boolean)[];
    "object-literal-shorthand": boolean;
    "one-line": (string | boolean)[];
    "one-variable-per-declaration": boolean;
    "ordered-imports": (boolean | {
        "import-sources-order": string;
        "named-imports-order": string;
        "module-source-path": string;
    })[];
    "prefer-function-over-method": boolean;
    "prefer-method-signature": boolean;
    "prefer-object-spread": boolean;
    "prefer-switch": boolean;
    "prefer-template": boolean;
    "quotemark": (string | boolean)[];
    "return-undefined": boolean;
    "semicolon": (string | boolean)[];
    "space-before-function-paren": (boolean | {
        "anonymous": string;
        "asyncArrow": string;
        "constructor": string;
        "method": string;
        "named": string;
    })[];
    "space-within-parens": (number | boolean)[];
    "switch-final-break": boolean;
    "type-literal-delimiter": boolean;
    "variable-name": (string | boolean)[];
    "whitespace": (string | boolean)[];
};
export declare const RULES_EXCLUDED_FROM_ALL_CONFIG: string[];
export declare const jsRules: {
    [key: string]: any;
};
