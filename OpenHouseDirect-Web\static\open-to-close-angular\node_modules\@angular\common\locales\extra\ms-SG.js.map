{"version": 3, "file": "ms-SG.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ms-SG.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,AAAD;QACnD,CAAC,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC;KAC3D;IACD;QACE,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC;QAClD,CAAC,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC;KAC3D;IACD;QACE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC9E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['pagi', 'pagi', 'tengah hari', 'petang', 'malam'], ,\n    ['tengah malam', 'pagi', 'tengah hari', 'petang', 'malam']\n  ],\n  [\n    ['pagi', 'pagi', 'tengah hari', 'petang', 'malam'],\n    ['tengah malam', 'pagi', 'tengah hari', 'petang', 'malam'],\n  ],\n  [\n    ['00:00', '01:00'], ['01:00', '12:00'], ['12:00', '14:00'], ['14:00', '19:00'],\n    ['19:00', '24:00']\n  ]\n];\n"]}