{"_from": "oauth-sign@~0.8.1", "_id": "oauth-sign@0.8.2", "_inBundle": false, "_integrity": "sha512-VlF07iu3VV3+BTXj43Nmp6Irt/G7j/NgEctUS6IweH1RGhURjjCc2NWtzXFPXXWWfc7hgbXQdtiQu2LGp6MxUg==", "_location": "/oauth-sign", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "oauth-sign@~0.8.1", "name": "oauth-sign", "escapedName": "oauth-sign", "rawSpec": "~0.8.1", "saveSpec": null, "fetchSpec": "~0.8.1"}, "_requiredBy": ["/loggly/request", "/request"], "_resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "_shasum": "46a6ab7f0aead8deae9ec0565780b7d4efeb9d43", "_spec": "oauth-sign@~0.8.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\request", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "devDependencies": {}, "engines": {"node": "*"}, "files": ["index.js"], "homepage": "https://github.com/mikeal/oauth-sign#readme", "license": "Apache-2.0", "main": "index.js", "name": "oauth-sign", "optionalDependencies": {}, "repository": {"url": "git+https://github.com/mikeal/oauth-sign.git"}, "scripts": {"test": "node test.js"}, "version": "0.8.2"}