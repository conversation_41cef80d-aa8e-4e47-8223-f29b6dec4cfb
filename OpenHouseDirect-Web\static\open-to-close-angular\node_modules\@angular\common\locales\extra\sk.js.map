{"version": 3, "file": "sk.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/sk.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QAC3D,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;QACpE,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC;KAClF;IACD;QACE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;QACxD,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;QAC7D,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC;KAC3E;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['o poln.', 'nap.', 'ráno', 'dop.', 'pop.', 'več.', 'v n.'],\n    ['o poln.', 'napol.', 'ráno', 'dopol.', 'popol.', 'večer', 'v noci'],\n    ['o polnoci', 'napoludnie', 'ráno', 'dopoludnia', 'popoludní', 'večer', 'v noci']\n  ],\n  [\n    ['poln.', 'pol.', 'ráno', 'dop.', 'pop.', 'več.', 'noc'],\n    ['poln.', 'pol.', 'ráno', 'dopol.', 'popol.', 'večer', 'noc'],\n    ['polnoc', 'poludnie', 'ráno', 'dopoludnie', 'popoludnie', 'večer', 'noc']\n  ],\n  [\n    '00:00', '12:00', ['04:00', '09:00'], ['09:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '22:00'], ['22:00', '04:00']\n  ]\n];\n"]}