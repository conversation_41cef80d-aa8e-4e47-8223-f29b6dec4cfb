{"_from": "supports-preserve-symlinks-flag@^1.0.0", "_id": "supports-preserve-symlinks-flag@1.0.0", "_inBundle": false, "_integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "_location": "/supports-preserve-symlinks-flag", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "supports-preserve-symlinks-flag@^1.0.0", "name": "supports-preserve-symlinks-flag", "escapedName": "supports-preserve-symlinks-flag", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/resolve"], "_resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "_shasum": "6eda4bd344a3c94aea376d4cc31bc77311039e09", "_spec": "supports-preserve-symlinks-flag@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\resolve", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": "./browser.js", "bugs": {"url": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Determine if the current node version supports the `--preserve-symlinks` flag.", "devDependencies": {"@ljharb/eslint-config": "^20.1.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "eslint": "^8.6.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "semver": "^6.3.0", "tape": "^5.4.0"}, "engines": {"node": ">= 0.4"}, "exports": {".": [{"browser": "./browser.js", "default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag#readme", "keywords": ["node", "flag", "symlink", "symlinks", "preserve-symlinks"], "license": "MIT", "main": "./index.js", "name": "supports-preserve-symlinks-flag", "repository": {"type": "git", "url": "git+https://github.com/inspect-js/node-supports-preserve-symlinks-flag.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.0.0"}