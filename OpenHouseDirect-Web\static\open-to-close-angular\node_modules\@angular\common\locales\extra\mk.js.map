{"version": 3, "file": "mk.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/mk.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;QAC9D;YACE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO;YAClD,SAAS,EAAE,MAAM;SAClB;QACD;YACE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY;YAC5C,UAAU,EAAE,SAAS,EAAE,WAAW;SACnC;KACF;IACD;QACE;YACE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO;YAChD,SAAS,EAAE,WAAW;SACvB;QACD;YACE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO;YAClD,SAAS,EAAE,WAAW;SACvB;QACD;YACE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY;YAC/C,UAAU,EAAE,SAAS,EAAE,WAAW;SACnC;KACF;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KACvC;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['полн.', 'напл.', 'утро', 'претпл.', 'попл.', 'веч.', 'ноќе'],\n    [\n      'полноќ', 'напладне', 'наутро', 'претпл.', 'попл.',\n      'навечер', 'ноќе'\n    ],\n    [\n      'полноќ', 'напладне', 'наутро', 'претпладне',\n      'попладне', 'навечер', 'по полноќ'\n    ]\n  ],\n  [\n    [\n      'полноќ', 'пладне', 'наутро', 'претпл.', 'попл.',\n      'навечер', 'по полноќ'\n    ],\n    [\n      'полноќ', 'напладне', 'наутро', 'претпл.', 'попл.',\n      'навечер', 'по полноќ'\n    ],\n    [\n      'на полноќ', 'напладне', 'наутро', 'претпладне',\n      'попладне', 'навечер', 'по полноќ'\n    ]\n  ],\n  [\n    '00:00', '12:00', ['04:00', '10:00'], ['10:00', '12:00'], ['12:00', '18:00'],\n    ['18:00', '24:00'], ['00:00', '04:00']\n  ]\n];\n"]}