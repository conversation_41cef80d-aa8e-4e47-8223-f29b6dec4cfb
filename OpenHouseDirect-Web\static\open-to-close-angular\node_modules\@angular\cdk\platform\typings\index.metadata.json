{"__symbolic": "module", "version": 4, "metadata": {"Platform": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "Injectable", "line": 18, "character": 1}}], "members": {}}, "supportsPassiveEventListeners": {"__symbolic": "function"}, "getSupportedInputTypes": {"__symbolic": "function"}, "PlatformModule": {"__symbolic": "class", "decorators": [{"__symbolic": "call", "expression": {"__symbolic": "reference", "module": "@angular/core", "name": "NgModule", "line": 12, "character": 1}, "arguments": [{"providers": [{"__symbolic": "reference", "name": "Platform"}]}]}], "members": {}}}, "origins": {"Platform": "./platform", "supportsPassiveEventListeners": "./features", "getSupportedInputTypes": "./features", "PlatformModule": "./platform-module"}, "importAs": "@angular/cdk/platform"}