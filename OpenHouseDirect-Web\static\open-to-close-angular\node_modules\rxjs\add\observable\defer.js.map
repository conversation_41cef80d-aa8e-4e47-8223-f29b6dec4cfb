{"version": 3, "file": "defer.js", "sourceRoot": "", "sources": ["../../../src/add/observable/defer.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,sBAAqC,wBAAwB,CAAC,CAAA;AAE9D,uBAAU,CAAC,KAAK,GAAG,aAAW,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { defer as staticDefer } from '../../observable/defer';\n\nObservable.defer = staticDefer;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let defer: typeof staticDefer;\n  }\n}"]}