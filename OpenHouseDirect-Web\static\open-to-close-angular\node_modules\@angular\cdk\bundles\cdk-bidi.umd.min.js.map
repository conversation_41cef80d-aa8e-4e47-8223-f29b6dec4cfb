{"version": 3, "file": "cdk-bidi.umd.min.js", "sources": ["../../src/cdk/bidi/directionality.ts", "../../src/cdk/bidi/dir.ts", "../../src/cdk/bidi/bidi-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  EventEmitter,\n  Injectable,\n  Optional,\n  Inject,\n  InjectionToken,\n} from '@angular/core';\n\n\nexport type Direction = 'ltr' | 'rtl';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-brower because the unit tests\n * themselves use things like `querySelector` in test code.\n */\nexport const DIR_DOCUMENT = new InjectionToken<Document>('cdk-dir-doc');\n\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\n@Injectable()\nexport class Directionality {\n  /** The current 'ltr' or 'rtl' value. */\n  readonly value: Direction = 'ltr';\n\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  readonly change = new EventEmitter<Direction>();\n\n  constructor(@Optional() @Inject(DIR_DOCUMENT) _document?: any) {\n    if (_document) {\n      // TODO: handle 'auto' value -\n      // We still need to account for dir=\"auto\".\n      // It looks like HTMLElemenet.dir is also \"auto\" when that's set to the attribute,\n      // but getComputedStyle return either \"ltr\" or \"rtl\". avoiding getComputedStyle for now\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.value = (bodyDir || htmlDir || 'ltr') as Direction;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  Output,\n  Input,\n  EventEmitter,\n  AfterContentInit,\n  OnDestroy,\n} from '@angular/core';\n\nimport {Direction, Directionality} from './directionality';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n@Directive({\n  selector: '[dir]',\n  providers: [{provide: Directionality, useExisting: Dir}],\n  host: {'[dir]': 'dir'},\n  exportAs: 'dir',\n})\nexport class Dir implements Directionality, AfterContentInit, OnDestroy {\n  _dir: Direction = 'ltr';\n\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Event emitted when the direction changes. */\n  @Output('dirChange') change = new EventEmitter<Direction>();\n\n  /** @docs-private */\n  @Input()\n  get dir(): Direction { return this._dir; }\n  set dir(v: Direction) {\n    const old = this._dir;\n    this._dir = v;\n    if (old !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n\n  /** Current layout direction of the element. */\n  get value(): Direction { return this.dir; }\n\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {Dir} from './dir';\nimport {DIR_DOCUMENT, Directionality} from './directionality';\n\n\n@NgModule({\n  exports: [Dir],\n  declarations: [Dir],\n  providers: [\n    {provide: DIR_DOCUMENT, useExisting: DOCUMENT},\n    Directionality,\n  ]\n})\nexport class BidiModule { }\n"], "names": ["DIR_DOCUMENT", "InjectionToken", "Directionality", "_document", "this", "value", "change", "EventEmitter", "bodyDir", "body", "dir", "htmlDir", "documentElement", "type", "Injectable", "undefined", "decorators", "Optional", "Inject", "args", "_dir", "_isInitialized", "Object", "defineProperty", "<PERSON><PERSON>", "prototype", "v", "old", "emit", "ngAfterContentInit", "ngOnDestroy", "complete", "Directive", "selector", "providers", "provide", "useExisting", "host", "[dir]", "exportAs", "Output", "Input", "BidiModule", "NgModule", "exports", "declarations", "DOCUMENT"], "mappings": ";;;;;;;kWA6BA,IAAaA,GAAe,GAAIC,GAAAA,eAAyB,4BAcvD,QAAFC,GAAgDC,GAC5C,GANJC,KAAAC,MAA8B,MAG9BD,KAAAE,OAAoB,GAAIC,GAAAA,aAGhBJ,EAAW,CAKb,GAAMK,GAAUL,EAAUM,KAAON,EAAUM,KAAKC,IAAM,KAChDC,EAAUR,EAAUS,gBAAkBT,EAAUS,gBAAgBF,IAAM,IAC5EN,MAAKC,MAASG,GAAWG,GAAW,OAnD1C,sBAmCAE,KAACC,EAAAA,iDAQDD,SAAAE,GAAAC,aAAAH,KAAeI,EAAAA,WAAfJ,KAA2BK,EAAAA,OAA3BC,MAAkCnB,QA3ClCE,+BCgCAE,KAAAgB,KAAoB,MAGpBhB,KAAAiB,gBAAoC,EAGpCjB,KAAAE,OAAgC,GAAIC,GAAAA,aAtCpC,MA0CAe,QAAAC,eAAMC,EAANC,UAAA,WAAA,WAAyB,MAAOrB,MAAKgB,UACnC,SAAQM,GACN,GAAMC,GAAMvB,KAAKgB,IACjBhB,MAAKgB,KAAOM,EACRC,IAAQvB,KAAKgB,MAAQhB,KAAKiB,gBAC5BjB,KAAKE,OAAOsB,KAAKxB,KAAKgB,uCAK1BE,OAAFC,eAAMC,EAANC,UAAA,aAAE,WAAyB,MAAOrB,MAAKM,qCAGrCc,EAAFC,UAAAI,mBAAE,WACEzB,KAAKiB,gBAAiB,GAGxBG,EAAFC,UAAAK,YAAE,WACE1B,KAAKE,OAAOyB,2BAnChBlB,KAACmB,EAAAA,UAADb,OACEc,SAAU,QACVC,YAAaC,QAASjC,EAAgBkC,YAAaZ,IACnDa,MAAOC,QAAS,OAChBC,SAAU,kEASZjC,SAAAO,KAAG2B,EAAAA,OAAHrB,MAAU,eAGVT,MAAAG,KAAG4B,EAAAA,SAzCHjB,KCQAkB,EAAA,yBARA,sBAcA7B,KAAC8B,EAAAA,SAADxB,OACEyB,SAAUpB,GACVqB,cAAerB,GACfU,YACGC,QAASnC,EAAcoC,YAAaU,EAAAA,UACrC5C,6CAnBJwC"}