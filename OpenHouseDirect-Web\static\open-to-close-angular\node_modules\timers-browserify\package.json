{"_from": "timers-browserify@^2.0.4", "_id": "timers-browserify@2.0.12", "_inBundle": false, "_integrity": "sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==", "_location": "/timers-browserify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "timers-browserify@^2.0.4", "name": "timers-browserify", "escapedName": "timers-browserify", "rawSpec": "^2.0.4", "saveSpec": null, "fetchSpec": "^2.0.4"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.12.tgz", "_shasum": "44a45c11fbf407f34f97bccd1577c652361b00ee", "_spec": "timers-browserify@^2.0.4", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\node-libs-browser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://convolv.es/"}, "bugs": {"url": "https://github.com/jryans/timers-browserify/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>ut<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "taoqf", "email": "tao_qiu<PERSON>@126.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wtgtybhertgeghgtwtg", "email": "<EMAIL>"}], "dependencies": {"setimmediate": "^1.0.4"}, "deprecated": false, "description": "timers module for browserify", "devDependencies": {"browserify": "~1.10.16", "connect": "~2.30.2"}, "engines": {"node": ">=0.6.0"}, "homepage": "https://github.com/jryans/timers-browserify", "jspm": {"map": {"./main.js": {"node": "@node/timers"}}}, "keywords": ["timers", "browserify", "browser"], "license": "MIT", "main": "main.js", "name": "timers-browserify", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/jryans/timers-browserify.git"}, "version": "2.0.12"}