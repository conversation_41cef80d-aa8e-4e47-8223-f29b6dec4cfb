/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.keycodes=e.ng.cdk.keycodes||{}))}(this,function(e){"use strict";e.UP_ARROW=38,e.DOWN_ARROW=40,e.RIGHT_ARROW=39,e.LEFT_ARROW=37,e.PAGE_UP=33,e.PAGE_DOWN=34,e.HOME=36,e.END=35,e.ENTER=13,e.SPACE=32,e.TAB=9,e.ESCAPE=27,e.BACKSPACE=8,e.DELETE=46,e.A=65,e.Z=90,e.ZERO=48,e.NINE=57,e.COMMA=188,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-keycodes.umd.min.js.map
