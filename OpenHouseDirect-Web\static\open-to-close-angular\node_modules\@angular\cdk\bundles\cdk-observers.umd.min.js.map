{"version": 3, "file": "cdk-observers.umd.min.js", "sources": ["../../src/cdk/observers/observe-content.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  NgModule,\n  Output,\n  Input,\n  EventEmitter,\n  OnDestroy,\n  AfterContentInit,\n  Injectable,\n  NgZone,\n  OnChanges,\n  SimpleChanges,\n} from '@angular/core';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {Subject} from 'rxjs/Subject';\nimport {debounceTime} from 'rxjs/operators/debounceTime';\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\n@Injectable()\nexport class MutationObserverFactory {\n  create(callback: MutationCallback): MutationObserver | null {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n}\n\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\n@Directive({\n  selector: '[cdkObserveContent]',\n  exportAs: 'cdkObserveContent',\n})\nexport class CdkObserveContent implements AfterContentInit, OnChanges, OnDestroy {\n  private _observer: MutationObserver | null;\n  private _disabled = false;\n\n  /** Event emitted for each change in the element's content. */\n  @Output('cdkObserveContent') event = new EventEmitter<MutationRecord[]>();\n\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  @Input('cdkObserveContentDisabled')\n  get disabled() { return this._disabled; }\n  set disabled(value: any) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n\n  /** Used for debouncing the emitted values to the observeContent event. */\n  private _debouncer = new Subject<MutationRecord[]>();\n\n  /** Debounce interval for emitting the changes. */\n  @Input() debounce: number;\n\n  constructor(\n    private _mutationObserverFactory: MutationObserverFactory,\n    private _elementRef: ElementRef,\n    private _ngZone: NgZone) { }\n\n  ngAfterContentInit() {\n    if (this.debounce > 0) {\n      this._ngZone.runOutsideAngular(() => {\n        this._debouncer.pipe(debounceTime(this.debounce))\n            .subscribe((mutations: MutationRecord[]) => this.event.emit(mutations));\n      });\n    } else {\n      this._debouncer.subscribe(mutations => this.event.emit(mutations));\n    }\n\n    this._observer = this._ngZone.runOutsideAngular(() => {\n      return this._mutationObserverFactory.create((mutations: MutationRecord[]) => {\n        this._debouncer.next(mutations);\n      });\n    });\n\n    if (!this.disabled) {\n      this._enable();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['disabled']) {\n      changes['disabled'].currentValue ? this._disable() : this._enable();\n    }\n  }\n\n  ngOnDestroy() {\n    this._disable();\n    this._debouncer.complete();\n  }\n\n  private _disable() {\n    if (this._observer) {\n      this._observer.disconnect();\n    }\n  }\n\n  private _enable() {\n    if (this._observer) {\n      this._observer.observe(this._elementRef.nativeElement, {\n        characterData: true,\n        childList: true,\n        subtree: true\n      });\n    }\n  }\n}\n\n\n@NgModule({\n  exports: [CdkObserveContent],\n  declarations: [CdkObserveContent],\n  providers: [MutationObserverFactory]\n})\nexport class ObserversModule {}\n"], "names": ["MutationObserverFactory", "prototype", "create", "callback", "MutationObserver", "type", "Injectable", "CdkObserveContent", "_mutationObserverFactory", "_elementRef", "_ngZone", "this", "_disabled", "event", "EventEmitter", "_debouncer", "Subject", "Object", "defineProperty", "value", "coerceBooleanProperty", "ngAfterContentInit", "_this", "debounce", "runOutsideAngular", "pipe", "debounceTime", "subscribe", "mutations", "emit", "_observer", "next", "disabled", "_enable", "ngOnChanges", "changes", "currentValue", "_disable", "ngOnDestroy", "complete", "disconnect", "observe", "nativeElement", "characterData", "childList", "subtree", "Directive", "args", "selector", "exportAs", "ElementRef", "NgZone", "Output", "Input", "ObserversModule", "NgModule", "exports", "declarations", "providers"], "mappings": ";;;;;;;kiBAAA,MAgCEA,GAAFC,UAAAC,OAAE,SAAOC,GACL,MAAmC,mBAArBC,kBAAmC,KAAO,GAAIA,kBAAiBD,mBAHjFE,KAACC,EAAAA,mDA9BDN,kBAoEE,QAAFO,GACYC,EACAC,EACAC,GAFAC,KAAZH,yBAAYA,EACAG,KAAZF,YAAYA,EACAE,KAAZD,QAAYA,EAxBZC,KAAAC,WAAsB,EAGtBD,KAAAE,MAAuC,GAAIC,GAAAA,aAa3CH,KAAAI,WAAuB,GAAIC,GAAAA,QA/D3B,MAyDAC,QAAAC,eAAMX,EAANN,UAAA,gBAAA,WAAmB,MAAOU,MAAKC,eAC7B,SAAaO,GACXR,KAAKC,UAAYQ,EAAAA,sBAAsBD,oCAczCZ,EAAFN,UAAAoB,mBAAE,WAAA,GAAFC,GAAAX,IACQA,MAAKY,SAAW,EAClBZ,KAAKD,QAAQc,kBAAkB,WAC7BF,EAAKP,WAAWU,KAAKC,EAAAA,aAAaJ,EAAKC,WAClCI,UAAU,SAACC,GAAgC,MAAAN,GAAKT,MAAMgB,KAAKD,OAGlEjB,KAAKI,WAAWY,UAAU,SAAAC,GAAa,MAAAN,GAAKT,MAAMgB,KAAKD,KAGzDjB,KAAKmB,UAAYnB,KAAKD,QAAQc,kBAAkB,WAC9C,MAAOF,GAAKd,yBAAyBN,OAAO,SAAC0B,GAC3CN,EAAKP,WAAWgB,KAAKH,OAIpBjB,KAAKqB,UACRrB,KAAKsB,WAIT1B,EAAFN,UAAAiC,YAAE,SAAYC,GACNA,EAAkB,WACpBA,EAAkB,SAAEC,aAAezB,KAAK0B,WAAa1B,KAAKsB,YAI9D1B,EAAFN,UAAAqC,YAAE,WACE3B,KAAK0B,WACL1B,KAAKI,WAAWwB,YAGVhC,EAAVN,UAAAoC,oBACQ1B,KAAKmB,WACPnB,KAAKmB,UAAUU,cAIXjC,EAAVN,UAAAgC,mBACQtB,KAAKmB,WACPnB,KAAKmB,UAAUW,QAAQ9B,KAAKF,YAAYiC,eACtCC,eAAe,EACfC,WAAW,EACXC,SAAS,oBA3EjBxC,KAACyC,EAAAA,UAADC,OACEC,SAAU,sBACVC,SAAU,4DAZZ5C,KAAaL,IArBbK,KAAE6C,EAAAA,aAQF7C,KAAE8C,EAAAA,4BAgCFtC,QAAAR,KAAG+C,EAAAA,OAAHL,MAAU,uBAMVf,WAAA3B,KAAGgD,EAAAA,MAAHN,MAAS,+BAUTxB,WAAAlB,KAAGgD,EAAAA,SAlEH9C,KA6CA+C,EAAA,yBA7CA,sBA2HAjD,KAACkD,EAAAA,SAADR,OACES,SAAUjD,GACVkD,cAAelD,GACfmD,WAAY1D,6CA9HdsD"}