{"_from": "@angular/cdk@^5.2.1", "_id": "@angular/cdk@5.2.5", "_inBundle": false, "_integrity": "sha512-GN8m1d+VcCE9+Bgwv06Y8YJKyZ0i9ZIq2ZPBcJYt+KVgnVVRg4JkyUNxud07LNsvzOX22DquHqmIZiC4hAG7Ag==", "_location": "/@angular/cdk", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@angular/cdk@^5.2.1", "name": "@angular/cdk", "escapedName": "@angular%2fcdk", "scope": "@angular", "rawSpec": "^5.2.1", "saveSpec": null, "fetchSpec": "^5.2.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@angular/cdk/-/cdk-5.2.5.tgz", "_shasum": "cae2b12e1990a692dd267a73fdb1d49db37f9604", "_spec": "@angular/cdk@^5.2.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular", "bugs": {"url": "https://github.com/angular/material2/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.7.1"}, "deprecated": false, "description": "Angular Material Component Development Kit", "es2015": "./esm2015/cdk.js", "homepage": "https://github.com/angular/material2#readme", "keywords": ["angular", "cdk", "component", "development", "kit"], "license": "MIT", "main": "./bundles/cdk.umd.js", "module": "./esm5/cdk.es5.js", "name": "@angular/cdk", "peerDependencies": {"@angular/core": ">=5.0.0 <7.0.0", "@angular/common": ">=5.0.0 <7.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/angular/material2.git"}, "typings": "./cdk.d.ts", "version": "5.2.5"}