{"_from": "vm-browserify@^1.0.1", "_id": "vm-browserify@1.1.2", "_inBundle": false, "_integrity": "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==", "_location": "/vm-browserify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vm-browserify@^1.0.1", "name": "vm-browserify", "escapedName": "vm-browserify", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/vm-browserify/-/vm-browserify-1.1.2.tgz", "_shasum": "78641c488b8e6ca91a75f511e7a3b32a86e5dda0", "_spec": "vm-browserify@^1.0.1", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\node-libs-browser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/vm-browserify/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "vm module for the browser", "devDependencies": {"browserify": "^16.1.1", "tape": "^4.11.0", "tape-run": "^6.0.1"}, "homepage": "https://github.com/substack/vm-browserify#readme", "keywords": ["vm", "browser", "eval"], "license": "MIT", "main": "index.js", "name": "vm-browserify", "repository": {"type": "git", "url": "git+ssh://**************/substack/vm-browserify.git"}, "scripts": {"test": "browserify test/vm.js | tape-run"}, "version": "1.1.2"}