/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/cdk/collections"),require("@angular/cdk/coercion")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/collections","@angular/cdk/coercion"],t):t((e.ng=e.ng||{},e.ng.cdk=e.ng.cdk||{},e.ng.cdk.accordion=e.ng.cdk.accordion||{}),e.ng.core,e.ng.cdk.collections,e.ng.cdk.coercion)}(this,function(e,t,o,n){"use strict";var i=0,r=function(){function e(){this.id="cdk-accordion-"+i++,this._multi=!1}return Object.defineProperty(e.prototype,"multi",{get:function(){return this._multi},set:function(e){this._multi=n.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),e.decorators=[{type:t.Directive,args:[{selector:"cdk-accordion, [cdkAccordion]",exportAs:"cdkAccordion"}]}],e.ctorParameters=function(){return[]},e.propDecorators={multi:[{type:t.Input}]},e}(),c=0,d=function(){function e(e,o,n){var i=this;this.accordion=e,this._changeDetectorRef=o,this._expansionDispatcher=n,this.closed=new t.EventEmitter,this.opened=new t.EventEmitter,this.destroyed=new t.EventEmitter,this.expandedChange=new t.EventEmitter,this.id="cdk-accordion-child-"+c++,this._expanded=!1,this._disabled=!1,this._removeUniqueSelectionListener=function(){},this._removeUniqueSelectionListener=n.listen(function(e,t){i.accordion&&!i.accordion.multi&&i.accordion.id===t&&i.id!==e&&(i.expanded=!1)})}return Object.defineProperty(e.prototype,"expanded",{get:function(){return this._expanded},set:function(e){if(e=n.coerceBooleanProperty(e),this._expanded!==e){if(this._expanded=e,this.expandedChange.emit(e),e){this.opened.emit();var t=this.accordion?this.accordion.id:this.id;this._expansionDispatcher.notify(this.id,t)}else this.closed.emit();this._changeDetectorRef.markForCheck()}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"disabled",{get:function(){return this._disabled},set:function(e){this._disabled=n.coerceBooleanProperty(e)},enumerable:!0,configurable:!0}),e.prototype.ngOnDestroy=function(){this.destroyed.emit(),this._removeUniqueSelectionListener()},e.prototype.toggle=function(){this.disabled||(this.expanded=!this.expanded)},e.prototype.close=function(){this.disabled||(this.expanded=!1)},e.prototype.open=function(){this.disabled||(this.expanded=!0)},e.decorators=[{type:t.Directive,args:[{selector:"cdk-accordion-item",exportAs:"cdkAccordionItem"}]}],e.ctorParameters=function(){return[{type:r,decorators:[{type:t.Optional}]},{type:t.ChangeDetectorRef},{type:o.UniqueSelectionDispatcher}]},e.propDecorators={closed:[{type:t.Output}],opened:[{type:t.Output}],destroyed:[{type:t.Output}],expandedChange:[{type:t.Output}],expanded:[{type:t.Input}],disabled:[{type:t.Input}]},e}(),s=function(){function e(){}return e.decorators=[{type:t.NgModule,args:[{exports:[r,d],declarations:[r,d],providers:[o.UNIQUE_SELECTION_DISPATCHER_PROVIDER]}]}],e.ctorParameters=function(){return[]},e}();e.CdkAccordionItem=d,e.CdkAccordion=r,e.CdkAccordionModule=s,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-accordion.umd.min.js.map
