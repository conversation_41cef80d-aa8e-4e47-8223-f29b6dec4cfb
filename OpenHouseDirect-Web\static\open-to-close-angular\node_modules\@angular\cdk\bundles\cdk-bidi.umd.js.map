{"version": 3, "file": "cdk-bidi.umd.js", "sources": ["../../src/cdk/bidi/bidi-module.ts", "../../src/cdk/bidi/dir.ts", "../../src/cdk/bidi/directionality.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {Dir} from './dir';\nimport {DIR_DOCUMENT, Directionality} from './directionality';\n\n\n@NgModule({\n  exports: [Dir],\n  declarations: [Dir],\n  providers: [\n    {provide: DIR_DOCUMENT, useExisting: DOCUMENT},\n    Directionality,\n  ]\n})\nexport class BidiModule { }\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  Output,\n  Input,\n  EventEmitter,\n  AfterContentInit,\n  OnDestroy,\n} from '@angular/core';\n\nimport {Direction, Directionality} from './directionality';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n@Directive({\n  selector: '[dir]',\n  providers: [{provide: Directionality, useExisting: Dir}],\n  host: {'[dir]': 'dir'},\n  exportAs: 'dir',\n})\nexport class Dir implements Directionality, AfterContentInit, OnDestroy {\n  _dir: Direction = 'ltr';\n\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Event emitted when the direction changes. */\n  @Output('dirChange') change = new EventEmitter<Direction>();\n\n  /** @docs-private */\n  @Input()\n  get dir(): Direction { return this._dir; }\n  set dir(v: Direction) {\n    const old = this._dir;\n    this._dir = v;\n    if (old !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n\n  /** Current layout direction of the element. */\n  get value(): Direction { return this.dir; }\n\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  EventEmitter,\n  Injectable,\n  Optional,\n  Inject,\n  InjectionToken,\n} from '@angular/core';\n\n\nexport type Direction = 'ltr' | 'rtl';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-brower because the unit tests\n * themselves use things like `querySelector` in test code.\n */\nexport const DIR_DOCUMENT = new InjectionToken<Document>('cdk-dir-doc');\n\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\n@Injectable()\nexport class Directionality {\n  /** The current 'ltr' or 'rtl' value. */\n  readonly value: Direction = 'ltr';\n\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  readonly change = new EventEmitter<Direction>();\n\n  constructor(@Optional() @Inject(DIR_DOCUMENT) _document?: any) {\n    if (_document) {\n      // TODO: handle 'auto' value -\n      // We still need to account for dir=\"auto\".\n      // It looks like HTMLElemenet.dir is also \"auto\" when that's set to the attribute,\n      // but getComputedStyle return either \"ltr\" or \"rtl\". avoiding getComputedStyle for now\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.value = (bodyDir || htmlDir || 'ltr') as Direction;\n    }\n  }\n}\n"], "names": ["DOCUMENT", "NgModule", "Input", "Output", "Directive", "EventEmitter", "Optional", "Inject", "Injectable", "InjectionToken"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AE6BA,IAAa,YAAY,GAAG,IAAIS,4BAAc,CAAW,aAAa,CAAC,CAAC;;;;;;IActE,SAAF,cAAA,CAAgD,SAAhD,EAAA;;;;QALA,IAAA,CAAA,KAAA,GAA8B,KAAK,CAAnC;;;;QAGA,IAAA,CAAA,MAAA,GAAoB,IAAIJ,0BAAY,EAAa,CAAjD;QAGI,IAAI,SAAS,EAAE;;;;;YAKb,qBAAM,OAAO,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;YAC3D,qBAAM,OAAO,GAAG,SAAS,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,GAAG,IAAI,CAAC;YACjF,IAAI,CAAC,KAAK,sBAAI,OAAO,IAAI,OAAO,IAAI,KAAK,EAAc,CAAC;SACzD;KACF;;QAlBH,EAAA,IAAA,EAACG,wBAAU,EAAX;;;;QAQA,EAAA,IAAA,EAAA,SAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAeF,sBAAQ,EAAvB,EAAA,EAAA,IAAA,EAA2BC,oBAAM,EAAjC,IAAA,EAAA,CAAkC,YAAY,EAA9C,EAAA,EAAA,EAAA;;IA3CA,OAAA,cAAA,CAAA;CAoCA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;QDJA,IAAA,CAAA,IAAA,GAAoB,KAAK,CAAzB;;;;QAGA,IAAA,CAAA,cAAA,GAAoC,KAAK,CAAzC;;;;QAGA,IAAA,CAAA,MAAA,GAAgC,IAAIF,0BAAY,EAAa,CAA7D;;IAIA,MAAA,CAAA,cAAA,CAAM,GAAN,CAAA,SAAA,EAAA,KAAS,EAAT;;;;;QAAA,YAAA,EAAyB,OAAO,IAAI,CAAC,IAAI,CAAC,EAA1C;;;;;QACE,UAAQ,CAAY,EAAtB;YACI,qBAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YACd,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC7B;SACF;;;;IAGD,MAAF,CAAA,cAAA,CAAM,GAAN,CAAA,SAAA,EAAA,OAAW,EAAX;;;;;;QAAE,YAAF,EAA2B,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE;;;KAA7C,CAAA,CAA6C;;;;;;IAG3C,GAAF,CAAA,SAAA,CAAA,kBAAoB;;;;IAAlB,YAAF;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC5B,CAAH;;;;IAEE,GAAF,CAAA,SAAA,CAAA,WAAa;;;IAAX,YAAF;QACI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KACxB,CAAH;;QApCA,EAAA,IAAA,EAACD,uBAAS,EAAV,IAAA,EAAA,CAAW;oBACT,QAAQ,EAAE,OAAO;oBACjB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,EAAC,CAAC;oBACxD,IAAI,EAAE,EAAC,OAAO,EAAE,KAAK,EAAC;oBACtB,QAAQ,EAAE,KAAK;iBAChB,EAAD,EAAA;;;;;QAQA,QAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,oBAAM,EAAT,IAAA,EAAA,CAAU,WAAW,EAArB,EAAA,EAAA;QAGA,KAAA,EAAA,CAAA,EAAA,IAAA,EAAGD,mBAAK,EAAR,EAAA;;IAzCA,OAAA,GAAA,CAAA;CA+BA,EAAA,CAAA,CAAA;;;;;;;ADvBA,IAAA,UAAA,kBAAA,YAAA;;;;QAMA,EAAA,IAAA,EAACD,sBAAQ,EAAT,IAAA,EAAA,CAAU;oBACR,OAAO,EAAE,CAAC,GAAG,CAAC;oBACd,YAAY,EAAE,CAAC,GAAG,CAAC;oBACnB,SAAS,EAAE;wBACT,EAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAED,wBAAQ,EAAC;wBAC9C,cAAc;qBACf;iBACF,EAAD,EAAA;;;;IArBA,OAAA,UAAA,CAAA;CAsBA,EAAA,CAAA,CAAA;;;;;;;;;"}