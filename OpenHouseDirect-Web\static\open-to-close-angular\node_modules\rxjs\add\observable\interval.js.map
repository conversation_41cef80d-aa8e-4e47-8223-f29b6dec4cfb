{"version": 3, "file": "interval.js", "sourceRoot": "", "sources": ["../../../src/add/observable/interval.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,yBAA2C,2BAA2B,CAAC,CAAA;AAEvE,uBAAU,CAAC,QAAQ,GAAG,mBAAc,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { interval as staticInterval } from '../../observable/interval';\n\nObservable.interval = staticInterval;\n\ndeclare module '../../Observable' {\n  namespace Observable {\n    export let interval: typeof staticInterval;\n  }\n}"]}