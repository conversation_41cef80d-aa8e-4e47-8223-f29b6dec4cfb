{"version": 3, "file": "hy.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/hy.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QAC1C;YACE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU;YACtD,QAAQ;SACT;QACD;YACE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ;YAC9C,UAAU,EAAE,QAAQ;SACrB;KACF;IACD;QACE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QAC1D,AAD2D;KAE5D;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['կգ․', 'կօ․', 'առվ', 'ցրկ', 'երկ', 'գշր'],\n    [\n      'կեսգիշեր', 'կեսօր', 'առավոտյան', 'ցերեկը', 'երեկոյան',\n      'գիշերը'\n    ],\n    [\n      'կեսգիշերին', 'կեսօրին', 'առավոտյան', 'ցերեկը',\n      'երեկոյան', 'գիշերը'\n    ]\n  ],\n  [\n    ['կեսգիշեր', 'կեսօր', 'առավոտ', 'ցերեկ', 'երեկո', 'գիշեր'],\n    ,\n  ],\n  [\n    '00:00', '12:00', ['06:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'],\n    ['00:00', '06:00']\n  ]\n];\n"]}