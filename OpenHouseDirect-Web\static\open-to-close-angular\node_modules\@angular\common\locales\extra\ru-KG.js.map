{"version": 3, "file": "ru-KG.js", "sourceRoot": "", "sources": ["../../../../../packages/common/locales/extra/ru-KG.ts"], "names": [], "mappings": ";;;;;;;AAWA,eAAe;IACb;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;QACjD,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;QACnD,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;KACxD;IACD;QACE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,AAAD;QACnD,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;KACxD;IACD;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;QAC5E,CAAC,OAAO,EAAE,OAAO,CAAC;KACnB;CACF,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY\n// See angular/tools/gulp-tasks/cldr/extract.js\n\nexport default [\n  [\n    ['полн.', 'полд.', 'утра', 'дня', 'веч.', 'ночи'],\n    ['полн.', 'полд.', 'утра', 'дня', 'вечера', 'ночи'],\n    ['полночь', 'полдень', 'утра', 'дня', 'вечера', 'ночи']\n  ],\n  [\n    ['полн.', 'полд.', 'утро', 'день', 'веч.', 'ночь'], ,\n    ['полночь', 'полдень', 'утро', 'день', 'вечер', 'ночь']\n  ],\n  [\n    '00:00', '12:00', ['04:00', '12:00'], ['12:00', '18:00'], ['18:00', '24:00'],\n    ['00:00', '04:00']\n  ]\n];\n"]}