/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@angular/core"),require("@angular/cdk/scrolling"),require("@angular/common"),require("@angular/cdk/bidi"),require("@angular/cdk/portal"),require("rxjs/operators/take"),require("rxjs/Subject"),require("rxjs/Subscription"),require("rxjs/operators/filter"),require("rxjs/observable/fromEvent"),require("@angular/cdk/coercion"),require("@angular/cdk/keycodes")):"function"==typeof define&&define.amd?define(["exports","@angular/core","@angular/cdk/scrolling","@angular/common","@angular/cdk/bidi","@angular/cdk/portal","rxjs/operators/take","rxjs/Subject","rxjs/Subscription","rxjs/operators/filter","rxjs/observable/fromEvent","@angular/cdk/coercion","@angular/cdk/keycodes"],e):e((t.ng=t.ng||{},t.ng.cdk=t.ng.cdk||{},t.ng.cdk.overlay=t.ng.cdk.overlay||{}),t.ng.core,t.ng.cdk.scrolling,t.ng.common,t.ng.cdk.bidi,t.ng.cdk.portal,t.Rx.operators,t.Rx,t.Rx,t.Rx.operators,t.Rx.Observable,t.ng.cdk.coercion,t.ng.cdk.keycodes)}(this,function(t,e,i,o,n,r,s,a,c,h,l,p,u){"use strict";function d(t,e){function i(){this.constructor=t}O(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}function f(){return Error("Scroll strategy has already been attached.")}function _(t,e){return e.some(function(e){var i=t.bottom<e.top,o=t.top>e.bottom,n=t.right<e.left,r=t.left>e.right;return i||o||n||r})}function y(t,e){return e.some(function(e){var i=t.top<e.top,o=t.bottom>e.bottom,n=t.left<e.left,r=t.right>e.right;return i||o||n||r})}function g(t){return"string"==typeof t?t:t+"px"}function v(t,e){return t||new L(e)}function b(t,e){return t||new F(e)}function m(t){return function(){return t.scrollStrategies.reposition()}}var O=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},k=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++){e=arguments[i];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t},w=function(){function t(){}return t.prototype.enable=function(){},t.prototype.disable=function(){},t.prototype.attach=function(){},t}(),E=function(){function t(t){var e=this;this.scrollStrategy=new w,this.panelClass="",this.hasBackdrop=!1,this.backdropClass="cdk-overlay-dark-backdrop",this.direction="ltr",t&&Object.keys(t).filter(function(e){return void 0!==t[e]}).forEach(function(i){return e[i]=t[i]})}return t}(),S=function(){function t(t,e,i,o){this.offsetX=i,this.offsetY=o,this.originX=t.originX,this.originY=t.originY,this.overlayX=e.overlayX,this.overlayY=e.overlayY}return t}(),C=function(){function t(){}return t}(),P=function(){function t(t,e){this.connectionPair=t,this.scrollableViewProperties=e}return t.ctorParameters=function(){return[{type:S},{type:C,decorators:[{type:e.Optional}]}]},t}(),R=function(){function t(t,e,i,o){var n=this;this._scrollDispatcher=t,this._ngZone=e,this._viewportRuler=i,this._config=o,this._scrollSubscription=null,this._detach=function(){n.disable(),n._overlayRef.hasAttached()&&n._ngZone.run(function(){return n._overlayRef.detach()})}}return t.prototype.attach=function(t){if(this._overlayRef)throw f();this._overlayRef=t},t.prototype.enable=function(){var t=this;if(!this._scrollSubscription){var e=this._scrollDispatcher.scrolled(0);this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=e.subscribe(function(){var e=t._viewportRuler.getViewportScrollPosition().top;Math.abs(e-t._initialScrollPosition)>t._config.threshold?t._detach():t._overlayRef.updatePosition()})):this._scrollSubscription=e.subscribe(this._detach)}},t.prototype.disable=function(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)},t}(),j=function(){function t(t,e){this._viewportRuler=t,this._previousHTMLStyles={top:"",left:""},this._isEnabled=!1,this._document=e}return t.prototype.attach=function(){},t.prototype.enable=function(){if(this._canBeEnabled()){var t=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=t.style.left||"",this._previousHTMLStyles.top=t.style.top||"",t.style.left=-this._previousScrollPosition.left+"px",t.style.top=-this._previousScrollPosition.top+"px",t.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}},t.prototype.disable=function(){if(this._isEnabled){var t=this._document.documentElement,e=this._document.body,i=t.style.scrollBehavior||"",o=e.style.scrollBehavior||"";this._isEnabled=!1,t.style.left=this._previousHTMLStyles.left,t.style.top=this._previousHTMLStyles.top,t.classList.remove("cdk-global-scrollblock"),t.style.scrollBehavior=e.style.scrollBehavior="auto",window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),t.style.scrollBehavior=i,e.style.scrollBehavior=o}},t.prototype._canBeEnabled=function(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;var t=this._document.body,e=this._viewportRuler.getViewportSize();return t.scrollHeight>e.height||t.scrollWidth>e.width},t}(),x=function(){function t(t,e,i,o){this._scrollDispatcher=t,this._viewportRuler=e,this._ngZone=i,this._config=o,this._scrollSubscription=null}return t.prototype.attach=function(t){if(this._overlayRef)throw f();this._overlayRef=t},t.prototype.enable=function(){var t=this;if(!this._scrollSubscription){var e=this._config?this._config.scrollThrottle:0;this._scrollSubscription=this._scrollDispatcher.scrolled(e).subscribe(function(){if(t._overlayRef.updatePosition(),t._config&&t._config.autoClose){var e=t._overlayRef.overlayElement.getBoundingClientRect(),i=t._viewportRuler.getViewportSize(),o=i.width,n=i.height;_(e,[{width:o,height:n,bottom:n,right:o,top:0,left:0}])&&(t.disable(),t._ngZone.run(function(){return t._overlayRef.detach()}))}})}},t.prototype.disable=function(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)},t}(),I=function(){function t(t,e,i,o){var n=this;this._scrollDispatcher=t,this._viewportRuler=e,this._ngZone=i,this.noop=function(){return new w},this.close=function(t){return new R(n._scrollDispatcher,n._ngZone,n._viewportRuler,t)},this.block=function(){return new j(n._viewportRuler,n._document)},this.reposition=function(t){return new x(n._scrollDispatcher,n._viewportRuler,n._ngZone,t)},this._document=o}return t.decorators=[{type:e.Injectable}],t.ctorParameters=function(){return[{type:i.ScrollDispatcher},{type:i.ViewportRuler},{type:e.NgZone},{type:void 0,decorators:[{type:e.Inject,args:[o.DOCUMENT]}]}]},t}(),X=function(){function t(t,e,i,o,n,r){this._portalOutlet=t,this._pane=e,this._config=i,this._ngZone=o,this._keyboardDispatcher=n,this._document=r,this._backdropElement=null,this._backdropClick=new a.Subject,this._attachments=new a.Subject,this._detachments=new a.Subject,this._keydownEvents=new a.Subject,i.scrollStrategy&&i.scrollStrategy.attach(this)}return Object.defineProperty(t.prototype,"overlayElement",{get:function(){return this._pane},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"backdropElement",{get:function(){return this._backdropElement},enumerable:!0,configurable:!0}),t.prototype.attach=function(t){var e=this,i=this._portalOutlet.attach(t);return this._config.positionStrategy&&this._config.positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._config.scrollStrategy&&this._config.scrollStrategy.enable(),this._ngZone.onStable.asObservable().pipe(s.take(1)).subscribe(function(){e.hasAttached()&&e.updatePosition()}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&(Array.isArray(this._config.panelClass)?this._config.panelClass.forEach(function(t){return e._pane.classList.add(t)}):this._pane.classList.add(this._config.panelClass)),this._attachments.next(),this._keyboardDispatcher.add(this),i},t.prototype.detach=function(){if(this.hasAttached()){this.detachBackdrop(),this._togglePointerEvents(!1),this._config.positionStrategy&&this._config.positionStrategy.detach&&this._config.positionStrategy.detach(),this._config.scrollStrategy&&this._config.scrollStrategy.disable();var t=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),t}},t.prototype.dispose=function(){var t=this.hasAttached();this._config.positionStrategy&&this._config.positionStrategy.dispose(),this._config.scrollStrategy&&this._config.scrollStrategy.disable(),this.detachBackdrop(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),t&&this._detachments.next(),this._detachments.complete()},t.prototype.hasAttached=function(){return this._portalOutlet.hasAttached()},t.prototype.backdropClick=function(){return this._backdropClick.asObservable()},t.prototype.attachments=function(){return this._attachments.asObservable()},t.prototype.detachments=function(){return this._detachments.asObservable()},t.prototype.keydownEvents=function(){return this._keydownEvents.asObservable()},t.prototype.getConfig=function(){return this._config},t.prototype.updatePosition=function(){this._config.positionStrategy&&this._config.positionStrategy.apply()},t.prototype.updateSize=function(t){this._config=k({},this._config,t),this._updateElementSize()},t.prototype.setDirection=function(t){this._config=k({},this._config,{direction:t}),this._updateElementDirection()},t.prototype._updateElementDirection=function(){this._pane.setAttribute("dir",this._config.direction)},t.prototype._updateElementSize=function(){(this._config.width||0===this._config.width)&&(this._pane.style.width=g(this._config.width)),(this._config.height||0===this._config.height)&&(this._pane.style.height=g(this._config.height)),(this._config.minWidth||0===this._config.minWidth)&&(this._pane.style.minWidth=g(this._config.minWidth)),(this._config.minHeight||0===this._config.minHeight)&&(this._pane.style.minHeight=g(this._config.minHeight)),(this._config.maxWidth||0===this._config.maxWidth)&&(this._pane.style.maxWidth=g(this._config.maxWidth)),(this._config.maxHeight||0===this._config.maxHeight)&&(this._pane.style.maxHeight=g(this._config.maxHeight))},t.prototype._togglePointerEvents=function(t){this._pane.style.pointerEvents=t?"auto":"none"},t.prototype._attachBackdrop=function(){var t=this;this._backdropElement=this._document.createElement("div"),this._backdropElement.classList.add("cdk-overlay-backdrop"),this._config.backdropClass&&this._backdropElement.classList.add(this._config.backdropClass),this._pane.parentElement.insertBefore(this._backdropElement,this._pane),this._backdropElement.addEventListener("click",function(e){return t._backdropClick.next(e)}),"undefined"!=typeof requestAnimationFrame?this._ngZone.runOutsideAngular(function(){requestAnimationFrame(function(){t._backdropElement&&t._backdropElement.classList.add("cdk-overlay-backdrop-showing")})}):this._backdropElement.classList.add("cdk-overlay-backdrop-showing")},t.prototype._updateStackingOrder=function(){this._pane.nextSibling&&this._pane.parentNode.appendChild(this._pane)},t.prototype.detachBackdrop=function(){var t=this,e=this._backdropElement;if(e){var i=function(){e&&e.parentNode&&e.parentNode.removeChild(e),t._backdropElement==e&&(t._backdropElement=null)};e.classList.remove("cdk-overlay-backdrop-showing"),this._config.backdropClass&&e.classList.remove(this._config.backdropClass),e.addEventListener("transitionend",i),e.style.pointerEvents="none",this._ngZone.runOutsideAngular(function(){setTimeout(i,500)})}},t}(),B=function(){function t(t,e,i,o,n){this._connectedTo=i,this._viewportRuler=o,this._document=n,this._dir="ltr",this._offsetX=0,this._offsetY=0,this.scrollables=[],this._resizeSubscription=c.Subscription.EMPTY,this._preferredPositions=[],this._applied=!1,this._positionLocked=!1,this._onPositionChange=new a.Subject,this._origin=this._connectedTo.nativeElement,this.withFallbackPosition(t,e)}return Object.defineProperty(t.prototype,"_isRtl",{get:function(){return"rtl"===this._dir},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onPositionChange",{get:function(){return this._onPositionChange.asObservable()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"positions",{get:function(){return this._preferredPositions},enumerable:!0,configurable:!0}),t.prototype.attach=function(t){var e=this;this._overlayRef=t,this._pane=t.overlayElement,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(function(){return e.apply()})},t.prototype.dispose=function(){this._applied=!1,this._resizeSubscription.unsubscribe(),this._onPositionChange.complete()},t.prototype.detach=function(){this._applied=!1,this._resizeSubscription.unsubscribe()},t.prototype.apply=function(){if(this._applied&&this._positionLocked&&this._lastConnectedPosition)return void this.recalculateLastPosition();this._applied=!0;for(var t,e,i=this._pane,o=this._origin.getBoundingClientRect(),n=i.getBoundingClientRect(),r=this._viewportRuler.getViewportSize(),s=0,a=this._preferredPositions;s<a.length;s++){var c=a[s],h=this._getOriginConnectionPoint(o,c),l=this._getOverlayPoint(h,n,r,c);if(l.fitsInViewport)return this._setElementPosition(i,n,l,c),void(this._lastConnectedPosition=c);(!t||t.visibleArea<l.visibleArea)&&(t=l,e=c)}this._setElementPosition(i,n,t,e)},t.prototype.recalculateLastPosition=function(){if(this._lastConnectedPosition){var t=this._origin.getBoundingClientRect(),e=this._pane.getBoundingClientRect(),i=this._viewportRuler.getViewportSize(),o=this._lastConnectedPosition||this._preferredPositions[0],n=this._getOriginConnectionPoint(t,o),r=this._getOverlayPoint(n,e,i,o);this._setElementPosition(this._pane,e,r,o)}},t.prototype.withScrollableContainers=function(t){this.scrollables=t},t.prototype.withFallbackPosition=function(t,e,i,o){var n=new S(t,e,i,o);return this._preferredPositions.push(n),this},t.prototype.withDirection=function(t){return this._dir=t,this},t.prototype.withOffsetX=function(t){return this._offsetX=t,this},t.prototype.withOffsetY=function(t){return this._offsetY=t,this},t.prototype.withLockedPosition=function(t){return this._positionLocked=t,this},t.prototype.withPositions=function(t){return this._preferredPositions=t.slice(),this},t.prototype.setOrigin=function(t){return this._origin=t.nativeElement,this},t.prototype._getStartX=function(t){return this._isRtl?t.right:t.left},t.prototype._getEndX=function(t){return this._isRtl?t.left:t.right},t.prototype._getOriginConnectionPoint=function(t,e){var i,o=this._getStartX(t),n=this._getEndX(t);i="center"==e.originX?o+t.width/2:"start"==e.originX?o:n;var r;return r="center"==e.originY?t.top+t.height/2:"top"==e.originY?t.top:t.bottom,{x:i,y:r}},t.prototype._getOverlayPoint=function(t,e,i,o){var n;n="center"==o.overlayX?-e.width/2:"start"===o.overlayX?this._isRtl?-e.width:0:this._isRtl?0:-e.width;var r;r="center"==o.overlayY?-e.height/2:"top"==o.overlayY?0:-e.height;var s=void 0===o.offsetX?this._offsetX:o.offsetX,a=void 0===o.offsetY?this._offsetY:o.offsetY,c=t.x+n+s,h=t.y+r+a,l=0-c,p=c+e.width-i.width,u=0-h,d=h+e.height-i.height,f=this._subtractOverflows(e.width,l,p),_=this._subtractOverflows(e.height,u,d),y=f*_;return{x:c,y:h,fitsInViewport:e.width*e.height===y,visibleArea:y}},t.prototype._getScrollVisibility=function(t){var e=this._origin.getBoundingClientRect(),i=t.getBoundingClientRect(),o=this.scrollables.map(function(t){return t.getElementRef().nativeElement.getBoundingClientRect()});return{isOriginClipped:y(e,o),isOriginOutsideView:_(e,o),isOverlayClipped:y(i,o),isOverlayOutsideView:_(i,o)}},t.prototype._setElementPosition=function(t,e,i,o){var n,r="bottom"===o.overlayY?"bottom":"top",s="top"===r?i.y:this._document.documentElement.clientHeight-(i.y+e.height);n="rtl"===this._dir?"end"===o.overlayX?"left":"right":"end"===o.overlayX?"right":"left";var a="left"===n?i.x:this._document.documentElement.clientWidth-(i.x+e.width);["top","bottom","left","right"].forEach(function(e){return t.style[e]=null}),t.style[r]=s+"px",t.style[n]=a+"px";var c=this._getScrollVisibility(t),h=new P(o,c);this._onPositionChange.next(h)},t.prototype._subtractOverflows=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];return e.reduce(function(t,e){return t-Math.max(e,0)},t)},t}(),Y=function(){function t(t){this._document=t,this._cssPosition="static",this._topOffset="",this._bottomOffset="",this._leftOffset="",this._rightOffset="",this._alignItems="",this._justifyContent="",this._width="",this._height="",this._wrapper=null}return t.prototype.attach=function(t){var e=t.getConfig();this._overlayRef=t,this._width&&!e.width&&t.updateSize({width:this._width}),this._height&&!e.height&&t.updateSize({height:this._height})},t.prototype.top=function(t){return void 0===t&&(t=""),this._bottomOffset="",this._topOffset=t,this._alignItems="flex-start",this},t.prototype.left=function(t){return void 0===t&&(t=""),this._rightOffset="",this._leftOffset=t,this._justifyContent="flex-start",this},t.prototype.bottom=function(t){return void 0===t&&(t=""),this._topOffset="",this._bottomOffset=t,this._alignItems="flex-end",this},t.prototype.right=function(t){return void 0===t&&(t=""),this._leftOffset="",this._rightOffset=t,this._justifyContent="flex-end",this},t.prototype.width=function(t){return void 0===t&&(t=""),this._overlayRef?this._overlayRef.updateSize({width:t}):this._width=t,this},t.prototype.height=function(t){return void 0===t&&(t=""),this._overlayRef?this._overlayRef.updateSize({height:t}):this._height=t,this},t.prototype.centerHorizontally=function(t){return void 0===t&&(t=""),this.left(t),this._justifyContent="center",this},t.prototype.centerVertically=function(t){return void 0===t&&(t=""),this.top(t),this._alignItems="center",this},t.prototype.apply=function(){if(this._overlayRef.hasAttached()){var t=this._overlayRef.overlayElement;!this._wrapper&&t.parentNode&&(this._wrapper=this._document.createElement("div"),this._wrapper.classList.add("cdk-global-overlay-wrapper"),t.parentNode.insertBefore(this._wrapper,t),this._wrapper.appendChild(t));var e=t.style,i=t.parentNode.style,o=this._overlayRef.getConfig();e.position=this._cssPosition,e.marginLeft="100%"===o.width?"0":this._leftOffset,e.marginTop="100%"===o.height?"0":this._topOffset,e.marginBottom=this._bottomOffset,e.marginRight=this._rightOffset,i.justifyContent="100%"===o.width?"flex-start":this._justifyContent,i.alignItems="100%"===o.height?"flex-start":this._alignItems}},t.prototype.dispose=function(){this._wrapper&&this._wrapper.parentNode&&(this._wrapper.parentNode.removeChild(this._wrapper),this._wrapper=null)},t}(),D=function(){function t(t,e){this._viewportRuler=t,this._document=e}return t.prototype.global=function(){return new Y(this._document)},t.prototype.connectedTo=function(t,e,i){return new B(e,i,t,this._viewportRuler,this._document)},t.decorators=[{type:e.Injectable}],t.ctorParameters=function(){return[{type:i.ViewportRuler},{type:void 0,decorators:[{type:e.Inject,args:[o.DOCUMENT]}]}]},t}(),L=function(){function t(t){this._document=t,this._attachedOverlays=[]}return t.prototype.ngOnDestroy=function(){this._unsubscribeFromKeydownEvents()},t.prototype.add=function(t){this._keydownEventSubscription||this._subscribeToKeydownEvents(),this._attachedOverlays.push(t)},t.prototype.remove=function(t){var e=this._attachedOverlays.indexOf(t);e>-1&&this._attachedOverlays.splice(e,1),0===this._attachedOverlays.length&&this._unsubscribeFromKeydownEvents()},t.prototype._subscribeToKeydownEvents=function(){var t=this,e=l.fromEvent(this._document.body,"keydown",!0);this._keydownEventSubscription=e.pipe(h.filter(function(){return!!t._attachedOverlays.length})).subscribe(function(e){t._selectOverlayFromEvent(e)._keydownEvents.next(e)})},t.prototype._unsubscribeFromKeydownEvents=function(){this._keydownEventSubscription&&(this._keydownEventSubscription.unsubscribe(),this._keydownEventSubscription=null)},t.prototype._selectOverlayFromEvent=function(t){return this._attachedOverlays.find(function(e){return e.overlayElement===t.target||e.overlayElement.contains(t.target)})||this._attachedOverlays[this._attachedOverlays.length-1]},t.decorators=[{type:e.Injectable}],t.ctorParameters=function(){return[{type:void 0,decorators:[{type:e.Inject,args:[o.DOCUMENT]}]}]},t}(),H={provide:L,deps:[[new e.Optional,new e.SkipSelf,L],o.DOCUMENT],useFactory:v},F=function(){function t(t){this._document=t}return t.prototype.ngOnDestroy=function(){this._containerElement&&this._containerElement.parentNode&&this._containerElement.parentNode.removeChild(this._containerElement)},t.prototype.getContainerElement=function(){return this._containerElement||this._createContainer(),this._containerElement},t.prototype._createContainer=function(){var t=this._document.createElement("div");t.classList.add("cdk-overlay-container"),this._document.body.appendChild(t),this._containerElement=t},t.decorators=[{type:e.Injectable}],t.ctorParameters=function(){return[{type:void 0,decorators:[{type:e.Inject,args:[o.DOCUMENT]}]}]},t}(),M={provide:F,deps:[[new e.Optional,new e.SkipSelf,F],o.DOCUMENT],useFactory:b},T=0,V=function(){function t(t,e,i,o,n,r,s,a,c){this.scrollStrategies=t,this._overlayContainer=e,this._componentFactoryResolver=i,this._positionBuilder=o,this._keyboardDispatcher=n,this._appRef=r,this._injector=s,this._ngZone=a,this._document=c}return t.prototype.create=function(t){var e=this._createPaneElement(),i=this._createPortalOutlet(e);return new X(i,e,new E(t),this._ngZone,this._keyboardDispatcher,this._document)},t.prototype.position=function(){return this._positionBuilder},t.prototype._createPaneElement=function(){var t=this._document.createElement("div");return t.id="cdk-overlay-"+T++,t.classList.add("cdk-overlay-pane"),this._overlayContainer.getContainerElement().appendChild(t),t},t.prototype._createPortalOutlet=function(t){return new r.DomPortalOutlet(t,this._componentFactoryResolver,this._appRef,this._injector)},t.decorators=[{type:e.Injectable}],t.ctorParameters=function(){return[{type:I},{type:F},{type:e.ComponentFactoryResolver},{type:D},{type:L},{type:e.ApplicationRef},{type:e.Injector},{type:e.NgZone},{type:void 0,decorators:[{type:e.Inject,args:[o.DOCUMENT]}]}]},t}(),W=[new S({originX:"start",originY:"bottom"},{overlayX:"start",overlayY:"top"}),new S({originX:"start",originY:"top"},{overlayX:"start",overlayY:"bottom"}),new S({originX:"end",originY:"top"},{overlayX:"end",overlayY:"bottom"}),new S({originX:"end",originY:"bottom"},{overlayX:"end",overlayY:"top"})],A=new e.InjectionToken("cdk-connected-overlay-scroll-strategy"),z={provide:A,deps:[V],useFactory:m},N=function(){function t(t){this.elementRef=t}return t.decorators=[{type:e.Directive,args:[{selector:"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]",exportAs:"cdkOverlayOrigin"}]}],t.ctorParameters=function(){return[{type:e.ElementRef}]},t}(),Z=function(){function t(t,i,o,n,s){this._overlay=t,this._scrollStrategy=n,this._dir=s,this._hasBackdrop=!1,this._backdropSubscription=c.Subscription.EMPTY,this._offsetX=0,this._offsetY=0,this.scrollStrategy=this._scrollStrategy(),this.open=!1,this.backdropClick=new e.EventEmitter,this.positionChange=new e.EventEmitter,this.attach=new e.EventEmitter,this.detach=new e.EventEmitter,this._templatePortal=new r.TemplatePortal(i,o)}return Object.defineProperty(t.prototype,"offsetX",{get:function(){return this._offsetX},set:function(t){this._offsetX=t,this._position&&this._position.withOffsetX(t)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"offsetY",{get:function(){return this._offsetY},set:function(t){this._offsetY=t,this._position&&this._position.withOffsetY(t)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hasBackdrop",{get:function(){return this._hasBackdrop},set:function(t){this._hasBackdrop=p.coerceBooleanProperty(t)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedOrigin",{get:function(){return this.origin},set:function(t){this.origin=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedPositions",{get:function(){return this.positions},set:function(t){this.positions=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedOffsetX",{get:function(){return this.offsetX},set:function(t){this.offsetX=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedOffsetY",{get:function(){return this.offsetY},set:function(t){this.offsetY=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedWidth",{get:function(){return this.width},set:function(t){this.width=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedHeight",{get:function(){return this.height},set:function(t){this.height=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedMinWidth",{get:function(){return this.minWidth},set:function(t){this.minWidth=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedMinHeight",{get:function(){return this.minHeight},set:function(t){this.minHeight=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedBackdropClass",{get:function(){return this.backdropClass},set:function(t){this.backdropClass=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedScrollStrategy",{get:function(){return this.scrollStrategy},set:function(t){this.scrollStrategy=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedOpen",{get:function(){return this.open},set:function(t){this.open=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_deprecatedHasBackdrop",{get:function(){return this.hasBackdrop},set:function(t){this.hasBackdrop=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"overlayRef",{get:function(){return this._overlayRef},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dir",{get:function(){return this._dir?this._dir.value:"ltr"},enumerable:!0,configurable:!0}),t.prototype.ngOnDestroy=function(){this._destroyOverlay()},t.prototype.ngOnChanges=function(t){this._position&&((t.positions||t._deprecatedPositions)&&this._position.withPositions(this.positions),(t.origin||t._deprecatedOrigin)&&(this._position.setOrigin(this.origin.elementRef),this.open&&this._position.apply())),(t.open||t._deprecatedOpen)&&(this.open?this._attachOverlay():this._detachOverlay())},t.prototype._createOverlay=function(){this.positions&&this.positions.length||(this.positions=W),this._overlayRef=this._overlay.create(this._buildConfig())},t.prototype._buildConfig=function(){var t=this._position=this._createPositionStrategy(),e=new E({positionStrategy:t,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop});return(this.width||0===this.width)&&(e.width=this.width),(this.height||0===this.height)&&(e.height=this.height),(this.minWidth||0===this.minWidth)&&(e.minWidth=this.minWidth),(this.minHeight||0===this.minHeight)&&(e.minHeight=this.minHeight),this.backdropClass&&(e.backdropClass=this.backdropClass),e},t.prototype._createPositionStrategy=function(){for(var t=this,e=this.positions[0],i={originX:e.originX,originY:e.originY},o={overlayX:e.overlayX,overlayY:e.overlayY},n=this._overlay.position().connectedTo(this.origin.elementRef,i,o).withOffsetX(this.offsetX).withOffsetY(this.offsetY),r=1;r<this.positions.length;r++)n.withFallbackPosition({originX:this.positions[r].originX,originY:this.positions[r].originY},{overlayX:this.positions[r].overlayX,overlayY:this.positions[r].overlayY});return n.onPositionChange.subscribe(function(e){return t.positionChange.emit(e)}),n},t.prototype._attachOverlay=function(){var t=this;this._overlayRef?this._overlayRef.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}):(this._createOverlay(),this._overlayRef.keydownEvents().subscribe(function(e){e.keyCode===u.ESCAPE&&t._detachOverlay()})),this._position.withDirection(this.dir),this._overlayRef.setDirection(this.dir),this._overlayRef.hasAttached()||(this._overlayRef.attach(this._templatePortal),this.attach.emit()),this.hasBackdrop&&(this._backdropSubscription=this._overlayRef.backdropClick().subscribe(function(){t.backdropClick.emit()}))},t.prototype._detachOverlay=function(){this._overlayRef&&(this._overlayRef.detach(),this.detach.emit()),this._backdropSubscription.unsubscribe()},t.prototype._destroyOverlay=function(){this._overlayRef&&this._overlayRef.dispose(),this._backdropSubscription.unsubscribe()},t.decorators=[{type:e.Directive,args:[{selector:"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]",exportAs:"cdkConnectedOverlay"}]}],t.ctorParameters=function(){return[{type:V},{type:e.TemplateRef},{type:e.ViewContainerRef},{type:void 0,decorators:[{type:e.Inject,args:[A]}]},{type:n.Directionality,decorators:[{type:e.Optional}]}]},t.propDecorators={origin:[{type:e.Input,args:["cdkConnectedOverlayOrigin"]}],positions:[{type:e.Input,args:["cdkConnectedOverlayPositions"]}],offsetX:[{type:e.Input,args:["cdkConnectedOverlayOffsetX"]}],offsetY:[{type:e.Input,args:["cdkConnectedOverlayOffsetY"]}],width:[{type:e.Input,args:["cdkConnectedOverlayWidth"]}],height:[{type:e.Input,args:["cdkConnectedOverlayHeight"]}],minWidth:[{type:e.Input,args:["cdkConnectedOverlayMinWidth"]}],minHeight:[{type:e.Input,args:["cdkConnectedOverlayMinHeight"]}],backdropClass:[{type:e.Input,args:["cdkConnectedOverlayBackdropClass"]}],scrollStrategy:[{type:e.Input,args:["cdkConnectedOverlayScrollStrategy"]}],open:[{type:e.Input,args:["cdkConnectedOverlayOpen"]}],hasBackdrop:[{type:e.Input,args:["cdkConnectedOverlayHasBackdrop"]}],_deprecatedOrigin:[{type:e.Input,args:["origin"]}],_deprecatedPositions:[{type:e.Input,args:["positions"]}],_deprecatedOffsetX:[{type:e.Input,args:["offsetX"]}],_deprecatedOffsetY:[{type:e.Input,args:["offsetY"]}],_deprecatedWidth:[{type:e.Input,args:["width"]}],_deprecatedHeight:[{type:e.Input,args:["height"]}],_deprecatedMinWidth:[{type:e.Input,args:["minWidth"]}],_deprecatedMinHeight:[{type:e.Input,args:["minHeight"]}],_deprecatedBackdropClass:[{type:e.Input,args:["backdropClass"]}],_deprecatedScrollStrategy:[{type:e.Input,args:["scrollStrategy"]}],_deprecatedOpen:[{type:e.Input,args:["open"]}],_deprecatedHasBackdrop:[{type:e.Input,args:["hasBackdrop"]}],backdropClick:[{type:e.Output}],positionChange:[{type:e.Output}],attach:[{type:e.Output}],detach:[{type:e.Output}]},t}(),q=[V,D,H,i.VIEWPORT_RULER_PROVIDER,M,z],U=function(){function t(){}return t.decorators=[{type:e.NgModule,args:[{imports:[n.BidiModule,r.PortalModule,i.ScrollDispatchModule],exports:[Z,N,i.ScrollDispatchModule],declarations:[Z,N],providers:[q,I]}]}],t.ctorParameters=function(){return[]},t}(),K=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return d(i,t),i.prototype._createContainer=function(){var e=this;t.prototype._createContainer.call(this),this._adjustParentForFullscreenChange(),this._addFullscreenChangeListener(function(){return e._adjustParentForFullscreenChange()})},i.prototype._adjustParentForFullscreenChange=function(){if(this._containerElement){(this.getFullscreenElement()||document.body).appendChild(this._containerElement)}},i.prototype._addFullscreenChangeListener=function(t){document.fullscreenEnabled?document.addEventListener("fullscreenchange",t):document.webkitFullscreenEnabled?document.addEventListener("webkitfullscreenchange",t):document.mozFullScreenEnabled?document.addEventListener("mozfullscreenchange",t):document.msFullscreenEnabled&&document.addEventListener("MSFullscreenChange",t)},i.prototype.getFullscreenElement=function(){
return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||null},i.decorators=[{type:e.Injectable}],i.ctorParameters=function(){return[]},i}(F);t.Overlay=V,t.OverlayContainer=F,t.CdkOverlayOrigin=N,t.CdkConnectedOverlay=Z,t.FullscreenOverlayContainer=K,t.OverlayRef=X,t.ViewportRuler=i.ViewportRuler,t.OverlayKeyboardDispatcher=L,t.OverlayPositionBuilder=D,t.GlobalPositionStrategy=Y,t.ConnectedPositionStrategy=B,t.VIEWPORT_RULER_PROVIDER=i.VIEWPORT_RULER_PROVIDER,t.ConnectedOverlayDirective=Z,t.OverlayOrigin=N,t.OverlayConfig=E,t.ConnectionPositionPair=S,t.ScrollingVisibility=C,t.ConnectedOverlayPositionChange=P,t.CdkScrollable=i.CdkScrollable,t.ScrollDispatcher=i.ScrollDispatcher,t.ScrollStrategyOptions=I,t.RepositionScrollStrategy=x,t.CloseScrollStrategy=R,t.NoopScrollStrategy=w,t.BlockScrollStrategy=j,t.OVERLAY_PROVIDERS=q,t.OverlayModule=U,t.ɵg=H,t.ɵf=v,t.ɵb=M,t.ɵa=b,t.ɵc=A,t.ɵe=z,t.ɵd=m,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=cdk-overlay.umd.min.js.map
