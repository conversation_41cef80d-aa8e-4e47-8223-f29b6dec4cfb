{"version": 3, "file": "retryWhen.js", "sourceRoot": "", "sources": ["../../src/operators/retryWhen.ts"], "names": [], "mappings": ";;;;;;AAGA,wBAAwB,YAAY,CAAC,CAAA;AAErC,yBAAyB,kBAAkB,CAAC,CAAA;AAC5C,4BAA4B,qBAAqB,CAAC,CAAA;AAElD,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAI9D;;;;;;;;;;;;;GAaG;AACH,mBAA6B,QAAsD;IACjF,MAAM,CAAC,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,EAApD,CAAoD,CAAC;AACzF,CAAC;AAFe,iBAAS,YAExB,CAAA;AAED;IACE,2BAAsB,QAAsD,EACzD,MAAqB;QADlB,aAAQ,GAAR,QAAQ,CAA8C;QACzD,WAAM,GAAN,MAAM,CAAe;IACxC,CAAC;IAED,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3F,CAAC;IACH,wBAAC;AAAD,CAAC,AARD,IAQC;AAED;;;;GAIG;AACH;IAAwC,uCAAqB;IAM3D,6BAAY,WAA0B,EAClB,QAAsD,EACtD,MAAqB;QACvC,kBAAM,WAAW,CAAC,CAAC;QAFD,aAAQ,GAAR,QAAQ,CAA8C;QACtD,WAAM,GAAN,MAAM,CAAe;IAEzC,CAAC;IAED,mCAAK,GAAL,UAAM,GAAQ;QACZ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAEpB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,IAAI,OAAO,GAAQ,IAAI,CAAC,OAAO,CAAC;YAChC,IAAI,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAEnD,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBACb,MAAM,GAAG,IAAI,iBAAO,EAAE,CAAC;gBACvB,OAAO,GAAG,mBAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC1C,EAAE,CAAC,CAAC,OAAO,KAAK,yBAAW,CAAC,CAAC,CAAC;oBAC5B,MAAM,CAAC,gBAAK,CAAC,KAAK,YAAC,yBAAW,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC;gBACD,mBAAmB,GAAG,qCAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC;YAAC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAClC,CAAC;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAED,oCAAoC,CAAC,0CAAY,GAAZ;QACnC,IAAA,SAA4C,EAApC,kBAAM,EAAE,4CAAmB,CAAU;QAC7C,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;QACD,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACxB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,wCAAU,GAAV,UAAW,UAAa,EAAE,UAAa,EAC5B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACxC,IAAA,SAAqD,EAA7C,kBAAM,EAAE,oBAAO,EAAE,4CAAmB,CAAU;QACtD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAE/C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IACH,0BAAC;AAAD,CAAC,AAtED,CAAwC,iCAAe,GAsEtD", "sourcesContent": ["import { Operator } from '../Operator';\nimport { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { Subscription, TeardownLogic } from '../Subscription';\nimport { tryCatch } from '../util/tryCatch';\nimport { errorObject } from '../util/errorObject';\n\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\n\nimport { MonoTypeOperatorFunction } from '../interfaces';\n\n/**\n * Returns an Observable that mirrors the source Observable with the exception of an `error`. If the source Observable\n * calls `error`, this method will emit the Throwable that caused the error to the Observable returned from `notifier`.\n * If that Observable calls `complete` or `error` then this method will call `complete` or `error` on the child\n * subscription. Otherwise this method will resubscribe to the source Observable.\n *\n * <img src=\"./img/retryWhen.png\" width=\"100%\">\n *\n * @param {function(errors: Observable): Observable} notifier - Receives an Observable of notifications with which a\n * user can `complete` or `error`, aborting the retry.\n * @return {Observable} The source Observable modified with retry logic.\n * @method retryWhen\n * @owner Observable\n */\nexport function retryWhen<T>(notifier: (errors: Observable<any>) => Observable<any>): MonoTypeOperatorFunction<T> {\n  return (source: Observable<T>) => source.lift(new RetryWhenOperator(notifier, source));\n}\n\nclass RetryWhenOperator<T> implements Operator<T, T> {\n  constructor(protected notifier: (errors: Observable<any>) => Observable<any>,\n              public source: Observable<T>) {\n  }\n\n  call(subscriber: Subscriber<T>, source: any): TeardownLogic {\n    return source.subscribe(new RetryWhenSubscriber(subscriber, this.notifier, this.source));\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass RetryWhenSubscriber<T, R> extends OuterSubscriber<T, R> {\n\n  private errors: Subject<any>;\n  private retries: Observable<any>;\n  private retriesSubscription: Subscription;\n\n  constructor(destination: Subscriber<R>,\n              private notifier: (errors: Observable<any>) => Observable<any>,\n              private source: Observable<T>) {\n    super(destination);\n  }\n\n  error(err: any) {\n    if (!this.isStopped) {\n\n      let errors = this.errors;\n      let retries: any = this.retries;\n      let retriesSubscription = this.retriesSubscription;\n\n      if (!retries) {\n        errors = new Subject();\n        retries = tryCatch(this.notifier)(errors);\n        if (retries === errorObject) {\n          return super.error(errorObject.e);\n        }\n        retriesSubscription = subscribeToResult(this, retries);\n      } else {\n        this.errors = null;\n        this.retriesSubscription = null;\n      }\n\n      this._unsubscribeAndRecycle();\n\n      this.errors = errors;\n      this.retries = retries;\n      this.retriesSubscription = retriesSubscription;\n\n      errors.next(err);\n    }\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    const { errors, retriesSubscription } = this;\n    if (errors) {\n      errors.unsubscribe();\n      this.errors = null;\n    }\n    if (retriesSubscription) {\n      retriesSubscription.unsubscribe();\n      this.retriesSubscription = null;\n    }\n    this.retries = null;\n  }\n\n  notifyNext(outerValue: T, innerValue: R,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, R>): void {\n    const { errors, retries, retriesSubscription } = this;\n    this.errors = null;\n    this.retries = null;\n    this.retriesSubscription = null;\n\n    this._unsubscribeAndRecycle();\n\n    this.errors = errors;\n    this.retries = retries;\n    this.retriesSubscription = retriesSubscription;\n\n    this.source.subscribe(this);\n  }\n}\n"]}