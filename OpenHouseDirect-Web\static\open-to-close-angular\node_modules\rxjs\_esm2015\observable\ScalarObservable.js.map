{"version": 3, "file": "ScalarObservable.js", "sourceRoot": "", "sources": ["../../src/observable/ScalarObservable.ts"], "names": [], "mappings": "OACO,EAAE,UAAU,EAAE,MAAM,eAAe;AAI1C;;;;GAIG;AACH,sCAAyC,UAAU;IAwBjD,YAAmB,KAAQ,EAAU,SAAsB;QACzD,OAAO,CAAC;QADS,UAAK,GAAL,KAAK,CAAG;QAAU,cAAS,GAAT,SAAS,CAAa;QAF3D,cAAS,GAAY,IAAI,CAAC;QAIxB,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IA5BD,OAAO,MAAM,CAAI,KAAQ,EAAE,SAAsB;QAC/C,MAAM,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,QAAQ,CAAC,KAAU;QACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;QAE1C,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACT,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,EAAE,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC;QACT,CAAC;QAED,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QACX,IAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAWD,oCAAoC,CAAC,UAAU,CAAC,UAAyB;QACvE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACtD,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;aAC/B,CAAC,CAAC;QACL,CAAC;QAAC,IAAI,CAAC,CAAC;YACN,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvB,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gBACvB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAAA"}