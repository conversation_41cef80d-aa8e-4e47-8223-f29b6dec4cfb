
/*!
 * Stylus - BinOp
 * Copyright (c) Automattic <developer.wordpress.com>
 * MIT Licensed
 */

/**
 * Module dependencies.
 */

var Node = require('./node');

/**
 * Initialize a new `BinOp` with `op`, `left` and `right`.
 *
 * @param {String} op
 * @param {Node} left
 * @param {Node} right
 * @api public
 */

var BinOp = module.exports = function BinOp(op, left, right){
  Node.call(this);
  this.op = op;
  this.left = left;
  this.right = right;
};

/**
 * Inherit from `Node.prototype`.
 */

BinOp.prototype.__proto__ = Node.prototype;

/**
 * Return a clone of this node.
 * 
 * @return {Node}
 * @api public
 */

BinOp.prototype.clone = function(parent){
  var clone = new BinOp(this.op);
  clone.left = this.left.clone(parent, clone);
  clone.right = this.right && this.right.clone(parent, clone);
  clone.lineno = this.lineno;
  clone.column = this.column;
  clone.filename = this.filename;
  if (this.val) clone.val = this.val.clone(parent, clone);
  return clone;
};

/**
 * Return <left> <op> <right>
 *
 * @return {String}
 * @api public
 */
BinOp.prototype.toString = function() {
  return this.left.toString() + ' ' + this.op + ' ' + this.right.toString();
};

/**
 * Return a JSON representation of this node.
 *
 * @return {Object}
 * @api public
 */

BinOp.prototype.toJSON = function(){
  var json = {
    __type: 'BinOp',
    left: this.left,
    right: this.right,
    op: this.op,
    lineno: this.lineno,
    column: this.column,
    filename: this.filename
  };
  if (this.val) json.val = this.val;
  return json;
};
