{"_from": "to-arraybuffer@^1.0.0", "_id": "to-arraybuffer@1.0.1", "_inBundle": false, "_integrity": "sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA==", "_location": "/to-arraybuffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "to-arraybuffer@^1.0.0", "name": "to-arraybuffer", "escapedName": "to-arraybuffer", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/stream-http"], "_resolved": "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "_shasum": "7d229b1fcc637e466ca081180836a7aabff83f43", "_spec": "to-arraybuffer@^1.0.0", "_where": "C:\\Users\\<USER>\\openhouse\\OpenHouseDirect-Web\\static\\open-to-close-angular\\node_modules\\stream-http", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/jhiesey/to-arraybuffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get an ArrayBuffer from a Buffer as fast as possible", "devDependencies": {"tape": "^4.4.0", "zuul": "^3.9.0"}, "homepage": "https://github.com/jhiesey/to-arraybuffer#readme", "keywords": ["buffer", "to", "arraybuffer", "fast", "read", "only"], "license": "MIT", "main": "index.js", "name": "to-arraybuffer", "repository": {"type": "git", "url": "git://github.com/jhiesey/to-arraybuffer.git"}, "scripts": {"test": "npm run test-node && ([ -n \"${TRAVIS_PULL_REQUEST}\" -a \"${TRAVIS_PULL_REQUEST}\" != 'false' ] || npm run test-browser)", "test-browser": "zuul --no-coverage -- test.js", "test-browser-local": "zuul --local 8080 --no-coverage -- test.js", "test-node": "tape test.js"}, "version": "1.0.1"}