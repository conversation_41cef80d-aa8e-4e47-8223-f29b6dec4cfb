{"version": 3, "file": "cdk-platform.umd.min.js", "sources": ["../../src/cdk/platform/features.ts", "../../src/cdk/platform/platform.ts", "../../src/cdk/platform/platform-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents: boolean;\n\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nexport function supportsPassiveEventListeners(): boolean {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null!, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n\n  return supportsPassiveEvents;\n}\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes: Set<string>;\n\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n  // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n  // first changing it to something else:\n  // The specified value \"\" does not conform to the required format.\n  // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n  'color',\n  'button',\n  'checkbox',\n  'date',\n  'datetime-local',\n  'email',\n  'file',\n  'hidden',\n  'image',\n  'month',\n  'number',\n  'password',\n  'radio',\n  'range',\n  'reset',\n  'search',\n  'submit',\n  'tel',\n  'text',\n  'time',\n  'url',\n  'week',\n];\n\n/** @returns The input types supported by this browser. */\nexport function getSupportedInputTypes(): Set<string> {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n\n  return supportedInputTypes;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nconst hasV8BreakIterator = (typeof(Intl) !== 'undefined' && (Intl as any).v8BreakIterator);\n\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\n@Injectable()\nexport class Platform {\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser: boolean = typeof document === 'object' && !!document;\n\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE: boolean = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT: boolean = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n\n  /** Whether the current rendering engine is Blink. */\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  BLINK: boolean = this.isBrowser &&\n      (!!((window as any).chrome || hasV8BreakIterator) && !!CSS && !this.EDGE && !this.TRIDENT);\n\n  /** Whether the current rendering engine is WebKit. */\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  WEBKIT: boolean = this.isBrowser &&\n      /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n\n  /** Whether the current platform is Apple iOS. */\n  IOS: boolean = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) &&\n      !(window as any).MSStream;\n\n  /** Whether the current browser is Firefox. */\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  FIREFOX: boolean = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID: boolean = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n\n  /** Whether the current browser is Safari. */\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  SAFARI: boolean = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {Platform} from './platform';\n\n\n@NgModule({\n  providers: [Platform]\n})\nexport class PlatformModule {}\n"], "names": ["supportsPassiveEventListeners", "supportsPassiveEvents", "window", "addEventListener", "Object", "defineProperty", "get", "getSupportedInputTypes", "supportedInputTypes", "document", "Set", "candidateInputTypes", "featureTestInput", "createElement", "filter", "value", "setAttribute", "type", "hasV8BreakIterator", "v8BreakIterator", "this", "<PERSON><PERSON><PERSON><PERSON>", "EDGE", "test", "navigator", "userAgent", "TRIDENT", "BLINK", "chrome", "CSS", "WEBKIT", "IOS", "MSStream", "FIREFOX", "ANDROID", "SAFARI", "Injectable", "Platform", "PlatformModule", "NgModule", "args", "providers"], "mappings": ";;;;;;;+SAeA,SAAAA,KACE,GAA6B,MAAzBC,GAAmD,mBAAXC,QAC1C,IACEA,OAAOC,iBAAiB,OAAM,KAASC,OAAOC,kBAAmB,WAC/DC,IAAK,WAAM,MAAAL,IAAwB,cAGrCA,EAAwBA,IAAyB,EAIrD,MAAOA,GAqCT,QAAAM,KAEE,GAAIC,EACF,MAAOA,EAMT,IAAwB,gBAAbC,YAA0BA,SAEnC,MADAD,GAAsB,GAAIE,KAAIC,EAIhC,IAAIC,GAAmBH,SAASI,cAAc,QAM9C,OALAL,GAAsB,GAAIE,KAAIC,EAAoBG,OAAO,SAAAC,GAEvD,MADAH,GAAiBI,aAAa,OAAQD,GAC/BH,EAAiBK,OAASF,KCpErC,GDHId,GAqBAO,EClBEU,EAAuC,mBAAjB,OAAgC,KAAcC,0CAS1EC,KAAAC,UAA2C,gBAAbZ,aAA2BA,SAGzDW,KAAAE,KAAkBF,KAAKC,WAAa,UAAUE,KAAKC,UAAUC,WAG7DL,KAAAM,QAAqBN,KAAKC,WAAa,kBAAkBE,KAAKC,UAAUC,WAIxEL,KAAAO,MAAmBP,KAAKC,cACd,OAAgBO,SAAUV,MAAyBW,MAAQT,KAAKE,OAASF,KAAKM,QAKxFN,KAAAU,OAAoBV,KAAKC,WACnB,eAAeE,KAAKC,UAAUC,aAAeL,KAAKO,QAAUP,KAAKE,OAASF,KAAKM,QAGrFN,KAAAW,IAAiBX,KAAKC,WAAa,mBAAmBE,KAAKC,UAAUC,aAC9D,OAAgBO,SAOvBZ,KAAAa,QAAqBb,KAAKC,WAAa,uBAAuBE,KAAKC,UAAUC,WAI7EL,KAAAc,QAAqBd,KAAKC,WAAa,WAAWE,KAAKC,UAAUC,aAAeL,KAAKM,QAMrFN,KAAAe,OAAoBf,KAAKC,WAAa,UAAUE,KAAKC,UAAUC,YAAcL,KAAKU,OA3DlF,sBAkBAb,KAACmB,EAAAA,mDAlBDC,KDiCM1B,GAKJ,QACA,SACA,WACA,OACA,iBACA,QACA,OACA,SACA,QACA,QACA,SACA,WACA,QACA,QACA,QACA,SACA,SACA,MACA,OACA,OACA,MACA,QEnDF2B,EAAA,yBARA,sBAYArB,KAACsB,EAAAA,SAADC,OACEC,WAAYJ,6CAbdC"}