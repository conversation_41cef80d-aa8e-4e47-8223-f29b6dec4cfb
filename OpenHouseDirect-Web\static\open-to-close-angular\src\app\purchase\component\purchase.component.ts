import { Component, OnInit } from '@angular/core';
import { Params } from '@angular/router';
import { BaseComponent } from '@app/base/components/base.component';
import { FormGroup, FormControl, Validators} from '@angular/forms';
import { NgForm } from '@angular/forms';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Purchase } from '@app/purchase/model/purchase';
import { Plans } from '@app/profile/models/plan';
import { ListingAgentProfileComponent } from '@app/profile/component/listing-agent-profile.component';
import { ProfileService } from '@app/profile/service/profile.service';
import { FormArray } from '@angular/forms/src/model';
import { CreditCard } from '@app/purchase/model/credit-card';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { AgentDetail } from '@app/profile/models/agent-detail';
import { ignoreElements } from 'rxjs/operator/ignoreElements';
import { BrokerageProfileComponent } from '@app/profile/component/brokerage-profile.component';
import { BrokerageDetail } from '@app/profile/models/brokerage-detail';

declare var require;

@Component({
  selector: 'app-purchase',
  templateUrl: '../views/purchase.component.html',
  styleUrls: ['../css/purchase.component.css']
})

export class PurchaseComponent extends BaseComponent implements OnInit {

  public showNewCard:boolean = false;
  public showBillingInfo : boolean = true;
  public showCarditCardInfo : boolean = true;
  public selectCardToken='';
  public selectCard='';
  public CLIENT_AUTHORIZATION;
  public savedCreditCard;
  public userProfile: any;
  public userComponent;
  public userProfileType;
  private buttonEnable : Boolean = true;
  public cardPresent: Boolean = false;
  public selectedMonth:string;
  public selectedYear:String;

  purchaseCardInfoForm:FormGroup;
  purchaseBillingInfoForm:FormGroup;
  purchaseService:PurchaseService;
  profileService: ProfileService;
  creditCard = new CreditCard();
  public selectedPlans:Plans = new Plans();
  public subscribePurchase: Purchase = new Purchase();

  // Coupon-related properties
  public couponCode: string = '';
  public couponApplied: boolean = false;
  public validatingCoupon: boolean = false;
  public couponMessage: string = '';
  public discountAmount: number = 0;
  public finalPrice: string = '';
  public appliedCouponData: any = null;

  cardNumberPattern = "^((\\+91-?)|0)?[0-9]{13,16}$";
  cvvPattern = "^((\\+91-?)|0)?[0-9]{3,4}$";
  EXMonthPattern = "^((\\+91-?)|0)?[0-9]{1,2}$";
  EXYearPattern = "^((\\+91-?)|0)?[0-9]{4}$";


  constructor() {
    super();
    this.profileService = ServiceLocator.injector.get(ProfileService);
    this.purchaseService=ServiceLocator.injector.get(PurchaseService);
  }

  ngOnInit(){
    this.initData();
    this.initAPI();

    if(this.purchaseService.getPlan() != undefined){
      this.selectedPlans=this.purchaseService.getPlan();
      setTimeout(() => {
        if(BaseComponent.user.user_type != undefined){
          if(BaseComponent.user.user_type == 'LA'){
            if(Object.keys(ListingAgentProfileComponent.listingAgent).length == 0){
              this.showNewCard = true;
              this.userProfile = ListingAgentProfileComponent.listingAgent;
            }else{
              this.userProfile = ListingAgentProfileComponent.listingAgent;
              this.loadUserDetail();
            }
          }
          else if(BaseComponent.user.user_type == 'BR'){
            if(Object.keys(BrokerageProfileComponent.brokerageUser).length == 0){
              this.showNewCard = true;
              this.userProfile = BrokerageProfileComponent.brokerageUser;
            }else{
              this.userProfile = BrokerageProfileComponent.brokerageUser;
              this.loadUserDetail();
            }
            // this.userProfile = BrokerageProfileComponent.brokerageUser;
            // this.loadUserDetail();
          }
        }
        else
        {
          if(BaseComponent.accessToken != '' && BaseComponent.userType != ''){
            this.routeOnUrl('/profile/'+BaseComponent.userType);
          }
          else{
            this.route.queryParams.subscribe((params:Params)=>{
              this.routeOnUrl('/profile/'+params['user_type']);
            });
          }
        }
      }, 700);
    }
    else{
      if(BaseComponent.accessToken != '' && BaseComponent.userType != ''){
        this.routeOnUrl('/profile/'+BaseComponent.userType);
      }
      else{
        this.routeOnUrl('/profile/'+this.getUserType());
      }
    }
  }

  initData(){
    this.purchaseCardInfoForm = new FormGroup({
      first_Name:new FormControl('',Validators.required),
      last_Name: new FormControl('',Validators.required),
        creditCard : new FormGroup({
          number:new FormControl('',[Validators.pattern(this.cardNumberPattern)]),
          cvv:new FormControl('',[Validators.pattern(this.cvvPattern)]),
          expirationMonth:new FormControl('',[Validators.required,Validators.maxLength(2),Validators.minLength(1)]),
          expirationYear:new FormControl('',[Validators.required,Validators.maxLength(4),Validators.minLength(4)]),
          billingAddress:new FormGroup({
            address_1 : new FormControl('',Validators.required),
            address_2 : new FormControl(''),
            state : new FormControl('',Validators.required),
            city : new FormControl('',Validators.required),
            zipcode : new FormControl('',[Validators.required,Validators.minLength(5)]),
            user : new FormGroup({
              id : new FormControl('')
            })
          })
        }),
    });
  }

  loadUserDetail(){
    if(this.userProfile.payment_method !=undefined && Object.keys(this.userProfile.payment_method).length != 0){
      this.purchaseCardInfoForm.controls['creditCard'].patchValue(this.userProfile.payment_method);
      this.purchaseCardInfoForm.controls.first_Name.setValue(this.userProfile.payment_method.first_name);
      this.purchaseCardInfoForm.controls.last_Name.setValue(this.userProfile.payment_method.last_name);
      this.purchaseCardInfoForm.controls.creditCard['controls']['number'].setValue('0000000000000000')
      this.purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].setValue('00');
      this.purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].setValue('9999');
      this.purchaseCardInfoForm.controls.creditCard['controls']['cvv'].setValue('000');
      this.showNewCard = true;
      this.showCarditCardInfo = false;

      if(this.userProfile.payment_method.last_4 != undefined){
        this.cardPresent = true;
        this.savedCreditCard = this.userProfile.payment_method.last_4;
      }
      else{
        this.cardPresent = false;
      }
    }

    if(this.userProfile.billing_info !=undefined && Object.keys(this.userProfile.billing_info).length != 0){
      this.purchaseCardInfoForm.controls['creditCard']['controls']['billingAddress'].patchValue(this.userProfile.billing_info);
      this.showNewCard = true;
      this.showBillingInfo = false;

    }

    if(Object.keys(this.userProfile.billing_info).length != 0 && Object.keys(this.userProfile.payment_method).length != 0){
        this.savedCreditCard = this.userProfile.payment_method.last_4;
        this.showNewCard = false;
    }

    if(Object.keys(this.userProfile.payment_method).length == 0 && Object.keys(this.userProfile.billing_info).length == 0){
      this.showNewCard = true;
    }

  }

  initAPI(){
    this.purchaseService.subscriptionClientToken().subscribe(res =>{
      this.CLIENT_AUTHORIZATION=res['result']['client_token'];
    }, err => console.log(err));
  }

  formBilling:NgForm;
  completeOrder(form:NgForm){
    this.buttonEnable = false;
    var self =this;
    if(Object.keys(this.userProfile.payment_method).length == 0){
      this.creditCard.cardHolderName=form.value['first_Name']+' '+form.value['last_Name'];
      this.creditCard.creditCard.number= form.value['creditCard']['number'];
      this.creditCard.creditCard.cvv= form.value['creditCard']['cvv'];
      this.creditCard.creditCard.expirationMonth = form.value['creditCard']['expirationMonth'];
      this.creditCard.creditCard.expirationYear = form.value['creditCard']['expirationYear'] ;
      this.creditCard.creditCard.billingAddress.firstName =  form.value['first_Name'];
      this.creditCard.creditCard.billingAddress.lastName = form.value['last_Name'];
      this.creditCard.creditCard.options.validate = false;

      var createClient = require('braintree-web/client').create;

      createClient({
        authorization: this.CLIENT_AUTHORIZATION
      }, function (createErr, clientInstance) {
        clientInstance.request({
          endpoint: 'payment_methods/credit_cards',
          method: 'post',
          data: self.creditCard
        }, function (requestErr, response) {
          if (requestErr) {
            self.errMessageResponse('Invalid CreditCard');
            this.buttonEnable = true;
            throw new Error(requestErr);
          }
          self.subscribePlan(response.creditCards[0].nonce,'', form);
        });
      });
    }else{
      this.subscribePlan('',this.userProfile.payment_method.token,form);
    }
  }

  subscribePlan(nonce,token,form){
    if(Object.keys(this.userProfile.billing_info).length == 0)
    {
      if(form.value != undefined){
        form.value['creditCard']['billingAddress']['user']['id']=BaseComponent.user.id;
        this.profileService.updateBillingInfo(form.value['creditCard']['billingAddress']).subscribe(res =>{
          this.completeUpgrade(form,token,nonce)
      },err =>{
        this.errorResponse(err.json());
        this.buttonEnable = true;
      })
      }
      else{
        this.completeUpgrade(form,token,nonce);
      }
    }
    else
    {
      this.completeUpgrade(form,token,nonce);
    }
  }

  completeUpgrade(form,token,nonce){
    if(this.selectedPlans != undefined){
      this.subscribePurchase.user.id = BaseComponent.user.id;
      this.subscribePurchase.plan_id = this.selectedPlans['id'];
      if(nonce !=''){
        this.subscribePurchase.payment_method.nonce = nonce;
      }
      if(token !=''){
        this.subscribePurchase.payment_method.token = token;
      }
      // Add coupon code if applied
      if(this.couponApplied && this.couponCode){
        this.subscribePurchase.coupon_code = this.couponCode;
      }
      this.purchaseService.subsciptionPlan(this.subscribePurchase).subscribe(res =>{
        this.successResponse(res);
        if(form != ''){
          form.reset();
        }
        this.routeOnUrl('/purchase/purchase-success');
        BaseComponent.user.is_paid_account = true;
        if(this.userProfile == ListingAgentProfileComponent.listingAgent){
          ProfileComponent.listingAgent = new AgentDetail();
        }
        if(this.userProfile == BrokerageProfileComponent.brokerageUser){
          ProfileComponent.brokerageUser = new BrokerageDetail();
        }
        this.buttonEnable = true;
      },error=>{
        this.buttonEnable = true;
      this.errorResponse(error.json())})
    }
  }

  // Coupon-related methods
  applyCoupon() {
    if (!this.couponCode || this.couponCode.trim() === '') {
      this.couponMessage = 'Please enter a coupon code';
      return;
    }

    this.validatingCoupon = true;
    this.couponMessage = '';

    const couponData = {
      coupon_code: this.couponCode.trim()
    };

    this.purchaseService.validateCoupon(couponData).subscribe(
      res => {
        this.validatingCoupon = false;
        if (res && res.result) {
          this.couponApplied = true;
          this.appliedCouponData = res.result;
          this.discountAmount = parseFloat((res.result.amount || '0').toString());
          this.couponMessage = `Coupon applied successfully! ${(res.result.description || '').toString()}`;
          this.calculateFinalPrice();
        } else {
          this.couponMessage = 'Invalid coupon code';
          this.resetCoupon();
        }
      },
      error => {
        this.validatingCoupon = false;
        const errorData = error.json();
        this.couponMessage = (errorData && errorData.message) ? errorData.message : 'Error validating coupon code';
        this.resetCoupon();
      }
    );
  }

  removeCoupon() {
    this.resetCoupon();
    this.couponMessage = 'Coupon removed';
  }

  private resetCoupon() {
    this.couponApplied = false;
    this.discountAmount = 0;
    this.finalPrice = '';
    this.appliedCouponData = null;
  }

  private calculateFinalPrice() {
    if (this.couponApplied && this.discountAmount > 0) {
      const originalPrice = parseFloat(this.selectedPlans.price.toString());
      const discounted = Math.max(0, originalPrice - this.discountAmount);
      this.finalPrice = discounted.toFixed(2);
    }
  }

  selectedCreditCard(creditCard){
    this.selectCard=creditCard;
    this.selectCardToken=creditCard.token;
    this.showNewCard=false;
  }

  completeOrderByCard(){
     this.subscribePlan('',this.userProfile.payment_method.token, '');
     this.showNewCard = false;
  }

  addNewCard(){
    if(this.showNewCard == false){
      this.showNewCard=true;
      this.selectCard='';
    }
    else{
      this.showNewCard=false;
    }
  }

  cardExDateV(value){
    if(this.isInteger(value)){
      if(value.length == 0)
      {
        this.purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].setErrors({'incorrect': true});
      }
      else
      {
        if(value > 12 || value.length > 2){
          this.purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].setErrors({'incorrect': true});
        }
        else{
          if(value.length == 1)
          {
            this.selectedMonth = '0'+value.toString();
          }
          else
          {
            this.selectedMonth = value;
          }
        }
        if(this.selectedYear != null){
          this.cardExYearV(this.selectedYear);
        }
      }
    }
    else{
      this.purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].setErrors({'number': true});
    }
  }

  cardExYearV(year){
    var pattern = /^\d+\.?\d*$/;
    console.log(pattern.test(year))
    if(!pattern.test(year)){
      this.purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].setErrors({'incorrect': true});
      return;
    } else if(year.length != 4){
      this.purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].setErrors({'yearNumber': true});
      return;
    }
    let currentYear:String = new Date().getFullYear().toString();
    if(year.toString() >= currentYear.toString()){
      this.selectedYear = year;
      let month:Number = new Date().getMonth()+1;

      let months:String;

      if(month <= 9){
        months = "0"+month;
      }
      else{
        months = month.toString();
      }

      if(this.selectedMonth >= months && this.selectedYear == currentYear){
        if(this.selectedMonth >= months){
        }
      }
      else
      {
        if(this.selectedYear > currentYear && this.selectedYear.length  == 4){

          if(this.selectedYear.length == 4 && parseInt(this.selectedMonth) > 12)
          {
            this.purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].setErrors({'incorrect': true});
          }
        }
        else
        {
          if(this.selectedYear.length == 4){
            this.purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].setErrors({'incorrect': true});
          }
        }
      }
    }
    else
    {
      this.purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].setErrors({'incorrect': true});
    }
  }
}

